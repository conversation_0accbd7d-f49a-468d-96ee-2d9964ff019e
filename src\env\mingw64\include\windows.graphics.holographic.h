/*** Autogenerated by WIDL 10.8 from include/windows.graphics.holographic.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __windows_graphics_holographic_h__
#define __windows_graphics_holographic_h__

/* Forward declarations */

#ifndef ____x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace_FWD_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace ABI::Windows::Graphics::Holographic::IHolographicSpace
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Holographic {
                interface IHolographicSpace;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2_FWD_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2 __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2 ABI::Windows::Graphics::Holographic::IHolographicSpaceStatics2
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Holographic {
                interface IHolographicSpaceStatics2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics3_FWD_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics3_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics3 __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics3;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics3 ABI::Windows::Graphics::Holographic::IHolographicSpaceStatics3
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Holographic {
                interface IHolographicSpaceStatics3;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraAddedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraAddedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraAddedEventArgs __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraAddedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraAddedEventArgs ABI::Windows::Graphics::Holographic::IHolographicSpaceCameraAddedEventArgs
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Holographic {
                interface IHolographicSpaceCameraAddedEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraRemovedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraRemovedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraRemovedEventArgs __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraRemovedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraRemovedEventArgs ABI::Windows::Graphics::Holographic::IHolographicSpaceCameraRemovedEventArgs
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Holographic {
                interface IHolographicSpaceCameraRemovedEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGraphics_CHolographic_CHolographicCamera_FWD_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CHolographic_CHolographicCamera_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Holographic {
                class HolographicCamera;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CGraphics_CHolographic_CHolographicCamera __x_ABI_CWindows_CGraphics_CHolographic_CHolographicCamera;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CGraphics_CHolographic_CHolographicCamera_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CGraphics_CHolographic_CHolographicFrame_FWD_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CHolographic_CHolographicFrame_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Holographic {
                class HolographicFrame;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CGraphics_CHolographic_CHolographicFrame __x_ABI_CWindows_CGraphics_CHolographic_CHolographicFrame;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CGraphics_CHolographic_CHolographicFrame_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CGraphics_CHolographic_CHolographicSpace_FWD_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CHolographic_CHolographicSpace_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Holographic {
                class HolographicSpace;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CGraphics_CHolographic_CHolographicSpace __x_ABI_CWindows_CGraphics_CHolographic_CHolographicSpace;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CGraphics_CHolographic_CHolographicSpace_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CGraphics_CHolographic_CHolographicSpaceCameraAddedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CHolographic_CHolographicSpaceCameraAddedEventArgs_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Holographic {
                class HolographicSpaceCameraAddedEventArgs;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CGraphics_CHolographic_CHolographicSpaceCameraAddedEventArgs __x_ABI_CWindows_CGraphics_CHolographic_CHolographicSpaceCameraAddedEventArgs;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CGraphics_CHolographic_CHolographicSpaceCameraAddedEventArgs_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CGraphics_CHolographic_CHolographicSpaceCameraRemovedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CHolographic_CHolographicSpaceCameraRemovedEventArgs_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Holographic {
                class HolographicSpaceCameraRemovedEventArgs;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CGraphics_CHolographic_CHolographicSpaceCameraRemovedEventArgs __x_ABI_CWindows_CGraphics_CHolographic_CHolographicSpaceCameraRemovedEventArgs;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CGraphics_CHolographic_CHolographicSpaceCameraRemovedEventArgs_FWD_DEFINED__ */

#ifndef ____FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_IInspectable_FWD_DEFINED__
#define ____FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_IInspectable_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_IInspectable __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_IInspectable;
#ifdef __cplusplus
#define __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_IInspectable ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Graphics::Holographic::HolographicSpace*,IInspectable* >
#endif /* __cplusplus */
#endif

#ifndef ____FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraAddedEventArgs_FWD_DEFINED__
#define ____FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraAddedEventArgs_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraAddedEventArgs __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraAddedEventArgs;
#ifdef __cplusplus
#define __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraAddedEventArgs ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Graphics::Holographic::HolographicSpace*,ABI::Windows::Graphics::Holographic::HolographicSpaceCameraAddedEventArgs* >
#endif /* __cplusplus */
#endif

#ifndef ____FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraRemovedEventArgs_FWD_DEFINED__
#define ____FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraRemovedEventArgs_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraRemovedEventArgs __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraRemovedEventArgs;
#ifdef __cplusplus
#define __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraRemovedEventArgs ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Graphics::Holographic::HolographicSpace*,ABI::Windows::Graphics::Holographic::HolographicSpaceCameraRemovedEventArgs* >
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <inspectable.h>
#include <asyncinfo.h>
#include <eventtoken.h>
#include <windowscontracts.h>
#include <windows.foundation.h>
#include <windows.foundation.numerics.h>
#include <windows.graphics.directx.h>
#include <windows.graphics.directx.direct3d11.h>
#include <windows.perception.spatial.h>
#include <windows.ui.core.h>

#ifdef __cplusplus
extern "C" {
#endif

#ifndef __cplusplus
typedef struct __x_ABI_CWindows_CGraphics_CHolographic_CHolographicAdapterId __x_ABI_CWindows_CGraphics_CHolographic_CHolographicAdapterId;
#else /* __cplusplus */
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Holographic {
                typedef struct HolographicAdapterId HolographicAdapterId;
            }
        }
    }
}
#endif /* __cplusplus */

#ifndef ____x_ABI_CWindows_CGraphics_CHolographic_CIHolographicCamera_FWD_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CHolographic_CIHolographicCamera_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicCamera __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicCamera;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicCamera ABI::Windows::Graphics::Holographic::IHolographicCamera
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Holographic {
                interface IHolographicCamera;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGraphics_CHolographic_CIHolographicFrame_FWD_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CHolographic_CIHolographicFrame_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicFrame __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicFrame;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicFrame ABI::Windows::Graphics::Holographic::IHolographicFrame
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Holographic {
                interface IHolographicFrame;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics ABI::Windows::Graphics::Holographic::IHolographicSpaceStatics
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Holographic {
                interface IHolographicSpaceStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2_FWD_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2 __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2 ABI::Windows::Graphics::Holographic::IHolographicSpaceStatics2
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Holographic {
                interface IHolographicSpaceStatics2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics3_FWD_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics3_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics3 __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics3;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics3 ABI::Windows::Graphics::Holographic::IHolographicSpaceStatics3
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Holographic {
                interface IHolographicSpaceStatics3;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraAddedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraAddedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraAddedEventArgs __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraAddedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraAddedEventArgs ABI::Windows::Graphics::Holographic::IHolographicSpaceCameraAddedEventArgs
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Holographic {
                interface IHolographicSpaceCameraAddedEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraRemovedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraRemovedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraRemovedEventArgs __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraRemovedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraRemovedEventArgs ABI::Windows::Graphics::Holographic::IHolographicSpaceCameraRemovedEventArgs
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Holographic {
                interface IHolographicSpaceCameraRemovedEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Holographic {
                struct HolographicAdapterId {
                    UINT32 LowPart;
                    INT32 HighPart;
                };
            }
        }
    }
}
extern "C" {
#else
struct __x_ABI_CWindows_CGraphics_CHolographic_CHolographicAdapterId {
    UINT32 LowPart;
    INT32 HighPart;
};
#ifdef WIDL_using_Windows_Graphics_Holographic
#define HolographicAdapterId __x_ABI_CWindows_CGraphics_CHolographic_CHolographicAdapterId
#endif /* WIDL_using_Windows_Graphics_Holographic */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000 */
/*****************************************************************************
 * IHolographicSpace interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000
#ifndef ____x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace, 0x4380dba6, 0x5e78, 0x434f, 0x80,0x7c, 0x34,0x33,0xd1,0xef,0xe8,0xb7);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Holographic {
                MIDL_INTERFACE("4380dba6-5e78-434f-807c-3433d1efe8b7")
                IHolographicSpace : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_PrimaryAdapterId(
                        ABI::Windows::Graphics::Holographic::HolographicAdapterId *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE SetDirect3D11Device(
                        ABI::Windows::Graphics::DirectX::Direct3D11::IDirect3DDevice *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_CameraAdded(
                        ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Graphics::Holographic::HolographicSpace*,ABI::Windows::Graphics::Holographic::HolographicSpaceCameraAddedEventArgs* > *handler,
                        EventRegistrationToken *cookie) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_CameraAdded(
                        EventRegistrationToken cookie) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_CameraRemoved(
                        ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Graphics::Holographic::HolographicSpace*,ABI::Windows::Graphics::Holographic::HolographicSpaceCameraRemovedEventArgs* > *handler,
                        EventRegistrationToken *cookie) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_CameraRemoved(
                        EventRegistrationToken cookie) = 0;

                    virtual HRESULT STDMETHODCALLTYPE CreateNextFrame(
                        ABI::Windows::Graphics::Holographic::IHolographicFrame **value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace, 0x4380dba6, 0x5e78, 0x434f, 0x80,0x7c, 0x34,0x33,0xd1,0xef,0xe8,0xb7)
#endif
#else
typedef struct __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace *This,
        TrustLevel *trustLevel);

    /*** IHolographicSpace methods ***/
    HRESULT (STDMETHODCALLTYPE *get_PrimaryAdapterId)(
        __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace *This,
        __x_ABI_CWindows_CGraphics_CHolographic_CHolographicAdapterId *value);

    HRESULT (STDMETHODCALLTYPE *SetDirect3D11Device)(
        __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace *This,
        __x_ABI_CWindows_CGraphics_CDirectX_CDirect3D11_CIDirect3DDevice *value);

    HRESULT (STDMETHODCALLTYPE *add_CameraAdded)(
        __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace *This,
        __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraAddedEventArgs *handler,
        EventRegistrationToken *cookie);

    HRESULT (STDMETHODCALLTYPE *remove_CameraAdded)(
        __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace *This,
        EventRegistrationToken cookie);

    HRESULT (STDMETHODCALLTYPE *add_CameraRemoved)(
        __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace *This,
        __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraRemovedEventArgs *handler,
        EventRegistrationToken *cookie);

    HRESULT (STDMETHODCALLTYPE *remove_CameraRemoved)(
        __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace *This,
        EventRegistrationToken cookie);

    HRESULT (STDMETHODCALLTYPE *CreateNextFrame)(
        __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace *This,
        __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicFrame **value);

    END_INTERFACE
} __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceVtbl;

interface __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace {
    CONST_VTBL __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IHolographicSpace methods ***/
#define __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace_get_PrimaryAdapterId(This,value) (This)->lpVtbl->get_PrimaryAdapterId(This,value)
#define __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace_SetDirect3D11Device(This,value) (This)->lpVtbl->SetDirect3D11Device(This,value)
#define __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace_add_CameraAdded(This,handler,cookie) (This)->lpVtbl->add_CameraAdded(This,handler,cookie)
#define __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace_remove_CameraAdded(This,cookie) (This)->lpVtbl->remove_CameraAdded(This,cookie)
#define __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace_add_CameraRemoved(This,handler,cookie) (This)->lpVtbl->add_CameraRemoved(This,handler,cookie)
#define __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace_remove_CameraRemoved(This,cookie) (This)->lpVtbl->remove_CameraRemoved(This,cookie)
#define __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace_CreateNextFrame(This,value) (This)->lpVtbl->CreateNextFrame(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace_QueryInterface(__x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace_AddRef(__x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace_Release(__x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace_GetIids(__x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace_GetRuntimeClassName(__x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace_GetTrustLevel(__x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IHolographicSpace methods ***/
static inline HRESULT __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace_get_PrimaryAdapterId(__x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace* This,__x_ABI_CWindows_CGraphics_CHolographic_CHolographicAdapterId *value) {
    return This->lpVtbl->get_PrimaryAdapterId(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace_SetDirect3D11Device(__x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace* This,__x_ABI_CWindows_CGraphics_CDirectX_CDirect3D11_CIDirect3DDevice *value) {
    return This->lpVtbl->SetDirect3D11Device(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace_add_CameraAdded(__x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace* This,__FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraAddedEventArgs *handler,EventRegistrationToken *cookie) {
    return This->lpVtbl->add_CameraAdded(This,handler,cookie);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace_remove_CameraAdded(__x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace* This,EventRegistrationToken cookie) {
    return This->lpVtbl->remove_CameraAdded(This,cookie);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace_add_CameraRemoved(__x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace* This,__FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraRemovedEventArgs *handler,EventRegistrationToken *cookie) {
    return This->lpVtbl->add_CameraRemoved(This,handler,cookie);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace_remove_CameraRemoved(__x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace* This,EventRegistrationToken cookie) {
    return This->lpVtbl->remove_CameraRemoved(This,cookie);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace_CreateNextFrame(__x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace* This,__x_ABI_CWindows_CGraphics_CHolographic_CIHolographicFrame **value) {
    return This->lpVtbl->CreateNextFrame(This,value);
}
#endif
#ifdef WIDL_using_Windows_Graphics_Holographic
#define IID_IHolographicSpace IID___x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace
#define IHolographicSpaceVtbl __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceVtbl
#define IHolographicSpace __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace
#define IHolographicSpace_QueryInterface __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace_QueryInterface
#define IHolographicSpace_AddRef __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace_AddRef
#define IHolographicSpace_Release __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace_Release
#define IHolographicSpace_GetIids __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace_GetIids
#define IHolographicSpace_GetRuntimeClassName __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace_GetRuntimeClassName
#define IHolographicSpace_GetTrustLevel __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace_GetTrustLevel
#define IHolographicSpace_get_PrimaryAdapterId __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace_get_PrimaryAdapterId
#define IHolographicSpace_SetDirect3D11Device __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace_SetDirect3D11Device
#define IHolographicSpace_add_CameraAdded __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace_add_CameraAdded
#define IHolographicSpace_remove_CameraAdded __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace_remove_CameraAdded
#define IHolographicSpace_add_CameraRemoved __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace_add_CameraRemoved
#define IHolographicSpace_remove_CameraRemoved __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace_remove_CameraRemoved
#define IHolographicSpace_CreateNextFrame __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace_CreateNextFrame
#endif /* WIDL_using_Windows_Graphics_Holographic */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000 */

/*****************************************************************************
 * IHolographicSpaceStatics2 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000
#ifndef ____x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2, 0x0e777088, 0x75fc, 0x48af, 0x87,0x58, 0x06,0x52,0xf6,0xf0,0x7c,0x59);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Holographic {
                MIDL_INTERFACE("0e777088-75fc-48af-8758-0652f6f07c59")
                IHolographicSpaceStatics2 : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_IsSupported(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_IsAvailable(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_IsAvailableChanged(
                        ABI::Windows::Foundation::IEventHandler<IInspectable* > *handler,
                        EventRegistrationToken *token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_IsAvailableChanged(
                        EventRegistrationToken token) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2, 0x0e777088, 0x75fc, 0x48af, 0x87,0x58, 0x06,0x52,0xf6,0xf0,0x7c,0x59)
#endif
#else
typedef struct __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2 *This,
        TrustLevel *trustLevel);

    /*** IHolographicSpaceStatics2 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_IsSupported)(
        __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2 *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *get_IsAvailable)(
        __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2 *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *add_IsAvailableChanged)(
        __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2 *This,
        __FIEventHandler_1_IInspectable *handler,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_IsAvailableChanged)(
        __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2 *This,
        EventRegistrationToken token);

    END_INTERFACE
} __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2Vtbl;

interface __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2 {
    CONST_VTBL __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IHolographicSpaceStatics2 methods ***/
#define __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2_get_IsSupported(This,value) (This)->lpVtbl->get_IsSupported(This,value)
#define __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2_get_IsAvailable(This,value) (This)->lpVtbl->get_IsAvailable(This,value)
#define __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2_add_IsAvailableChanged(This,handler,token) (This)->lpVtbl->add_IsAvailableChanged(This,handler,token)
#define __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2_remove_IsAvailableChanged(This,token) (This)->lpVtbl->remove_IsAvailableChanged(This,token)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2_QueryInterface(__x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2_AddRef(__x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2_Release(__x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2_GetIids(__x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2_GetRuntimeClassName(__x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2_GetTrustLevel(__x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IHolographicSpaceStatics2 methods ***/
static inline HRESULT __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2_get_IsSupported(__x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2* This,boolean *value) {
    return This->lpVtbl->get_IsSupported(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2_get_IsAvailable(__x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2* This,boolean *value) {
    return This->lpVtbl->get_IsAvailable(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2_add_IsAvailableChanged(__x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2* This,__FIEventHandler_1_IInspectable *handler,EventRegistrationToken *token) {
    return This->lpVtbl->add_IsAvailableChanged(This,handler,token);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2_remove_IsAvailableChanged(__x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_IsAvailableChanged(This,token);
}
#endif
#ifdef WIDL_using_Windows_Graphics_Holographic
#define IID_IHolographicSpaceStatics2 IID___x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2
#define IHolographicSpaceStatics2Vtbl __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2Vtbl
#define IHolographicSpaceStatics2 __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2
#define IHolographicSpaceStatics2_QueryInterface __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2_QueryInterface
#define IHolographicSpaceStatics2_AddRef __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2_AddRef
#define IHolographicSpaceStatics2_Release __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2_Release
#define IHolographicSpaceStatics2_GetIids __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2_GetIids
#define IHolographicSpaceStatics2_GetRuntimeClassName __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2_GetRuntimeClassName
#define IHolographicSpaceStatics2_GetTrustLevel __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2_GetTrustLevel
#define IHolographicSpaceStatics2_get_IsSupported __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2_get_IsSupported
#define IHolographicSpaceStatics2_get_IsAvailable __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2_get_IsAvailable
#define IHolographicSpaceStatics2_add_IsAvailableChanged __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2_add_IsAvailableChanged
#define IHolographicSpaceStatics2_remove_IsAvailableChanged __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2_remove_IsAvailableChanged
#endif /* WIDL_using_Windows_Graphics_Holographic */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics2_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000 */

/*****************************************************************************
 * IHolographicSpaceStatics3 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000
#ifndef ____x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics3_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics3_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics3, 0x3b00de3d, 0xb1a3, 0x4dfe, 0x8e,0x79, 0xfe,0xc5,0x90,0x9e,0x6d,0xf8);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Holographic {
                MIDL_INTERFACE("3b00de3d-b1a3-4dfe-8e79-fec5909e6df8")
                IHolographicSpaceStatics3 : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_IsConfigured(
                        boolean *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics3, 0x3b00de3d, 0xb1a3, 0x4dfe, 0x8e,0x79, 0xfe,0xc5,0x90,0x9e,0x6d,0xf8)
#endif
#else
typedef struct __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics3Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics3 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics3 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics3 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics3 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics3 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics3 *This,
        TrustLevel *trustLevel);

    /*** IHolographicSpaceStatics3 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_IsConfigured)(
        __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics3 *This,
        boolean *value);

    END_INTERFACE
} __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics3Vtbl;

interface __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics3 {
    CONST_VTBL __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics3Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics3_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics3_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics3_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics3_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics3_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics3_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IHolographicSpaceStatics3 methods ***/
#define __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics3_get_IsConfigured(This,value) (This)->lpVtbl->get_IsConfigured(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics3_QueryInterface(__x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics3* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics3_AddRef(__x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics3* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics3_Release(__x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics3* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics3_GetIids(__x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics3* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics3_GetRuntimeClassName(__x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics3* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics3_GetTrustLevel(__x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics3* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IHolographicSpaceStatics3 methods ***/
static inline HRESULT __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics3_get_IsConfigured(__x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics3* This,boolean *value) {
    return This->lpVtbl->get_IsConfigured(This,value);
}
#endif
#ifdef WIDL_using_Windows_Graphics_Holographic
#define IID_IHolographicSpaceStatics3 IID___x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics3
#define IHolographicSpaceStatics3Vtbl __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics3Vtbl
#define IHolographicSpaceStatics3 __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics3
#define IHolographicSpaceStatics3_QueryInterface __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics3_QueryInterface
#define IHolographicSpaceStatics3_AddRef __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics3_AddRef
#define IHolographicSpaceStatics3_Release __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics3_Release
#define IHolographicSpaceStatics3_GetIids __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics3_GetIids
#define IHolographicSpaceStatics3_GetRuntimeClassName __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics3_GetRuntimeClassName
#define IHolographicSpaceStatics3_GetTrustLevel __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics3_GetTrustLevel
#define IHolographicSpaceStatics3_get_IsConfigured __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics3_get_IsConfigured
#endif /* WIDL_using_Windows_Graphics_Holographic */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceStatics3_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000 */

/*****************************************************************************
 * IHolographicSpaceCameraAddedEventArgs interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000
#ifndef ____x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraAddedEventArgs_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraAddedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraAddedEventArgs, 0x58f1da35, 0xbbb3, 0x3c8f, 0x99,0x3d, 0x6c,0x80,0xe7,0xfe,0xb9,0x9f);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Holographic {
                MIDL_INTERFACE("58f1da35-bbb3-3c8f-993d-6c80e7feb99f")
                IHolographicSpaceCameraAddedEventArgs : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_Camera(
                        ABI::Windows::Graphics::Holographic::IHolographicCamera **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetDeferral(
                        ABI::Windows::Foundation::IDeferral **value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraAddedEventArgs, 0x58f1da35, 0xbbb3, 0x3c8f, 0x99,0x3d, 0x6c,0x80,0xe7,0xfe,0xb9,0x9f)
#endif
#else
typedef struct __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraAddedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraAddedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraAddedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraAddedEventArgs *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraAddedEventArgs *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraAddedEventArgs *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraAddedEventArgs *This,
        TrustLevel *trustLevel);

    /*** IHolographicSpaceCameraAddedEventArgs methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Camera)(
        __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraAddedEventArgs *This,
        __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicCamera **value);

    HRESULT (STDMETHODCALLTYPE *GetDeferral)(
        __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraAddedEventArgs *This,
        __x_ABI_CWindows_CFoundation_CIDeferral **value);

    END_INTERFACE
} __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraAddedEventArgsVtbl;

interface __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraAddedEventArgs {
    CONST_VTBL __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraAddedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraAddedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraAddedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraAddedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraAddedEventArgs_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraAddedEventArgs_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraAddedEventArgs_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IHolographicSpaceCameraAddedEventArgs methods ***/
#define __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraAddedEventArgs_get_Camera(This,value) (This)->lpVtbl->get_Camera(This,value)
#define __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraAddedEventArgs_GetDeferral(This,value) (This)->lpVtbl->GetDeferral(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraAddedEventArgs_QueryInterface(__x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraAddedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraAddedEventArgs_AddRef(__x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraAddedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraAddedEventArgs_Release(__x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraAddedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraAddedEventArgs_GetIids(__x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraAddedEventArgs* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraAddedEventArgs_GetRuntimeClassName(__x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraAddedEventArgs* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraAddedEventArgs_GetTrustLevel(__x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraAddedEventArgs* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IHolographicSpaceCameraAddedEventArgs methods ***/
static inline HRESULT __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraAddedEventArgs_get_Camera(__x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraAddedEventArgs* This,__x_ABI_CWindows_CGraphics_CHolographic_CIHolographicCamera **value) {
    return This->lpVtbl->get_Camera(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraAddedEventArgs_GetDeferral(__x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraAddedEventArgs* This,__x_ABI_CWindows_CFoundation_CIDeferral **value) {
    return This->lpVtbl->GetDeferral(This,value);
}
#endif
#ifdef WIDL_using_Windows_Graphics_Holographic
#define IID_IHolographicSpaceCameraAddedEventArgs IID___x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraAddedEventArgs
#define IHolographicSpaceCameraAddedEventArgsVtbl __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraAddedEventArgsVtbl
#define IHolographicSpaceCameraAddedEventArgs __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraAddedEventArgs
#define IHolographicSpaceCameraAddedEventArgs_QueryInterface __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraAddedEventArgs_QueryInterface
#define IHolographicSpaceCameraAddedEventArgs_AddRef __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraAddedEventArgs_AddRef
#define IHolographicSpaceCameraAddedEventArgs_Release __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraAddedEventArgs_Release
#define IHolographicSpaceCameraAddedEventArgs_GetIids __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraAddedEventArgs_GetIids
#define IHolographicSpaceCameraAddedEventArgs_GetRuntimeClassName __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraAddedEventArgs_GetRuntimeClassName
#define IHolographicSpaceCameraAddedEventArgs_GetTrustLevel __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraAddedEventArgs_GetTrustLevel
#define IHolographicSpaceCameraAddedEventArgs_get_Camera __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraAddedEventArgs_get_Camera
#define IHolographicSpaceCameraAddedEventArgs_GetDeferral __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraAddedEventArgs_GetDeferral
#endif /* WIDL_using_Windows_Graphics_Holographic */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraAddedEventArgs_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000 */

/*****************************************************************************
 * IHolographicSpaceCameraRemovedEventArgs interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000
#ifndef ____x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraRemovedEventArgs_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraRemovedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraRemovedEventArgs, 0x805444a8, 0xf2ae, 0x322e, 0x8d,0xa9, 0x83,0x6a,0x0a,0x95,0xa4,0xc1);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Holographic {
                MIDL_INTERFACE("805444a8-f2ae-322e-8da9-836a0a95a4c1")
                IHolographicSpaceCameraRemovedEventArgs : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_Camera(
                        ABI::Windows::Graphics::Holographic::IHolographicCamera **value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraRemovedEventArgs, 0x805444a8, 0xf2ae, 0x322e, 0x8d,0xa9, 0x83,0x6a,0x0a,0x95,0xa4,0xc1)
#endif
#else
typedef struct __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraRemovedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraRemovedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraRemovedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraRemovedEventArgs *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraRemovedEventArgs *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraRemovedEventArgs *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraRemovedEventArgs *This,
        TrustLevel *trustLevel);

    /*** IHolographicSpaceCameraRemovedEventArgs methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Camera)(
        __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraRemovedEventArgs *This,
        __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicCamera **value);

    END_INTERFACE
} __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraRemovedEventArgsVtbl;

interface __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraRemovedEventArgs {
    CONST_VTBL __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraRemovedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraRemovedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraRemovedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraRemovedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraRemovedEventArgs_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraRemovedEventArgs_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraRemovedEventArgs_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IHolographicSpaceCameraRemovedEventArgs methods ***/
#define __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraRemovedEventArgs_get_Camera(This,value) (This)->lpVtbl->get_Camera(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraRemovedEventArgs_QueryInterface(__x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraRemovedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraRemovedEventArgs_AddRef(__x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraRemovedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraRemovedEventArgs_Release(__x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraRemovedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraRemovedEventArgs_GetIids(__x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraRemovedEventArgs* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraRemovedEventArgs_GetRuntimeClassName(__x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraRemovedEventArgs* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraRemovedEventArgs_GetTrustLevel(__x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraRemovedEventArgs* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IHolographicSpaceCameraRemovedEventArgs methods ***/
static inline HRESULT __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraRemovedEventArgs_get_Camera(__x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraRemovedEventArgs* This,__x_ABI_CWindows_CGraphics_CHolographic_CIHolographicCamera **value) {
    return This->lpVtbl->get_Camera(This,value);
}
#endif
#ifdef WIDL_using_Windows_Graphics_Holographic
#define IID_IHolographicSpaceCameraRemovedEventArgs IID___x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraRemovedEventArgs
#define IHolographicSpaceCameraRemovedEventArgsVtbl __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraRemovedEventArgsVtbl
#define IHolographicSpaceCameraRemovedEventArgs __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraRemovedEventArgs
#define IHolographicSpaceCameraRemovedEventArgs_QueryInterface __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraRemovedEventArgs_QueryInterface
#define IHolographicSpaceCameraRemovedEventArgs_AddRef __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraRemovedEventArgs_AddRef
#define IHolographicSpaceCameraRemovedEventArgs_Release __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraRemovedEventArgs_Release
#define IHolographicSpaceCameraRemovedEventArgs_GetIids __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraRemovedEventArgs_GetIids
#define IHolographicSpaceCameraRemovedEventArgs_GetRuntimeClassName __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraRemovedEventArgs_GetRuntimeClassName
#define IHolographicSpaceCameraRemovedEventArgs_GetTrustLevel __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraRemovedEventArgs_GetTrustLevel
#define IHolographicSpaceCameraRemovedEventArgs_get_Camera __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraRemovedEventArgs_get_Camera
#endif /* WIDL_using_Windows_Graphics_Holographic */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraRemovedEventArgs_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000 */

/*
 * Class Windows.Graphics.Holographic.HolographicCamera
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000
#ifndef RUNTIMECLASS_Windows_Graphics_Holographic_HolographicCamera_DEFINED
#define RUNTIMECLASS_Windows_Graphics_Holographic_HolographicCamera_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Graphics_Holographic_HolographicCamera[] = {'W','i','n','d','o','w','s','.','G','r','a','p','h','i','c','s','.','H','o','l','o','g','r','a','p','h','i','c','.','H','o','l','o','g','r','a','p','h','i','c','C','a','m','e','r','a',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Graphics_Holographic_HolographicCamera[] = L"Windows.Graphics.Holographic.HolographicCamera";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Graphics_Holographic_HolographicCamera[] = {'W','i','n','d','o','w','s','.','G','r','a','p','h','i','c','s','.','H','o','l','o','g','r','a','p','h','i','c','.','H','o','l','o','g','r','a','p','h','i','c','C','a','m','e','r','a',0};
#endif
#endif /* RUNTIMECLASS_Windows_Graphics_Holographic_HolographicCamera_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000 */

/*
 * Class Windows.Graphics.Holographic.HolographicFrame
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000
#ifndef RUNTIMECLASS_Windows_Graphics_Holographic_HolographicFrame_DEFINED
#define RUNTIMECLASS_Windows_Graphics_Holographic_HolographicFrame_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Graphics_Holographic_HolographicFrame[] = {'W','i','n','d','o','w','s','.','G','r','a','p','h','i','c','s','.','H','o','l','o','g','r','a','p','h','i','c','.','H','o','l','o','g','r','a','p','h','i','c','F','r','a','m','e',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Graphics_Holographic_HolographicFrame[] = L"Windows.Graphics.Holographic.HolographicFrame";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Graphics_Holographic_HolographicFrame[] = {'W','i','n','d','o','w','s','.','G','r','a','p','h','i','c','s','.','H','o','l','o','g','r','a','p','h','i','c','.','H','o','l','o','g','r','a','p','h','i','c','F','r','a','m','e',0};
#endif
#endif /* RUNTIMECLASS_Windows_Graphics_Holographic_HolographicFrame_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000 */

/*
 * Class Windows.Graphics.Holographic.HolographicSpace
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000
#ifndef RUNTIMECLASS_Windows_Graphics_Holographic_HolographicSpace_DEFINED
#define RUNTIMECLASS_Windows_Graphics_Holographic_HolographicSpace_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Graphics_Holographic_HolographicSpace[] = {'W','i','n','d','o','w','s','.','G','r','a','p','h','i','c','s','.','H','o','l','o','g','r','a','p','h','i','c','.','H','o','l','o','g','r','a','p','h','i','c','S','p','a','c','e',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Graphics_Holographic_HolographicSpace[] = L"Windows.Graphics.Holographic.HolographicSpace";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Graphics_Holographic_HolographicSpace[] = {'W','i','n','d','o','w','s','.','G','r','a','p','h','i','c','s','.','H','o','l','o','g','r','a','p','h','i','c','.','H','o','l','o','g','r','a','p','h','i','c','S','p','a','c','e',0};
#endif
#endif /* RUNTIMECLASS_Windows_Graphics_Holographic_HolographicSpace_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000 */

/*
 * Class Windows.Graphics.Holographic.HolographicSpaceCameraAddedEventArgs
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000
#ifndef RUNTIMECLASS_Windows_Graphics_Holographic_HolographicSpaceCameraAddedEventArgs_DEFINED
#define RUNTIMECLASS_Windows_Graphics_Holographic_HolographicSpaceCameraAddedEventArgs_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Graphics_Holographic_HolographicSpaceCameraAddedEventArgs[] = {'W','i','n','d','o','w','s','.','G','r','a','p','h','i','c','s','.','H','o','l','o','g','r','a','p','h','i','c','.','H','o','l','o','g','r','a','p','h','i','c','S','p','a','c','e','C','a','m','e','r','a','A','d','d','e','d','E','v','e','n','t','A','r','g','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Graphics_Holographic_HolographicSpaceCameraAddedEventArgs[] = L"Windows.Graphics.Holographic.HolographicSpaceCameraAddedEventArgs";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Graphics_Holographic_HolographicSpaceCameraAddedEventArgs[] = {'W','i','n','d','o','w','s','.','G','r','a','p','h','i','c','s','.','H','o','l','o','g','r','a','p','h','i','c','.','H','o','l','o','g','r','a','p','h','i','c','S','p','a','c','e','C','a','m','e','r','a','A','d','d','e','d','E','v','e','n','t','A','r','g','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_Graphics_Holographic_HolographicSpaceCameraAddedEventArgs_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000 */

/*
 * Class Windows.Graphics.Holographic.HolographicSpaceCameraRemovedEventArgs
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000
#ifndef RUNTIMECLASS_Windows_Graphics_Holographic_HolographicSpaceCameraRemovedEventArgs_DEFINED
#define RUNTIMECLASS_Windows_Graphics_Holographic_HolographicSpaceCameraRemovedEventArgs_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Graphics_Holographic_HolographicSpaceCameraRemovedEventArgs[] = {'W','i','n','d','o','w','s','.','G','r','a','p','h','i','c','s','.','H','o','l','o','g','r','a','p','h','i','c','.','H','o','l','o','g','r','a','p','h','i','c','S','p','a','c','e','C','a','m','e','r','a','R','e','m','o','v','e','d','E','v','e','n','t','A','r','g','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Graphics_Holographic_HolographicSpaceCameraRemovedEventArgs[] = L"Windows.Graphics.Holographic.HolographicSpaceCameraRemovedEventArgs";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Graphics_Holographic_HolographicSpaceCameraRemovedEventArgs[] = {'W','i','n','d','o','w','s','.','G','r','a','p','h','i','c','s','.','H','o','l','o','g','r','a','p','h','i','c','.','H','o','l','o','g','r','a','p','h','i','c','S','p','a','c','e','C','a','m','e','r','a','R','e','m','o','v','e','d','E','v','e','n','t','A','r','g','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_Graphics_Holographic_HolographicSpaceCameraRemovedEventArgs_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000 */

/*****************************************************************************
 * ITypedEventHandler<ABI::Windows::Graphics::Holographic::HolographicSpace*,IInspectable* > interface
 */
#ifndef ____FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_IInspectable_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_IInspectable_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_IInspectable, 0x67aae2f2, 0x42d8, 0x5503, 0x91,0x31, 0xde,0xeb,0x45,0xa6,0xca,0x03);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("67aae2f2-42d8-5503-9131-deeb45a6ca03")
            ITypedEventHandler<ABI::Windows::Graphics::Holographic::HolographicSpace*,IInspectable* > : ITypedEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Graphics::Holographic::HolographicSpace*, ABI::Windows::Graphics::Holographic::IHolographicSpace* >, IInspectable* >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_IInspectable, 0x67aae2f2, 0x42d8, 0x5503, 0x91,0x31, 0xde,0xeb,0x45,0xa6,0xca,0x03)
#endif
#else
typedef struct __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_IInspectableVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_IInspectable *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_IInspectable *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_IInspectable *This);

    /*** ITypedEventHandler<ABI::Windows::Graphics::Holographic::HolographicSpace*,IInspectable* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_IInspectable *This,
        __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace *sender,
        IInspectable *args);

    END_INTERFACE
} __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_IInspectableVtbl;

interface __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_IInspectable {
    CONST_VTBL __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_IInspectableVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_IInspectable_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_IInspectable_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_IInspectable_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<ABI::Windows::Graphics::Holographic::HolographicSpace*,IInspectable* > methods ***/
#define __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_IInspectable_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_IInspectable_QueryInterface(__FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_IInspectable* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_IInspectable_AddRef(__FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_IInspectable* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_IInspectable_Release(__FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_IInspectable* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<ABI::Windows::Graphics::Holographic::HolographicSpace*,IInspectable* > methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_IInspectable_Invoke(__FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_IInspectable* This,__x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace *sender,IInspectable *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_HolographicSpace_IInspectable IID___FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_IInspectable
#define ITypedEventHandler_HolographicSpace_IInspectableVtbl __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_IInspectableVtbl
#define ITypedEventHandler_HolographicSpace_IInspectable __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_IInspectable
#define ITypedEventHandler_HolographicSpace_IInspectable_QueryInterface __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_IInspectable_QueryInterface
#define ITypedEventHandler_HolographicSpace_IInspectable_AddRef __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_IInspectable_AddRef
#define ITypedEventHandler_HolographicSpace_IInspectable_Release __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_IInspectable_Release
#define ITypedEventHandler_HolographicSpace_IInspectable_Invoke __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_IInspectable_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_IInspectable_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITypedEventHandler<ABI::Windows::Graphics::Holographic::HolographicSpace*,ABI::Windows::Graphics::Holographic::HolographicSpaceCameraAddedEventArgs* > interface
 */
#ifndef ____FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraAddedEventArgs_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraAddedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraAddedEventArgs, 0x71d80b7c, 0x1d27, 0x5102, 0x83,0xd1, 0x4f,0x0e,0xfc,0x7c,0x9d,0x6f);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("71d80b7c-1d27-5102-83d1-4f0efc7c9d6f")
            ITypedEventHandler<ABI::Windows::Graphics::Holographic::HolographicSpace*,ABI::Windows::Graphics::Holographic::HolographicSpaceCameraAddedEventArgs* > : ITypedEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Graphics::Holographic::HolographicSpace*, ABI::Windows::Graphics::Holographic::IHolographicSpace* >, ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Graphics::Holographic::HolographicSpaceCameraAddedEventArgs*, ABI::Windows::Graphics::Holographic::IHolographicSpaceCameraAddedEventArgs* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraAddedEventArgs, 0x71d80b7c, 0x1d27, 0x5102, 0x83,0xd1, 0x4f,0x0e,0xfc,0x7c,0x9d,0x6f)
#endif
#else
typedef struct __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraAddedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraAddedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraAddedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraAddedEventArgs *This);

    /*** ITypedEventHandler<ABI::Windows::Graphics::Holographic::HolographicSpace*,ABI::Windows::Graphics::Holographic::HolographicSpaceCameraAddedEventArgs* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraAddedEventArgs *This,
        __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace *sender,
        __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraAddedEventArgs *args);

    END_INTERFACE
} __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraAddedEventArgsVtbl;

interface __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraAddedEventArgs {
    CONST_VTBL __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraAddedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraAddedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraAddedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraAddedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<ABI::Windows::Graphics::Holographic::HolographicSpace*,ABI::Windows::Graphics::Holographic::HolographicSpaceCameraAddedEventArgs* > methods ***/
#define __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraAddedEventArgs_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraAddedEventArgs_QueryInterface(__FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraAddedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraAddedEventArgs_AddRef(__FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraAddedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraAddedEventArgs_Release(__FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraAddedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<ABI::Windows::Graphics::Holographic::HolographicSpace*,ABI::Windows::Graphics::Holographic::HolographicSpaceCameraAddedEventArgs* > methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraAddedEventArgs_Invoke(__FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraAddedEventArgs* This,__x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace *sender,__x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraAddedEventArgs *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_HolographicSpace_HolographicSpaceCameraAddedEventArgs IID___FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraAddedEventArgs
#define ITypedEventHandler_HolographicSpace_HolographicSpaceCameraAddedEventArgsVtbl __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraAddedEventArgsVtbl
#define ITypedEventHandler_HolographicSpace_HolographicSpaceCameraAddedEventArgs __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraAddedEventArgs
#define ITypedEventHandler_HolographicSpace_HolographicSpaceCameraAddedEventArgs_QueryInterface __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraAddedEventArgs_QueryInterface
#define ITypedEventHandler_HolographicSpace_HolographicSpaceCameraAddedEventArgs_AddRef __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraAddedEventArgs_AddRef
#define ITypedEventHandler_HolographicSpace_HolographicSpaceCameraAddedEventArgs_Release __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraAddedEventArgs_Release
#define ITypedEventHandler_HolographicSpace_HolographicSpaceCameraAddedEventArgs_Invoke __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraAddedEventArgs_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraAddedEventArgs_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITypedEventHandler<ABI::Windows::Graphics::Holographic::HolographicSpace*,ABI::Windows::Graphics::Holographic::HolographicSpaceCameraRemovedEventArgs* > interface
 */
#ifndef ____FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraRemovedEventArgs_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraRemovedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraRemovedEventArgs, 0xdb68cfc3, 0x0874, 0x502a, 0xa3,0xb9, 0x2b,0x1f,0xe8,0x6c,0x67,0xbe);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("db68cfc3-0874-502a-a3b9-2b1fe86c67be")
            ITypedEventHandler<ABI::Windows::Graphics::Holographic::HolographicSpace*,ABI::Windows::Graphics::Holographic::HolographicSpaceCameraRemovedEventArgs* > : ITypedEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Graphics::Holographic::HolographicSpace*, ABI::Windows::Graphics::Holographic::IHolographicSpace* >, ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Graphics::Holographic::HolographicSpaceCameraRemovedEventArgs*, ABI::Windows::Graphics::Holographic::IHolographicSpaceCameraRemovedEventArgs* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraRemovedEventArgs, 0xdb68cfc3, 0x0874, 0x502a, 0xa3,0xb9, 0x2b,0x1f,0xe8,0x6c,0x67,0xbe)
#endif
#else
typedef struct __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraRemovedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraRemovedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraRemovedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraRemovedEventArgs *This);

    /*** ITypedEventHandler<ABI::Windows::Graphics::Holographic::HolographicSpace*,ABI::Windows::Graphics::Holographic::HolographicSpaceCameraRemovedEventArgs* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraRemovedEventArgs *This,
        __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace *sender,
        __x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraRemovedEventArgs *args);

    END_INTERFACE
} __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraRemovedEventArgsVtbl;

interface __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraRemovedEventArgs {
    CONST_VTBL __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraRemovedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraRemovedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraRemovedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraRemovedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<ABI::Windows::Graphics::Holographic::HolographicSpace*,ABI::Windows::Graphics::Holographic::HolographicSpaceCameraRemovedEventArgs* > methods ***/
#define __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraRemovedEventArgs_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraRemovedEventArgs_QueryInterface(__FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraRemovedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraRemovedEventArgs_AddRef(__FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraRemovedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraRemovedEventArgs_Release(__FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraRemovedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<ABI::Windows::Graphics::Holographic::HolographicSpace*,ABI::Windows::Graphics::Holographic::HolographicSpaceCameraRemovedEventArgs* > methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraRemovedEventArgs_Invoke(__FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraRemovedEventArgs* This,__x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpace *sender,__x_ABI_CWindows_CGraphics_CHolographic_CIHolographicSpaceCameraRemovedEventArgs *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_HolographicSpace_HolographicSpaceCameraRemovedEventArgs IID___FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraRemovedEventArgs
#define ITypedEventHandler_HolographicSpace_HolographicSpaceCameraRemovedEventArgsVtbl __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraRemovedEventArgsVtbl
#define ITypedEventHandler_HolographicSpace_HolographicSpaceCameraRemovedEventArgs __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraRemovedEventArgs
#define ITypedEventHandler_HolographicSpace_HolographicSpaceCameraRemovedEventArgs_QueryInterface __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraRemovedEventArgs_QueryInterface
#define ITypedEventHandler_HolographicSpace_HolographicSpaceCameraRemovedEventArgs_AddRef __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraRemovedEventArgs_AddRef
#define ITypedEventHandler_HolographicSpace_HolographicSpaceCameraRemovedEventArgs_Release __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraRemovedEventArgs_Release
#define ITypedEventHandler_HolographicSpace_HolographicSpaceCameraRemovedEventArgs_Invoke __FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraRemovedEventArgs_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_Windows__CGraphics__CHolographic__CHolographicSpace_Windows__CGraphics__CHolographic__CHolographicSpaceCameraRemovedEventArgs_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __windows_graphics_holographic_h__ */
