// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/framework/summary.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fsummary_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fsummary_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "xla/tsl/protobuf/histogram.pb.h"
#include "tensorflow/core/framework/tensor.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fframework_2fsummary_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fframework_2fsummary_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fframework_2fsummary_2eproto;
namespace tensorflow {
class Summary;
struct SummaryDefaultTypeInternal;
extern SummaryDefaultTypeInternal _Summary_default_instance_;
class SummaryDescription;
struct SummaryDescriptionDefaultTypeInternal;
extern SummaryDescriptionDefaultTypeInternal _SummaryDescription_default_instance_;
class SummaryMetadata;
struct SummaryMetadataDefaultTypeInternal;
extern SummaryMetadataDefaultTypeInternal _SummaryMetadata_default_instance_;
class SummaryMetadata_PluginData;
struct SummaryMetadata_PluginDataDefaultTypeInternal;
extern SummaryMetadata_PluginDataDefaultTypeInternal _SummaryMetadata_PluginData_default_instance_;
class Summary_Audio;
struct Summary_AudioDefaultTypeInternal;
extern Summary_AudioDefaultTypeInternal _Summary_Audio_default_instance_;
class Summary_Image;
struct Summary_ImageDefaultTypeInternal;
extern Summary_ImageDefaultTypeInternal _Summary_Image_default_instance_;
class Summary_Value;
struct Summary_ValueDefaultTypeInternal;
extern Summary_ValueDefaultTypeInternal _Summary_Value_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::Summary* Arena::CreateMaybeMessage<::tensorflow::Summary>(Arena*);
template<> ::tensorflow::SummaryDescription* Arena::CreateMaybeMessage<::tensorflow::SummaryDescription>(Arena*);
template<> ::tensorflow::SummaryMetadata* Arena::CreateMaybeMessage<::tensorflow::SummaryMetadata>(Arena*);
template<> ::tensorflow::SummaryMetadata_PluginData* Arena::CreateMaybeMessage<::tensorflow::SummaryMetadata_PluginData>(Arena*);
template<> ::tensorflow::Summary_Audio* Arena::CreateMaybeMessage<::tensorflow::Summary_Audio>(Arena*);
template<> ::tensorflow::Summary_Image* Arena::CreateMaybeMessage<::tensorflow::Summary_Image>(Arena*);
template<> ::tensorflow::Summary_Value* Arena::CreateMaybeMessage<::tensorflow::Summary_Value>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

enum DataClass : int {
  DATA_CLASS_UNKNOWN = 0,
  DATA_CLASS_SCALAR = 1,
  DATA_CLASS_TENSOR = 2,
  DATA_CLASS_BLOB_SEQUENCE = 3,
  DataClass_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  DataClass_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool DataClass_IsValid(int value);
constexpr DataClass DataClass_MIN = DATA_CLASS_UNKNOWN;
constexpr DataClass DataClass_MAX = DATA_CLASS_BLOB_SEQUENCE;
constexpr int DataClass_ARRAYSIZE = DataClass_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* DataClass_descriptor();
template<typename T>
inline const std::string& DataClass_Name(T enum_t_value) {
  static_assert(::std::is_same<T, DataClass>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function DataClass_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    DataClass_descriptor(), enum_t_value);
}
inline bool DataClass_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, DataClass* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<DataClass>(
    DataClass_descriptor(), name, value);
}
// ===================================================================

class SummaryDescription final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.SummaryDescription) */ {
 public:
  inline SummaryDescription() : SummaryDescription(nullptr) {}
  ~SummaryDescription() override;
  explicit PROTOBUF_CONSTEXPR SummaryDescription(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SummaryDescription(const SummaryDescription& from);
  SummaryDescription(SummaryDescription&& from) noexcept
    : SummaryDescription() {
    *this = ::std::move(from);
  }

  inline SummaryDescription& operator=(const SummaryDescription& from) {
    CopyFrom(from);
    return *this;
  }
  inline SummaryDescription& operator=(SummaryDescription&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SummaryDescription& default_instance() {
    return *internal_default_instance();
  }
  static inline const SummaryDescription* internal_default_instance() {
    return reinterpret_cast<const SummaryDescription*>(
               &_SummaryDescription_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(SummaryDescription& a, SummaryDescription& b) {
    a.Swap(&b);
  }
  inline void Swap(SummaryDescription* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SummaryDescription* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SummaryDescription* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SummaryDescription>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SummaryDescription& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const SummaryDescription& from) {
    SummaryDescription::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SummaryDescription* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.SummaryDescription";
  }
  protected:
  explicit SummaryDescription(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTypeHintFieldNumber = 1,
  };
  // string type_hint = 1;
  void clear_type_hint();
  const std::string& type_hint() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_type_hint(ArgT0&& arg0, ArgT... args);
  std::string* mutable_type_hint();
  PROTOBUF_NODISCARD std::string* release_type_hint();
  void set_allocated_type_hint(std::string* type_hint);
  private:
  const std::string& _internal_type_hint() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_type_hint(const std::string& value);
  std::string* _internal_mutable_type_hint();
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.SummaryDescription)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr type_hint_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fsummary_2eproto;
};
// -------------------------------------------------------------------

class SummaryMetadata_PluginData final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.SummaryMetadata.PluginData) */ {
 public:
  inline SummaryMetadata_PluginData() : SummaryMetadata_PluginData(nullptr) {}
  ~SummaryMetadata_PluginData() override;
  explicit PROTOBUF_CONSTEXPR SummaryMetadata_PluginData(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SummaryMetadata_PluginData(const SummaryMetadata_PluginData& from);
  SummaryMetadata_PluginData(SummaryMetadata_PluginData&& from) noexcept
    : SummaryMetadata_PluginData() {
    *this = ::std::move(from);
  }

  inline SummaryMetadata_PluginData& operator=(const SummaryMetadata_PluginData& from) {
    CopyFrom(from);
    return *this;
  }
  inline SummaryMetadata_PluginData& operator=(SummaryMetadata_PluginData&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SummaryMetadata_PluginData& default_instance() {
    return *internal_default_instance();
  }
  static inline const SummaryMetadata_PluginData* internal_default_instance() {
    return reinterpret_cast<const SummaryMetadata_PluginData*>(
               &_SummaryMetadata_PluginData_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(SummaryMetadata_PluginData& a, SummaryMetadata_PluginData& b) {
    a.Swap(&b);
  }
  inline void Swap(SummaryMetadata_PluginData* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SummaryMetadata_PluginData* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SummaryMetadata_PluginData* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SummaryMetadata_PluginData>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SummaryMetadata_PluginData& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const SummaryMetadata_PluginData& from) {
    SummaryMetadata_PluginData::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SummaryMetadata_PluginData* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.SummaryMetadata.PluginData";
  }
  protected:
  explicit SummaryMetadata_PluginData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPluginNameFieldNumber = 1,
    kContentFieldNumber = 2,
  };
  // string plugin_name = 1;
  void clear_plugin_name();
  const std::string& plugin_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_plugin_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_plugin_name();
  PROTOBUF_NODISCARD std::string* release_plugin_name();
  void set_allocated_plugin_name(std::string* plugin_name);
  private:
  const std::string& _internal_plugin_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_plugin_name(const std::string& value);
  std::string* _internal_mutable_plugin_name();
  public:

  // bytes content = 2;
  void clear_content();
  const std::string& content() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_content(ArgT0&& arg0, ArgT... args);
  std::string* mutable_content();
  PROTOBUF_NODISCARD std::string* release_content();
  void set_allocated_content(std::string* content);
  private:
  const std::string& _internal_content() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_content(const std::string& value);
  std::string* _internal_mutable_content();
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.SummaryMetadata.PluginData)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr plugin_name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr content_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fsummary_2eproto;
};
// -------------------------------------------------------------------

class SummaryMetadata final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.SummaryMetadata) */ {
 public:
  inline SummaryMetadata() : SummaryMetadata(nullptr) {}
  ~SummaryMetadata() override;
  explicit PROTOBUF_CONSTEXPR SummaryMetadata(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SummaryMetadata(const SummaryMetadata& from);
  SummaryMetadata(SummaryMetadata&& from) noexcept
    : SummaryMetadata() {
    *this = ::std::move(from);
  }

  inline SummaryMetadata& operator=(const SummaryMetadata& from) {
    CopyFrom(from);
    return *this;
  }
  inline SummaryMetadata& operator=(SummaryMetadata&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SummaryMetadata& default_instance() {
    return *internal_default_instance();
  }
  static inline const SummaryMetadata* internal_default_instance() {
    return reinterpret_cast<const SummaryMetadata*>(
               &_SummaryMetadata_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(SummaryMetadata& a, SummaryMetadata& b) {
    a.Swap(&b);
  }
  inline void Swap(SummaryMetadata* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SummaryMetadata* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SummaryMetadata* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SummaryMetadata>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SummaryMetadata& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const SummaryMetadata& from) {
    SummaryMetadata::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SummaryMetadata* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.SummaryMetadata";
  }
  protected:
  explicit SummaryMetadata(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef SummaryMetadata_PluginData PluginData;

  // accessors -------------------------------------------------------

  enum : int {
    kDisplayNameFieldNumber = 2,
    kSummaryDescriptionFieldNumber = 3,
    kPluginDataFieldNumber = 1,
    kDataClassFieldNumber = 4,
  };
  // string display_name = 2;
  void clear_display_name();
  const std::string& display_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_display_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_display_name();
  PROTOBUF_NODISCARD std::string* release_display_name();
  void set_allocated_display_name(std::string* display_name);
  private:
  const std::string& _internal_display_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_display_name(const std::string& value);
  std::string* _internal_mutable_display_name();
  public:

  // string summary_description = 3;
  void clear_summary_description();
  const std::string& summary_description() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_summary_description(ArgT0&& arg0, ArgT... args);
  std::string* mutable_summary_description();
  PROTOBUF_NODISCARD std::string* release_summary_description();
  void set_allocated_summary_description(std::string* summary_description);
  private:
  const std::string& _internal_summary_description() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_summary_description(const std::string& value);
  std::string* _internal_mutable_summary_description();
  public:

  // .tensorflow.SummaryMetadata.PluginData plugin_data = 1;
  bool has_plugin_data() const;
  private:
  bool _internal_has_plugin_data() const;
  public:
  void clear_plugin_data();
  const ::tensorflow::SummaryMetadata_PluginData& plugin_data() const;
  PROTOBUF_NODISCARD ::tensorflow::SummaryMetadata_PluginData* release_plugin_data();
  ::tensorflow::SummaryMetadata_PluginData* mutable_plugin_data();
  void set_allocated_plugin_data(::tensorflow::SummaryMetadata_PluginData* plugin_data);
  private:
  const ::tensorflow::SummaryMetadata_PluginData& _internal_plugin_data() const;
  ::tensorflow::SummaryMetadata_PluginData* _internal_mutable_plugin_data();
  public:
  void unsafe_arena_set_allocated_plugin_data(
      ::tensorflow::SummaryMetadata_PluginData* plugin_data);
  ::tensorflow::SummaryMetadata_PluginData* unsafe_arena_release_plugin_data();

  // .tensorflow.DataClass data_class = 4;
  void clear_data_class();
  ::tensorflow::DataClass data_class() const;
  void set_data_class(::tensorflow::DataClass value);
  private:
  ::tensorflow::DataClass _internal_data_class() const;
  void _internal_set_data_class(::tensorflow::DataClass value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.SummaryMetadata)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr display_name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr summary_description_;
    ::tensorflow::SummaryMetadata_PluginData* plugin_data_;
    int data_class_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fsummary_2eproto;
};
// -------------------------------------------------------------------

class Summary_Image final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.Summary.Image) */ {
 public:
  inline Summary_Image() : Summary_Image(nullptr) {}
  ~Summary_Image() override;
  explicit PROTOBUF_CONSTEXPR Summary_Image(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Summary_Image(const Summary_Image& from);
  Summary_Image(Summary_Image&& from) noexcept
    : Summary_Image() {
    *this = ::std::move(from);
  }

  inline Summary_Image& operator=(const Summary_Image& from) {
    CopyFrom(from);
    return *this;
  }
  inline Summary_Image& operator=(Summary_Image&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Summary_Image& default_instance() {
    return *internal_default_instance();
  }
  static inline const Summary_Image* internal_default_instance() {
    return reinterpret_cast<const Summary_Image*>(
               &_Summary_Image_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(Summary_Image& a, Summary_Image& b) {
    a.Swap(&b);
  }
  inline void Swap(Summary_Image* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Summary_Image* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Summary_Image* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Summary_Image>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Summary_Image& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Summary_Image& from) {
    Summary_Image::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Summary_Image* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.Summary.Image";
  }
  protected:
  explicit Summary_Image(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kEncodedImageStringFieldNumber = 4,
    kHeightFieldNumber = 1,
    kWidthFieldNumber = 2,
    kColorspaceFieldNumber = 3,
  };
  // bytes encoded_image_string = 4;
  void clear_encoded_image_string();
  const std::string& encoded_image_string() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_encoded_image_string(ArgT0&& arg0, ArgT... args);
  std::string* mutable_encoded_image_string();
  PROTOBUF_NODISCARD std::string* release_encoded_image_string();
  void set_allocated_encoded_image_string(std::string* encoded_image_string);
  private:
  const std::string& _internal_encoded_image_string() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_encoded_image_string(const std::string& value);
  std::string* _internal_mutable_encoded_image_string();
  public:

  // int32 height = 1;
  void clear_height();
  int32_t height() const;
  void set_height(int32_t value);
  private:
  int32_t _internal_height() const;
  void _internal_set_height(int32_t value);
  public:

  // int32 width = 2;
  void clear_width();
  int32_t width() const;
  void set_width(int32_t value);
  private:
  int32_t _internal_width() const;
  void _internal_set_width(int32_t value);
  public:

  // int32 colorspace = 3;
  void clear_colorspace();
  int32_t colorspace() const;
  void set_colorspace(int32_t value);
  private:
  int32_t _internal_colorspace() const;
  void _internal_set_colorspace(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.Summary.Image)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr encoded_image_string_;
    int32_t height_;
    int32_t width_;
    int32_t colorspace_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fsummary_2eproto;
};
// -------------------------------------------------------------------

class Summary_Audio final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.Summary.Audio) */ {
 public:
  inline Summary_Audio() : Summary_Audio(nullptr) {}
  ~Summary_Audio() override;
  explicit PROTOBUF_CONSTEXPR Summary_Audio(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Summary_Audio(const Summary_Audio& from);
  Summary_Audio(Summary_Audio&& from) noexcept
    : Summary_Audio() {
    *this = ::std::move(from);
  }

  inline Summary_Audio& operator=(const Summary_Audio& from) {
    CopyFrom(from);
    return *this;
  }
  inline Summary_Audio& operator=(Summary_Audio&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Summary_Audio& default_instance() {
    return *internal_default_instance();
  }
  static inline const Summary_Audio* internal_default_instance() {
    return reinterpret_cast<const Summary_Audio*>(
               &_Summary_Audio_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(Summary_Audio& a, Summary_Audio& b) {
    a.Swap(&b);
  }
  inline void Swap(Summary_Audio* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Summary_Audio* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Summary_Audio* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Summary_Audio>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Summary_Audio& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Summary_Audio& from) {
    Summary_Audio::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Summary_Audio* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.Summary.Audio";
  }
  protected:
  explicit Summary_Audio(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kEncodedAudioStringFieldNumber = 4,
    kContentTypeFieldNumber = 5,
    kNumChannelsFieldNumber = 2,
    kLengthFramesFieldNumber = 3,
    kSampleRateFieldNumber = 1,
  };
  // bytes encoded_audio_string = 4;
  void clear_encoded_audio_string();
  const std::string& encoded_audio_string() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_encoded_audio_string(ArgT0&& arg0, ArgT... args);
  std::string* mutable_encoded_audio_string();
  PROTOBUF_NODISCARD std::string* release_encoded_audio_string();
  void set_allocated_encoded_audio_string(std::string* encoded_audio_string);
  private:
  const std::string& _internal_encoded_audio_string() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_encoded_audio_string(const std::string& value);
  std::string* _internal_mutable_encoded_audio_string();
  public:

  // string content_type = 5;
  void clear_content_type();
  const std::string& content_type() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_content_type(ArgT0&& arg0, ArgT... args);
  std::string* mutable_content_type();
  PROTOBUF_NODISCARD std::string* release_content_type();
  void set_allocated_content_type(std::string* content_type);
  private:
  const std::string& _internal_content_type() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_content_type(const std::string& value);
  std::string* _internal_mutable_content_type();
  public:

  // int64 num_channels = 2;
  void clear_num_channels();
  int64_t num_channels() const;
  void set_num_channels(int64_t value);
  private:
  int64_t _internal_num_channels() const;
  void _internal_set_num_channels(int64_t value);
  public:

  // int64 length_frames = 3;
  void clear_length_frames();
  int64_t length_frames() const;
  void set_length_frames(int64_t value);
  private:
  int64_t _internal_length_frames() const;
  void _internal_set_length_frames(int64_t value);
  public:

  // float sample_rate = 1;
  void clear_sample_rate();
  float sample_rate() const;
  void set_sample_rate(float value);
  private:
  float _internal_sample_rate() const;
  void _internal_set_sample_rate(float value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.Summary.Audio)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr encoded_audio_string_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr content_type_;
    int64_t num_channels_;
    int64_t length_frames_;
    float sample_rate_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fsummary_2eproto;
};
// -------------------------------------------------------------------

class Summary_Value final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.Summary.Value) */ {
 public:
  inline Summary_Value() : Summary_Value(nullptr) {}
  ~Summary_Value() override;
  explicit PROTOBUF_CONSTEXPR Summary_Value(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Summary_Value(const Summary_Value& from);
  Summary_Value(Summary_Value&& from) noexcept
    : Summary_Value() {
    *this = ::std::move(from);
  }

  inline Summary_Value& operator=(const Summary_Value& from) {
    CopyFrom(from);
    return *this;
  }
  inline Summary_Value& operator=(Summary_Value&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Summary_Value& default_instance() {
    return *internal_default_instance();
  }
  enum ValueCase {
    kSimpleValue = 2,
    kObsoleteOldStyleHistogram = 3,
    kImage = 4,
    kHisto = 5,
    kAudio = 6,
    kTensor = 8,
    VALUE_NOT_SET = 0,
  };

  static inline const Summary_Value* internal_default_instance() {
    return reinterpret_cast<const Summary_Value*>(
               &_Summary_Value_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(Summary_Value& a, Summary_Value& b) {
    a.Swap(&b);
  }
  inline void Swap(Summary_Value* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Summary_Value* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Summary_Value* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Summary_Value>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Summary_Value& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Summary_Value& from) {
    Summary_Value::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Summary_Value* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.Summary.Value";
  }
  protected:
  explicit Summary_Value(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTagFieldNumber = 1,
    kNodeNameFieldNumber = 7,
    kMetadataFieldNumber = 9,
    kSimpleValueFieldNumber = 2,
    kObsoleteOldStyleHistogramFieldNumber = 3,
    kImageFieldNumber = 4,
    kHistoFieldNumber = 5,
    kAudioFieldNumber = 6,
    kTensorFieldNumber = 8,
  };
  // string tag = 1;
  void clear_tag();
  const std::string& tag() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_tag(ArgT0&& arg0, ArgT... args);
  std::string* mutable_tag();
  PROTOBUF_NODISCARD std::string* release_tag();
  void set_allocated_tag(std::string* tag);
  private:
  const std::string& _internal_tag() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_tag(const std::string& value);
  std::string* _internal_mutable_tag();
  public:

  // string node_name = 7;
  void clear_node_name();
  const std::string& node_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_node_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_node_name();
  PROTOBUF_NODISCARD std::string* release_node_name();
  void set_allocated_node_name(std::string* node_name);
  private:
  const std::string& _internal_node_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_node_name(const std::string& value);
  std::string* _internal_mutable_node_name();
  public:

  // .tensorflow.SummaryMetadata metadata = 9;
  bool has_metadata() const;
  private:
  bool _internal_has_metadata() const;
  public:
  void clear_metadata();
  const ::tensorflow::SummaryMetadata& metadata() const;
  PROTOBUF_NODISCARD ::tensorflow::SummaryMetadata* release_metadata();
  ::tensorflow::SummaryMetadata* mutable_metadata();
  void set_allocated_metadata(::tensorflow::SummaryMetadata* metadata);
  private:
  const ::tensorflow::SummaryMetadata& _internal_metadata() const;
  ::tensorflow::SummaryMetadata* _internal_mutable_metadata();
  public:
  void unsafe_arena_set_allocated_metadata(
      ::tensorflow::SummaryMetadata* metadata);
  ::tensorflow::SummaryMetadata* unsafe_arena_release_metadata();

  // float simple_value = 2;
  bool has_simple_value() const;
  private:
  bool _internal_has_simple_value() const;
  public:
  void clear_simple_value();
  float simple_value() const;
  void set_simple_value(float value);
  private:
  float _internal_simple_value() const;
  void _internal_set_simple_value(float value);
  public:

  // bytes obsolete_old_style_histogram = 3;
  bool has_obsolete_old_style_histogram() const;
  private:
  bool _internal_has_obsolete_old_style_histogram() const;
  public:
  void clear_obsolete_old_style_histogram();
  const std::string& obsolete_old_style_histogram() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_obsolete_old_style_histogram(ArgT0&& arg0, ArgT... args);
  std::string* mutable_obsolete_old_style_histogram();
  PROTOBUF_NODISCARD std::string* release_obsolete_old_style_histogram();
  void set_allocated_obsolete_old_style_histogram(std::string* obsolete_old_style_histogram);
  private:
  const std::string& _internal_obsolete_old_style_histogram() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_obsolete_old_style_histogram(const std::string& value);
  std::string* _internal_mutable_obsolete_old_style_histogram();
  public:

  // .tensorflow.Summary.Image image = 4;
  bool has_image() const;
  private:
  bool _internal_has_image() const;
  public:
  void clear_image();
  const ::tensorflow::Summary_Image& image() const;
  PROTOBUF_NODISCARD ::tensorflow::Summary_Image* release_image();
  ::tensorflow::Summary_Image* mutable_image();
  void set_allocated_image(::tensorflow::Summary_Image* image);
  private:
  const ::tensorflow::Summary_Image& _internal_image() const;
  ::tensorflow::Summary_Image* _internal_mutable_image();
  public:
  void unsafe_arena_set_allocated_image(
      ::tensorflow::Summary_Image* image);
  ::tensorflow::Summary_Image* unsafe_arena_release_image();

  // .tensorflow.HistogramProto histo = 5;
  bool has_histo() const;
  private:
  bool _internal_has_histo() const;
  public:
  void clear_histo();
  const ::tensorflow::HistogramProto& histo() const;
  PROTOBUF_NODISCARD ::tensorflow::HistogramProto* release_histo();
  ::tensorflow::HistogramProto* mutable_histo();
  void set_allocated_histo(::tensorflow::HistogramProto* histo);
  private:
  const ::tensorflow::HistogramProto& _internal_histo() const;
  ::tensorflow::HistogramProto* _internal_mutable_histo();
  public:
  void unsafe_arena_set_allocated_histo(
      ::tensorflow::HistogramProto* histo);
  ::tensorflow::HistogramProto* unsafe_arena_release_histo();

  // .tensorflow.Summary.Audio audio = 6;
  bool has_audio() const;
  private:
  bool _internal_has_audio() const;
  public:
  void clear_audio();
  const ::tensorflow::Summary_Audio& audio() const;
  PROTOBUF_NODISCARD ::tensorflow::Summary_Audio* release_audio();
  ::tensorflow::Summary_Audio* mutable_audio();
  void set_allocated_audio(::tensorflow::Summary_Audio* audio);
  private:
  const ::tensorflow::Summary_Audio& _internal_audio() const;
  ::tensorflow::Summary_Audio* _internal_mutable_audio();
  public:
  void unsafe_arena_set_allocated_audio(
      ::tensorflow::Summary_Audio* audio);
  ::tensorflow::Summary_Audio* unsafe_arena_release_audio();

  // .tensorflow.TensorProto tensor = 8;
  bool has_tensor() const;
  private:
  bool _internal_has_tensor() const;
  public:
  void clear_tensor();
  const ::tensorflow::TensorProto& tensor() const;
  PROTOBUF_NODISCARD ::tensorflow::TensorProto* release_tensor();
  ::tensorflow::TensorProto* mutable_tensor();
  void set_allocated_tensor(::tensorflow::TensorProto* tensor);
  private:
  const ::tensorflow::TensorProto& _internal_tensor() const;
  ::tensorflow::TensorProto* _internal_mutable_tensor();
  public:
  void unsafe_arena_set_allocated_tensor(
      ::tensorflow::TensorProto* tensor);
  ::tensorflow::TensorProto* unsafe_arena_release_tensor();

  void clear_value();
  ValueCase value_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.Summary.Value)
 private:
  class _Internal;
  void set_has_simple_value();
  void set_has_obsolete_old_style_histogram();
  void set_has_image();
  void set_has_histo();
  void set_has_audio();
  void set_has_tensor();

  inline bool has_value() const;
  inline void clear_has_value();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr tag_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr node_name_;
    ::tensorflow::SummaryMetadata* metadata_;
    union ValueUnion {
      constexpr ValueUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      float simple_value_;
      ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr obsolete_old_style_histogram_;
      ::tensorflow::Summary_Image* image_;
      ::tensorflow::HistogramProto* histo_;
      ::tensorflow::Summary_Audio* audio_;
      ::tensorflow::TensorProto* tensor_;
    } value_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    uint32_t _oneof_case_[1];

  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fsummary_2eproto;
};
// -------------------------------------------------------------------

class Summary final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.Summary) */ {
 public:
  inline Summary() : Summary(nullptr) {}
  ~Summary() override;
  explicit PROTOBUF_CONSTEXPR Summary(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Summary(const Summary& from);
  Summary(Summary&& from) noexcept
    : Summary() {
    *this = ::std::move(from);
  }

  inline Summary& operator=(const Summary& from) {
    CopyFrom(from);
    return *this;
  }
  inline Summary& operator=(Summary&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Summary& default_instance() {
    return *internal_default_instance();
  }
  static inline const Summary* internal_default_instance() {
    return reinterpret_cast<const Summary*>(
               &_Summary_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(Summary& a, Summary& b) {
    a.Swap(&b);
  }
  inline void Swap(Summary* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Summary* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Summary* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Summary>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Summary& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Summary& from) {
    Summary::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Summary* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.Summary";
  }
  protected:
  explicit Summary(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef Summary_Image Image;
  typedef Summary_Audio Audio;
  typedef Summary_Value Value;

  // accessors -------------------------------------------------------

  enum : int {
    kValueFieldNumber = 1,
  };
  // repeated .tensorflow.Summary.Value value = 1;
  int value_size() const;
  private:
  int _internal_value_size() const;
  public:
  void clear_value();
  ::tensorflow::Summary_Value* mutable_value(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::Summary_Value >*
      mutable_value();
  private:
  const ::tensorflow::Summary_Value& _internal_value(int index) const;
  ::tensorflow::Summary_Value* _internal_add_value();
  public:
  const ::tensorflow::Summary_Value& value(int index) const;
  ::tensorflow::Summary_Value* add_value();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::Summary_Value >&
      value() const;

  // @@protoc_insertion_point(class_scope:tensorflow.Summary)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::Summary_Value > value_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fsummary_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// SummaryDescription

// string type_hint = 1;
inline void SummaryDescription::clear_type_hint() {
  _impl_.type_hint_.ClearToEmpty();
}
inline const std::string& SummaryDescription::type_hint() const {
  // @@protoc_insertion_point(field_get:tensorflow.SummaryDescription.type_hint)
  return _internal_type_hint();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SummaryDescription::set_type_hint(ArgT0&& arg0, ArgT... args) {
 
 _impl_.type_hint_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.SummaryDescription.type_hint)
}
inline std::string* SummaryDescription::mutable_type_hint() {
  std::string* _s = _internal_mutable_type_hint();
  // @@protoc_insertion_point(field_mutable:tensorflow.SummaryDescription.type_hint)
  return _s;
}
inline const std::string& SummaryDescription::_internal_type_hint() const {
  return _impl_.type_hint_.Get();
}
inline void SummaryDescription::_internal_set_type_hint(const std::string& value) {
  
  _impl_.type_hint_.Set(value, GetArenaForAllocation());
}
inline std::string* SummaryDescription::_internal_mutable_type_hint() {
  
  return _impl_.type_hint_.Mutable(GetArenaForAllocation());
}
inline std::string* SummaryDescription::release_type_hint() {
  // @@protoc_insertion_point(field_release:tensorflow.SummaryDescription.type_hint)
  return _impl_.type_hint_.Release();
}
inline void SummaryDescription::set_allocated_type_hint(std::string* type_hint) {
  if (type_hint != nullptr) {
    
  } else {
    
  }
  _impl_.type_hint_.SetAllocated(type_hint, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.type_hint_.IsDefault()) {
    _impl_.type_hint_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SummaryDescription.type_hint)
}

// -------------------------------------------------------------------

// SummaryMetadata_PluginData

// string plugin_name = 1;
inline void SummaryMetadata_PluginData::clear_plugin_name() {
  _impl_.plugin_name_.ClearToEmpty();
}
inline const std::string& SummaryMetadata_PluginData::plugin_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.SummaryMetadata.PluginData.plugin_name)
  return _internal_plugin_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SummaryMetadata_PluginData::set_plugin_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.plugin_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.SummaryMetadata.PluginData.plugin_name)
}
inline std::string* SummaryMetadata_PluginData::mutable_plugin_name() {
  std::string* _s = _internal_mutable_plugin_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.SummaryMetadata.PluginData.plugin_name)
  return _s;
}
inline const std::string& SummaryMetadata_PluginData::_internal_plugin_name() const {
  return _impl_.plugin_name_.Get();
}
inline void SummaryMetadata_PluginData::_internal_set_plugin_name(const std::string& value) {
  
  _impl_.plugin_name_.Set(value, GetArenaForAllocation());
}
inline std::string* SummaryMetadata_PluginData::_internal_mutable_plugin_name() {
  
  return _impl_.plugin_name_.Mutable(GetArenaForAllocation());
}
inline std::string* SummaryMetadata_PluginData::release_plugin_name() {
  // @@protoc_insertion_point(field_release:tensorflow.SummaryMetadata.PluginData.plugin_name)
  return _impl_.plugin_name_.Release();
}
inline void SummaryMetadata_PluginData::set_allocated_plugin_name(std::string* plugin_name) {
  if (plugin_name != nullptr) {
    
  } else {
    
  }
  _impl_.plugin_name_.SetAllocated(plugin_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.plugin_name_.IsDefault()) {
    _impl_.plugin_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SummaryMetadata.PluginData.plugin_name)
}

// bytes content = 2;
inline void SummaryMetadata_PluginData::clear_content() {
  _impl_.content_.ClearToEmpty();
}
inline const std::string& SummaryMetadata_PluginData::content() const {
  // @@protoc_insertion_point(field_get:tensorflow.SummaryMetadata.PluginData.content)
  return _internal_content();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SummaryMetadata_PluginData::set_content(ArgT0&& arg0, ArgT... args) {
 
 _impl_.content_.SetBytes(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.SummaryMetadata.PluginData.content)
}
inline std::string* SummaryMetadata_PluginData::mutable_content() {
  std::string* _s = _internal_mutable_content();
  // @@protoc_insertion_point(field_mutable:tensorflow.SummaryMetadata.PluginData.content)
  return _s;
}
inline const std::string& SummaryMetadata_PluginData::_internal_content() const {
  return _impl_.content_.Get();
}
inline void SummaryMetadata_PluginData::_internal_set_content(const std::string& value) {
  
  _impl_.content_.Set(value, GetArenaForAllocation());
}
inline std::string* SummaryMetadata_PluginData::_internal_mutable_content() {
  
  return _impl_.content_.Mutable(GetArenaForAllocation());
}
inline std::string* SummaryMetadata_PluginData::release_content() {
  // @@protoc_insertion_point(field_release:tensorflow.SummaryMetadata.PluginData.content)
  return _impl_.content_.Release();
}
inline void SummaryMetadata_PluginData::set_allocated_content(std::string* content) {
  if (content != nullptr) {
    
  } else {
    
  }
  _impl_.content_.SetAllocated(content, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.content_.IsDefault()) {
    _impl_.content_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SummaryMetadata.PluginData.content)
}

// -------------------------------------------------------------------

// SummaryMetadata

// .tensorflow.SummaryMetadata.PluginData plugin_data = 1;
inline bool SummaryMetadata::_internal_has_plugin_data() const {
  return this != internal_default_instance() && _impl_.plugin_data_ != nullptr;
}
inline bool SummaryMetadata::has_plugin_data() const {
  return _internal_has_plugin_data();
}
inline void SummaryMetadata::clear_plugin_data() {
  if (GetArenaForAllocation() == nullptr && _impl_.plugin_data_ != nullptr) {
    delete _impl_.plugin_data_;
  }
  _impl_.plugin_data_ = nullptr;
}
inline const ::tensorflow::SummaryMetadata_PluginData& SummaryMetadata::_internal_plugin_data() const {
  const ::tensorflow::SummaryMetadata_PluginData* p = _impl_.plugin_data_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::SummaryMetadata_PluginData&>(
      ::tensorflow::_SummaryMetadata_PluginData_default_instance_);
}
inline const ::tensorflow::SummaryMetadata_PluginData& SummaryMetadata::plugin_data() const {
  // @@protoc_insertion_point(field_get:tensorflow.SummaryMetadata.plugin_data)
  return _internal_plugin_data();
}
inline void SummaryMetadata::unsafe_arena_set_allocated_plugin_data(
    ::tensorflow::SummaryMetadata_PluginData* plugin_data) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.plugin_data_);
  }
  _impl_.plugin_data_ = plugin_data;
  if (plugin_data) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SummaryMetadata.plugin_data)
}
inline ::tensorflow::SummaryMetadata_PluginData* SummaryMetadata::release_plugin_data() {
  
  ::tensorflow::SummaryMetadata_PluginData* temp = _impl_.plugin_data_;
  _impl_.plugin_data_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::SummaryMetadata_PluginData* SummaryMetadata::unsafe_arena_release_plugin_data() {
  // @@protoc_insertion_point(field_release:tensorflow.SummaryMetadata.plugin_data)
  
  ::tensorflow::SummaryMetadata_PluginData* temp = _impl_.plugin_data_;
  _impl_.plugin_data_ = nullptr;
  return temp;
}
inline ::tensorflow::SummaryMetadata_PluginData* SummaryMetadata::_internal_mutable_plugin_data() {
  
  if (_impl_.plugin_data_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::SummaryMetadata_PluginData>(GetArenaForAllocation());
    _impl_.plugin_data_ = p;
  }
  return _impl_.plugin_data_;
}
inline ::tensorflow::SummaryMetadata_PluginData* SummaryMetadata::mutable_plugin_data() {
  ::tensorflow::SummaryMetadata_PluginData* _msg = _internal_mutable_plugin_data();
  // @@protoc_insertion_point(field_mutable:tensorflow.SummaryMetadata.plugin_data)
  return _msg;
}
inline void SummaryMetadata::set_allocated_plugin_data(::tensorflow::SummaryMetadata_PluginData* plugin_data) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.plugin_data_;
  }
  if (plugin_data) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(plugin_data);
    if (message_arena != submessage_arena) {
      plugin_data = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, plugin_data, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.plugin_data_ = plugin_data;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SummaryMetadata.plugin_data)
}

// string display_name = 2;
inline void SummaryMetadata::clear_display_name() {
  _impl_.display_name_.ClearToEmpty();
}
inline const std::string& SummaryMetadata::display_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.SummaryMetadata.display_name)
  return _internal_display_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SummaryMetadata::set_display_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.display_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.SummaryMetadata.display_name)
}
inline std::string* SummaryMetadata::mutable_display_name() {
  std::string* _s = _internal_mutable_display_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.SummaryMetadata.display_name)
  return _s;
}
inline const std::string& SummaryMetadata::_internal_display_name() const {
  return _impl_.display_name_.Get();
}
inline void SummaryMetadata::_internal_set_display_name(const std::string& value) {
  
  _impl_.display_name_.Set(value, GetArenaForAllocation());
}
inline std::string* SummaryMetadata::_internal_mutable_display_name() {
  
  return _impl_.display_name_.Mutable(GetArenaForAllocation());
}
inline std::string* SummaryMetadata::release_display_name() {
  // @@protoc_insertion_point(field_release:tensorflow.SummaryMetadata.display_name)
  return _impl_.display_name_.Release();
}
inline void SummaryMetadata::set_allocated_display_name(std::string* display_name) {
  if (display_name != nullptr) {
    
  } else {
    
  }
  _impl_.display_name_.SetAllocated(display_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.display_name_.IsDefault()) {
    _impl_.display_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SummaryMetadata.display_name)
}

// string summary_description = 3;
inline void SummaryMetadata::clear_summary_description() {
  _impl_.summary_description_.ClearToEmpty();
}
inline const std::string& SummaryMetadata::summary_description() const {
  // @@protoc_insertion_point(field_get:tensorflow.SummaryMetadata.summary_description)
  return _internal_summary_description();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SummaryMetadata::set_summary_description(ArgT0&& arg0, ArgT... args) {
 
 _impl_.summary_description_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.SummaryMetadata.summary_description)
}
inline std::string* SummaryMetadata::mutable_summary_description() {
  std::string* _s = _internal_mutable_summary_description();
  // @@protoc_insertion_point(field_mutable:tensorflow.SummaryMetadata.summary_description)
  return _s;
}
inline const std::string& SummaryMetadata::_internal_summary_description() const {
  return _impl_.summary_description_.Get();
}
inline void SummaryMetadata::_internal_set_summary_description(const std::string& value) {
  
  _impl_.summary_description_.Set(value, GetArenaForAllocation());
}
inline std::string* SummaryMetadata::_internal_mutable_summary_description() {
  
  return _impl_.summary_description_.Mutable(GetArenaForAllocation());
}
inline std::string* SummaryMetadata::release_summary_description() {
  // @@protoc_insertion_point(field_release:tensorflow.SummaryMetadata.summary_description)
  return _impl_.summary_description_.Release();
}
inline void SummaryMetadata::set_allocated_summary_description(std::string* summary_description) {
  if (summary_description != nullptr) {
    
  } else {
    
  }
  _impl_.summary_description_.SetAllocated(summary_description, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.summary_description_.IsDefault()) {
    _impl_.summary_description_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SummaryMetadata.summary_description)
}

// .tensorflow.DataClass data_class = 4;
inline void SummaryMetadata::clear_data_class() {
  _impl_.data_class_ = 0;
}
inline ::tensorflow::DataClass SummaryMetadata::_internal_data_class() const {
  return static_cast< ::tensorflow::DataClass >(_impl_.data_class_);
}
inline ::tensorflow::DataClass SummaryMetadata::data_class() const {
  // @@protoc_insertion_point(field_get:tensorflow.SummaryMetadata.data_class)
  return _internal_data_class();
}
inline void SummaryMetadata::_internal_set_data_class(::tensorflow::DataClass value) {
  
  _impl_.data_class_ = value;
}
inline void SummaryMetadata::set_data_class(::tensorflow::DataClass value) {
  _internal_set_data_class(value);
  // @@protoc_insertion_point(field_set:tensorflow.SummaryMetadata.data_class)
}

// -------------------------------------------------------------------

// Summary_Image

// int32 height = 1;
inline void Summary_Image::clear_height() {
  _impl_.height_ = 0;
}
inline int32_t Summary_Image::_internal_height() const {
  return _impl_.height_;
}
inline int32_t Summary_Image::height() const {
  // @@protoc_insertion_point(field_get:tensorflow.Summary.Image.height)
  return _internal_height();
}
inline void Summary_Image::_internal_set_height(int32_t value) {
  
  _impl_.height_ = value;
}
inline void Summary_Image::set_height(int32_t value) {
  _internal_set_height(value);
  // @@protoc_insertion_point(field_set:tensorflow.Summary.Image.height)
}

// int32 width = 2;
inline void Summary_Image::clear_width() {
  _impl_.width_ = 0;
}
inline int32_t Summary_Image::_internal_width() const {
  return _impl_.width_;
}
inline int32_t Summary_Image::width() const {
  // @@protoc_insertion_point(field_get:tensorflow.Summary.Image.width)
  return _internal_width();
}
inline void Summary_Image::_internal_set_width(int32_t value) {
  
  _impl_.width_ = value;
}
inline void Summary_Image::set_width(int32_t value) {
  _internal_set_width(value);
  // @@protoc_insertion_point(field_set:tensorflow.Summary.Image.width)
}

// int32 colorspace = 3;
inline void Summary_Image::clear_colorspace() {
  _impl_.colorspace_ = 0;
}
inline int32_t Summary_Image::_internal_colorspace() const {
  return _impl_.colorspace_;
}
inline int32_t Summary_Image::colorspace() const {
  // @@protoc_insertion_point(field_get:tensorflow.Summary.Image.colorspace)
  return _internal_colorspace();
}
inline void Summary_Image::_internal_set_colorspace(int32_t value) {
  
  _impl_.colorspace_ = value;
}
inline void Summary_Image::set_colorspace(int32_t value) {
  _internal_set_colorspace(value);
  // @@protoc_insertion_point(field_set:tensorflow.Summary.Image.colorspace)
}

// bytes encoded_image_string = 4;
inline void Summary_Image::clear_encoded_image_string() {
  _impl_.encoded_image_string_.ClearToEmpty();
}
inline const std::string& Summary_Image::encoded_image_string() const {
  // @@protoc_insertion_point(field_get:tensorflow.Summary.Image.encoded_image_string)
  return _internal_encoded_image_string();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Summary_Image::set_encoded_image_string(ArgT0&& arg0, ArgT... args) {
 
 _impl_.encoded_image_string_.SetBytes(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.Summary.Image.encoded_image_string)
}
inline std::string* Summary_Image::mutable_encoded_image_string() {
  std::string* _s = _internal_mutable_encoded_image_string();
  // @@protoc_insertion_point(field_mutable:tensorflow.Summary.Image.encoded_image_string)
  return _s;
}
inline const std::string& Summary_Image::_internal_encoded_image_string() const {
  return _impl_.encoded_image_string_.Get();
}
inline void Summary_Image::_internal_set_encoded_image_string(const std::string& value) {
  
  _impl_.encoded_image_string_.Set(value, GetArenaForAllocation());
}
inline std::string* Summary_Image::_internal_mutable_encoded_image_string() {
  
  return _impl_.encoded_image_string_.Mutable(GetArenaForAllocation());
}
inline std::string* Summary_Image::release_encoded_image_string() {
  // @@protoc_insertion_point(field_release:tensorflow.Summary.Image.encoded_image_string)
  return _impl_.encoded_image_string_.Release();
}
inline void Summary_Image::set_allocated_encoded_image_string(std::string* encoded_image_string) {
  if (encoded_image_string != nullptr) {
    
  } else {
    
  }
  _impl_.encoded_image_string_.SetAllocated(encoded_image_string, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.encoded_image_string_.IsDefault()) {
    _impl_.encoded_image_string_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.Summary.Image.encoded_image_string)
}

// -------------------------------------------------------------------

// Summary_Audio

// float sample_rate = 1;
inline void Summary_Audio::clear_sample_rate() {
  _impl_.sample_rate_ = 0;
}
inline float Summary_Audio::_internal_sample_rate() const {
  return _impl_.sample_rate_;
}
inline float Summary_Audio::sample_rate() const {
  // @@protoc_insertion_point(field_get:tensorflow.Summary.Audio.sample_rate)
  return _internal_sample_rate();
}
inline void Summary_Audio::_internal_set_sample_rate(float value) {
  
  _impl_.sample_rate_ = value;
}
inline void Summary_Audio::set_sample_rate(float value) {
  _internal_set_sample_rate(value);
  // @@protoc_insertion_point(field_set:tensorflow.Summary.Audio.sample_rate)
}

// int64 num_channels = 2;
inline void Summary_Audio::clear_num_channels() {
  _impl_.num_channels_ = int64_t{0};
}
inline int64_t Summary_Audio::_internal_num_channels() const {
  return _impl_.num_channels_;
}
inline int64_t Summary_Audio::num_channels() const {
  // @@protoc_insertion_point(field_get:tensorflow.Summary.Audio.num_channels)
  return _internal_num_channels();
}
inline void Summary_Audio::_internal_set_num_channels(int64_t value) {
  
  _impl_.num_channels_ = value;
}
inline void Summary_Audio::set_num_channels(int64_t value) {
  _internal_set_num_channels(value);
  // @@protoc_insertion_point(field_set:tensorflow.Summary.Audio.num_channels)
}

// int64 length_frames = 3;
inline void Summary_Audio::clear_length_frames() {
  _impl_.length_frames_ = int64_t{0};
}
inline int64_t Summary_Audio::_internal_length_frames() const {
  return _impl_.length_frames_;
}
inline int64_t Summary_Audio::length_frames() const {
  // @@protoc_insertion_point(field_get:tensorflow.Summary.Audio.length_frames)
  return _internal_length_frames();
}
inline void Summary_Audio::_internal_set_length_frames(int64_t value) {
  
  _impl_.length_frames_ = value;
}
inline void Summary_Audio::set_length_frames(int64_t value) {
  _internal_set_length_frames(value);
  // @@protoc_insertion_point(field_set:tensorflow.Summary.Audio.length_frames)
}

// bytes encoded_audio_string = 4;
inline void Summary_Audio::clear_encoded_audio_string() {
  _impl_.encoded_audio_string_.ClearToEmpty();
}
inline const std::string& Summary_Audio::encoded_audio_string() const {
  // @@protoc_insertion_point(field_get:tensorflow.Summary.Audio.encoded_audio_string)
  return _internal_encoded_audio_string();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Summary_Audio::set_encoded_audio_string(ArgT0&& arg0, ArgT... args) {
 
 _impl_.encoded_audio_string_.SetBytes(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.Summary.Audio.encoded_audio_string)
}
inline std::string* Summary_Audio::mutable_encoded_audio_string() {
  std::string* _s = _internal_mutable_encoded_audio_string();
  // @@protoc_insertion_point(field_mutable:tensorflow.Summary.Audio.encoded_audio_string)
  return _s;
}
inline const std::string& Summary_Audio::_internal_encoded_audio_string() const {
  return _impl_.encoded_audio_string_.Get();
}
inline void Summary_Audio::_internal_set_encoded_audio_string(const std::string& value) {
  
  _impl_.encoded_audio_string_.Set(value, GetArenaForAllocation());
}
inline std::string* Summary_Audio::_internal_mutable_encoded_audio_string() {
  
  return _impl_.encoded_audio_string_.Mutable(GetArenaForAllocation());
}
inline std::string* Summary_Audio::release_encoded_audio_string() {
  // @@protoc_insertion_point(field_release:tensorflow.Summary.Audio.encoded_audio_string)
  return _impl_.encoded_audio_string_.Release();
}
inline void Summary_Audio::set_allocated_encoded_audio_string(std::string* encoded_audio_string) {
  if (encoded_audio_string != nullptr) {
    
  } else {
    
  }
  _impl_.encoded_audio_string_.SetAllocated(encoded_audio_string, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.encoded_audio_string_.IsDefault()) {
    _impl_.encoded_audio_string_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.Summary.Audio.encoded_audio_string)
}

// string content_type = 5;
inline void Summary_Audio::clear_content_type() {
  _impl_.content_type_.ClearToEmpty();
}
inline const std::string& Summary_Audio::content_type() const {
  // @@protoc_insertion_point(field_get:tensorflow.Summary.Audio.content_type)
  return _internal_content_type();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Summary_Audio::set_content_type(ArgT0&& arg0, ArgT... args) {
 
 _impl_.content_type_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.Summary.Audio.content_type)
}
inline std::string* Summary_Audio::mutable_content_type() {
  std::string* _s = _internal_mutable_content_type();
  // @@protoc_insertion_point(field_mutable:tensorflow.Summary.Audio.content_type)
  return _s;
}
inline const std::string& Summary_Audio::_internal_content_type() const {
  return _impl_.content_type_.Get();
}
inline void Summary_Audio::_internal_set_content_type(const std::string& value) {
  
  _impl_.content_type_.Set(value, GetArenaForAllocation());
}
inline std::string* Summary_Audio::_internal_mutable_content_type() {
  
  return _impl_.content_type_.Mutable(GetArenaForAllocation());
}
inline std::string* Summary_Audio::release_content_type() {
  // @@protoc_insertion_point(field_release:tensorflow.Summary.Audio.content_type)
  return _impl_.content_type_.Release();
}
inline void Summary_Audio::set_allocated_content_type(std::string* content_type) {
  if (content_type != nullptr) {
    
  } else {
    
  }
  _impl_.content_type_.SetAllocated(content_type, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.content_type_.IsDefault()) {
    _impl_.content_type_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.Summary.Audio.content_type)
}

// -------------------------------------------------------------------

// Summary_Value

// string node_name = 7;
inline void Summary_Value::clear_node_name() {
  _impl_.node_name_.ClearToEmpty();
}
inline const std::string& Summary_Value::node_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.Summary.Value.node_name)
  return _internal_node_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Summary_Value::set_node_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.node_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.Summary.Value.node_name)
}
inline std::string* Summary_Value::mutable_node_name() {
  std::string* _s = _internal_mutable_node_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.Summary.Value.node_name)
  return _s;
}
inline const std::string& Summary_Value::_internal_node_name() const {
  return _impl_.node_name_.Get();
}
inline void Summary_Value::_internal_set_node_name(const std::string& value) {
  
  _impl_.node_name_.Set(value, GetArenaForAllocation());
}
inline std::string* Summary_Value::_internal_mutable_node_name() {
  
  return _impl_.node_name_.Mutable(GetArenaForAllocation());
}
inline std::string* Summary_Value::release_node_name() {
  // @@protoc_insertion_point(field_release:tensorflow.Summary.Value.node_name)
  return _impl_.node_name_.Release();
}
inline void Summary_Value::set_allocated_node_name(std::string* node_name) {
  if (node_name != nullptr) {
    
  } else {
    
  }
  _impl_.node_name_.SetAllocated(node_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.node_name_.IsDefault()) {
    _impl_.node_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.Summary.Value.node_name)
}

// string tag = 1;
inline void Summary_Value::clear_tag() {
  _impl_.tag_.ClearToEmpty();
}
inline const std::string& Summary_Value::tag() const {
  // @@protoc_insertion_point(field_get:tensorflow.Summary.Value.tag)
  return _internal_tag();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Summary_Value::set_tag(ArgT0&& arg0, ArgT... args) {
 
 _impl_.tag_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.Summary.Value.tag)
}
inline std::string* Summary_Value::mutable_tag() {
  std::string* _s = _internal_mutable_tag();
  // @@protoc_insertion_point(field_mutable:tensorflow.Summary.Value.tag)
  return _s;
}
inline const std::string& Summary_Value::_internal_tag() const {
  return _impl_.tag_.Get();
}
inline void Summary_Value::_internal_set_tag(const std::string& value) {
  
  _impl_.tag_.Set(value, GetArenaForAllocation());
}
inline std::string* Summary_Value::_internal_mutable_tag() {
  
  return _impl_.tag_.Mutable(GetArenaForAllocation());
}
inline std::string* Summary_Value::release_tag() {
  // @@protoc_insertion_point(field_release:tensorflow.Summary.Value.tag)
  return _impl_.tag_.Release();
}
inline void Summary_Value::set_allocated_tag(std::string* tag) {
  if (tag != nullptr) {
    
  } else {
    
  }
  _impl_.tag_.SetAllocated(tag, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.tag_.IsDefault()) {
    _impl_.tag_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.Summary.Value.tag)
}

// .tensorflow.SummaryMetadata metadata = 9;
inline bool Summary_Value::_internal_has_metadata() const {
  return this != internal_default_instance() && _impl_.metadata_ != nullptr;
}
inline bool Summary_Value::has_metadata() const {
  return _internal_has_metadata();
}
inline void Summary_Value::clear_metadata() {
  if (GetArenaForAllocation() == nullptr && _impl_.metadata_ != nullptr) {
    delete _impl_.metadata_;
  }
  _impl_.metadata_ = nullptr;
}
inline const ::tensorflow::SummaryMetadata& Summary_Value::_internal_metadata() const {
  const ::tensorflow::SummaryMetadata* p = _impl_.metadata_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::SummaryMetadata&>(
      ::tensorflow::_SummaryMetadata_default_instance_);
}
inline const ::tensorflow::SummaryMetadata& Summary_Value::metadata() const {
  // @@protoc_insertion_point(field_get:tensorflow.Summary.Value.metadata)
  return _internal_metadata();
}
inline void Summary_Value::unsafe_arena_set_allocated_metadata(
    ::tensorflow::SummaryMetadata* metadata) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.metadata_);
  }
  _impl_.metadata_ = metadata;
  if (metadata) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.Summary.Value.metadata)
}
inline ::tensorflow::SummaryMetadata* Summary_Value::release_metadata() {
  
  ::tensorflow::SummaryMetadata* temp = _impl_.metadata_;
  _impl_.metadata_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::SummaryMetadata* Summary_Value::unsafe_arena_release_metadata() {
  // @@protoc_insertion_point(field_release:tensorflow.Summary.Value.metadata)
  
  ::tensorflow::SummaryMetadata* temp = _impl_.metadata_;
  _impl_.metadata_ = nullptr;
  return temp;
}
inline ::tensorflow::SummaryMetadata* Summary_Value::_internal_mutable_metadata() {
  
  if (_impl_.metadata_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::SummaryMetadata>(GetArenaForAllocation());
    _impl_.metadata_ = p;
  }
  return _impl_.metadata_;
}
inline ::tensorflow::SummaryMetadata* Summary_Value::mutable_metadata() {
  ::tensorflow::SummaryMetadata* _msg = _internal_mutable_metadata();
  // @@protoc_insertion_point(field_mutable:tensorflow.Summary.Value.metadata)
  return _msg;
}
inline void Summary_Value::set_allocated_metadata(::tensorflow::SummaryMetadata* metadata) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.metadata_;
  }
  if (metadata) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(metadata);
    if (message_arena != submessage_arena) {
      metadata = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, metadata, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.metadata_ = metadata;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.Summary.Value.metadata)
}

// float simple_value = 2;
inline bool Summary_Value::_internal_has_simple_value() const {
  return value_case() == kSimpleValue;
}
inline bool Summary_Value::has_simple_value() const {
  return _internal_has_simple_value();
}
inline void Summary_Value::set_has_simple_value() {
  _impl_._oneof_case_[0] = kSimpleValue;
}
inline void Summary_Value::clear_simple_value() {
  if (_internal_has_simple_value()) {
    _impl_.value_.simple_value_ = 0;
    clear_has_value();
  }
}
inline float Summary_Value::_internal_simple_value() const {
  if (_internal_has_simple_value()) {
    return _impl_.value_.simple_value_;
  }
  return 0;
}
inline void Summary_Value::_internal_set_simple_value(float value) {
  if (!_internal_has_simple_value()) {
    clear_value();
    set_has_simple_value();
  }
  _impl_.value_.simple_value_ = value;
}
inline float Summary_Value::simple_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.Summary.Value.simple_value)
  return _internal_simple_value();
}
inline void Summary_Value::set_simple_value(float value) {
  _internal_set_simple_value(value);
  // @@protoc_insertion_point(field_set:tensorflow.Summary.Value.simple_value)
}

// bytes obsolete_old_style_histogram = 3;
inline bool Summary_Value::_internal_has_obsolete_old_style_histogram() const {
  return value_case() == kObsoleteOldStyleHistogram;
}
inline bool Summary_Value::has_obsolete_old_style_histogram() const {
  return _internal_has_obsolete_old_style_histogram();
}
inline void Summary_Value::set_has_obsolete_old_style_histogram() {
  _impl_._oneof_case_[0] = kObsoleteOldStyleHistogram;
}
inline void Summary_Value::clear_obsolete_old_style_histogram() {
  if (_internal_has_obsolete_old_style_histogram()) {
    _impl_.value_.obsolete_old_style_histogram_.Destroy();
    clear_has_value();
  }
}
inline const std::string& Summary_Value::obsolete_old_style_histogram() const {
  // @@protoc_insertion_point(field_get:tensorflow.Summary.Value.obsolete_old_style_histogram)
  return _internal_obsolete_old_style_histogram();
}
template <typename ArgT0, typename... ArgT>
inline void Summary_Value::set_obsolete_old_style_histogram(ArgT0&& arg0, ArgT... args) {
  if (!_internal_has_obsolete_old_style_histogram()) {
    clear_value();
    set_has_obsolete_old_style_histogram();
    _impl_.value_.obsolete_old_style_histogram_.InitDefault();
  }
  _impl_.value_.obsolete_old_style_histogram_.SetBytes( static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.Summary.Value.obsolete_old_style_histogram)
}
inline std::string* Summary_Value::mutable_obsolete_old_style_histogram() {
  std::string* _s = _internal_mutable_obsolete_old_style_histogram();
  // @@protoc_insertion_point(field_mutable:tensorflow.Summary.Value.obsolete_old_style_histogram)
  return _s;
}
inline const std::string& Summary_Value::_internal_obsolete_old_style_histogram() const {
  if (_internal_has_obsolete_old_style_histogram()) {
    return _impl_.value_.obsolete_old_style_histogram_.Get();
  }
  return ::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited();
}
inline void Summary_Value::_internal_set_obsolete_old_style_histogram(const std::string& value) {
  if (!_internal_has_obsolete_old_style_histogram()) {
    clear_value();
    set_has_obsolete_old_style_histogram();
    _impl_.value_.obsolete_old_style_histogram_.InitDefault();
  }
  _impl_.value_.obsolete_old_style_histogram_.Set(value, GetArenaForAllocation());
}
inline std::string* Summary_Value::_internal_mutable_obsolete_old_style_histogram() {
  if (!_internal_has_obsolete_old_style_histogram()) {
    clear_value();
    set_has_obsolete_old_style_histogram();
    _impl_.value_.obsolete_old_style_histogram_.InitDefault();
  }
  return _impl_.value_.obsolete_old_style_histogram_.Mutable(      GetArenaForAllocation());
}
inline std::string* Summary_Value::release_obsolete_old_style_histogram() {
  // @@protoc_insertion_point(field_release:tensorflow.Summary.Value.obsolete_old_style_histogram)
  if (_internal_has_obsolete_old_style_histogram()) {
    clear_has_value();
    return _impl_.value_.obsolete_old_style_histogram_.Release();
  } else {
    return nullptr;
  }
}
inline void Summary_Value::set_allocated_obsolete_old_style_histogram(std::string* obsolete_old_style_histogram) {
  if (has_value()) {
    clear_value();
  }
  if (obsolete_old_style_histogram != nullptr) {
    set_has_obsolete_old_style_histogram();
    _impl_.value_.obsolete_old_style_histogram_.InitAllocated(obsolete_old_style_histogram, GetArenaForAllocation());
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.Summary.Value.obsolete_old_style_histogram)
}

// .tensorflow.Summary.Image image = 4;
inline bool Summary_Value::_internal_has_image() const {
  return value_case() == kImage;
}
inline bool Summary_Value::has_image() const {
  return _internal_has_image();
}
inline void Summary_Value::set_has_image() {
  _impl_._oneof_case_[0] = kImage;
}
inline void Summary_Value::clear_image() {
  if (_internal_has_image()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.value_.image_;
    }
    clear_has_value();
  }
}
inline ::tensorflow::Summary_Image* Summary_Value::release_image() {
  // @@protoc_insertion_point(field_release:tensorflow.Summary.Value.image)
  if (_internal_has_image()) {
    clear_has_value();
    ::tensorflow::Summary_Image* temp = _impl_.value_.image_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.value_.image_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::Summary_Image& Summary_Value::_internal_image() const {
  return _internal_has_image()
      ? *_impl_.value_.image_
      : reinterpret_cast< ::tensorflow::Summary_Image&>(::tensorflow::_Summary_Image_default_instance_);
}
inline const ::tensorflow::Summary_Image& Summary_Value::image() const {
  // @@protoc_insertion_point(field_get:tensorflow.Summary.Value.image)
  return _internal_image();
}
inline ::tensorflow::Summary_Image* Summary_Value::unsafe_arena_release_image() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.Summary.Value.image)
  if (_internal_has_image()) {
    clear_has_value();
    ::tensorflow::Summary_Image* temp = _impl_.value_.image_;
    _impl_.value_.image_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void Summary_Value::unsafe_arena_set_allocated_image(::tensorflow::Summary_Image* image) {
  clear_value();
  if (image) {
    set_has_image();
    _impl_.value_.image_ = image;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.Summary.Value.image)
}
inline ::tensorflow::Summary_Image* Summary_Value::_internal_mutable_image() {
  if (!_internal_has_image()) {
    clear_value();
    set_has_image();
    _impl_.value_.image_ = CreateMaybeMessage< ::tensorflow::Summary_Image >(GetArenaForAllocation());
  }
  return _impl_.value_.image_;
}
inline ::tensorflow::Summary_Image* Summary_Value::mutable_image() {
  ::tensorflow::Summary_Image* _msg = _internal_mutable_image();
  // @@protoc_insertion_point(field_mutable:tensorflow.Summary.Value.image)
  return _msg;
}

// .tensorflow.HistogramProto histo = 5;
inline bool Summary_Value::_internal_has_histo() const {
  return value_case() == kHisto;
}
inline bool Summary_Value::has_histo() const {
  return _internal_has_histo();
}
inline void Summary_Value::set_has_histo() {
  _impl_._oneof_case_[0] = kHisto;
}
inline ::tensorflow::HistogramProto* Summary_Value::release_histo() {
  // @@protoc_insertion_point(field_release:tensorflow.Summary.Value.histo)
  if (_internal_has_histo()) {
    clear_has_value();
    ::tensorflow::HistogramProto* temp = _impl_.value_.histo_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.value_.histo_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::HistogramProto& Summary_Value::_internal_histo() const {
  return _internal_has_histo()
      ? *_impl_.value_.histo_
      : reinterpret_cast< ::tensorflow::HistogramProto&>(::tensorflow::_HistogramProto_default_instance_);
}
inline const ::tensorflow::HistogramProto& Summary_Value::histo() const {
  // @@protoc_insertion_point(field_get:tensorflow.Summary.Value.histo)
  return _internal_histo();
}
inline ::tensorflow::HistogramProto* Summary_Value::unsafe_arena_release_histo() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.Summary.Value.histo)
  if (_internal_has_histo()) {
    clear_has_value();
    ::tensorflow::HistogramProto* temp = _impl_.value_.histo_;
    _impl_.value_.histo_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void Summary_Value::unsafe_arena_set_allocated_histo(::tensorflow::HistogramProto* histo) {
  clear_value();
  if (histo) {
    set_has_histo();
    _impl_.value_.histo_ = histo;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.Summary.Value.histo)
}
inline ::tensorflow::HistogramProto* Summary_Value::_internal_mutable_histo() {
  if (!_internal_has_histo()) {
    clear_value();
    set_has_histo();
    _impl_.value_.histo_ = CreateMaybeMessage< ::tensorflow::HistogramProto >(GetArenaForAllocation());
  }
  return _impl_.value_.histo_;
}
inline ::tensorflow::HistogramProto* Summary_Value::mutable_histo() {
  ::tensorflow::HistogramProto* _msg = _internal_mutable_histo();
  // @@protoc_insertion_point(field_mutable:tensorflow.Summary.Value.histo)
  return _msg;
}

// .tensorflow.Summary.Audio audio = 6;
inline bool Summary_Value::_internal_has_audio() const {
  return value_case() == kAudio;
}
inline bool Summary_Value::has_audio() const {
  return _internal_has_audio();
}
inline void Summary_Value::set_has_audio() {
  _impl_._oneof_case_[0] = kAudio;
}
inline void Summary_Value::clear_audio() {
  if (_internal_has_audio()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.value_.audio_;
    }
    clear_has_value();
  }
}
inline ::tensorflow::Summary_Audio* Summary_Value::release_audio() {
  // @@protoc_insertion_point(field_release:tensorflow.Summary.Value.audio)
  if (_internal_has_audio()) {
    clear_has_value();
    ::tensorflow::Summary_Audio* temp = _impl_.value_.audio_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.value_.audio_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::Summary_Audio& Summary_Value::_internal_audio() const {
  return _internal_has_audio()
      ? *_impl_.value_.audio_
      : reinterpret_cast< ::tensorflow::Summary_Audio&>(::tensorflow::_Summary_Audio_default_instance_);
}
inline const ::tensorflow::Summary_Audio& Summary_Value::audio() const {
  // @@protoc_insertion_point(field_get:tensorflow.Summary.Value.audio)
  return _internal_audio();
}
inline ::tensorflow::Summary_Audio* Summary_Value::unsafe_arena_release_audio() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.Summary.Value.audio)
  if (_internal_has_audio()) {
    clear_has_value();
    ::tensorflow::Summary_Audio* temp = _impl_.value_.audio_;
    _impl_.value_.audio_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void Summary_Value::unsafe_arena_set_allocated_audio(::tensorflow::Summary_Audio* audio) {
  clear_value();
  if (audio) {
    set_has_audio();
    _impl_.value_.audio_ = audio;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.Summary.Value.audio)
}
inline ::tensorflow::Summary_Audio* Summary_Value::_internal_mutable_audio() {
  if (!_internal_has_audio()) {
    clear_value();
    set_has_audio();
    _impl_.value_.audio_ = CreateMaybeMessage< ::tensorflow::Summary_Audio >(GetArenaForAllocation());
  }
  return _impl_.value_.audio_;
}
inline ::tensorflow::Summary_Audio* Summary_Value::mutable_audio() {
  ::tensorflow::Summary_Audio* _msg = _internal_mutable_audio();
  // @@protoc_insertion_point(field_mutable:tensorflow.Summary.Value.audio)
  return _msg;
}

// .tensorflow.TensorProto tensor = 8;
inline bool Summary_Value::_internal_has_tensor() const {
  return value_case() == kTensor;
}
inline bool Summary_Value::has_tensor() const {
  return _internal_has_tensor();
}
inline void Summary_Value::set_has_tensor() {
  _impl_._oneof_case_[0] = kTensor;
}
inline ::tensorflow::TensorProto* Summary_Value::release_tensor() {
  // @@protoc_insertion_point(field_release:tensorflow.Summary.Value.tensor)
  if (_internal_has_tensor()) {
    clear_has_value();
    ::tensorflow::TensorProto* temp = _impl_.value_.tensor_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.value_.tensor_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::TensorProto& Summary_Value::_internal_tensor() const {
  return _internal_has_tensor()
      ? *_impl_.value_.tensor_
      : reinterpret_cast< ::tensorflow::TensorProto&>(::tensorflow::_TensorProto_default_instance_);
}
inline const ::tensorflow::TensorProto& Summary_Value::tensor() const {
  // @@protoc_insertion_point(field_get:tensorflow.Summary.Value.tensor)
  return _internal_tensor();
}
inline ::tensorflow::TensorProto* Summary_Value::unsafe_arena_release_tensor() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.Summary.Value.tensor)
  if (_internal_has_tensor()) {
    clear_has_value();
    ::tensorflow::TensorProto* temp = _impl_.value_.tensor_;
    _impl_.value_.tensor_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void Summary_Value::unsafe_arena_set_allocated_tensor(::tensorflow::TensorProto* tensor) {
  clear_value();
  if (tensor) {
    set_has_tensor();
    _impl_.value_.tensor_ = tensor;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.Summary.Value.tensor)
}
inline ::tensorflow::TensorProto* Summary_Value::_internal_mutable_tensor() {
  if (!_internal_has_tensor()) {
    clear_value();
    set_has_tensor();
    _impl_.value_.tensor_ = CreateMaybeMessage< ::tensorflow::TensorProto >(GetArenaForAllocation());
  }
  return _impl_.value_.tensor_;
}
inline ::tensorflow::TensorProto* Summary_Value::mutable_tensor() {
  ::tensorflow::TensorProto* _msg = _internal_mutable_tensor();
  // @@protoc_insertion_point(field_mutable:tensorflow.Summary.Value.tensor)
  return _msg;
}

inline bool Summary_Value::has_value() const {
  return value_case() != VALUE_NOT_SET;
}
inline void Summary_Value::clear_has_value() {
  _impl_._oneof_case_[0] = VALUE_NOT_SET;
}
inline Summary_Value::ValueCase Summary_Value::value_case() const {
  return Summary_Value::ValueCase(_impl_._oneof_case_[0]);
}
// -------------------------------------------------------------------

// Summary

// repeated .tensorflow.Summary.Value value = 1;
inline int Summary::_internal_value_size() const {
  return _impl_.value_.size();
}
inline int Summary::value_size() const {
  return _internal_value_size();
}
inline void Summary::clear_value() {
  _impl_.value_.Clear();
}
inline ::tensorflow::Summary_Value* Summary::mutable_value(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.Summary.value)
  return _impl_.value_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::Summary_Value >*
Summary::mutable_value() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.Summary.value)
  return &_impl_.value_;
}
inline const ::tensorflow::Summary_Value& Summary::_internal_value(int index) const {
  return _impl_.value_.Get(index);
}
inline const ::tensorflow::Summary_Value& Summary::value(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.Summary.value)
  return _internal_value(index);
}
inline ::tensorflow::Summary_Value* Summary::_internal_add_value() {
  return _impl_.value_.Add();
}
inline ::tensorflow::Summary_Value* Summary::add_value() {
  ::tensorflow::Summary_Value* _add = _internal_add_value();
  // @@protoc_insertion_point(field_add:tensorflow.Summary.value)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::Summary_Value >&
Summary::value() const {
  // @@protoc_insertion_point(field_list:tensorflow.Summary.value)
  return _impl_.value_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::tensorflow::DataClass> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::DataClass>() {
  return ::tensorflow::DataClass_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fsummary_2eproto
