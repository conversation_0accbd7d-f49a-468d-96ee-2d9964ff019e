@echo off
echo 尝试编译denoise_training...

REM 尝试使用系统中可能存在的编译器
echo 正在搜索可用的编译器...

REM 检查是否有Visual Studio的cl编译器
where cl >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo 找到Visual Studio编译器，使用cl.exe编译...
    cl /DTRAINING=1 /I..\include denoise.c kiss_fft.c pitch.c celt_lpc.c rnn.c rnnoise_data.c /Fe:denoise_training.exe
    goto :end
)

REM 检查是否有gcc
where gcc >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo 找到gcc编译器...
    gcc -DTRAINING=1 -Wall -W -O3 -g -I../include denoise.c kiss_fft.c pitch.c celt_lpc.c rnn.c rnnoise_data.c -o denoise_training.exe -lm
    goto :end
)

REM 检查是否有clang
where clang >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo 找到clang编译器...
    clang -DTRAINING=1 -Wall -W -O3 -g -I../include denoise.c kiss_fft.c pitch.c celt_lpc.c rnn.c rnnoise_data.c -o denoise_training.exe -lm
    goto :end
)

echo 错误：未找到任何可用的编译器！
echo 请安装以下编译器之一：
echo 1. Visual Studio Build Tools (推荐)
echo 2. MinGW-w64
echo 3. MSYS2
echo 4. Clang

:end
if exist denoise_training.exe (
    echo 编译成功！生成了denoise_training.exe
) else (
    echo 编译失败！
)
pause
