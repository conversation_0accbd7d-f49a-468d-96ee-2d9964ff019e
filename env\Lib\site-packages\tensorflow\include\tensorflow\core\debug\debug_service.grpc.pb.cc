// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: tensorflow/core/debug/debug_service.proto

#include "tensorflow/core/debug/debug_service.pb.h"
#include "tensorflow/core/debug/debug_service.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace tensorflow {

static const char* grpcEventListener_method_names[] = {
  "/tensorflow.EventListener/SendEvents",
  "/tensorflow.EventListener/SendTracebacks",
  "/tensorflow.EventListener/SendSourceFiles",
};

std::unique_ptr< grpc::EventListener::Stub> grpc::EventListener::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< grpc::EventListener::Stub> stub(new grpc::EventListener::Stub(channel));
  return stub;
}

grpc::EventListener::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel)
  : channel_(channel), rpcmethod_SendEvents_(grpcEventListener_method_names[0], ::grpc::internal::RpcMethod::BIDI_STREAMING, channel)
  , rpcmethod_SendTracebacks_(grpcEventListener_method_names[1], ::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SendSourceFiles_(grpcEventListener_method_names[2], ::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::ClientReaderWriter< ::tensorflow::Event, ::tensorflow::EventReply>* grpc::EventListener::Stub::SendEventsRaw(::grpc::ClientContext* context) {
  return ::grpc_impl::internal::ClientReaderWriterFactory< ::tensorflow::Event, ::tensorflow::EventReply>::Create(channel_.get(), rpcmethod_SendEvents_, context);
}

void grpc::EventListener::Stub::experimental_async::SendEvents(::grpc::ClientContext* context, ::grpc::experimental::ClientBidiReactor< ::tensorflow::Event,::tensorflow::EventReply>* reactor) {
  ::grpc_impl::internal::ClientCallbackReaderWriterFactory< ::tensorflow::Event,::tensorflow::EventReply>::Create(stub_->channel_.get(), stub_->rpcmethod_SendEvents_, context, reactor);
}

::grpc::ClientAsyncReaderWriter< ::tensorflow::Event, ::tensorflow::EventReply>* grpc::EventListener::Stub::AsyncSendEventsRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag) {
  return ::grpc_impl::internal::ClientAsyncReaderWriterFactory< ::tensorflow::Event, ::tensorflow::EventReply>::Create(channel_.get(), cq, rpcmethod_SendEvents_, context, true, tag);
}

::grpc::ClientAsyncReaderWriter< ::tensorflow::Event, ::tensorflow::EventReply>* grpc::EventListener::Stub::PrepareAsyncSendEventsRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq) {
  return ::grpc_impl::internal::ClientAsyncReaderWriterFactory< ::tensorflow::Event, ::tensorflow::EventReply>::Create(channel_.get(), cq, rpcmethod_SendEvents_, context, false, nullptr);
}

::grpc::Status grpc::EventListener::Stub::SendTracebacks(::grpc::ClientContext* context, const ::tensorflow::CallTraceback& request, ::tensorflow::EventReply* response) {
  return ::grpc::internal::BlockingUnaryCall(channel_.get(), rpcmethod_SendTracebacks_, context, request, response);
}

void grpc::EventListener::Stub::experimental_async::SendTracebacks(::grpc::ClientContext* context, const ::tensorflow::CallTraceback* request, ::tensorflow::EventReply* response, std::function<void(::grpc::Status)> f) {
  ::grpc_impl::internal::CallbackUnaryCall(stub_->channel_.get(), stub_->rpcmethod_SendTracebacks_, context, request, response, std::move(f));
}

void grpc::EventListener::Stub::experimental_async::SendTracebacks(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::EventReply* response, std::function<void(::grpc::Status)> f) {
  ::grpc_impl::internal::CallbackUnaryCall(stub_->channel_.get(), stub_->rpcmethod_SendTracebacks_, context, request, response, std::move(f));
}

void grpc::EventListener::Stub::experimental_async::SendTracebacks(::grpc::ClientContext* context, const ::tensorflow::CallTraceback* request, ::tensorflow::EventReply* response, ::grpc::experimental::ClientUnaryReactor* reactor) {
  ::grpc_impl::internal::ClientCallbackUnaryFactory::Create(stub_->channel_.get(), stub_->rpcmethod_SendTracebacks_, context, request, response, reactor);
}

void grpc::EventListener::Stub::experimental_async::SendTracebacks(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::EventReply* response, ::grpc::experimental::ClientUnaryReactor* reactor) {
  ::grpc_impl::internal::ClientCallbackUnaryFactory::Create(stub_->channel_.get(), stub_->rpcmethod_SendTracebacks_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::tensorflow::EventReply>* grpc::EventListener::Stub::AsyncSendTracebacksRaw(::grpc::ClientContext* context, const ::tensorflow::CallTraceback& request, ::grpc::CompletionQueue* cq) {
  return ::grpc_impl::internal::ClientAsyncResponseReaderFactory< ::tensorflow::EventReply>::Create(channel_.get(), cq, rpcmethod_SendTracebacks_, context, request, true);
}

::grpc::ClientAsyncResponseReader< ::tensorflow::EventReply>* grpc::EventListener::Stub::PrepareAsyncSendTracebacksRaw(::grpc::ClientContext* context, const ::tensorflow::CallTraceback& request, ::grpc::CompletionQueue* cq) {
  return ::grpc_impl::internal::ClientAsyncResponseReaderFactory< ::tensorflow::EventReply>::Create(channel_.get(), cq, rpcmethod_SendTracebacks_, context, request, false);
}

::grpc::Status grpc::EventListener::Stub::SendSourceFiles(::grpc::ClientContext* context, const ::tensorflow::DebuggedSourceFiles& request, ::tensorflow::EventReply* response) {
  return ::grpc::internal::BlockingUnaryCall(channel_.get(), rpcmethod_SendSourceFiles_, context, request, response);
}

void grpc::EventListener::Stub::experimental_async::SendSourceFiles(::grpc::ClientContext* context, const ::tensorflow::DebuggedSourceFiles* request, ::tensorflow::EventReply* response, std::function<void(::grpc::Status)> f) {
  ::grpc_impl::internal::CallbackUnaryCall(stub_->channel_.get(), stub_->rpcmethod_SendSourceFiles_, context, request, response, std::move(f));
}

void grpc::EventListener::Stub::experimental_async::SendSourceFiles(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::EventReply* response, std::function<void(::grpc::Status)> f) {
  ::grpc_impl::internal::CallbackUnaryCall(stub_->channel_.get(), stub_->rpcmethod_SendSourceFiles_, context, request, response, std::move(f));
}

void grpc::EventListener::Stub::experimental_async::SendSourceFiles(::grpc::ClientContext* context, const ::tensorflow::DebuggedSourceFiles* request, ::tensorflow::EventReply* response, ::grpc::experimental::ClientUnaryReactor* reactor) {
  ::grpc_impl::internal::ClientCallbackUnaryFactory::Create(stub_->channel_.get(), stub_->rpcmethod_SendSourceFiles_, context, request, response, reactor);
}

void grpc::EventListener::Stub::experimental_async::SendSourceFiles(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::EventReply* response, ::grpc::experimental::ClientUnaryReactor* reactor) {
  ::grpc_impl::internal::ClientCallbackUnaryFactory::Create(stub_->channel_.get(), stub_->rpcmethod_SendSourceFiles_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::tensorflow::EventReply>* grpc::EventListener::Stub::AsyncSendSourceFilesRaw(::grpc::ClientContext* context, const ::tensorflow::DebuggedSourceFiles& request, ::grpc::CompletionQueue* cq) {
  return ::grpc_impl::internal::ClientAsyncResponseReaderFactory< ::tensorflow::EventReply>::Create(channel_.get(), cq, rpcmethod_SendSourceFiles_, context, request, true);
}

::grpc::ClientAsyncResponseReader< ::tensorflow::EventReply>* grpc::EventListener::Stub::PrepareAsyncSendSourceFilesRaw(::grpc::ClientContext* context, const ::tensorflow::DebuggedSourceFiles& request, ::grpc::CompletionQueue* cq) {
  return ::grpc_impl::internal::ClientAsyncResponseReaderFactory< ::tensorflow::EventReply>::Create(channel_.get(), cq, rpcmethod_SendSourceFiles_, context, request, false);
}

grpc::EventListener::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      grpcEventListener_method_names[0],
      ::grpc::internal::RpcMethod::BIDI_STREAMING,
      new ::grpc::internal::BidiStreamingHandler< grpc::EventListener::Service, ::tensorflow::Event, ::tensorflow::EventReply>(
          std::mem_fn(&grpc::EventListener::Service::SendEvents), this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      grpcEventListener_method_names[1],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< grpc::EventListener::Service, ::tensorflow::CallTraceback, ::tensorflow::EventReply>(
          std::mem_fn(&grpc::EventListener::Service::SendTracebacks), this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      grpcEventListener_method_names[2],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< grpc::EventListener::Service, ::tensorflow::DebuggedSourceFiles, ::tensorflow::EventReply>(
          std::mem_fn(&grpc::EventListener::Service::SendSourceFiles), this)));
}

grpc::EventListener::Service::~Service() {
}

::grpc::Status grpc::EventListener::Service::SendEvents(::grpc::ServerContext* context, ::grpc::ServerReaderWriter< ::tensorflow::EventReply, ::tensorflow::Event>* stream) {
  (void) context;
  (void) stream;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status grpc::EventListener::Service::SendTracebacks(::grpc::ServerContext* context, const ::tensorflow::CallTraceback* request, ::tensorflow::EventReply* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status grpc::EventListener::Service::SendSourceFiles(::grpc::ServerContext* context, const ::tensorflow::DebuggedSourceFiles* request, ::tensorflow::EventReply* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace tensorflow

