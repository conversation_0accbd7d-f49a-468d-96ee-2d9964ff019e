/* Copyright 2022 The OpenXLA Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
==============================================================================*/

#ifndef XLA_SERVICE_SELECT_AND_SCATTER_EXPANDER_H_
#define XLA_SERVICE_SELECT_AND_SCATTER_EXPANDER_H_

#include "xla/hlo/transforms/expanders/op_expander_pass.h"

namespace xla {

// This pass rewrites select-and-scatter operations into a window reduction and
// a scatter as described in the conceptual explanation of the "select" and
// "scatter" steps of this operation.
class SelectAndScatterExpander : public OpExpanderPass {
 public:
  absl::string_view name() const override {
    return "select_and_scatter_expander";
  }

 protected:
  bool InstructionMatchesPattern(HloInstruction* inst) override;

  absl::StatusOr<HloInstruction*> ExpandInstruction(
      HloInstruction* inst) override;
};

}  // namespace xla

#endif  // XLA_SERVICE_SELECT_AND_SCATTER_EXPANDER_H_
