
/* Autogenerated by mlir-tblgen; don't manually edit. */

#include "mlir-c/Pass.h"

#ifdef __cplusplus
extern "C" {
#endif

// Registration for the entire group
MLIR_CAPI_EXPORTED void mlirRegisterPasses(void);


/* Create  Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateAddDataFlowEdgesPass(void);
MLIR_CAPI_EXPORTED void mlirRegisterAddDataFlowEdgesPass(void);


/* Create  Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateApplyShardingConstraintsPass(void);
MLIR_CAPI_EXPORTED void mlirRegisterApplyShardingConstraintsPass(void);


/* Create  Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateConstantSplitterPass(void);
MLIR_CAPI_EXPORTED void mlirRegisterConstantSplitterPass(void);


/* Create  Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateLiftInlinedMeshesPass(void);
MLIR_CAPI_EXPORTED void mlirRegisterLiftInlinedMeshesPass(void);


/* Create  Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateManualAxesCleanupPass(void);
MLIR_CAPI_EXPORTED void mlirRegisterManualAxesCleanupPass(void);


/* Create  Pass. */
MLIR_CAPI_EXPORTED MlirPass mlirCreateShardingGroupImportPass(void);
MLIR_CAPI_EXPORTED void mlirRegisterShardingGroupImportPass(void);



#ifdef __cplusplus
}
#endif
