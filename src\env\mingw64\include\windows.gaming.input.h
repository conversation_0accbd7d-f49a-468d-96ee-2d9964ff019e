/*** Autogenerated by WIDL 10.8 from include/windows.gaming.input.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __windows_gaming_input_h__
#define __windows_gaming_input_h__

/* Forward declarations */

#ifndef ____x_ABI_CWindows_CGaming_CInput_CIArcadeStick_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CIArcadeStick_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CIArcadeStick __x_ABI_CWindows_CGaming_CInput_CIArcadeStick;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CIArcadeStick ABI::Windows::Gaming::Input::IArcadeStick
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                interface IArcadeStick;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics ABI::Windows::Gaming::Input::IArcadeStickStatics
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                interface IArcadeStickStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics2_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics2 __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics2 ABI::Windows::Gaming::Input::IArcadeStickStatics2
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                interface IArcadeStickStatics2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGaming_CInput_CIFlightStick_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CIFlightStick_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CIFlightStick __x_ABI_CWindows_CGaming_CInput_CIFlightStick;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CIFlightStick ABI::Windows::Gaming::Input::IFlightStick
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                interface IFlightStick;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics __x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics ABI::Windows::Gaming::Input::IFlightStickStatics
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                interface IFlightStickStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGaming_CInput_CIGameController_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CIGameController_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CIGameController __x_ABI_CWindows_CGaming_CInput_CIGameController;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CIGameController ABI::Windows::Gaming::Input::IGameController
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                interface IGameController;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGaming_CInput_CIGamepad_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CIGamepad_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CIGamepad __x_ABI_CWindows_CGaming_CInput_CIGamepad;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CIGamepad ABI::Windows::Gaming::Input::IGamepad
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                interface IGamepad;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGaming_CInput_CIGamepad2_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CIGamepad2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CIGamepad2 __x_ABI_CWindows_CGaming_CInput_CIGamepad2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CIGamepad2 ABI::Windows::Gaming::Input::IGamepad2
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                interface IGamepad2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGaming_CInput_CIRacingWheel_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CIRacingWheel_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CIRacingWheel __x_ABI_CWindows_CGaming_CInput_CIRacingWheel;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CIRacingWheel ABI::Windows::Gaming::Input::IRacingWheel
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                interface IRacingWheel;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics ABI::Windows::Gaming::Input::IRacingWheelStatics
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                interface IRacingWheelStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics2_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics2 __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics2 ABI::Windows::Gaming::Input::IRacingWheelStatics2
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                interface IRacingWheelStatics2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGaming_CInput_CIRawGameController_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CIRawGameController_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CIRawGameController __x_ABI_CWindows_CGaming_CInput_CIRawGameController;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CIRawGameController ABI::Windows::Gaming::Input::IRawGameController
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                interface IRawGameController;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGaming_CInput_CIRawGameController2_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CIRawGameController2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CIRawGameController2 __x_ABI_CWindows_CGaming_CInput_CIRawGameController2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CIRawGameController2 ABI::Windows::Gaming::Input::IRawGameController2
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                interface IRawGameController2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGaming_CInput_CIGamepadStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CIGamepadStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics ABI::Windows::Gaming::Input::IGamepadStatics
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                interface IGamepadStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGaming_CInput_CIGamepadStatics2_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CIGamepadStatics2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics2 __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics2 ABI::Windows::Gaming::Input::IGamepadStatics2
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                interface IGamepadStatics2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGaming_CInput_CIHeadset_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CIHeadset_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CIHeadset __x_ABI_CWindows_CGaming_CInput_CIHeadset;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CIHeadset ABI::Windows::Gaming::Input::IHeadset
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                interface IHeadset;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics __x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics ABI::Windows::Gaming::Input::IRawGameControllerStatics
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                interface IRawGameControllerStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGaming_CInput_CIGameControllerBatteryInfo_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CIGameControllerBatteryInfo_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CIGameControllerBatteryInfo __x_ABI_CWindows_CGaming_CInput_CIGameControllerBatteryInfo;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CIGameControllerBatteryInfo ABI::Windows::Gaming::Input::IGameControllerBatteryInfo
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                interface IGameControllerBatteryInfo;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGaming_CInput_CArcadeStick_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CArcadeStick_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                class ArcadeStick;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CGaming_CInput_CArcadeStick __x_ABI_CWindows_CGaming_CInput_CArcadeStick;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CGaming_CInput_CArcadeStick_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CGaming_CInput_CFlightStick_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CFlightStick_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                class FlightStick;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CGaming_CInput_CFlightStick __x_ABI_CWindows_CGaming_CInput_CFlightStick;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CGaming_CInput_CFlightStick_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CGaming_CInput_CGamepad_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CGamepad_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                class Gamepad;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CGaming_CInput_CGamepad __x_ABI_CWindows_CGaming_CInput_CGamepad;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CGaming_CInput_CGamepad_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CGaming_CInput_CHeadset_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CHeadset_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                class Headset;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CGaming_CInput_CHeadset __x_ABI_CWindows_CGaming_CInput_CHeadset;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CGaming_CInput_CHeadset_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CGaming_CInput_CRacingWheel_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CRacingWheel_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                class RacingWheel;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CGaming_CInput_CRacingWheel __x_ABI_CWindows_CGaming_CInput_CRacingWheel;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CGaming_CInput_CRacingWheel_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CGaming_CInput_CRawGameController_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CRawGameController_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                class RawGameController;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CGaming_CInput_CRawGameController __x_ABI_CWindows_CGaming_CInput_CRawGameController;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CGaming_CInput_CRawGameController_FWD_DEFINED__ */

#ifndef ____FIEventHandler_1_Windows__CGaming__CInput__CArcadeStick_FWD_DEFINED__
#define ____FIEventHandler_1_Windows__CGaming__CInput__CArcadeStick_FWD_DEFINED__
typedef interface __FIEventHandler_1_Windows__CGaming__CInput__CArcadeStick __FIEventHandler_1_Windows__CGaming__CInput__CArcadeStick;
#ifdef __cplusplus
#define __FIEventHandler_1_Windows__CGaming__CInput__CArcadeStick ABI::Windows::Foundation::IEventHandler<ABI::Windows::Gaming::Input::ArcadeStick* >
#endif /* __cplusplus */
#endif

#ifndef ____FIEventHandler_1_Windows__CGaming__CInput__CFlightStick_FWD_DEFINED__
#define ____FIEventHandler_1_Windows__CGaming__CInput__CFlightStick_FWD_DEFINED__
typedef interface __FIEventHandler_1_Windows__CGaming__CInput__CFlightStick __FIEventHandler_1_Windows__CGaming__CInput__CFlightStick;
#ifdef __cplusplus
#define __FIEventHandler_1_Windows__CGaming__CInput__CFlightStick ABI::Windows::Foundation::IEventHandler<ABI::Windows::Gaming::Input::FlightStick* >
#endif /* __cplusplus */
#endif

#ifndef ____FIEventHandler_1_Windows__CGaming__CInput__CGamepad_FWD_DEFINED__
#define ____FIEventHandler_1_Windows__CGaming__CInput__CGamepad_FWD_DEFINED__
typedef interface __FIEventHandler_1_Windows__CGaming__CInput__CGamepad __FIEventHandler_1_Windows__CGaming__CInput__CGamepad;
#ifdef __cplusplus
#define __FIEventHandler_1_Windows__CGaming__CInput__CGamepad ABI::Windows::Foundation::IEventHandler<ABI::Windows::Gaming::Input::Gamepad* >
#endif /* __cplusplus */
#endif

#ifndef ____FIEventHandler_1_Windows__CGaming__CInput__CRacingWheel_FWD_DEFINED__
#define ____FIEventHandler_1_Windows__CGaming__CInput__CRacingWheel_FWD_DEFINED__
typedef interface __FIEventHandler_1_Windows__CGaming__CInput__CRacingWheel __FIEventHandler_1_Windows__CGaming__CInput__CRacingWheel;
#ifdef __cplusplus
#define __FIEventHandler_1_Windows__CGaming__CInput__CRacingWheel ABI::Windows::Foundation::IEventHandler<ABI::Windows::Gaming::Input::RacingWheel* >
#endif /* __cplusplus */
#endif

#ifndef ____FIEventHandler_1_Windows__CGaming__CInput__CRawGameController_FWD_DEFINED__
#define ____FIEventHandler_1_Windows__CGaming__CInput__CRawGameController_FWD_DEFINED__
typedef interface __FIEventHandler_1_Windows__CGaming__CInput__CRawGameController __FIEventHandler_1_Windows__CGaming__CInput__CRawGameController;
#ifdef __cplusplus
#define __FIEventHandler_1_Windows__CGaming__CInput__CRawGameController ABI::Windows::Foundation::IEventHandler<ABI::Windows::Gaming::Input::RawGameController* >
#endif /* __cplusplus */
#endif

#ifndef ____FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CGaming__CInput__CHeadset_FWD_DEFINED__
#define ____FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CGaming__CInput__CHeadset_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CGaming__CInput__CHeadset __FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CGaming__CInput__CHeadset;
#ifdef __cplusplus
#define __FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CGaming__CInput__CHeadset ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Gaming::Input::IGameController*,ABI::Windows::Gaming::Input::Headset* >
#endif /* __cplusplus */
#endif

#ifndef ____FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CSystem__CUserChangedEventArgs_FWD_DEFINED__
#define ____FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CSystem__CUserChangedEventArgs_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CSystem__CUserChangedEventArgs __FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CSystem__CUserChangedEventArgs;
#ifdef __cplusplus
#define __FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CSystem__CUserChangedEventArgs ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Gaming::Input::IGameController*,ABI::Windows::System::UserChangedEventArgs* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_Windows__CGaming__CInput__CArcadeStick_FWD_DEFINED__
#define ____FIIterator_1_Windows__CGaming__CInput__CArcadeStick_FWD_DEFINED__
typedef interface __FIIterator_1_Windows__CGaming__CInput__CArcadeStick __FIIterator_1_Windows__CGaming__CInput__CArcadeStick;
#ifdef __cplusplus
#define __FIIterator_1_Windows__CGaming__CInput__CArcadeStick ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Gaming::Input::ArcadeStick* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterable_1_Windows__CGaming__CInput__CArcadeStick_FWD_DEFINED__
#define ____FIIterable_1_Windows__CGaming__CInput__CArcadeStick_FWD_DEFINED__
typedef interface __FIIterable_1_Windows__CGaming__CInput__CArcadeStick __FIIterable_1_Windows__CGaming__CInput__CArcadeStick;
#ifdef __cplusplus
#define __FIIterable_1_Windows__CGaming__CInput__CArcadeStick ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Gaming::Input::ArcadeStick* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CGaming__CInput__CArcadeStick_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CGaming__CInput__CArcadeStick_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CGaming__CInput__CArcadeStick __FIVectorView_1_Windows__CGaming__CInput__CArcadeStick;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CGaming__CInput__CArcadeStick ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Gaming::Input::ArcadeStick* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_Windows__CGaming__CInput__CFlightStick_FWD_DEFINED__
#define ____FIIterator_1_Windows__CGaming__CInput__CFlightStick_FWD_DEFINED__
typedef interface __FIIterator_1_Windows__CGaming__CInput__CFlightStick __FIIterator_1_Windows__CGaming__CInput__CFlightStick;
#ifdef __cplusplus
#define __FIIterator_1_Windows__CGaming__CInput__CFlightStick ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Gaming::Input::FlightStick* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterable_1_Windows__CGaming__CInput__CFlightStick_FWD_DEFINED__
#define ____FIIterable_1_Windows__CGaming__CInput__CFlightStick_FWD_DEFINED__
typedef interface __FIIterable_1_Windows__CGaming__CInput__CFlightStick __FIIterable_1_Windows__CGaming__CInput__CFlightStick;
#ifdef __cplusplus
#define __FIIterable_1_Windows__CGaming__CInput__CFlightStick ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Gaming::Input::FlightStick* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CGaming__CInput__CFlightStick_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CGaming__CInput__CFlightStick_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CGaming__CInput__CFlightStick __FIVectorView_1_Windows__CGaming__CInput__CFlightStick;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CGaming__CInput__CFlightStick ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Gaming::Input::FlightStick* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_Windows__CGaming__CInput__CGamepad_FWD_DEFINED__
#define ____FIIterator_1_Windows__CGaming__CInput__CGamepad_FWD_DEFINED__
typedef interface __FIIterator_1_Windows__CGaming__CInput__CGamepad __FIIterator_1_Windows__CGaming__CInput__CGamepad;
#ifdef __cplusplus
#define __FIIterator_1_Windows__CGaming__CInput__CGamepad ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Gaming::Input::Gamepad* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterable_1_Windows__CGaming__CInput__CGamepad_FWD_DEFINED__
#define ____FIIterable_1_Windows__CGaming__CInput__CGamepad_FWD_DEFINED__
typedef interface __FIIterable_1_Windows__CGaming__CInput__CGamepad __FIIterable_1_Windows__CGaming__CInput__CGamepad;
#ifdef __cplusplus
#define __FIIterable_1_Windows__CGaming__CInput__CGamepad ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Gaming::Input::Gamepad* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CGaming__CInput__CGamepad_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CGaming__CInput__CGamepad_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CGaming__CInput__CGamepad __FIVectorView_1_Windows__CGaming__CInput__CGamepad;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CGaming__CInput__CGamepad ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Gaming::Input::Gamepad* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVector_1_Windows__CGaming__CInput__CGamepad_FWD_DEFINED__
#define ____FIVector_1_Windows__CGaming__CInput__CGamepad_FWD_DEFINED__
typedef interface __FIVector_1_Windows__CGaming__CInput__CGamepad __FIVector_1_Windows__CGaming__CInput__CGamepad;
#ifdef __cplusplus
#define __FIVector_1_Windows__CGaming__CInput__CGamepad ABI::Windows::Foundation::Collections::IVector<ABI::Windows::Gaming::Input::Gamepad* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_Windows__CGaming__CInput__CRacingWheel_FWD_DEFINED__
#define ____FIIterator_1_Windows__CGaming__CInput__CRacingWheel_FWD_DEFINED__
typedef interface __FIIterator_1_Windows__CGaming__CInput__CRacingWheel __FIIterator_1_Windows__CGaming__CInput__CRacingWheel;
#ifdef __cplusplus
#define __FIIterator_1_Windows__CGaming__CInput__CRacingWheel ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Gaming::Input::RacingWheel* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterable_1_Windows__CGaming__CInput__CRacingWheel_FWD_DEFINED__
#define ____FIIterable_1_Windows__CGaming__CInput__CRacingWheel_FWD_DEFINED__
typedef interface __FIIterable_1_Windows__CGaming__CInput__CRacingWheel __FIIterable_1_Windows__CGaming__CInput__CRacingWheel;
#ifdef __cplusplus
#define __FIIterable_1_Windows__CGaming__CInput__CRacingWheel ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Gaming::Input::RacingWheel* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CGaming__CInput__CRacingWheel_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CGaming__CInput__CRacingWheel_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CGaming__CInput__CRacingWheel __FIVectorView_1_Windows__CGaming__CInput__CRacingWheel;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CGaming__CInput__CRacingWheel ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Gaming::Input::RacingWheel* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVector_1_Windows__CGaming__CInput__CRacingWheel_FWD_DEFINED__
#define ____FIVector_1_Windows__CGaming__CInput__CRacingWheel_FWD_DEFINED__
typedef interface __FIVector_1_Windows__CGaming__CInput__CRacingWheel __FIVector_1_Windows__CGaming__CInput__CRacingWheel;
#ifdef __cplusplus
#define __FIVector_1_Windows__CGaming__CInput__CRacingWheel ABI::Windows::Foundation::Collections::IVector<ABI::Windows::Gaming::Input::RacingWheel* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_Windows__CGaming__CInput__CRawGameController_FWD_DEFINED__
#define ____FIIterator_1_Windows__CGaming__CInput__CRawGameController_FWD_DEFINED__
typedef interface __FIIterator_1_Windows__CGaming__CInput__CRawGameController __FIIterator_1_Windows__CGaming__CInput__CRawGameController;
#ifdef __cplusplus
#define __FIIterator_1_Windows__CGaming__CInput__CRawGameController ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Gaming::Input::RawGameController* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterable_1_Windows__CGaming__CInput__CRawGameController_FWD_DEFINED__
#define ____FIIterable_1_Windows__CGaming__CInput__CRawGameController_FWD_DEFINED__
typedef interface __FIIterable_1_Windows__CGaming__CInput__CRawGameController __FIIterable_1_Windows__CGaming__CInput__CRawGameController;
#ifdef __cplusplus
#define __FIIterable_1_Windows__CGaming__CInput__CRawGameController ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Gaming::Input::RawGameController* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CGaming__CInput__CRawGameController_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CGaming__CInput__CRawGameController_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CGaming__CInput__CRawGameController __FIVectorView_1_Windows__CGaming__CInput__CRawGameController;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CGaming__CInput__CRawGameController ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Gaming::Input::RawGameController* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVector_1_Windows__CGaming__CInput__CRawGameController_FWD_DEFINED__
#define ____FIVector_1_Windows__CGaming__CInput__CRawGameController_FWD_DEFINED__
typedef interface __FIVector_1_Windows__CGaming__CInput__CRawGameController __FIVector_1_Windows__CGaming__CInput__CRawGameController;
#ifdef __cplusplus
#define __FIVector_1_Windows__CGaming__CInput__CRawGameController ABI::Windows::Foundation::Collections::IVector<ABI::Windows::Gaming::Input::RawGameController* >
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <inspectable.h>
#include <asyncinfo.h>
#include <eventtoken.h>
#include <windowscontracts.h>
#include <windows.foundation.h>
#include <windows.devices.haptics.h>
#include <windows.gaming.input.forcefeedback.h>
#include <windows.system.h>
#include <windows.devices.power.h>

#ifdef __cplusplus
extern "C" {
#endif

#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CGaming_CInput_CArcadeStickButtons __x_ABI_CWindows_CGaming_CInput_CArcadeStickButtons;
#endif /* __cplusplus */

#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CGaming_CInput_CFlightStickButtons __x_ABI_CWindows_CGaming_CInput_CFlightStickButtons;
#endif /* __cplusplus */

#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CGaming_CInput_CGamepadButtons __x_ABI_CWindows_CGaming_CInput_CGamepadButtons;
#endif /* __cplusplus */

#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CGaming_CInput_CGameControllerButtonLabel __x_ABI_CWindows_CGaming_CInput_CGameControllerButtonLabel;
#endif /* __cplusplus */

#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CGaming_CInput_CGameControllerSwitchKind __x_ABI_CWindows_CGaming_CInput_CGameControllerSwitchKind;
#endif /* __cplusplus */

#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CGaming_CInput_CGameControllerSwitchPosition __x_ABI_CWindows_CGaming_CInput_CGameControllerSwitchPosition;
#endif /* __cplusplus */

#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CGaming_CInput_CRacingWheelButtons __x_ABI_CWindows_CGaming_CInput_CRacingWheelButtons;
#endif /* __cplusplus */

#ifndef __cplusplus
typedef struct __x_ABI_CWindows_CGaming_CInput_CArcadeStickReading __x_ABI_CWindows_CGaming_CInput_CArcadeStickReading;
#else /* __cplusplus */
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                typedef struct ArcadeStickReading ArcadeStickReading;
            }
        }
    }
}
#endif /* __cplusplus */

#ifndef __cplusplus
typedef struct __x_ABI_CWindows_CGaming_CInput_CFlightStickReading __x_ABI_CWindows_CGaming_CInput_CFlightStickReading;
#else /* __cplusplus */
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                typedef struct FlightStickReading FlightStickReading;
            }
        }
    }
}
#endif /* __cplusplus */

#ifndef __cplusplus
typedef struct __x_ABI_CWindows_CGaming_CInput_CGamepadReading __x_ABI_CWindows_CGaming_CInput_CGamepadReading;
#else /* __cplusplus */
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                typedef struct GamepadReading GamepadReading;
            }
        }
    }
}
#endif /* __cplusplus */

#ifndef __cplusplus
typedef struct __x_ABI_CWindows_CGaming_CInput_CGamepadVibration __x_ABI_CWindows_CGaming_CInput_CGamepadVibration;
#else /* __cplusplus */
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                typedef struct GamepadVibration GamepadVibration;
            }
        }
    }
}
#endif /* __cplusplus */

#ifndef __cplusplus
typedef struct __x_ABI_CWindows_CGaming_CInput_CRacingWheelReading __x_ABI_CWindows_CGaming_CInput_CRacingWheelReading;
#else /* __cplusplus */
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                typedef struct RacingWheelReading RacingWheelReading;
            }
        }
    }
}
#endif /* __cplusplus */

#ifndef ____x_ABI_CWindows_CGaming_CInput_CIArcadeStick_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CIArcadeStick_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CIArcadeStick __x_ABI_CWindows_CGaming_CInput_CIArcadeStick;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CIArcadeStick ABI::Windows::Gaming::Input::IArcadeStick
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                interface IArcadeStick;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics ABI::Windows::Gaming::Input::IArcadeStickStatics
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                interface IArcadeStickStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics2_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics2 __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics2 ABI::Windows::Gaming::Input::IArcadeStickStatics2
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                interface IArcadeStickStatics2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGaming_CInput_CIFlightStick_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CIFlightStick_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CIFlightStick __x_ABI_CWindows_CGaming_CInput_CIFlightStick;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CIFlightStick ABI::Windows::Gaming::Input::IFlightStick
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                interface IFlightStick;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics __x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics ABI::Windows::Gaming::Input::IFlightStickStatics
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                interface IFlightStickStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGaming_CInput_CIGameController_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CIGameController_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CIGameController __x_ABI_CWindows_CGaming_CInput_CIGameController;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CIGameController ABI::Windows::Gaming::Input::IGameController
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                interface IGameController;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGaming_CInput_CIGameControllerBatteryInfo_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CIGameControllerBatteryInfo_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CIGameControllerBatteryInfo __x_ABI_CWindows_CGaming_CInput_CIGameControllerBatteryInfo;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CIGameControllerBatteryInfo ABI::Windows::Gaming::Input::IGameControllerBatteryInfo
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                interface IGameControllerBatteryInfo;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGaming_CInput_CIGamepad_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CIGamepad_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CIGamepad __x_ABI_CWindows_CGaming_CInput_CIGamepad;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CIGamepad ABI::Windows::Gaming::Input::IGamepad
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                interface IGamepad;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGaming_CInput_CIGamepad2_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CIGamepad2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CIGamepad2 __x_ABI_CWindows_CGaming_CInput_CIGamepad2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CIGamepad2 ABI::Windows::Gaming::Input::IGamepad2
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                interface IGamepad2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGaming_CInput_CIGamepadStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CIGamepadStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics ABI::Windows::Gaming::Input::IGamepadStatics
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                interface IGamepadStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGaming_CInput_CIGamepadStatics2_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CIGamepadStatics2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics2 __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics2 ABI::Windows::Gaming::Input::IGamepadStatics2
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                interface IGamepadStatics2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGaming_CInput_CIRacingWheel_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CIRacingWheel_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CIRacingWheel __x_ABI_CWindows_CGaming_CInput_CIRacingWheel;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CIRacingWheel ABI::Windows::Gaming::Input::IRacingWheel
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                interface IRacingWheel;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics ABI::Windows::Gaming::Input::IRacingWheelStatics
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                interface IRacingWheelStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics2_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics2 __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics2 ABI::Windows::Gaming::Input::IRacingWheelStatics2
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                interface IRacingWheelStatics2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGaming_CInput_CIRawGameController_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CIRawGameController_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CIRawGameController __x_ABI_CWindows_CGaming_CInput_CIRawGameController;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CIRawGameController ABI::Windows::Gaming::Input::IRawGameController
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                interface IRawGameController;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGaming_CInput_CIRawGameController2_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CIRawGameController2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CIRawGameController2 __x_ABI_CWindows_CGaming_CInput_CIRawGameController2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CIRawGameController2 ABI::Windows::Gaming::Input::IRawGameController2
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                interface IRawGameController2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_Windows__CGaming__CInput__CArcadeStick_FWD_DEFINED__
#define ____FIIterator_1_Windows__CGaming__CInput__CArcadeStick_FWD_DEFINED__
typedef interface __FIIterator_1_Windows__CGaming__CInput__CArcadeStick __FIIterator_1_Windows__CGaming__CInput__CArcadeStick;
#ifdef __cplusplus
#define __FIIterator_1_Windows__CGaming__CInput__CArcadeStick ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Gaming::Input::ArcadeStick* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterable_1_Windows__CGaming__CInput__CArcadeStick_FWD_DEFINED__
#define ____FIIterable_1_Windows__CGaming__CInput__CArcadeStick_FWD_DEFINED__
typedef interface __FIIterable_1_Windows__CGaming__CInput__CArcadeStick __FIIterable_1_Windows__CGaming__CInput__CArcadeStick;
#ifdef __cplusplus
#define __FIIterable_1_Windows__CGaming__CInput__CArcadeStick ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Gaming::Input::ArcadeStick* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CGaming__CInput__CArcadeStick_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CGaming__CInput__CArcadeStick_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CGaming__CInput__CArcadeStick __FIVectorView_1_Windows__CGaming__CInput__CArcadeStick;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CGaming__CInput__CArcadeStick ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Gaming::Input::ArcadeStick* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_Windows__CGaming__CInput__CFlightStick_FWD_DEFINED__
#define ____FIIterator_1_Windows__CGaming__CInput__CFlightStick_FWD_DEFINED__
typedef interface __FIIterator_1_Windows__CGaming__CInput__CFlightStick __FIIterator_1_Windows__CGaming__CInput__CFlightStick;
#ifdef __cplusplus
#define __FIIterator_1_Windows__CGaming__CInput__CFlightStick ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Gaming::Input::FlightStick* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterable_1_Windows__CGaming__CInput__CFlightStick_FWD_DEFINED__
#define ____FIIterable_1_Windows__CGaming__CInput__CFlightStick_FWD_DEFINED__
typedef interface __FIIterable_1_Windows__CGaming__CInput__CFlightStick __FIIterable_1_Windows__CGaming__CInput__CFlightStick;
#ifdef __cplusplus
#define __FIIterable_1_Windows__CGaming__CInput__CFlightStick ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Gaming::Input::FlightStick* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CGaming__CInput__CFlightStick_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CGaming__CInput__CFlightStick_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CGaming__CInput__CFlightStick __FIVectorView_1_Windows__CGaming__CInput__CFlightStick;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CGaming__CInput__CFlightStick ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Gaming::Input::FlightStick* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_Windows__CGaming__CInput__CGamepad_FWD_DEFINED__
#define ____FIIterator_1_Windows__CGaming__CInput__CGamepad_FWD_DEFINED__
typedef interface __FIIterator_1_Windows__CGaming__CInput__CGamepad __FIIterator_1_Windows__CGaming__CInput__CGamepad;
#ifdef __cplusplus
#define __FIIterator_1_Windows__CGaming__CInput__CGamepad ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Gaming::Input::Gamepad* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterable_1_Windows__CGaming__CInput__CGamepad_FWD_DEFINED__
#define ____FIIterable_1_Windows__CGaming__CInput__CGamepad_FWD_DEFINED__
typedef interface __FIIterable_1_Windows__CGaming__CInput__CGamepad __FIIterable_1_Windows__CGaming__CInput__CGamepad;
#ifdef __cplusplus
#define __FIIterable_1_Windows__CGaming__CInput__CGamepad ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Gaming::Input::Gamepad* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CGaming__CInput__CGamepad_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CGaming__CInput__CGamepad_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CGaming__CInput__CGamepad __FIVectorView_1_Windows__CGaming__CInput__CGamepad;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CGaming__CInput__CGamepad ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Gaming::Input::Gamepad* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVector_1_Windows__CGaming__CInput__CGamepad_FWD_DEFINED__
#define ____FIVector_1_Windows__CGaming__CInput__CGamepad_FWD_DEFINED__
typedef interface __FIVector_1_Windows__CGaming__CInput__CGamepad __FIVector_1_Windows__CGaming__CInput__CGamepad;
#ifdef __cplusplus
#define __FIVector_1_Windows__CGaming__CInput__CGamepad ABI::Windows::Foundation::Collections::IVector<ABI::Windows::Gaming::Input::Gamepad* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_Windows__CGaming__CInput__CRacingWheel_FWD_DEFINED__
#define ____FIIterator_1_Windows__CGaming__CInput__CRacingWheel_FWD_DEFINED__
typedef interface __FIIterator_1_Windows__CGaming__CInput__CRacingWheel __FIIterator_1_Windows__CGaming__CInput__CRacingWheel;
#ifdef __cplusplus
#define __FIIterator_1_Windows__CGaming__CInput__CRacingWheel ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Gaming::Input::RacingWheel* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterable_1_Windows__CGaming__CInput__CRacingWheel_FWD_DEFINED__
#define ____FIIterable_1_Windows__CGaming__CInput__CRacingWheel_FWD_DEFINED__
typedef interface __FIIterable_1_Windows__CGaming__CInput__CRacingWheel __FIIterable_1_Windows__CGaming__CInput__CRacingWheel;
#ifdef __cplusplus
#define __FIIterable_1_Windows__CGaming__CInput__CRacingWheel ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Gaming::Input::RacingWheel* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CGaming__CInput__CRacingWheel_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CGaming__CInput__CRacingWheel_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CGaming__CInput__CRacingWheel __FIVectorView_1_Windows__CGaming__CInput__CRacingWheel;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CGaming__CInput__CRacingWheel ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Gaming::Input::RacingWheel* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVector_1_Windows__CGaming__CInput__CRacingWheel_FWD_DEFINED__
#define ____FIVector_1_Windows__CGaming__CInput__CRacingWheel_FWD_DEFINED__
typedef interface __FIVector_1_Windows__CGaming__CInput__CRacingWheel __FIVector_1_Windows__CGaming__CInput__CRacingWheel;
#ifdef __cplusplus
#define __FIVector_1_Windows__CGaming__CInput__CRacingWheel ABI::Windows::Foundation::Collections::IVector<ABI::Windows::Gaming::Input::RacingWheel* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_Windows__CGaming__CInput__CRawGameController_FWD_DEFINED__
#define ____FIIterator_1_Windows__CGaming__CInput__CRawGameController_FWD_DEFINED__
typedef interface __FIIterator_1_Windows__CGaming__CInput__CRawGameController __FIIterator_1_Windows__CGaming__CInput__CRawGameController;
#ifdef __cplusplus
#define __FIIterator_1_Windows__CGaming__CInput__CRawGameController ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Gaming::Input::RawGameController* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterable_1_Windows__CGaming__CInput__CRawGameController_FWD_DEFINED__
#define ____FIIterable_1_Windows__CGaming__CInput__CRawGameController_FWD_DEFINED__
typedef interface __FIIterable_1_Windows__CGaming__CInput__CRawGameController __FIIterable_1_Windows__CGaming__CInput__CRawGameController;
#ifdef __cplusplus
#define __FIIterable_1_Windows__CGaming__CInput__CRawGameController ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Gaming::Input::RawGameController* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CGaming__CInput__CRawGameController_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CGaming__CInput__CRawGameController_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CGaming__CInput__CRawGameController __FIVectorView_1_Windows__CGaming__CInput__CRawGameController;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CGaming__CInput__CRawGameController ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Gaming::Input::RawGameController* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVector_1_Windows__CGaming__CInput__CRawGameController_FWD_DEFINED__
#define ____FIVector_1_Windows__CGaming__CInput__CRawGameController_FWD_DEFINED__
typedef interface __FIVector_1_Windows__CGaming__CInput__CRawGameController __FIVector_1_Windows__CGaming__CInput__CRawGameController;
#ifdef __cplusplus
#define __FIVector_1_Windows__CGaming__CInput__CRawGameController ABI::Windows::Foundation::Collections::IVector<ABI::Windows::Gaming::Input::RawGameController* >
#endif /* __cplusplus */
#endif

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                enum ArcadeStickButtons {
                    ArcadeStickButtons_None = 0x0,
                    ArcadeStickButtons_StickUp = 0x1,
                    ArcadeStickButtons_StickDown = 0x2,
                    ArcadeStickButtons_StickLeft = 0x4,
                    ArcadeStickButtons_StickRight = 0x8,
                    ArcadeStickButtons_Action1 = 0x10,
                    ArcadeStickButtons_Action2 = 0x20,
                    ArcadeStickButtons_Action3 = 0x40,
                    ArcadeStickButtons_Action4 = 0x80,
                    ArcadeStickButtons_Action5 = 0x100,
                    ArcadeStickButtons_Action6 = 0x200,
                    ArcadeStickButtons_Special1 = 0x400,
                    ArcadeStickButtons_Special2 = 0x800
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CGaming_CInput_CArcadeStickButtons {
    ArcadeStickButtons_None = 0x0,
    ArcadeStickButtons_StickUp = 0x1,
    ArcadeStickButtons_StickDown = 0x2,
    ArcadeStickButtons_StickLeft = 0x4,
    ArcadeStickButtons_StickRight = 0x8,
    ArcadeStickButtons_Action1 = 0x10,
    ArcadeStickButtons_Action2 = 0x20,
    ArcadeStickButtons_Action3 = 0x40,
    ArcadeStickButtons_Action4 = 0x80,
    ArcadeStickButtons_Action5 = 0x100,
    ArcadeStickButtons_Action6 = 0x200,
    ArcadeStickButtons_Special1 = 0x400,
    ArcadeStickButtons_Special2 = 0x800
};
#ifdef WIDL_using_Windows_Gaming_Input
#define ArcadeStickButtons __x_ABI_CWindows_CGaming_CInput_CArcadeStickButtons
#endif /* WIDL_using_Windows_Gaming_Input */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                enum FlightStickButtons {
                    FlightStickButtons_None = 0x0,
                    FlightStickButtons_FirePrimary = 0x1,
                    FlightStickButtons_FireSecondary = 0x2
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CGaming_CInput_CFlightStickButtons {
    FlightStickButtons_None = 0x0,
    FlightStickButtons_FirePrimary = 0x1,
    FlightStickButtons_FireSecondary = 0x2
};
#ifdef WIDL_using_Windows_Gaming_Input
#define FlightStickButtons __x_ABI_CWindows_CGaming_CInput_CFlightStickButtons
#endif /* WIDL_using_Windows_Gaming_Input */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                enum GamepadButtons {
                    GamepadButtons_None = 0x0,
                    GamepadButtons_Menu = 0x1,
                    GamepadButtons_View = 0x2,
                    GamepadButtons_A = 0x4,
                    GamepadButtons_B = 0x8,
                    GamepadButtons_X = 0x10,
                    GamepadButtons_Y = 0x20,
                    GamepadButtons_DPadUp = 0x40,
                    GamepadButtons_DPadDown = 0x80,
                    GamepadButtons_DPadLeft = 0x100,
                    GamepadButtons_DPadRight = 0x200,
                    GamepadButtons_LeftShoulder = 0x400,
                    GamepadButtons_RightShoulder = 0x800,
                    GamepadButtons_LeftThumbstick = 0x1000,
                    GamepadButtons_RightThumbstick = 0x2000,
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
                    GamepadButtons_Paddle1 = 0x4000,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
                    GamepadButtons_Paddle2 = 0x8000,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
                    GamepadButtons_Paddle3 = 0x10000,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
                    GamepadButtons_Paddle4 = 0x20000
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CGaming_CInput_CGamepadButtons {
    GamepadButtons_None = 0x0,
    GamepadButtons_Menu = 0x1,
    GamepadButtons_View = 0x2,
    GamepadButtons_A = 0x4,
    GamepadButtons_B = 0x8,
    GamepadButtons_X = 0x10,
    GamepadButtons_Y = 0x20,
    GamepadButtons_DPadUp = 0x40,
    GamepadButtons_DPadDown = 0x80,
    GamepadButtons_DPadLeft = 0x100,
    GamepadButtons_DPadRight = 0x200,
    GamepadButtons_LeftShoulder = 0x400,
    GamepadButtons_RightShoulder = 0x800,
    GamepadButtons_LeftThumbstick = 0x1000,
    GamepadButtons_RightThumbstick = 0x2000,
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
    GamepadButtons_Paddle1 = 0x4000,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
    GamepadButtons_Paddle2 = 0x8000,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
    GamepadButtons_Paddle3 = 0x10000,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
    GamepadButtons_Paddle4 = 0x20000
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */
};
#ifdef WIDL_using_Windows_Gaming_Input
#define GamepadButtons __x_ABI_CWindows_CGaming_CInput_CGamepadButtons
#endif /* WIDL_using_Windows_Gaming_Input */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                enum GameControllerButtonLabel {
                    GameControllerButtonLabel_None = 0,
                    GameControllerButtonLabel_XboxBack = 1,
                    GameControllerButtonLabel_XboxStart = 2,
                    GameControllerButtonLabel_XboxMenu = 3,
                    GameControllerButtonLabel_XboxView = 4,
                    GameControllerButtonLabel_XboxUp = 5,
                    GameControllerButtonLabel_XboxDown = 6,
                    GameControllerButtonLabel_XboxLeft = 7,
                    GameControllerButtonLabel_XboxRight = 8,
                    GameControllerButtonLabel_XboxA = 9,
                    GameControllerButtonLabel_XboxB = 10,
                    GameControllerButtonLabel_XboxX = 11,
                    GameControllerButtonLabel_XboxY = 12,
                    GameControllerButtonLabel_XboxLeftBumper = 13,
                    GameControllerButtonLabel_XboxLeftTrigger = 14,
                    GameControllerButtonLabel_XboxLeftStickButton = 15,
                    GameControllerButtonLabel_XboxRightBumper = 16,
                    GameControllerButtonLabel_XboxRightTrigger = 17,
                    GameControllerButtonLabel_XboxRightStickButton = 18,
                    GameControllerButtonLabel_XboxPaddle1 = 19,
                    GameControllerButtonLabel_XboxPaddle2 = 20,
                    GameControllerButtonLabel_XboxPaddle3 = 21,
                    GameControllerButtonLabel_XboxPaddle4 = 22,
                    GameControllerButtonLabel_Mode = 23,
                    GameControllerButtonLabel_Select = 24,
                    GameControllerButtonLabel_Menu = 25,
                    GameControllerButtonLabel_View = 26,
                    GameControllerButtonLabel_Back = 27,
                    GameControllerButtonLabel_Start = 28,
                    GameControllerButtonLabel_Options = 29,
                    GameControllerButtonLabel_Share = 30,
                    GameControllerButtonLabel_Up = 31,
                    GameControllerButtonLabel_Down = 32,
                    GameControllerButtonLabel_Left = 33,
                    GameControllerButtonLabel_Right = 34,
                    GameControllerButtonLabel_LetterA = 35,
                    GameControllerButtonLabel_LetterB = 36,
                    GameControllerButtonLabel_LetterC = 37,
                    GameControllerButtonLabel_LetterL = 38,
                    GameControllerButtonLabel_LetterR = 39,
                    GameControllerButtonLabel_LetterX = 40,
                    GameControllerButtonLabel_LetterY = 41,
                    GameControllerButtonLabel_LetterZ = 42,
                    GameControllerButtonLabel_Cross = 43,
                    GameControllerButtonLabel_Circle = 44,
                    GameControllerButtonLabel_Square = 45,
                    GameControllerButtonLabel_Triangle = 46,
                    GameControllerButtonLabel_LeftBumper = 47,
                    GameControllerButtonLabel_LeftTrigger = 48,
                    GameControllerButtonLabel_LeftStickButton = 49,
                    GameControllerButtonLabel_Left1 = 50,
                    GameControllerButtonLabel_Left2 = 51,
                    GameControllerButtonLabel_Left3 = 52,
                    GameControllerButtonLabel_RightBumper = 53,
                    GameControllerButtonLabel_RightTrigger = 54,
                    GameControllerButtonLabel_RightStickButton = 55,
                    GameControllerButtonLabel_Right1 = 56,
                    GameControllerButtonLabel_Right2 = 57,
                    GameControllerButtonLabel_Right3 = 58,
                    GameControllerButtonLabel_Paddle1 = 59,
                    GameControllerButtonLabel_Paddle2 = 60,
                    GameControllerButtonLabel_Paddle3 = 61,
                    GameControllerButtonLabel_Paddle4 = 62,
                    GameControllerButtonLabel_Plus = 63,
                    GameControllerButtonLabel_Minus = 64,
                    GameControllerButtonLabel_DownLeftArrow = 65,
                    GameControllerButtonLabel_DialLeft = 66,
                    GameControllerButtonLabel_DialRight = 67,
                    GameControllerButtonLabel_Suspension = 68
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CGaming_CInput_CGameControllerButtonLabel {
    GameControllerButtonLabel_None = 0,
    GameControllerButtonLabel_XboxBack = 1,
    GameControllerButtonLabel_XboxStart = 2,
    GameControllerButtonLabel_XboxMenu = 3,
    GameControllerButtonLabel_XboxView = 4,
    GameControllerButtonLabel_XboxUp = 5,
    GameControllerButtonLabel_XboxDown = 6,
    GameControllerButtonLabel_XboxLeft = 7,
    GameControllerButtonLabel_XboxRight = 8,
    GameControllerButtonLabel_XboxA = 9,
    GameControllerButtonLabel_XboxB = 10,
    GameControllerButtonLabel_XboxX = 11,
    GameControllerButtonLabel_XboxY = 12,
    GameControllerButtonLabel_XboxLeftBumper = 13,
    GameControllerButtonLabel_XboxLeftTrigger = 14,
    GameControllerButtonLabel_XboxLeftStickButton = 15,
    GameControllerButtonLabel_XboxRightBumper = 16,
    GameControllerButtonLabel_XboxRightTrigger = 17,
    GameControllerButtonLabel_XboxRightStickButton = 18,
    GameControllerButtonLabel_XboxPaddle1 = 19,
    GameControllerButtonLabel_XboxPaddle2 = 20,
    GameControllerButtonLabel_XboxPaddle3 = 21,
    GameControllerButtonLabel_XboxPaddle4 = 22,
    GameControllerButtonLabel_Mode = 23,
    GameControllerButtonLabel_Select = 24,
    GameControllerButtonLabel_Menu = 25,
    GameControllerButtonLabel_View = 26,
    GameControllerButtonLabel_Back = 27,
    GameControllerButtonLabel_Start = 28,
    GameControllerButtonLabel_Options = 29,
    GameControllerButtonLabel_Share = 30,
    GameControllerButtonLabel_Up = 31,
    GameControllerButtonLabel_Down = 32,
    GameControllerButtonLabel_Left = 33,
    GameControllerButtonLabel_Right = 34,
    GameControllerButtonLabel_LetterA = 35,
    GameControllerButtonLabel_LetterB = 36,
    GameControllerButtonLabel_LetterC = 37,
    GameControllerButtonLabel_LetterL = 38,
    GameControllerButtonLabel_LetterR = 39,
    GameControllerButtonLabel_LetterX = 40,
    GameControllerButtonLabel_LetterY = 41,
    GameControllerButtonLabel_LetterZ = 42,
    GameControllerButtonLabel_Cross = 43,
    GameControllerButtonLabel_Circle = 44,
    GameControllerButtonLabel_Square = 45,
    GameControllerButtonLabel_Triangle = 46,
    GameControllerButtonLabel_LeftBumper = 47,
    GameControllerButtonLabel_LeftTrigger = 48,
    GameControllerButtonLabel_LeftStickButton = 49,
    GameControllerButtonLabel_Left1 = 50,
    GameControllerButtonLabel_Left2 = 51,
    GameControllerButtonLabel_Left3 = 52,
    GameControllerButtonLabel_RightBumper = 53,
    GameControllerButtonLabel_RightTrigger = 54,
    GameControllerButtonLabel_RightStickButton = 55,
    GameControllerButtonLabel_Right1 = 56,
    GameControllerButtonLabel_Right2 = 57,
    GameControllerButtonLabel_Right3 = 58,
    GameControllerButtonLabel_Paddle1 = 59,
    GameControllerButtonLabel_Paddle2 = 60,
    GameControllerButtonLabel_Paddle3 = 61,
    GameControllerButtonLabel_Paddle4 = 62,
    GameControllerButtonLabel_Plus = 63,
    GameControllerButtonLabel_Minus = 64,
    GameControllerButtonLabel_DownLeftArrow = 65,
    GameControllerButtonLabel_DialLeft = 66,
    GameControllerButtonLabel_DialRight = 67,
    GameControllerButtonLabel_Suspension = 68
};
#ifdef WIDL_using_Windows_Gaming_Input
#define GameControllerButtonLabel __x_ABI_CWindows_CGaming_CInput_CGameControllerButtonLabel
#endif /* WIDL_using_Windows_Gaming_Input */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                enum GameControllerSwitchKind {
                    GameControllerSwitchKind_TwoWay = 0,
                    GameControllerSwitchKind_FourWay = 1,
                    GameControllerSwitchKind_EightWay = 2
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CGaming_CInput_CGameControllerSwitchKind {
    GameControllerSwitchKind_TwoWay = 0,
    GameControllerSwitchKind_FourWay = 1,
    GameControllerSwitchKind_EightWay = 2
};
#ifdef WIDL_using_Windows_Gaming_Input
#define GameControllerSwitchKind __x_ABI_CWindows_CGaming_CInput_CGameControllerSwitchKind
#endif /* WIDL_using_Windows_Gaming_Input */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                enum GameControllerSwitchPosition {
                    GameControllerSwitchPosition_Center = 0,
                    GameControllerSwitchPosition_Up = 1,
                    GameControllerSwitchPosition_UpRight = 2,
                    GameControllerSwitchPosition_Right = 3,
                    GameControllerSwitchPosition_DownRight = 4,
                    GameControllerSwitchPosition_Down = 5,
                    GameControllerSwitchPosition_DownLeft = 6,
                    GameControllerSwitchPosition_Left = 7,
                    GameControllerSwitchPosition_UpLeft = 8
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CGaming_CInput_CGameControllerSwitchPosition {
    GameControllerSwitchPosition_Center = 0,
    GameControllerSwitchPosition_Up = 1,
    GameControllerSwitchPosition_UpRight = 2,
    GameControllerSwitchPosition_Right = 3,
    GameControllerSwitchPosition_DownRight = 4,
    GameControllerSwitchPosition_Down = 5,
    GameControllerSwitchPosition_DownLeft = 6,
    GameControllerSwitchPosition_Left = 7,
    GameControllerSwitchPosition_UpLeft = 8
};
#ifdef WIDL_using_Windows_Gaming_Input
#define GameControllerSwitchPosition __x_ABI_CWindows_CGaming_CInput_CGameControllerSwitchPosition
#endif /* WIDL_using_Windows_Gaming_Input */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                enum RacingWheelButtons {
                    RacingWheelButtons_None = 0x0,
                    RacingWheelButtons_PreviousGear = 0x1,
                    RacingWheelButtons_NextGear = 0x2,
                    RacingWheelButtons_DPadUp = 0x4,
                    RacingWheelButtons_DPadDown = 0x8,
                    RacingWheelButtons_DPadLeft = 0x10,
                    RacingWheelButtons_DPadRight = 0x20,
                    RacingWheelButtons_Button1 = 0x40,
                    RacingWheelButtons_Button2 = 0x80,
                    RacingWheelButtons_Button3 = 0x100,
                    RacingWheelButtons_Button4 = 0x200,
                    RacingWheelButtons_Button5 = 0x400,
                    RacingWheelButtons_Button6 = 0x800,
                    RacingWheelButtons_Button7 = 0x1000,
                    RacingWheelButtons_Button8 = 0x2000,
                    RacingWheelButtons_Button9 = 0x4000,
                    RacingWheelButtons_Button10 = 0x8000,
                    RacingWheelButtons_Button11 = 0x10000,
                    RacingWheelButtons_Button12 = 0x20000,
                    RacingWheelButtons_Button13 = 0x40000,
                    RacingWheelButtons_Button14 = 0x80000,
                    RacingWheelButtons_Button15 = 0x100000,
                    RacingWheelButtons_Button16 = 0x200000
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CGaming_CInput_CRacingWheelButtons {
    RacingWheelButtons_None = 0x0,
    RacingWheelButtons_PreviousGear = 0x1,
    RacingWheelButtons_NextGear = 0x2,
    RacingWheelButtons_DPadUp = 0x4,
    RacingWheelButtons_DPadDown = 0x8,
    RacingWheelButtons_DPadLeft = 0x10,
    RacingWheelButtons_DPadRight = 0x20,
    RacingWheelButtons_Button1 = 0x40,
    RacingWheelButtons_Button2 = 0x80,
    RacingWheelButtons_Button3 = 0x100,
    RacingWheelButtons_Button4 = 0x200,
    RacingWheelButtons_Button5 = 0x400,
    RacingWheelButtons_Button6 = 0x800,
    RacingWheelButtons_Button7 = 0x1000,
    RacingWheelButtons_Button8 = 0x2000,
    RacingWheelButtons_Button9 = 0x4000,
    RacingWheelButtons_Button10 = 0x8000,
    RacingWheelButtons_Button11 = 0x10000,
    RacingWheelButtons_Button12 = 0x20000,
    RacingWheelButtons_Button13 = 0x40000,
    RacingWheelButtons_Button14 = 0x80000,
    RacingWheelButtons_Button15 = 0x100000,
    RacingWheelButtons_Button16 = 0x200000
};
#ifdef WIDL_using_Windows_Gaming_Input
#define RacingWheelButtons __x_ABI_CWindows_CGaming_CInput_CRacingWheelButtons
#endif /* WIDL_using_Windows_Gaming_Input */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                struct ArcadeStickReading {
                    UINT64 Timestamp;
                    ABI::Windows::Gaming::Input::ArcadeStickButtons Buttons;
                };
            }
        }
    }
}
extern "C" {
#else
struct __x_ABI_CWindows_CGaming_CInput_CArcadeStickReading {
    UINT64 Timestamp;
    __x_ABI_CWindows_CGaming_CInput_CArcadeStickButtons Buttons;
};
#ifdef WIDL_using_Windows_Gaming_Input
#define ArcadeStickReading __x_ABI_CWindows_CGaming_CInput_CArcadeStickReading
#endif /* WIDL_using_Windows_Gaming_Input */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                struct FlightStickReading {
                    UINT64 Timestamp;
                    ABI::Windows::Gaming::Input::FlightStickButtons Buttons;
                    ABI::Windows::Gaming::Input::GameControllerSwitchPosition HatSwitch;
                    DOUBLE Roll;
                    DOUBLE Pitch;
                    DOUBLE Yaw;
                    DOUBLE Throttle;
                };
            }
        }
    }
}
extern "C" {
#else
struct __x_ABI_CWindows_CGaming_CInput_CFlightStickReading {
    UINT64 Timestamp;
    __x_ABI_CWindows_CGaming_CInput_CFlightStickButtons Buttons;
    __x_ABI_CWindows_CGaming_CInput_CGameControllerSwitchPosition HatSwitch;
    DOUBLE Roll;
    DOUBLE Pitch;
    DOUBLE Yaw;
    DOUBLE Throttle;
};
#ifdef WIDL_using_Windows_Gaming_Input
#define FlightStickReading __x_ABI_CWindows_CGaming_CInput_CFlightStickReading
#endif /* WIDL_using_Windows_Gaming_Input */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                struct GamepadReading {
                    UINT64 Timestamp;
                    ABI::Windows::Gaming::Input::GamepadButtons Buttons;
                    DOUBLE LeftTrigger;
                    DOUBLE RightTrigger;
                    DOUBLE LeftThumbstickX;
                    DOUBLE LeftThumbstickY;
                    DOUBLE RightThumbstickX;
                    DOUBLE RightThumbstickY;
                };
            }
        }
    }
}
extern "C" {
#else
struct __x_ABI_CWindows_CGaming_CInput_CGamepadReading {
    UINT64 Timestamp;
    __x_ABI_CWindows_CGaming_CInput_CGamepadButtons Buttons;
    DOUBLE LeftTrigger;
    DOUBLE RightTrigger;
    DOUBLE LeftThumbstickX;
    DOUBLE LeftThumbstickY;
    DOUBLE RightThumbstickX;
    DOUBLE RightThumbstickY;
};
#ifdef WIDL_using_Windows_Gaming_Input
#define GamepadReading __x_ABI_CWindows_CGaming_CInput_CGamepadReading
#endif /* WIDL_using_Windows_Gaming_Input */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                struct GamepadVibration {
                    DOUBLE LeftMotor;
                    DOUBLE RightMotor;
                    DOUBLE LeftTrigger;
                    DOUBLE RightTrigger;
                };
            }
        }
    }
}
extern "C" {
#else
struct __x_ABI_CWindows_CGaming_CInput_CGamepadVibration {
    DOUBLE LeftMotor;
    DOUBLE RightMotor;
    DOUBLE LeftTrigger;
    DOUBLE RightTrigger;
};
#ifdef WIDL_using_Windows_Gaming_Input
#define GamepadVibration __x_ABI_CWindows_CGaming_CInput_CGamepadVibration
#endif /* WIDL_using_Windows_Gaming_Input */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                struct RacingWheelReading {
                    UINT64 Timestamp;
                    ABI::Windows::Gaming::Input::RacingWheelButtons Buttons;
                    INT32 PatternShifterGear;
                    DOUBLE Wheel;
                    DOUBLE Throttle;
                    DOUBLE Brake;
                    DOUBLE Clutch;
                    DOUBLE Handbrake;
                };
            }
        }
    }
}
extern "C" {
#else
struct __x_ABI_CWindows_CGaming_CInput_CRacingWheelReading {
    UINT64 Timestamp;
    __x_ABI_CWindows_CGaming_CInput_CRacingWheelButtons Buttons;
    INT32 PatternShifterGear;
    DOUBLE Wheel;
    DOUBLE Throttle;
    DOUBLE Brake;
    DOUBLE Clutch;
    DOUBLE Handbrake;
};
#ifdef WIDL_using_Windows_Gaming_Input
#define RacingWheelReading __x_ABI_CWindows_CGaming_CInput_CRacingWheelReading
#endif /* WIDL_using_Windows_Gaming_Input */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */
/*****************************************************************************
 * IArcadeStick interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
#ifndef ____x_ABI_CWindows_CGaming_CInput_CIArcadeStick_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CIArcadeStick_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGaming_CInput_CIArcadeStick, 0xb14a539d, 0xbefb, 0x4c81, 0x80,0x51, 0x15,0xec,0xf3,0xb1,0x30,0x36);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                MIDL_INTERFACE("b14a539d-befb-4c81-8051-15ecf3b13036")
                IArcadeStick : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE GetButtonLabel(
                        ABI::Windows::Gaming::Input::ArcadeStickButtons button,
                        ABI::Windows::Gaming::Input::GameControllerButtonLabel *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetCurrentReading(
                        ABI::Windows::Gaming::Input::ArcadeStickReading *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGaming_CInput_CIArcadeStick, 0xb14a539d, 0xbefb, 0x4c81, 0x80,0x51, 0x15,0xec,0xf3,0xb1,0x30,0x36)
#endif
#else
typedef struct __x_ABI_CWindows_CGaming_CInput_CIArcadeStickVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGaming_CInput_CIArcadeStick *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGaming_CInput_CIArcadeStick *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGaming_CInput_CIArcadeStick *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGaming_CInput_CIArcadeStick *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGaming_CInput_CIArcadeStick *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGaming_CInput_CIArcadeStick *This,
        TrustLevel *trustLevel);

    /*** IArcadeStick methods ***/
    HRESULT (STDMETHODCALLTYPE *GetButtonLabel)(
        __x_ABI_CWindows_CGaming_CInput_CIArcadeStick *This,
        __x_ABI_CWindows_CGaming_CInput_CArcadeStickButtons button,
        __x_ABI_CWindows_CGaming_CInput_CGameControllerButtonLabel *value);

    HRESULT (STDMETHODCALLTYPE *GetCurrentReading)(
        __x_ABI_CWindows_CGaming_CInput_CIArcadeStick *This,
        __x_ABI_CWindows_CGaming_CInput_CArcadeStickReading *value);

    END_INTERFACE
} __x_ABI_CWindows_CGaming_CInput_CIArcadeStickVtbl;

interface __x_ABI_CWindows_CGaming_CInput_CIArcadeStick {
    CONST_VTBL __x_ABI_CWindows_CGaming_CInput_CIArcadeStickVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CIArcadeStick_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGaming_CInput_CIArcadeStick_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGaming_CInput_CIArcadeStick_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CIArcadeStick_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGaming_CInput_CIArcadeStick_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGaming_CInput_CIArcadeStick_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IArcadeStick methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CIArcadeStick_GetButtonLabel(This,button,value) (This)->lpVtbl->GetButtonLabel(This,button,value)
#define __x_ABI_CWindows_CGaming_CInput_CIArcadeStick_GetCurrentReading(This,value) (This)->lpVtbl->GetCurrentReading(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIArcadeStick_QueryInterface(__x_ABI_CWindows_CGaming_CInput_CIArcadeStick* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CIArcadeStick_AddRef(__x_ABI_CWindows_CGaming_CInput_CIArcadeStick* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CIArcadeStick_Release(__x_ABI_CWindows_CGaming_CInput_CIArcadeStick* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIArcadeStick_GetIids(__x_ABI_CWindows_CGaming_CInput_CIArcadeStick* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIArcadeStick_GetRuntimeClassName(__x_ABI_CWindows_CGaming_CInput_CIArcadeStick* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIArcadeStick_GetTrustLevel(__x_ABI_CWindows_CGaming_CInput_CIArcadeStick* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IArcadeStick methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIArcadeStick_GetButtonLabel(__x_ABI_CWindows_CGaming_CInput_CIArcadeStick* This,__x_ABI_CWindows_CGaming_CInput_CArcadeStickButtons button,__x_ABI_CWindows_CGaming_CInput_CGameControllerButtonLabel *value) {
    return This->lpVtbl->GetButtonLabel(This,button,value);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIArcadeStick_GetCurrentReading(__x_ABI_CWindows_CGaming_CInput_CIArcadeStick* This,__x_ABI_CWindows_CGaming_CInput_CArcadeStickReading *value) {
    return This->lpVtbl->GetCurrentReading(This,value);
}
#endif
#ifdef WIDL_using_Windows_Gaming_Input
#define IID_IArcadeStick IID___x_ABI_CWindows_CGaming_CInput_CIArcadeStick
#define IArcadeStickVtbl __x_ABI_CWindows_CGaming_CInput_CIArcadeStickVtbl
#define IArcadeStick __x_ABI_CWindows_CGaming_CInput_CIArcadeStick
#define IArcadeStick_QueryInterface __x_ABI_CWindows_CGaming_CInput_CIArcadeStick_QueryInterface
#define IArcadeStick_AddRef __x_ABI_CWindows_CGaming_CInput_CIArcadeStick_AddRef
#define IArcadeStick_Release __x_ABI_CWindows_CGaming_CInput_CIArcadeStick_Release
#define IArcadeStick_GetIids __x_ABI_CWindows_CGaming_CInput_CIArcadeStick_GetIids
#define IArcadeStick_GetRuntimeClassName __x_ABI_CWindows_CGaming_CInput_CIArcadeStick_GetRuntimeClassName
#define IArcadeStick_GetTrustLevel __x_ABI_CWindows_CGaming_CInput_CIArcadeStick_GetTrustLevel
#define IArcadeStick_GetButtonLabel __x_ABI_CWindows_CGaming_CInput_CIArcadeStick_GetButtonLabel
#define IArcadeStick_GetCurrentReading __x_ABI_CWindows_CGaming_CInput_CIArcadeStick_GetCurrentReading
#endif /* WIDL_using_Windows_Gaming_Input */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGaming_CInput_CIArcadeStick_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */

/*****************************************************************************
 * IArcadeStickStatics interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
#ifndef ____x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics, 0x5c37b8c8, 0x37b1, 0x4ad8, 0x94,0x58, 0x20,0x0f,0x1a,0x30,0x01,0x8e);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                MIDL_INTERFACE("5c37b8c8-37b1-4ad8-9458-200f1a30018e")
                IArcadeStickStatics : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE add_ArcadeStickAdded(
                        ABI::Windows::Foundation::IEventHandler<ABI::Windows::Gaming::Input::ArcadeStick* > *value,
                        EventRegistrationToken *token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_ArcadeStickAdded(
                        EventRegistrationToken token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_ArcadeStickRemoved(
                        ABI::Windows::Foundation::IEventHandler<ABI::Windows::Gaming::Input::ArcadeStick* > *value,
                        EventRegistrationToken *token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_ArcadeStickRemoved(
                        EventRegistrationToken token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_ArcadeSticks(
                        ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Gaming::Input::ArcadeStick* > **value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics, 0x5c37b8c8, 0x37b1, 0x4ad8, 0x94,0x58, 0x20,0x0f,0x1a,0x30,0x01,0x8e)
#endif
#else
typedef struct __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStaticsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics *This,
        TrustLevel *trustLevel);

    /*** IArcadeStickStatics methods ***/
    HRESULT (STDMETHODCALLTYPE *add_ArcadeStickAdded)(
        __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics *This,
        __FIEventHandler_1_Windows__CGaming__CInput__CArcadeStick *value,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_ArcadeStickAdded)(
        __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics *This,
        EventRegistrationToken token);

    HRESULT (STDMETHODCALLTYPE *add_ArcadeStickRemoved)(
        __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics *This,
        __FIEventHandler_1_Windows__CGaming__CInput__CArcadeStick *value,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_ArcadeStickRemoved)(
        __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics *This,
        EventRegistrationToken token);

    HRESULT (STDMETHODCALLTYPE *get_ArcadeSticks)(
        __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics *This,
        __FIVectorView_1_Windows__CGaming__CInput__CArcadeStick **value);

    END_INTERFACE
} __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStaticsVtbl;

interface __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics {
    CONST_VTBL __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStaticsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IArcadeStickStatics methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics_add_ArcadeStickAdded(This,value,token) (This)->lpVtbl->add_ArcadeStickAdded(This,value,token)
#define __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics_remove_ArcadeStickAdded(This,token) (This)->lpVtbl->remove_ArcadeStickAdded(This,token)
#define __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics_add_ArcadeStickRemoved(This,value,token) (This)->lpVtbl->add_ArcadeStickRemoved(This,value,token)
#define __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics_remove_ArcadeStickRemoved(This,token) (This)->lpVtbl->remove_ArcadeStickRemoved(This,token)
#define __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics_get_ArcadeSticks(This,value) (This)->lpVtbl->get_ArcadeSticks(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics_QueryInterface(__x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics_AddRef(__x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics_Release(__x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics_GetIids(__x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics_GetRuntimeClassName(__x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics_GetTrustLevel(__x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IArcadeStickStatics methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics_add_ArcadeStickAdded(__x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics* This,__FIEventHandler_1_Windows__CGaming__CInput__CArcadeStick *value,EventRegistrationToken *token) {
    return This->lpVtbl->add_ArcadeStickAdded(This,value,token);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics_remove_ArcadeStickAdded(__x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_ArcadeStickAdded(This,token);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics_add_ArcadeStickRemoved(__x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics* This,__FIEventHandler_1_Windows__CGaming__CInput__CArcadeStick *value,EventRegistrationToken *token) {
    return This->lpVtbl->add_ArcadeStickRemoved(This,value,token);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics_remove_ArcadeStickRemoved(__x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_ArcadeStickRemoved(This,token);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics_get_ArcadeSticks(__x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics* This,__FIVectorView_1_Windows__CGaming__CInput__CArcadeStick **value) {
    return This->lpVtbl->get_ArcadeSticks(This,value);
}
#endif
#ifdef WIDL_using_Windows_Gaming_Input
#define IID_IArcadeStickStatics IID___x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics
#define IArcadeStickStaticsVtbl __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStaticsVtbl
#define IArcadeStickStatics __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics
#define IArcadeStickStatics_QueryInterface __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics_QueryInterface
#define IArcadeStickStatics_AddRef __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics_AddRef
#define IArcadeStickStatics_Release __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics_Release
#define IArcadeStickStatics_GetIids __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics_GetIids
#define IArcadeStickStatics_GetRuntimeClassName __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics_GetRuntimeClassName
#define IArcadeStickStatics_GetTrustLevel __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics_GetTrustLevel
#define IArcadeStickStatics_add_ArcadeStickAdded __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics_add_ArcadeStickAdded
#define IArcadeStickStatics_remove_ArcadeStickAdded __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics_remove_ArcadeStickAdded
#define IArcadeStickStatics_add_ArcadeStickRemoved __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics_add_ArcadeStickRemoved
#define IArcadeStickStatics_remove_ArcadeStickRemoved __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics_remove_ArcadeStickRemoved
#define IArcadeStickStatics_get_ArcadeSticks __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics_get_ArcadeSticks
#endif /* WIDL_using_Windows_Gaming_Input */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */

/*****************************************************************************
 * IArcadeStickStatics2 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000
#ifndef ____x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics2_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics2_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics2, 0x52b5d744, 0xbb86, 0x445a, 0xb5,0x9c, 0x59,0x6f,0x0e,0x2a,0x49,0xdf);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                MIDL_INTERFACE("52b5d744-bb86-445a-b59c-596f0e2a49df")
                IArcadeStickStatics2 : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE FromGameController(
                        ABI::Windows::Gaming::Input::IGameController *controller,
                        ABI::Windows::Gaming::Input::IArcadeStick **value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics2, 0x52b5d744, 0xbb86, 0x445a, 0xb5,0x9c, 0x59,0x6f,0x0e,0x2a,0x49,0xdf)
#endif
#else
typedef struct __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics2 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics2 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics2 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics2 *This,
        TrustLevel *trustLevel);

    /*** IArcadeStickStatics2 methods ***/
    HRESULT (STDMETHODCALLTYPE *FromGameController)(
        __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics2 *This,
        __x_ABI_CWindows_CGaming_CInput_CIGameController *controller,
        __x_ABI_CWindows_CGaming_CInput_CIArcadeStick **value);

    END_INTERFACE
} __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics2Vtbl;

interface __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics2 {
    CONST_VTBL __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics2_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics2_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics2_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics2_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IArcadeStickStatics2 methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics2_FromGameController(This,controller,value) (This)->lpVtbl->FromGameController(This,controller,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics2_QueryInterface(__x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics2_AddRef(__x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics2_Release(__x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics2* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics2_GetIids(__x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics2* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics2_GetRuntimeClassName(__x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics2* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics2_GetTrustLevel(__x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics2* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IArcadeStickStatics2 methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics2_FromGameController(__x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics2* This,__x_ABI_CWindows_CGaming_CInput_CIGameController *controller,__x_ABI_CWindows_CGaming_CInput_CIArcadeStick **value) {
    return This->lpVtbl->FromGameController(This,controller,value);
}
#endif
#ifdef WIDL_using_Windows_Gaming_Input
#define IID_IArcadeStickStatics2 IID___x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics2
#define IArcadeStickStatics2Vtbl __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics2Vtbl
#define IArcadeStickStatics2 __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics2
#define IArcadeStickStatics2_QueryInterface __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics2_QueryInterface
#define IArcadeStickStatics2_AddRef __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics2_AddRef
#define IArcadeStickStatics2_Release __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics2_Release
#define IArcadeStickStatics2_GetIids __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics2_GetIids
#define IArcadeStickStatics2_GetRuntimeClassName __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics2_GetRuntimeClassName
#define IArcadeStickStatics2_GetTrustLevel __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics2_GetTrustLevel
#define IArcadeStickStatics2_FromGameController __x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics2_FromGameController
#endif /* WIDL_using_Windows_Gaming_Input */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGaming_CInput_CIArcadeStickStatics2_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000 */

/*****************************************************************************
 * IFlightStick interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000
#ifndef ____x_ABI_CWindows_CGaming_CInput_CIFlightStick_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CIFlightStick_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGaming_CInput_CIFlightStick, 0xb4a2c01c, 0xb83b, 0x4459, 0xa1,0xa9, 0x97,0xb0,0x3c,0x33,0xda,0x7c);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                MIDL_INTERFACE("b4a2c01c-b83b-4459-a1a9-97b03c33da7c")
                IFlightStick : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_HatSwitchKind(
                        ABI::Windows::Gaming::Input::GameControllerSwitchKind *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetButtonLabel(
                        ABI::Windows::Gaming::Input::FlightStickButtons button,
                        ABI::Windows::Gaming::Input::GameControllerButtonLabel *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetCurrentReading(
                        ABI::Windows::Gaming::Input::FlightStickReading *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGaming_CInput_CIFlightStick, 0xb4a2c01c, 0xb83b, 0x4459, 0xa1,0xa9, 0x97,0xb0,0x3c,0x33,0xda,0x7c)
#endif
#else
typedef struct __x_ABI_CWindows_CGaming_CInput_CIFlightStickVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGaming_CInput_CIFlightStick *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGaming_CInput_CIFlightStick *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGaming_CInput_CIFlightStick *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGaming_CInput_CIFlightStick *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGaming_CInput_CIFlightStick *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGaming_CInput_CIFlightStick *This,
        TrustLevel *trustLevel);

    /*** IFlightStick methods ***/
    HRESULT (STDMETHODCALLTYPE *get_HatSwitchKind)(
        __x_ABI_CWindows_CGaming_CInput_CIFlightStick *This,
        __x_ABI_CWindows_CGaming_CInput_CGameControllerSwitchKind *value);

    HRESULT (STDMETHODCALLTYPE *GetButtonLabel)(
        __x_ABI_CWindows_CGaming_CInput_CIFlightStick *This,
        __x_ABI_CWindows_CGaming_CInput_CFlightStickButtons button,
        __x_ABI_CWindows_CGaming_CInput_CGameControllerButtonLabel *value);

    HRESULT (STDMETHODCALLTYPE *GetCurrentReading)(
        __x_ABI_CWindows_CGaming_CInput_CIFlightStick *This,
        __x_ABI_CWindows_CGaming_CInput_CFlightStickReading *value);

    END_INTERFACE
} __x_ABI_CWindows_CGaming_CInput_CIFlightStickVtbl;

interface __x_ABI_CWindows_CGaming_CInput_CIFlightStick {
    CONST_VTBL __x_ABI_CWindows_CGaming_CInput_CIFlightStickVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CIFlightStick_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGaming_CInput_CIFlightStick_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGaming_CInput_CIFlightStick_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CIFlightStick_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGaming_CInput_CIFlightStick_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGaming_CInput_CIFlightStick_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IFlightStick methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CIFlightStick_get_HatSwitchKind(This,value) (This)->lpVtbl->get_HatSwitchKind(This,value)
#define __x_ABI_CWindows_CGaming_CInput_CIFlightStick_GetButtonLabel(This,button,value) (This)->lpVtbl->GetButtonLabel(This,button,value)
#define __x_ABI_CWindows_CGaming_CInput_CIFlightStick_GetCurrentReading(This,value) (This)->lpVtbl->GetCurrentReading(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIFlightStick_QueryInterface(__x_ABI_CWindows_CGaming_CInput_CIFlightStick* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CIFlightStick_AddRef(__x_ABI_CWindows_CGaming_CInput_CIFlightStick* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CIFlightStick_Release(__x_ABI_CWindows_CGaming_CInput_CIFlightStick* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIFlightStick_GetIids(__x_ABI_CWindows_CGaming_CInput_CIFlightStick* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIFlightStick_GetRuntimeClassName(__x_ABI_CWindows_CGaming_CInput_CIFlightStick* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIFlightStick_GetTrustLevel(__x_ABI_CWindows_CGaming_CInput_CIFlightStick* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IFlightStick methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIFlightStick_get_HatSwitchKind(__x_ABI_CWindows_CGaming_CInput_CIFlightStick* This,__x_ABI_CWindows_CGaming_CInput_CGameControllerSwitchKind *value) {
    return This->lpVtbl->get_HatSwitchKind(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIFlightStick_GetButtonLabel(__x_ABI_CWindows_CGaming_CInput_CIFlightStick* This,__x_ABI_CWindows_CGaming_CInput_CFlightStickButtons button,__x_ABI_CWindows_CGaming_CInput_CGameControllerButtonLabel *value) {
    return This->lpVtbl->GetButtonLabel(This,button,value);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIFlightStick_GetCurrentReading(__x_ABI_CWindows_CGaming_CInput_CIFlightStick* This,__x_ABI_CWindows_CGaming_CInput_CFlightStickReading *value) {
    return This->lpVtbl->GetCurrentReading(This,value);
}
#endif
#ifdef WIDL_using_Windows_Gaming_Input
#define IID_IFlightStick IID___x_ABI_CWindows_CGaming_CInput_CIFlightStick
#define IFlightStickVtbl __x_ABI_CWindows_CGaming_CInput_CIFlightStickVtbl
#define IFlightStick __x_ABI_CWindows_CGaming_CInput_CIFlightStick
#define IFlightStick_QueryInterface __x_ABI_CWindows_CGaming_CInput_CIFlightStick_QueryInterface
#define IFlightStick_AddRef __x_ABI_CWindows_CGaming_CInput_CIFlightStick_AddRef
#define IFlightStick_Release __x_ABI_CWindows_CGaming_CInput_CIFlightStick_Release
#define IFlightStick_GetIids __x_ABI_CWindows_CGaming_CInput_CIFlightStick_GetIids
#define IFlightStick_GetRuntimeClassName __x_ABI_CWindows_CGaming_CInput_CIFlightStick_GetRuntimeClassName
#define IFlightStick_GetTrustLevel __x_ABI_CWindows_CGaming_CInput_CIFlightStick_GetTrustLevel
#define IFlightStick_get_HatSwitchKind __x_ABI_CWindows_CGaming_CInput_CIFlightStick_get_HatSwitchKind
#define IFlightStick_GetButtonLabel __x_ABI_CWindows_CGaming_CInput_CIFlightStick_GetButtonLabel
#define IFlightStick_GetCurrentReading __x_ABI_CWindows_CGaming_CInput_CIFlightStick_GetCurrentReading
#endif /* WIDL_using_Windows_Gaming_Input */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGaming_CInput_CIFlightStick_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000 */

/*****************************************************************************
 * IFlightStickStatics interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000
#ifndef ____x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics, 0x5514924a, 0xfecc, 0x435e, 0x83,0xdc, 0x5c,0xec,0x8a,0x18,0xa5,0x20);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                MIDL_INTERFACE("5514924a-fecc-435e-83dc-5cec8a18a520")
                IFlightStickStatics : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE add_FlightStickAdded(
                        ABI::Windows::Foundation::IEventHandler<ABI::Windows::Gaming::Input::FlightStick* > *value,
                        EventRegistrationToken *token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_FlightStickAdded(
                        EventRegistrationToken token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_FlightStickRemoved(
                        ABI::Windows::Foundation::IEventHandler<ABI::Windows::Gaming::Input::FlightStick* > *value,
                        EventRegistrationToken *token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_FlightStickRemoved(
                        EventRegistrationToken token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_FlightSticks(
                        ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Gaming::Input::FlightStick* > **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE FromGameController(
                        ABI::Windows::Gaming::Input::IGameController *controller,
                        ABI::Windows::Gaming::Input::IFlightStick **value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics, 0x5514924a, 0xfecc, 0x435e, 0x83,0xdc, 0x5c,0xec,0x8a,0x18,0xa5,0x20)
#endif
#else
typedef struct __x_ABI_CWindows_CGaming_CInput_CIFlightStickStaticsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics *This,
        TrustLevel *trustLevel);

    /*** IFlightStickStatics methods ***/
    HRESULT (STDMETHODCALLTYPE *add_FlightStickAdded)(
        __x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics *This,
        __FIEventHandler_1_Windows__CGaming__CInput__CFlightStick *value,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_FlightStickAdded)(
        __x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics *This,
        EventRegistrationToken token);

    HRESULT (STDMETHODCALLTYPE *add_FlightStickRemoved)(
        __x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics *This,
        __FIEventHandler_1_Windows__CGaming__CInput__CFlightStick *value,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_FlightStickRemoved)(
        __x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics *This,
        EventRegistrationToken token);

    HRESULT (STDMETHODCALLTYPE *get_FlightSticks)(
        __x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics *This,
        __FIVectorView_1_Windows__CGaming__CInput__CFlightStick **value);

    HRESULT (STDMETHODCALLTYPE *FromGameController)(
        __x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics *This,
        __x_ABI_CWindows_CGaming_CInput_CIGameController *controller,
        __x_ABI_CWindows_CGaming_CInput_CIFlightStick **value);

    END_INTERFACE
} __x_ABI_CWindows_CGaming_CInput_CIFlightStickStaticsVtbl;

interface __x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics {
    CONST_VTBL __x_ABI_CWindows_CGaming_CInput_CIFlightStickStaticsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IFlightStickStatics methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics_add_FlightStickAdded(This,value,token) (This)->lpVtbl->add_FlightStickAdded(This,value,token)
#define __x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics_remove_FlightStickAdded(This,token) (This)->lpVtbl->remove_FlightStickAdded(This,token)
#define __x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics_add_FlightStickRemoved(This,value,token) (This)->lpVtbl->add_FlightStickRemoved(This,value,token)
#define __x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics_remove_FlightStickRemoved(This,token) (This)->lpVtbl->remove_FlightStickRemoved(This,token)
#define __x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics_get_FlightSticks(This,value) (This)->lpVtbl->get_FlightSticks(This,value)
#define __x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics_FromGameController(This,controller,value) (This)->lpVtbl->FromGameController(This,controller,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics_QueryInterface(__x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics_AddRef(__x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics_Release(__x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics_GetIids(__x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics_GetRuntimeClassName(__x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics_GetTrustLevel(__x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IFlightStickStatics methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics_add_FlightStickAdded(__x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics* This,__FIEventHandler_1_Windows__CGaming__CInput__CFlightStick *value,EventRegistrationToken *token) {
    return This->lpVtbl->add_FlightStickAdded(This,value,token);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics_remove_FlightStickAdded(__x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_FlightStickAdded(This,token);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics_add_FlightStickRemoved(__x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics* This,__FIEventHandler_1_Windows__CGaming__CInput__CFlightStick *value,EventRegistrationToken *token) {
    return This->lpVtbl->add_FlightStickRemoved(This,value,token);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics_remove_FlightStickRemoved(__x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_FlightStickRemoved(This,token);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics_get_FlightSticks(__x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics* This,__FIVectorView_1_Windows__CGaming__CInput__CFlightStick **value) {
    return This->lpVtbl->get_FlightSticks(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics_FromGameController(__x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics* This,__x_ABI_CWindows_CGaming_CInput_CIGameController *controller,__x_ABI_CWindows_CGaming_CInput_CIFlightStick **value) {
    return This->lpVtbl->FromGameController(This,controller,value);
}
#endif
#ifdef WIDL_using_Windows_Gaming_Input
#define IID_IFlightStickStatics IID___x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics
#define IFlightStickStaticsVtbl __x_ABI_CWindows_CGaming_CInput_CIFlightStickStaticsVtbl
#define IFlightStickStatics __x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics
#define IFlightStickStatics_QueryInterface __x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics_QueryInterface
#define IFlightStickStatics_AddRef __x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics_AddRef
#define IFlightStickStatics_Release __x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics_Release
#define IFlightStickStatics_GetIids __x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics_GetIids
#define IFlightStickStatics_GetRuntimeClassName __x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics_GetRuntimeClassName
#define IFlightStickStatics_GetTrustLevel __x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics_GetTrustLevel
#define IFlightStickStatics_add_FlightStickAdded __x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics_add_FlightStickAdded
#define IFlightStickStatics_remove_FlightStickAdded __x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics_remove_FlightStickAdded
#define IFlightStickStatics_add_FlightStickRemoved __x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics_add_FlightStickRemoved
#define IFlightStickStatics_remove_FlightStickRemoved __x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics_remove_FlightStickRemoved
#define IFlightStickStatics_get_FlightSticks __x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics_get_FlightSticks
#define IFlightStickStatics_FromGameController __x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics_FromGameController
#endif /* WIDL_using_Windows_Gaming_Input */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGaming_CInput_CIFlightStickStatics_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000 */

/*****************************************************************************
 * IGameController interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CGaming_CInput_CIGameController_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CIGameController_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGaming_CInput_CIGameController, 0x1baf6522, 0x5f64, 0x42c5, 0x82,0x67, 0xb9,0xfe,0x22,0x15,0xbf,0xbd);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                MIDL_INTERFACE("1baf6522-5f64-42c5-8267-b9fe2215bfbd")
                IGameController : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE add_HeadsetConnected(
                        ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Gaming::Input::IGameController*,ABI::Windows::Gaming::Input::Headset* > *handler,
                        EventRegistrationToken *token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_HeadsetConnected(
                        EventRegistrationToken token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_HeadsetDisconnected(
                        ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Gaming::Input::IGameController*,ABI::Windows::Gaming::Input::Headset* > *handler,
                        EventRegistrationToken *token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_HeadsetDisconnected(
                        EventRegistrationToken token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_UserChanged(
                        ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Gaming::Input::IGameController*,ABI::Windows::System::UserChangedEventArgs* > *handler,
                        EventRegistrationToken *token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_UserChanged(
                        EventRegistrationToken token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_Headset(
                        ABI::Windows::Gaming::Input::IHeadset **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_IsWireless(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_User(
                        ABI::Windows::System::IUser **value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGaming_CInput_CIGameController, 0x1baf6522, 0x5f64, 0x42c5, 0x82,0x67, 0xb9,0xfe,0x22,0x15,0xbf,0xbd)
#endif
#else
typedef struct __x_ABI_CWindows_CGaming_CInput_CIGameControllerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGaming_CInput_CIGameController *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGaming_CInput_CIGameController *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGaming_CInput_CIGameController *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGaming_CInput_CIGameController *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGaming_CInput_CIGameController *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGaming_CInput_CIGameController *This,
        TrustLevel *trustLevel);

    /*** IGameController methods ***/
    HRESULT (STDMETHODCALLTYPE *add_HeadsetConnected)(
        __x_ABI_CWindows_CGaming_CInput_CIGameController *This,
        __FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CGaming__CInput__CHeadset *handler,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_HeadsetConnected)(
        __x_ABI_CWindows_CGaming_CInput_CIGameController *This,
        EventRegistrationToken token);

    HRESULT (STDMETHODCALLTYPE *add_HeadsetDisconnected)(
        __x_ABI_CWindows_CGaming_CInput_CIGameController *This,
        __FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CGaming__CInput__CHeadset *handler,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_HeadsetDisconnected)(
        __x_ABI_CWindows_CGaming_CInput_CIGameController *This,
        EventRegistrationToken token);

    HRESULT (STDMETHODCALLTYPE *add_UserChanged)(
        __x_ABI_CWindows_CGaming_CInput_CIGameController *This,
        __FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CSystem__CUserChangedEventArgs *handler,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_UserChanged)(
        __x_ABI_CWindows_CGaming_CInput_CIGameController *This,
        EventRegistrationToken token);

    HRESULT (STDMETHODCALLTYPE *get_Headset)(
        __x_ABI_CWindows_CGaming_CInput_CIGameController *This,
        __x_ABI_CWindows_CGaming_CInput_CIHeadset **value);

    HRESULT (STDMETHODCALLTYPE *get_IsWireless)(
        __x_ABI_CWindows_CGaming_CInput_CIGameController *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *get_User)(
        __x_ABI_CWindows_CGaming_CInput_CIGameController *This,
        __x_ABI_CWindows_CSystem_CIUser **value);

    END_INTERFACE
} __x_ABI_CWindows_CGaming_CInput_CIGameControllerVtbl;

interface __x_ABI_CWindows_CGaming_CInput_CIGameController {
    CONST_VTBL __x_ABI_CWindows_CGaming_CInput_CIGameControllerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CIGameController_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGaming_CInput_CIGameController_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGaming_CInput_CIGameController_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CIGameController_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGaming_CInput_CIGameController_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGaming_CInput_CIGameController_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IGameController methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CIGameController_add_HeadsetConnected(This,handler,token) (This)->lpVtbl->add_HeadsetConnected(This,handler,token)
#define __x_ABI_CWindows_CGaming_CInput_CIGameController_remove_HeadsetConnected(This,token) (This)->lpVtbl->remove_HeadsetConnected(This,token)
#define __x_ABI_CWindows_CGaming_CInput_CIGameController_add_HeadsetDisconnected(This,handler,token) (This)->lpVtbl->add_HeadsetDisconnected(This,handler,token)
#define __x_ABI_CWindows_CGaming_CInput_CIGameController_remove_HeadsetDisconnected(This,token) (This)->lpVtbl->remove_HeadsetDisconnected(This,token)
#define __x_ABI_CWindows_CGaming_CInput_CIGameController_add_UserChanged(This,handler,token) (This)->lpVtbl->add_UserChanged(This,handler,token)
#define __x_ABI_CWindows_CGaming_CInput_CIGameController_remove_UserChanged(This,token) (This)->lpVtbl->remove_UserChanged(This,token)
#define __x_ABI_CWindows_CGaming_CInput_CIGameController_get_Headset(This,value) (This)->lpVtbl->get_Headset(This,value)
#define __x_ABI_CWindows_CGaming_CInput_CIGameController_get_IsWireless(This,value) (This)->lpVtbl->get_IsWireless(This,value)
#define __x_ABI_CWindows_CGaming_CInput_CIGameController_get_User(This,value) (This)->lpVtbl->get_User(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIGameController_QueryInterface(__x_ABI_CWindows_CGaming_CInput_CIGameController* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CIGameController_AddRef(__x_ABI_CWindows_CGaming_CInput_CIGameController* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CIGameController_Release(__x_ABI_CWindows_CGaming_CInput_CIGameController* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIGameController_GetIids(__x_ABI_CWindows_CGaming_CInput_CIGameController* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIGameController_GetRuntimeClassName(__x_ABI_CWindows_CGaming_CInput_CIGameController* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIGameController_GetTrustLevel(__x_ABI_CWindows_CGaming_CInput_CIGameController* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IGameController methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIGameController_add_HeadsetConnected(__x_ABI_CWindows_CGaming_CInput_CIGameController* This,__FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CGaming__CInput__CHeadset *handler,EventRegistrationToken *token) {
    return This->lpVtbl->add_HeadsetConnected(This,handler,token);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIGameController_remove_HeadsetConnected(__x_ABI_CWindows_CGaming_CInput_CIGameController* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_HeadsetConnected(This,token);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIGameController_add_HeadsetDisconnected(__x_ABI_CWindows_CGaming_CInput_CIGameController* This,__FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CGaming__CInput__CHeadset *handler,EventRegistrationToken *token) {
    return This->lpVtbl->add_HeadsetDisconnected(This,handler,token);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIGameController_remove_HeadsetDisconnected(__x_ABI_CWindows_CGaming_CInput_CIGameController* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_HeadsetDisconnected(This,token);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIGameController_add_UserChanged(__x_ABI_CWindows_CGaming_CInput_CIGameController* This,__FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CSystem__CUserChangedEventArgs *handler,EventRegistrationToken *token) {
    return This->lpVtbl->add_UserChanged(This,handler,token);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIGameController_remove_UserChanged(__x_ABI_CWindows_CGaming_CInput_CIGameController* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_UserChanged(This,token);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIGameController_get_Headset(__x_ABI_CWindows_CGaming_CInput_CIGameController* This,__x_ABI_CWindows_CGaming_CInput_CIHeadset **value) {
    return This->lpVtbl->get_Headset(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIGameController_get_IsWireless(__x_ABI_CWindows_CGaming_CInput_CIGameController* This,boolean *value) {
    return This->lpVtbl->get_IsWireless(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIGameController_get_User(__x_ABI_CWindows_CGaming_CInput_CIGameController* This,__x_ABI_CWindows_CSystem_CIUser **value) {
    return This->lpVtbl->get_User(This,value);
}
#endif
#ifdef WIDL_using_Windows_Gaming_Input
#define IID_IGameController IID___x_ABI_CWindows_CGaming_CInput_CIGameController
#define IGameControllerVtbl __x_ABI_CWindows_CGaming_CInput_CIGameControllerVtbl
#define IGameController __x_ABI_CWindows_CGaming_CInput_CIGameController
#define IGameController_QueryInterface __x_ABI_CWindows_CGaming_CInput_CIGameController_QueryInterface
#define IGameController_AddRef __x_ABI_CWindows_CGaming_CInput_CIGameController_AddRef
#define IGameController_Release __x_ABI_CWindows_CGaming_CInput_CIGameController_Release
#define IGameController_GetIids __x_ABI_CWindows_CGaming_CInput_CIGameController_GetIids
#define IGameController_GetRuntimeClassName __x_ABI_CWindows_CGaming_CInput_CIGameController_GetRuntimeClassName
#define IGameController_GetTrustLevel __x_ABI_CWindows_CGaming_CInput_CIGameController_GetTrustLevel
#define IGameController_add_HeadsetConnected __x_ABI_CWindows_CGaming_CInput_CIGameController_add_HeadsetConnected
#define IGameController_remove_HeadsetConnected __x_ABI_CWindows_CGaming_CInput_CIGameController_remove_HeadsetConnected
#define IGameController_add_HeadsetDisconnected __x_ABI_CWindows_CGaming_CInput_CIGameController_add_HeadsetDisconnected
#define IGameController_remove_HeadsetDisconnected __x_ABI_CWindows_CGaming_CInput_CIGameController_remove_HeadsetDisconnected
#define IGameController_add_UserChanged __x_ABI_CWindows_CGaming_CInput_CIGameController_add_UserChanged
#define IGameController_remove_UserChanged __x_ABI_CWindows_CGaming_CInput_CIGameController_remove_UserChanged
#define IGameController_get_Headset __x_ABI_CWindows_CGaming_CInput_CIGameController_get_Headset
#define IGameController_get_IsWireless __x_ABI_CWindows_CGaming_CInput_CIGameController_get_IsWireless
#define IGameController_get_User __x_ABI_CWindows_CGaming_CInput_CIGameController_get_User
#endif /* WIDL_using_Windows_Gaming_Input */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGaming_CInput_CIGameController_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IGamepad interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CGaming_CInput_CIGamepad_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CIGamepad_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGaming_CInput_CIGamepad, 0xbc7bb43c, 0x0a69, 0x3903, 0x9e,0x9d, 0xa5,0x0f,0x86,0xa4,0x5d,0xe5);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                MIDL_INTERFACE("bc7bb43c-0a69-3903-9e9d-a50f86a45de5")
                IGamepad : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_Vibration(
                        ABI::Windows::Gaming::Input::GamepadVibration *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE put_Vibration(
                        ABI::Windows::Gaming::Input::GamepadVibration value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetCurrentReading(
                        ABI::Windows::Gaming::Input::GamepadReading *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGaming_CInput_CIGamepad, 0xbc7bb43c, 0x0a69, 0x3903, 0x9e,0x9d, 0xa5,0x0f,0x86,0xa4,0x5d,0xe5)
#endif
#else
typedef struct __x_ABI_CWindows_CGaming_CInput_CIGamepadVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGaming_CInput_CIGamepad *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGaming_CInput_CIGamepad *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGaming_CInput_CIGamepad *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGaming_CInput_CIGamepad *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGaming_CInput_CIGamepad *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGaming_CInput_CIGamepad *This,
        TrustLevel *trustLevel);

    /*** IGamepad methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Vibration)(
        __x_ABI_CWindows_CGaming_CInput_CIGamepad *This,
        __x_ABI_CWindows_CGaming_CInput_CGamepadVibration *value);

    HRESULT (STDMETHODCALLTYPE *put_Vibration)(
        __x_ABI_CWindows_CGaming_CInput_CIGamepad *This,
        __x_ABI_CWindows_CGaming_CInput_CGamepadVibration value);

    HRESULT (STDMETHODCALLTYPE *GetCurrentReading)(
        __x_ABI_CWindows_CGaming_CInput_CIGamepad *This,
        __x_ABI_CWindows_CGaming_CInput_CGamepadReading *value);

    END_INTERFACE
} __x_ABI_CWindows_CGaming_CInput_CIGamepadVtbl;

interface __x_ABI_CWindows_CGaming_CInput_CIGamepad {
    CONST_VTBL __x_ABI_CWindows_CGaming_CInput_CIGamepadVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CIGamepad_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGaming_CInput_CIGamepad_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGaming_CInput_CIGamepad_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CIGamepad_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGaming_CInput_CIGamepad_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGaming_CInput_CIGamepad_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IGamepad methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CIGamepad_get_Vibration(This,value) (This)->lpVtbl->get_Vibration(This,value)
#define __x_ABI_CWindows_CGaming_CInput_CIGamepad_put_Vibration(This,value) (This)->lpVtbl->put_Vibration(This,value)
#define __x_ABI_CWindows_CGaming_CInput_CIGamepad_GetCurrentReading(This,value) (This)->lpVtbl->GetCurrentReading(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIGamepad_QueryInterface(__x_ABI_CWindows_CGaming_CInput_CIGamepad* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CIGamepad_AddRef(__x_ABI_CWindows_CGaming_CInput_CIGamepad* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CIGamepad_Release(__x_ABI_CWindows_CGaming_CInput_CIGamepad* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIGamepad_GetIids(__x_ABI_CWindows_CGaming_CInput_CIGamepad* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIGamepad_GetRuntimeClassName(__x_ABI_CWindows_CGaming_CInput_CIGamepad* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIGamepad_GetTrustLevel(__x_ABI_CWindows_CGaming_CInput_CIGamepad* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IGamepad methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIGamepad_get_Vibration(__x_ABI_CWindows_CGaming_CInput_CIGamepad* This,__x_ABI_CWindows_CGaming_CInput_CGamepadVibration *value) {
    return This->lpVtbl->get_Vibration(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIGamepad_put_Vibration(__x_ABI_CWindows_CGaming_CInput_CIGamepad* This,__x_ABI_CWindows_CGaming_CInput_CGamepadVibration value) {
    return This->lpVtbl->put_Vibration(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIGamepad_GetCurrentReading(__x_ABI_CWindows_CGaming_CInput_CIGamepad* This,__x_ABI_CWindows_CGaming_CInput_CGamepadReading *value) {
    return This->lpVtbl->GetCurrentReading(This,value);
}
#endif
#ifdef WIDL_using_Windows_Gaming_Input
#define IID_IGamepad IID___x_ABI_CWindows_CGaming_CInput_CIGamepad
#define IGamepadVtbl __x_ABI_CWindows_CGaming_CInput_CIGamepadVtbl
#define IGamepad __x_ABI_CWindows_CGaming_CInput_CIGamepad
#define IGamepad_QueryInterface __x_ABI_CWindows_CGaming_CInput_CIGamepad_QueryInterface
#define IGamepad_AddRef __x_ABI_CWindows_CGaming_CInput_CIGamepad_AddRef
#define IGamepad_Release __x_ABI_CWindows_CGaming_CInput_CIGamepad_Release
#define IGamepad_GetIids __x_ABI_CWindows_CGaming_CInput_CIGamepad_GetIids
#define IGamepad_GetRuntimeClassName __x_ABI_CWindows_CGaming_CInput_CIGamepad_GetRuntimeClassName
#define IGamepad_GetTrustLevel __x_ABI_CWindows_CGaming_CInput_CIGamepad_GetTrustLevel
#define IGamepad_get_Vibration __x_ABI_CWindows_CGaming_CInput_CIGamepad_get_Vibration
#define IGamepad_put_Vibration __x_ABI_CWindows_CGaming_CInput_CIGamepad_put_Vibration
#define IGamepad_GetCurrentReading __x_ABI_CWindows_CGaming_CInput_CIGamepad_GetCurrentReading
#endif /* WIDL_using_Windows_Gaming_Input */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGaming_CInput_CIGamepad_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IGamepad2 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
#ifndef ____x_ABI_CWindows_CGaming_CInput_CIGamepad2_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CIGamepad2_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGaming_CInput_CIGamepad2, 0x3c1689bd, 0x5915, 0x4245, 0xb0,0xc0, 0xc8,0x9f,0xae,0x03,0x08,0xff);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                MIDL_INTERFACE("3c1689bd-5915-4245-b0c0-c89fae0308ff")
                IGamepad2 : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE GetButtonLabel(
                        ABI::Windows::Gaming::Input::GamepadButtons button,
                        ABI::Windows::Gaming::Input::GameControllerButtonLabel *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGaming_CInput_CIGamepad2, 0x3c1689bd, 0x5915, 0x4245, 0xb0,0xc0, 0xc8,0x9f,0xae,0x03,0x08,0xff)
#endif
#else
typedef struct __x_ABI_CWindows_CGaming_CInput_CIGamepad2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGaming_CInput_CIGamepad2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGaming_CInput_CIGamepad2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGaming_CInput_CIGamepad2 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGaming_CInput_CIGamepad2 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGaming_CInput_CIGamepad2 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGaming_CInput_CIGamepad2 *This,
        TrustLevel *trustLevel);

    /*** IGamepad2 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetButtonLabel)(
        __x_ABI_CWindows_CGaming_CInput_CIGamepad2 *This,
        __x_ABI_CWindows_CGaming_CInput_CGamepadButtons button,
        __x_ABI_CWindows_CGaming_CInput_CGameControllerButtonLabel *value);

    END_INTERFACE
} __x_ABI_CWindows_CGaming_CInput_CIGamepad2Vtbl;

interface __x_ABI_CWindows_CGaming_CInput_CIGamepad2 {
    CONST_VTBL __x_ABI_CWindows_CGaming_CInput_CIGamepad2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CIGamepad2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGaming_CInput_CIGamepad2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGaming_CInput_CIGamepad2_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CIGamepad2_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGaming_CInput_CIGamepad2_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGaming_CInput_CIGamepad2_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IGamepad2 methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CIGamepad2_GetButtonLabel(This,button,value) (This)->lpVtbl->GetButtonLabel(This,button,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIGamepad2_QueryInterface(__x_ABI_CWindows_CGaming_CInput_CIGamepad2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CIGamepad2_AddRef(__x_ABI_CWindows_CGaming_CInput_CIGamepad2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CIGamepad2_Release(__x_ABI_CWindows_CGaming_CInput_CIGamepad2* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIGamepad2_GetIids(__x_ABI_CWindows_CGaming_CInput_CIGamepad2* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIGamepad2_GetRuntimeClassName(__x_ABI_CWindows_CGaming_CInput_CIGamepad2* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIGamepad2_GetTrustLevel(__x_ABI_CWindows_CGaming_CInput_CIGamepad2* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IGamepad2 methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIGamepad2_GetButtonLabel(__x_ABI_CWindows_CGaming_CInput_CIGamepad2* This,__x_ABI_CWindows_CGaming_CInput_CGamepadButtons button,__x_ABI_CWindows_CGaming_CInput_CGameControllerButtonLabel *value) {
    return This->lpVtbl->GetButtonLabel(This,button,value);
}
#endif
#ifdef WIDL_using_Windows_Gaming_Input
#define IID_IGamepad2 IID___x_ABI_CWindows_CGaming_CInput_CIGamepad2
#define IGamepad2Vtbl __x_ABI_CWindows_CGaming_CInput_CIGamepad2Vtbl
#define IGamepad2 __x_ABI_CWindows_CGaming_CInput_CIGamepad2
#define IGamepad2_QueryInterface __x_ABI_CWindows_CGaming_CInput_CIGamepad2_QueryInterface
#define IGamepad2_AddRef __x_ABI_CWindows_CGaming_CInput_CIGamepad2_AddRef
#define IGamepad2_Release __x_ABI_CWindows_CGaming_CInput_CIGamepad2_Release
#define IGamepad2_GetIids __x_ABI_CWindows_CGaming_CInput_CIGamepad2_GetIids
#define IGamepad2_GetRuntimeClassName __x_ABI_CWindows_CGaming_CInput_CIGamepad2_GetRuntimeClassName
#define IGamepad2_GetTrustLevel __x_ABI_CWindows_CGaming_CInput_CIGamepad2_GetTrustLevel
#define IGamepad2_GetButtonLabel __x_ABI_CWindows_CGaming_CInput_CIGamepad2_GetButtonLabel
#endif /* WIDL_using_Windows_Gaming_Input */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGaming_CInput_CIGamepad2_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */

/*****************************************************************************
 * IRacingWheel interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
#ifndef ____x_ABI_CWindows_CGaming_CInput_CIRacingWheel_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CIRacingWheel_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGaming_CInput_CIRacingWheel, 0xf546656f, 0xe106, 0x4c82, 0xa9,0x0f, 0x55,0x40,0x12,0x90,0x4b,0x85);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                MIDL_INTERFACE("f546656f-e106-4c82-a90f-554012904b85")
                IRacingWheel : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_HasClutch(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_HasHandbrake(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_HasPatternShifter(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_MaxPatternShifterGear(
                        INT32 *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_MaxWheelAngle(
                        DOUBLE *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_WheelMotor(
                        ABI::Windows::Gaming::Input::ForceFeedback::IForceFeedbackMotor **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetButtonLabel(
                        ABI::Windows::Gaming::Input::RacingWheelButtons button,
                        ABI::Windows::Gaming::Input::GameControllerButtonLabel *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetCurrentReading(
                        ABI::Windows::Gaming::Input::RacingWheelReading *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGaming_CInput_CIRacingWheel, 0xf546656f, 0xe106, 0x4c82, 0xa9,0x0f, 0x55,0x40,0x12,0x90,0x4b,0x85)
#endif
#else
typedef struct __x_ABI_CWindows_CGaming_CInput_CIRacingWheelVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGaming_CInput_CIRacingWheel *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGaming_CInput_CIRacingWheel *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGaming_CInput_CIRacingWheel *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGaming_CInput_CIRacingWheel *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGaming_CInput_CIRacingWheel *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGaming_CInput_CIRacingWheel *This,
        TrustLevel *trustLevel);

    /*** IRacingWheel methods ***/
    HRESULT (STDMETHODCALLTYPE *get_HasClutch)(
        __x_ABI_CWindows_CGaming_CInput_CIRacingWheel *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *get_HasHandbrake)(
        __x_ABI_CWindows_CGaming_CInput_CIRacingWheel *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *get_HasPatternShifter)(
        __x_ABI_CWindows_CGaming_CInput_CIRacingWheel *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *get_MaxPatternShifterGear)(
        __x_ABI_CWindows_CGaming_CInput_CIRacingWheel *This,
        INT32 *value);

    HRESULT (STDMETHODCALLTYPE *get_MaxWheelAngle)(
        __x_ABI_CWindows_CGaming_CInput_CIRacingWheel *This,
        DOUBLE *value);

    HRESULT (STDMETHODCALLTYPE *get_WheelMotor)(
        __x_ABI_CWindows_CGaming_CInput_CIRacingWheel *This,
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor **value);

    HRESULT (STDMETHODCALLTYPE *GetButtonLabel)(
        __x_ABI_CWindows_CGaming_CInput_CIRacingWheel *This,
        __x_ABI_CWindows_CGaming_CInput_CRacingWheelButtons button,
        __x_ABI_CWindows_CGaming_CInput_CGameControllerButtonLabel *value);

    HRESULT (STDMETHODCALLTYPE *GetCurrentReading)(
        __x_ABI_CWindows_CGaming_CInput_CIRacingWheel *This,
        __x_ABI_CWindows_CGaming_CInput_CRacingWheelReading *value);

    END_INTERFACE
} __x_ABI_CWindows_CGaming_CInput_CIRacingWheelVtbl;

interface __x_ABI_CWindows_CGaming_CInput_CIRacingWheel {
    CONST_VTBL __x_ABI_CWindows_CGaming_CInput_CIRacingWheelVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CIRacingWheel_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGaming_CInput_CIRacingWheel_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGaming_CInput_CIRacingWheel_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CIRacingWheel_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGaming_CInput_CIRacingWheel_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGaming_CInput_CIRacingWheel_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IRacingWheel methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CIRacingWheel_get_HasClutch(This,value) (This)->lpVtbl->get_HasClutch(This,value)
#define __x_ABI_CWindows_CGaming_CInput_CIRacingWheel_get_HasHandbrake(This,value) (This)->lpVtbl->get_HasHandbrake(This,value)
#define __x_ABI_CWindows_CGaming_CInput_CIRacingWheel_get_HasPatternShifter(This,value) (This)->lpVtbl->get_HasPatternShifter(This,value)
#define __x_ABI_CWindows_CGaming_CInput_CIRacingWheel_get_MaxPatternShifterGear(This,value) (This)->lpVtbl->get_MaxPatternShifterGear(This,value)
#define __x_ABI_CWindows_CGaming_CInput_CIRacingWheel_get_MaxWheelAngle(This,value) (This)->lpVtbl->get_MaxWheelAngle(This,value)
#define __x_ABI_CWindows_CGaming_CInput_CIRacingWheel_get_WheelMotor(This,value) (This)->lpVtbl->get_WheelMotor(This,value)
#define __x_ABI_CWindows_CGaming_CInput_CIRacingWheel_GetButtonLabel(This,button,value) (This)->lpVtbl->GetButtonLabel(This,button,value)
#define __x_ABI_CWindows_CGaming_CInput_CIRacingWheel_GetCurrentReading(This,value) (This)->lpVtbl->GetCurrentReading(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIRacingWheel_QueryInterface(__x_ABI_CWindows_CGaming_CInput_CIRacingWheel* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CIRacingWheel_AddRef(__x_ABI_CWindows_CGaming_CInput_CIRacingWheel* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CIRacingWheel_Release(__x_ABI_CWindows_CGaming_CInput_CIRacingWheel* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIRacingWheel_GetIids(__x_ABI_CWindows_CGaming_CInput_CIRacingWheel* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIRacingWheel_GetRuntimeClassName(__x_ABI_CWindows_CGaming_CInput_CIRacingWheel* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIRacingWheel_GetTrustLevel(__x_ABI_CWindows_CGaming_CInput_CIRacingWheel* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IRacingWheel methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIRacingWheel_get_HasClutch(__x_ABI_CWindows_CGaming_CInput_CIRacingWheel* This,boolean *value) {
    return This->lpVtbl->get_HasClutch(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIRacingWheel_get_HasHandbrake(__x_ABI_CWindows_CGaming_CInput_CIRacingWheel* This,boolean *value) {
    return This->lpVtbl->get_HasHandbrake(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIRacingWheel_get_HasPatternShifter(__x_ABI_CWindows_CGaming_CInput_CIRacingWheel* This,boolean *value) {
    return This->lpVtbl->get_HasPatternShifter(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIRacingWheel_get_MaxPatternShifterGear(__x_ABI_CWindows_CGaming_CInput_CIRacingWheel* This,INT32 *value) {
    return This->lpVtbl->get_MaxPatternShifterGear(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIRacingWheel_get_MaxWheelAngle(__x_ABI_CWindows_CGaming_CInput_CIRacingWheel* This,DOUBLE *value) {
    return This->lpVtbl->get_MaxWheelAngle(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIRacingWheel_get_WheelMotor(__x_ABI_CWindows_CGaming_CInput_CIRacingWheel* This,__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor **value) {
    return This->lpVtbl->get_WheelMotor(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIRacingWheel_GetButtonLabel(__x_ABI_CWindows_CGaming_CInput_CIRacingWheel* This,__x_ABI_CWindows_CGaming_CInput_CRacingWheelButtons button,__x_ABI_CWindows_CGaming_CInput_CGameControllerButtonLabel *value) {
    return This->lpVtbl->GetButtonLabel(This,button,value);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIRacingWheel_GetCurrentReading(__x_ABI_CWindows_CGaming_CInput_CIRacingWheel* This,__x_ABI_CWindows_CGaming_CInput_CRacingWheelReading *value) {
    return This->lpVtbl->GetCurrentReading(This,value);
}
#endif
#ifdef WIDL_using_Windows_Gaming_Input
#define IID_IRacingWheel IID___x_ABI_CWindows_CGaming_CInput_CIRacingWheel
#define IRacingWheelVtbl __x_ABI_CWindows_CGaming_CInput_CIRacingWheelVtbl
#define IRacingWheel __x_ABI_CWindows_CGaming_CInput_CIRacingWheel
#define IRacingWheel_QueryInterface __x_ABI_CWindows_CGaming_CInput_CIRacingWheel_QueryInterface
#define IRacingWheel_AddRef __x_ABI_CWindows_CGaming_CInput_CIRacingWheel_AddRef
#define IRacingWheel_Release __x_ABI_CWindows_CGaming_CInput_CIRacingWheel_Release
#define IRacingWheel_GetIids __x_ABI_CWindows_CGaming_CInput_CIRacingWheel_GetIids
#define IRacingWheel_GetRuntimeClassName __x_ABI_CWindows_CGaming_CInput_CIRacingWheel_GetRuntimeClassName
#define IRacingWheel_GetTrustLevel __x_ABI_CWindows_CGaming_CInput_CIRacingWheel_GetTrustLevel
#define IRacingWheel_get_HasClutch __x_ABI_CWindows_CGaming_CInput_CIRacingWheel_get_HasClutch
#define IRacingWheel_get_HasHandbrake __x_ABI_CWindows_CGaming_CInput_CIRacingWheel_get_HasHandbrake
#define IRacingWheel_get_HasPatternShifter __x_ABI_CWindows_CGaming_CInput_CIRacingWheel_get_HasPatternShifter
#define IRacingWheel_get_MaxPatternShifterGear __x_ABI_CWindows_CGaming_CInput_CIRacingWheel_get_MaxPatternShifterGear
#define IRacingWheel_get_MaxWheelAngle __x_ABI_CWindows_CGaming_CInput_CIRacingWheel_get_MaxWheelAngle
#define IRacingWheel_get_WheelMotor __x_ABI_CWindows_CGaming_CInput_CIRacingWheel_get_WheelMotor
#define IRacingWheel_GetButtonLabel __x_ABI_CWindows_CGaming_CInput_CIRacingWheel_GetButtonLabel
#define IRacingWheel_GetCurrentReading __x_ABI_CWindows_CGaming_CInput_CIRacingWheel_GetCurrentReading
#endif /* WIDL_using_Windows_Gaming_Input */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGaming_CInput_CIRacingWheel_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */

/*****************************************************************************
 * IRacingWheelStatics interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
#ifndef ____x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics, 0x3ac12cd5, 0x581b, 0x4936, 0x9f,0x94, 0x69,0xf1,0xe6,0x51,0x4c,0x7d);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                MIDL_INTERFACE("3ac12cd5-581b-4936-9f94-69f1e6514c7d")
                IRacingWheelStatics : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE add_RacingWheelAdded(
                        ABI::Windows::Foundation::IEventHandler<ABI::Windows::Gaming::Input::RacingWheel* > *value,
                        EventRegistrationToken *token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_RacingWheelAdded(
                        EventRegistrationToken token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_RacingWheelRemoved(
                        ABI::Windows::Foundation::IEventHandler<ABI::Windows::Gaming::Input::RacingWheel* > *value,
                        EventRegistrationToken *token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_RacingWheelRemoved(
                        EventRegistrationToken token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_RacingWheels(
                        ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Gaming::Input::RacingWheel* > **value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics, 0x3ac12cd5, 0x581b, 0x4936, 0x9f,0x94, 0x69,0xf1,0xe6,0x51,0x4c,0x7d)
#endif
#else
typedef struct __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStaticsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics *This,
        TrustLevel *trustLevel);

    /*** IRacingWheelStatics methods ***/
    HRESULT (STDMETHODCALLTYPE *add_RacingWheelAdded)(
        __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics *This,
        __FIEventHandler_1_Windows__CGaming__CInput__CRacingWheel *value,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_RacingWheelAdded)(
        __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics *This,
        EventRegistrationToken token);

    HRESULT (STDMETHODCALLTYPE *add_RacingWheelRemoved)(
        __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics *This,
        __FIEventHandler_1_Windows__CGaming__CInput__CRacingWheel *value,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_RacingWheelRemoved)(
        __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics *This,
        EventRegistrationToken token);

    HRESULT (STDMETHODCALLTYPE *get_RacingWheels)(
        __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics *This,
        __FIVectorView_1_Windows__CGaming__CInput__CRacingWheel **value);

    END_INTERFACE
} __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStaticsVtbl;

interface __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics {
    CONST_VTBL __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStaticsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IRacingWheelStatics methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics_add_RacingWheelAdded(This,value,token) (This)->lpVtbl->add_RacingWheelAdded(This,value,token)
#define __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics_remove_RacingWheelAdded(This,token) (This)->lpVtbl->remove_RacingWheelAdded(This,token)
#define __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics_add_RacingWheelRemoved(This,value,token) (This)->lpVtbl->add_RacingWheelRemoved(This,value,token)
#define __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics_remove_RacingWheelRemoved(This,token) (This)->lpVtbl->remove_RacingWheelRemoved(This,token)
#define __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics_get_RacingWheels(This,value) (This)->lpVtbl->get_RacingWheels(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics_QueryInterface(__x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics_AddRef(__x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics_Release(__x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics_GetIids(__x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics_GetRuntimeClassName(__x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics_GetTrustLevel(__x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IRacingWheelStatics methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics_add_RacingWheelAdded(__x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics* This,__FIEventHandler_1_Windows__CGaming__CInput__CRacingWheel *value,EventRegistrationToken *token) {
    return This->lpVtbl->add_RacingWheelAdded(This,value,token);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics_remove_RacingWheelAdded(__x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_RacingWheelAdded(This,token);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics_add_RacingWheelRemoved(__x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics* This,__FIEventHandler_1_Windows__CGaming__CInput__CRacingWheel *value,EventRegistrationToken *token) {
    return This->lpVtbl->add_RacingWheelRemoved(This,value,token);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics_remove_RacingWheelRemoved(__x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_RacingWheelRemoved(This,token);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics_get_RacingWheels(__x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics* This,__FIVectorView_1_Windows__CGaming__CInput__CRacingWheel **value) {
    return This->lpVtbl->get_RacingWheels(This,value);
}
#endif
#ifdef WIDL_using_Windows_Gaming_Input
#define IID_IRacingWheelStatics IID___x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics
#define IRacingWheelStaticsVtbl __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStaticsVtbl
#define IRacingWheelStatics __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics
#define IRacingWheelStatics_QueryInterface __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics_QueryInterface
#define IRacingWheelStatics_AddRef __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics_AddRef
#define IRacingWheelStatics_Release __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics_Release
#define IRacingWheelStatics_GetIids __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics_GetIids
#define IRacingWheelStatics_GetRuntimeClassName __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics_GetRuntimeClassName
#define IRacingWheelStatics_GetTrustLevel __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics_GetTrustLevel
#define IRacingWheelStatics_add_RacingWheelAdded __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics_add_RacingWheelAdded
#define IRacingWheelStatics_remove_RacingWheelAdded __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics_remove_RacingWheelAdded
#define IRacingWheelStatics_add_RacingWheelRemoved __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics_add_RacingWheelRemoved
#define IRacingWheelStatics_remove_RacingWheelRemoved __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics_remove_RacingWheelRemoved
#define IRacingWheelStatics_get_RacingWheels __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics_get_RacingWheels
#endif /* WIDL_using_Windows_Gaming_Input */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */

/*****************************************************************************
 * IRacingWheelStatics2 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000
#ifndef ____x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics2_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics2_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics2, 0xe666bcaa, 0xedfd, 0x4323, 0xa9,0xf6, 0x3c,0x38,0x40,0x48,0xd1,0xed);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                MIDL_INTERFACE("e666bcaa-edfd-4323-a9f6-3c384048d1ed")
                IRacingWheelStatics2 : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE FromGameController(
                        ABI::Windows::Gaming::Input::IGameController *controller,
                        ABI::Windows::Gaming::Input::IRacingWheel **value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics2, 0xe666bcaa, 0xedfd, 0x4323, 0xa9,0xf6, 0x3c,0x38,0x40,0x48,0xd1,0xed)
#endif
#else
typedef struct __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics2 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics2 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics2 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics2 *This,
        TrustLevel *trustLevel);

    /*** IRacingWheelStatics2 methods ***/
    HRESULT (STDMETHODCALLTYPE *FromGameController)(
        __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics2 *This,
        __x_ABI_CWindows_CGaming_CInput_CIGameController *controller,
        __x_ABI_CWindows_CGaming_CInput_CIRacingWheel **value);

    END_INTERFACE
} __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics2Vtbl;

interface __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics2 {
    CONST_VTBL __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics2_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics2_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics2_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics2_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IRacingWheelStatics2 methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics2_FromGameController(This,controller,value) (This)->lpVtbl->FromGameController(This,controller,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics2_QueryInterface(__x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics2_AddRef(__x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics2_Release(__x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics2* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics2_GetIids(__x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics2* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics2_GetRuntimeClassName(__x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics2* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics2_GetTrustLevel(__x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics2* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IRacingWheelStatics2 methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics2_FromGameController(__x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics2* This,__x_ABI_CWindows_CGaming_CInput_CIGameController *controller,__x_ABI_CWindows_CGaming_CInput_CIRacingWheel **value) {
    return This->lpVtbl->FromGameController(This,controller,value);
}
#endif
#ifdef WIDL_using_Windows_Gaming_Input
#define IID_IRacingWheelStatics2 IID___x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics2
#define IRacingWheelStatics2Vtbl __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics2Vtbl
#define IRacingWheelStatics2 __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics2
#define IRacingWheelStatics2_QueryInterface __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics2_QueryInterface
#define IRacingWheelStatics2_AddRef __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics2_AddRef
#define IRacingWheelStatics2_Release __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics2_Release
#define IRacingWheelStatics2_GetIids __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics2_GetIids
#define IRacingWheelStatics2_GetRuntimeClassName __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics2_GetRuntimeClassName
#define IRacingWheelStatics2_GetTrustLevel __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics2_GetTrustLevel
#define IRacingWheelStatics2_FromGameController __x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics2_FromGameController
#endif /* WIDL_using_Windows_Gaming_Input */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGaming_CInput_CIRacingWheelStatics2_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000 */

/*****************************************************************************
 * IRawGameController interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000
#ifndef ____x_ABI_CWindows_CGaming_CInput_CIRawGameController_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CIRawGameController_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGaming_CInput_CIRawGameController, 0x7cad6d91, 0xa7e1, 0x4f71, 0x9a,0x78, 0x33,0xe9,0xc5,0xdf,0xea,0x62);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                MIDL_INTERFACE("7cad6d91-a7e1-4f71-9a78-33e9c5dfea62")
                IRawGameController : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_AxisCount(
                        INT32 *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_ButtonCount(
                        INT32 *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_ForceFeedbackMotors(
                        ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Gaming::Input::ForceFeedback::ForceFeedbackMotor* > **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_HardwareProductId(
                        UINT16 *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_HardwareVendorId(
                        UINT16 *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_SwitchCount(
                        INT32 *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetButtonLabel(
                        INT32 index,
                        ABI::Windows::Gaming::Input::GameControllerButtonLabel *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetCurrentReading(
                        UINT32 buttons_size,
                        boolean *buttons,
                        UINT32 switches_size,
                        ABI::Windows::Gaming::Input::GameControllerSwitchPosition *switches,
                        UINT32 axes_size,
                        DOUBLE *axes,
                        UINT64 *timestamp) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetSwitchKind(
                        INT32 index,
                        ABI::Windows::Gaming::Input::GameControllerSwitchKind *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGaming_CInput_CIRawGameController, 0x7cad6d91, 0xa7e1, 0x4f71, 0x9a,0x78, 0x33,0xe9,0xc5,0xdf,0xea,0x62)
#endif
#else
typedef struct __x_ABI_CWindows_CGaming_CInput_CIRawGameControllerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGaming_CInput_CIRawGameController *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGaming_CInput_CIRawGameController *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGaming_CInput_CIRawGameController *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGaming_CInput_CIRawGameController *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGaming_CInput_CIRawGameController *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGaming_CInput_CIRawGameController *This,
        TrustLevel *trustLevel);

    /*** IRawGameController methods ***/
    HRESULT (STDMETHODCALLTYPE *get_AxisCount)(
        __x_ABI_CWindows_CGaming_CInput_CIRawGameController *This,
        INT32 *value);

    HRESULT (STDMETHODCALLTYPE *get_ButtonCount)(
        __x_ABI_CWindows_CGaming_CInput_CIRawGameController *This,
        INT32 *value);

    HRESULT (STDMETHODCALLTYPE *get_ForceFeedbackMotors)(
        __x_ABI_CWindows_CGaming_CInput_CIRawGameController *This,
        __FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor **value);

    HRESULT (STDMETHODCALLTYPE *get_HardwareProductId)(
        __x_ABI_CWindows_CGaming_CInput_CIRawGameController *This,
        UINT16 *value);

    HRESULT (STDMETHODCALLTYPE *get_HardwareVendorId)(
        __x_ABI_CWindows_CGaming_CInput_CIRawGameController *This,
        UINT16 *value);

    HRESULT (STDMETHODCALLTYPE *get_SwitchCount)(
        __x_ABI_CWindows_CGaming_CInput_CIRawGameController *This,
        INT32 *value);

    HRESULT (STDMETHODCALLTYPE *GetButtonLabel)(
        __x_ABI_CWindows_CGaming_CInput_CIRawGameController *This,
        INT32 index,
        __x_ABI_CWindows_CGaming_CInput_CGameControllerButtonLabel *value);

    HRESULT (STDMETHODCALLTYPE *GetCurrentReading)(
        __x_ABI_CWindows_CGaming_CInput_CIRawGameController *This,
        UINT32 buttons_size,
        boolean *buttons,
        UINT32 switches_size,
        __x_ABI_CWindows_CGaming_CInput_CGameControllerSwitchPosition *switches,
        UINT32 axes_size,
        DOUBLE *axes,
        UINT64 *timestamp);

    HRESULT (STDMETHODCALLTYPE *GetSwitchKind)(
        __x_ABI_CWindows_CGaming_CInput_CIRawGameController *This,
        INT32 index,
        __x_ABI_CWindows_CGaming_CInput_CGameControllerSwitchKind *value);

    END_INTERFACE
} __x_ABI_CWindows_CGaming_CInput_CIRawGameControllerVtbl;

interface __x_ABI_CWindows_CGaming_CInput_CIRawGameController {
    CONST_VTBL __x_ABI_CWindows_CGaming_CInput_CIRawGameControllerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CIRawGameController_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGaming_CInput_CIRawGameController_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGaming_CInput_CIRawGameController_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CIRawGameController_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGaming_CInput_CIRawGameController_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGaming_CInput_CIRawGameController_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IRawGameController methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CIRawGameController_get_AxisCount(This,value) (This)->lpVtbl->get_AxisCount(This,value)
#define __x_ABI_CWindows_CGaming_CInput_CIRawGameController_get_ButtonCount(This,value) (This)->lpVtbl->get_ButtonCount(This,value)
#define __x_ABI_CWindows_CGaming_CInput_CIRawGameController_get_ForceFeedbackMotors(This,value) (This)->lpVtbl->get_ForceFeedbackMotors(This,value)
#define __x_ABI_CWindows_CGaming_CInput_CIRawGameController_get_HardwareProductId(This,value) (This)->lpVtbl->get_HardwareProductId(This,value)
#define __x_ABI_CWindows_CGaming_CInput_CIRawGameController_get_HardwareVendorId(This,value) (This)->lpVtbl->get_HardwareVendorId(This,value)
#define __x_ABI_CWindows_CGaming_CInput_CIRawGameController_get_SwitchCount(This,value) (This)->lpVtbl->get_SwitchCount(This,value)
#define __x_ABI_CWindows_CGaming_CInput_CIRawGameController_GetButtonLabel(This,index,value) (This)->lpVtbl->GetButtonLabel(This,index,value)
#define __x_ABI_CWindows_CGaming_CInput_CIRawGameController_GetCurrentReading(This,buttons_size,buttons,switches_size,switches,axes_size,axes,timestamp) (This)->lpVtbl->GetCurrentReading(This,buttons_size,buttons,switches_size,switches,axes_size,axes,timestamp)
#define __x_ABI_CWindows_CGaming_CInput_CIRawGameController_GetSwitchKind(This,index,value) (This)->lpVtbl->GetSwitchKind(This,index,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIRawGameController_QueryInterface(__x_ABI_CWindows_CGaming_CInput_CIRawGameController* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CIRawGameController_AddRef(__x_ABI_CWindows_CGaming_CInput_CIRawGameController* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CIRawGameController_Release(__x_ABI_CWindows_CGaming_CInput_CIRawGameController* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIRawGameController_GetIids(__x_ABI_CWindows_CGaming_CInput_CIRawGameController* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIRawGameController_GetRuntimeClassName(__x_ABI_CWindows_CGaming_CInput_CIRawGameController* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIRawGameController_GetTrustLevel(__x_ABI_CWindows_CGaming_CInput_CIRawGameController* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IRawGameController methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIRawGameController_get_AxisCount(__x_ABI_CWindows_CGaming_CInput_CIRawGameController* This,INT32 *value) {
    return This->lpVtbl->get_AxisCount(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIRawGameController_get_ButtonCount(__x_ABI_CWindows_CGaming_CInput_CIRawGameController* This,INT32 *value) {
    return This->lpVtbl->get_ButtonCount(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIRawGameController_get_ForceFeedbackMotors(__x_ABI_CWindows_CGaming_CInput_CIRawGameController* This,__FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor **value) {
    return This->lpVtbl->get_ForceFeedbackMotors(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIRawGameController_get_HardwareProductId(__x_ABI_CWindows_CGaming_CInput_CIRawGameController* This,UINT16 *value) {
    return This->lpVtbl->get_HardwareProductId(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIRawGameController_get_HardwareVendorId(__x_ABI_CWindows_CGaming_CInput_CIRawGameController* This,UINT16 *value) {
    return This->lpVtbl->get_HardwareVendorId(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIRawGameController_get_SwitchCount(__x_ABI_CWindows_CGaming_CInput_CIRawGameController* This,INT32 *value) {
    return This->lpVtbl->get_SwitchCount(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIRawGameController_GetButtonLabel(__x_ABI_CWindows_CGaming_CInput_CIRawGameController* This,INT32 index,__x_ABI_CWindows_CGaming_CInput_CGameControllerButtonLabel *value) {
    return This->lpVtbl->GetButtonLabel(This,index,value);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIRawGameController_GetCurrentReading(__x_ABI_CWindows_CGaming_CInput_CIRawGameController* This,UINT32 buttons_size,boolean *buttons,UINT32 switches_size,__x_ABI_CWindows_CGaming_CInput_CGameControllerSwitchPosition *switches,UINT32 axes_size,DOUBLE *axes,UINT64 *timestamp) {
    return This->lpVtbl->GetCurrentReading(This,buttons_size,buttons,switches_size,switches,axes_size,axes,timestamp);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIRawGameController_GetSwitchKind(__x_ABI_CWindows_CGaming_CInput_CIRawGameController* This,INT32 index,__x_ABI_CWindows_CGaming_CInput_CGameControllerSwitchKind *value) {
    return This->lpVtbl->GetSwitchKind(This,index,value);
}
#endif
#ifdef WIDL_using_Windows_Gaming_Input
#define IID_IRawGameController IID___x_ABI_CWindows_CGaming_CInput_CIRawGameController
#define IRawGameControllerVtbl __x_ABI_CWindows_CGaming_CInput_CIRawGameControllerVtbl
#define IRawGameController __x_ABI_CWindows_CGaming_CInput_CIRawGameController
#define IRawGameController_QueryInterface __x_ABI_CWindows_CGaming_CInput_CIRawGameController_QueryInterface
#define IRawGameController_AddRef __x_ABI_CWindows_CGaming_CInput_CIRawGameController_AddRef
#define IRawGameController_Release __x_ABI_CWindows_CGaming_CInput_CIRawGameController_Release
#define IRawGameController_GetIids __x_ABI_CWindows_CGaming_CInput_CIRawGameController_GetIids
#define IRawGameController_GetRuntimeClassName __x_ABI_CWindows_CGaming_CInput_CIRawGameController_GetRuntimeClassName
#define IRawGameController_GetTrustLevel __x_ABI_CWindows_CGaming_CInput_CIRawGameController_GetTrustLevel
#define IRawGameController_get_AxisCount __x_ABI_CWindows_CGaming_CInput_CIRawGameController_get_AxisCount
#define IRawGameController_get_ButtonCount __x_ABI_CWindows_CGaming_CInput_CIRawGameController_get_ButtonCount
#define IRawGameController_get_ForceFeedbackMotors __x_ABI_CWindows_CGaming_CInput_CIRawGameController_get_ForceFeedbackMotors
#define IRawGameController_get_HardwareProductId __x_ABI_CWindows_CGaming_CInput_CIRawGameController_get_HardwareProductId
#define IRawGameController_get_HardwareVendorId __x_ABI_CWindows_CGaming_CInput_CIRawGameController_get_HardwareVendorId
#define IRawGameController_get_SwitchCount __x_ABI_CWindows_CGaming_CInput_CIRawGameController_get_SwitchCount
#define IRawGameController_GetButtonLabel __x_ABI_CWindows_CGaming_CInput_CIRawGameController_GetButtonLabel
#define IRawGameController_GetCurrentReading __x_ABI_CWindows_CGaming_CInput_CIRawGameController_GetCurrentReading
#define IRawGameController_GetSwitchKind __x_ABI_CWindows_CGaming_CInput_CIRawGameController_GetSwitchKind
#endif /* WIDL_using_Windows_Gaming_Input */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGaming_CInput_CIRawGameController_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000 */

/*****************************************************************************
 * IRawGameController2 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000
#ifndef ____x_ABI_CWindows_CGaming_CInput_CIRawGameController2_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CIRawGameController2_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGaming_CInput_CIRawGameController2, 0x43c0c035, 0xbb73, 0x4756, 0xa7,0x87, 0x3e,0xd6,0xbe,0xa6,0x17,0xbd);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                MIDL_INTERFACE("43c0c035-bb73-4756-a787-3ed6bea617bd")
                IRawGameController2 : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_SimpleHapticsControllers(
                        ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Devices::Haptics::SimpleHapticsController* > **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_NonRoamableId(
                        HSTRING *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_DisplayName(
                        HSTRING *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGaming_CInput_CIRawGameController2, 0x43c0c035, 0xbb73, 0x4756, 0xa7,0x87, 0x3e,0xd6,0xbe,0xa6,0x17,0xbd)
#endif
#else
typedef struct __x_ABI_CWindows_CGaming_CInput_CIRawGameController2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGaming_CInput_CIRawGameController2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGaming_CInput_CIRawGameController2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGaming_CInput_CIRawGameController2 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGaming_CInput_CIRawGameController2 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGaming_CInput_CIRawGameController2 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGaming_CInput_CIRawGameController2 *This,
        TrustLevel *trustLevel);

    /*** IRawGameController2 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_SimpleHapticsControllers)(
        __x_ABI_CWindows_CGaming_CInput_CIRawGameController2 *This,
        __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController **value);

    HRESULT (STDMETHODCALLTYPE *get_NonRoamableId)(
        __x_ABI_CWindows_CGaming_CInput_CIRawGameController2 *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_DisplayName)(
        __x_ABI_CWindows_CGaming_CInput_CIRawGameController2 *This,
        HSTRING *value);

    END_INTERFACE
} __x_ABI_CWindows_CGaming_CInput_CIRawGameController2Vtbl;

interface __x_ABI_CWindows_CGaming_CInput_CIRawGameController2 {
    CONST_VTBL __x_ABI_CWindows_CGaming_CInput_CIRawGameController2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CIRawGameController2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGaming_CInput_CIRawGameController2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGaming_CInput_CIRawGameController2_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CIRawGameController2_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGaming_CInput_CIRawGameController2_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGaming_CInput_CIRawGameController2_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IRawGameController2 methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CIRawGameController2_get_SimpleHapticsControllers(This,value) (This)->lpVtbl->get_SimpleHapticsControllers(This,value)
#define __x_ABI_CWindows_CGaming_CInput_CIRawGameController2_get_NonRoamableId(This,value) (This)->lpVtbl->get_NonRoamableId(This,value)
#define __x_ABI_CWindows_CGaming_CInput_CIRawGameController2_get_DisplayName(This,value) (This)->lpVtbl->get_DisplayName(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIRawGameController2_QueryInterface(__x_ABI_CWindows_CGaming_CInput_CIRawGameController2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CIRawGameController2_AddRef(__x_ABI_CWindows_CGaming_CInput_CIRawGameController2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CIRawGameController2_Release(__x_ABI_CWindows_CGaming_CInput_CIRawGameController2* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIRawGameController2_GetIids(__x_ABI_CWindows_CGaming_CInput_CIRawGameController2* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIRawGameController2_GetRuntimeClassName(__x_ABI_CWindows_CGaming_CInput_CIRawGameController2* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIRawGameController2_GetTrustLevel(__x_ABI_CWindows_CGaming_CInput_CIRawGameController2* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IRawGameController2 methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIRawGameController2_get_SimpleHapticsControllers(__x_ABI_CWindows_CGaming_CInput_CIRawGameController2* This,__FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController **value) {
    return This->lpVtbl->get_SimpleHapticsControllers(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIRawGameController2_get_NonRoamableId(__x_ABI_CWindows_CGaming_CInput_CIRawGameController2* This,HSTRING *value) {
    return This->lpVtbl->get_NonRoamableId(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIRawGameController2_get_DisplayName(__x_ABI_CWindows_CGaming_CInput_CIRawGameController2* This,HSTRING *value) {
    return This->lpVtbl->get_DisplayName(This,value);
}
#endif
#ifdef WIDL_using_Windows_Gaming_Input
#define IID_IRawGameController2 IID___x_ABI_CWindows_CGaming_CInput_CIRawGameController2
#define IRawGameController2Vtbl __x_ABI_CWindows_CGaming_CInput_CIRawGameController2Vtbl
#define IRawGameController2 __x_ABI_CWindows_CGaming_CInput_CIRawGameController2
#define IRawGameController2_QueryInterface __x_ABI_CWindows_CGaming_CInput_CIRawGameController2_QueryInterface
#define IRawGameController2_AddRef __x_ABI_CWindows_CGaming_CInput_CIRawGameController2_AddRef
#define IRawGameController2_Release __x_ABI_CWindows_CGaming_CInput_CIRawGameController2_Release
#define IRawGameController2_GetIids __x_ABI_CWindows_CGaming_CInput_CIRawGameController2_GetIids
#define IRawGameController2_GetRuntimeClassName __x_ABI_CWindows_CGaming_CInput_CIRawGameController2_GetRuntimeClassName
#define IRawGameController2_GetTrustLevel __x_ABI_CWindows_CGaming_CInput_CIRawGameController2_GetTrustLevel
#define IRawGameController2_get_SimpleHapticsControllers __x_ABI_CWindows_CGaming_CInput_CIRawGameController2_get_SimpleHapticsControllers
#define IRawGameController2_get_NonRoamableId __x_ABI_CWindows_CGaming_CInput_CIRawGameController2_get_NonRoamableId
#define IRawGameController2_get_DisplayName __x_ABI_CWindows_CGaming_CInput_CIRawGameController2_get_DisplayName
#endif /* WIDL_using_Windows_Gaming_Input */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGaming_CInput_CIRawGameController2_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000 */

/*****************************************************************************
 * IGamepadStatics interface
 */
#ifndef ____x_ABI_CWindows_CGaming_CInput_CIGamepadStatics_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CIGamepadStatics_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGaming_CInput_CIGamepadStatics, 0x8bbce529, 0xd49c, 0x39e9, 0x95,0x60, 0xe4,0x7d,0xde,0x96,0xb7,0xc8);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                MIDL_INTERFACE("8bbce529-d49c-39e9-9560-e47dde96b7c8")
                IGamepadStatics : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE add_GamepadAdded(
                        ABI::Windows::Foundation::IEventHandler<ABI::Windows::Gaming::Input::Gamepad* > *value,
                        EventRegistrationToken *token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_GamepadAdded(
                        EventRegistrationToken token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_GamepadRemoved(
                        ABI::Windows::Foundation::IEventHandler<ABI::Windows::Gaming::Input::Gamepad* > *value,
                        EventRegistrationToken *token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_GamepadRemoved(
                        EventRegistrationToken token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_Gamepads(
                        ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Gaming::Input::Gamepad* > **value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGaming_CInput_CIGamepadStatics, 0x8bbce529, 0xd49c, 0x39e9, 0x95,0x60, 0xe4,0x7d,0xde,0x96,0xb7,0xc8)
#endif
#else
typedef struct __x_ABI_CWindows_CGaming_CInput_CIGamepadStaticsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics *This,
        TrustLevel *trustLevel);

    /*** IGamepadStatics methods ***/
    HRESULT (STDMETHODCALLTYPE *add_GamepadAdded)(
        __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics *This,
        __FIEventHandler_1_Windows__CGaming__CInput__CGamepad *value,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_GamepadAdded)(
        __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics *This,
        EventRegistrationToken token);

    HRESULT (STDMETHODCALLTYPE *add_GamepadRemoved)(
        __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics *This,
        __FIEventHandler_1_Windows__CGaming__CInput__CGamepad *value,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_GamepadRemoved)(
        __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics *This,
        EventRegistrationToken token);

    HRESULT (STDMETHODCALLTYPE *get_Gamepads)(
        __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics *This,
        __FIVectorView_1_Windows__CGaming__CInput__CGamepad **value);

    END_INTERFACE
} __x_ABI_CWindows_CGaming_CInput_CIGamepadStaticsVtbl;

interface __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics {
    CONST_VTBL __x_ABI_CWindows_CGaming_CInput_CIGamepadStaticsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IGamepadStatics methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics_add_GamepadAdded(This,value,token) (This)->lpVtbl->add_GamepadAdded(This,value,token)
#define __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics_remove_GamepadAdded(This,token) (This)->lpVtbl->remove_GamepadAdded(This,token)
#define __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics_add_GamepadRemoved(This,value,token) (This)->lpVtbl->add_GamepadRemoved(This,value,token)
#define __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics_remove_GamepadRemoved(This,token) (This)->lpVtbl->remove_GamepadRemoved(This,token)
#define __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics_get_Gamepads(This,value) (This)->lpVtbl->get_Gamepads(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics_QueryInterface(__x_ABI_CWindows_CGaming_CInput_CIGamepadStatics* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics_AddRef(__x_ABI_CWindows_CGaming_CInput_CIGamepadStatics* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics_Release(__x_ABI_CWindows_CGaming_CInput_CIGamepadStatics* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics_GetIids(__x_ABI_CWindows_CGaming_CInput_CIGamepadStatics* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics_GetRuntimeClassName(__x_ABI_CWindows_CGaming_CInput_CIGamepadStatics* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics_GetTrustLevel(__x_ABI_CWindows_CGaming_CInput_CIGamepadStatics* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IGamepadStatics methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics_add_GamepadAdded(__x_ABI_CWindows_CGaming_CInput_CIGamepadStatics* This,__FIEventHandler_1_Windows__CGaming__CInput__CGamepad *value,EventRegistrationToken *token) {
    return This->lpVtbl->add_GamepadAdded(This,value,token);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics_remove_GamepadAdded(__x_ABI_CWindows_CGaming_CInput_CIGamepadStatics* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_GamepadAdded(This,token);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics_add_GamepadRemoved(__x_ABI_CWindows_CGaming_CInput_CIGamepadStatics* This,__FIEventHandler_1_Windows__CGaming__CInput__CGamepad *value,EventRegistrationToken *token) {
    return This->lpVtbl->add_GamepadRemoved(This,value,token);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics_remove_GamepadRemoved(__x_ABI_CWindows_CGaming_CInput_CIGamepadStatics* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_GamepadRemoved(This,token);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics_get_Gamepads(__x_ABI_CWindows_CGaming_CInput_CIGamepadStatics* This,__FIVectorView_1_Windows__CGaming__CInput__CGamepad **value) {
    return This->lpVtbl->get_Gamepads(This,value);
}
#endif
#ifdef WIDL_using_Windows_Gaming_Input
#define IID_IGamepadStatics IID___x_ABI_CWindows_CGaming_CInput_CIGamepadStatics
#define IGamepadStaticsVtbl __x_ABI_CWindows_CGaming_CInput_CIGamepadStaticsVtbl
#define IGamepadStatics __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics
#define IGamepadStatics_QueryInterface __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics_QueryInterface
#define IGamepadStatics_AddRef __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics_AddRef
#define IGamepadStatics_Release __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics_Release
#define IGamepadStatics_GetIids __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics_GetIids
#define IGamepadStatics_GetRuntimeClassName __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics_GetRuntimeClassName
#define IGamepadStatics_GetTrustLevel __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics_GetTrustLevel
#define IGamepadStatics_add_GamepadAdded __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics_add_GamepadAdded
#define IGamepadStatics_remove_GamepadAdded __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics_remove_GamepadAdded
#define IGamepadStatics_add_GamepadRemoved __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics_add_GamepadRemoved
#define IGamepadStatics_remove_GamepadRemoved __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics_remove_GamepadRemoved
#define IGamepadStatics_get_Gamepads __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics_get_Gamepads
#endif /* WIDL_using_Windows_Gaming_Input */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGaming_CInput_CIGamepadStatics_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IGamepadStatics2 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000
#ifndef ____x_ABI_CWindows_CGaming_CInput_CIGamepadStatics2_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CIGamepadStatics2_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGaming_CInput_CIGamepadStatics2, 0x42676dc5, 0x0856, 0x47c4, 0x92,0x13, 0xb3,0x95,0x50,0x4c,0x3a,0x3c);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                MIDL_INTERFACE("42676dc5-0856-47c4-9213-b395504c3a3c")
                IGamepadStatics2 : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE FromGameController(
                        ABI::Windows::Gaming::Input::IGameController *controller,
                        ABI::Windows::Gaming::Input::IGamepad **value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGaming_CInput_CIGamepadStatics2, 0x42676dc5, 0x0856, 0x47c4, 0x92,0x13, 0xb3,0x95,0x50,0x4c,0x3a,0x3c)
#endif
#else
typedef struct __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics2 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics2 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics2 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics2 *This,
        TrustLevel *trustLevel);

    /*** IGamepadStatics2 methods ***/
    HRESULT (STDMETHODCALLTYPE *FromGameController)(
        __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics2 *This,
        __x_ABI_CWindows_CGaming_CInput_CIGameController *controller,
        __x_ABI_CWindows_CGaming_CInput_CIGamepad **value);

    END_INTERFACE
} __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics2Vtbl;

interface __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics2 {
    CONST_VTBL __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics2_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics2_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics2_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics2_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IGamepadStatics2 methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics2_FromGameController(This,controller,value) (This)->lpVtbl->FromGameController(This,controller,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics2_QueryInterface(__x_ABI_CWindows_CGaming_CInput_CIGamepadStatics2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics2_AddRef(__x_ABI_CWindows_CGaming_CInput_CIGamepadStatics2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics2_Release(__x_ABI_CWindows_CGaming_CInput_CIGamepadStatics2* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics2_GetIids(__x_ABI_CWindows_CGaming_CInput_CIGamepadStatics2* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics2_GetRuntimeClassName(__x_ABI_CWindows_CGaming_CInput_CIGamepadStatics2* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics2_GetTrustLevel(__x_ABI_CWindows_CGaming_CInput_CIGamepadStatics2* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IGamepadStatics2 methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics2_FromGameController(__x_ABI_CWindows_CGaming_CInput_CIGamepadStatics2* This,__x_ABI_CWindows_CGaming_CInput_CIGameController *controller,__x_ABI_CWindows_CGaming_CInput_CIGamepad **value) {
    return This->lpVtbl->FromGameController(This,controller,value);
}
#endif
#ifdef WIDL_using_Windows_Gaming_Input
#define IID_IGamepadStatics2 IID___x_ABI_CWindows_CGaming_CInput_CIGamepadStatics2
#define IGamepadStatics2Vtbl __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics2Vtbl
#define IGamepadStatics2 __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics2
#define IGamepadStatics2_QueryInterface __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics2_QueryInterface
#define IGamepadStatics2_AddRef __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics2_AddRef
#define IGamepadStatics2_Release __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics2_Release
#define IGamepadStatics2_GetIids __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics2_GetIids
#define IGamepadStatics2_GetRuntimeClassName __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics2_GetRuntimeClassName
#define IGamepadStatics2_GetTrustLevel __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics2_GetTrustLevel
#define IGamepadStatics2_FromGameController __x_ABI_CWindows_CGaming_CInput_CIGamepadStatics2_FromGameController
#endif /* WIDL_using_Windows_Gaming_Input */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGaming_CInput_CIGamepadStatics2_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000 */

/*****************************************************************************
 * IHeadset interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CGaming_CInput_CIHeadset_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CIHeadset_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGaming_CInput_CIHeadset, 0x3fd156ef, 0x6925, 0x3fa8, 0x91,0x81, 0x02,0x9c,0x52,0x23,0xae,0x3b);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                MIDL_INTERFACE("3fd156ef-6925-3fa8-9181-029c5223ae3b")
                IHeadset : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_CaptureDeviceId(
                        HSTRING *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_RenderDeviceId(
                        HSTRING *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGaming_CInput_CIHeadset, 0x3fd156ef, 0x6925, 0x3fa8, 0x91,0x81, 0x02,0x9c,0x52,0x23,0xae,0x3b)
#endif
#else
typedef struct __x_ABI_CWindows_CGaming_CInput_CIHeadsetVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGaming_CInput_CIHeadset *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGaming_CInput_CIHeadset *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGaming_CInput_CIHeadset *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGaming_CInput_CIHeadset *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGaming_CInput_CIHeadset *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGaming_CInput_CIHeadset *This,
        TrustLevel *trustLevel);

    /*** IHeadset methods ***/
    HRESULT (STDMETHODCALLTYPE *get_CaptureDeviceId)(
        __x_ABI_CWindows_CGaming_CInput_CIHeadset *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_RenderDeviceId)(
        __x_ABI_CWindows_CGaming_CInput_CIHeadset *This,
        HSTRING *value);

    END_INTERFACE
} __x_ABI_CWindows_CGaming_CInput_CIHeadsetVtbl;

interface __x_ABI_CWindows_CGaming_CInput_CIHeadset {
    CONST_VTBL __x_ABI_CWindows_CGaming_CInput_CIHeadsetVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CIHeadset_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGaming_CInput_CIHeadset_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGaming_CInput_CIHeadset_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CIHeadset_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGaming_CInput_CIHeadset_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGaming_CInput_CIHeadset_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IHeadset methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CIHeadset_get_CaptureDeviceId(This,value) (This)->lpVtbl->get_CaptureDeviceId(This,value)
#define __x_ABI_CWindows_CGaming_CInput_CIHeadset_get_RenderDeviceId(This,value) (This)->lpVtbl->get_RenderDeviceId(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIHeadset_QueryInterface(__x_ABI_CWindows_CGaming_CInput_CIHeadset* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CIHeadset_AddRef(__x_ABI_CWindows_CGaming_CInput_CIHeadset* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CIHeadset_Release(__x_ABI_CWindows_CGaming_CInput_CIHeadset* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIHeadset_GetIids(__x_ABI_CWindows_CGaming_CInput_CIHeadset* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIHeadset_GetRuntimeClassName(__x_ABI_CWindows_CGaming_CInput_CIHeadset* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIHeadset_GetTrustLevel(__x_ABI_CWindows_CGaming_CInput_CIHeadset* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IHeadset methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIHeadset_get_CaptureDeviceId(__x_ABI_CWindows_CGaming_CInput_CIHeadset* This,HSTRING *value) {
    return This->lpVtbl->get_CaptureDeviceId(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIHeadset_get_RenderDeviceId(__x_ABI_CWindows_CGaming_CInput_CIHeadset* This,HSTRING *value) {
    return This->lpVtbl->get_RenderDeviceId(This,value);
}
#endif
#ifdef WIDL_using_Windows_Gaming_Input
#define IID_IHeadset IID___x_ABI_CWindows_CGaming_CInput_CIHeadset
#define IHeadsetVtbl __x_ABI_CWindows_CGaming_CInput_CIHeadsetVtbl
#define IHeadset __x_ABI_CWindows_CGaming_CInput_CIHeadset
#define IHeadset_QueryInterface __x_ABI_CWindows_CGaming_CInput_CIHeadset_QueryInterface
#define IHeadset_AddRef __x_ABI_CWindows_CGaming_CInput_CIHeadset_AddRef
#define IHeadset_Release __x_ABI_CWindows_CGaming_CInput_CIHeadset_Release
#define IHeadset_GetIids __x_ABI_CWindows_CGaming_CInput_CIHeadset_GetIids
#define IHeadset_GetRuntimeClassName __x_ABI_CWindows_CGaming_CInput_CIHeadset_GetRuntimeClassName
#define IHeadset_GetTrustLevel __x_ABI_CWindows_CGaming_CInput_CIHeadset_GetTrustLevel
#define IHeadset_get_CaptureDeviceId __x_ABI_CWindows_CGaming_CInput_CIHeadset_get_CaptureDeviceId
#define IHeadset_get_RenderDeviceId __x_ABI_CWindows_CGaming_CInput_CIHeadset_get_RenderDeviceId
#endif /* WIDL_using_Windows_Gaming_Input */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGaming_CInput_CIHeadset_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IRawGameControllerStatics interface
 */
#ifndef ____x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics, 0xeb8d0792, 0xe95a, 0x4b19, 0xaf,0xc7, 0x0a,0x59,0xf8,0xbf,0x75,0x9e);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                MIDL_INTERFACE("eb8d0792-e95a-4b19-afc7-0a59f8bf759e")
                IRawGameControllerStatics : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE add_RawGameControllerAdded(
                        ABI::Windows::Foundation::IEventHandler<ABI::Windows::Gaming::Input::RawGameController* > *handler,
                        EventRegistrationToken *token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_RawGameControllerAdded(
                        EventRegistrationToken token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_RawGameControllerRemoved(
                        ABI::Windows::Foundation::IEventHandler<ABI::Windows::Gaming::Input::RawGameController* > *handler,
                        EventRegistrationToken *token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_RawGameControllerRemoved(
                        EventRegistrationToken token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_RawGameControllers(
                        ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Gaming::Input::RawGameController* > **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE FromGameController(
                        ABI::Windows::Gaming::Input::IGameController *game_controller,
                        ABI::Windows::Gaming::Input::IRawGameController **value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics, 0xeb8d0792, 0xe95a, 0x4b19, 0xaf,0xc7, 0x0a,0x59,0xf8,0xbf,0x75,0x9e)
#endif
#else
typedef struct __x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStaticsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics *This,
        TrustLevel *trustLevel);

    /*** IRawGameControllerStatics methods ***/
    HRESULT (STDMETHODCALLTYPE *add_RawGameControllerAdded)(
        __x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics *This,
        __FIEventHandler_1_Windows__CGaming__CInput__CRawGameController *handler,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_RawGameControllerAdded)(
        __x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics *This,
        EventRegistrationToken token);

    HRESULT (STDMETHODCALLTYPE *add_RawGameControllerRemoved)(
        __x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics *This,
        __FIEventHandler_1_Windows__CGaming__CInput__CRawGameController *handler,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_RawGameControllerRemoved)(
        __x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics *This,
        EventRegistrationToken token);

    HRESULT (STDMETHODCALLTYPE *get_RawGameControllers)(
        __x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics *This,
        __FIVectorView_1_Windows__CGaming__CInput__CRawGameController **value);

    HRESULT (STDMETHODCALLTYPE *FromGameController)(
        __x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics *This,
        __x_ABI_CWindows_CGaming_CInput_CIGameController *game_controller,
        __x_ABI_CWindows_CGaming_CInput_CIRawGameController **value);

    END_INTERFACE
} __x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStaticsVtbl;

interface __x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics {
    CONST_VTBL __x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStaticsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IRawGameControllerStatics methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics_add_RawGameControllerAdded(This,handler,token) (This)->lpVtbl->add_RawGameControllerAdded(This,handler,token)
#define __x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics_remove_RawGameControllerAdded(This,token) (This)->lpVtbl->remove_RawGameControllerAdded(This,token)
#define __x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics_add_RawGameControllerRemoved(This,handler,token) (This)->lpVtbl->add_RawGameControllerRemoved(This,handler,token)
#define __x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics_remove_RawGameControllerRemoved(This,token) (This)->lpVtbl->remove_RawGameControllerRemoved(This,token)
#define __x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics_get_RawGameControllers(This,value) (This)->lpVtbl->get_RawGameControllers(This,value)
#define __x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics_FromGameController(This,game_controller,value) (This)->lpVtbl->FromGameController(This,game_controller,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics_QueryInterface(__x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics_AddRef(__x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics_Release(__x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics_GetIids(__x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics_GetRuntimeClassName(__x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics_GetTrustLevel(__x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IRawGameControllerStatics methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics_add_RawGameControllerAdded(__x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics* This,__FIEventHandler_1_Windows__CGaming__CInput__CRawGameController *handler,EventRegistrationToken *token) {
    return This->lpVtbl->add_RawGameControllerAdded(This,handler,token);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics_remove_RawGameControllerAdded(__x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_RawGameControllerAdded(This,token);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics_add_RawGameControllerRemoved(__x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics* This,__FIEventHandler_1_Windows__CGaming__CInput__CRawGameController *handler,EventRegistrationToken *token) {
    return This->lpVtbl->add_RawGameControllerRemoved(This,handler,token);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics_remove_RawGameControllerRemoved(__x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_RawGameControllerRemoved(This,token);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics_get_RawGameControllers(__x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics* This,__FIVectorView_1_Windows__CGaming__CInput__CRawGameController **value) {
    return This->lpVtbl->get_RawGameControllers(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics_FromGameController(__x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics* This,__x_ABI_CWindows_CGaming_CInput_CIGameController *game_controller,__x_ABI_CWindows_CGaming_CInput_CIRawGameController **value) {
    return This->lpVtbl->FromGameController(This,game_controller,value);
}
#endif
#ifdef WIDL_using_Windows_Gaming_Input
#define IID_IRawGameControllerStatics IID___x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics
#define IRawGameControllerStaticsVtbl __x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStaticsVtbl
#define IRawGameControllerStatics __x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics
#define IRawGameControllerStatics_QueryInterface __x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics_QueryInterface
#define IRawGameControllerStatics_AddRef __x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics_AddRef
#define IRawGameControllerStatics_Release __x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics_Release
#define IRawGameControllerStatics_GetIids __x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics_GetIids
#define IRawGameControllerStatics_GetRuntimeClassName __x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics_GetRuntimeClassName
#define IRawGameControllerStatics_GetTrustLevel __x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics_GetTrustLevel
#define IRawGameControllerStatics_add_RawGameControllerAdded __x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics_add_RawGameControllerAdded
#define IRawGameControllerStatics_remove_RawGameControllerAdded __x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics_remove_RawGameControllerAdded
#define IRawGameControllerStatics_add_RawGameControllerRemoved __x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics_add_RawGameControllerRemoved
#define IRawGameControllerStatics_remove_RawGameControllerRemoved __x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics_remove_RawGameControllerRemoved
#define IRawGameControllerStatics_get_RawGameControllers __x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics_get_RawGameControllers
#define IRawGameControllerStatics_FromGameController __x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics_FromGameController
#endif /* WIDL_using_Windows_Gaming_Input */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGaming_CInput_CIRawGameControllerStatics_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IGameControllerBatteryInfo interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000
#ifndef ____x_ABI_CWindows_CGaming_CInput_CIGameControllerBatteryInfo_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CIGameControllerBatteryInfo_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGaming_CInput_CIGameControllerBatteryInfo, 0xdcecc681, 0x3963, 0x4da6, 0x95,0x5d, 0x55,0x3f,0x3b,0x6f,0x61,0x61);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                MIDL_INTERFACE("dcecc681-3963-4da6-955d-553f3b6f6161")
                IGameControllerBatteryInfo : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE TryGetBatteryReport(
                        ABI::Windows::Devices::Power::IBatteryReport **value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGaming_CInput_CIGameControllerBatteryInfo, 0xdcecc681, 0x3963, 0x4da6, 0x95,0x5d, 0x55,0x3f,0x3b,0x6f,0x61,0x61)
#endif
#else
typedef struct __x_ABI_CWindows_CGaming_CInput_CIGameControllerBatteryInfoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGaming_CInput_CIGameControllerBatteryInfo *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGaming_CInput_CIGameControllerBatteryInfo *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGaming_CInput_CIGameControllerBatteryInfo *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGaming_CInput_CIGameControllerBatteryInfo *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGaming_CInput_CIGameControllerBatteryInfo *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGaming_CInput_CIGameControllerBatteryInfo *This,
        TrustLevel *trustLevel);

    /*** IGameControllerBatteryInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *TryGetBatteryReport)(
        __x_ABI_CWindows_CGaming_CInput_CIGameControllerBatteryInfo *This,
        __x_ABI_CWindows_CDevices_CPower_CIBatteryReport **value);

    END_INTERFACE
} __x_ABI_CWindows_CGaming_CInput_CIGameControllerBatteryInfoVtbl;

interface __x_ABI_CWindows_CGaming_CInput_CIGameControllerBatteryInfo {
    CONST_VTBL __x_ABI_CWindows_CGaming_CInput_CIGameControllerBatteryInfoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CIGameControllerBatteryInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGaming_CInput_CIGameControllerBatteryInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGaming_CInput_CIGameControllerBatteryInfo_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CIGameControllerBatteryInfo_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGaming_CInput_CIGameControllerBatteryInfo_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGaming_CInput_CIGameControllerBatteryInfo_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IGameControllerBatteryInfo methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CIGameControllerBatteryInfo_TryGetBatteryReport(This,value) (This)->lpVtbl->TryGetBatteryReport(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIGameControllerBatteryInfo_QueryInterface(__x_ABI_CWindows_CGaming_CInput_CIGameControllerBatteryInfo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CIGameControllerBatteryInfo_AddRef(__x_ABI_CWindows_CGaming_CInput_CIGameControllerBatteryInfo* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CIGameControllerBatteryInfo_Release(__x_ABI_CWindows_CGaming_CInput_CIGameControllerBatteryInfo* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIGameControllerBatteryInfo_GetIids(__x_ABI_CWindows_CGaming_CInput_CIGameControllerBatteryInfo* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIGameControllerBatteryInfo_GetRuntimeClassName(__x_ABI_CWindows_CGaming_CInput_CIGameControllerBatteryInfo* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIGameControllerBatteryInfo_GetTrustLevel(__x_ABI_CWindows_CGaming_CInput_CIGameControllerBatteryInfo* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IGameControllerBatteryInfo methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CIGameControllerBatteryInfo_TryGetBatteryReport(__x_ABI_CWindows_CGaming_CInput_CIGameControllerBatteryInfo* This,__x_ABI_CWindows_CDevices_CPower_CIBatteryReport **value) {
    return This->lpVtbl->TryGetBatteryReport(This,value);
}
#endif
#ifdef WIDL_using_Windows_Gaming_Input
#define IID_IGameControllerBatteryInfo IID___x_ABI_CWindows_CGaming_CInput_CIGameControllerBatteryInfo
#define IGameControllerBatteryInfoVtbl __x_ABI_CWindows_CGaming_CInput_CIGameControllerBatteryInfoVtbl
#define IGameControllerBatteryInfo __x_ABI_CWindows_CGaming_CInput_CIGameControllerBatteryInfo
#define IGameControllerBatteryInfo_QueryInterface __x_ABI_CWindows_CGaming_CInput_CIGameControllerBatteryInfo_QueryInterface
#define IGameControllerBatteryInfo_AddRef __x_ABI_CWindows_CGaming_CInput_CIGameControllerBatteryInfo_AddRef
#define IGameControllerBatteryInfo_Release __x_ABI_CWindows_CGaming_CInput_CIGameControllerBatteryInfo_Release
#define IGameControllerBatteryInfo_GetIids __x_ABI_CWindows_CGaming_CInput_CIGameControllerBatteryInfo_GetIids
#define IGameControllerBatteryInfo_GetRuntimeClassName __x_ABI_CWindows_CGaming_CInput_CIGameControllerBatteryInfo_GetRuntimeClassName
#define IGameControllerBatteryInfo_GetTrustLevel __x_ABI_CWindows_CGaming_CInput_CIGameControllerBatteryInfo_GetTrustLevel
#define IGameControllerBatteryInfo_TryGetBatteryReport __x_ABI_CWindows_CGaming_CInput_CIGameControllerBatteryInfo_TryGetBatteryReport
#endif /* WIDL_using_Windows_Gaming_Input */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGaming_CInput_CIGameControllerBatteryInfo_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000 */

/*
 * Class Windows.Gaming.Input.ArcadeStick
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
#ifndef RUNTIMECLASS_Windows_Gaming_Input_ArcadeStick_DEFINED
#define RUNTIMECLASS_Windows_Gaming_Input_ArcadeStick_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Gaming_Input_ArcadeStick[] = {'W','i','n','d','o','w','s','.','G','a','m','i','n','g','.','I','n','p','u','t','.','A','r','c','a','d','e','S','t','i','c','k',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Gaming_Input_ArcadeStick[] = L"Windows.Gaming.Input.ArcadeStick";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Gaming_Input_ArcadeStick[] = {'W','i','n','d','o','w','s','.','G','a','m','i','n','g','.','I','n','p','u','t','.','A','r','c','a','d','e','S','t','i','c','k',0};
#endif
#endif /* RUNTIMECLASS_Windows_Gaming_Input_ArcadeStick_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */

/*
 * Class Windows.Gaming.Input.FlightStick
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000
#ifndef RUNTIMECLASS_Windows_Gaming_Input_FlightStick_DEFINED
#define RUNTIMECLASS_Windows_Gaming_Input_FlightStick_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Gaming_Input_FlightStick[] = {'W','i','n','d','o','w','s','.','G','a','m','i','n','g','.','I','n','p','u','t','.','F','l','i','g','h','t','S','t','i','c','k',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Gaming_Input_FlightStick[] = L"Windows.Gaming.Input.FlightStick";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Gaming_Input_FlightStick[] = {'W','i','n','d','o','w','s','.','G','a','m','i','n','g','.','I','n','p','u','t','.','F','l','i','g','h','t','S','t','i','c','k',0};
#endif
#endif /* RUNTIMECLASS_Windows_Gaming_Input_FlightStick_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000 */

/*
 * Class Windows.Gaming.Input.Gamepad
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Gaming_Input_Gamepad_DEFINED
#define RUNTIMECLASS_Windows_Gaming_Input_Gamepad_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Gaming_Input_Gamepad[] = {'W','i','n','d','o','w','s','.','G','a','m','i','n','g','.','I','n','p','u','t','.','G','a','m','e','p','a','d',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Gaming_Input_Gamepad[] = L"Windows.Gaming.Input.Gamepad";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Gaming_Input_Gamepad[] = {'W','i','n','d','o','w','s','.','G','a','m','i','n','g','.','I','n','p','u','t','.','G','a','m','e','p','a','d',0};
#endif
#endif /* RUNTIMECLASS_Windows_Gaming_Input_Gamepad_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Gaming.Input.Headset
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Gaming_Input_Headset_DEFINED
#define RUNTIMECLASS_Windows_Gaming_Input_Headset_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Gaming_Input_Headset[] = {'W','i','n','d','o','w','s','.','G','a','m','i','n','g','.','I','n','p','u','t','.','H','e','a','d','s','e','t',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Gaming_Input_Headset[] = L"Windows.Gaming.Input.Headset";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Gaming_Input_Headset[] = {'W','i','n','d','o','w','s','.','G','a','m','i','n','g','.','I','n','p','u','t','.','H','e','a','d','s','e','t',0};
#endif
#endif /* RUNTIMECLASS_Windows_Gaming_Input_Headset_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Gaming.Input.RacingWheel
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
#ifndef RUNTIMECLASS_Windows_Gaming_Input_RacingWheel_DEFINED
#define RUNTIMECLASS_Windows_Gaming_Input_RacingWheel_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Gaming_Input_RacingWheel[] = {'W','i','n','d','o','w','s','.','G','a','m','i','n','g','.','I','n','p','u','t','.','R','a','c','i','n','g','W','h','e','e','l',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Gaming_Input_RacingWheel[] = L"Windows.Gaming.Input.RacingWheel";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Gaming_Input_RacingWheel[] = {'W','i','n','d','o','w','s','.','G','a','m','i','n','g','.','I','n','p','u','t','.','R','a','c','i','n','g','W','h','e','e','l',0};
#endif
#endif /* RUNTIMECLASS_Windows_Gaming_Input_RacingWheel_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */

/*
 * Class Windows.Gaming.Input.RawGameController
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000
#ifndef RUNTIMECLASS_Windows_Gaming_Input_RawGameController_DEFINED
#define RUNTIMECLASS_Windows_Gaming_Input_RawGameController_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Gaming_Input_RawGameController[] = {'W','i','n','d','o','w','s','.','G','a','m','i','n','g','.','I','n','p','u','t','.','R','a','w','G','a','m','e','C','o','n','t','r','o','l','l','e','r',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Gaming_Input_RawGameController[] = L"Windows.Gaming.Input.RawGameController";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Gaming_Input_RawGameController[] = {'W','i','n','d','o','w','s','.','G','a','m','i','n','g','.','I','n','p','u','t','.','R','a','w','G','a','m','e','C','o','n','t','r','o','l','l','e','r',0};
#endif
#endif /* RUNTIMECLASS_Windows_Gaming_Input_RawGameController_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000 */

/*****************************************************************************
 * IEventHandler<ABI::Windows::Gaming::Input::ArcadeStick* > interface
 */
#ifndef ____FIEventHandler_1_Windows__CGaming__CInput__CArcadeStick_INTERFACE_DEFINED__
#define ____FIEventHandler_1_Windows__CGaming__CInput__CArcadeStick_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIEventHandler_1_Windows__CGaming__CInput__CArcadeStick, 0x6afb8188, 0xd28d, 0x539b, 0xbb,0x69, 0xea,0x17,0x63,0xfb,0x99,0x20);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("6afb8188-d28d-539b-bb69-ea1763fb9920")
            IEventHandler<ABI::Windows::Gaming::Input::ArcadeStick* > : IEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Gaming::Input::ArcadeStick*, ABI::Windows::Gaming::Input::IArcadeStick* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIEventHandler_1_Windows__CGaming__CInput__CArcadeStick, 0x6afb8188, 0xd28d, 0x539b, 0xbb,0x69, 0xea,0x17,0x63,0xfb,0x99,0x20)
#endif
#else
typedef struct __FIEventHandler_1_Windows__CGaming__CInput__CArcadeStickVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIEventHandler_1_Windows__CGaming__CInput__CArcadeStick *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIEventHandler_1_Windows__CGaming__CInput__CArcadeStick *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIEventHandler_1_Windows__CGaming__CInput__CArcadeStick *This);

    /*** IEventHandler<ABI::Windows::Gaming::Input::ArcadeStick* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIEventHandler_1_Windows__CGaming__CInput__CArcadeStick *This,
        IInspectable *sender,
        __x_ABI_CWindows_CGaming_CInput_CIArcadeStick *args);

    END_INTERFACE
} __FIEventHandler_1_Windows__CGaming__CInput__CArcadeStickVtbl;

interface __FIEventHandler_1_Windows__CGaming__CInput__CArcadeStick {
    CONST_VTBL __FIEventHandler_1_Windows__CGaming__CInput__CArcadeStickVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIEventHandler_1_Windows__CGaming__CInput__CArcadeStick_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIEventHandler_1_Windows__CGaming__CInput__CArcadeStick_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIEventHandler_1_Windows__CGaming__CInput__CArcadeStick_Release(This) (This)->lpVtbl->Release(This)
/*** IEventHandler<ABI::Windows::Gaming::Input::ArcadeStick* > methods ***/
#define __FIEventHandler_1_Windows__CGaming__CInput__CArcadeStick_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIEventHandler_1_Windows__CGaming__CInput__CArcadeStick_QueryInterface(__FIEventHandler_1_Windows__CGaming__CInput__CArcadeStick* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIEventHandler_1_Windows__CGaming__CInput__CArcadeStick_AddRef(__FIEventHandler_1_Windows__CGaming__CInput__CArcadeStick* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIEventHandler_1_Windows__CGaming__CInput__CArcadeStick_Release(__FIEventHandler_1_Windows__CGaming__CInput__CArcadeStick* This) {
    return This->lpVtbl->Release(This);
}
/*** IEventHandler<ABI::Windows::Gaming::Input::ArcadeStick* > methods ***/
static inline HRESULT __FIEventHandler_1_Windows__CGaming__CInput__CArcadeStick_Invoke(__FIEventHandler_1_Windows__CGaming__CInput__CArcadeStick* This,IInspectable *sender,__x_ABI_CWindows_CGaming_CInput_CIArcadeStick *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IEventHandler_ArcadeStick IID___FIEventHandler_1_Windows__CGaming__CInput__CArcadeStick
#define IEventHandler_ArcadeStickVtbl __FIEventHandler_1_Windows__CGaming__CInput__CArcadeStickVtbl
#define IEventHandler_ArcadeStick __FIEventHandler_1_Windows__CGaming__CInput__CArcadeStick
#define IEventHandler_ArcadeStick_QueryInterface __FIEventHandler_1_Windows__CGaming__CInput__CArcadeStick_QueryInterface
#define IEventHandler_ArcadeStick_AddRef __FIEventHandler_1_Windows__CGaming__CInput__CArcadeStick_AddRef
#define IEventHandler_ArcadeStick_Release __FIEventHandler_1_Windows__CGaming__CInput__CArcadeStick_Release
#define IEventHandler_ArcadeStick_Invoke __FIEventHandler_1_Windows__CGaming__CInput__CArcadeStick_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIEventHandler_1_Windows__CGaming__CInput__CArcadeStick_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IEventHandler<ABI::Windows::Gaming::Input::FlightStick* > interface
 */
#ifndef ____FIEventHandler_1_Windows__CGaming__CInput__CFlightStick_INTERFACE_DEFINED__
#define ____FIEventHandler_1_Windows__CGaming__CInput__CFlightStick_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIEventHandler_1_Windows__CGaming__CInput__CFlightStick, 0xd57470b1, 0xcc22, 0x5a43, 0x8e,0x18, 0x5c,0xa0,0x64,0xaa,0xfe,0x21);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("d57470b1-cc22-5a43-8e18-5ca064aafe21")
            IEventHandler<ABI::Windows::Gaming::Input::FlightStick* > : IEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Gaming::Input::FlightStick*, ABI::Windows::Gaming::Input::IFlightStick* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIEventHandler_1_Windows__CGaming__CInput__CFlightStick, 0xd57470b1, 0xcc22, 0x5a43, 0x8e,0x18, 0x5c,0xa0,0x64,0xaa,0xfe,0x21)
#endif
#else
typedef struct __FIEventHandler_1_Windows__CGaming__CInput__CFlightStickVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIEventHandler_1_Windows__CGaming__CInput__CFlightStick *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIEventHandler_1_Windows__CGaming__CInput__CFlightStick *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIEventHandler_1_Windows__CGaming__CInput__CFlightStick *This);

    /*** IEventHandler<ABI::Windows::Gaming::Input::FlightStick* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIEventHandler_1_Windows__CGaming__CInput__CFlightStick *This,
        IInspectable *sender,
        __x_ABI_CWindows_CGaming_CInput_CIFlightStick *args);

    END_INTERFACE
} __FIEventHandler_1_Windows__CGaming__CInput__CFlightStickVtbl;

interface __FIEventHandler_1_Windows__CGaming__CInput__CFlightStick {
    CONST_VTBL __FIEventHandler_1_Windows__CGaming__CInput__CFlightStickVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIEventHandler_1_Windows__CGaming__CInput__CFlightStick_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIEventHandler_1_Windows__CGaming__CInput__CFlightStick_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIEventHandler_1_Windows__CGaming__CInput__CFlightStick_Release(This) (This)->lpVtbl->Release(This)
/*** IEventHandler<ABI::Windows::Gaming::Input::FlightStick* > methods ***/
#define __FIEventHandler_1_Windows__CGaming__CInput__CFlightStick_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIEventHandler_1_Windows__CGaming__CInput__CFlightStick_QueryInterface(__FIEventHandler_1_Windows__CGaming__CInput__CFlightStick* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIEventHandler_1_Windows__CGaming__CInput__CFlightStick_AddRef(__FIEventHandler_1_Windows__CGaming__CInput__CFlightStick* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIEventHandler_1_Windows__CGaming__CInput__CFlightStick_Release(__FIEventHandler_1_Windows__CGaming__CInput__CFlightStick* This) {
    return This->lpVtbl->Release(This);
}
/*** IEventHandler<ABI::Windows::Gaming::Input::FlightStick* > methods ***/
static inline HRESULT __FIEventHandler_1_Windows__CGaming__CInput__CFlightStick_Invoke(__FIEventHandler_1_Windows__CGaming__CInput__CFlightStick* This,IInspectable *sender,__x_ABI_CWindows_CGaming_CInput_CIFlightStick *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IEventHandler_FlightStick IID___FIEventHandler_1_Windows__CGaming__CInput__CFlightStick
#define IEventHandler_FlightStickVtbl __FIEventHandler_1_Windows__CGaming__CInput__CFlightStickVtbl
#define IEventHandler_FlightStick __FIEventHandler_1_Windows__CGaming__CInput__CFlightStick
#define IEventHandler_FlightStick_QueryInterface __FIEventHandler_1_Windows__CGaming__CInput__CFlightStick_QueryInterface
#define IEventHandler_FlightStick_AddRef __FIEventHandler_1_Windows__CGaming__CInput__CFlightStick_AddRef
#define IEventHandler_FlightStick_Release __FIEventHandler_1_Windows__CGaming__CInput__CFlightStick_Release
#define IEventHandler_FlightStick_Invoke __FIEventHandler_1_Windows__CGaming__CInput__CFlightStick_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIEventHandler_1_Windows__CGaming__CInput__CFlightStick_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IEventHandler<ABI::Windows::Gaming::Input::Gamepad* > interface
 */
#ifndef ____FIEventHandler_1_Windows__CGaming__CInput__CGamepad_INTERFACE_DEFINED__
#define ____FIEventHandler_1_Windows__CGaming__CInput__CGamepad_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIEventHandler_1_Windows__CGaming__CInput__CGamepad, 0x8a7639ee, 0x624a, 0x501a, 0xbb,0x53, 0x56,0x2d,0x1e,0xc1,0x1b,0x52);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("8a7639ee-624a-501a-bb53-562d1ec11b52")
            IEventHandler<ABI::Windows::Gaming::Input::Gamepad* > : IEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Gaming::Input::Gamepad*, ABI::Windows::Gaming::Input::IGamepad* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIEventHandler_1_Windows__CGaming__CInput__CGamepad, 0x8a7639ee, 0x624a, 0x501a, 0xbb,0x53, 0x56,0x2d,0x1e,0xc1,0x1b,0x52)
#endif
#else
typedef struct __FIEventHandler_1_Windows__CGaming__CInput__CGamepadVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIEventHandler_1_Windows__CGaming__CInput__CGamepad *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIEventHandler_1_Windows__CGaming__CInput__CGamepad *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIEventHandler_1_Windows__CGaming__CInput__CGamepad *This);

    /*** IEventHandler<ABI::Windows::Gaming::Input::Gamepad* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIEventHandler_1_Windows__CGaming__CInput__CGamepad *This,
        IInspectable *sender,
        __x_ABI_CWindows_CGaming_CInput_CIGamepad *args);

    END_INTERFACE
} __FIEventHandler_1_Windows__CGaming__CInput__CGamepadVtbl;

interface __FIEventHandler_1_Windows__CGaming__CInput__CGamepad {
    CONST_VTBL __FIEventHandler_1_Windows__CGaming__CInput__CGamepadVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIEventHandler_1_Windows__CGaming__CInput__CGamepad_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIEventHandler_1_Windows__CGaming__CInput__CGamepad_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIEventHandler_1_Windows__CGaming__CInput__CGamepad_Release(This) (This)->lpVtbl->Release(This)
/*** IEventHandler<ABI::Windows::Gaming::Input::Gamepad* > methods ***/
#define __FIEventHandler_1_Windows__CGaming__CInput__CGamepad_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIEventHandler_1_Windows__CGaming__CInput__CGamepad_QueryInterface(__FIEventHandler_1_Windows__CGaming__CInput__CGamepad* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIEventHandler_1_Windows__CGaming__CInput__CGamepad_AddRef(__FIEventHandler_1_Windows__CGaming__CInput__CGamepad* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIEventHandler_1_Windows__CGaming__CInput__CGamepad_Release(__FIEventHandler_1_Windows__CGaming__CInput__CGamepad* This) {
    return This->lpVtbl->Release(This);
}
/*** IEventHandler<ABI::Windows::Gaming::Input::Gamepad* > methods ***/
static inline HRESULT __FIEventHandler_1_Windows__CGaming__CInput__CGamepad_Invoke(__FIEventHandler_1_Windows__CGaming__CInput__CGamepad* This,IInspectable *sender,__x_ABI_CWindows_CGaming_CInput_CIGamepad *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IEventHandler_Gamepad IID___FIEventHandler_1_Windows__CGaming__CInput__CGamepad
#define IEventHandler_GamepadVtbl __FIEventHandler_1_Windows__CGaming__CInput__CGamepadVtbl
#define IEventHandler_Gamepad __FIEventHandler_1_Windows__CGaming__CInput__CGamepad
#define IEventHandler_Gamepad_QueryInterface __FIEventHandler_1_Windows__CGaming__CInput__CGamepad_QueryInterface
#define IEventHandler_Gamepad_AddRef __FIEventHandler_1_Windows__CGaming__CInput__CGamepad_AddRef
#define IEventHandler_Gamepad_Release __FIEventHandler_1_Windows__CGaming__CInput__CGamepad_Release
#define IEventHandler_Gamepad_Invoke __FIEventHandler_1_Windows__CGaming__CInput__CGamepad_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIEventHandler_1_Windows__CGaming__CInput__CGamepad_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IEventHandler<ABI::Windows::Gaming::Input::RacingWheel* > interface
 */
#ifndef ____FIEventHandler_1_Windows__CGaming__CInput__CRacingWheel_INTERFACE_DEFINED__
#define ____FIEventHandler_1_Windows__CGaming__CInput__CRacingWheel_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIEventHandler_1_Windows__CGaming__CInput__CRacingWheel, 0x352ec824, 0xf64b, 0x5353, 0x80,0xea, 0x7f,0xf5,0x8e,0x3b,0x92,0xa4);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("352ec824-f64b-5353-80ea-7ff58e3b92a4")
            IEventHandler<ABI::Windows::Gaming::Input::RacingWheel* > : IEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Gaming::Input::RacingWheel*, ABI::Windows::Gaming::Input::IRacingWheel* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIEventHandler_1_Windows__CGaming__CInput__CRacingWheel, 0x352ec824, 0xf64b, 0x5353, 0x80,0xea, 0x7f,0xf5,0x8e,0x3b,0x92,0xa4)
#endif
#else
typedef struct __FIEventHandler_1_Windows__CGaming__CInput__CRacingWheelVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIEventHandler_1_Windows__CGaming__CInput__CRacingWheel *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIEventHandler_1_Windows__CGaming__CInput__CRacingWheel *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIEventHandler_1_Windows__CGaming__CInput__CRacingWheel *This);

    /*** IEventHandler<ABI::Windows::Gaming::Input::RacingWheel* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIEventHandler_1_Windows__CGaming__CInput__CRacingWheel *This,
        IInspectable *sender,
        __x_ABI_CWindows_CGaming_CInput_CIRacingWheel *args);

    END_INTERFACE
} __FIEventHandler_1_Windows__CGaming__CInput__CRacingWheelVtbl;

interface __FIEventHandler_1_Windows__CGaming__CInput__CRacingWheel {
    CONST_VTBL __FIEventHandler_1_Windows__CGaming__CInput__CRacingWheelVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIEventHandler_1_Windows__CGaming__CInput__CRacingWheel_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIEventHandler_1_Windows__CGaming__CInput__CRacingWheel_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIEventHandler_1_Windows__CGaming__CInput__CRacingWheel_Release(This) (This)->lpVtbl->Release(This)
/*** IEventHandler<ABI::Windows::Gaming::Input::RacingWheel* > methods ***/
#define __FIEventHandler_1_Windows__CGaming__CInput__CRacingWheel_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIEventHandler_1_Windows__CGaming__CInput__CRacingWheel_QueryInterface(__FIEventHandler_1_Windows__CGaming__CInput__CRacingWheel* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIEventHandler_1_Windows__CGaming__CInput__CRacingWheel_AddRef(__FIEventHandler_1_Windows__CGaming__CInput__CRacingWheel* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIEventHandler_1_Windows__CGaming__CInput__CRacingWheel_Release(__FIEventHandler_1_Windows__CGaming__CInput__CRacingWheel* This) {
    return This->lpVtbl->Release(This);
}
/*** IEventHandler<ABI::Windows::Gaming::Input::RacingWheel* > methods ***/
static inline HRESULT __FIEventHandler_1_Windows__CGaming__CInput__CRacingWheel_Invoke(__FIEventHandler_1_Windows__CGaming__CInput__CRacingWheel* This,IInspectable *sender,__x_ABI_CWindows_CGaming_CInput_CIRacingWheel *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IEventHandler_RacingWheel IID___FIEventHandler_1_Windows__CGaming__CInput__CRacingWheel
#define IEventHandler_RacingWheelVtbl __FIEventHandler_1_Windows__CGaming__CInput__CRacingWheelVtbl
#define IEventHandler_RacingWheel __FIEventHandler_1_Windows__CGaming__CInput__CRacingWheel
#define IEventHandler_RacingWheel_QueryInterface __FIEventHandler_1_Windows__CGaming__CInput__CRacingWheel_QueryInterface
#define IEventHandler_RacingWheel_AddRef __FIEventHandler_1_Windows__CGaming__CInput__CRacingWheel_AddRef
#define IEventHandler_RacingWheel_Release __FIEventHandler_1_Windows__CGaming__CInput__CRacingWheel_Release
#define IEventHandler_RacingWheel_Invoke __FIEventHandler_1_Windows__CGaming__CInput__CRacingWheel_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIEventHandler_1_Windows__CGaming__CInput__CRacingWheel_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IEventHandler<ABI::Windows::Gaming::Input::RawGameController* > interface
 */
#ifndef ____FIEventHandler_1_Windows__CGaming__CInput__CRawGameController_INTERFACE_DEFINED__
#define ____FIEventHandler_1_Windows__CGaming__CInput__CRawGameController_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIEventHandler_1_Windows__CGaming__CInput__CRawGameController, 0x00621c22, 0x42e8, 0x529f, 0x92,0x70, 0x83,0x6b,0x32,0x93,0x1d,0x72);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("00621c22-42e8-529f-9270-836b32931d72")
            IEventHandler<ABI::Windows::Gaming::Input::RawGameController* > : IEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Gaming::Input::RawGameController*, ABI::Windows::Gaming::Input::IRawGameController* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIEventHandler_1_Windows__CGaming__CInput__CRawGameController, 0x00621c22, 0x42e8, 0x529f, 0x92,0x70, 0x83,0x6b,0x32,0x93,0x1d,0x72)
#endif
#else
typedef struct __FIEventHandler_1_Windows__CGaming__CInput__CRawGameControllerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIEventHandler_1_Windows__CGaming__CInput__CRawGameController *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIEventHandler_1_Windows__CGaming__CInput__CRawGameController *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIEventHandler_1_Windows__CGaming__CInput__CRawGameController *This);

    /*** IEventHandler<ABI::Windows::Gaming::Input::RawGameController* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIEventHandler_1_Windows__CGaming__CInput__CRawGameController *This,
        IInspectable *sender,
        __x_ABI_CWindows_CGaming_CInput_CIRawGameController *args);

    END_INTERFACE
} __FIEventHandler_1_Windows__CGaming__CInput__CRawGameControllerVtbl;

interface __FIEventHandler_1_Windows__CGaming__CInput__CRawGameController {
    CONST_VTBL __FIEventHandler_1_Windows__CGaming__CInput__CRawGameControllerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIEventHandler_1_Windows__CGaming__CInput__CRawGameController_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIEventHandler_1_Windows__CGaming__CInput__CRawGameController_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIEventHandler_1_Windows__CGaming__CInput__CRawGameController_Release(This) (This)->lpVtbl->Release(This)
/*** IEventHandler<ABI::Windows::Gaming::Input::RawGameController* > methods ***/
#define __FIEventHandler_1_Windows__CGaming__CInput__CRawGameController_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIEventHandler_1_Windows__CGaming__CInput__CRawGameController_QueryInterface(__FIEventHandler_1_Windows__CGaming__CInput__CRawGameController* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIEventHandler_1_Windows__CGaming__CInput__CRawGameController_AddRef(__FIEventHandler_1_Windows__CGaming__CInput__CRawGameController* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIEventHandler_1_Windows__CGaming__CInput__CRawGameController_Release(__FIEventHandler_1_Windows__CGaming__CInput__CRawGameController* This) {
    return This->lpVtbl->Release(This);
}
/*** IEventHandler<ABI::Windows::Gaming::Input::RawGameController* > methods ***/
static inline HRESULT __FIEventHandler_1_Windows__CGaming__CInput__CRawGameController_Invoke(__FIEventHandler_1_Windows__CGaming__CInput__CRawGameController* This,IInspectable *sender,__x_ABI_CWindows_CGaming_CInput_CIRawGameController *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IEventHandler_RawGameController IID___FIEventHandler_1_Windows__CGaming__CInput__CRawGameController
#define IEventHandler_RawGameControllerVtbl __FIEventHandler_1_Windows__CGaming__CInput__CRawGameControllerVtbl
#define IEventHandler_RawGameController __FIEventHandler_1_Windows__CGaming__CInput__CRawGameController
#define IEventHandler_RawGameController_QueryInterface __FIEventHandler_1_Windows__CGaming__CInput__CRawGameController_QueryInterface
#define IEventHandler_RawGameController_AddRef __FIEventHandler_1_Windows__CGaming__CInput__CRawGameController_AddRef
#define IEventHandler_RawGameController_Release __FIEventHandler_1_Windows__CGaming__CInput__CRawGameController_Release
#define IEventHandler_RawGameController_Invoke __FIEventHandler_1_Windows__CGaming__CInput__CRawGameController_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIEventHandler_1_Windows__CGaming__CInput__CRawGameController_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITypedEventHandler<ABI::Windows::Gaming::Input::IGameController*,ABI::Windows::Gaming::Input::Headset* > interface
 */
#ifndef ____FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CGaming__CInput__CHeadset_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CGaming__CInput__CHeadset_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CGaming__CInput__CHeadset, 0x07b2f2b7, 0x8825, 0x5c4e, 0xa0,0x52, 0xfc,0xfe,0xdf,0x3a,0xee,0xa1);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("07b2f2b7-8825-5c4e-a052-fcfedf3aeea1")
            ITypedEventHandler<ABI::Windows::Gaming::Input::IGameController*,ABI::Windows::Gaming::Input::Headset* > : ITypedEventHandler_impl<ABI::Windows::Gaming::Input::IGameController*, ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Gaming::Input::Headset*, ABI::Windows::Gaming::Input::IHeadset* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CGaming__CInput__CHeadset, 0x07b2f2b7, 0x8825, 0x5c4e, 0xa0,0x52, 0xfc,0xfe,0xdf,0x3a,0xee,0xa1)
#endif
#else
typedef struct __FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CGaming__CInput__CHeadsetVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CGaming__CInput__CHeadset *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CGaming__CInput__CHeadset *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CGaming__CInput__CHeadset *This);

    /*** ITypedEventHandler<ABI::Windows::Gaming::Input::IGameController*,ABI::Windows::Gaming::Input::Headset* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CGaming__CInput__CHeadset *This,
        __x_ABI_CWindows_CGaming_CInput_CIGameController *sender,
        __x_ABI_CWindows_CGaming_CInput_CIHeadset *args);

    END_INTERFACE
} __FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CGaming__CInput__CHeadsetVtbl;

interface __FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CGaming__CInput__CHeadset {
    CONST_VTBL __FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CGaming__CInput__CHeadsetVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CGaming__CInput__CHeadset_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CGaming__CInput__CHeadset_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CGaming__CInput__CHeadset_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<ABI::Windows::Gaming::Input::IGameController*,ABI::Windows::Gaming::Input::Headset* > methods ***/
#define __FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CGaming__CInput__CHeadset_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CGaming__CInput__CHeadset_QueryInterface(__FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CGaming__CInput__CHeadset* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CGaming__CInput__CHeadset_AddRef(__FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CGaming__CInput__CHeadset* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CGaming__CInput__CHeadset_Release(__FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CGaming__CInput__CHeadset* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<ABI::Windows::Gaming::Input::IGameController*,ABI::Windows::Gaming::Input::Headset* > methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CGaming__CInput__CHeadset_Invoke(__FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CGaming__CInput__CHeadset* This,__x_ABI_CWindows_CGaming_CInput_CIGameController *sender,__x_ABI_CWindows_CGaming_CInput_CIHeadset *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_IGameController_Headset IID___FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CGaming__CInput__CHeadset
#define ITypedEventHandler_IGameController_HeadsetVtbl __FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CGaming__CInput__CHeadsetVtbl
#define ITypedEventHandler_IGameController_Headset __FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CGaming__CInput__CHeadset
#define ITypedEventHandler_IGameController_Headset_QueryInterface __FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CGaming__CInput__CHeadset_QueryInterface
#define ITypedEventHandler_IGameController_Headset_AddRef __FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CGaming__CInput__CHeadset_AddRef
#define ITypedEventHandler_IGameController_Headset_Release __FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CGaming__CInput__CHeadset_Release
#define ITypedEventHandler_IGameController_Headset_Invoke __FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CGaming__CInput__CHeadset_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CGaming__CInput__CHeadset_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITypedEventHandler<ABI::Windows::Gaming::Input::IGameController*,ABI::Windows::System::UserChangedEventArgs* > interface
 */
#ifndef ____FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CSystem__CUserChangedEventArgs_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CSystem__CUserChangedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CSystem__CUserChangedEventArgs, 0xcb753f2c, 0x2f36, 0x5a8f, 0xad,0xad, 0x05,0x7b,0xea,0xe7,0x3a,0xa4);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("cb753f2c-2f36-5a8f-adad-057beae73aa4")
            ITypedEventHandler<ABI::Windows::Gaming::Input::IGameController*,ABI::Windows::System::UserChangedEventArgs* > : ITypedEventHandler_impl<ABI::Windows::Gaming::Input::IGameController*, ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::System::UserChangedEventArgs*, ABI::Windows::System::IUserChangedEventArgs* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CSystem__CUserChangedEventArgs, 0xcb753f2c, 0x2f36, 0x5a8f, 0xad,0xad, 0x05,0x7b,0xea,0xe7,0x3a,0xa4)
#endif
#else
typedef struct __FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CSystem__CUserChangedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CSystem__CUserChangedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CSystem__CUserChangedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CSystem__CUserChangedEventArgs *This);

    /*** ITypedEventHandler<ABI::Windows::Gaming::Input::IGameController*,ABI::Windows::System::UserChangedEventArgs* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CSystem__CUserChangedEventArgs *This,
        __x_ABI_CWindows_CGaming_CInput_CIGameController *sender,
        __x_ABI_CWindows_CSystem_CIUserChangedEventArgs *args);

    END_INTERFACE
} __FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CSystem__CUserChangedEventArgsVtbl;

interface __FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CSystem__CUserChangedEventArgs {
    CONST_VTBL __FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CSystem__CUserChangedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CSystem__CUserChangedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CSystem__CUserChangedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CSystem__CUserChangedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<ABI::Windows::Gaming::Input::IGameController*,ABI::Windows::System::UserChangedEventArgs* > methods ***/
#define __FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CSystem__CUserChangedEventArgs_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CSystem__CUserChangedEventArgs_QueryInterface(__FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CSystem__CUserChangedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CSystem__CUserChangedEventArgs_AddRef(__FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CSystem__CUserChangedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CSystem__CUserChangedEventArgs_Release(__FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CSystem__CUserChangedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<ABI::Windows::Gaming::Input::IGameController*,ABI::Windows::System::UserChangedEventArgs* > methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CSystem__CUserChangedEventArgs_Invoke(__FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CSystem__CUserChangedEventArgs* This,__x_ABI_CWindows_CGaming_CInput_CIGameController *sender,__x_ABI_CWindows_CSystem_CIUserChangedEventArgs *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_IGameController_UserChangedEventArgs IID___FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CSystem__CUserChangedEventArgs
#define ITypedEventHandler_IGameController_UserChangedEventArgsVtbl __FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CSystem__CUserChangedEventArgsVtbl
#define ITypedEventHandler_IGameController_UserChangedEventArgs __FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CSystem__CUserChangedEventArgs
#define ITypedEventHandler_IGameController_UserChangedEventArgs_QueryInterface __FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CSystem__CUserChangedEventArgs_QueryInterface
#define ITypedEventHandler_IGameController_UserChangedEventArgs_AddRef __FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CSystem__CUserChangedEventArgs_AddRef
#define ITypedEventHandler_IGameController_UserChangedEventArgs_Release __FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CSystem__CUserChangedEventArgs_Release
#define ITypedEventHandler_IGameController_UserChangedEventArgs_Invoke __FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CSystem__CUserChangedEventArgs_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_Windows__CGaming__CInput__CIGameController_Windows__CSystem__CUserChangedEventArgs_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IIterator<ABI::Windows::Gaming::Input::ArcadeStick* > interface
 */
#ifndef ____FIIterator_1_Windows__CGaming__CInput__CArcadeStick_INTERFACE_DEFINED__
#define ____FIIterator_1_Windows__CGaming__CInput__CArcadeStick_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterator_1_Windows__CGaming__CInput__CArcadeStick, 0xd30629af, 0xcc9d, 0x52e1, 0x8b,0x1f, 0x0f,0xfa,0x96,0x29,0xaf,0xee);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("d30629af-cc9d-52e1-8b1f-0ffa9629afee")
                IIterator<ABI::Windows::Gaming::Input::ArcadeStick* > : IIterator_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Gaming::Input::ArcadeStick*, ABI::Windows::Gaming::Input::IArcadeStick* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterator_1_Windows__CGaming__CInput__CArcadeStick, 0xd30629af, 0xcc9d, 0x52e1, 0x8b,0x1f, 0x0f,0xfa,0x96,0x29,0xaf,0xee)
#endif
#else
typedef struct __FIIterator_1_Windows__CGaming__CInput__CArcadeStickVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterator_1_Windows__CGaming__CInput__CArcadeStick *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterator_1_Windows__CGaming__CInput__CArcadeStick *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterator_1_Windows__CGaming__CInput__CArcadeStick *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterator_1_Windows__CGaming__CInput__CArcadeStick *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterator_1_Windows__CGaming__CInput__CArcadeStick *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterator_1_Windows__CGaming__CInput__CArcadeStick *This,
        TrustLevel *trustLevel);

    /*** IIterator<ABI::Windows::Gaming::Input::ArcadeStick* > methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Current)(
        __FIIterator_1_Windows__CGaming__CInput__CArcadeStick *This,
        __x_ABI_CWindows_CGaming_CInput_CIArcadeStick **value);

    HRESULT (STDMETHODCALLTYPE *get_HasCurrent)(
        __FIIterator_1_Windows__CGaming__CInput__CArcadeStick *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *MoveNext)(
        __FIIterator_1_Windows__CGaming__CInput__CArcadeStick *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIIterator_1_Windows__CGaming__CInput__CArcadeStick *This,
        UINT32 items_size,
        __x_ABI_CWindows_CGaming_CInput_CIArcadeStick **items,
        UINT32 *value);

    END_INTERFACE
} __FIIterator_1_Windows__CGaming__CInput__CArcadeStickVtbl;

interface __FIIterator_1_Windows__CGaming__CInput__CArcadeStick {
    CONST_VTBL __FIIterator_1_Windows__CGaming__CInput__CArcadeStickVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterator_1_Windows__CGaming__CInput__CArcadeStick_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterator_1_Windows__CGaming__CInput__CArcadeStick_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterator_1_Windows__CGaming__CInput__CArcadeStick_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterator_1_Windows__CGaming__CInput__CArcadeStick_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterator_1_Windows__CGaming__CInput__CArcadeStick_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterator_1_Windows__CGaming__CInput__CArcadeStick_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterator<ABI::Windows::Gaming::Input::ArcadeStick* > methods ***/
#define __FIIterator_1_Windows__CGaming__CInput__CArcadeStick_get_Current(This,value) (This)->lpVtbl->get_Current(This,value)
#define __FIIterator_1_Windows__CGaming__CInput__CArcadeStick_get_HasCurrent(This,value) (This)->lpVtbl->get_HasCurrent(This,value)
#define __FIIterator_1_Windows__CGaming__CInput__CArcadeStick_MoveNext(This,value) (This)->lpVtbl->MoveNext(This,value)
#define __FIIterator_1_Windows__CGaming__CInput__CArcadeStick_GetMany(This,items_size,items,value) (This)->lpVtbl->GetMany(This,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterator_1_Windows__CGaming__CInput__CArcadeStick_QueryInterface(__FIIterator_1_Windows__CGaming__CInput__CArcadeStick* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterator_1_Windows__CGaming__CInput__CArcadeStick_AddRef(__FIIterator_1_Windows__CGaming__CInput__CArcadeStick* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterator_1_Windows__CGaming__CInput__CArcadeStick_Release(__FIIterator_1_Windows__CGaming__CInput__CArcadeStick* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterator_1_Windows__CGaming__CInput__CArcadeStick_GetIids(__FIIterator_1_Windows__CGaming__CInput__CArcadeStick* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterator_1_Windows__CGaming__CInput__CArcadeStick_GetRuntimeClassName(__FIIterator_1_Windows__CGaming__CInput__CArcadeStick* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterator_1_Windows__CGaming__CInput__CArcadeStick_GetTrustLevel(__FIIterator_1_Windows__CGaming__CInput__CArcadeStick* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterator<ABI::Windows::Gaming::Input::ArcadeStick* > methods ***/
static inline HRESULT __FIIterator_1_Windows__CGaming__CInput__CArcadeStick_get_Current(__FIIterator_1_Windows__CGaming__CInput__CArcadeStick* This,__x_ABI_CWindows_CGaming_CInput_CIArcadeStick **value) {
    return This->lpVtbl->get_Current(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CGaming__CInput__CArcadeStick_get_HasCurrent(__FIIterator_1_Windows__CGaming__CInput__CArcadeStick* This,boolean *value) {
    return This->lpVtbl->get_HasCurrent(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CGaming__CInput__CArcadeStick_MoveNext(__FIIterator_1_Windows__CGaming__CInput__CArcadeStick* This,boolean *value) {
    return This->lpVtbl->MoveNext(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CGaming__CInput__CArcadeStick_GetMany(__FIIterator_1_Windows__CGaming__CInput__CArcadeStick* This,UINT32 items_size,__x_ABI_CWindows_CGaming_CInput_CIArcadeStick **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterator_ArcadeStick IID___FIIterator_1_Windows__CGaming__CInput__CArcadeStick
#define IIterator_ArcadeStickVtbl __FIIterator_1_Windows__CGaming__CInput__CArcadeStickVtbl
#define IIterator_ArcadeStick __FIIterator_1_Windows__CGaming__CInput__CArcadeStick
#define IIterator_ArcadeStick_QueryInterface __FIIterator_1_Windows__CGaming__CInput__CArcadeStick_QueryInterface
#define IIterator_ArcadeStick_AddRef __FIIterator_1_Windows__CGaming__CInput__CArcadeStick_AddRef
#define IIterator_ArcadeStick_Release __FIIterator_1_Windows__CGaming__CInput__CArcadeStick_Release
#define IIterator_ArcadeStick_GetIids __FIIterator_1_Windows__CGaming__CInput__CArcadeStick_GetIids
#define IIterator_ArcadeStick_GetRuntimeClassName __FIIterator_1_Windows__CGaming__CInput__CArcadeStick_GetRuntimeClassName
#define IIterator_ArcadeStick_GetTrustLevel __FIIterator_1_Windows__CGaming__CInput__CArcadeStick_GetTrustLevel
#define IIterator_ArcadeStick_get_Current __FIIterator_1_Windows__CGaming__CInput__CArcadeStick_get_Current
#define IIterator_ArcadeStick_get_HasCurrent __FIIterator_1_Windows__CGaming__CInput__CArcadeStick_get_HasCurrent
#define IIterator_ArcadeStick_MoveNext __FIIterator_1_Windows__CGaming__CInput__CArcadeStick_MoveNext
#define IIterator_ArcadeStick_GetMany __FIIterator_1_Windows__CGaming__CInput__CArcadeStick_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterator_1_Windows__CGaming__CInput__CArcadeStick_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IIterable<ABI::Windows::Gaming::Input::ArcadeStick* > interface
 */
#ifndef ____FIIterable_1_Windows__CGaming__CInput__CArcadeStick_INTERFACE_DEFINED__
#define ____FIIterable_1_Windows__CGaming__CInput__CArcadeStick_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterable_1_Windows__CGaming__CInput__CArcadeStick, 0x9376f457, 0x2da5, 0x544a, 0xa4,0x09, 0xc6,0x36,0xf5,0xd8,0x1c,0x35);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("9376f457-2da5-544a-a409-c636f5d81c35")
                IIterable<ABI::Windows::Gaming::Input::ArcadeStick* > : IIterable_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Gaming::Input::ArcadeStick*, ABI::Windows::Gaming::Input::IArcadeStick* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterable_1_Windows__CGaming__CInput__CArcadeStick, 0x9376f457, 0x2da5, 0x544a, 0xa4,0x09, 0xc6,0x36,0xf5,0xd8,0x1c,0x35)
#endif
#else
typedef struct __FIIterable_1_Windows__CGaming__CInput__CArcadeStickVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterable_1_Windows__CGaming__CInput__CArcadeStick *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterable_1_Windows__CGaming__CInput__CArcadeStick *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterable_1_Windows__CGaming__CInput__CArcadeStick *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterable_1_Windows__CGaming__CInput__CArcadeStick *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterable_1_Windows__CGaming__CInput__CArcadeStick *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterable_1_Windows__CGaming__CInput__CArcadeStick *This,
        TrustLevel *trustLevel);

    /*** IIterable<ABI::Windows::Gaming::Input::ArcadeStick* > methods ***/
    HRESULT (STDMETHODCALLTYPE *First)(
        __FIIterable_1_Windows__CGaming__CInput__CArcadeStick *This,
        __FIIterator_1_Windows__CGaming__CInput__CArcadeStick **value);

    END_INTERFACE
} __FIIterable_1_Windows__CGaming__CInput__CArcadeStickVtbl;

interface __FIIterable_1_Windows__CGaming__CInput__CArcadeStick {
    CONST_VTBL __FIIterable_1_Windows__CGaming__CInput__CArcadeStickVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterable_1_Windows__CGaming__CInput__CArcadeStick_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterable_1_Windows__CGaming__CInput__CArcadeStick_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterable_1_Windows__CGaming__CInput__CArcadeStick_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterable_1_Windows__CGaming__CInput__CArcadeStick_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterable_1_Windows__CGaming__CInput__CArcadeStick_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterable_1_Windows__CGaming__CInput__CArcadeStick_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterable<ABI::Windows::Gaming::Input::ArcadeStick* > methods ***/
#define __FIIterable_1_Windows__CGaming__CInput__CArcadeStick_First(This,value) (This)->lpVtbl->First(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterable_1_Windows__CGaming__CInput__CArcadeStick_QueryInterface(__FIIterable_1_Windows__CGaming__CInput__CArcadeStick* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterable_1_Windows__CGaming__CInput__CArcadeStick_AddRef(__FIIterable_1_Windows__CGaming__CInput__CArcadeStick* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterable_1_Windows__CGaming__CInput__CArcadeStick_Release(__FIIterable_1_Windows__CGaming__CInput__CArcadeStick* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterable_1_Windows__CGaming__CInput__CArcadeStick_GetIids(__FIIterable_1_Windows__CGaming__CInput__CArcadeStick* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterable_1_Windows__CGaming__CInput__CArcadeStick_GetRuntimeClassName(__FIIterable_1_Windows__CGaming__CInput__CArcadeStick* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterable_1_Windows__CGaming__CInput__CArcadeStick_GetTrustLevel(__FIIterable_1_Windows__CGaming__CInput__CArcadeStick* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterable<ABI::Windows::Gaming::Input::ArcadeStick* > methods ***/
static inline HRESULT __FIIterable_1_Windows__CGaming__CInput__CArcadeStick_First(__FIIterable_1_Windows__CGaming__CInput__CArcadeStick* This,__FIIterator_1_Windows__CGaming__CInput__CArcadeStick **value) {
    return This->lpVtbl->First(This,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterable_ArcadeStick IID___FIIterable_1_Windows__CGaming__CInput__CArcadeStick
#define IIterable_ArcadeStickVtbl __FIIterable_1_Windows__CGaming__CInput__CArcadeStickVtbl
#define IIterable_ArcadeStick __FIIterable_1_Windows__CGaming__CInput__CArcadeStick
#define IIterable_ArcadeStick_QueryInterface __FIIterable_1_Windows__CGaming__CInput__CArcadeStick_QueryInterface
#define IIterable_ArcadeStick_AddRef __FIIterable_1_Windows__CGaming__CInput__CArcadeStick_AddRef
#define IIterable_ArcadeStick_Release __FIIterable_1_Windows__CGaming__CInput__CArcadeStick_Release
#define IIterable_ArcadeStick_GetIids __FIIterable_1_Windows__CGaming__CInput__CArcadeStick_GetIids
#define IIterable_ArcadeStick_GetRuntimeClassName __FIIterable_1_Windows__CGaming__CInput__CArcadeStick_GetRuntimeClassName
#define IIterable_ArcadeStick_GetTrustLevel __FIIterable_1_Windows__CGaming__CInput__CArcadeStick_GetTrustLevel
#define IIterable_ArcadeStick_First __FIIterable_1_Windows__CGaming__CInput__CArcadeStick_First
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterable_1_Windows__CGaming__CInput__CArcadeStick_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVectorView<ABI::Windows::Gaming::Input::ArcadeStick* > interface
 */
#ifndef ____FIVectorView_1_Windows__CGaming__CInput__CArcadeStick_INTERFACE_DEFINED__
#define ____FIVectorView_1_Windows__CGaming__CInput__CArcadeStick_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIVectorView_1_Windows__CGaming__CInput__CArcadeStick, 0xbecace75, 0xd0cd, 0x5a9c, 0x84,0x5f, 0x72,0xf0,0x85,0x50,0x3c,0xdf);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("becace75-d0cd-5a9c-845f-72f085503cdf")
                IVectorView<ABI::Windows::Gaming::Input::ArcadeStick* > : IVectorView_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Gaming::Input::ArcadeStick*, ABI::Windows::Gaming::Input::IArcadeStick* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIVectorView_1_Windows__CGaming__CInput__CArcadeStick, 0xbecace75, 0xd0cd, 0x5a9c, 0x84,0x5f, 0x72,0xf0,0x85,0x50,0x3c,0xdf)
#endif
#else
typedef struct __FIVectorView_1_Windows__CGaming__CInput__CArcadeStickVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIVectorView_1_Windows__CGaming__CInput__CArcadeStick *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIVectorView_1_Windows__CGaming__CInput__CArcadeStick *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIVectorView_1_Windows__CGaming__CInput__CArcadeStick *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIVectorView_1_Windows__CGaming__CInput__CArcadeStick *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIVectorView_1_Windows__CGaming__CInput__CArcadeStick *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIVectorView_1_Windows__CGaming__CInput__CArcadeStick *This,
        TrustLevel *trustLevel);

    /*** IVectorView<ABI::Windows::Gaming::Input::ArcadeStick* > methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAt)(
        __FIVectorView_1_Windows__CGaming__CInput__CArcadeStick *This,
        UINT32 index,
        __x_ABI_CWindows_CGaming_CInput_CIArcadeStick **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIVectorView_1_Windows__CGaming__CInput__CArcadeStick *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *IndexOf)(
        __FIVectorView_1_Windows__CGaming__CInput__CArcadeStick *This,
        __x_ABI_CWindows_CGaming_CInput_CIArcadeStick *element,
        UINT32 *index,
        BOOLEAN *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIVectorView_1_Windows__CGaming__CInput__CArcadeStick *This,
        UINT32 start_index,
        UINT32 items_size,
        __x_ABI_CWindows_CGaming_CInput_CIArcadeStick **items,
        UINT32 *value);

    END_INTERFACE
} __FIVectorView_1_Windows__CGaming__CInput__CArcadeStickVtbl;

interface __FIVectorView_1_Windows__CGaming__CInput__CArcadeStick {
    CONST_VTBL __FIVectorView_1_Windows__CGaming__CInput__CArcadeStickVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIVectorView_1_Windows__CGaming__CInput__CArcadeStick_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIVectorView_1_Windows__CGaming__CInput__CArcadeStick_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIVectorView_1_Windows__CGaming__CInput__CArcadeStick_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIVectorView_1_Windows__CGaming__CInput__CArcadeStick_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIVectorView_1_Windows__CGaming__CInput__CArcadeStick_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIVectorView_1_Windows__CGaming__CInput__CArcadeStick_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IVectorView<ABI::Windows::Gaming::Input::ArcadeStick* > methods ***/
#define __FIVectorView_1_Windows__CGaming__CInput__CArcadeStick_GetAt(This,index,value) (This)->lpVtbl->GetAt(This,index,value)
#define __FIVectorView_1_Windows__CGaming__CInput__CArcadeStick_get_Size(This,value) (This)->lpVtbl->get_Size(This,value)
#define __FIVectorView_1_Windows__CGaming__CInput__CArcadeStick_IndexOf(This,element,index,value) (This)->lpVtbl->IndexOf(This,element,index,value)
#define __FIVectorView_1_Windows__CGaming__CInput__CArcadeStick_GetMany(This,start_index,items_size,items,value) (This)->lpVtbl->GetMany(This,start_index,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIVectorView_1_Windows__CGaming__CInput__CArcadeStick_QueryInterface(__FIVectorView_1_Windows__CGaming__CInput__CArcadeStick* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIVectorView_1_Windows__CGaming__CInput__CArcadeStick_AddRef(__FIVectorView_1_Windows__CGaming__CInput__CArcadeStick* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIVectorView_1_Windows__CGaming__CInput__CArcadeStick_Release(__FIVectorView_1_Windows__CGaming__CInput__CArcadeStick* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIVectorView_1_Windows__CGaming__CInput__CArcadeStick_GetIids(__FIVectorView_1_Windows__CGaming__CInput__CArcadeStick* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIVectorView_1_Windows__CGaming__CInput__CArcadeStick_GetRuntimeClassName(__FIVectorView_1_Windows__CGaming__CInput__CArcadeStick* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIVectorView_1_Windows__CGaming__CInput__CArcadeStick_GetTrustLevel(__FIVectorView_1_Windows__CGaming__CInput__CArcadeStick* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IVectorView<ABI::Windows::Gaming::Input::ArcadeStick* > methods ***/
static inline HRESULT __FIVectorView_1_Windows__CGaming__CInput__CArcadeStick_GetAt(__FIVectorView_1_Windows__CGaming__CInput__CArcadeStick* This,UINT32 index,__x_ABI_CWindows_CGaming_CInput_CIArcadeStick **value) {
    return This->lpVtbl->GetAt(This,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CGaming__CInput__CArcadeStick_get_Size(__FIVectorView_1_Windows__CGaming__CInput__CArcadeStick* This,UINT32 *value) {
    return This->lpVtbl->get_Size(This,value);
}
static inline HRESULT __FIVectorView_1_Windows__CGaming__CInput__CArcadeStick_IndexOf(__FIVectorView_1_Windows__CGaming__CInput__CArcadeStick* This,__x_ABI_CWindows_CGaming_CInput_CIArcadeStick *element,UINT32 *index,BOOLEAN *value) {
    return This->lpVtbl->IndexOf(This,element,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CGaming__CInput__CArcadeStick_GetMany(__FIVectorView_1_Windows__CGaming__CInput__CArcadeStick* This,UINT32 start_index,UINT32 items_size,__x_ABI_CWindows_CGaming_CInput_CIArcadeStick **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,start_index,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IVectorView_ArcadeStick IID___FIVectorView_1_Windows__CGaming__CInput__CArcadeStick
#define IVectorView_ArcadeStickVtbl __FIVectorView_1_Windows__CGaming__CInput__CArcadeStickVtbl
#define IVectorView_ArcadeStick __FIVectorView_1_Windows__CGaming__CInput__CArcadeStick
#define IVectorView_ArcadeStick_QueryInterface __FIVectorView_1_Windows__CGaming__CInput__CArcadeStick_QueryInterface
#define IVectorView_ArcadeStick_AddRef __FIVectorView_1_Windows__CGaming__CInput__CArcadeStick_AddRef
#define IVectorView_ArcadeStick_Release __FIVectorView_1_Windows__CGaming__CInput__CArcadeStick_Release
#define IVectorView_ArcadeStick_GetIids __FIVectorView_1_Windows__CGaming__CInput__CArcadeStick_GetIids
#define IVectorView_ArcadeStick_GetRuntimeClassName __FIVectorView_1_Windows__CGaming__CInput__CArcadeStick_GetRuntimeClassName
#define IVectorView_ArcadeStick_GetTrustLevel __FIVectorView_1_Windows__CGaming__CInput__CArcadeStick_GetTrustLevel
#define IVectorView_ArcadeStick_GetAt __FIVectorView_1_Windows__CGaming__CInput__CArcadeStick_GetAt
#define IVectorView_ArcadeStick_get_Size __FIVectorView_1_Windows__CGaming__CInput__CArcadeStick_get_Size
#define IVectorView_ArcadeStick_IndexOf __FIVectorView_1_Windows__CGaming__CInput__CArcadeStick_IndexOf
#define IVectorView_ArcadeStick_GetMany __FIVectorView_1_Windows__CGaming__CInput__CArcadeStick_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIVectorView_1_Windows__CGaming__CInput__CArcadeStick_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IIterator<ABI::Windows::Gaming::Input::FlightStick* > interface
 */
#ifndef ____FIIterator_1_Windows__CGaming__CInput__CFlightStick_INTERFACE_DEFINED__
#define ____FIIterator_1_Windows__CGaming__CInput__CFlightStick_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterator_1_Windows__CGaming__CInput__CFlightStick, 0xf5fa1919, 0x3f18, 0x5560, 0xbb,0x13, 0xcf,0x70,0x18,0xac,0x41,0xd5);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("f5fa1919-3f18-5560-bb13-cf7018ac41d5")
                IIterator<ABI::Windows::Gaming::Input::FlightStick* > : IIterator_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Gaming::Input::FlightStick*, ABI::Windows::Gaming::Input::IFlightStick* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterator_1_Windows__CGaming__CInput__CFlightStick, 0xf5fa1919, 0x3f18, 0x5560, 0xbb,0x13, 0xcf,0x70,0x18,0xac,0x41,0xd5)
#endif
#else
typedef struct __FIIterator_1_Windows__CGaming__CInput__CFlightStickVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterator_1_Windows__CGaming__CInput__CFlightStick *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterator_1_Windows__CGaming__CInput__CFlightStick *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterator_1_Windows__CGaming__CInput__CFlightStick *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterator_1_Windows__CGaming__CInput__CFlightStick *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterator_1_Windows__CGaming__CInput__CFlightStick *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterator_1_Windows__CGaming__CInput__CFlightStick *This,
        TrustLevel *trustLevel);

    /*** IIterator<ABI::Windows::Gaming::Input::FlightStick* > methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Current)(
        __FIIterator_1_Windows__CGaming__CInput__CFlightStick *This,
        __x_ABI_CWindows_CGaming_CInput_CIFlightStick **value);

    HRESULT (STDMETHODCALLTYPE *get_HasCurrent)(
        __FIIterator_1_Windows__CGaming__CInput__CFlightStick *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *MoveNext)(
        __FIIterator_1_Windows__CGaming__CInput__CFlightStick *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIIterator_1_Windows__CGaming__CInput__CFlightStick *This,
        UINT32 items_size,
        __x_ABI_CWindows_CGaming_CInput_CIFlightStick **items,
        UINT32 *value);

    END_INTERFACE
} __FIIterator_1_Windows__CGaming__CInput__CFlightStickVtbl;

interface __FIIterator_1_Windows__CGaming__CInput__CFlightStick {
    CONST_VTBL __FIIterator_1_Windows__CGaming__CInput__CFlightStickVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterator_1_Windows__CGaming__CInput__CFlightStick_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterator_1_Windows__CGaming__CInput__CFlightStick_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterator_1_Windows__CGaming__CInput__CFlightStick_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterator_1_Windows__CGaming__CInput__CFlightStick_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterator_1_Windows__CGaming__CInput__CFlightStick_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterator_1_Windows__CGaming__CInput__CFlightStick_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterator<ABI::Windows::Gaming::Input::FlightStick* > methods ***/
#define __FIIterator_1_Windows__CGaming__CInput__CFlightStick_get_Current(This,value) (This)->lpVtbl->get_Current(This,value)
#define __FIIterator_1_Windows__CGaming__CInput__CFlightStick_get_HasCurrent(This,value) (This)->lpVtbl->get_HasCurrent(This,value)
#define __FIIterator_1_Windows__CGaming__CInput__CFlightStick_MoveNext(This,value) (This)->lpVtbl->MoveNext(This,value)
#define __FIIterator_1_Windows__CGaming__CInput__CFlightStick_GetMany(This,items_size,items,value) (This)->lpVtbl->GetMany(This,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterator_1_Windows__CGaming__CInput__CFlightStick_QueryInterface(__FIIterator_1_Windows__CGaming__CInput__CFlightStick* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterator_1_Windows__CGaming__CInput__CFlightStick_AddRef(__FIIterator_1_Windows__CGaming__CInput__CFlightStick* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterator_1_Windows__CGaming__CInput__CFlightStick_Release(__FIIterator_1_Windows__CGaming__CInput__CFlightStick* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterator_1_Windows__CGaming__CInput__CFlightStick_GetIids(__FIIterator_1_Windows__CGaming__CInput__CFlightStick* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterator_1_Windows__CGaming__CInput__CFlightStick_GetRuntimeClassName(__FIIterator_1_Windows__CGaming__CInput__CFlightStick* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterator_1_Windows__CGaming__CInput__CFlightStick_GetTrustLevel(__FIIterator_1_Windows__CGaming__CInput__CFlightStick* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterator<ABI::Windows::Gaming::Input::FlightStick* > methods ***/
static inline HRESULT __FIIterator_1_Windows__CGaming__CInput__CFlightStick_get_Current(__FIIterator_1_Windows__CGaming__CInput__CFlightStick* This,__x_ABI_CWindows_CGaming_CInput_CIFlightStick **value) {
    return This->lpVtbl->get_Current(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CGaming__CInput__CFlightStick_get_HasCurrent(__FIIterator_1_Windows__CGaming__CInput__CFlightStick* This,boolean *value) {
    return This->lpVtbl->get_HasCurrent(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CGaming__CInput__CFlightStick_MoveNext(__FIIterator_1_Windows__CGaming__CInput__CFlightStick* This,boolean *value) {
    return This->lpVtbl->MoveNext(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CGaming__CInput__CFlightStick_GetMany(__FIIterator_1_Windows__CGaming__CInput__CFlightStick* This,UINT32 items_size,__x_ABI_CWindows_CGaming_CInput_CIFlightStick **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterator_FlightStick IID___FIIterator_1_Windows__CGaming__CInput__CFlightStick
#define IIterator_FlightStickVtbl __FIIterator_1_Windows__CGaming__CInput__CFlightStickVtbl
#define IIterator_FlightStick __FIIterator_1_Windows__CGaming__CInput__CFlightStick
#define IIterator_FlightStick_QueryInterface __FIIterator_1_Windows__CGaming__CInput__CFlightStick_QueryInterface
#define IIterator_FlightStick_AddRef __FIIterator_1_Windows__CGaming__CInput__CFlightStick_AddRef
#define IIterator_FlightStick_Release __FIIterator_1_Windows__CGaming__CInput__CFlightStick_Release
#define IIterator_FlightStick_GetIids __FIIterator_1_Windows__CGaming__CInput__CFlightStick_GetIids
#define IIterator_FlightStick_GetRuntimeClassName __FIIterator_1_Windows__CGaming__CInput__CFlightStick_GetRuntimeClassName
#define IIterator_FlightStick_GetTrustLevel __FIIterator_1_Windows__CGaming__CInput__CFlightStick_GetTrustLevel
#define IIterator_FlightStick_get_Current __FIIterator_1_Windows__CGaming__CInput__CFlightStick_get_Current
#define IIterator_FlightStick_get_HasCurrent __FIIterator_1_Windows__CGaming__CInput__CFlightStick_get_HasCurrent
#define IIterator_FlightStick_MoveNext __FIIterator_1_Windows__CGaming__CInput__CFlightStick_MoveNext
#define IIterator_FlightStick_GetMany __FIIterator_1_Windows__CGaming__CInput__CFlightStick_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterator_1_Windows__CGaming__CInput__CFlightStick_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IIterable<ABI::Windows::Gaming::Input::FlightStick* > interface
 */
#ifndef ____FIIterable_1_Windows__CGaming__CInput__CFlightStick_INTERFACE_DEFINED__
#define ____FIIterable_1_Windows__CGaming__CInput__CFlightStick_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterable_1_Windows__CGaming__CInput__CFlightStick, 0x3b7fc175, 0xbebe, 0x52ef, 0xa3,0xe9, 0xdd,0xa7,0x5e,0xa1,0xac,0xfc);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("3b7fc175-bebe-52ef-a3e9-dda75ea1acfc")
                IIterable<ABI::Windows::Gaming::Input::FlightStick* > : IIterable_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Gaming::Input::FlightStick*, ABI::Windows::Gaming::Input::IFlightStick* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterable_1_Windows__CGaming__CInput__CFlightStick, 0x3b7fc175, 0xbebe, 0x52ef, 0xa3,0xe9, 0xdd,0xa7,0x5e,0xa1,0xac,0xfc)
#endif
#else
typedef struct __FIIterable_1_Windows__CGaming__CInput__CFlightStickVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterable_1_Windows__CGaming__CInput__CFlightStick *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterable_1_Windows__CGaming__CInput__CFlightStick *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterable_1_Windows__CGaming__CInput__CFlightStick *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterable_1_Windows__CGaming__CInput__CFlightStick *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterable_1_Windows__CGaming__CInput__CFlightStick *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterable_1_Windows__CGaming__CInput__CFlightStick *This,
        TrustLevel *trustLevel);

    /*** IIterable<ABI::Windows::Gaming::Input::FlightStick* > methods ***/
    HRESULT (STDMETHODCALLTYPE *First)(
        __FIIterable_1_Windows__CGaming__CInput__CFlightStick *This,
        __FIIterator_1_Windows__CGaming__CInput__CFlightStick **value);

    END_INTERFACE
} __FIIterable_1_Windows__CGaming__CInput__CFlightStickVtbl;

interface __FIIterable_1_Windows__CGaming__CInput__CFlightStick {
    CONST_VTBL __FIIterable_1_Windows__CGaming__CInput__CFlightStickVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterable_1_Windows__CGaming__CInput__CFlightStick_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterable_1_Windows__CGaming__CInput__CFlightStick_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterable_1_Windows__CGaming__CInput__CFlightStick_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterable_1_Windows__CGaming__CInput__CFlightStick_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterable_1_Windows__CGaming__CInput__CFlightStick_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterable_1_Windows__CGaming__CInput__CFlightStick_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterable<ABI::Windows::Gaming::Input::FlightStick* > methods ***/
#define __FIIterable_1_Windows__CGaming__CInput__CFlightStick_First(This,value) (This)->lpVtbl->First(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterable_1_Windows__CGaming__CInput__CFlightStick_QueryInterface(__FIIterable_1_Windows__CGaming__CInput__CFlightStick* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterable_1_Windows__CGaming__CInput__CFlightStick_AddRef(__FIIterable_1_Windows__CGaming__CInput__CFlightStick* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterable_1_Windows__CGaming__CInput__CFlightStick_Release(__FIIterable_1_Windows__CGaming__CInput__CFlightStick* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterable_1_Windows__CGaming__CInput__CFlightStick_GetIids(__FIIterable_1_Windows__CGaming__CInput__CFlightStick* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterable_1_Windows__CGaming__CInput__CFlightStick_GetRuntimeClassName(__FIIterable_1_Windows__CGaming__CInput__CFlightStick* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterable_1_Windows__CGaming__CInput__CFlightStick_GetTrustLevel(__FIIterable_1_Windows__CGaming__CInput__CFlightStick* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterable<ABI::Windows::Gaming::Input::FlightStick* > methods ***/
static inline HRESULT __FIIterable_1_Windows__CGaming__CInput__CFlightStick_First(__FIIterable_1_Windows__CGaming__CInput__CFlightStick* This,__FIIterator_1_Windows__CGaming__CInput__CFlightStick **value) {
    return This->lpVtbl->First(This,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterable_FlightStick IID___FIIterable_1_Windows__CGaming__CInput__CFlightStick
#define IIterable_FlightStickVtbl __FIIterable_1_Windows__CGaming__CInput__CFlightStickVtbl
#define IIterable_FlightStick __FIIterable_1_Windows__CGaming__CInput__CFlightStick
#define IIterable_FlightStick_QueryInterface __FIIterable_1_Windows__CGaming__CInput__CFlightStick_QueryInterface
#define IIterable_FlightStick_AddRef __FIIterable_1_Windows__CGaming__CInput__CFlightStick_AddRef
#define IIterable_FlightStick_Release __FIIterable_1_Windows__CGaming__CInput__CFlightStick_Release
#define IIterable_FlightStick_GetIids __FIIterable_1_Windows__CGaming__CInput__CFlightStick_GetIids
#define IIterable_FlightStick_GetRuntimeClassName __FIIterable_1_Windows__CGaming__CInput__CFlightStick_GetRuntimeClassName
#define IIterable_FlightStick_GetTrustLevel __FIIterable_1_Windows__CGaming__CInput__CFlightStick_GetTrustLevel
#define IIterable_FlightStick_First __FIIterable_1_Windows__CGaming__CInput__CFlightStick_First
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterable_1_Windows__CGaming__CInput__CFlightStick_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVectorView<ABI::Windows::Gaming::Input::FlightStick* > interface
 */
#ifndef ____FIVectorView_1_Windows__CGaming__CInput__CFlightStick_INTERFACE_DEFINED__
#define ____FIVectorView_1_Windows__CGaming__CInput__CFlightStick_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIVectorView_1_Windows__CGaming__CInput__CFlightStick, 0x8b9d067e, 0xb6f5, 0x592f, 0xa9,0x0a, 0xd7,0x2c,0x3d,0x98,0xd4,0xda);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("8b9d067e-b6f5-592f-a90a-d72c3d98d4da")
                IVectorView<ABI::Windows::Gaming::Input::FlightStick* > : IVectorView_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Gaming::Input::FlightStick*, ABI::Windows::Gaming::Input::IFlightStick* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIVectorView_1_Windows__CGaming__CInput__CFlightStick, 0x8b9d067e, 0xb6f5, 0x592f, 0xa9,0x0a, 0xd7,0x2c,0x3d,0x98,0xd4,0xda)
#endif
#else
typedef struct __FIVectorView_1_Windows__CGaming__CInput__CFlightStickVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIVectorView_1_Windows__CGaming__CInput__CFlightStick *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIVectorView_1_Windows__CGaming__CInput__CFlightStick *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIVectorView_1_Windows__CGaming__CInput__CFlightStick *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIVectorView_1_Windows__CGaming__CInput__CFlightStick *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIVectorView_1_Windows__CGaming__CInput__CFlightStick *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIVectorView_1_Windows__CGaming__CInput__CFlightStick *This,
        TrustLevel *trustLevel);

    /*** IVectorView<ABI::Windows::Gaming::Input::FlightStick* > methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAt)(
        __FIVectorView_1_Windows__CGaming__CInput__CFlightStick *This,
        UINT32 index,
        __x_ABI_CWindows_CGaming_CInput_CIFlightStick **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIVectorView_1_Windows__CGaming__CInput__CFlightStick *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *IndexOf)(
        __FIVectorView_1_Windows__CGaming__CInput__CFlightStick *This,
        __x_ABI_CWindows_CGaming_CInput_CIFlightStick *element,
        UINT32 *index,
        BOOLEAN *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIVectorView_1_Windows__CGaming__CInput__CFlightStick *This,
        UINT32 start_index,
        UINT32 items_size,
        __x_ABI_CWindows_CGaming_CInput_CIFlightStick **items,
        UINT32 *value);

    END_INTERFACE
} __FIVectorView_1_Windows__CGaming__CInput__CFlightStickVtbl;

interface __FIVectorView_1_Windows__CGaming__CInput__CFlightStick {
    CONST_VTBL __FIVectorView_1_Windows__CGaming__CInput__CFlightStickVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIVectorView_1_Windows__CGaming__CInput__CFlightStick_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIVectorView_1_Windows__CGaming__CInput__CFlightStick_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIVectorView_1_Windows__CGaming__CInput__CFlightStick_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIVectorView_1_Windows__CGaming__CInput__CFlightStick_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIVectorView_1_Windows__CGaming__CInput__CFlightStick_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIVectorView_1_Windows__CGaming__CInput__CFlightStick_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IVectorView<ABI::Windows::Gaming::Input::FlightStick* > methods ***/
#define __FIVectorView_1_Windows__CGaming__CInput__CFlightStick_GetAt(This,index,value) (This)->lpVtbl->GetAt(This,index,value)
#define __FIVectorView_1_Windows__CGaming__CInput__CFlightStick_get_Size(This,value) (This)->lpVtbl->get_Size(This,value)
#define __FIVectorView_1_Windows__CGaming__CInput__CFlightStick_IndexOf(This,element,index,value) (This)->lpVtbl->IndexOf(This,element,index,value)
#define __FIVectorView_1_Windows__CGaming__CInput__CFlightStick_GetMany(This,start_index,items_size,items,value) (This)->lpVtbl->GetMany(This,start_index,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIVectorView_1_Windows__CGaming__CInput__CFlightStick_QueryInterface(__FIVectorView_1_Windows__CGaming__CInput__CFlightStick* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIVectorView_1_Windows__CGaming__CInput__CFlightStick_AddRef(__FIVectorView_1_Windows__CGaming__CInput__CFlightStick* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIVectorView_1_Windows__CGaming__CInput__CFlightStick_Release(__FIVectorView_1_Windows__CGaming__CInput__CFlightStick* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIVectorView_1_Windows__CGaming__CInput__CFlightStick_GetIids(__FIVectorView_1_Windows__CGaming__CInput__CFlightStick* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIVectorView_1_Windows__CGaming__CInput__CFlightStick_GetRuntimeClassName(__FIVectorView_1_Windows__CGaming__CInput__CFlightStick* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIVectorView_1_Windows__CGaming__CInput__CFlightStick_GetTrustLevel(__FIVectorView_1_Windows__CGaming__CInput__CFlightStick* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IVectorView<ABI::Windows::Gaming::Input::FlightStick* > methods ***/
static inline HRESULT __FIVectorView_1_Windows__CGaming__CInput__CFlightStick_GetAt(__FIVectorView_1_Windows__CGaming__CInput__CFlightStick* This,UINT32 index,__x_ABI_CWindows_CGaming_CInput_CIFlightStick **value) {
    return This->lpVtbl->GetAt(This,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CGaming__CInput__CFlightStick_get_Size(__FIVectorView_1_Windows__CGaming__CInput__CFlightStick* This,UINT32 *value) {
    return This->lpVtbl->get_Size(This,value);
}
static inline HRESULT __FIVectorView_1_Windows__CGaming__CInput__CFlightStick_IndexOf(__FIVectorView_1_Windows__CGaming__CInput__CFlightStick* This,__x_ABI_CWindows_CGaming_CInput_CIFlightStick *element,UINT32 *index,BOOLEAN *value) {
    return This->lpVtbl->IndexOf(This,element,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CGaming__CInput__CFlightStick_GetMany(__FIVectorView_1_Windows__CGaming__CInput__CFlightStick* This,UINT32 start_index,UINT32 items_size,__x_ABI_CWindows_CGaming_CInput_CIFlightStick **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,start_index,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IVectorView_FlightStick IID___FIVectorView_1_Windows__CGaming__CInput__CFlightStick
#define IVectorView_FlightStickVtbl __FIVectorView_1_Windows__CGaming__CInput__CFlightStickVtbl
#define IVectorView_FlightStick __FIVectorView_1_Windows__CGaming__CInput__CFlightStick
#define IVectorView_FlightStick_QueryInterface __FIVectorView_1_Windows__CGaming__CInput__CFlightStick_QueryInterface
#define IVectorView_FlightStick_AddRef __FIVectorView_1_Windows__CGaming__CInput__CFlightStick_AddRef
#define IVectorView_FlightStick_Release __FIVectorView_1_Windows__CGaming__CInput__CFlightStick_Release
#define IVectorView_FlightStick_GetIids __FIVectorView_1_Windows__CGaming__CInput__CFlightStick_GetIids
#define IVectorView_FlightStick_GetRuntimeClassName __FIVectorView_1_Windows__CGaming__CInput__CFlightStick_GetRuntimeClassName
#define IVectorView_FlightStick_GetTrustLevel __FIVectorView_1_Windows__CGaming__CInput__CFlightStick_GetTrustLevel
#define IVectorView_FlightStick_GetAt __FIVectorView_1_Windows__CGaming__CInput__CFlightStick_GetAt
#define IVectorView_FlightStick_get_Size __FIVectorView_1_Windows__CGaming__CInput__CFlightStick_get_Size
#define IVectorView_FlightStick_IndexOf __FIVectorView_1_Windows__CGaming__CInput__CFlightStick_IndexOf
#define IVectorView_FlightStick_GetMany __FIVectorView_1_Windows__CGaming__CInput__CFlightStick_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIVectorView_1_Windows__CGaming__CInput__CFlightStick_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IIterator<ABI::Windows::Gaming::Input::Gamepad* > interface
 */
#ifndef ____FIIterator_1_Windows__CGaming__CInput__CGamepad_INTERFACE_DEFINED__
#define ____FIIterator_1_Windows__CGaming__CInput__CGamepad_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterator_1_Windows__CGaming__CInput__CGamepad, 0x246737e8, 0x12bc, 0x5c64, 0xaf,0x52, 0x06,0xdb,0x4b,0x13,0xfa,0x2f);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("246737e8-12bc-5c64-af52-06db4b13fa2f")
                IIterator<ABI::Windows::Gaming::Input::Gamepad* > : IIterator_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Gaming::Input::Gamepad*, ABI::Windows::Gaming::Input::IGamepad* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterator_1_Windows__CGaming__CInput__CGamepad, 0x246737e8, 0x12bc, 0x5c64, 0xaf,0x52, 0x06,0xdb,0x4b,0x13,0xfa,0x2f)
#endif
#else
typedef struct __FIIterator_1_Windows__CGaming__CInput__CGamepadVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterator_1_Windows__CGaming__CInput__CGamepad *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterator_1_Windows__CGaming__CInput__CGamepad *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterator_1_Windows__CGaming__CInput__CGamepad *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterator_1_Windows__CGaming__CInput__CGamepad *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterator_1_Windows__CGaming__CInput__CGamepad *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterator_1_Windows__CGaming__CInput__CGamepad *This,
        TrustLevel *trustLevel);

    /*** IIterator<ABI::Windows::Gaming::Input::Gamepad* > methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Current)(
        __FIIterator_1_Windows__CGaming__CInput__CGamepad *This,
        __x_ABI_CWindows_CGaming_CInput_CIGamepad **value);

    HRESULT (STDMETHODCALLTYPE *get_HasCurrent)(
        __FIIterator_1_Windows__CGaming__CInput__CGamepad *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *MoveNext)(
        __FIIterator_1_Windows__CGaming__CInput__CGamepad *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIIterator_1_Windows__CGaming__CInput__CGamepad *This,
        UINT32 items_size,
        __x_ABI_CWindows_CGaming_CInput_CIGamepad **items,
        UINT32 *value);

    END_INTERFACE
} __FIIterator_1_Windows__CGaming__CInput__CGamepadVtbl;

interface __FIIterator_1_Windows__CGaming__CInput__CGamepad {
    CONST_VTBL __FIIterator_1_Windows__CGaming__CInput__CGamepadVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterator_1_Windows__CGaming__CInput__CGamepad_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterator_1_Windows__CGaming__CInput__CGamepad_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterator_1_Windows__CGaming__CInput__CGamepad_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterator_1_Windows__CGaming__CInput__CGamepad_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterator_1_Windows__CGaming__CInput__CGamepad_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterator_1_Windows__CGaming__CInput__CGamepad_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterator<ABI::Windows::Gaming::Input::Gamepad* > methods ***/
#define __FIIterator_1_Windows__CGaming__CInput__CGamepad_get_Current(This,value) (This)->lpVtbl->get_Current(This,value)
#define __FIIterator_1_Windows__CGaming__CInput__CGamepad_get_HasCurrent(This,value) (This)->lpVtbl->get_HasCurrent(This,value)
#define __FIIterator_1_Windows__CGaming__CInput__CGamepad_MoveNext(This,value) (This)->lpVtbl->MoveNext(This,value)
#define __FIIterator_1_Windows__CGaming__CInput__CGamepad_GetMany(This,items_size,items,value) (This)->lpVtbl->GetMany(This,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterator_1_Windows__CGaming__CInput__CGamepad_QueryInterface(__FIIterator_1_Windows__CGaming__CInput__CGamepad* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterator_1_Windows__CGaming__CInput__CGamepad_AddRef(__FIIterator_1_Windows__CGaming__CInput__CGamepad* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterator_1_Windows__CGaming__CInput__CGamepad_Release(__FIIterator_1_Windows__CGaming__CInput__CGamepad* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterator_1_Windows__CGaming__CInput__CGamepad_GetIids(__FIIterator_1_Windows__CGaming__CInput__CGamepad* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterator_1_Windows__CGaming__CInput__CGamepad_GetRuntimeClassName(__FIIterator_1_Windows__CGaming__CInput__CGamepad* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterator_1_Windows__CGaming__CInput__CGamepad_GetTrustLevel(__FIIterator_1_Windows__CGaming__CInput__CGamepad* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterator<ABI::Windows::Gaming::Input::Gamepad* > methods ***/
static inline HRESULT __FIIterator_1_Windows__CGaming__CInput__CGamepad_get_Current(__FIIterator_1_Windows__CGaming__CInput__CGamepad* This,__x_ABI_CWindows_CGaming_CInput_CIGamepad **value) {
    return This->lpVtbl->get_Current(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CGaming__CInput__CGamepad_get_HasCurrent(__FIIterator_1_Windows__CGaming__CInput__CGamepad* This,boolean *value) {
    return This->lpVtbl->get_HasCurrent(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CGaming__CInput__CGamepad_MoveNext(__FIIterator_1_Windows__CGaming__CInput__CGamepad* This,boolean *value) {
    return This->lpVtbl->MoveNext(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CGaming__CInput__CGamepad_GetMany(__FIIterator_1_Windows__CGaming__CInput__CGamepad* This,UINT32 items_size,__x_ABI_CWindows_CGaming_CInput_CIGamepad **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterator_Gamepad IID___FIIterator_1_Windows__CGaming__CInput__CGamepad
#define IIterator_GamepadVtbl __FIIterator_1_Windows__CGaming__CInput__CGamepadVtbl
#define IIterator_Gamepad __FIIterator_1_Windows__CGaming__CInput__CGamepad
#define IIterator_Gamepad_QueryInterface __FIIterator_1_Windows__CGaming__CInput__CGamepad_QueryInterface
#define IIterator_Gamepad_AddRef __FIIterator_1_Windows__CGaming__CInput__CGamepad_AddRef
#define IIterator_Gamepad_Release __FIIterator_1_Windows__CGaming__CInput__CGamepad_Release
#define IIterator_Gamepad_GetIids __FIIterator_1_Windows__CGaming__CInput__CGamepad_GetIids
#define IIterator_Gamepad_GetRuntimeClassName __FIIterator_1_Windows__CGaming__CInput__CGamepad_GetRuntimeClassName
#define IIterator_Gamepad_GetTrustLevel __FIIterator_1_Windows__CGaming__CInput__CGamepad_GetTrustLevel
#define IIterator_Gamepad_get_Current __FIIterator_1_Windows__CGaming__CInput__CGamepad_get_Current
#define IIterator_Gamepad_get_HasCurrent __FIIterator_1_Windows__CGaming__CInput__CGamepad_get_HasCurrent
#define IIterator_Gamepad_MoveNext __FIIterator_1_Windows__CGaming__CInput__CGamepad_MoveNext
#define IIterator_Gamepad_GetMany __FIIterator_1_Windows__CGaming__CInput__CGamepad_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterator_1_Windows__CGaming__CInput__CGamepad_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IIterable<ABI::Windows::Gaming::Input::Gamepad* > interface
 */
#ifndef ____FIIterable_1_Windows__CGaming__CInput__CGamepad_INTERFACE_DEFINED__
#define ____FIIterable_1_Windows__CGaming__CInput__CGamepad_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterable_1_Windows__CGaming__CInput__CGamepad, 0x47132ba0, 0x6b17, 0x5cd2, 0xa8,0xbd, 0xb5,0xd3,0x44,0x3c,0xcb,0x13);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("47132ba0-6b17-5cd2-a8bd-b5d3443ccb13")
                IIterable<ABI::Windows::Gaming::Input::Gamepad* > : IIterable_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Gaming::Input::Gamepad*, ABI::Windows::Gaming::Input::IGamepad* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterable_1_Windows__CGaming__CInput__CGamepad, 0x47132ba0, 0x6b17, 0x5cd2, 0xa8,0xbd, 0xb5,0xd3,0x44,0x3c,0xcb,0x13)
#endif
#else
typedef struct __FIIterable_1_Windows__CGaming__CInput__CGamepadVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterable_1_Windows__CGaming__CInput__CGamepad *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterable_1_Windows__CGaming__CInput__CGamepad *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterable_1_Windows__CGaming__CInput__CGamepad *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterable_1_Windows__CGaming__CInput__CGamepad *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterable_1_Windows__CGaming__CInput__CGamepad *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterable_1_Windows__CGaming__CInput__CGamepad *This,
        TrustLevel *trustLevel);

    /*** IIterable<ABI::Windows::Gaming::Input::Gamepad* > methods ***/
    HRESULT (STDMETHODCALLTYPE *First)(
        __FIIterable_1_Windows__CGaming__CInput__CGamepad *This,
        __FIIterator_1_Windows__CGaming__CInput__CGamepad **value);

    END_INTERFACE
} __FIIterable_1_Windows__CGaming__CInput__CGamepadVtbl;

interface __FIIterable_1_Windows__CGaming__CInput__CGamepad {
    CONST_VTBL __FIIterable_1_Windows__CGaming__CInput__CGamepadVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterable_1_Windows__CGaming__CInput__CGamepad_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterable_1_Windows__CGaming__CInput__CGamepad_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterable_1_Windows__CGaming__CInput__CGamepad_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterable_1_Windows__CGaming__CInput__CGamepad_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterable_1_Windows__CGaming__CInput__CGamepad_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterable_1_Windows__CGaming__CInput__CGamepad_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterable<ABI::Windows::Gaming::Input::Gamepad* > methods ***/
#define __FIIterable_1_Windows__CGaming__CInput__CGamepad_First(This,value) (This)->lpVtbl->First(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterable_1_Windows__CGaming__CInput__CGamepad_QueryInterface(__FIIterable_1_Windows__CGaming__CInput__CGamepad* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterable_1_Windows__CGaming__CInput__CGamepad_AddRef(__FIIterable_1_Windows__CGaming__CInput__CGamepad* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterable_1_Windows__CGaming__CInput__CGamepad_Release(__FIIterable_1_Windows__CGaming__CInput__CGamepad* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterable_1_Windows__CGaming__CInput__CGamepad_GetIids(__FIIterable_1_Windows__CGaming__CInput__CGamepad* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterable_1_Windows__CGaming__CInput__CGamepad_GetRuntimeClassName(__FIIterable_1_Windows__CGaming__CInput__CGamepad* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterable_1_Windows__CGaming__CInput__CGamepad_GetTrustLevel(__FIIterable_1_Windows__CGaming__CInput__CGamepad* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterable<ABI::Windows::Gaming::Input::Gamepad* > methods ***/
static inline HRESULT __FIIterable_1_Windows__CGaming__CInput__CGamepad_First(__FIIterable_1_Windows__CGaming__CInput__CGamepad* This,__FIIterator_1_Windows__CGaming__CInput__CGamepad **value) {
    return This->lpVtbl->First(This,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterable_Gamepad IID___FIIterable_1_Windows__CGaming__CInput__CGamepad
#define IIterable_GamepadVtbl __FIIterable_1_Windows__CGaming__CInput__CGamepadVtbl
#define IIterable_Gamepad __FIIterable_1_Windows__CGaming__CInput__CGamepad
#define IIterable_Gamepad_QueryInterface __FIIterable_1_Windows__CGaming__CInput__CGamepad_QueryInterface
#define IIterable_Gamepad_AddRef __FIIterable_1_Windows__CGaming__CInput__CGamepad_AddRef
#define IIterable_Gamepad_Release __FIIterable_1_Windows__CGaming__CInput__CGamepad_Release
#define IIterable_Gamepad_GetIids __FIIterable_1_Windows__CGaming__CInput__CGamepad_GetIids
#define IIterable_Gamepad_GetRuntimeClassName __FIIterable_1_Windows__CGaming__CInput__CGamepad_GetRuntimeClassName
#define IIterable_Gamepad_GetTrustLevel __FIIterable_1_Windows__CGaming__CInput__CGamepad_GetTrustLevel
#define IIterable_Gamepad_First __FIIterable_1_Windows__CGaming__CInput__CGamepad_First
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterable_1_Windows__CGaming__CInput__CGamepad_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVectorView<ABI::Windows::Gaming::Input::Gamepad* > interface
 */
#ifndef ____FIVectorView_1_Windows__CGaming__CInput__CGamepad_INTERFACE_DEFINED__
#define ____FIVectorView_1_Windows__CGaming__CInput__CGamepad_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIVectorView_1_Windows__CGaming__CInput__CGamepad, 0xeb97bb69, 0x09c9, 0x5a99, 0x86,0xb2, 0x3e,0x36,0x08,0x52,0x84,0xd4);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("eb97bb69-09c9-5a99-86b2-3e36085284d4")
                IVectorView<ABI::Windows::Gaming::Input::Gamepad* > : IVectorView_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Gaming::Input::Gamepad*, ABI::Windows::Gaming::Input::IGamepad* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIVectorView_1_Windows__CGaming__CInput__CGamepad, 0xeb97bb69, 0x09c9, 0x5a99, 0x86,0xb2, 0x3e,0x36,0x08,0x52,0x84,0xd4)
#endif
#else
typedef struct __FIVectorView_1_Windows__CGaming__CInput__CGamepadVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIVectorView_1_Windows__CGaming__CInput__CGamepad *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIVectorView_1_Windows__CGaming__CInput__CGamepad *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIVectorView_1_Windows__CGaming__CInput__CGamepad *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIVectorView_1_Windows__CGaming__CInput__CGamepad *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIVectorView_1_Windows__CGaming__CInput__CGamepad *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIVectorView_1_Windows__CGaming__CInput__CGamepad *This,
        TrustLevel *trustLevel);

    /*** IVectorView<ABI::Windows::Gaming::Input::Gamepad* > methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAt)(
        __FIVectorView_1_Windows__CGaming__CInput__CGamepad *This,
        UINT32 index,
        __x_ABI_CWindows_CGaming_CInput_CIGamepad **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIVectorView_1_Windows__CGaming__CInput__CGamepad *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *IndexOf)(
        __FIVectorView_1_Windows__CGaming__CInput__CGamepad *This,
        __x_ABI_CWindows_CGaming_CInput_CIGamepad *element,
        UINT32 *index,
        BOOLEAN *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIVectorView_1_Windows__CGaming__CInput__CGamepad *This,
        UINT32 start_index,
        UINT32 items_size,
        __x_ABI_CWindows_CGaming_CInput_CIGamepad **items,
        UINT32 *value);

    END_INTERFACE
} __FIVectorView_1_Windows__CGaming__CInput__CGamepadVtbl;

interface __FIVectorView_1_Windows__CGaming__CInput__CGamepad {
    CONST_VTBL __FIVectorView_1_Windows__CGaming__CInput__CGamepadVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIVectorView_1_Windows__CGaming__CInput__CGamepad_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIVectorView_1_Windows__CGaming__CInput__CGamepad_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIVectorView_1_Windows__CGaming__CInput__CGamepad_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIVectorView_1_Windows__CGaming__CInput__CGamepad_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIVectorView_1_Windows__CGaming__CInput__CGamepad_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIVectorView_1_Windows__CGaming__CInput__CGamepad_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IVectorView<ABI::Windows::Gaming::Input::Gamepad* > methods ***/
#define __FIVectorView_1_Windows__CGaming__CInput__CGamepad_GetAt(This,index,value) (This)->lpVtbl->GetAt(This,index,value)
#define __FIVectorView_1_Windows__CGaming__CInput__CGamepad_get_Size(This,value) (This)->lpVtbl->get_Size(This,value)
#define __FIVectorView_1_Windows__CGaming__CInput__CGamepad_IndexOf(This,element,index,value) (This)->lpVtbl->IndexOf(This,element,index,value)
#define __FIVectorView_1_Windows__CGaming__CInput__CGamepad_GetMany(This,start_index,items_size,items,value) (This)->lpVtbl->GetMany(This,start_index,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIVectorView_1_Windows__CGaming__CInput__CGamepad_QueryInterface(__FIVectorView_1_Windows__CGaming__CInput__CGamepad* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIVectorView_1_Windows__CGaming__CInput__CGamepad_AddRef(__FIVectorView_1_Windows__CGaming__CInput__CGamepad* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIVectorView_1_Windows__CGaming__CInput__CGamepad_Release(__FIVectorView_1_Windows__CGaming__CInput__CGamepad* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIVectorView_1_Windows__CGaming__CInput__CGamepad_GetIids(__FIVectorView_1_Windows__CGaming__CInput__CGamepad* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIVectorView_1_Windows__CGaming__CInput__CGamepad_GetRuntimeClassName(__FIVectorView_1_Windows__CGaming__CInput__CGamepad* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIVectorView_1_Windows__CGaming__CInput__CGamepad_GetTrustLevel(__FIVectorView_1_Windows__CGaming__CInput__CGamepad* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IVectorView<ABI::Windows::Gaming::Input::Gamepad* > methods ***/
static inline HRESULT __FIVectorView_1_Windows__CGaming__CInput__CGamepad_GetAt(__FIVectorView_1_Windows__CGaming__CInput__CGamepad* This,UINT32 index,__x_ABI_CWindows_CGaming_CInput_CIGamepad **value) {
    return This->lpVtbl->GetAt(This,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CGaming__CInput__CGamepad_get_Size(__FIVectorView_1_Windows__CGaming__CInput__CGamepad* This,UINT32 *value) {
    return This->lpVtbl->get_Size(This,value);
}
static inline HRESULT __FIVectorView_1_Windows__CGaming__CInput__CGamepad_IndexOf(__FIVectorView_1_Windows__CGaming__CInput__CGamepad* This,__x_ABI_CWindows_CGaming_CInput_CIGamepad *element,UINT32 *index,BOOLEAN *value) {
    return This->lpVtbl->IndexOf(This,element,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CGaming__CInput__CGamepad_GetMany(__FIVectorView_1_Windows__CGaming__CInput__CGamepad* This,UINT32 start_index,UINT32 items_size,__x_ABI_CWindows_CGaming_CInput_CIGamepad **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,start_index,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IVectorView_Gamepad IID___FIVectorView_1_Windows__CGaming__CInput__CGamepad
#define IVectorView_GamepadVtbl __FIVectorView_1_Windows__CGaming__CInput__CGamepadVtbl
#define IVectorView_Gamepad __FIVectorView_1_Windows__CGaming__CInput__CGamepad
#define IVectorView_Gamepad_QueryInterface __FIVectorView_1_Windows__CGaming__CInput__CGamepad_QueryInterface
#define IVectorView_Gamepad_AddRef __FIVectorView_1_Windows__CGaming__CInput__CGamepad_AddRef
#define IVectorView_Gamepad_Release __FIVectorView_1_Windows__CGaming__CInput__CGamepad_Release
#define IVectorView_Gamepad_GetIids __FIVectorView_1_Windows__CGaming__CInput__CGamepad_GetIids
#define IVectorView_Gamepad_GetRuntimeClassName __FIVectorView_1_Windows__CGaming__CInput__CGamepad_GetRuntimeClassName
#define IVectorView_Gamepad_GetTrustLevel __FIVectorView_1_Windows__CGaming__CInput__CGamepad_GetTrustLevel
#define IVectorView_Gamepad_GetAt __FIVectorView_1_Windows__CGaming__CInput__CGamepad_GetAt
#define IVectorView_Gamepad_get_Size __FIVectorView_1_Windows__CGaming__CInput__CGamepad_get_Size
#define IVectorView_Gamepad_IndexOf __FIVectorView_1_Windows__CGaming__CInput__CGamepad_IndexOf
#define IVectorView_Gamepad_GetMany __FIVectorView_1_Windows__CGaming__CInput__CGamepad_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIVectorView_1_Windows__CGaming__CInput__CGamepad_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVector<ABI::Windows::Gaming::Input::Gamepad* > interface
 */
#ifndef ____FIVector_1_Windows__CGaming__CInput__CGamepad_INTERFACE_DEFINED__
#define ____FIVector_1_Windows__CGaming__CInput__CGamepad_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIVector_1_Windows__CGaming__CInput__CGamepad, 0x152bec39, 0x0a47, 0x5466, 0xb2,0x53, 0x64,0xe2,0xbb,0x68,0xd7,0x44);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("152bec39-0a47-5466-b253-64e2bb68d744")
                IVector<ABI::Windows::Gaming::Input::Gamepad* > : IVector_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Gaming::Input::Gamepad*, ABI::Windows::Gaming::Input::IGamepad* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIVector_1_Windows__CGaming__CInput__CGamepad, 0x152bec39, 0x0a47, 0x5466, 0xb2,0x53, 0x64,0xe2,0xbb,0x68,0xd7,0x44)
#endif
#else
typedef struct __FIVector_1_Windows__CGaming__CInput__CGamepadVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIVector_1_Windows__CGaming__CInput__CGamepad *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIVector_1_Windows__CGaming__CInput__CGamepad *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIVector_1_Windows__CGaming__CInput__CGamepad *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIVector_1_Windows__CGaming__CInput__CGamepad *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIVector_1_Windows__CGaming__CInput__CGamepad *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIVector_1_Windows__CGaming__CInput__CGamepad *This,
        TrustLevel *trustLevel);

    /*** IVector<ABI::Windows::Gaming::Input::Gamepad* > methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAt)(
        __FIVector_1_Windows__CGaming__CInput__CGamepad *This,
        UINT32 index,
        __x_ABI_CWindows_CGaming_CInput_CIGamepad **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIVector_1_Windows__CGaming__CInput__CGamepad *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *GetView)(
        __FIVector_1_Windows__CGaming__CInput__CGamepad *This,
        __FIVectorView_1_Windows__CGaming__CInput__CGamepad **value);

    HRESULT (STDMETHODCALLTYPE *IndexOf)(
        __FIVector_1_Windows__CGaming__CInput__CGamepad *This,
        __x_ABI_CWindows_CGaming_CInput_CIGamepad *element,
        UINT32 *index,
        BOOLEAN *value);

    HRESULT (STDMETHODCALLTYPE *SetAt)(
        __FIVector_1_Windows__CGaming__CInput__CGamepad *This,
        UINT32 index,
        __x_ABI_CWindows_CGaming_CInput_CIGamepad *value);

    HRESULT (STDMETHODCALLTYPE *InsertAt)(
        __FIVector_1_Windows__CGaming__CInput__CGamepad *This,
        UINT32 index,
        __x_ABI_CWindows_CGaming_CInput_CIGamepad *value);

    HRESULT (STDMETHODCALLTYPE *RemoveAt)(
        __FIVector_1_Windows__CGaming__CInput__CGamepad *This,
        UINT32 index);

    HRESULT (STDMETHODCALLTYPE *Append)(
        __FIVector_1_Windows__CGaming__CInput__CGamepad *This,
        __x_ABI_CWindows_CGaming_CInput_CIGamepad *value);

    HRESULT (STDMETHODCALLTYPE *RemoveAtEnd)(
        __FIVector_1_Windows__CGaming__CInput__CGamepad *This);

    HRESULT (STDMETHODCALLTYPE *Clear)(
        __FIVector_1_Windows__CGaming__CInput__CGamepad *This);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIVector_1_Windows__CGaming__CInput__CGamepad *This,
        UINT32 start_index,
        UINT32 items_size,
        __x_ABI_CWindows_CGaming_CInput_CIGamepad **items,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *ReplaceAll)(
        __FIVector_1_Windows__CGaming__CInput__CGamepad *This,
        UINT32 count,
        __x_ABI_CWindows_CGaming_CInput_CIGamepad **items);

    END_INTERFACE
} __FIVector_1_Windows__CGaming__CInput__CGamepadVtbl;

interface __FIVector_1_Windows__CGaming__CInput__CGamepad {
    CONST_VTBL __FIVector_1_Windows__CGaming__CInput__CGamepadVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIVector_1_Windows__CGaming__CInput__CGamepad_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIVector_1_Windows__CGaming__CInput__CGamepad_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIVector_1_Windows__CGaming__CInput__CGamepad_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIVector_1_Windows__CGaming__CInput__CGamepad_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIVector_1_Windows__CGaming__CInput__CGamepad_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIVector_1_Windows__CGaming__CInput__CGamepad_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IVector<ABI::Windows::Gaming::Input::Gamepad* > methods ***/
#define __FIVector_1_Windows__CGaming__CInput__CGamepad_GetAt(This,index,value) (This)->lpVtbl->GetAt(This,index,value)
#define __FIVector_1_Windows__CGaming__CInput__CGamepad_get_Size(This,value) (This)->lpVtbl->get_Size(This,value)
#define __FIVector_1_Windows__CGaming__CInput__CGamepad_GetView(This,value) (This)->lpVtbl->GetView(This,value)
#define __FIVector_1_Windows__CGaming__CInput__CGamepad_IndexOf(This,element,index,value) (This)->lpVtbl->IndexOf(This,element,index,value)
#define __FIVector_1_Windows__CGaming__CInput__CGamepad_SetAt(This,index,value) (This)->lpVtbl->SetAt(This,index,value)
#define __FIVector_1_Windows__CGaming__CInput__CGamepad_InsertAt(This,index,value) (This)->lpVtbl->InsertAt(This,index,value)
#define __FIVector_1_Windows__CGaming__CInput__CGamepad_RemoveAt(This,index) (This)->lpVtbl->RemoveAt(This,index)
#define __FIVector_1_Windows__CGaming__CInput__CGamepad_Append(This,value) (This)->lpVtbl->Append(This,value)
#define __FIVector_1_Windows__CGaming__CInput__CGamepad_RemoveAtEnd(This) (This)->lpVtbl->RemoveAtEnd(This)
#define __FIVector_1_Windows__CGaming__CInput__CGamepad_Clear(This) (This)->lpVtbl->Clear(This)
#define __FIVector_1_Windows__CGaming__CInput__CGamepad_GetMany(This,start_index,items_size,items,value) (This)->lpVtbl->GetMany(This,start_index,items_size,items,value)
#define __FIVector_1_Windows__CGaming__CInput__CGamepad_ReplaceAll(This,count,items) (This)->lpVtbl->ReplaceAll(This,count,items)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIVector_1_Windows__CGaming__CInput__CGamepad_QueryInterface(__FIVector_1_Windows__CGaming__CInput__CGamepad* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIVector_1_Windows__CGaming__CInput__CGamepad_AddRef(__FIVector_1_Windows__CGaming__CInput__CGamepad* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIVector_1_Windows__CGaming__CInput__CGamepad_Release(__FIVector_1_Windows__CGaming__CInput__CGamepad* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIVector_1_Windows__CGaming__CInput__CGamepad_GetIids(__FIVector_1_Windows__CGaming__CInput__CGamepad* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIVector_1_Windows__CGaming__CInput__CGamepad_GetRuntimeClassName(__FIVector_1_Windows__CGaming__CInput__CGamepad* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIVector_1_Windows__CGaming__CInput__CGamepad_GetTrustLevel(__FIVector_1_Windows__CGaming__CInput__CGamepad* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IVector<ABI::Windows::Gaming::Input::Gamepad* > methods ***/
static inline HRESULT __FIVector_1_Windows__CGaming__CInput__CGamepad_GetAt(__FIVector_1_Windows__CGaming__CInput__CGamepad* This,UINT32 index,__x_ABI_CWindows_CGaming_CInput_CIGamepad **value) {
    return This->lpVtbl->GetAt(This,index,value);
}
static inline HRESULT __FIVector_1_Windows__CGaming__CInput__CGamepad_get_Size(__FIVector_1_Windows__CGaming__CInput__CGamepad* This,UINT32 *value) {
    return This->lpVtbl->get_Size(This,value);
}
static inline HRESULT __FIVector_1_Windows__CGaming__CInput__CGamepad_GetView(__FIVector_1_Windows__CGaming__CInput__CGamepad* This,__FIVectorView_1_Windows__CGaming__CInput__CGamepad **value) {
    return This->lpVtbl->GetView(This,value);
}
static inline HRESULT __FIVector_1_Windows__CGaming__CInput__CGamepad_IndexOf(__FIVector_1_Windows__CGaming__CInput__CGamepad* This,__x_ABI_CWindows_CGaming_CInput_CIGamepad *element,UINT32 *index,BOOLEAN *value) {
    return This->lpVtbl->IndexOf(This,element,index,value);
}
static inline HRESULT __FIVector_1_Windows__CGaming__CInput__CGamepad_SetAt(__FIVector_1_Windows__CGaming__CInput__CGamepad* This,UINT32 index,__x_ABI_CWindows_CGaming_CInput_CIGamepad *value) {
    return This->lpVtbl->SetAt(This,index,value);
}
static inline HRESULT __FIVector_1_Windows__CGaming__CInput__CGamepad_InsertAt(__FIVector_1_Windows__CGaming__CInput__CGamepad* This,UINT32 index,__x_ABI_CWindows_CGaming_CInput_CIGamepad *value) {
    return This->lpVtbl->InsertAt(This,index,value);
}
static inline HRESULT __FIVector_1_Windows__CGaming__CInput__CGamepad_RemoveAt(__FIVector_1_Windows__CGaming__CInput__CGamepad* This,UINT32 index) {
    return This->lpVtbl->RemoveAt(This,index);
}
static inline HRESULT __FIVector_1_Windows__CGaming__CInput__CGamepad_Append(__FIVector_1_Windows__CGaming__CInput__CGamepad* This,__x_ABI_CWindows_CGaming_CInput_CIGamepad *value) {
    return This->lpVtbl->Append(This,value);
}
static inline HRESULT __FIVector_1_Windows__CGaming__CInput__CGamepad_RemoveAtEnd(__FIVector_1_Windows__CGaming__CInput__CGamepad* This) {
    return This->lpVtbl->RemoveAtEnd(This);
}
static inline HRESULT __FIVector_1_Windows__CGaming__CInput__CGamepad_Clear(__FIVector_1_Windows__CGaming__CInput__CGamepad* This) {
    return This->lpVtbl->Clear(This);
}
static inline HRESULT __FIVector_1_Windows__CGaming__CInput__CGamepad_GetMany(__FIVector_1_Windows__CGaming__CInput__CGamepad* This,UINT32 start_index,UINT32 items_size,__x_ABI_CWindows_CGaming_CInput_CIGamepad **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,start_index,items_size,items,value);
}
static inline HRESULT __FIVector_1_Windows__CGaming__CInput__CGamepad_ReplaceAll(__FIVector_1_Windows__CGaming__CInput__CGamepad* This,UINT32 count,__x_ABI_CWindows_CGaming_CInput_CIGamepad **items) {
    return This->lpVtbl->ReplaceAll(This,count,items);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IVector_Gamepad IID___FIVector_1_Windows__CGaming__CInput__CGamepad
#define IVector_GamepadVtbl __FIVector_1_Windows__CGaming__CInput__CGamepadVtbl
#define IVector_Gamepad __FIVector_1_Windows__CGaming__CInput__CGamepad
#define IVector_Gamepad_QueryInterface __FIVector_1_Windows__CGaming__CInput__CGamepad_QueryInterface
#define IVector_Gamepad_AddRef __FIVector_1_Windows__CGaming__CInput__CGamepad_AddRef
#define IVector_Gamepad_Release __FIVector_1_Windows__CGaming__CInput__CGamepad_Release
#define IVector_Gamepad_GetIids __FIVector_1_Windows__CGaming__CInput__CGamepad_GetIids
#define IVector_Gamepad_GetRuntimeClassName __FIVector_1_Windows__CGaming__CInput__CGamepad_GetRuntimeClassName
#define IVector_Gamepad_GetTrustLevel __FIVector_1_Windows__CGaming__CInput__CGamepad_GetTrustLevel
#define IVector_Gamepad_GetAt __FIVector_1_Windows__CGaming__CInput__CGamepad_GetAt
#define IVector_Gamepad_get_Size __FIVector_1_Windows__CGaming__CInput__CGamepad_get_Size
#define IVector_Gamepad_GetView __FIVector_1_Windows__CGaming__CInput__CGamepad_GetView
#define IVector_Gamepad_IndexOf __FIVector_1_Windows__CGaming__CInput__CGamepad_IndexOf
#define IVector_Gamepad_SetAt __FIVector_1_Windows__CGaming__CInput__CGamepad_SetAt
#define IVector_Gamepad_InsertAt __FIVector_1_Windows__CGaming__CInput__CGamepad_InsertAt
#define IVector_Gamepad_RemoveAt __FIVector_1_Windows__CGaming__CInput__CGamepad_RemoveAt
#define IVector_Gamepad_Append __FIVector_1_Windows__CGaming__CInput__CGamepad_Append
#define IVector_Gamepad_RemoveAtEnd __FIVector_1_Windows__CGaming__CInput__CGamepad_RemoveAtEnd
#define IVector_Gamepad_Clear __FIVector_1_Windows__CGaming__CInput__CGamepad_Clear
#define IVector_Gamepad_GetMany __FIVector_1_Windows__CGaming__CInput__CGamepad_GetMany
#define IVector_Gamepad_ReplaceAll __FIVector_1_Windows__CGaming__CInput__CGamepad_ReplaceAll
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIVector_1_Windows__CGaming__CInput__CGamepad_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IIterator<ABI::Windows::Gaming::Input::RacingWheel* > interface
 */
#ifndef ____FIIterator_1_Windows__CGaming__CInput__CRacingWheel_INTERFACE_DEFINED__
#define ____FIIterator_1_Windows__CGaming__CInput__CRacingWheel_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterator_1_Windows__CGaming__CInput__CRacingWheel, 0x23d735b8, 0x4d36, 0x5377, 0xa2,0x45, 0x69,0xdf,0x97,0xc9,0xfc,0xd9);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("23d735b8-4d36-5377-a245-69df97c9fcd9")
                IIterator<ABI::Windows::Gaming::Input::RacingWheel* > : IIterator_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Gaming::Input::RacingWheel*, ABI::Windows::Gaming::Input::IRacingWheel* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterator_1_Windows__CGaming__CInput__CRacingWheel, 0x23d735b8, 0x4d36, 0x5377, 0xa2,0x45, 0x69,0xdf,0x97,0xc9,0xfc,0xd9)
#endif
#else
typedef struct __FIIterator_1_Windows__CGaming__CInput__CRacingWheelVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterator_1_Windows__CGaming__CInput__CRacingWheel *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterator_1_Windows__CGaming__CInput__CRacingWheel *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterator_1_Windows__CGaming__CInput__CRacingWheel *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterator_1_Windows__CGaming__CInput__CRacingWheel *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterator_1_Windows__CGaming__CInput__CRacingWheel *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterator_1_Windows__CGaming__CInput__CRacingWheel *This,
        TrustLevel *trustLevel);

    /*** IIterator<ABI::Windows::Gaming::Input::RacingWheel* > methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Current)(
        __FIIterator_1_Windows__CGaming__CInput__CRacingWheel *This,
        __x_ABI_CWindows_CGaming_CInput_CIRacingWheel **value);

    HRESULT (STDMETHODCALLTYPE *get_HasCurrent)(
        __FIIterator_1_Windows__CGaming__CInput__CRacingWheel *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *MoveNext)(
        __FIIterator_1_Windows__CGaming__CInput__CRacingWheel *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIIterator_1_Windows__CGaming__CInput__CRacingWheel *This,
        UINT32 items_size,
        __x_ABI_CWindows_CGaming_CInput_CIRacingWheel **items,
        UINT32 *value);

    END_INTERFACE
} __FIIterator_1_Windows__CGaming__CInput__CRacingWheelVtbl;

interface __FIIterator_1_Windows__CGaming__CInput__CRacingWheel {
    CONST_VTBL __FIIterator_1_Windows__CGaming__CInput__CRacingWheelVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterator_1_Windows__CGaming__CInput__CRacingWheel_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterator_1_Windows__CGaming__CInput__CRacingWheel_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterator_1_Windows__CGaming__CInput__CRacingWheel_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterator_1_Windows__CGaming__CInput__CRacingWheel_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterator_1_Windows__CGaming__CInput__CRacingWheel_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterator_1_Windows__CGaming__CInput__CRacingWheel_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterator<ABI::Windows::Gaming::Input::RacingWheel* > methods ***/
#define __FIIterator_1_Windows__CGaming__CInput__CRacingWheel_get_Current(This,value) (This)->lpVtbl->get_Current(This,value)
#define __FIIterator_1_Windows__CGaming__CInput__CRacingWheel_get_HasCurrent(This,value) (This)->lpVtbl->get_HasCurrent(This,value)
#define __FIIterator_1_Windows__CGaming__CInput__CRacingWheel_MoveNext(This,value) (This)->lpVtbl->MoveNext(This,value)
#define __FIIterator_1_Windows__CGaming__CInput__CRacingWheel_GetMany(This,items_size,items,value) (This)->lpVtbl->GetMany(This,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterator_1_Windows__CGaming__CInput__CRacingWheel_QueryInterface(__FIIterator_1_Windows__CGaming__CInput__CRacingWheel* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterator_1_Windows__CGaming__CInput__CRacingWheel_AddRef(__FIIterator_1_Windows__CGaming__CInput__CRacingWheel* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterator_1_Windows__CGaming__CInput__CRacingWheel_Release(__FIIterator_1_Windows__CGaming__CInput__CRacingWheel* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterator_1_Windows__CGaming__CInput__CRacingWheel_GetIids(__FIIterator_1_Windows__CGaming__CInput__CRacingWheel* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterator_1_Windows__CGaming__CInput__CRacingWheel_GetRuntimeClassName(__FIIterator_1_Windows__CGaming__CInput__CRacingWheel* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterator_1_Windows__CGaming__CInput__CRacingWheel_GetTrustLevel(__FIIterator_1_Windows__CGaming__CInput__CRacingWheel* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterator<ABI::Windows::Gaming::Input::RacingWheel* > methods ***/
static inline HRESULT __FIIterator_1_Windows__CGaming__CInput__CRacingWheel_get_Current(__FIIterator_1_Windows__CGaming__CInput__CRacingWheel* This,__x_ABI_CWindows_CGaming_CInput_CIRacingWheel **value) {
    return This->lpVtbl->get_Current(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CGaming__CInput__CRacingWheel_get_HasCurrent(__FIIterator_1_Windows__CGaming__CInput__CRacingWheel* This,boolean *value) {
    return This->lpVtbl->get_HasCurrent(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CGaming__CInput__CRacingWheel_MoveNext(__FIIterator_1_Windows__CGaming__CInput__CRacingWheel* This,boolean *value) {
    return This->lpVtbl->MoveNext(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CGaming__CInput__CRacingWheel_GetMany(__FIIterator_1_Windows__CGaming__CInput__CRacingWheel* This,UINT32 items_size,__x_ABI_CWindows_CGaming_CInput_CIRacingWheel **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterator_RacingWheel IID___FIIterator_1_Windows__CGaming__CInput__CRacingWheel
#define IIterator_RacingWheelVtbl __FIIterator_1_Windows__CGaming__CInput__CRacingWheelVtbl
#define IIterator_RacingWheel __FIIterator_1_Windows__CGaming__CInput__CRacingWheel
#define IIterator_RacingWheel_QueryInterface __FIIterator_1_Windows__CGaming__CInput__CRacingWheel_QueryInterface
#define IIterator_RacingWheel_AddRef __FIIterator_1_Windows__CGaming__CInput__CRacingWheel_AddRef
#define IIterator_RacingWheel_Release __FIIterator_1_Windows__CGaming__CInput__CRacingWheel_Release
#define IIterator_RacingWheel_GetIids __FIIterator_1_Windows__CGaming__CInput__CRacingWheel_GetIids
#define IIterator_RacingWheel_GetRuntimeClassName __FIIterator_1_Windows__CGaming__CInput__CRacingWheel_GetRuntimeClassName
#define IIterator_RacingWheel_GetTrustLevel __FIIterator_1_Windows__CGaming__CInput__CRacingWheel_GetTrustLevel
#define IIterator_RacingWheel_get_Current __FIIterator_1_Windows__CGaming__CInput__CRacingWheel_get_Current
#define IIterator_RacingWheel_get_HasCurrent __FIIterator_1_Windows__CGaming__CInput__CRacingWheel_get_HasCurrent
#define IIterator_RacingWheel_MoveNext __FIIterator_1_Windows__CGaming__CInput__CRacingWheel_MoveNext
#define IIterator_RacingWheel_GetMany __FIIterator_1_Windows__CGaming__CInput__CRacingWheel_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterator_1_Windows__CGaming__CInput__CRacingWheel_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IIterable<ABI::Windows::Gaming::Input::RacingWheel* > interface
 */
#ifndef ____FIIterable_1_Windows__CGaming__CInput__CRacingWheel_INTERFACE_DEFINED__
#define ____FIIterable_1_Windows__CGaming__CInput__CRacingWheel_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterable_1_Windows__CGaming__CInput__CRacingWheel, 0x9a7c3830, 0x9a87, 0x5287, 0xa1,0xe2, 0x8a,0x2a,0xf2,0x9c,0xf6,0x8c);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("9a7c3830-9a87-5287-a1e2-8a2af29cf68c")
                IIterable<ABI::Windows::Gaming::Input::RacingWheel* > : IIterable_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Gaming::Input::RacingWheel*, ABI::Windows::Gaming::Input::IRacingWheel* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterable_1_Windows__CGaming__CInput__CRacingWheel, 0x9a7c3830, 0x9a87, 0x5287, 0xa1,0xe2, 0x8a,0x2a,0xf2,0x9c,0xf6,0x8c)
#endif
#else
typedef struct __FIIterable_1_Windows__CGaming__CInput__CRacingWheelVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterable_1_Windows__CGaming__CInput__CRacingWheel *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterable_1_Windows__CGaming__CInput__CRacingWheel *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterable_1_Windows__CGaming__CInput__CRacingWheel *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterable_1_Windows__CGaming__CInput__CRacingWheel *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterable_1_Windows__CGaming__CInput__CRacingWheel *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterable_1_Windows__CGaming__CInput__CRacingWheel *This,
        TrustLevel *trustLevel);

    /*** IIterable<ABI::Windows::Gaming::Input::RacingWheel* > methods ***/
    HRESULT (STDMETHODCALLTYPE *First)(
        __FIIterable_1_Windows__CGaming__CInput__CRacingWheel *This,
        __FIIterator_1_Windows__CGaming__CInput__CRacingWheel **value);

    END_INTERFACE
} __FIIterable_1_Windows__CGaming__CInput__CRacingWheelVtbl;

interface __FIIterable_1_Windows__CGaming__CInput__CRacingWheel {
    CONST_VTBL __FIIterable_1_Windows__CGaming__CInput__CRacingWheelVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterable_1_Windows__CGaming__CInput__CRacingWheel_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterable_1_Windows__CGaming__CInput__CRacingWheel_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterable_1_Windows__CGaming__CInput__CRacingWheel_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterable_1_Windows__CGaming__CInput__CRacingWheel_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterable_1_Windows__CGaming__CInput__CRacingWheel_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterable_1_Windows__CGaming__CInput__CRacingWheel_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterable<ABI::Windows::Gaming::Input::RacingWheel* > methods ***/
#define __FIIterable_1_Windows__CGaming__CInput__CRacingWheel_First(This,value) (This)->lpVtbl->First(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterable_1_Windows__CGaming__CInput__CRacingWheel_QueryInterface(__FIIterable_1_Windows__CGaming__CInput__CRacingWheel* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterable_1_Windows__CGaming__CInput__CRacingWheel_AddRef(__FIIterable_1_Windows__CGaming__CInput__CRacingWheel* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterable_1_Windows__CGaming__CInput__CRacingWheel_Release(__FIIterable_1_Windows__CGaming__CInput__CRacingWheel* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterable_1_Windows__CGaming__CInput__CRacingWheel_GetIids(__FIIterable_1_Windows__CGaming__CInput__CRacingWheel* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterable_1_Windows__CGaming__CInput__CRacingWheel_GetRuntimeClassName(__FIIterable_1_Windows__CGaming__CInput__CRacingWheel* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterable_1_Windows__CGaming__CInput__CRacingWheel_GetTrustLevel(__FIIterable_1_Windows__CGaming__CInput__CRacingWheel* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterable<ABI::Windows::Gaming::Input::RacingWheel* > methods ***/
static inline HRESULT __FIIterable_1_Windows__CGaming__CInput__CRacingWheel_First(__FIIterable_1_Windows__CGaming__CInput__CRacingWheel* This,__FIIterator_1_Windows__CGaming__CInput__CRacingWheel **value) {
    return This->lpVtbl->First(This,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterable_RacingWheel IID___FIIterable_1_Windows__CGaming__CInput__CRacingWheel
#define IIterable_RacingWheelVtbl __FIIterable_1_Windows__CGaming__CInput__CRacingWheelVtbl
#define IIterable_RacingWheel __FIIterable_1_Windows__CGaming__CInput__CRacingWheel
#define IIterable_RacingWheel_QueryInterface __FIIterable_1_Windows__CGaming__CInput__CRacingWheel_QueryInterface
#define IIterable_RacingWheel_AddRef __FIIterable_1_Windows__CGaming__CInput__CRacingWheel_AddRef
#define IIterable_RacingWheel_Release __FIIterable_1_Windows__CGaming__CInput__CRacingWheel_Release
#define IIterable_RacingWheel_GetIids __FIIterable_1_Windows__CGaming__CInput__CRacingWheel_GetIids
#define IIterable_RacingWheel_GetRuntimeClassName __FIIterable_1_Windows__CGaming__CInput__CRacingWheel_GetRuntimeClassName
#define IIterable_RacingWheel_GetTrustLevel __FIIterable_1_Windows__CGaming__CInput__CRacingWheel_GetTrustLevel
#define IIterable_RacingWheel_First __FIIterable_1_Windows__CGaming__CInput__CRacingWheel_First
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterable_1_Windows__CGaming__CInput__CRacingWheel_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVectorView<ABI::Windows::Gaming::Input::RacingWheel* > interface
 */
#ifndef ____FIVectorView_1_Windows__CGaming__CInput__CRacingWheel_INTERFACE_DEFINED__
#define ____FIVectorView_1_Windows__CGaming__CInput__CRacingWheel_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIVectorView_1_Windows__CGaming__CInput__CRacingWheel, 0x153993b2, 0x6052, 0x5959, 0x91,0xec, 0x90,0x0c,0x53,0xfe,0xf1,0x20);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("153993b2-6052-5959-91ec-900c53fef120")
                IVectorView<ABI::Windows::Gaming::Input::RacingWheel* > : IVectorView_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Gaming::Input::RacingWheel*, ABI::Windows::Gaming::Input::IRacingWheel* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIVectorView_1_Windows__CGaming__CInput__CRacingWheel, 0x153993b2, 0x6052, 0x5959, 0x91,0xec, 0x90,0x0c,0x53,0xfe,0xf1,0x20)
#endif
#else
typedef struct __FIVectorView_1_Windows__CGaming__CInput__CRacingWheelVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIVectorView_1_Windows__CGaming__CInput__CRacingWheel *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIVectorView_1_Windows__CGaming__CInput__CRacingWheel *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIVectorView_1_Windows__CGaming__CInput__CRacingWheel *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIVectorView_1_Windows__CGaming__CInput__CRacingWheel *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIVectorView_1_Windows__CGaming__CInput__CRacingWheel *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIVectorView_1_Windows__CGaming__CInput__CRacingWheel *This,
        TrustLevel *trustLevel);

    /*** IVectorView<ABI::Windows::Gaming::Input::RacingWheel* > methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAt)(
        __FIVectorView_1_Windows__CGaming__CInput__CRacingWheel *This,
        UINT32 index,
        __x_ABI_CWindows_CGaming_CInput_CIRacingWheel **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIVectorView_1_Windows__CGaming__CInput__CRacingWheel *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *IndexOf)(
        __FIVectorView_1_Windows__CGaming__CInput__CRacingWheel *This,
        __x_ABI_CWindows_CGaming_CInput_CIRacingWheel *element,
        UINT32 *index,
        BOOLEAN *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIVectorView_1_Windows__CGaming__CInput__CRacingWheel *This,
        UINT32 start_index,
        UINT32 items_size,
        __x_ABI_CWindows_CGaming_CInput_CIRacingWheel **items,
        UINT32 *value);

    END_INTERFACE
} __FIVectorView_1_Windows__CGaming__CInput__CRacingWheelVtbl;

interface __FIVectorView_1_Windows__CGaming__CInput__CRacingWheel {
    CONST_VTBL __FIVectorView_1_Windows__CGaming__CInput__CRacingWheelVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIVectorView_1_Windows__CGaming__CInput__CRacingWheel_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIVectorView_1_Windows__CGaming__CInput__CRacingWheel_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIVectorView_1_Windows__CGaming__CInput__CRacingWheel_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIVectorView_1_Windows__CGaming__CInput__CRacingWheel_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIVectorView_1_Windows__CGaming__CInput__CRacingWheel_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIVectorView_1_Windows__CGaming__CInput__CRacingWheel_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IVectorView<ABI::Windows::Gaming::Input::RacingWheel* > methods ***/
#define __FIVectorView_1_Windows__CGaming__CInput__CRacingWheel_GetAt(This,index,value) (This)->lpVtbl->GetAt(This,index,value)
#define __FIVectorView_1_Windows__CGaming__CInput__CRacingWheel_get_Size(This,value) (This)->lpVtbl->get_Size(This,value)
#define __FIVectorView_1_Windows__CGaming__CInput__CRacingWheel_IndexOf(This,element,index,value) (This)->lpVtbl->IndexOf(This,element,index,value)
#define __FIVectorView_1_Windows__CGaming__CInput__CRacingWheel_GetMany(This,start_index,items_size,items,value) (This)->lpVtbl->GetMany(This,start_index,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIVectorView_1_Windows__CGaming__CInput__CRacingWheel_QueryInterface(__FIVectorView_1_Windows__CGaming__CInput__CRacingWheel* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIVectorView_1_Windows__CGaming__CInput__CRacingWheel_AddRef(__FIVectorView_1_Windows__CGaming__CInput__CRacingWheel* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIVectorView_1_Windows__CGaming__CInput__CRacingWheel_Release(__FIVectorView_1_Windows__CGaming__CInput__CRacingWheel* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIVectorView_1_Windows__CGaming__CInput__CRacingWheel_GetIids(__FIVectorView_1_Windows__CGaming__CInput__CRacingWheel* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIVectorView_1_Windows__CGaming__CInput__CRacingWheel_GetRuntimeClassName(__FIVectorView_1_Windows__CGaming__CInput__CRacingWheel* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIVectorView_1_Windows__CGaming__CInput__CRacingWheel_GetTrustLevel(__FIVectorView_1_Windows__CGaming__CInput__CRacingWheel* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IVectorView<ABI::Windows::Gaming::Input::RacingWheel* > methods ***/
static inline HRESULT __FIVectorView_1_Windows__CGaming__CInput__CRacingWheel_GetAt(__FIVectorView_1_Windows__CGaming__CInput__CRacingWheel* This,UINT32 index,__x_ABI_CWindows_CGaming_CInput_CIRacingWheel **value) {
    return This->lpVtbl->GetAt(This,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CGaming__CInput__CRacingWheel_get_Size(__FIVectorView_1_Windows__CGaming__CInput__CRacingWheel* This,UINT32 *value) {
    return This->lpVtbl->get_Size(This,value);
}
static inline HRESULT __FIVectorView_1_Windows__CGaming__CInput__CRacingWheel_IndexOf(__FIVectorView_1_Windows__CGaming__CInput__CRacingWheel* This,__x_ABI_CWindows_CGaming_CInput_CIRacingWheel *element,UINT32 *index,BOOLEAN *value) {
    return This->lpVtbl->IndexOf(This,element,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CGaming__CInput__CRacingWheel_GetMany(__FIVectorView_1_Windows__CGaming__CInput__CRacingWheel* This,UINT32 start_index,UINT32 items_size,__x_ABI_CWindows_CGaming_CInput_CIRacingWheel **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,start_index,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IVectorView_RacingWheel IID___FIVectorView_1_Windows__CGaming__CInput__CRacingWheel
#define IVectorView_RacingWheelVtbl __FIVectorView_1_Windows__CGaming__CInput__CRacingWheelVtbl
#define IVectorView_RacingWheel __FIVectorView_1_Windows__CGaming__CInput__CRacingWheel
#define IVectorView_RacingWheel_QueryInterface __FIVectorView_1_Windows__CGaming__CInput__CRacingWheel_QueryInterface
#define IVectorView_RacingWheel_AddRef __FIVectorView_1_Windows__CGaming__CInput__CRacingWheel_AddRef
#define IVectorView_RacingWheel_Release __FIVectorView_1_Windows__CGaming__CInput__CRacingWheel_Release
#define IVectorView_RacingWheel_GetIids __FIVectorView_1_Windows__CGaming__CInput__CRacingWheel_GetIids
#define IVectorView_RacingWheel_GetRuntimeClassName __FIVectorView_1_Windows__CGaming__CInput__CRacingWheel_GetRuntimeClassName
#define IVectorView_RacingWheel_GetTrustLevel __FIVectorView_1_Windows__CGaming__CInput__CRacingWheel_GetTrustLevel
#define IVectorView_RacingWheel_GetAt __FIVectorView_1_Windows__CGaming__CInput__CRacingWheel_GetAt
#define IVectorView_RacingWheel_get_Size __FIVectorView_1_Windows__CGaming__CInput__CRacingWheel_get_Size
#define IVectorView_RacingWheel_IndexOf __FIVectorView_1_Windows__CGaming__CInput__CRacingWheel_IndexOf
#define IVectorView_RacingWheel_GetMany __FIVectorView_1_Windows__CGaming__CInput__CRacingWheel_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIVectorView_1_Windows__CGaming__CInput__CRacingWheel_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVector<ABI::Windows::Gaming::Input::RacingWheel* > interface
 */
#ifndef ____FIVector_1_Windows__CGaming__CInput__CRacingWheel_INTERFACE_DEFINED__
#define ____FIVector_1_Windows__CGaming__CInput__CRacingWheel_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIVector_1_Windows__CGaming__CInput__CRacingWheel, 0xe21d3be8, 0xb215, 0x5c0f, 0xaf,0x72, 0x33,0x9f,0x41,0xaf,0x73,0x4d);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("e21d3be8-b215-5c0f-af72-339f41af734d")
                IVector<ABI::Windows::Gaming::Input::RacingWheel* > : IVector_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Gaming::Input::RacingWheel*, ABI::Windows::Gaming::Input::IRacingWheel* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIVector_1_Windows__CGaming__CInput__CRacingWheel, 0xe21d3be8, 0xb215, 0x5c0f, 0xaf,0x72, 0x33,0x9f,0x41,0xaf,0x73,0x4d)
#endif
#else
typedef struct __FIVector_1_Windows__CGaming__CInput__CRacingWheelVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIVector_1_Windows__CGaming__CInput__CRacingWheel *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIVector_1_Windows__CGaming__CInput__CRacingWheel *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIVector_1_Windows__CGaming__CInput__CRacingWheel *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIVector_1_Windows__CGaming__CInput__CRacingWheel *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIVector_1_Windows__CGaming__CInput__CRacingWheel *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIVector_1_Windows__CGaming__CInput__CRacingWheel *This,
        TrustLevel *trustLevel);

    /*** IVector<ABI::Windows::Gaming::Input::RacingWheel* > methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAt)(
        __FIVector_1_Windows__CGaming__CInput__CRacingWheel *This,
        UINT32 index,
        __x_ABI_CWindows_CGaming_CInput_CIRacingWheel **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIVector_1_Windows__CGaming__CInput__CRacingWheel *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *GetView)(
        __FIVector_1_Windows__CGaming__CInput__CRacingWheel *This,
        __FIVectorView_1_Windows__CGaming__CInput__CRacingWheel **value);

    HRESULT (STDMETHODCALLTYPE *IndexOf)(
        __FIVector_1_Windows__CGaming__CInput__CRacingWheel *This,
        __x_ABI_CWindows_CGaming_CInput_CIRacingWheel *element,
        UINT32 *index,
        BOOLEAN *value);

    HRESULT (STDMETHODCALLTYPE *SetAt)(
        __FIVector_1_Windows__CGaming__CInput__CRacingWheel *This,
        UINT32 index,
        __x_ABI_CWindows_CGaming_CInput_CIRacingWheel *value);

    HRESULT (STDMETHODCALLTYPE *InsertAt)(
        __FIVector_1_Windows__CGaming__CInput__CRacingWheel *This,
        UINT32 index,
        __x_ABI_CWindows_CGaming_CInput_CIRacingWheel *value);

    HRESULT (STDMETHODCALLTYPE *RemoveAt)(
        __FIVector_1_Windows__CGaming__CInput__CRacingWheel *This,
        UINT32 index);

    HRESULT (STDMETHODCALLTYPE *Append)(
        __FIVector_1_Windows__CGaming__CInput__CRacingWheel *This,
        __x_ABI_CWindows_CGaming_CInput_CIRacingWheel *value);

    HRESULT (STDMETHODCALLTYPE *RemoveAtEnd)(
        __FIVector_1_Windows__CGaming__CInput__CRacingWheel *This);

    HRESULT (STDMETHODCALLTYPE *Clear)(
        __FIVector_1_Windows__CGaming__CInput__CRacingWheel *This);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIVector_1_Windows__CGaming__CInput__CRacingWheel *This,
        UINT32 start_index,
        UINT32 items_size,
        __x_ABI_CWindows_CGaming_CInput_CIRacingWheel **items,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *ReplaceAll)(
        __FIVector_1_Windows__CGaming__CInput__CRacingWheel *This,
        UINT32 count,
        __x_ABI_CWindows_CGaming_CInput_CIRacingWheel **items);

    END_INTERFACE
} __FIVector_1_Windows__CGaming__CInput__CRacingWheelVtbl;

interface __FIVector_1_Windows__CGaming__CInput__CRacingWheel {
    CONST_VTBL __FIVector_1_Windows__CGaming__CInput__CRacingWheelVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIVector_1_Windows__CGaming__CInput__CRacingWheel_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIVector_1_Windows__CGaming__CInput__CRacingWheel_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIVector_1_Windows__CGaming__CInput__CRacingWheel_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIVector_1_Windows__CGaming__CInput__CRacingWheel_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIVector_1_Windows__CGaming__CInput__CRacingWheel_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIVector_1_Windows__CGaming__CInput__CRacingWheel_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IVector<ABI::Windows::Gaming::Input::RacingWheel* > methods ***/
#define __FIVector_1_Windows__CGaming__CInput__CRacingWheel_GetAt(This,index,value) (This)->lpVtbl->GetAt(This,index,value)
#define __FIVector_1_Windows__CGaming__CInput__CRacingWheel_get_Size(This,value) (This)->lpVtbl->get_Size(This,value)
#define __FIVector_1_Windows__CGaming__CInput__CRacingWheel_GetView(This,value) (This)->lpVtbl->GetView(This,value)
#define __FIVector_1_Windows__CGaming__CInput__CRacingWheel_IndexOf(This,element,index,value) (This)->lpVtbl->IndexOf(This,element,index,value)
#define __FIVector_1_Windows__CGaming__CInput__CRacingWheel_SetAt(This,index,value) (This)->lpVtbl->SetAt(This,index,value)
#define __FIVector_1_Windows__CGaming__CInput__CRacingWheel_InsertAt(This,index,value) (This)->lpVtbl->InsertAt(This,index,value)
#define __FIVector_1_Windows__CGaming__CInput__CRacingWheel_RemoveAt(This,index) (This)->lpVtbl->RemoveAt(This,index)
#define __FIVector_1_Windows__CGaming__CInput__CRacingWheel_Append(This,value) (This)->lpVtbl->Append(This,value)
#define __FIVector_1_Windows__CGaming__CInput__CRacingWheel_RemoveAtEnd(This) (This)->lpVtbl->RemoveAtEnd(This)
#define __FIVector_1_Windows__CGaming__CInput__CRacingWheel_Clear(This) (This)->lpVtbl->Clear(This)
#define __FIVector_1_Windows__CGaming__CInput__CRacingWheel_GetMany(This,start_index,items_size,items,value) (This)->lpVtbl->GetMany(This,start_index,items_size,items,value)
#define __FIVector_1_Windows__CGaming__CInput__CRacingWheel_ReplaceAll(This,count,items) (This)->lpVtbl->ReplaceAll(This,count,items)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIVector_1_Windows__CGaming__CInput__CRacingWheel_QueryInterface(__FIVector_1_Windows__CGaming__CInput__CRacingWheel* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIVector_1_Windows__CGaming__CInput__CRacingWheel_AddRef(__FIVector_1_Windows__CGaming__CInput__CRacingWheel* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIVector_1_Windows__CGaming__CInput__CRacingWheel_Release(__FIVector_1_Windows__CGaming__CInput__CRacingWheel* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIVector_1_Windows__CGaming__CInput__CRacingWheel_GetIids(__FIVector_1_Windows__CGaming__CInput__CRacingWheel* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIVector_1_Windows__CGaming__CInput__CRacingWheel_GetRuntimeClassName(__FIVector_1_Windows__CGaming__CInput__CRacingWheel* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIVector_1_Windows__CGaming__CInput__CRacingWheel_GetTrustLevel(__FIVector_1_Windows__CGaming__CInput__CRacingWheel* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IVector<ABI::Windows::Gaming::Input::RacingWheel* > methods ***/
static inline HRESULT __FIVector_1_Windows__CGaming__CInput__CRacingWheel_GetAt(__FIVector_1_Windows__CGaming__CInput__CRacingWheel* This,UINT32 index,__x_ABI_CWindows_CGaming_CInput_CIRacingWheel **value) {
    return This->lpVtbl->GetAt(This,index,value);
}
static inline HRESULT __FIVector_1_Windows__CGaming__CInput__CRacingWheel_get_Size(__FIVector_1_Windows__CGaming__CInput__CRacingWheel* This,UINT32 *value) {
    return This->lpVtbl->get_Size(This,value);
}
static inline HRESULT __FIVector_1_Windows__CGaming__CInput__CRacingWheel_GetView(__FIVector_1_Windows__CGaming__CInput__CRacingWheel* This,__FIVectorView_1_Windows__CGaming__CInput__CRacingWheel **value) {
    return This->lpVtbl->GetView(This,value);
}
static inline HRESULT __FIVector_1_Windows__CGaming__CInput__CRacingWheel_IndexOf(__FIVector_1_Windows__CGaming__CInput__CRacingWheel* This,__x_ABI_CWindows_CGaming_CInput_CIRacingWheel *element,UINT32 *index,BOOLEAN *value) {
    return This->lpVtbl->IndexOf(This,element,index,value);
}
static inline HRESULT __FIVector_1_Windows__CGaming__CInput__CRacingWheel_SetAt(__FIVector_1_Windows__CGaming__CInput__CRacingWheel* This,UINT32 index,__x_ABI_CWindows_CGaming_CInput_CIRacingWheel *value) {
    return This->lpVtbl->SetAt(This,index,value);
}
static inline HRESULT __FIVector_1_Windows__CGaming__CInput__CRacingWheel_InsertAt(__FIVector_1_Windows__CGaming__CInput__CRacingWheel* This,UINT32 index,__x_ABI_CWindows_CGaming_CInput_CIRacingWheel *value) {
    return This->lpVtbl->InsertAt(This,index,value);
}
static inline HRESULT __FIVector_1_Windows__CGaming__CInput__CRacingWheel_RemoveAt(__FIVector_1_Windows__CGaming__CInput__CRacingWheel* This,UINT32 index) {
    return This->lpVtbl->RemoveAt(This,index);
}
static inline HRESULT __FIVector_1_Windows__CGaming__CInput__CRacingWheel_Append(__FIVector_1_Windows__CGaming__CInput__CRacingWheel* This,__x_ABI_CWindows_CGaming_CInput_CIRacingWheel *value) {
    return This->lpVtbl->Append(This,value);
}
static inline HRESULT __FIVector_1_Windows__CGaming__CInput__CRacingWheel_RemoveAtEnd(__FIVector_1_Windows__CGaming__CInput__CRacingWheel* This) {
    return This->lpVtbl->RemoveAtEnd(This);
}
static inline HRESULT __FIVector_1_Windows__CGaming__CInput__CRacingWheel_Clear(__FIVector_1_Windows__CGaming__CInput__CRacingWheel* This) {
    return This->lpVtbl->Clear(This);
}
static inline HRESULT __FIVector_1_Windows__CGaming__CInput__CRacingWheel_GetMany(__FIVector_1_Windows__CGaming__CInput__CRacingWheel* This,UINT32 start_index,UINT32 items_size,__x_ABI_CWindows_CGaming_CInput_CIRacingWheel **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,start_index,items_size,items,value);
}
static inline HRESULT __FIVector_1_Windows__CGaming__CInput__CRacingWheel_ReplaceAll(__FIVector_1_Windows__CGaming__CInput__CRacingWheel* This,UINT32 count,__x_ABI_CWindows_CGaming_CInput_CIRacingWheel **items) {
    return This->lpVtbl->ReplaceAll(This,count,items);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IVector_RacingWheel IID___FIVector_1_Windows__CGaming__CInput__CRacingWheel
#define IVector_RacingWheelVtbl __FIVector_1_Windows__CGaming__CInput__CRacingWheelVtbl
#define IVector_RacingWheel __FIVector_1_Windows__CGaming__CInput__CRacingWheel
#define IVector_RacingWheel_QueryInterface __FIVector_1_Windows__CGaming__CInput__CRacingWheel_QueryInterface
#define IVector_RacingWheel_AddRef __FIVector_1_Windows__CGaming__CInput__CRacingWheel_AddRef
#define IVector_RacingWheel_Release __FIVector_1_Windows__CGaming__CInput__CRacingWheel_Release
#define IVector_RacingWheel_GetIids __FIVector_1_Windows__CGaming__CInput__CRacingWheel_GetIids
#define IVector_RacingWheel_GetRuntimeClassName __FIVector_1_Windows__CGaming__CInput__CRacingWheel_GetRuntimeClassName
#define IVector_RacingWheel_GetTrustLevel __FIVector_1_Windows__CGaming__CInput__CRacingWheel_GetTrustLevel
#define IVector_RacingWheel_GetAt __FIVector_1_Windows__CGaming__CInput__CRacingWheel_GetAt
#define IVector_RacingWheel_get_Size __FIVector_1_Windows__CGaming__CInput__CRacingWheel_get_Size
#define IVector_RacingWheel_GetView __FIVector_1_Windows__CGaming__CInput__CRacingWheel_GetView
#define IVector_RacingWheel_IndexOf __FIVector_1_Windows__CGaming__CInput__CRacingWheel_IndexOf
#define IVector_RacingWheel_SetAt __FIVector_1_Windows__CGaming__CInput__CRacingWheel_SetAt
#define IVector_RacingWheel_InsertAt __FIVector_1_Windows__CGaming__CInput__CRacingWheel_InsertAt
#define IVector_RacingWheel_RemoveAt __FIVector_1_Windows__CGaming__CInput__CRacingWheel_RemoveAt
#define IVector_RacingWheel_Append __FIVector_1_Windows__CGaming__CInput__CRacingWheel_Append
#define IVector_RacingWheel_RemoveAtEnd __FIVector_1_Windows__CGaming__CInput__CRacingWheel_RemoveAtEnd
#define IVector_RacingWheel_Clear __FIVector_1_Windows__CGaming__CInput__CRacingWheel_Clear
#define IVector_RacingWheel_GetMany __FIVector_1_Windows__CGaming__CInput__CRacingWheel_GetMany
#define IVector_RacingWheel_ReplaceAll __FIVector_1_Windows__CGaming__CInput__CRacingWheel_ReplaceAll
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIVector_1_Windows__CGaming__CInput__CRacingWheel_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IIterator<ABI::Windows::Gaming::Input::RawGameController* > interface
 */
#ifndef ____FIIterator_1_Windows__CGaming__CInput__CRawGameController_INTERFACE_DEFINED__
#define ____FIIterator_1_Windows__CGaming__CInput__CRawGameController_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterator_1_Windows__CGaming__CInput__CRawGameController, 0x51cc88dc, 0x66fb, 0x55ea, 0x9a,0x1b, 0xaa,0xdc,0xd7,0x1c,0xc0,0x8e);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("51cc88dc-66fb-55ea-9a1b-aadcd71cc08e")
                IIterator<ABI::Windows::Gaming::Input::RawGameController* > : IIterator_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Gaming::Input::RawGameController*, ABI::Windows::Gaming::Input::IRawGameController* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterator_1_Windows__CGaming__CInput__CRawGameController, 0x51cc88dc, 0x66fb, 0x55ea, 0x9a,0x1b, 0xaa,0xdc,0xd7,0x1c,0xc0,0x8e)
#endif
#else
typedef struct __FIIterator_1_Windows__CGaming__CInput__CRawGameControllerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterator_1_Windows__CGaming__CInput__CRawGameController *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterator_1_Windows__CGaming__CInput__CRawGameController *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterator_1_Windows__CGaming__CInput__CRawGameController *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterator_1_Windows__CGaming__CInput__CRawGameController *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterator_1_Windows__CGaming__CInput__CRawGameController *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterator_1_Windows__CGaming__CInput__CRawGameController *This,
        TrustLevel *trustLevel);

    /*** IIterator<ABI::Windows::Gaming::Input::RawGameController* > methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Current)(
        __FIIterator_1_Windows__CGaming__CInput__CRawGameController *This,
        __x_ABI_CWindows_CGaming_CInput_CIRawGameController **value);

    HRESULT (STDMETHODCALLTYPE *get_HasCurrent)(
        __FIIterator_1_Windows__CGaming__CInput__CRawGameController *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *MoveNext)(
        __FIIterator_1_Windows__CGaming__CInput__CRawGameController *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIIterator_1_Windows__CGaming__CInput__CRawGameController *This,
        UINT32 items_size,
        __x_ABI_CWindows_CGaming_CInput_CIRawGameController **items,
        UINT32 *value);

    END_INTERFACE
} __FIIterator_1_Windows__CGaming__CInput__CRawGameControllerVtbl;

interface __FIIterator_1_Windows__CGaming__CInput__CRawGameController {
    CONST_VTBL __FIIterator_1_Windows__CGaming__CInput__CRawGameControllerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterator_1_Windows__CGaming__CInput__CRawGameController_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterator_1_Windows__CGaming__CInput__CRawGameController_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterator_1_Windows__CGaming__CInput__CRawGameController_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterator_1_Windows__CGaming__CInput__CRawGameController_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterator_1_Windows__CGaming__CInput__CRawGameController_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterator_1_Windows__CGaming__CInput__CRawGameController_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterator<ABI::Windows::Gaming::Input::RawGameController* > methods ***/
#define __FIIterator_1_Windows__CGaming__CInput__CRawGameController_get_Current(This,value) (This)->lpVtbl->get_Current(This,value)
#define __FIIterator_1_Windows__CGaming__CInput__CRawGameController_get_HasCurrent(This,value) (This)->lpVtbl->get_HasCurrent(This,value)
#define __FIIterator_1_Windows__CGaming__CInput__CRawGameController_MoveNext(This,value) (This)->lpVtbl->MoveNext(This,value)
#define __FIIterator_1_Windows__CGaming__CInput__CRawGameController_GetMany(This,items_size,items,value) (This)->lpVtbl->GetMany(This,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterator_1_Windows__CGaming__CInput__CRawGameController_QueryInterface(__FIIterator_1_Windows__CGaming__CInput__CRawGameController* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterator_1_Windows__CGaming__CInput__CRawGameController_AddRef(__FIIterator_1_Windows__CGaming__CInput__CRawGameController* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterator_1_Windows__CGaming__CInput__CRawGameController_Release(__FIIterator_1_Windows__CGaming__CInput__CRawGameController* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterator_1_Windows__CGaming__CInput__CRawGameController_GetIids(__FIIterator_1_Windows__CGaming__CInput__CRawGameController* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterator_1_Windows__CGaming__CInput__CRawGameController_GetRuntimeClassName(__FIIterator_1_Windows__CGaming__CInput__CRawGameController* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterator_1_Windows__CGaming__CInput__CRawGameController_GetTrustLevel(__FIIterator_1_Windows__CGaming__CInput__CRawGameController* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterator<ABI::Windows::Gaming::Input::RawGameController* > methods ***/
static inline HRESULT __FIIterator_1_Windows__CGaming__CInput__CRawGameController_get_Current(__FIIterator_1_Windows__CGaming__CInput__CRawGameController* This,__x_ABI_CWindows_CGaming_CInput_CIRawGameController **value) {
    return This->lpVtbl->get_Current(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CGaming__CInput__CRawGameController_get_HasCurrent(__FIIterator_1_Windows__CGaming__CInput__CRawGameController* This,boolean *value) {
    return This->lpVtbl->get_HasCurrent(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CGaming__CInput__CRawGameController_MoveNext(__FIIterator_1_Windows__CGaming__CInput__CRawGameController* This,boolean *value) {
    return This->lpVtbl->MoveNext(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CGaming__CInput__CRawGameController_GetMany(__FIIterator_1_Windows__CGaming__CInput__CRawGameController* This,UINT32 items_size,__x_ABI_CWindows_CGaming_CInput_CIRawGameController **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterator_RawGameController IID___FIIterator_1_Windows__CGaming__CInput__CRawGameController
#define IIterator_RawGameControllerVtbl __FIIterator_1_Windows__CGaming__CInput__CRawGameControllerVtbl
#define IIterator_RawGameController __FIIterator_1_Windows__CGaming__CInput__CRawGameController
#define IIterator_RawGameController_QueryInterface __FIIterator_1_Windows__CGaming__CInput__CRawGameController_QueryInterface
#define IIterator_RawGameController_AddRef __FIIterator_1_Windows__CGaming__CInput__CRawGameController_AddRef
#define IIterator_RawGameController_Release __FIIterator_1_Windows__CGaming__CInput__CRawGameController_Release
#define IIterator_RawGameController_GetIids __FIIterator_1_Windows__CGaming__CInput__CRawGameController_GetIids
#define IIterator_RawGameController_GetRuntimeClassName __FIIterator_1_Windows__CGaming__CInput__CRawGameController_GetRuntimeClassName
#define IIterator_RawGameController_GetTrustLevel __FIIterator_1_Windows__CGaming__CInput__CRawGameController_GetTrustLevel
#define IIterator_RawGameController_get_Current __FIIterator_1_Windows__CGaming__CInput__CRawGameController_get_Current
#define IIterator_RawGameController_get_HasCurrent __FIIterator_1_Windows__CGaming__CInput__CRawGameController_get_HasCurrent
#define IIterator_RawGameController_MoveNext __FIIterator_1_Windows__CGaming__CInput__CRawGameController_MoveNext
#define IIterator_RawGameController_GetMany __FIIterator_1_Windows__CGaming__CInput__CRawGameController_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterator_1_Windows__CGaming__CInput__CRawGameController_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IIterable<ABI::Windows::Gaming::Input::RawGameController* > interface
 */
#ifndef ____FIIterable_1_Windows__CGaming__CInput__CRawGameController_INTERFACE_DEFINED__
#define ____FIIterable_1_Windows__CGaming__CInput__CRawGameController_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterable_1_Windows__CGaming__CInput__CRawGameController, 0x8f2f08cc, 0xf4f4, 0x5539, 0x93,0x57, 0x1f,0x07,0x33,0x4d,0x38,0x1f);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("8f2f08cc-f4f4-5539-9357-1f07334d381f")
                IIterable<ABI::Windows::Gaming::Input::RawGameController* > : IIterable_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Gaming::Input::RawGameController*, ABI::Windows::Gaming::Input::IRawGameController* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterable_1_Windows__CGaming__CInput__CRawGameController, 0x8f2f08cc, 0xf4f4, 0x5539, 0x93,0x57, 0x1f,0x07,0x33,0x4d,0x38,0x1f)
#endif
#else
typedef struct __FIIterable_1_Windows__CGaming__CInput__CRawGameControllerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterable_1_Windows__CGaming__CInput__CRawGameController *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterable_1_Windows__CGaming__CInput__CRawGameController *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterable_1_Windows__CGaming__CInput__CRawGameController *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterable_1_Windows__CGaming__CInput__CRawGameController *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterable_1_Windows__CGaming__CInput__CRawGameController *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterable_1_Windows__CGaming__CInput__CRawGameController *This,
        TrustLevel *trustLevel);

    /*** IIterable<ABI::Windows::Gaming::Input::RawGameController* > methods ***/
    HRESULT (STDMETHODCALLTYPE *First)(
        __FIIterable_1_Windows__CGaming__CInput__CRawGameController *This,
        __FIIterator_1_Windows__CGaming__CInput__CRawGameController **value);

    END_INTERFACE
} __FIIterable_1_Windows__CGaming__CInput__CRawGameControllerVtbl;

interface __FIIterable_1_Windows__CGaming__CInput__CRawGameController {
    CONST_VTBL __FIIterable_1_Windows__CGaming__CInput__CRawGameControllerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterable_1_Windows__CGaming__CInput__CRawGameController_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterable_1_Windows__CGaming__CInput__CRawGameController_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterable_1_Windows__CGaming__CInput__CRawGameController_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterable_1_Windows__CGaming__CInput__CRawGameController_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterable_1_Windows__CGaming__CInput__CRawGameController_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterable_1_Windows__CGaming__CInput__CRawGameController_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterable<ABI::Windows::Gaming::Input::RawGameController* > methods ***/
#define __FIIterable_1_Windows__CGaming__CInput__CRawGameController_First(This,value) (This)->lpVtbl->First(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterable_1_Windows__CGaming__CInput__CRawGameController_QueryInterface(__FIIterable_1_Windows__CGaming__CInput__CRawGameController* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterable_1_Windows__CGaming__CInput__CRawGameController_AddRef(__FIIterable_1_Windows__CGaming__CInput__CRawGameController* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterable_1_Windows__CGaming__CInput__CRawGameController_Release(__FIIterable_1_Windows__CGaming__CInput__CRawGameController* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterable_1_Windows__CGaming__CInput__CRawGameController_GetIids(__FIIterable_1_Windows__CGaming__CInput__CRawGameController* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterable_1_Windows__CGaming__CInput__CRawGameController_GetRuntimeClassName(__FIIterable_1_Windows__CGaming__CInput__CRawGameController* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterable_1_Windows__CGaming__CInput__CRawGameController_GetTrustLevel(__FIIterable_1_Windows__CGaming__CInput__CRawGameController* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterable<ABI::Windows::Gaming::Input::RawGameController* > methods ***/
static inline HRESULT __FIIterable_1_Windows__CGaming__CInput__CRawGameController_First(__FIIterable_1_Windows__CGaming__CInput__CRawGameController* This,__FIIterator_1_Windows__CGaming__CInput__CRawGameController **value) {
    return This->lpVtbl->First(This,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterable_RawGameController IID___FIIterable_1_Windows__CGaming__CInput__CRawGameController
#define IIterable_RawGameControllerVtbl __FIIterable_1_Windows__CGaming__CInput__CRawGameControllerVtbl
#define IIterable_RawGameController __FIIterable_1_Windows__CGaming__CInput__CRawGameController
#define IIterable_RawGameController_QueryInterface __FIIterable_1_Windows__CGaming__CInput__CRawGameController_QueryInterface
#define IIterable_RawGameController_AddRef __FIIterable_1_Windows__CGaming__CInput__CRawGameController_AddRef
#define IIterable_RawGameController_Release __FIIterable_1_Windows__CGaming__CInput__CRawGameController_Release
#define IIterable_RawGameController_GetIids __FIIterable_1_Windows__CGaming__CInput__CRawGameController_GetIids
#define IIterable_RawGameController_GetRuntimeClassName __FIIterable_1_Windows__CGaming__CInput__CRawGameController_GetRuntimeClassName
#define IIterable_RawGameController_GetTrustLevel __FIIterable_1_Windows__CGaming__CInput__CRawGameController_GetTrustLevel
#define IIterable_RawGameController_First __FIIterable_1_Windows__CGaming__CInput__CRawGameController_First
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterable_1_Windows__CGaming__CInput__CRawGameController_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVectorView<ABI::Windows::Gaming::Input::RawGameController* > interface
 */
#ifndef ____FIVectorView_1_Windows__CGaming__CInput__CRawGameController_INTERFACE_DEFINED__
#define ____FIVectorView_1_Windows__CGaming__CInput__CRawGameController_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIVectorView_1_Windows__CGaming__CInput__CRawGameController, 0x779cc322, 0x40c0, 0x55c1, 0x8d,0xc5, 0xcc,0x6e,0x3a,0xfe,0x02,0xcf);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("779cc322-40c0-55c1-8dc5-cc6e3afe02cf")
                IVectorView<ABI::Windows::Gaming::Input::RawGameController* > : IVectorView_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Gaming::Input::RawGameController*, ABI::Windows::Gaming::Input::IRawGameController* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIVectorView_1_Windows__CGaming__CInput__CRawGameController, 0x779cc322, 0x40c0, 0x55c1, 0x8d,0xc5, 0xcc,0x6e,0x3a,0xfe,0x02,0xcf)
#endif
#else
typedef struct __FIVectorView_1_Windows__CGaming__CInput__CRawGameControllerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIVectorView_1_Windows__CGaming__CInput__CRawGameController *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIVectorView_1_Windows__CGaming__CInput__CRawGameController *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIVectorView_1_Windows__CGaming__CInput__CRawGameController *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIVectorView_1_Windows__CGaming__CInput__CRawGameController *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIVectorView_1_Windows__CGaming__CInput__CRawGameController *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIVectorView_1_Windows__CGaming__CInput__CRawGameController *This,
        TrustLevel *trustLevel);

    /*** IVectorView<ABI::Windows::Gaming::Input::RawGameController* > methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAt)(
        __FIVectorView_1_Windows__CGaming__CInput__CRawGameController *This,
        UINT32 index,
        __x_ABI_CWindows_CGaming_CInput_CIRawGameController **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIVectorView_1_Windows__CGaming__CInput__CRawGameController *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *IndexOf)(
        __FIVectorView_1_Windows__CGaming__CInput__CRawGameController *This,
        __x_ABI_CWindows_CGaming_CInput_CIRawGameController *element,
        UINT32 *index,
        BOOLEAN *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIVectorView_1_Windows__CGaming__CInput__CRawGameController *This,
        UINT32 start_index,
        UINT32 items_size,
        __x_ABI_CWindows_CGaming_CInput_CIRawGameController **items,
        UINT32 *value);

    END_INTERFACE
} __FIVectorView_1_Windows__CGaming__CInput__CRawGameControllerVtbl;

interface __FIVectorView_1_Windows__CGaming__CInput__CRawGameController {
    CONST_VTBL __FIVectorView_1_Windows__CGaming__CInput__CRawGameControllerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIVectorView_1_Windows__CGaming__CInput__CRawGameController_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIVectorView_1_Windows__CGaming__CInput__CRawGameController_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIVectorView_1_Windows__CGaming__CInput__CRawGameController_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIVectorView_1_Windows__CGaming__CInput__CRawGameController_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIVectorView_1_Windows__CGaming__CInput__CRawGameController_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIVectorView_1_Windows__CGaming__CInput__CRawGameController_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IVectorView<ABI::Windows::Gaming::Input::RawGameController* > methods ***/
#define __FIVectorView_1_Windows__CGaming__CInput__CRawGameController_GetAt(This,index,value) (This)->lpVtbl->GetAt(This,index,value)
#define __FIVectorView_1_Windows__CGaming__CInput__CRawGameController_get_Size(This,value) (This)->lpVtbl->get_Size(This,value)
#define __FIVectorView_1_Windows__CGaming__CInput__CRawGameController_IndexOf(This,element,index,value) (This)->lpVtbl->IndexOf(This,element,index,value)
#define __FIVectorView_1_Windows__CGaming__CInput__CRawGameController_GetMany(This,start_index,items_size,items,value) (This)->lpVtbl->GetMany(This,start_index,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIVectorView_1_Windows__CGaming__CInput__CRawGameController_QueryInterface(__FIVectorView_1_Windows__CGaming__CInput__CRawGameController* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIVectorView_1_Windows__CGaming__CInput__CRawGameController_AddRef(__FIVectorView_1_Windows__CGaming__CInput__CRawGameController* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIVectorView_1_Windows__CGaming__CInput__CRawGameController_Release(__FIVectorView_1_Windows__CGaming__CInput__CRawGameController* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIVectorView_1_Windows__CGaming__CInput__CRawGameController_GetIids(__FIVectorView_1_Windows__CGaming__CInput__CRawGameController* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIVectorView_1_Windows__CGaming__CInput__CRawGameController_GetRuntimeClassName(__FIVectorView_1_Windows__CGaming__CInput__CRawGameController* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIVectorView_1_Windows__CGaming__CInput__CRawGameController_GetTrustLevel(__FIVectorView_1_Windows__CGaming__CInput__CRawGameController* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IVectorView<ABI::Windows::Gaming::Input::RawGameController* > methods ***/
static inline HRESULT __FIVectorView_1_Windows__CGaming__CInput__CRawGameController_GetAt(__FIVectorView_1_Windows__CGaming__CInput__CRawGameController* This,UINT32 index,__x_ABI_CWindows_CGaming_CInput_CIRawGameController **value) {
    return This->lpVtbl->GetAt(This,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CGaming__CInput__CRawGameController_get_Size(__FIVectorView_1_Windows__CGaming__CInput__CRawGameController* This,UINT32 *value) {
    return This->lpVtbl->get_Size(This,value);
}
static inline HRESULT __FIVectorView_1_Windows__CGaming__CInput__CRawGameController_IndexOf(__FIVectorView_1_Windows__CGaming__CInput__CRawGameController* This,__x_ABI_CWindows_CGaming_CInput_CIRawGameController *element,UINT32 *index,BOOLEAN *value) {
    return This->lpVtbl->IndexOf(This,element,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CGaming__CInput__CRawGameController_GetMany(__FIVectorView_1_Windows__CGaming__CInput__CRawGameController* This,UINT32 start_index,UINT32 items_size,__x_ABI_CWindows_CGaming_CInput_CIRawGameController **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,start_index,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IVectorView_RawGameController IID___FIVectorView_1_Windows__CGaming__CInput__CRawGameController
#define IVectorView_RawGameControllerVtbl __FIVectorView_1_Windows__CGaming__CInput__CRawGameControllerVtbl
#define IVectorView_RawGameController __FIVectorView_1_Windows__CGaming__CInput__CRawGameController
#define IVectorView_RawGameController_QueryInterface __FIVectorView_1_Windows__CGaming__CInput__CRawGameController_QueryInterface
#define IVectorView_RawGameController_AddRef __FIVectorView_1_Windows__CGaming__CInput__CRawGameController_AddRef
#define IVectorView_RawGameController_Release __FIVectorView_1_Windows__CGaming__CInput__CRawGameController_Release
#define IVectorView_RawGameController_GetIids __FIVectorView_1_Windows__CGaming__CInput__CRawGameController_GetIids
#define IVectorView_RawGameController_GetRuntimeClassName __FIVectorView_1_Windows__CGaming__CInput__CRawGameController_GetRuntimeClassName
#define IVectorView_RawGameController_GetTrustLevel __FIVectorView_1_Windows__CGaming__CInput__CRawGameController_GetTrustLevel
#define IVectorView_RawGameController_GetAt __FIVectorView_1_Windows__CGaming__CInput__CRawGameController_GetAt
#define IVectorView_RawGameController_get_Size __FIVectorView_1_Windows__CGaming__CInput__CRawGameController_get_Size
#define IVectorView_RawGameController_IndexOf __FIVectorView_1_Windows__CGaming__CInput__CRawGameController_IndexOf
#define IVectorView_RawGameController_GetMany __FIVectorView_1_Windows__CGaming__CInput__CRawGameController_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIVectorView_1_Windows__CGaming__CInput__CRawGameController_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVector<ABI::Windows::Gaming::Input::RawGameController* > interface
 */
#ifndef ____FIVector_1_Windows__CGaming__CInput__CRawGameController_INTERFACE_DEFINED__
#define ____FIVector_1_Windows__CGaming__CInput__CRawGameController_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIVector_1_Windows__CGaming__CInput__CRawGameController, 0x9136cbcf, 0xb87c, 0x5886, 0x89,0x62, 0xb0,0xff,0xbd,0x64,0xb7,0x63);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("9136cbcf-b87c-5886-8962-b0ffbd64b763")
                IVector<ABI::Windows::Gaming::Input::RawGameController* > : IVector_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Gaming::Input::RawGameController*, ABI::Windows::Gaming::Input::IRawGameController* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIVector_1_Windows__CGaming__CInput__CRawGameController, 0x9136cbcf, 0xb87c, 0x5886, 0x89,0x62, 0xb0,0xff,0xbd,0x64,0xb7,0x63)
#endif
#else
typedef struct __FIVector_1_Windows__CGaming__CInput__CRawGameControllerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIVector_1_Windows__CGaming__CInput__CRawGameController *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIVector_1_Windows__CGaming__CInput__CRawGameController *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIVector_1_Windows__CGaming__CInput__CRawGameController *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIVector_1_Windows__CGaming__CInput__CRawGameController *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIVector_1_Windows__CGaming__CInput__CRawGameController *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIVector_1_Windows__CGaming__CInput__CRawGameController *This,
        TrustLevel *trustLevel);

    /*** IVector<ABI::Windows::Gaming::Input::RawGameController* > methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAt)(
        __FIVector_1_Windows__CGaming__CInput__CRawGameController *This,
        UINT32 index,
        __x_ABI_CWindows_CGaming_CInput_CIRawGameController **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIVector_1_Windows__CGaming__CInput__CRawGameController *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *GetView)(
        __FIVector_1_Windows__CGaming__CInput__CRawGameController *This,
        __FIVectorView_1_Windows__CGaming__CInput__CRawGameController **value);

    HRESULT (STDMETHODCALLTYPE *IndexOf)(
        __FIVector_1_Windows__CGaming__CInput__CRawGameController *This,
        __x_ABI_CWindows_CGaming_CInput_CIRawGameController *element,
        UINT32 *index,
        BOOLEAN *value);

    HRESULT (STDMETHODCALLTYPE *SetAt)(
        __FIVector_1_Windows__CGaming__CInput__CRawGameController *This,
        UINT32 index,
        __x_ABI_CWindows_CGaming_CInput_CIRawGameController *value);

    HRESULT (STDMETHODCALLTYPE *InsertAt)(
        __FIVector_1_Windows__CGaming__CInput__CRawGameController *This,
        UINT32 index,
        __x_ABI_CWindows_CGaming_CInput_CIRawGameController *value);

    HRESULT (STDMETHODCALLTYPE *RemoveAt)(
        __FIVector_1_Windows__CGaming__CInput__CRawGameController *This,
        UINT32 index);

    HRESULT (STDMETHODCALLTYPE *Append)(
        __FIVector_1_Windows__CGaming__CInput__CRawGameController *This,
        __x_ABI_CWindows_CGaming_CInput_CIRawGameController *value);

    HRESULT (STDMETHODCALLTYPE *RemoveAtEnd)(
        __FIVector_1_Windows__CGaming__CInput__CRawGameController *This);

    HRESULT (STDMETHODCALLTYPE *Clear)(
        __FIVector_1_Windows__CGaming__CInput__CRawGameController *This);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIVector_1_Windows__CGaming__CInput__CRawGameController *This,
        UINT32 start_index,
        UINT32 items_size,
        __x_ABI_CWindows_CGaming_CInput_CIRawGameController **items,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *ReplaceAll)(
        __FIVector_1_Windows__CGaming__CInput__CRawGameController *This,
        UINT32 count,
        __x_ABI_CWindows_CGaming_CInput_CIRawGameController **items);

    END_INTERFACE
} __FIVector_1_Windows__CGaming__CInput__CRawGameControllerVtbl;

interface __FIVector_1_Windows__CGaming__CInput__CRawGameController {
    CONST_VTBL __FIVector_1_Windows__CGaming__CInput__CRawGameControllerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIVector_1_Windows__CGaming__CInput__CRawGameController_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIVector_1_Windows__CGaming__CInput__CRawGameController_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIVector_1_Windows__CGaming__CInput__CRawGameController_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIVector_1_Windows__CGaming__CInput__CRawGameController_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIVector_1_Windows__CGaming__CInput__CRawGameController_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIVector_1_Windows__CGaming__CInput__CRawGameController_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IVector<ABI::Windows::Gaming::Input::RawGameController* > methods ***/
#define __FIVector_1_Windows__CGaming__CInput__CRawGameController_GetAt(This,index,value) (This)->lpVtbl->GetAt(This,index,value)
#define __FIVector_1_Windows__CGaming__CInput__CRawGameController_get_Size(This,value) (This)->lpVtbl->get_Size(This,value)
#define __FIVector_1_Windows__CGaming__CInput__CRawGameController_GetView(This,value) (This)->lpVtbl->GetView(This,value)
#define __FIVector_1_Windows__CGaming__CInput__CRawGameController_IndexOf(This,element,index,value) (This)->lpVtbl->IndexOf(This,element,index,value)
#define __FIVector_1_Windows__CGaming__CInput__CRawGameController_SetAt(This,index,value) (This)->lpVtbl->SetAt(This,index,value)
#define __FIVector_1_Windows__CGaming__CInput__CRawGameController_InsertAt(This,index,value) (This)->lpVtbl->InsertAt(This,index,value)
#define __FIVector_1_Windows__CGaming__CInput__CRawGameController_RemoveAt(This,index) (This)->lpVtbl->RemoveAt(This,index)
#define __FIVector_1_Windows__CGaming__CInput__CRawGameController_Append(This,value) (This)->lpVtbl->Append(This,value)
#define __FIVector_1_Windows__CGaming__CInput__CRawGameController_RemoveAtEnd(This) (This)->lpVtbl->RemoveAtEnd(This)
#define __FIVector_1_Windows__CGaming__CInput__CRawGameController_Clear(This) (This)->lpVtbl->Clear(This)
#define __FIVector_1_Windows__CGaming__CInput__CRawGameController_GetMany(This,start_index,items_size,items,value) (This)->lpVtbl->GetMany(This,start_index,items_size,items,value)
#define __FIVector_1_Windows__CGaming__CInput__CRawGameController_ReplaceAll(This,count,items) (This)->lpVtbl->ReplaceAll(This,count,items)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIVector_1_Windows__CGaming__CInput__CRawGameController_QueryInterface(__FIVector_1_Windows__CGaming__CInput__CRawGameController* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIVector_1_Windows__CGaming__CInput__CRawGameController_AddRef(__FIVector_1_Windows__CGaming__CInput__CRawGameController* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIVector_1_Windows__CGaming__CInput__CRawGameController_Release(__FIVector_1_Windows__CGaming__CInput__CRawGameController* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIVector_1_Windows__CGaming__CInput__CRawGameController_GetIids(__FIVector_1_Windows__CGaming__CInput__CRawGameController* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIVector_1_Windows__CGaming__CInput__CRawGameController_GetRuntimeClassName(__FIVector_1_Windows__CGaming__CInput__CRawGameController* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIVector_1_Windows__CGaming__CInput__CRawGameController_GetTrustLevel(__FIVector_1_Windows__CGaming__CInput__CRawGameController* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IVector<ABI::Windows::Gaming::Input::RawGameController* > methods ***/
static inline HRESULT __FIVector_1_Windows__CGaming__CInput__CRawGameController_GetAt(__FIVector_1_Windows__CGaming__CInput__CRawGameController* This,UINT32 index,__x_ABI_CWindows_CGaming_CInput_CIRawGameController **value) {
    return This->lpVtbl->GetAt(This,index,value);
}
static inline HRESULT __FIVector_1_Windows__CGaming__CInput__CRawGameController_get_Size(__FIVector_1_Windows__CGaming__CInput__CRawGameController* This,UINT32 *value) {
    return This->lpVtbl->get_Size(This,value);
}
static inline HRESULT __FIVector_1_Windows__CGaming__CInput__CRawGameController_GetView(__FIVector_1_Windows__CGaming__CInput__CRawGameController* This,__FIVectorView_1_Windows__CGaming__CInput__CRawGameController **value) {
    return This->lpVtbl->GetView(This,value);
}
static inline HRESULT __FIVector_1_Windows__CGaming__CInput__CRawGameController_IndexOf(__FIVector_1_Windows__CGaming__CInput__CRawGameController* This,__x_ABI_CWindows_CGaming_CInput_CIRawGameController *element,UINT32 *index,BOOLEAN *value) {
    return This->lpVtbl->IndexOf(This,element,index,value);
}
static inline HRESULT __FIVector_1_Windows__CGaming__CInput__CRawGameController_SetAt(__FIVector_1_Windows__CGaming__CInput__CRawGameController* This,UINT32 index,__x_ABI_CWindows_CGaming_CInput_CIRawGameController *value) {
    return This->lpVtbl->SetAt(This,index,value);
}
static inline HRESULT __FIVector_1_Windows__CGaming__CInput__CRawGameController_InsertAt(__FIVector_1_Windows__CGaming__CInput__CRawGameController* This,UINT32 index,__x_ABI_CWindows_CGaming_CInput_CIRawGameController *value) {
    return This->lpVtbl->InsertAt(This,index,value);
}
static inline HRESULT __FIVector_1_Windows__CGaming__CInput__CRawGameController_RemoveAt(__FIVector_1_Windows__CGaming__CInput__CRawGameController* This,UINT32 index) {
    return This->lpVtbl->RemoveAt(This,index);
}
static inline HRESULT __FIVector_1_Windows__CGaming__CInput__CRawGameController_Append(__FIVector_1_Windows__CGaming__CInput__CRawGameController* This,__x_ABI_CWindows_CGaming_CInput_CIRawGameController *value) {
    return This->lpVtbl->Append(This,value);
}
static inline HRESULT __FIVector_1_Windows__CGaming__CInput__CRawGameController_RemoveAtEnd(__FIVector_1_Windows__CGaming__CInput__CRawGameController* This) {
    return This->lpVtbl->RemoveAtEnd(This);
}
static inline HRESULT __FIVector_1_Windows__CGaming__CInput__CRawGameController_Clear(__FIVector_1_Windows__CGaming__CInput__CRawGameController* This) {
    return This->lpVtbl->Clear(This);
}
static inline HRESULT __FIVector_1_Windows__CGaming__CInput__CRawGameController_GetMany(__FIVector_1_Windows__CGaming__CInput__CRawGameController* This,UINT32 start_index,UINT32 items_size,__x_ABI_CWindows_CGaming_CInput_CIRawGameController **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,start_index,items_size,items,value);
}
static inline HRESULT __FIVector_1_Windows__CGaming__CInput__CRawGameController_ReplaceAll(__FIVector_1_Windows__CGaming__CInput__CRawGameController* This,UINT32 count,__x_ABI_CWindows_CGaming_CInput_CIRawGameController **items) {
    return This->lpVtbl->ReplaceAll(This,count,items);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IVector_RawGameController IID___FIVector_1_Windows__CGaming__CInput__CRawGameController
#define IVector_RawGameControllerVtbl __FIVector_1_Windows__CGaming__CInput__CRawGameControllerVtbl
#define IVector_RawGameController __FIVector_1_Windows__CGaming__CInput__CRawGameController
#define IVector_RawGameController_QueryInterface __FIVector_1_Windows__CGaming__CInput__CRawGameController_QueryInterface
#define IVector_RawGameController_AddRef __FIVector_1_Windows__CGaming__CInput__CRawGameController_AddRef
#define IVector_RawGameController_Release __FIVector_1_Windows__CGaming__CInput__CRawGameController_Release
#define IVector_RawGameController_GetIids __FIVector_1_Windows__CGaming__CInput__CRawGameController_GetIids
#define IVector_RawGameController_GetRuntimeClassName __FIVector_1_Windows__CGaming__CInput__CRawGameController_GetRuntimeClassName
#define IVector_RawGameController_GetTrustLevel __FIVector_1_Windows__CGaming__CInput__CRawGameController_GetTrustLevel
#define IVector_RawGameController_GetAt __FIVector_1_Windows__CGaming__CInput__CRawGameController_GetAt
#define IVector_RawGameController_get_Size __FIVector_1_Windows__CGaming__CInput__CRawGameController_get_Size
#define IVector_RawGameController_GetView __FIVector_1_Windows__CGaming__CInput__CRawGameController_GetView
#define IVector_RawGameController_IndexOf __FIVector_1_Windows__CGaming__CInput__CRawGameController_IndexOf
#define IVector_RawGameController_SetAt __FIVector_1_Windows__CGaming__CInput__CRawGameController_SetAt
#define IVector_RawGameController_InsertAt __FIVector_1_Windows__CGaming__CInput__CRawGameController_InsertAt
#define IVector_RawGameController_RemoveAt __FIVector_1_Windows__CGaming__CInput__CRawGameController_RemoveAt
#define IVector_RawGameController_Append __FIVector_1_Windows__CGaming__CInput__CRawGameController_Append
#define IVector_RawGameController_RemoveAtEnd __FIVector_1_Windows__CGaming__CInput__CRawGameController_RemoveAtEnd
#define IVector_RawGameController_Clear __FIVector_1_Windows__CGaming__CInput__CRawGameController_Clear
#define IVector_RawGameController_GetMany __FIVector_1_Windows__CGaming__CInput__CRawGameController_GetMany
#define IVector_RawGameController_ReplaceAll __FIVector_1_Windows__CGaming__CInput__CRawGameController_ReplaceAll
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIVector_1_Windows__CGaming__CInput__CRawGameController_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER HSTRING_UserSize     (ULONG *, ULONG, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserMarshal  (ULONG *, unsigned char *, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserUnmarshal(ULONG *, unsigned char *, HSTRING *);
void            __RPC_USER HSTRING_UserFree     (ULONG *, HSTRING *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __windows_gaming_input_h__ */
