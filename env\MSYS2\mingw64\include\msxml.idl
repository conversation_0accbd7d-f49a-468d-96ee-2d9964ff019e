/*
 * Copyright (C) 2005 <PERSON>
 * Copyright (C) 2005 <PERSON>
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

#include <msxmldid.h>
#include <msxml2did.h>
#include <idispids.h>

#ifndef DO_NO_IMPORTS
import "unknwn.idl";
import "wtypes.idl";
import "objidl.idl";
import "oaidl.idl";
#endif

[
    uuid(d63e0ce2-a0a2-11d0-9c02-00c04fc99c8e),
    version(2.0),
    helpstring("Microsoft XML, v2.0")
]
library MSXML
{

importlib("stdole2.tlb");

#include <xmldom.idl>
#include <xmldso.idl>

[
local,
object,
odl,
dual,
oleautomation,
uuid(65725580-9b5d-11d0-9bfe-00c04fc99c8e)
]
interface IXMLElementCollection : IDispatch
{
    [propput, id(DISPID_XMLELEMENTCOLLECTION_LENGTH)]
    HRESULT length( [in] LONG v );

    [propget, id(DISPID_XMLELEMENTCOLLECTION_LENGTH)]
    HRESULT length( [retval, out] LONG *p );

    [propget, id(DISPID_XMLELEMENTCOLLECTION_NEWENUM)]
    HRESULT _newEnum( [retval, out] IUnknown ** ppUnk );

    [id(DISPID_XMLELEMENTCOLLECTION_ITEM)]
    HRESULT item(
            [optional, in] VARIANT var1,
            [optional, in] VARIANT var2,
            [retval, out] IDispatch ** ppDisp );
}

[
local,
object,
uuid(3f7f31ac-e15f-11d0-9c25-00c04fc99c8e)
]
interface IXMLElement : IDispatch
{
    [propget, id(DISPID_XMLELEMENT_TAGNAME)]
    HRESULT tagName( [retval, out] BSTR *p);

    [propput, id(DISPID_XMLELEMENT_TAGNAME)]
    HRESULT tagName( [in] BSTR p );

    [propget, id(DISPID_XMLELEMENT_PARENT)]
    HRESULT parent( [retval, out] IXMLElement **parent );

    [id(DISPID_XMLELEMENT_SETATTRIBUTE)]
    HRESULT setAttribute(
            [in] BSTR strPropertyName,
            [in] VARIANT PropertyValue );

    [id(DISPID_XMLELEMENT_GETATTRIBUTE)]
    HRESULT getAttribute(
            [in] BSTR strPropertyName,
            [retval, out] VARIANT *PropertyValue );

    [id(DISPID_XMLELEMENT_REMOVEATTRIBUTE)]
    HRESULT removeAttribute(
            [in] BSTR strPropertyName );

    [propget, id(DISPID_XMLELEMENT_CHILDREN)]
    HRESULT children( [retval, out] IXMLElementCollection **p );

    [propget, id(DISPID_XMLELEMENT_TYPE)]
    HRESULT type( [retval, out] LONG *p );

    [propget, id(DISPID_XMLELEMENT_TEXT)]
    HRESULT text( [retval, out] BSTR *p );

    [propput, id(DISPID_XMLELEMENT_TEXT)]
    HRESULT text( [in] BSTR p );

    [id(DISPID_XMLELEMENT_ADDCHILD)]
    HRESULT addChild(
            [in] IXMLElement *pChildElem,
            [in] LONG lIndex,
            [in] LONG lreserved );

    [id(DISPID_XMLELEMENT_REMOVECHILD)]
    HRESULT removeChild(
            [in] IXMLElement *pChildElem );
}

[
local,
object,
uuid(f52e2b61-18a1-11d1-b105-00805f49916b)
]
interface IXMLDocument : IDispatch
{
    [propget, id(DISPID_XMLDOCUMENT_ROOT)]
    HRESULT root( [retval, out] IXMLElement **p );

    [propget, id(DISPID_XMLDOCUMENT_FILESIZE)]
    HRESULT fileSize( [retval, out] BSTR *p );

    [propget, id(DISPID_XMLDOCUMENT_FILEMODIFIEDDATE)]
    HRESULT fileModifiedDate( [retval, out] BSTR *p );

    [propget, id(DISPID_XMLDOCUMENT_FILEUPDATEDDATE)]
    HRESULT fileUpdatedDate( [retval, out] BSTR *p );

    [propget, id(DISPID_XMLDOCUMENT_URL)]
    HRESULT URL( [retval, out] BSTR *p );

    [propput, id(DISPID_XMLDOCUMENT_URL)]
    HRESULT URL( [in] BSTR p );

    [propget, id(DISPID_XMLDOCUMENT_MIMETYPE)]
    HRESULT mimeType( [retval, out] BSTR *p );

    [propget, id(DISPID_XMLDOCUMENT_READYSTATE)]
    HRESULT readyState( [retval, out] LONG *p );

    [propget, id(DISPID_XMLDOCUMENT_CHARSET)]
    HRESULT charset( [retval, out] BSTR *p );

    [propput, id(DISPID_XMLDOCUMENT_CHARSET)]
    HRESULT charset( [in] BSTR p );

    [propget, id(DISPID_XMLDOCUMENT_VERSION)]
    HRESULT version( [retval, out] BSTR *p );

    [propget, id(DISPID_XMLDOCUMENT_DOCTYPE)]
    HRESULT doctype( [retval, out] BSTR *p );

    [propget, id(DISPID_XMLDOCUMENT_DTDURL)]
    HRESULT dtdURl( [retval, out] BSTR *p );

    [id(DISPID_XMLDOCUMENT_CREATEELEMENT)]
    HRESULT createElement(
            [in] VARIANT vType,
            [optional, in] VARIANT var1,
            [retval, out] IXMLElement **ppElem );
}

[
local,
object,
uuid(2B8DE2FF-8D2D-11d1-B2FC-00C04FD915A9)
]
interface IXMLElement2 : IDispatch
{
    [propget, id(DISPID_XMLELEMENT_TAGNAME)]
    HRESULT tagName([retval, out] BSTR *p);

    [propput, id(DISPID_XMLELEMENT_TAGNAME)]
    HRESULT tagName([in] BSTR p);

    [propget, id(DISPID_XMLELEMENT_PARENT)]
    HRESULT parent([retval, out]IXMLElement2 **ppParent);

    [id(DISPID_XMLELEMENT_SETATTRIBUTE)]
    HRESULT setAttribute(
        [in] BSTR strPropertyName,
        [in] VARIANT PropertyValue);

    [id(DISPID_XMLELEMENT_GETATTRIBUTE)]
    HRESULT getAttribute(
        [in] BSTR strPropertyName,
        [retval, out] VARIANT *PropertyValue);

    [id(DISPID_XMLELEMENT_REMOVEATTRIBUTE)]
    HRESULT removeAttribute([in] BSTR strPropertyName);

    [propget, id(DISPID_XMLELEMENT_CHILDREN)]
    HRESULT children([retval, out] IXMLElementCollection **pp);

    [propget, id(DISPID_XMLELEMENT_TYPE)]
    HRESULT type([retval, out] LONG *plType);

    [propget, id(DISPID_XMLELEMENT_TEXT)]
    HRESULT text([retval, out] BSTR *p);

    [propput, id(DISPID_XMLELEMENT_TEXT)]
    HRESULT text([in] BSTR p);

    [id(DISPID_XMLELEMENT_ADDCHILD)]
    HRESULT addChild(
        [in] IXMLElement2 *pChildElem,
        [in] LONG lIndex,
        [in] LONG lReserved);

    [id(DISPID_XMLELEMENT_REMOVECHILD)]
    HRESULT removeChild([in]IXMLElement2 *pChildElem);

    [propget, id(DISPID_XMLELEMENT_ATTRIBUTES)]
    HRESULT attributes([retval, out] IXMLElementCollection **pp); 
}

[
local,
object,
uuid(2B8DE2FE-8D2D-11d1-B2FC-00C04FD915A9)
]
interface IXMLDocument2 : IDispatch
{
    [propget, id(DISPID_XMLDOCUMENT_ROOT)]
    HRESULT root([retval, out] IXMLElement2 **p);

    [propget, id(DISPID_XMLDOCUMENT_FILESIZE)]
    HRESULT fileSize([retval, out] BSTR *p);

    [propget, id(DISPID_XMLDOCUMENT_FILEMODIFIEDDATE)]
    HRESULT fileModifiedDate([retval, out] BSTR *p);

    [propget, id(DISPID_XMLDOCUMENT_FILEUPDATEDDATE)]
    HRESULT fileUpdatedDate([retval, out] BSTR *p);

    [propget, id(DISPID_XMLDOCUMENT_URL)]
    HRESULT URL([retval, out] BSTR *p);

    [propput, id(DISPID_XMLDOCUMENT_URL)]
    HRESULT URL([in] BSTR p);

    [propget, id(DISPID_XMLDOCUMENT_MIMETYPE)]
    HRESULT mimeType([retval, out] BSTR *p);
   
    [propget, id(DISPID_XMLDOCUMENT_READYSTATE)]
    HRESULT readyState([retval, out]LONG *pl);

    [propget, id(DISPID_XMLDOCUMENT_CHARSET)]
    HRESULT charset([retval, out]BSTR *p);

    [propput, id(DISPID_XMLDOCUMENT_CHARSET)]
    HRESULT charset([in]BSTR p);

    [propget, id(DISPID_XMLDOCUMENT_VERSION)]
    HRESULT version([retval, out]BSTR *p);

    [propget, id(DISPID_XMLDOCUMENT_DOCTYPE)]
    HRESULT doctype([retval, out]BSTR *p);

    [propget, restricted, id(DISPID_XMLDOCUMENT_DTDURL)]
    HRESULT dtdURL([retval, out]BSTR *p);

    [id(DISPID_XMLDOCUMENT_CREATEELEMENT)]
    HRESULT createElement(
        [in] VARIANT vType, 
        [optional, in] VARIANT var, 
        [retval, out] IXMLElement2 **ppElem);

    [propget, id(DISPID_XMLDOCUMENT_ASYNC)]
    HRESULT async([retval, out] VARIANT_BOOL *pf);

    [propput, id(DISPID_XMLDOCUMENT_ASYNC)]
    HRESULT async([in] VARIANT_BOOL f);
}

typedef enum tagXMLEMEM_TYPE {
	XMLELEMTYPE_ELEMENT	= 0,
	XMLELEMTYPE_TEXT 	= 1,
	XMLELEMTYPE_COMMENT	= 2,
	XMLELEMTYPE_DOCUMENT	= 3,
	XMLELEMTYPE_DTD		= 4,
	XMLELEMTYPE_PI		= 5,
	XMLELEMTYPE_OTHER	= 6
} XMLELEM_TYPE;

typedef struct _xml_error {
	UINT _nLine;
	BSTR _pchBuf;
	BSTR _cchBuf;
	UINT _ich;
	BSTR _pszFound;
	BSTR _pszExpected;
	DWORD _reserved1;
	DWORD _reserved2;
} XML_ERROR;

[
local,
object,
uuid(d4d4a0fc-3b73-11d1-b2b4-00c04fb92596)
]
interface IXMLAttribute : IDispatch
{
    [propget, id(DISPID_XMLATTRIBUTE_NAME)]
    HRESULT name( [retval, out] BSTR *p );

    [propget, id(DISPID_XMLATTRIBUTE_VALUE)]
    HRESULT value( [retval, out] BSTR *p );
}

[
local,
object,
uuid(948c5ad3-c58d-11d0-9c0b-00c04fc99c8e)
]
interface IXMLError : IUnknown
{
    HRESULT GetErrorInfo([out]XML_ERROR *pErrorReturn);
}

[
    object,
    local,
    uuid(D9F1E15A-CCDB-11d0-9C0C-00C04FC99C8E)
]
interface IXMLElementNotificationSink : IDispatch
{
    [id(DISPID_XMLNOTIFSINK_CHILDADDED)] 
    HRESULT ChildAdded([in]IDispatch *pChildElem); 
}

[
    helpstring("Msxml"),
    progid("Msxml"),
    threading(both),
    version(1.0),
    uuid(cfc399af-d876-11d0-9c10-00c04fc99c8e)
]
coclass XMLDocument
{
    [default] interface IXMLDocument;
}

} /* Library MSXML */
