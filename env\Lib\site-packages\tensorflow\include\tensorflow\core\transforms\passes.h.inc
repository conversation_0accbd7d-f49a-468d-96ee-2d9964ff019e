/* Autogenerated by mlir-tblgen; don't manually edit */

#ifdef GEN_PASS_DECL
// Generate declarations for all passes.
#define GEN_PASS_DECL_ADDDEFAULTATTRS
#define GEN_PASS_DECL_CSEPASS
#define GEN_PASS_DECL_CONSOLIDATEATTRIBUTES
#define GEN_PASS_DECL_CONSTANTFOLDINGPASS
#define GEN_PASS_DECL_CONTROLFLOWSINK
#define GEN_PASS_DECL_DEDUPEANDHOISTCONSTANT
#define GEN_PASS_DECL_DROPOUTPUTSHAPESATTR
#define GEN_PASS_DECL_ELIMINATEPASSTHROUGHITERARGS
#define GEN_PASS_DECL_FUNCTOGRAPH
#define GEN_PASS_DECL_FUNCTIONALTOREGION
#define GEN_PASS_DECL_GRAPHTOFUNC
#define GEN_PASS_DECL_LIFTLEGACYCALL
#define GEN_PASS_DECL_NAMECOMPRESS
#define GEN_PASS_DECL_PREPAREATTRIBUTESFOREXPORT
#define GEN_PASS_DECL_REGIONTOFUNCTIONAL
#define GEN_PASS_DECL_REMAPPER
#define GEN_PASS_DECL_SHAPEINFERENCE
#define GEN_PASS_DECL_STRIPDEFAULTATTRS
#define GEN_PASS_DECL_TOPOSORT
#undef GEN_PASS_DECL
#endif // GEN_PASS_DECL

//===----------------------------------------------------------------------===//
// AddDefaultAttrs
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_ADDDEFAULTATTRS
#undef GEN_PASS_DECL_ADDDEFAULTATTRS
#endif // GEN_PASS_DECL_ADDDEFAULTATTRS
#ifdef GEN_PASS_DEF_ADDDEFAULTATTRS
namespace impl {

template <typename DerivedT>
class AddDefaultAttrsBase : public ::mlir::OperationPass<> {
public:
  using Base = AddDefaultAttrsBase;

  AddDefaultAttrsBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  AddDefaultAttrsBase(const AddDefaultAttrsBase &other) : ::mlir::OperationPass<>(other) {}
  AddDefaultAttrsBase& operator=(const AddDefaultAttrsBase &) = delete;
  AddDefaultAttrsBase(AddDefaultAttrsBase &&) = delete;
  AddDefaultAttrsBase& operator=(AddDefaultAttrsBase &&) = delete;
  ~AddDefaultAttrsBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tfg-add-default-attrs");
  }
  ::llvm::StringRef getArgument() const override { return "tfg-add-default-attrs"; }

  ::llvm::StringRef getDescription() const override { return "Add default-valued attributes to the graph"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AddDefaultAttrs");
  }
  ::llvm::StringRef getName() const override { return "AddDefaultAttrs"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(AddDefaultAttrsBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_ADDDEFAULTATTRS
#endif // GEN_PASS_DEF_ADDDEFAULTATTRS

//===----------------------------------------------------------------------===//
// CSEPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CSEPASS
#undef GEN_PASS_DECL_CSEPASS
#endif // GEN_PASS_DECL_CSEPASS
#ifdef GEN_PASS_DEF_CSEPASS
namespace impl {

template <typename DerivedT>
class CSEPassBase : public ::mlir::OperationPass<GraphFuncOp> {
public:
  using Base = CSEPassBase;

  CSEPassBase() : ::mlir::OperationPass<GraphFuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  CSEPassBase(const CSEPassBase &other) : ::mlir::OperationPass<GraphFuncOp>(other) {}
  CSEPassBase& operator=(const CSEPassBase &) = delete;
  CSEPassBase(CSEPassBase &&) = delete;
  CSEPassBase& operator=(CSEPassBase &&) = delete;
  ~CSEPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tfg-cse");
  }
  ::llvm::StringRef getArgument() const override { return "tfg-cse"; }

  ::llvm::StringRef getDescription() const override { return "Common sub-expression elimination, ignoring op names"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("CSEPass");
  }
  ::llvm::StringRef getName() const override { return "CSEPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(CSEPassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CSEPASS
#endif // GEN_PASS_DEF_CSEPASS

//===----------------------------------------------------------------------===//
// ConsolidateAttributes
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONSOLIDATEATTRIBUTES
#undef GEN_PASS_DECL_CONSOLIDATEATTRIBUTES
#endif // GEN_PASS_DECL_CONSOLIDATEATTRIBUTES
#ifdef GEN_PASS_DEF_CONSOLIDATEATTRIBUTES
namespace impl {

template <typename DerivedT>
class ConsolidateAttributesBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConsolidateAttributesBase;

  ConsolidateAttributesBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConsolidateAttributesBase(const ConsolidateAttributesBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  ConsolidateAttributesBase& operator=(const ConsolidateAttributesBase &) = delete;
  ConsolidateAttributesBase(ConsolidateAttributesBase &&) = delete;
  ConsolidateAttributesBase& operator=(ConsolidateAttributesBase &&) = delete;
  ~ConsolidateAttributesBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tfg-consolidate-attrs");
  }
  ::llvm::StringRef getArgument() const override { return "tfg-consolidate-attrs"; }

  ::llvm::StringRef getDescription() const override { return "Reify type data from attributes to types."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConsolidateAttributes");
  }
  ::llvm::StringRef getName() const override { return "ConsolidateAttributes"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConsolidateAttributesBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONSOLIDATEATTRIBUTES
#endif // GEN_PASS_DEF_CONSOLIDATEATTRIBUTES

//===----------------------------------------------------------------------===//
// ConstantFoldingPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONSTANTFOLDINGPASS
struct ConstantFoldingPassOptions {
  ::llvm::SmallVector<std::string> feeds_;
  ::llvm::SmallVector<std::string> fetches_;
  bool disable_compressed_tensor_optimization_ = false;
  bool fold_quantization_emulation_ = true;
  int pattern_category_ = 0;
};
#undef GEN_PASS_DECL_CONSTANTFOLDINGPASS
#endif // GEN_PASS_DECL_CONSTANTFOLDINGPASS
#ifdef GEN_PASS_DEF_CONSTANTFOLDINGPASS
namespace impl {

template <typename DerivedT>
class ConstantFoldingPassBase : public ::mlir::OperationPass<GraphFuncOp> {
public:
  using Base = ConstantFoldingPassBase;

  ConstantFoldingPassBase() : ::mlir::OperationPass<GraphFuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConstantFoldingPassBase(const ConstantFoldingPassBase &other) : ::mlir::OperationPass<GraphFuncOp>(other) {}
  ConstantFoldingPassBase& operator=(const ConstantFoldingPassBase &) = delete;
  ConstantFoldingPassBase(ConstantFoldingPassBase &&) = delete;
  ConstantFoldingPassBase& operator=(ConstantFoldingPassBase &&) = delete;
  ~ConstantFoldingPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tfg-constant-folding");
  }
  ::llvm::StringRef getArgument() const override { return "tfg-constant-folding"; }

  ::llvm::StringRef getDescription() const override { return "constant-folding on tfg"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConstantFoldingPass");
  }
  ::llvm::StringRef getName() const override { return "ConstantFoldingPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConstantFoldingPassBase<DerivedT>)

  ConstantFoldingPassBase(ConstantFoldingPassOptions options) : ConstantFoldingPassBase() {
    feeds_ = std::move(options.feeds_);
    fetches_ = std::move(options.fetches_);
    disable_compressed_tensor_optimization_ = std::move(options.disable_compressed_tensor_optimization_);
    fold_quantization_emulation_ = std::move(options.fold_quantization_emulation_);
    pattern_category_ = std::move(options.pattern_category_);
  }
protected:
  ::mlir::Pass::ListOption<std::string> feeds_{*this, "feeds", ::llvm::cl::desc("Comma separated list of feed ops."), llvm::cl::ZeroOrMore};
  ::mlir::Pass::ListOption<std::string> fetches_{*this, "fetches", ::llvm::cl::desc("Comma separated list of fetch ops."), llvm::cl::ZeroOrMore};
  ::mlir::Pass::Option<bool> disable_compressed_tensor_optimization_{*this, "disable-compressed-tensor-optimization", ::llvm::cl::desc("Determine if we should disable compressed tensor optimization"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> fold_quantization_emulation_{*this, "fold-quantization-emulation", ::llvm::cl::desc("Determine if we should fold quantization emulation ops"), ::llvm::cl::init(true)};
  ::mlir::Pass::Option<int> pattern_category_{*this, "pattern-category", ::llvm::cl::desc("Select the pattern kind that we would like to run:0 = all patterns, 1 = folder patterns, 2 = propagation patterns"), ::llvm::cl::init(0)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONSTANTFOLDINGPASS
#endif // GEN_PASS_DEF_CONSTANTFOLDINGPASS

//===----------------------------------------------------------------------===//
// ControlFlowSink
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONTROLFLOWSINK
#undef GEN_PASS_DECL_CONTROLFLOWSINK
#endif // GEN_PASS_DECL_CONTROLFLOWSINK
#ifdef GEN_PASS_DEF_CONTROLFLOWSINK
namespace impl {

template <typename DerivedT>
class ControlFlowSinkBase : public ::mlir::OperationPass<GraphFuncOp> {
public:
  using Base = ControlFlowSinkBase;

  ControlFlowSinkBase() : ::mlir::OperationPass<GraphFuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  ControlFlowSinkBase(const ControlFlowSinkBase &other) : ::mlir::OperationPass<GraphFuncOp>(other) {}
  ControlFlowSinkBase& operator=(const ControlFlowSinkBase &) = delete;
  ControlFlowSinkBase(ControlFlowSinkBase &&) = delete;
  ControlFlowSinkBase& operator=(ControlFlowSinkBase &&) = delete;
  ~ControlFlowSinkBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tfg-cf-sink");
  }
  ::llvm::StringRef getArgument() const override { return "tfg-cf-sink"; }

  ::llvm::StringRef getDescription() const override { return "Perform control-flow sink on region-based control-flow ops."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ControlFlowSink");
  }
  ::llvm::StringRef getName() const override { return "ControlFlowSink"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ControlFlowSinkBase<DerivedT>)

protected:
  ::mlir::Pass::Statistic num_sunk{this, "num-sunk", "Number of operations sunk"};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONTROLFLOWSINK
#endif // GEN_PASS_DEF_CONTROLFLOWSINK

//===----------------------------------------------------------------------===//
// DedupeAndHoistConstant
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_DEDUPEANDHOISTCONSTANT
struct DedupeAndHoistConstantOptions {
  int64_t max_size_ = 10;
  bool assume_strict_calls_ = false;
};
#undef GEN_PASS_DECL_DEDUPEANDHOISTCONSTANT
#endif // GEN_PASS_DECL_DEDUPEANDHOISTCONSTANT
#ifdef GEN_PASS_DEF_DEDUPEANDHOISTCONSTANT
namespace impl {

template <typename DerivedT>
class DedupeAndHoistConstantBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = DedupeAndHoistConstantBase;

  DedupeAndHoistConstantBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  DedupeAndHoistConstantBase(const DedupeAndHoistConstantBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  DedupeAndHoistConstantBase& operator=(const DedupeAndHoistConstantBase &) = delete;
  DedupeAndHoistConstantBase(DedupeAndHoistConstantBase &&) = delete;
  DedupeAndHoistConstantBase& operator=(DedupeAndHoistConstantBase &&) = delete;
  ~DedupeAndHoistConstantBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tfg-dedupe-hoist-constant");
  }
  ::llvm::StringRef getArgument() const override { return "tfg-dedupe-hoist-constant"; }

  ::llvm::StringRef getDescription() const override { return "Dedupe and hoist constants"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("DedupeAndHoistConstant");
  }
  ::llvm::StringRef getName() const override { return "DedupeAndHoistConstant"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(DedupeAndHoistConstantBase<DerivedT>)

  DedupeAndHoistConstantBase(DedupeAndHoistConstantOptions options) : DedupeAndHoistConstantBase() {
    max_size_ = std::move(options.max_size_);
    assume_strict_calls_ = std::move(options.assume_strict_calls_);
  }
protected:
  ::mlir::Pass::Option<int64_t> max_size_{*this, "max-size", ::llvm::cl::desc("The maximum number of elements when considering whether a constant is small"), ::llvm::cl::init(10)};
  ::mlir::Pass::Option<bool> assume_strict_calls_{*this, "assume-strict-calls", ::llvm::cl::desc("Assume all function calls are stricts, that is operands are evaluated prior to the call"), ::llvm::cl::init(false)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_DEDUPEANDHOISTCONSTANT
#endif // GEN_PASS_DEF_DEDUPEANDHOISTCONSTANT

//===----------------------------------------------------------------------===//
// DropOutputShapesAttr
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_DROPOUTPUTSHAPESATTR
struct DropOutputShapesAttrOptions {
  ::llvm::SmallVector<std::string> skip_;
};
#undef GEN_PASS_DECL_DROPOUTPUTSHAPESATTR
#endif // GEN_PASS_DECL_DROPOUTPUTSHAPESATTR
#ifdef GEN_PASS_DEF_DROPOUTPUTSHAPESATTR
namespace impl {

template <typename DerivedT>
class DropOutputShapesAttrBase : public ::mlir::OperationPass<> {
public:
  using Base = DropOutputShapesAttrBase;

  DropOutputShapesAttrBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  DropOutputShapesAttrBase(const DropOutputShapesAttrBase &other) : ::mlir::OperationPass<>(other) {}
  DropOutputShapesAttrBase& operator=(const DropOutputShapesAttrBase &) = delete;
  DropOutputShapesAttrBase(DropOutputShapesAttrBase &&) = delete;
  DropOutputShapesAttrBase& operator=(DropOutputShapesAttrBase &&) = delete;
  ~DropOutputShapesAttrBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tfg-drop-unregistered-output-shapes");
  }
  ::llvm::StringRef getArgument() const override { return "tfg-drop-unregistered-output-shapes"; }

  ::llvm::StringRef getDescription() const override { return "Drop _output_shapes attribute"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("DropOutputShapesAttr");
  }
  ::llvm::StringRef getName() const override { return "DropOutputShapesAttr"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(DropOutputShapesAttrBase<DerivedT>)

  DropOutputShapesAttrBase(DropOutputShapesAttrOptions options) : DropOutputShapesAttrBase() {
    skip_ = std::move(options.skip_);
  }
protected:
  ::mlir::Pass::ListOption<std::string> skip_{*this, "skip", ::llvm::cl::desc("Comma separated list of ops that will be skipped.")};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_DROPOUTPUTSHAPESATTR
#endif // GEN_PASS_DEF_DROPOUTPUTSHAPESATTR

//===----------------------------------------------------------------------===//
// EliminatePassthroughIterArgs
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_ELIMINATEPASSTHROUGHITERARGS
#undef GEN_PASS_DECL_ELIMINATEPASSTHROUGHITERARGS
#endif // GEN_PASS_DECL_ELIMINATEPASSTHROUGHITERARGS
#ifdef GEN_PASS_DEF_ELIMINATEPASSTHROUGHITERARGS
namespace impl {

template <typename DerivedT>
class EliminatePassthroughIterArgsBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = EliminatePassthroughIterArgsBase;

  EliminatePassthroughIterArgsBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  EliminatePassthroughIterArgsBase(const EliminatePassthroughIterArgsBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  EliminatePassthroughIterArgsBase& operator=(const EliminatePassthroughIterArgsBase &) = delete;
  EliminatePassthroughIterArgsBase(EliminatePassthroughIterArgsBase &&) = delete;
  EliminatePassthroughIterArgsBase& operator=(EliminatePassthroughIterArgsBase &&) = delete;
  ~EliminatePassthroughIterArgsBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tfg-eliminate-passthrough-iter-args");
  }
  ::llvm::StringRef getArgument() const override { return "tfg-eliminate-passthrough-iter-args"; }

  ::llvm::StringRef getDescription() const override { return "Eliminate passthrough loop iteration arguments."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("EliminatePassthroughIterArgs");
  }
  ::llvm::StringRef getName() const override { return "EliminatePassthroughIterArgs"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<tfg::TFGraphDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(EliminatePassthroughIterArgsBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_ELIMINATEPASSTHROUGHITERARGS
#endif // GEN_PASS_DEF_ELIMINATEPASSTHROUGHITERARGS

//===----------------------------------------------------------------------===//
// FuncToGraph
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_FUNCTOGRAPH
#undef GEN_PASS_DECL_FUNCTOGRAPH
#endif // GEN_PASS_DECL_FUNCTOGRAPH
#ifdef GEN_PASS_DEF_FUNCTOGRAPH
namespace impl {

template <typename DerivedT>
class FuncToGraphBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = FuncToGraphBase;

  FuncToGraphBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  FuncToGraphBase(const FuncToGraphBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  FuncToGraphBase& operator=(const FuncToGraphBase &) = delete;
  FuncToGraphBase(FuncToGraphBase &&) = delete;
  FuncToGraphBase& operator=(FuncToGraphBase &&) = delete;
  ~FuncToGraphBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tfg-lower-func-to-graph");
  }
  ::llvm::StringRef getArgument() const override { return "tfg-lower-func-to-graph"; }

  ::llvm::StringRef getDescription() const override { return "Turns a function back to a graph"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("FuncToGraph");
  }
  ::llvm::StringRef getName() const override { return "FuncToGraph"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(FuncToGraphBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_FUNCTOGRAPH
#endif // GEN_PASS_DEF_FUNCTOGRAPH

//===----------------------------------------------------------------------===//
// FunctionalToRegion
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_FUNCTIONALTOREGION
#undef GEN_PASS_DECL_FUNCTIONALTOREGION
#endif // GEN_PASS_DECL_FUNCTIONALTOREGION
#ifdef GEN_PASS_DEF_FUNCTIONALTOREGION
namespace impl {

template <typename DerivedT>
class FunctionalToRegionBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = FunctionalToRegionBase;

  FunctionalToRegionBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  FunctionalToRegionBase(const FunctionalToRegionBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  FunctionalToRegionBase& operator=(const FunctionalToRegionBase &) = delete;
  FunctionalToRegionBase(FunctionalToRegionBase &&) = delete;
  FunctionalToRegionBase& operator=(FunctionalToRegionBase &&) = delete;
  ~FunctionalToRegionBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tfg-functional-to-region");
  }
  ::llvm::StringRef getArgument() const override { return "tfg-functional-to-region"; }

  ::llvm::StringRef getDescription() const override { return "Convert functional control-flow ops to region-based."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("FunctionalToRegion");
  }
  ::llvm::StringRef getName() const override { return "FunctionalToRegion"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<tfg::TFGraphDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(FunctionalToRegionBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_FUNCTIONALTOREGION
#endif // GEN_PASS_DEF_FUNCTIONALTOREGION

//===----------------------------------------------------------------------===//
// GraphToFunc
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_GRAPHTOFUNC
struct GraphToFuncOptions {
  ::llvm::SmallVector<std::string> feeds_;
  ::llvm::SmallVector<std::string> fetches_;
  ::llvm::SmallVector<std::string> control_rets_;
};
#undef GEN_PASS_DECL_GRAPHTOFUNC
#endif // GEN_PASS_DECL_GRAPHTOFUNC
#ifdef GEN_PASS_DEF_GRAPHTOFUNC
namespace impl {

template <typename DerivedT>
class GraphToFuncBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = GraphToFuncBase;

  GraphToFuncBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  GraphToFuncBase(const GraphToFuncBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  GraphToFuncBase& operator=(const GraphToFuncBase &) = delete;
  GraphToFuncBase(GraphToFuncBase &&) = delete;
  GraphToFuncBase& operator=(GraphToFuncBase &&) = delete;
  ~GraphToFuncBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tfg-lift-graph-to-func");
  }
  ::llvm::StringRef getArgument() const override { return "tfg-lift-graph-to-func"; }

  ::llvm::StringRef getDescription() const override { return "Turns a graph into a function."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("GraphToFunc");
  }
  ::llvm::StringRef getName() const override { return "GraphToFunc"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(GraphToFuncBase<DerivedT>)

  GraphToFuncBase(GraphToFuncOptions options) : GraphToFuncBase() {
    feeds_ = std::move(options.feeds_);
    fetches_ = std::move(options.fetches_);
    control_rets_ = std::move(options.control_rets_);
  }
protected:
  ::mlir::Pass::ListOption<std::string> feeds_{*this, "feeds", ::llvm::cl::desc("Comma separated list of ops that will be turned into arguments.")};
  ::mlir::Pass::ListOption<std::string> fetches_{*this, "fetches", ::llvm::cl::desc("Comma separated list of ops that will be turned into results.")};
  ::mlir::Pass::ListOption<std::string> control_rets_{*this, "control_rets", ::llvm::cl::desc("Comma separated list of ops that will be turned into control returned.")};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_GRAPHTOFUNC
#endif // GEN_PASS_DEF_GRAPHTOFUNC

//===----------------------------------------------------------------------===//
// LiftLegacyCall
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_LIFTLEGACYCALL
#undef GEN_PASS_DECL_LIFTLEGACYCALL
#endif // GEN_PASS_DECL_LIFTLEGACYCALL
#ifdef GEN_PASS_DEF_LIFTLEGACYCALL
namespace impl {

template <typename DerivedT>
class LiftLegacyCallBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = LiftLegacyCallBase;

  LiftLegacyCallBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  LiftLegacyCallBase(const LiftLegacyCallBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  LiftLegacyCallBase& operator=(const LiftLegacyCallBase &) = delete;
  LiftLegacyCallBase(LiftLegacyCallBase &&) = delete;
  LiftLegacyCallBase& operator=(LiftLegacyCallBase &&) = delete;
  ~LiftLegacyCallBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tfg-lift-legacy-call");
  }
  ::llvm::StringRef getArgument() const override { return "tfg-lift-legacy-call"; }

  ::llvm::StringRef getDescription() const override { return "Tag legacy calls with symbol references to add symbol uses"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LiftLegacyCall");
  }
  ::llvm::StringRef getName() const override { return "LiftLegacyCall"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LiftLegacyCallBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_LIFTLEGACYCALL
#endif // GEN_PASS_DEF_LIFTLEGACYCALL

//===----------------------------------------------------------------------===//
// NameCompress
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_NAMECOMPRESS
#undef GEN_PASS_DECL_NAMECOMPRESS
#endif // GEN_PASS_DECL_NAMECOMPRESS
#ifdef GEN_PASS_DEF_NAMECOMPRESS
namespace impl {

template <typename DerivedT>
class NameCompressBase : public ::mlir::OperationPass<GraphFuncOp> {
public:
  using Base = NameCompressBase;

  NameCompressBase() : ::mlir::OperationPass<GraphFuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  NameCompressBase(const NameCompressBase &other) : ::mlir::OperationPass<GraphFuncOp>(other) {}
  NameCompressBase& operator=(const NameCompressBase &) = delete;
  NameCompressBase(NameCompressBase &&) = delete;
  NameCompressBase& operator=(NameCompressBase &&) = delete;
  ~NameCompressBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tfg-name-compress");
  }
  ::llvm::StringRef getArgument() const override { return "tfg-name-compress"; }

  ::llvm::StringRef getDescription() const override { return "Compress the graph by shortening names"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("NameCompress");
  }
  ::llvm::StringRef getName() const override { return "NameCompress"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(NameCompressBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_NAMECOMPRESS
#endif // GEN_PASS_DEF_NAMECOMPRESS

//===----------------------------------------------------------------------===//
// PrepareAttributesForExport
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_PREPAREATTRIBUTESFOREXPORT
#undef GEN_PASS_DECL_PREPAREATTRIBUTESFOREXPORT
#endif // GEN_PASS_DECL_PREPAREATTRIBUTESFOREXPORT
#ifdef GEN_PASS_DEF_PREPAREATTRIBUTESFOREXPORT
namespace impl {

template <typename DerivedT>
class PrepareAttributesForExportBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = PrepareAttributesForExportBase;

  PrepareAttributesForExportBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  PrepareAttributesForExportBase(const PrepareAttributesForExportBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  PrepareAttributesForExportBase& operator=(const PrepareAttributesForExportBase &) = delete;
  PrepareAttributesForExportBase(PrepareAttributesForExportBase &&) = delete;
  PrepareAttributesForExportBase& operator=(PrepareAttributesForExportBase &&) = delete;
  ~PrepareAttributesForExportBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tfg-prepare-attrs-export");
  }
  ::llvm::StringRef getArgument() const override { return "tfg-prepare-attrs-export"; }

  ::llvm::StringRef getDescription() const override { return "Legalize ops' attributes for export."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("PrepareAttributesForExport");
  }
  ::llvm::StringRef getName() const override { return "PrepareAttributesForExport"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(PrepareAttributesForExportBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_PREPAREATTRIBUTESFOREXPORT
#endif // GEN_PASS_DEF_PREPAREATTRIBUTESFOREXPORT

//===----------------------------------------------------------------------===//
// RegionToFunctional
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_REGIONTOFUNCTIONAL
struct RegionToFunctionalOptions {
  bool force_control_capture = false;
};
#undef GEN_PASS_DECL_REGIONTOFUNCTIONAL
#endif // GEN_PASS_DECL_REGIONTOFUNCTIONAL
#ifdef GEN_PASS_DEF_REGIONTOFUNCTIONAL
namespace impl {

template <typename DerivedT>
class RegionToFunctionalBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = RegionToFunctionalBase;

  RegionToFunctionalBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  RegionToFunctionalBase(const RegionToFunctionalBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  RegionToFunctionalBase& operator=(const RegionToFunctionalBase &) = delete;
  RegionToFunctionalBase(RegionToFunctionalBase &&) = delete;
  RegionToFunctionalBase& operator=(RegionToFunctionalBase &&) = delete;
  ~RegionToFunctionalBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tfg-region-to-functional");
  }
  ::llvm::StringRef getArgument() const override { return "tfg-region-to-functional"; }

  ::llvm::StringRef getDescription() const override { return "Convert region-based control-flow ops to functional."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("RegionToFunctional");
  }
  ::llvm::StringRef getName() const override { return "RegionToFunctional"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<tfg::TFGraphDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(RegionToFunctionalBase<DerivedT>)

  RegionToFunctionalBase(RegionToFunctionalOptions options) : RegionToFunctionalBase() {
    force_control_capture = std::move(options.force_control_capture);
  }
protected:
  ::mlir::Pass::Option<bool> force_control_capture{*this, "force-control-capture", ::llvm::cl::desc("Force the capture of control tokens by inserting chain `Const` ops"), ::llvm::cl::init(false)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_REGIONTOFUNCTIONAL
#endif // GEN_PASS_DEF_REGIONTOFUNCTIONAL

//===----------------------------------------------------------------------===//
// Remapper
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_REMAPPER
struct RemapperOptions {
  bool enable_onednn_patterns_ = false;
  bool verify_pdll_patterns_only_ = false;
  bool xla_auto_clustering_ = false;
};
#undef GEN_PASS_DECL_REMAPPER
#endif // GEN_PASS_DECL_REMAPPER
#ifdef GEN_PASS_DEF_REMAPPER
namespace impl {

template <typename DerivedT>
class RemapperBase : public ::mlir::OperationPass<> {
public:
  using Base = RemapperBase;

  RemapperBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  RemapperBase(const RemapperBase &other) : ::mlir::OperationPass<>(other) {}
  RemapperBase& operator=(const RemapperBase &) = delete;
  RemapperBase(RemapperBase &&) = delete;
  RemapperBase& operator=(RemapperBase &&) = delete;
  ~RemapperBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tfg-remapper");
  }
  ::llvm::StringRef getArgument() const override { return "tfg-remapper"; }

  ::llvm::StringRef getDescription() const override { return "Remap operations to decrease amount of operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("Remapper");
  }
  ::llvm::StringRef getName() const override { return "Remapper"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(RemapperBase<DerivedT>)

  RemapperBase(RemapperOptions options) : RemapperBase() {
    enable_onednn_patterns_ = std::move(options.enable_onednn_patterns_);
    verify_pdll_patterns_only_ = std::move(options.verify_pdll_patterns_only_);
    xla_auto_clustering_ = std::move(options.xla_auto_clustering_);
  }
protected:
  ::mlir::Pass::Option<bool> enable_onednn_patterns_{*this, "enable-onednn-patterns", ::llvm::cl::desc("Enable the oneDNN related patterns."), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> verify_pdll_patterns_only_{*this, "verify-pdll-patterns-only", ::llvm::cl::desc("Only enable PDLL patterns."), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> xla_auto_clustering_{*this, "xla-auto-clustering", ::llvm::cl::desc("Sets XLA auto clustering flag."), ::llvm::cl::init(false)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_REMAPPER
#endif // GEN_PASS_DEF_REMAPPER

//===----------------------------------------------------------------------===//
// ShapeInference
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_SHAPEINFERENCE
struct ShapeInferenceOptions {
  int graph_version_;
};
#undef GEN_PASS_DECL_SHAPEINFERENCE
#endif // GEN_PASS_DECL_SHAPEINFERENCE
#ifdef GEN_PASS_DEF_SHAPEINFERENCE
namespace impl {

template <typename DerivedT>
class ShapeInferenceBase : public ::mlir::OperationPass<> {
public:
  using Base = ShapeInferenceBase;

  ShapeInferenceBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ShapeInferenceBase(const ShapeInferenceBase &other) : ::mlir::OperationPass<>(other) {}
  ShapeInferenceBase& operator=(const ShapeInferenceBase &) = delete;
  ShapeInferenceBase(ShapeInferenceBase &&) = delete;
  ShapeInferenceBase& operator=(ShapeInferenceBase &&) = delete;
  ~ShapeInferenceBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tfg-shape-inference");
  }
  ::llvm::StringRef getArgument() const override { return "tfg-shape-inference"; }

  ::llvm::StringRef getDescription() const override { return "Infer the output shape of operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ShapeInference");
  }
  ::llvm::StringRef getName() const override { return "ShapeInference"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ShapeInferenceBase<DerivedT>)

  ShapeInferenceBase(ShapeInferenceOptions options) : ShapeInferenceBase() {
    graph_version_ = std::move(options.graph_version_);
  }
protected:
  ::mlir::Pass::Option<int> graph_version_{*this, "graph-version", ::llvm::cl::desc("The graph producer version")};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_SHAPEINFERENCE
#endif // GEN_PASS_DEF_SHAPEINFERENCE

//===----------------------------------------------------------------------===//
// StripDefaultAttrs
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_STRIPDEFAULTATTRS
#undef GEN_PASS_DECL_STRIPDEFAULTATTRS
#endif // GEN_PASS_DECL_STRIPDEFAULTATTRS
#ifdef GEN_PASS_DEF_STRIPDEFAULTATTRS
namespace impl {

template <typename DerivedT>
class StripDefaultAttrsBase : public ::mlir::OperationPass<> {
public:
  using Base = StripDefaultAttrsBase;

  StripDefaultAttrsBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  StripDefaultAttrsBase(const StripDefaultAttrsBase &other) : ::mlir::OperationPass<>(other) {}
  StripDefaultAttrsBase& operator=(const StripDefaultAttrsBase &) = delete;
  StripDefaultAttrsBase(StripDefaultAttrsBase &&) = delete;
  StripDefaultAttrsBase& operator=(StripDefaultAttrsBase &&) = delete;
  ~StripDefaultAttrsBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tfg-strip-default-attrs");
  }
  ::llvm::StringRef getArgument() const override { return "tfg-strip-default-attrs"; }

  ::llvm::StringRef getDescription() const override { return "Removes default-valued attributes from the graph"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("StripDefaultAttrs");
  }
  ::llvm::StringRef getName() const override { return "StripDefaultAttrs"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(StripDefaultAttrsBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_STRIPDEFAULTATTRS
#endif // GEN_PASS_DEF_STRIPDEFAULTATTRS

//===----------------------------------------------------------------------===//
// TopoSort
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_TOPOSORT
#undef GEN_PASS_DECL_TOPOSORT
#endif // GEN_PASS_DECL_TOPOSORT
#ifdef GEN_PASS_DEF_TOPOSORT
namespace impl {

template <typename DerivedT>
class TopoSortBase : public ::mlir::OperationPass<> {
public:
  using Base = TopoSortBase;

  TopoSortBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  TopoSortBase(const TopoSortBase &other) : ::mlir::OperationPass<>(other) {}
  TopoSortBase& operator=(const TopoSortBase &) = delete;
  TopoSortBase(TopoSortBase &&) = delete;
  TopoSortBase& operator=(TopoSortBase &&) = delete;
  ~TopoSortBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tfg-toposort");
  }
  ::llvm::StringRef getArgument() const override { return "tfg-toposort"; }

  ::llvm::StringRef getDescription() const override { return "Topologically sort graph and function regions"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TopoSort");
  }
  ::llvm::StringRef getName() const override { return "TopoSort"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TopoSortBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_TOPOSORT
#endif // GEN_PASS_DEF_TOPOSORT
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// AddDefaultAttrs Registration
//===----------------------------------------------------------------------===//

inline void registerAddDefaultAttrs() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return CreateAddDefaultAttrsPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerAddDefaultAttrsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return CreateAddDefaultAttrsPass();
  });
}

//===----------------------------------------------------------------------===//
// CSEPass Registration
//===----------------------------------------------------------------------===//

inline void registerCSEPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return CreateCSEPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerCSEPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return CreateCSEPass();
  });
}

//===----------------------------------------------------------------------===//
// ConsolidateAttributes Registration
//===----------------------------------------------------------------------===//

inline void registerConsolidateAttributes() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return CreateConsolidateAttributesPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConsolidateAttributesPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return CreateConsolidateAttributesPass();
  });
}

//===----------------------------------------------------------------------===//
// ConstantFoldingPass Registration
//===----------------------------------------------------------------------===//

inline void registerConstantFoldingPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return CreateConstantFoldingPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConstantFoldingPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return CreateConstantFoldingPass();
  });
}

//===----------------------------------------------------------------------===//
// ControlFlowSink Registration
//===----------------------------------------------------------------------===//

inline void registerControlFlowSink() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return CreateControlFlowSinkPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerControlFlowSinkPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return CreateControlFlowSinkPass();
  });
}

//===----------------------------------------------------------------------===//
// DedupeAndHoistConstant Registration
//===----------------------------------------------------------------------===//

inline void registerDedupeAndHoistConstant() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return CreateDedupeAndHoistConstantPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerDedupeAndHoistConstantPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return CreateDedupeAndHoistConstantPass();
  });
}

//===----------------------------------------------------------------------===//
// DropOutputShapesAttr Registration
//===----------------------------------------------------------------------===//

inline void registerDropOutputShapesAttr() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return CreateDropOutputShapesAttrPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerDropOutputShapesAttrPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return CreateDropOutputShapesAttrPass();
  });
}

//===----------------------------------------------------------------------===//
// EliminatePassthroughIterArgs Registration
//===----------------------------------------------------------------------===//

inline void registerEliminatePassthroughIterArgs() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return CreateEliminatePassthroughIterArgsPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerEliminatePassthroughIterArgsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return CreateEliminatePassthroughIterArgsPass();
  });
}

//===----------------------------------------------------------------------===//
// FuncToGraph Registration
//===----------------------------------------------------------------------===//

inline void registerFuncToGraph() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return CreateFuncToGraphPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerFuncToGraphPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return CreateFuncToGraphPass();
  });
}

//===----------------------------------------------------------------------===//
// FunctionalToRegion Registration
//===----------------------------------------------------------------------===//

inline void registerFunctionalToRegion() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return CreateFunctionalToRegionPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerFunctionalToRegionPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return CreateFunctionalToRegionPass();
  });
}

//===----------------------------------------------------------------------===//
// GraphToFunc Registration
//===----------------------------------------------------------------------===//

inline void registerGraphToFunc() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return CreateGraphToFuncPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerGraphToFuncPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return CreateGraphToFuncPass();
  });
}

//===----------------------------------------------------------------------===//
// LiftLegacyCall Registration
//===----------------------------------------------------------------------===//

inline void registerLiftLegacyCall() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return CreateLiftLegacyCallPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerLiftLegacyCallPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return CreateLiftLegacyCallPass();
  });
}

//===----------------------------------------------------------------------===//
// NameCompress Registration
//===----------------------------------------------------------------------===//

inline void registerNameCompress() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return CreateNameCompressPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerNameCompressPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return CreateNameCompressPass();
  });
}

//===----------------------------------------------------------------------===//
// PrepareAttributesForExport Registration
//===----------------------------------------------------------------------===//

inline void registerPrepareAttributesForExport() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return CreatePrepareAttributesForExportPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerPrepareAttributesForExportPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return CreatePrepareAttributesForExportPass();
  });
}

//===----------------------------------------------------------------------===//
// RegionToFunctional Registration
//===----------------------------------------------------------------------===//

inline void registerRegionToFunctional() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return CreateRegionToFunctionalPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerRegionToFunctionalPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return CreateRegionToFunctionalPass();
  });
}

//===----------------------------------------------------------------------===//
// Remapper Registration
//===----------------------------------------------------------------------===//

inline void registerRemapper() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return CreateRemapperPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerRemapperPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return CreateRemapperPass();
  });
}

//===----------------------------------------------------------------------===//
// ShapeInference Registration
//===----------------------------------------------------------------------===//

inline void registerShapeInference() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return CreateShapeInferencePass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerShapeInferencePass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return CreateShapeInferencePass();
  });
}

//===----------------------------------------------------------------------===//
// StripDefaultAttrs Registration
//===----------------------------------------------------------------------===//

inline void registerStripDefaultAttrs() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return CreateStripDefaultAttrsPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerStripDefaultAttrsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return CreateStripDefaultAttrsPass();
  });
}

//===----------------------------------------------------------------------===//
// TopoSort Registration
//===----------------------------------------------------------------------===//

inline void registerTopoSort() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return CreateTopoSortPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerTopoSortPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return CreateTopoSortPass();
  });
}

//===----------------------------------------------------------------------===//
// TFGraph Registration
//===----------------------------------------------------------------------===//

inline void registerTFGraphPasses() {
  registerAddDefaultAttrs();
  registerCSEPass();
  registerConsolidateAttributes();
  registerConstantFoldingPass();
  registerControlFlowSink();
  registerDedupeAndHoistConstant();
  registerDropOutputShapesAttr();
  registerEliminatePassthroughIterArgs();
  registerFuncToGraph();
  registerFunctionalToRegion();
  registerGraphToFunc();
  registerLiftLegacyCall();
  registerNameCompress();
  registerPrepareAttributesForExport();
  registerRegionToFunctional();
  registerRemapper();
  registerShapeInference();
  registerStripDefaultAttrs();
  registerTopoSort();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
// Deprecated. Please use the new per-pass macros.
#ifdef GEN_PASS_CLASSES

template <typename DerivedT>
class AddDefaultAttrsBase : public ::mlir::OperationPass<> {
public:
  using Base = AddDefaultAttrsBase;

  AddDefaultAttrsBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  AddDefaultAttrsBase(const AddDefaultAttrsBase &other) : ::mlir::OperationPass<>(other) {}
  AddDefaultAttrsBase& operator=(const AddDefaultAttrsBase &) = delete;
  AddDefaultAttrsBase(AddDefaultAttrsBase &&) = delete;
  AddDefaultAttrsBase& operator=(AddDefaultAttrsBase &&) = delete;
  ~AddDefaultAttrsBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tfg-add-default-attrs");
  }
  ::llvm::StringRef getArgument() const override { return "tfg-add-default-attrs"; }

  ::llvm::StringRef getDescription() const override { return "Add default-valued attributes to the graph"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AddDefaultAttrs");
  }
  ::llvm::StringRef getName() const override { return "AddDefaultAttrs"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(AddDefaultAttrsBase<DerivedT>)

protected:
};

template <typename DerivedT>
class CSEPassBase : public ::mlir::OperationPass<GraphFuncOp> {
public:
  using Base = CSEPassBase;

  CSEPassBase() : ::mlir::OperationPass<GraphFuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  CSEPassBase(const CSEPassBase &other) : ::mlir::OperationPass<GraphFuncOp>(other) {}
  CSEPassBase& operator=(const CSEPassBase &) = delete;
  CSEPassBase(CSEPassBase &&) = delete;
  CSEPassBase& operator=(CSEPassBase &&) = delete;
  ~CSEPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tfg-cse");
  }
  ::llvm::StringRef getArgument() const override { return "tfg-cse"; }

  ::llvm::StringRef getDescription() const override { return "Common sub-expression elimination, ignoring op names"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("CSEPass");
  }
  ::llvm::StringRef getName() const override { return "CSEPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(CSEPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ConsolidateAttributesBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConsolidateAttributesBase;

  ConsolidateAttributesBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConsolidateAttributesBase(const ConsolidateAttributesBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  ConsolidateAttributesBase& operator=(const ConsolidateAttributesBase &) = delete;
  ConsolidateAttributesBase(ConsolidateAttributesBase &&) = delete;
  ConsolidateAttributesBase& operator=(ConsolidateAttributesBase &&) = delete;
  ~ConsolidateAttributesBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tfg-consolidate-attrs");
  }
  ::llvm::StringRef getArgument() const override { return "tfg-consolidate-attrs"; }

  ::llvm::StringRef getDescription() const override { return "Reify type data from attributes to types."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConsolidateAttributes");
  }
  ::llvm::StringRef getName() const override { return "ConsolidateAttributes"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConsolidateAttributesBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ConstantFoldingPassBase : public ::mlir::OperationPass<GraphFuncOp> {
public:
  using Base = ConstantFoldingPassBase;

  ConstantFoldingPassBase() : ::mlir::OperationPass<GraphFuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConstantFoldingPassBase(const ConstantFoldingPassBase &other) : ::mlir::OperationPass<GraphFuncOp>(other) {}
  ConstantFoldingPassBase& operator=(const ConstantFoldingPassBase &) = delete;
  ConstantFoldingPassBase(ConstantFoldingPassBase &&) = delete;
  ConstantFoldingPassBase& operator=(ConstantFoldingPassBase &&) = delete;
  ~ConstantFoldingPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tfg-constant-folding");
  }
  ::llvm::StringRef getArgument() const override { return "tfg-constant-folding"; }

  ::llvm::StringRef getDescription() const override { return "constant-folding on tfg"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConstantFoldingPass");
  }
  ::llvm::StringRef getName() const override { return "ConstantFoldingPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConstantFoldingPassBase<DerivedT>)

protected:
  ::mlir::Pass::ListOption<std::string> feeds_{*this, "feeds", ::llvm::cl::desc("Comma separated list of feed ops."), llvm::cl::ZeroOrMore};
  ::mlir::Pass::ListOption<std::string> fetches_{*this, "fetches", ::llvm::cl::desc("Comma separated list of fetch ops."), llvm::cl::ZeroOrMore};
  ::mlir::Pass::Option<bool> disable_compressed_tensor_optimization_{*this, "disable-compressed-tensor-optimization", ::llvm::cl::desc("Determine if we should disable compressed tensor optimization"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> fold_quantization_emulation_{*this, "fold-quantization-emulation", ::llvm::cl::desc("Determine if we should fold quantization emulation ops"), ::llvm::cl::init(true)};
  ::mlir::Pass::Option<int> pattern_category_{*this, "pattern-category", ::llvm::cl::desc("Select the pattern kind that we would like to run:0 = all patterns, 1 = folder patterns, 2 = propagation patterns"), ::llvm::cl::init(0)};
};

template <typename DerivedT>
class ControlFlowSinkBase : public ::mlir::OperationPass<GraphFuncOp> {
public:
  using Base = ControlFlowSinkBase;

  ControlFlowSinkBase() : ::mlir::OperationPass<GraphFuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  ControlFlowSinkBase(const ControlFlowSinkBase &other) : ::mlir::OperationPass<GraphFuncOp>(other) {}
  ControlFlowSinkBase& operator=(const ControlFlowSinkBase &) = delete;
  ControlFlowSinkBase(ControlFlowSinkBase &&) = delete;
  ControlFlowSinkBase& operator=(ControlFlowSinkBase &&) = delete;
  ~ControlFlowSinkBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tfg-cf-sink");
  }
  ::llvm::StringRef getArgument() const override { return "tfg-cf-sink"; }

  ::llvm::StringRef getDescription() const override { return "Perform control-flow sink on region-based control-flow ops."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ControlFlowSink");
  }
  ::llvm::StringRef getName() const override { return "ControlFlowSink"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ControlFlowSinkBase<DerivedT>)

protected:
  ::mlir::Pass::Statistic num_sunk{this, "num-sunk", "Number of operations sunk"};
};

template <typename DerivedT>
class DedupeAndHoistConstantBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = DedupeAndHoistConstantBase;

  DedupeAndHoistConstantBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  DedupeAndHoistConstantBase(const DedupeAndHoistConstantBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  DedupeAndHoistConstantBase& operator=(const DedupeAndHoistConstantBase &) = delete;
  DedupeAndHoistConstantBase(DedupeAndHoistConstantBase &&) = delete;
  DedupeAndHoistConstantBase& operator=(DedupeAndHoistConstantBase &&) = delete;
  ~DedupeAndHoistConstantBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tfg-dedupe-hoist-constant");
  }
  ::llvm::StringRef getArgument() const override { return "tfg-dedupe-hoist-constant"; }

  ::llvm::StringRef getDescription() const override { return "Dedupe and hoist constants"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("DedupeAndHoistConstant");
  }
  ::llvm::StringRef getName() const override { return "DedupeAndHoistConstant"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(DedupeAndHoistConstantBase<DerivedT>)

protected:
  ::mlir::Pass::Option<int64_t> max_size_{*this, "max-size", ::llvm::cl::desc("The maximum number of elements when considering whether a constant is small"), ::llvm::cl::init(10)};
  ::mlir::Pass::Option<bool> assume_strict_calls_{*this, "assume-strict-calls", ::llvm::cl::desc("Assume all function calls are stricts, that is operands are evaluated prior to the call"), ::llvm::cl::init(false)};
};

template <typename DerivedT>
class DropOutputShapesAttrBase : public ::mlir::OperationPass<> {
public:
  using Base = DropOutputShapesAttrBase;

  DropOutputShapesAttrBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  DropOutputShapesAttrBase(const DropOutputShapesAttrBase &other) : ::mlir::OperationPass<>(other) {}
  DropOutputShapesAttrBase& operator=(const DropOutputShapesAttrBase &) = delete;
  DropOutputShapesAttrBase(DropOutputShapesAttrBase &&) = delete;
  DropOutputShapesAttrBase& operator=(DropOutputShapesAttrBase &&) = delete;
  ~DropOutputShapesAttrBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tfg-drop-unregistered-output-shapes");
  }
  ::llvm::StringRef getArgument() const override { return "tfg-drop-unregistered-output-shapes"; }

  ::llvm::StringRef getDescription() const override { return "Drop _output_shapes attribute"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("DropOutputShapesAttr");
  }
  ::llvm::StringRef getName() const override { return "DropOutputShapesAttr"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(DropOutputShapesAttrBase<DerivedT>)

protected:
  ::mlir::Pass::ListOption<std::string> skip_{*this, "skip", ::llvm::cl::desc("Comma separated list of ops that will be skipped.")};
};

template <typename DerivedT>
class EliminatePassthroughIterArgsBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = EliminatePassthroughIterArgsBase;

  EliminatePassthroughIterArgsBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  EliminatePassthroughIterArgsBase(const EliminatePassthroughIterArgsBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  EliminatePassthroughIterArgsBase& operator=(const EliminatePassthroughIterArgsBase &) = delete;
  EliminatePassthroughIterArgsBase(EliminatePassthroughIterArgsBase &&) = delete;
  EliminatePassthroughIterArgsBase& operator=(EliminatePassthroughIterArgsBase &&) = delete;
  ~EliminatePassthroughIterArgsBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tfg-eliminate-passthrough-iter-args");
  }
  ::llvm::StringRef getArgument() const override { return "tfg-eliminate-passthrough-iter-args"; }

  ::llvm::StringRef getDescription() const override { return "Eliminate passthrough loop iteration arguments."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("EliminatePassthroughIterArgs");
  }
  ::llvm::StringRef getName() const override { return "EliminatePassthroughIterArgs"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<tfg::TFGraphDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(EliminatePassthroughIterArgsBase<DerivedT>)

protected:
};

template <typename DerivedT>
class FuncToGraphBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = FuncToGraphBase;

  FuncToGraphBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  FuncToGraphBase(const FuncToGraphBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  FuncToGraphBase& operator=(const FuncToGraphBase &) = delete;
  FuncToGraphBase(FuncToGraphBase &&) = delete;
  FuncToGraphBase& operator=(FuncToGraphBase &&) = delete;
  ~FuncToGraphBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tfg-lower-func-to-graph");
  }
  ::llvm::StringRef getArgument() const override { return "tfg-lower-func-to-graph"; }

  ::llvm::StringRef getDescription() const override { return "Turns a function back to a graph"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("FuncToGraph");
  }
  ::llvm::StringRef getName() const override { return "FuncToGraph"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(FuncToGraphBase<DerivedT>)

protected:
};

template <typename DerivedT>
class FunctionalToRegionBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = FunctionalToRegionBase;

  FunctionalToRegionBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  FunctionalToRegionBase(const FunctionalToRegionBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  FunctionalToRegionBase& operator=(const FunctionalToRegionBase &) = delete;
  FunctionalToRegionBase(FunctionalToRegionBase &&) = delete;
  FunctionalToRegionBase& operator=(FunctionalToRegionBase &&) = delete;
  ~FunctionalToRegionBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tfg-functional-to-region");
  }
  ::llvm::StringRef getArgument() const override { return "tfg-functional-to-region"; }

  ::llvm::StringRef getDescription() const override { return "Convert functional control-flow ops to region-based."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("FunctionalToRegion");
  }
  ::llvm::StringRef getName() const override { return "FunctionalToRegion"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<tfg::TFGraphDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(FunctionalToRegionBase<DerivedT>)

protected:
};

template <typename DerivedT>
class GraphToFuncBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = GraphToFuncBase;

  GraphToFuncBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  GraphToFuncBase(const GraphToFuncBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  GraphToFuncBase& operator=(const GraphToFuncBase &) = delete;
  GraphToFuncBase(GraphToFuncBase &&) = delete;
  GraphToFuncBase& operator=(GraphToFuncBase &&) = delete;
  ~GraphToFuncBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tfg-lift-graph-to-func");
  }
  ::llvm::StringRef getArgument() const override { return "tfg-lift-graph-to-func"; }

  ::llvm::StringRef getDescription() const override { return "Turns a graph into a function."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("GraphToFunc");
  }
  ::llvm::StringRef getName() const override { return "GraphToFunc"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(GraphToFuncBase<DerivedT>)

protected:
  ::mlir::Pass::ListOption<std::string> feeds_{*this, "feeds", ::llvm::cl::desc("Comma separated list of ops that will be turned into arguments.")};
  ::mlir::Pass::ListOption<std::string> fetches_{*this, "fetches", ::llvm::cl::desc("Comma separated list of ops that will be turned into results.")};
  ::mlir::Pass::ListOption<std::string> control_rets_{*this, "control_rets", ::llvm::cl::desc("Comma separated list of ops that will be turned into control returned.")};
};

template <typename DerivedT>
class LiftLegacyCallBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = LiftLegacyCallBase;

  LiftLegacyCallBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  LiftLegacyCallBase(const LiftLegacyCallBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  LiftLegacyCallBase& operator=(const LiftLegacyCallBase &) = delete;
  LiftLegacyCallBase(LiftLegacyCallBase &&) = delete;
  LiftLegacyCallBase& operator=(LiftLegacyCallBase &&) = delete;
  ~LiftLegacyCallBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tfg-lift-legacy-call");
  }
  ::llvm::StringRef getArgument() const override { return "tfg-lift-legacy-call"; }

  ::llvm::StringRef getDescription() const override { return "Tag legacy calls with symbol references to add symbol uses"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LiftLegacyCall");
  }
  ::llvm::StringRef getName() const override { return "LiftLegacyCall"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LiftLegacyCallBase<DerivedT>)

protected:
};

template <typename DerivedT>
class NameCompressBase : public ::mlir::OperationPass<GraphFuncOp> {
public:
  using Base = NameCompressBase;

  NameCompressBase() : ::mlir::OperationPass<GraphFuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  NameCompressBase(const NameCompressBase &other) : ::mlir::OperationPass<GraphFuncOp>(other) {}
  NameCompressBase& operator=(const NameCompressBase &) = delete;
  NameCompressBase(NameCompressBase &&) = delete;
  NameCompressBase& operator=(NameCompressBase &&) = delete;
  ~NameCompressBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tfg-name-compress");
  }
  ::llvm::StringRef getArgument() const override { return "tfg-name-compress"; }

  ::llvm::StringRef getDescription() const override { return "Compress the graph by shortening names"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("NameCompress");
  }
  ::llvm::StringRef getName() const override { return "NameCompress"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(NameCompressBase<DerivedT>)

protected:
};

template <typename DerivedT>
class PrepareAttributesForExportBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = PrepareAttributesForExportBase;

  PrepareAttributesForExportBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  PrepareAttributesForExportBase(const PrepareAttributesForExportBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  PrepareAttributesForExportBase& operator=(const PrepareAttributesForExportBase &) = delete;
  PrepareAttributesForExportBase(PrepareAttributesForExportBase &&) = delete;
  PrepareAttributesForExportBase& operator=(PrepareAttributesForExportBase &&) = delete;
  ~PrepareAttributesForExportBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tfg-prepare-attrs-export");
  }
  ::llvm::StringRef getArgument() const override { return "tfg-prepare-attrs-export"; }

  ::llvm::StringRef getDescription() const override { return "Legalize ops' attributes for export."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("PrepareAttributesForExport");
  }
  ::llvm::StringRef getName() const override { return "PrepareAttributesForExport"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(PrepareAttributesForExportBase<DerivedT>)

protected:
};

template <typename DerivedT>
class RegionToFunctionalBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = RegionToFunctionalBase;

  RegionToFunctionalBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  RegionToFunctionalBase(const RegionToFunctionalBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  RegionToFunctionalBase& operator=(const RegionToFunctionalBase &) = delete;
  RegionToFunctionalBase(RegionToFunctionalBase &&) = delete;
  RegionToFunctionalBase& operator=(RegionToFunctionalBase &&) = delete;
  ~RegionToFunctionalBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tfg-region-to-functional");
  }
  ::llvm::StringRef getArgument() const override { return "tfg-region-to-functional"; }

  ::llvm::StringRef getDescription() const override { return "Convert region-based control-flow ops to functional."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("RegionToFunctional");
  }
  ::llvm::StringRef getName() const override { return "RegionToFunctional"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<tfg::TFGraphDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(RegionToFunctionalBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> force_control_capture{*this, "force-control-capture", ::llvm::cl::desc("Force the capture of control tokens by inserting chain `Const` ops"), ::llvm::cl::init(false)};
};

template <typename DerivedT>
class RemapperBase : public ::mlir::OperationPass<> {
public:
  using Base = RemapperBase;

  RemapperBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  RemapperBase(const RemapperBase &other) : ::mlir::OperationPass<>(other) {}
  RemapperBase& operator=(const RemapperBase &) = delete;
  RemapperBase(RemapperBase &&) = delete;
  RemapperBase& operator=(RemapperBase &&) = delete;
  ~RemapperBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tfg-remapper");
  }
  ::llvm::StringRef getArgument() const override { return "tfg-remapper"; }

  ::llvm::StringRef getDescription() const override { return "Remap operations to decrease amount of operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("Remapper");
  }
  ::llvm::StringRef getName() const override { return "Remapper"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(RemapperBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> enable_onednn_patterns_{*this, "enable-onednn-patterns", ::llvm::cl::desc("Enable the oneDNN related patterns."), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> verify_pdll_patterns_only_{*this, "verify-pdll-patterns-only", ::llvm::cl::desc("Only enable PDLL patterns."), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> xla_auto_clustering_{*this, "xla-auto-clustering", ::llvm::cl::desc("Sets XLA auto clustering flag."), ::llvm::cl::init(false)};
};

template <typename DerivedT>
class ShapeInferenceBase : public ::mlir::OperationPass<> {
public:
  using Base = ShapeInferenceBase;

  ShapeInferenceBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  ShapeInferenceBase(const ShapeInferenceBase &other) : ::mlir::OperationPass<>(other) {}
  ShapeInferenceBase& operator=(const ShapeInferenceBase &) = delete;
  ShapeInferenceBase(ShapeInferenceBase &&) = delete;
  ShapeInferenceBase& operator=(ShapeInferenceBase &&) = delete;
  ~ShapeInferenceBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tfg-shape-inference");
  }
  ::llvm::StringRef getArgument() const override { return "tfg-shape-inference"; }

  ::llvm::StringRef getDescription() const override { return "Infer the output shape of operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ShapeInference");
  }
  ::llvm::StringRef getName() const override { return "ShapeInference"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ShapeInferenceBase<DerivedT>)

protected:
  ::mlir::Pass::Option<int> graph_version_{*this, "graph-version", ::llvm::cl::desc("The graph producer version")};
};

template <typename DerivedT>
class StripDefaultAttrsBase : public ::mlir::OperationPass<> {
public:
  using Base = StripDefaultAttrsBase;

  StripDefaultAttrsBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  StripDefaultAttrsBase(const StripDefaultAttrsBase &other) : ::mlir::OperationPass<>(other) {}
  StripDefaultAttrsBase& operator=(const StripDefaultAttrsBase &) = delete;
  StripDefaultAttrsBase(StripDefaultAttrsBase &&) = delete;
  StripDefaultAttrsBase& operator=(StripDefaultAttrsBase &&) = delete;
  ~StripDefaultAttrsBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tfg-strip-default-attrs");
  }
  ::llvm::StringRef getArgument() const override { return "tfg-strip-default-attrs"; }

  ::llvm::StringRef getDescription() const override { return "Removes default-valued attributes from the graph"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("StripDefaultAttrs");
  }
  ::llvm::StringRef getName() const override { return "StripDefaultAttrs"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(StripDefaultAttrsBase<DerivedT>)

protected:
};

template <typename DerivedT>
class TopoSortBase : public ::mlir::OperationPass<> {
public:
  using Base = TopoSortBase;

  TopoSortBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  TopoSortBase(const TopoSortBase &other) : ::mlir::OperationPass<>(other) {}
  TopoSortBase& operator=(const TopoSortBase &) = delete;
  TopoSortBase(TopoSortBase &&) = delete;
  TopoSortBase& operator=(TopoSortBase &&) = delete;
  ~TopoSortBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tfg-toposort");
  }
  ::llvm::StringRef getArgument() const override { return "tfg-toposort"; }

  ::llvm::StringRef getDescription() const override { return "Topologically sort graph and function regions"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TopoSort");
  }
  ::llvm::StringRef getName() const override { return "TopoSort"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TopoSortBase<DerivedT>)

protected:
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
