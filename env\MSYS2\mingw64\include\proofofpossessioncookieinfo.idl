/*
 * Copyright 2021 <PERSON><PERSON> for CodeWeavers
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

import "oaidl.idl";
import "ocidl.idl";

typedef struct ProofOfPossessionCookieInfo
{
    LPWSTR  name;
    LPWSTR  data;
    DWORD   flags;
    LPWSTR  p3pHeader;
} ProofOfPossessionCookieInfo;


cpp_quote("static inline void FreeProofOfPossessionCookieInfoArray(ProofOfPossessionCookieInfo *cookie_info, DWORD count)")
cpp_quote("{")
cpp_quote("    unsigned int i;")
cpp_quote("    for (i = 0; i < count; i++)")
cpp_quote("    {")
cpp_quote("        CoTaskMemFree(cookie_info[i].name);")
cpp_quote("        CoTaskMemFree(cookie_info[i].data);")
cpp_quote("        CoTaskMemFree(cookie_info[i].p3pHeader);")
cpp_quote("    }")
cpp_quote("    CoTaskMemFree(cookie_info);")
cpp_quote("}")


[
    object,
    uuid(cdaece56-4edf-43df-b113-88e4556fa1bb),
    pointer_default(unique)
]
interface IProofOfPossessionCookieInfoManager : IUnknown
{
    HRESULT GetCookieInfoForUri([in] LPCWSTR uri,
                                [out] DWORD *cookieInfoCount,
                                [out, size_is(,*cookieInfoCount)] ProofOfPossessionCookieInfo **cookieInfo);
}


[
    uuid(7681a019-8f51-4594-9507-f27040f71f01),
    version(1.0)
]
library ProofOfPossessionCookieInfoManagerLib
{
    [
        uuid(a9927f85-a304-4390-8b23-a75f1c668600)
    ]
    coclass ProofOfPossessionCookieInfoManager
    {
        [default] interface IProofOfPossessionCookieInfoManager;
    }
}
