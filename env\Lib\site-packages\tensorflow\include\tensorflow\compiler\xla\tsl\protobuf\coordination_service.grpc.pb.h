// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: xla/tsl/protobuf/coordination_service.proto
#ifndef GRPC_xla_2ftsl_2fprotobuf_2fcoordination_5fservice_2eproto__INCLUDED
#define GRPC_xla_2ftsl_2fprotobuf_2fcoordination_5fservice_2eproto__INCLUDED

#include "xla/tsl/protobuf/coordination_service.pb.h"

#include <functional>
#include <grpc/impl/codegen/port_platform.h>
#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/client_context.h>
#include <grpcpp/impl/codegen/completion_queue.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace tensorflow {


namespace grpc {

// Coordination Service defines a TensorFlow service that controls and
// coordinates distributed execution in a cluster of multiple tasks.
//
// The service keeps track of the cluster configuration and the state of cluster
// members or the leader depending on the role of the current task. The
// distributed runtime leverages this service to coordinate and perform cluster
// initialization, check the healthiness of tasks, and propagate error
// messages to the cluster.
class CoordinationService final {
 public:
  static constexpr char const* service_full_name() {
    return "tensorflow.CoordinationService";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    // Register task to coordination service so that the service starts to track
    // liveness of the task. RPC blocks and returns only when it registers to
    // the service successfully, or error happens in the registering process.
    virtual ::grpc::Status RegisterTask(::grpc::ClientContext* context, const ::tensorflow::RegisterTaskRequest& request, ::tensorflow::RegisterTaskResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::RegisterTaskResponse>> AsyncRegisterTask(::grpc::ClientContext* context, const ::tensorflow::RegisterTaskRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::RegisterTaskResponse>>(AsyncRegisterTaskRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::RegisterTaskResponse>> PrepareAsyncRegisterTask(::grpc::ClientContext* context, const ::tensorflow::RegisterTaskRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::RegisterTaskResponse>>(PrepareAsyncRegisterTaskRaw(context, request, cq));
    }
    // [AUTOMATION]: Internal rpc option goes here.
    // Heartbeat message from task to coordination service. Heartbeat is sent from
    // a task to refresh its timestamp on leader to avoid it becoming stale.
    // RPC responds immediately after refreshing the timestamp on leader.
    virtual ::grpc::Status Heartbeat(::grpc::ClientContext* context, const ::tensorflow::HeartbeatRequest& request, ::tensorflow::HeartbeatResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::HeartbeatResponse>> AsyncHeartbeat(::grpc::ClientContext* context, const ::tensorflow::HeartbeatRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::HeartbeatResponse>>(AsyncHeartbeatRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::HeartbeatResponse>> PrepareAsyncHeartbeat(::grpc::ClientContext* context, const ::tensorflow::HeartbeatRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::HeartbeatResponse>>(PrepareAsyncHeartbeatRaw(context, request, cq));
    }
    // [AUTOMATION]: Internal rpc option goes here.
    // Wait for all tasks in the cluster to be up and running. The RPC request
    // only gets responded when all tasks have registered, or some error occurs.
    virtual ::grpc::Status WaitForAllTasks(::grpc::ClientContext* context, const ::tensorflow::WaitForAllTasksRequest& request, ::tensorflow::WaitForAllTasksResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::WaitForAllTasksResponse>> AsyncWaitForAllTasks(::grpc::ClientContext* context, const ::tensorflow::WaitForAllTasksRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::WaitForAllTasksResponse>>(AsyncWaitForAllTasksRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::WaitForAllTasksResponse>> PrepareAsyncWaitForAllTasks(::grpc::ClientContext* context, const ::tensorflow::WaitForAllTasksRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::WaitForAllTasksResponse>>(PrepareAsyncWaitForAllTasksRaw(context, request, cq));
    }
    // Disconnects task from the service. If `shutdown_barrier_timeout_in_ms` is
    // specified in the config, blocks until all tasks reach the barrier before
    // disconnecting together. If the barrier times out, tasks at the barrier will
    // still disconnect, while an error is reported to tasks that did not reach
    // the barrier on time.
    virtual ::grpc::Status ShutdownTask(::grpc::ClientContext* context, const ::tensorflow::ShutdownTaskRequest& request, ::tensorflow::ShutdownTaskResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::ShutdownTaskResponse>> AsyncShutdownTask(::grpc::ClientContext* context, const ::tensorflow::ShutdownTaskRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::ShutdownTaskResponse>>(AsyncShutdownTaskRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::ShutdownTaskResponse>> PrepareAsyncShutdownTask(::grpc::ClientContext* context, const ::tensorflow::ShutdownTaskRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::ShutdownTaskResponse>>(PrepareAsyncShutdownTaskRaw(context, request, cq));
    }
    // [AUTOMATION]: Internal rpc option goes here.
    // Disconnects task from the service if it is in an ERROR state, thereby
    // allowing it to reconnect via RegisterTask() in the future.
    virtual ::grpc::Status ResetTask(::grpc::ClientContext* context, const ::tensorflow::ResetTaskRequest& request, ::tensorflow::ResetTaskResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::ResetTaskResponse>> AsyncResetTask(::grpc::ClientContext* context, const ::tensorflow::ResetTaskRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::ResetTaskResponse>>(AsyncResetTaskRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::ResetTaskResponse>> PrepareAsyncResetTask(::grpc::ClientContext* context, const ::tensorflow::ResetTaskRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::ResetTaskResponse>>(PrepareAsyncResetTaskRaw(context, request, cq));
    }
    // Report error to the task. RPC sets the receiving instance of coordination
    // service agent to error state permanently.
    // TODO(b/195990880): Consider splitting this into a different RPC service.
    virtual ::grpc::Status ReportErrorToTask(::grpc::ClientContext* context, const ::tensorflow::ReportErrorToTaskRequest& request, ::tensorflow::ReportErrorToTaskResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::ReportErrorToTaskResponse>> AsyncReportErrorToTask(::grpc::ClientContext* context, const ::tensorflow::ReportErrorToTaskRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::ReportErrorToTaskResponse>>(AsyncReportErrorToTaskRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::ReportErrorToTaskResponse>> PrepareAsyncReportErrorToTask(::grpc::ClientContext* context, const ::tensorflow::ReportErrorToTaskRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::ReportErrorToTaskResponse>>(PrepareAsyncReportErrorToTaskRaw(context, request, cq));
    }
    // Report task error to coordination service. RPC sets the service-side task
    // state to error, and propagate the error to other tasks in the cluster.
    virtual ::grpc::Status ReportErrorToService(::grpc::ClientContext* context, const ::tensorflow::ReportErrorToServiceRequest& request, ::tensorflow::ReportErrorToServiceResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::ReportErrorToServiceResponse>> AsyncReportErrorToService(::grpc::ClientContext* context, const ::tensorflow::ReportErrorToServiceRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::ReportErrorToServiceResponse>>(AsyncReportErrorToServiceRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::ReportErrorToServiceResponse>> PrepareAsyncReportErrorToService(::grpc::ClientContext* context, const ::tensorflow::ReportErrorToServiceRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::ReportErrorToServiceResponse>>(PrepareAsyncReportErrorToServiceRaw(context, request, cq));
    }
    // Get the state of a remote task. Specifically, RPC returns a
    // CoordinatedTaskState, and if the task is in an error status, returns a
    // non-OK error code, non-empty error message and error payload.
    virtual ::grpc::Status GetTaskState(::grpc::ClientContext* context, const ::tensorflow::GetTaskStateRequest& request, ::tensorflow::GetTaskStateResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::GetTaskStateResponse>> AsyncGetTaskState(::grpc::ClientContext* context, const ::tensorflow::GetTaskStateRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::GetTaskStateResponse>>(AsyncGetTaskStateRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::GetTaskStateResponse>> PrepareAsyncGetTaskState(::grpc::ClientContext* context, const ::tensorflow::GetTaskStateRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::GetTaskStateResponse>>(PrepareAsyncGetTaskStateRaw(context, request, cq));
    }
    // Insert configuration key-value that will be accessible to all cluster
    // tasks. The key can be formatted as Unix file path with hierarchy. The
    // coordination service key-value store should only be used for cluster
    // configuration data.
    virtual ::grpc::Status InsertKeyValue(::grpc::ClientContext* context, const ::tensorflow::InsertKeyValueRequest& request, ::tensorflow::InsertKeyValueResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::InsertKeyValueResponse>> AsyncInsertKeyValue(::grpc::ClientContext* context, const ::tensorflow::InsertKeyValueRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::InsertKeyValueResponse>>(AsyncInsertKeyValueRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::InsertKeyValueResponse>> PrepareAsyncInsertKeyValue(::grpc::ClientContext* context, const ::tensorflow::InsertKeyValueRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::InsertKeyValueResponse>>(PrepareAsyncInsertKeyValueRaw(context, request, cq));
    }
    // [AUTOMATION]: Internal rpc option goes here.
    // Get configuration key-value. The request blocks until the key-value data
    // becomes available (i.e., set by a task in the cluster).
    virtual ::grpc::Status GetKeyValue(::grpc::ClientContext* context, const ::tensorflow::GetKeyValueRequest& request, ::tensorflow::GetKeyValueResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::GetKeyValueResponse>> AsyncGetKeyValue(::grpc::ClientContext* context, const ::tensorflow::GetKeyValueRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::GetKeyValueResponse>>(AsyncGetKeyValueRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::GetKeyValueResponse>> PrepareAsyncGetKeyValue(::grpc::ClientContext* context, const ::tensorflow::GetKeyValueRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::GetKeyValueResponse>>(PrepareAsyncGetKeyValueRaw(context, request, cq));
    }
    // [AUTOMATION]: Internal rpc option goes here.
    // Get configuration key-value. The request does not block, but returns an
    // error if the requested key does not exist.
    virtual ::grpc::Status TryGetKeyValue(::grpc::ClientContext* context, const ::tensorflow::TryGetKeyValueRequest& request, ::tensorflow::TryGetKeyValueResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::TryGetKeyValueResponse>> AsyncTryGetKeyValue(::grpc::ClientContext* context, const ::tensorflow::TryGetKeyValueRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::TryGetKeyValueResponse>>(AsyncTryGetKeyValueRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::TryGetKeyValueResponse>> PrepareAsyncTryGetKeyValue(::grpc::ClientContext* context, const ::tensorflow::TryGetKeyValueRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::TryGetKeyValueResponse>>(PrepareAsyncTryGetKeyValueRaw(context, request, cq));
    }
    // Same as GetKeyValue, but returns all values that have keys which are
    // prefixed with the directory key.
    virtual ::grpc::Status GetKeyValueDir(::grpc::ClientContext* context, const ::tensorflow::GetKeyValueDirRequest& request, ::tensorflow::GetKeyValueDirResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::GetKeyValueDirResponse>> AsyncGetKeyValueDir(::grpc::ClientContext* context, const ::tensorflow::GetKeyValueDirRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::GetKeyValueDirResponse>>(AsyncGetKeyValueDirRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::GetKeyValueDirResponse>> PrepareAsyncGetKeyValueDir(::grpc::ClientContext* context, const ::tensorflow::GetKeyValueDirRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::GetKeyValueDirResponse>>(PrepareAsyncGetKeyValueDirRaw(context, request, cq));
    }
    // [AUTOMATION]: Internal rpc option goes here.
    // Delete configuration key-value. If is_directory is set in request,
    // recursively clean up all key-values under the path specified by `key`.
    virtual ::grpc::Status DeleteKeyValue(::grpc::ClientContext* context, const ::tensorflow::DeleteKeyValueRequest& request, ::tensorflow::DeleteKeyValueResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::DeleteKeyValueResponse>> AsyncDeleteKeyValue(::grpc::ClientContext* context, const ::tensorflow::DeleteKeyValueRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::DeleteKeyValueResponse>>(AsyncDeleteKeyValueRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::DeleteKeyValueResponse>> PrepareAsyncDeleteKeyValue(::grpc::ClientContext* context, const ::tensorflow::DeleteKeyValueRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::DeleteKeyValueResponse>>(PrepareAsyncDeleteKeyValueRaw(context, request, cq));
    }
    // Blocks until all (or a subset of) tasks are at the barrier or the barrier
    // fails.
    //
    // `barrier_id` should be unique across barriers. Once the barrier has passed
    // or failed, subsequent calls will not block, and immediately respond with
    // the previous response.
    //
    // The first WaitAtBarrier() call received by the service for a particular
    // barrier id is special in that it determines the barrier deadline based on
    // timeout duration.
    // However, if subsequent calls by different agents specify a different set of
    // `tasks` for the same `barrier_id`, the barrier will fail instantly.
    //
    // If no tasks are specified (default), the barrier will block for all the
    // connected tasks.
    //
    // Possible service errors:
    //   - DeadlineExceeded: Timed out waiting for specified tasks at the barrier.
    //      Deadline is determined by the server timestamp when it receives the
    //      first WaitAtBarrier() + timeout duration.
    //   - Cancelled: One of the tasks called CancelBarrier().
    //   - Aborted: Service is shutting down.
    //   - Internal: Any participating task is in ERROR state.
    //   - InvalidArgument: (1) Conflicting tasks specified by different agents
    //       for the same barrier, (2) one of the participating tasks is not in
    //       the cluster, or (3) task making the request is not included in the
    //       list of participating tasks.
    virtual ::grpc::Status Barrier(::grpc::ClientContext* context, const ::tensorflow::BarrierRequest& request, ::tensorflow::BarrierResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::BarrierResponse>> AsyncBarrier(::grpc::ClientContext* context, const ::tensorflow::BarrierRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::BarrierResponse>>(AsyncBarrierRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::BarrierResponse>> PrepareAsyncBarrier(::grpc::ClientContext* context, const ::tensorflow::BarrierRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::BarrierResponse>>(PrepareAsyncBarrierRaw(context, request, cq));
    }
    // [AUTOMATION]: Internal rpc option goes here.
    // Aborts the barrier if it is ongoing.
    // Current and future WaitAtBarrier() calls with the same id will return a
    // CANCELLED error status.
    // Possible service errors:
    //   - FailedPrecondition: Barrier has already been passed.
    virtual ::grpc::Status CancelBarrier(::grpc::ClientContext* context, const ::tensorflow::CancelBarrierRequest& request, ::tensorflow::CancelBarrierResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::CancelBarrierResponse>> AsyncCancelBarrier(::grpc::ClientContext* context, const ::tensorflow::CancelBarrierRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::CancelBarrierResponse>>(AsyncCancelBarrierRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::CancelBarrierResponse>> PrepareAsyncCancelBarrier(::grpc::ClientContext* context, const ::tensorflow::CancelBarrierRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::CancelBarrierResponse>>(PrepareAsyncCancelBarrierRaw(context, request, cq));
    }
    // Returns the set of currently alive tasks. More specifically, given a set of
    // tasks T, GetAliveTasks(T) returns the subset T of alive tasks.
    //
    // # Barrier Semantics
    //
    // If multiple tasks call GetAliveTasks concurrently, it's important that they
    // all agree on which tasks are alive. Otherwise, the tasks' behavior might
    // diverge. For example, imagine a set of tasks trying to run an AllGather,
    // but they all disagree on which tasks should be participating in the
    // AllGather. This is buggy.
    //
    // To ensure that every task agrees on which tasks are alive, the
    // GetAliveTasks RPC has barrier-like semantics. Consider an invocation
    // GetAliveTasks(T) for a set of tasks T. The invocation acts as a barrier,
    // waiting for every task in T to call GetAliveTasks(T). Afterwards,
    // GetAliveTasks returns the same set of alive tasks A to all the tasks in T.
    // This ensures that every task agrees which tasks are alive.
    //
    // One small correction. GetAliveTasks doesn't act as a barrier for *every*
    // task in T. Some tasks in T might have failed, so we should not wait for
    // them. Instead, the GetAliveTasks RPC waits only for the returned tasks A.
    //
    // # An Example
    //
    // Imagine we have four tasks: A, B, C, and D. Further imagine that task D
    // has failed and that every task calls GetAliveTasks([A, B, C, D]). The
    // invocation will return tasks [A, B, C]. The GetAliveTasks call acts as a
    // barrier across tasks A, B, and C. Task D, which failed, is ignored.
    virtual ::grpc::Status GetAliveTasks(::grpc::ClientContext* context, const ::tensorflow::GetAliveTasksRequest& request, ::tensorflow::GetAliveTasksResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::GetAliveTasksResponse>> AsyncGetAliveTasks(::grpc::ClientContext* context, const ::tensorflow::GetAliveTasksRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::GetAliveTasksResponse>>(AsyncGetAliveTasksRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::GetAliveTasksResponse>> PrepareAsyncGetAliveTasks(::grpc::ClientContext* context, const ::tensorflow::GetAliveTasksRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::GetAliveTasksResponse>>(PrepareAsyncGetAliveTasksRaw(context, request, cq));
    }
    // Polls the service for errors.
    //
    // This RPC is used by the coordination service agent to send long polling
    // request to service for errors. The call will block until an error is
    // reported by the service.
    //
    // Possible service errors:
    //   - Aborted: Service is shutting down.
    virtual ::grpc::Status PollForError(::grpc::ClientContext* context, const ::tensorflow::PollForErrorRequest& request, ::tensorflow::PollForErrorResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::PollForErrorResponse>> AsyncPollForError(::grpc::ClientContext* context, const ::tensorflow::PollForErrorRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::PollForErrorResponse>>(AsyncPollForErrorRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::PollForErrorResponse>> PrepareAsyncPollForError(::grpc::ClientContext* context, const ::tensorflow::PollForErrorRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::PollForErrorResponse>>(PrepareAsyncPollForErrorRaw(context, request, cq));
    }
    // [AUTOMATION]: Internal rpc option goes here.
    class experimental_async_interface {
     public:
      virtual ~experimental_async_interface() {}
      // Register task to coordination service so that the service starts to track
      // liveness of the task. RPC blocks and returns only when it registers to
      // the service successfully, or error happens in the registering process.
      virtual void RegisterTask(::grpc::ClientContext* context, const ::tensorflow::RegisterTaskRequest* request, ::tensorflow::RegisterTaskResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void RegisterTask(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::RegisterTaskResponse* response, std::function<void(::grpc::Status)>) = 0;
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      virtual void RegisterTask(::grpc::ClientContext* context, const ::tensorflow::RegisterTaskRequest* request, ::tensorflow::RegisterTaskResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      #else
      virtual void RegisterTask(::grpc::ClientContext* context, const ::tensorflow::RegisterTaskRequest* request, ::tensorflow::RegisterTaskResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) = 0;
      #endif
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      virtual void RegisterTask(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::RegisterTaskResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      #else
      virtual void RegisterTask(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::RegisterTaskResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) = 0;
      #endif
      // [AUTOMATION]: Internal rpc option goes here.
      // Heartbeat message from task to coordination service. Heartbeat is sent from
      // a task to refresh its timestamp on leader to avoid it becoming stale.
      // RPC responds immediately after refreshing the timestamp on leader.
      virtual void Heartbeat(::grpc::ClientContext* context, const ::tensorflow::HeartbeatRequest* request, ::tensorflow::HeartbeatResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void Heartbeat(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::HeartbeatResponse* response, std::function<void(::grpc::Status)>) = 0;
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      virtual void Heartbeat(::grpc::ClientContext* context, const ::tensorflow::HeartbeatRequest* request, ::tensorflow::HeartbeatResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      #else
      virtual void Heartbeat(::grpc::ClientContext* context, const ::tensorflow::HeartbeatRequest* request, ::tensorflow::HeartbeatResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) = 0;
      #endif
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      virtual void Heartbeat(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::HeartbeatResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      #else
      virtual void Heartbeat(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::HeartbeatResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) = 0;
      #endif
      // [AUTOMATION]: Internal rpc option goes here.
      // Wait for all tasks in the cluster to be up and running. The RPC request
      // only gets responded when all tasks have registered, or some error occurs.
      virtual void WaitForAllTasks(::grpc::ClientContext* context, const ::tensorflow::WaitForAllTasksRequest* request, ::tensorflow::WaitForAllTasksResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void WaitForAllTasks(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::WaitForAllTasksResponse* response, std::function<void(::grpc::Status)>) = 0;
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      virtual void WaitForAllTasks(::grpc::ClientContext* context, const ::tensorflow::WaitForAllTasksRequest* request, ::tensorflow::WaitForAllTasksResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      #else
      virtual void WaitForAllTasks(::grpc::ClientContext* context, const ::tensorflow::WaitForAllTasksRequest* request, ::tensorflow::WaitForAllTasksResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) = 0;
      #endif
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      virtual void WaitForAllTasks(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::WaitForAllTasksResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      #else
      virtual void WaitForAllTasks(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::WaitForAllTasksResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) = 0;
      #endif
      // Disconnects task from the service. If `shutdown_barrier_timeout_in_ms` is
      // specified in the config, blocks until all tasks reach the barrier before
      // disconnecting together. If the barrier times out, tasks at the barrier will
      // still disconnect, while an error is reported to tasks that did not reach
      // the barrier on time.
      virtual void ShutdownTask(::grpc::ClientContext* context, const ::tensorflow::ShutdownTaskRequest* request, ::tensorflow::ShutdownTaskResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void ShutdownTask(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::ShutdownTaskResponse* response, std::function<void(::grpc::Status)>) = 0;
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      virtual void ShutdownTask(::grpc::ClientContext* context, const ::tensorflow::ShutdownTaskRequest* request, ::tensorflow::ShutdownTaskResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      #else
      virtual void ShutdownTask(::grpc::ClientContext* context, const ::tensorflow::ShutdownTaskRequest* request, ::tensorflow::ShutdownTaskResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) = 0;
      #endif
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      virtual void ShutdownTask(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::ShutdownTaskResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      #else
      virtual void ShutdownTask(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::ShutdownTaskResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) = 0;
      #endif
      // [AUTOMATION]: Internal rpc option goes here.
      // Disconnects task from the service if it is in an ERROR state, thereby
      // allowing it to reconnect via RegisterTask() in the future.
      virtual void ResetTask(::grpc::ClientContext* context, const ::tensorflow::ResetTaskRequest* request, ::tensorflow::ResetTaskResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void ResetTask(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::ResetTaskResponse* response, std::function<void(::grpc::Status)>) = 0;
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      virtual void ResetTask(::grpc::ClientContext* context, const ::tensorflow::ResetTaskRequest* request, ::tensorflow::ResetTaskResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      #else
      virtual void ResetTask(::grpc::ClientContext* context, const ::tensorflow::ResetTaskRequest* request, ::tensorflow::ResetTaskResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) = 0;
      #endif
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      virtual void ResetTask(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::ResetTaskResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      #else
      virtual void ResetTask(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::ResetTaskResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) = 0;
      #endif
      // Report error to the task. RPC sets the receiving instance of coordination
      // service agent to error state permanently.
      // TODO(b/195990880): Consider splitting this into a different RPC service.
      virtual void ReportErrorToTask(::grpc::ClientContext* context, const ::tensorflow::ReportErrorToTaskRequest* request, ::tensorflow::ReportErrorToTaskResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void ReportErrorToTask(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::ReportErrorToTaskResponse* response, std::function<void(::grpc::Status)>) = 0;
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      virtual void ReportErrorToTask(::grpc::ClientContext* context, const ::tensorflow::ReportErrorToTaskRequest* request, ::tensorflow::ReportErrorToTaskResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      #else
      virtual void ReportErrorToTask(::grpc::ClientContext* context, const ::tensorflow::ReportErrorToTaskRequest* request, ::tensorflow::ReportErrorToTaskResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) = 0;
      #endif
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      virtual void ReportErrorToTask(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::ReportErrorToTaskResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      #else
      virtual void ReportErrorToTask(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::ReportErrorToTaskResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) = 0;
      #endif
      // Report task error to coordination service. RPC sets the service-side task
      // state to error, and propagate the error to other tasks in the cluster.
      virtual void ReportErrorToService(::grpc::ClientContext* context, const ::tensorflow::ReportErrorToServiceRequest* request, ::tensorflow::ReportErrorToServiceResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void ReportErrorToService(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::ReportErrorToServiceResponse* response, std::function<void(::grpc::Status)>) = 0;
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      virtual void ReportErrorToService(::grpc::ClientContext* context, const ::tensorflow::ReportErrorToServiceRequest* request, ::tensorflow::ReportErrorToServiceResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      #else
      virtual void ReportErrorToService(::grpc::ClientContext* context, const ::tensorflow::ReportErrorToServiceRequest* request, ::tensorflow::ReportErrorToServiceResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) = 0;
      #endif
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      virtual void ReportErrorToService(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::ReportErrorToServiceResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      #else
      virtual void ReportErrorToService(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::ReportErrorToServiceResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) = 0;
      #endif
      // Get the state of a remote task. Specifically, RPC returns a
      // CoordinatedTaskState, and if the task is in an error status, returns a
      // non-OK error code, non-empty error message and error payload.
      virtual void GetTaskState(::grpc::ClientContext* context, const ::tensorflow::GetTaskStateRequest* request, ::tensorflow::GetTaskStateResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetTaskState(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::GetTaskStateResponse* response, std::function<void(::grpc::Status)>) = 0;
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      virtual void GetTaskState(::grpc::ClientContext* context, const ::tensorflow::GetTaskStateRequest* request, ::tensorflow::GetTaskStateResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      #else
      virtual void GetTaskState(::grpc::ClientContext* context, const ::tensorflow::GetTaskStateRequest* request, ::tensorflow::GetTaskStateResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) = 0;
      #endif
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      virtual void GetTaskState(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::GetTaskStateResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      #else
      virtual void GetTaskState(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::GetTaskStateResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) = 0;
      #endif
      // Insert configuration key-value that will be accessible to all cluster
      // tasks. The key can be formatted as Unix file path with hierarchy. The
      // coordination service key-value store should only be used for cluster
      // configuration data.
      virtual void InsertKeyValue(::grpc::ClientContext* context, const ::tensorflow::InsertKeyValueRequest* request, ::tensorflow::InsertKeyValueResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void InsertKeyValue(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::InsertKeyValueResponse* response, std::function<void(::grpc::Status)>) = 0;
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      virtual void InsertKeyValue(::grpc::ClientContext* context, const ::tensorflow::InsertKeyValueRequest* request, ::tensorflow::InsertKeyValueResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      #else
      virtual void InsertKeyValue(::grpc::ClientContext* context, const ::tensorflow::InsertKeyValueRequest* request, ::tensorflow::InsertKeyValueResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) = 0;
      #endif
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      virtual void InsertKeyValue(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::InsertKeyValueResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      #else
      virtual void InsertKeyValue(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::InsertKeyValueResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) = 0;
      #endif
      // [AUTOMATION]: Internal rpc option goes here.
      // Get configuration key-value. The request blocks until the key-value data
      // becomes available (i.e., set by a task in the cluster).
      virtual void GetKeyValue(::grpc::ClientContext* context, const ::tensorflow::GetKeyValueRequest* request, ::tensorflow::GetKeyValueResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetKeyValue(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::GetKeyValueResponse* response, std::function<void(::grpc::Status)>) = 0;
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      virtual void GetKeyValue(::grpc::ClientContext* context, const ::tensorflow::GetKeyValueRequest* request, ::tensorflow::GetKeyValueResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      #else
      virtual void GetKeyValue(::grpc::ClientContext* context, const ::tensorflow::GetKeyValueRequest* request, ::tensorflow::GetKeyValueResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) = 0;
      #endif
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      virtual void GetKeyValue(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::GetKeyValueResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      #else
      virtual void GetKeyValue(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::GetKeyValueResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) = 0;
      #endif
      // [AUTOMATION]: Internal rpc option goes here.
      // Get configuration key-value. The request does not block, but returns an
      // error if the requested key does not exist.
      virtual void TryGetKeyValue(::grpc::ClientContext* context, const ::tensorflow::TryGetKeyValueRequest* request, ::tensorflow::TryGetKeyValueResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void TryGetKeyValue(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::TryGetKeyValueResponse* response, std::function<void(::grpc::Status)>) = 0;
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      virtual void TryGetKeyValue(::grpc::ClientContext* context, const ::tensorflow::TryGetKeyValueRequest* request, ::tensorflow::TryGetKeyValueResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      #else
      virtual void TryGetKeyValue(::grpc::ClientContext* context, const ::tensorflow::TryGetKeyValueRequest* request, ::tensorflow::TryGetKeyValueResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) = 0;
      #endif
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      virtual void TryGetKeyValue(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::TryGetKeyValueResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      #else
      virtual void TryGetKeyValue(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::TryGetKeyValueResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) = 0;
      #endif
      // Same as GetKeyValue, but returns all values that have keys which are
      // prefixed with the directory key.
      virtual void GetKeyValueDir(::grpc::ClientContext* context, const ::tensorflow::GetKeyValueDirRequest* request, ::tensorflow::GetKeyValueDirResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetKeyValueDir(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::GetKeyValueDirResponse* response, std::function<void(::grpc::Status)>) = 0;
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      virtual void GetKeyValueDir(::grpc::ClientContext* context, const ::tensorflow::GetKeyValueDirRequest* request, ::tensorflow::GetKeyValueDirResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      #else
      virtual void GetKeyValueDir(::grpc::ClientContext* context, const ::tensorflow::GetKeyValueDirRequest* request, ::tensorflow::GetKeyValueDirResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) = 0;
      #endif
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      virtual void GetKeyValueDir(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::GetKeyValueDirResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      #else
      virtual void GetKeyValueDir(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::GetKeyValueDirResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) = 0;
      #endif
      // [AUTOMATION]: Internal rpc option goes here.
      // Delete configuration key-value. If is_directory is set in request,
      // recursively clean up all key-values under the path specified by `key`.
      virtual void DeleteKeyValue(::grpc::ClientContext* context, const ::tensorflow::DeleteKeyValueRequest* request, ::tensorflow::DeleteKeyValueResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void DeleteKeyValue(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::DeleteKeyValueResponse* response, std::function<void(::grpc::Status)>) = 0;
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      virtual void DeleteKeyValue(::grpc::ClientContext* context, const ::tensorflow::DeleteKeyValueRequest* request, ::tensorflow::DeleteKeyValueResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      #else
      virtual void DeleteKeyValue(::grpc::ClientContext* context, const ::tensorflow::DeleteKeyValueRequest* request, ::tensorflow::DeleteKeyValueResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) = 0;
      #endif
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      virtual void DeleteKeyValue(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::DeleteKeyValueResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      #else
      virtual void DeleteKeyValue(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::DeleteKeyValueResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) = 0;
      #endif
      // Blocks until all (or a subset of) tasks are at the barrier or the barrier
      // fails.
      //
      // `barrier_id` should be unique across barriers. Once the barrier has passed
      // or failed, subsequent calls will not block, and immediately respond with
      // the previous response.
      //
      // The first WaitAtBarrier() call received by the service for a particular
      // barrier id is special in that it determines the barrier deadline based on
      // timeout duration.
      // However, if subsequent calls by different agents specify a different set of
      // `tasks` for the same `barrier_id`, the barrier will fail instantly.
      //
      // If no tasks are specified (default), the barrier will block for all the
      // connected tasks.
      //
      // Possible service errors:
      //   - DeadlineExceeded: Timed out waiting for specified tasks at the barrier.
      //      Deadline is determined by the server timestamp when it receives the
      //      first WaitAtBarrier() + timeout duration.
      //   - Cancelled: One of the tasks called CancelBarrier().
      //   - Aborted: Service is shutting down.
      //   - Internal: Any participating task is in ERROR state.
      //   - InvalidArgument: (1) Conflicting tasks specified by different agents
      //       for the same barrier, (2) one of the participating tasks is not in
      //       the cluster, or (3) task making the request is not included in the
      //       list of participating tasks.
      virtual void Barrier(::grpc::ClientContext* context, const ::tensorflow::BarrierRequest* request, ::tensorflow::BarrierResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void Barrier(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::BarrierResponse* response, std::function<void(::grpc::Status)>) = 0;
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      virtual void Barrier(::grpc::ClientContext* context, const ::tensorflow::BarrierRequest* request, ::tensorflow::BarrierResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      #else
      virtual void Barrier(::grpc::ClientContext* context, const ::tensorflow::BarrierRequest* request, ::tensorflow::BarrierResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) = 0;
      #endif
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      virtual void Barrier(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::BarrierResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      #else
      virtual void Barrier(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::BarrierResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) = 0;
      #endif
      // [AUTOMATION]: Internal rpc option goes here.
      // Aborts the barrier if it is ongoing.
      // Current and future WaitAtBarrier() calls with the same id will return a
      // CANCELLED error status.
      // Possible service errors:
      //   - FailedPrecondition: Barrier has already been passed.
      virtual void CancelBarrier(::grpc::ClientContext* context, const ::tensorflow::CancelBarrierRequest* request, ::tensorflow::CancelBarrierResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void CancelBarrier(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::CancelBarrierResponse* response, std::function<void(::grpc::Status)>) = 0;
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      virtual void CancelBarrier(::grpc::ClientContext* context, const ::tensorflow::CancelBarrierRequest* request, ::tensorflow::CancelBarrierResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      #else
      virtual void CancelBarrier(::grpc::ClientContext* context, const ::tensorflow::CancelBarrierRequest* request, ::tensorflow::CancelBarrierResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) = 0;
      #endif
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      virtual void CancelBarrier(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::CancelBarrierResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      #else
      virtual void CancelBarrier(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::CancelBarrierResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) = 0;
      #endif
      // Returns the set of currently alive tasks. More specifically, given a set of
      // tasks T, GetAliveTasks(T) returns the subset T of alive tasks.
      //
      // # Barrier Semantics
      //
      // If multiple tasks call GetAliveTasks concurrently, it's important that they
      // all agree on which tasks are alive. Otherwise, the tasks' behavior might
      // diverge. For example, imagine a set of tasks trying to run an AllGather,
      // but they all disagree on which tasks should be participating in the
      // AllGather. This is buggy.
      //
      // To ensure that every task agrees on which tasks are alive, the
      // GetAliveTasks RPC has barrier-like semantics. Consider an invocation
      // GetAliveTasks(T) for a set of tasks T. The invocation acts as a barrier,
      // waiting for every task in T to call GetAliveTasks(T). Afterwards,
      // GetAliveTasks returns the same set of alive tasks A to all the tasks in T.
      // This ensures that every task agrees which tasks are alive.
      //
      // One small correction. GetAliveTasks doesn't act as a barrier for *every*
      // task in T. Some tasks in T might have failed, so we should not wait for
      // them. Instead, the GetAliveTasks RPC waits only for the returned tasks A.
      //
      // # An Example
      //
      // Imagine we have four tasks: A, B, C, and D. Further imagine that task D
      // has failed and that every task calls GetAliveTasks([A, B, C, D]). The
      // invocation will return tasks [A, B, C]. The GetAliveTasks call acts as a
      // barrier across tasks A, B, and C. Task D, which failed, is ignored.
      virtual void GetAliveTasks(::grpc::ClientContext* context, const ::tensorflow::GetAliveTasksRequest* request, ::tensorflow::GetAliveTasksResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetAliveTasks(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::GetAliveTasksResponse* response, std::function<void(::grpc::Status)>) = 0;
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      virtual void GetAliveTasks(::grpc::ClientContext* context, const ::tensorflow::GetAliveTasksRequest* request, ::tensorflow::GetAliveTasksResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      #else
      virtual void GetAliveTasks(::grpc::ClientContext* context, const ::tensorflow::GetAliveTasksRequest* request, ::tensorflow::GetAliveTasksResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) = 0;
      #endif
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      virtual void GetAliveTasks(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::GetAliveTasksResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      #else
      virtual void GetAliveTasks(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::GetAliveTasksResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) = 0;
      #endif
      // Polls the service for errors.
      //
      // This RPC is used by the coordination service agent to send long polling
      // request to service for errors. The call will block until an error is
      // reported by the service.
      //
      // Possible service errors:
      //   - Aborted: Service is shutting down.
      virtual void PollForError(::grpc::ClientContext* context, const ::tensorflow::PollForErrorRequest* request, ::tensorflow::PollForErrorResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void PollForError(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::PollForErrorResponse* response, std::function<void(::grpc::Status)>) = 0;
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      virtual void PollForError(::grpc::ClientContext* context, const ::tensorflow::PollForErrorRequest* request, ::tensorflow::PollForErrorResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      #else
      virtual void PollForError(::grpc::ClientContext* context, const ::tensorflow::PollForErrorRequest* request, ::tensorflow::PollForErrorResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) = 0;
      #endif
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      virtual void PollForError(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::PollForErrorResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      #else
      virtual void PollForError(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::PollForErrorResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) = 0;
      #endif
      // [AUTOMATION]: Internal rpc option goes here.
    };
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    typedef class experimental_async_interface async_interface;
    #endif
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    async_interface* async() { return experimental_async(); }
    #endif
    virtual class experimental_async_interface* experimental_async() { return nullptr; }
  private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::RegisterTaskResponse>* AsyncRegisterTaskRaw(::grpc::ClientContext* context, const ::tensorflow::RegisterTaskRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::RegisterTaskResponse>* PrepareAsyncRegisterTaskRaw(::grpc::ClientContext* context, const ::tensorflow::RegisterTaskRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::HeartbeatResponse>* AsyncHeartbeatRaw(::grpc::ClientContext* context, const ::tensorflow::HeartbeatRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::HeartbeatResponse>* PrepareAsyncHeartbeatRaw(::grpc::ClientContext* context, const ::tensorflow::HeartbeatRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::WaitForAllTasksResponse>* AsyncWaitForAllTasksRaw(::grpc::ClientContext* context, const ::tensorflow::WaitForAllTasksRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::WaitForAllTasksResponse>* PrepareAsyncWaitForAllTasksRaw(::grpc::ClientContext* context, const ::tensorflow::WaitForAllTasksRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::ShutdownTaskResponse>* AsyncShutdownTaskRaw(::grpc::ClientContext* context, const ::tensorflow::ShutdownTaskRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::ShutdownTaskResponse>* PrepareAsyncShutdownTaskRaw(::grpc::ClientContext* context, const ::tensorflow::ShutdownTaskRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::ResetTaskResponse>* AsyncResetTaskRaw(::grpc::ClientContext* context, const ::tensorflow::ResetTaskRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::ResetTaskResponse>* PrepareAsyncResetTaskRaw(::grpc::ClientContext* context, const ::tensorflow::ResetTaskRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::ReportErrorToTaskResponse>* AsyncReportErrorToTaskRaw(::grpc::ClientContext* context, const ::tensorflow::ReportErrorToTaskRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::ReportErrorToTaskResponse>* PrepareAsyncReportErrorToTaskRaw(::grpc::ClientContext* context, const ::tensorflow::ReportErrorToTaskRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::ReportErrorToServiceResponse>* AsyncReportErrorToServiceRaw(::grpc::ClientContext* context, const ::tensorflow::ReportErrorToServiceRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::ReportErrorToServiceResponse>* PrepareAsyncReportErrorToServiceRaw(::grpc::ClientContext* context, const ::tensorflow::ReportErrorToServiceRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::GetTaskStateResponse>* AsyncGetTaskStateRaw(::grpc::ClientContext* context, const ::tensorflow::GetTaskStateRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::GetTaskStateResponse>* PrepareAsyncGetTaskStateRaw(::grpc::ClientContext* context, const ::tensorflow::GetTaskStateRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::InsertKeyValueResponse>* AsyncInsertKeyValueRaw(::grpc::ClientContext* context, const ::tensorflow::InsertKeyValueRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::InsertKeyValueResponse>* PrepareAsyncInsertKeyValueRaw(::grpc::ClientContext* context, const ::tensorflow::InsertKeyValueRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::GetKeyValueResponse>* AsyncGetKeyValueRaw(::grpc::ClientContext* context, const ::tensorflow::GetKeyValueRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::GetKeyValueResponse>* PrepareAsyncGetKeyValueRaw(::grpc::ClientContext* context, const ::tensorflow::GetKeyValueRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::TryGetKeyValueResponse>* AsyncTryGetKeyValueRaw(::grpc::ClientContext* context, const ::tensorflow::TryGetKeyValueRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::TryGetKeyValueResponse>* PrepareAsyncTryGetKeyValueRaw(::grpc::ClientContext* context, const ::tensorflow::TryGetKeyValueRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::GetKeyValueDirResponse>* AsyncGetKeyValueDirRaw(::grpc::ClientContext* context, const ::tensorflow::GetKeyValueDirRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::GetKeyValueDirResponse>* PrepareAsyncGetKeyValueDirRaw(::grpc::ClientContext* context, const ::tensorflow::GetKeyValueDirRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::DeleteKeyValueResponse>* AsyncDeleteKeyValueRaw(::grpc::ClientContext* context, const ::tensorflow::DeleteKeyValueRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::DeleteKeyValueResponse>* PrepareAsyncDeleteKeyValueRaw(::grpc::ClientContext* context, const ::tensorflow::DeleteKeyValueRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::BarrierResponse>* AsyncBarrierRaw(::grpc::ClientContext* context, const ::tensorflow::BarrierRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::BarrierResponse>* PrepareAsyncBarrierRaw(::grpc::ClientContext* context, const ::tensorflow::BarrierRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::CancelBarrierResponse>* AsyncCancelBarrierRaw(::grpc::ClientContext* context, const ::tensorflow::CancelBarrierRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::CancelBarrierResponse>* PrepareAsyncCancelBarrierRaw(::grpc::ClientContext* context, const ::tensorflow::CancelBarrierRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::GetAliveTasksResponse>* AsyncGetAliveTasksRaw(::grpc::ClientContext* context, const ::tensorflow::GetAliveTasksRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::GetAliveTasksResponse>* PrepareAsyncGetAliveTasksRaw(::grpc::ClientContext* context, const ::tensorflow::GetAliveTasksRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::PollForErrorResponse>* AsyncPollForErrorRaw(::grpc::ClientContext* context, const ::tensorflow::PollForErrorRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::PollForErrorResponse>* PrepareAsyncPollForErrorRaw(::grpc::ClientContext* context, const ::tensorflow::PollForErrorRequest& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel);
    ::grpc::Status RegisterTask(::grpc::ClientContext* context, const ::tensorflow::RegisterTaskRequest& request, ::tensorflow::RegisterTaskResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::RegisterTaskResponse>> AsyncRegisterTask(::grpc::ClientContext* context, const ::tensorflow::RegisterTaskRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::RegisterTaskResponse>>(AsyncRegisterTaskRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::RegisterTaskResponse>> PrepareAsyncRegisterTask(::grpc::ClientContext* context, const ::tensorflow::RegisterTaskRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::RegisterTaskResponse>>(PrepareAsyncRegisterTaskRaw(context, request, cq));
    }
    ::grpc::Status Heartbeat(::grpc::ClientContext* context, const ::tensorflow::HeartbeatRequest& request, ::tensorflow::HeartbeatResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::HeartbeatResponse>> AsyncHeartbeat(::grpc::ClientContext* context, const ::tensorflow::HeartbeatRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::HeartbeatResponse>>(AsyncHeartbeatRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::HeartbeatResponse>> PrepareAsyncHeartbeat(::grpc::ClientContext* context, const ::tensorflow::HeartbeatRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::HeartbeatResponse>>(PrepareAsyncHeartbeatRaw(context, request, cq));
    }
    ::grpc::Status WaitForAllTasks(::grpc::ClientContext* context, const ::tensorflow::WaitForAllTasksRequest& request, ::tensorflow::WaitForAllTasksResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::WaitForAllTasksResponse>> AsyncWaitForAllTasks(::grpc::ClientContext* context, const ::tensorflow::WaitForAllTasksRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::WaitForAllTasksResponse>>(AsyncWaitForAllTasksRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::WaitForAllTasksResponse>> PrepareAsyncWaitForAllTasks(::grpc::ClientContext* context, const ::tensorflow::WaitForAllTasksRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::WaitForAllTasksResponse>>(PrepareAsyncWaitForAllTasksRaw(context, request, cq));
    }
    ::grpc::Status ShutdownTask(::grpc::ClientContext* context, const ::tensorflow::ShutdownTaskRequest& request, ::tensorflow::ShutdownTaskResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::ShutdownTaskResponse>> AsyncShutdownTask(::grpc::ClientContext* context, const ::tensorflow::ShutdownTaskRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::ShutdownTaskResponse>>(AsyncShutdownTaskRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::ShutdownTaskResponse>> PrepareAsyncShutdownTask(::grpc::ClientContext* context, const ::tensorflow::ShutdownTaskRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::ShutdownTaskResponse>>(PrepareAsyncShutdownTaskRaw(context, request, cq));
    }
    ::grpc::Status ResetTask(::grpc::ClientContext* context, const ::tensorflow::ResetTaskRequest& request, ::tensorflow::ResetTaskResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::ResetTaskResponse>> AsyncResetTask(::grpc::ClientContext* context, const ::tensorflow::ResetTaskRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::ResetTaskResponse>>(AsyncResetTaskRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::ResetTaskResponse>> PrepareAsyncResetTask(::grpc::ClientContext* context, const ::tensorflow::ResetTaskRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::ResetTaskResponse>>(PrepareAsyncResetTaskRaw(context, request, cq));
    }
    ::grpc::Status ReportErrorToTask(::grpc::ClientContext* context, const ::tensorflow::ReportErrorToTaskRequest& request, ::tensorflow::ReportErrorToTaskResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::ReportErrorToTaskResponse>> AsyncReportErrorToTask(::grpc::ClientContext* context, const ::tensorflow::ReportErrorToTaskRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::ReportErrorToTaskResponse>>(AsyncReportErrorToTaskRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::ReportErrorToTaskResponse>> PrepareAsyncReportErrorToTask(::grpc::ClientContext* context, const ::tensorflow::ReportErrorToTaskRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::ReportErrorToTaskResponse>>(PrepareAsyncReportErrorToTaskRaw(context, request, cq));
    }
    ::grpc::Status ReportErrorToService(::grpc::ClientContext* context, const ::tensorflow::ReportErrorToServiceRequest& request, ::tensorflow::ReportErrorToServiceResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::ReportErrorToServiceResponse>> AsyncReportErrorToService(::grpc::ClientContext* context, const ::tensorflow::ReportErrorToServiceRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::ReportErrorToServiceResponse>>(AsyncReportErrorToServiceRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::ReportErrorToServiceResponse>> PrepareAsyncReportErrorToService(::grpc::ClientContext* context, const ::tensorflow::ReportErrorToServiceRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::ReportErrorToServiceResponse>>(PrepareAsyncReportErrorToServiceRaw(context, request, cq));
    }
    ::grpc::Status GetTaskState(::grpc::ClientContext* context, const ::tensorflow::GetTaskStateRequest& request, ::tensorflow::GetTaskStateResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::GetTaskStateResponse>> AsyncGetTaskState(::grpc::ClientContext* context, const ::tensorflow::GetTaskStateRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::GetTaskStateResponse>>(AsyncGetTaskStateRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::GetTaskStateResponse>> PrepareAsyncGetTaskState(::grpc::ClientContext* context, const ::tensorflow::GetTaskStateRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::GetTaskStateResponse>>(PrepareAsyncGetTaskStateRaw(context, request, cq));
    }
    ::grpc::Status InsertKeyValue(::grpc::ClientContext* context, const ::tensorflow::InsertKeyValueRequest& request, ::tensorflow::InsertKeyValueResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::InsertKeyValueResponse>> AsyncInsertKeyValue(::grpc::ClientContext* context, const ::tensorflow::InsertKeyValueRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::InsertKeyValueResponse>>(AsyncInsertKeyValueRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::InsertKeyValueResponse>> PrepareAsyncInsertKeyValue(::grpc::ClientContext* context, const ::tensorflow::InsertKeyValueRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::InsertKeyValueResponse>>(PrepareAsyncInsertKeyValueRaw(context, request, cq));
    }
    ::grpc::Status GetKeyValue(::grpc::ClientContext* context, const ::tensorflow::GetKeyValueRequest& request, ::tensorflow::GetKeyValueResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::GetKeyValueResponse>> AsyncGetKeyValue(::grpc::ClientContext* context, const ::tensorflow::GetKeyValueRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::GetKeyValueResponse>>(AsyncGetKeyValueRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::GetKeyValueResponse>> PrepareAsyncGetKeyValue(::grpc::ClientContext* context, const ::tensorflow::GetKeyValueRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::GetKeyValueResponse>>(PrepareAsyncGetKeyValueRaw(context, request, cq));
    }
    ::grpc::Status TryGetKeyValue(::grpc::ClientContext* context, const ::tensorflow::TryGetKeyValueRequest& request, ::tensorflow::TryGetKeyValueResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::TryGetKeyValueResponse>> AsyncTryGetKeyValue(::grpc::ClientContext* context, const ::tensorflow::TryGetKeyValueRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::TryGetKeyValueResponse>>(AsyncTryGetKeyValueRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::TryGetKeyValueResponse>> PrepareAsyncTryGetKeyValue(::grpc::ClientContext* context, const ::tensorflow::TryGetKeyValueRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::TryGetKeyValueResponse>>(PrepareAsyncTryGetKeyValueRaw(context, request, cq));
    }
    ::grpc::Status GetKeyValueDir(::grpc::ClientContext* context, const ::tensorflow::GetKeyValueDirRequest& request, ::tensorflow::GetKeyValueDirResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::GetKeyValueDirResponse>> AsyncGetKeyValueDir(::grpc::ClientContext* context, const ::tensorflow::GetKeyValueDirRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::GetKeyValueDirResponse>>(AsyncGetKeyValueDirRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::GetKeyValueDirResponse>> PrepareAsyncGetKeyValueDir(::grpc::ClientContext* context, const ::tensorflow::GetKeyValueDirRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::GetKeyValueDirResponse>>(PrepareAsyncGetKeyValueDirRaw(context, request, cq));
    }
    ::grpc::Status DeleteKeyValue(::grpc::ClientContext* context, const ::tensorflow::DeleteKeyValueRequest& request, ::tensorflow::DeleteKeyValueResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::DeleteKeyValueResponse>> AsyncDeleteKeyValue(::grpc::ClientContext* context, const ::tensorflow::DeleteKeyValueRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::DeleteKeyValueResponse>>(AsyncDeleteKeyValueRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::DeleteKeyValueResponse>> PrepareAsyncDeleteKeyValue(::grpc::ClientContext* context, const ::tensorflow::DeleteKeyValueRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::DeleteKeyValueResponse>>(PrepareAsyncDeleteKeyValueRaw(context, request, cq));
    }
    ::grpc::Status Barrier(::grpc::ClientContext* context, const ::tensorflow::BarrierRequest& request, ::tensorflow::BarrierResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::BarrierResponse>> AsyncBarrier(::grpc::ClientContext* context, const ::tensorflow::BarrierRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::BarrierResponse>>(AsyncBarrierRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::BarrierResponse>> PrepareAsyncBarrier(::grpc::ClientContext* context, const ::tensorflow::BarrierRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::BarrierResponse>>(PrepareAsyncBarrierRaw(context, request, cq));
    }
    ::grpc::Status CancelBarrier(::grpc::ClientContext* context, const ::tensorflow::CancelBarrierRequest& request, ::tensorflow::CancelBarrierResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::CancelBarrierResponse>> AsyncCancelBarrier(::grpc::ClientContext* context, const ::tensorflow::CancelBarrierRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::CancelBarrierResponse>>(AsyncCancelBarrierRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::CancelBarrierResponse>> PrepareAsyncCancelBarrier(::grpc::ClientContext* context, const ::tensorflow::CancelBarrierRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::CancelBarrierResponse>>(PrepareAsyncCancelBarrierRaw(context, request, cq));
    }
    ::grpc::Status GetAliveTasks(::grpc::ClientContext* context, const ::tensorflow::GetAliveTasksRequest& request, ::tensorflow::GetAliveTasksResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::GetAliveTasksResponse>> AsyncGetAliveTasks(::grpc::ClientContext* context, const ::tensorflow::GetAliveTasksRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::GetAliveTasksResponse>>(AsyncGetAliveTasksRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::GetAliveTasksResponse>> PrepareAsyncGetAliveTasks(::grpc::ClientContext* context, const ::tensorflow::GetAliveTasksRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::GetAliveTasksResponse>>(PrepareAsyncGetAliveTasksRaw(context, request, cq));
    }
    ::grpc::Status PollForError(::grpc::ClientContext* context, const ::tensorflow::PollForErrorRequest& request, ::tensorflow::PollForErrorResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::PollForErrorResponse>> AsyncPollForError(::grpc::ClientContext* context, const ::tensorflow::PollForErrorRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::PollForErrorResponse>>(AsyncPollForErrorRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::PollForErrorResponse>> PrepareAsyncPollForError(::grpc::ClientContext* context, const ::tensorflow::PollForErrorRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::PollForErrorResponse>>(PrepareAsyncPollForErrorRaw(context, request, cq));
    }
    class experimental_async final :
      public StubInterface::experimental_async_interface {
     public:
      void RegisterTask(::grpc::ClientContext* context, const ::tensorflow::RegisterTaskRequest* request, ::tensorflow::RegisterTaskResponse* response, std::function<void(::grpc::Status)>) override;
      void RegisterTask(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::RegisterTaskResponse* response, std::function<void(::grpc::Status)>) override;
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      void RegisterTask(::grpc::ClientContext* context, const ::tensorflow::RegisterTaskRequest* request, ::tensorflow::RegisterTaskResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      #else
      void RegisterTask(::grpc::ClientContext* context, const ::tensorflow::RegisterTaskRequest* request, ::tensorflow::RegisterTaskResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) override;
      #endif
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      void RegisterTask(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::RegisterTaskResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      #else
      void RegisterTask(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::RegisterTaskResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) override;
      #endif
      void Heartbeat(::grpc::ClientContext* context, const ::tensorflow::HeartbeatRequest* request, ::tensorflow::HeartbeatResponse* response, std::function<void(::grpc::Status)>) override;
      void Heartbeat(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::HeartbeatResponse* response, std::function<void(::grpc::Status)>) override;
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      void Heartbeat(::grpc::ClientContext* context, const ::tensorflow::HeartbeatRequest* request, ::tensorflow::HeartbeatResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      #else
      void Heartbeat(::grpc::ClientContext* context, const ::tensorflow::HeartbeatRequest* request, ::tensorflow::HeartbeatResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) override;
      #endif
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      void Heartbeat(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::HeartbeatResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      #else
      void Heartbeat(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::HeartbeatResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) override;
      #endif
      void WaitForAllTasks(::grpc::ClientContext* context, const ::tensorflow::WaitForAllTasksRequest* request, ::tensorflow::WaitForAllTasksResponse* response, std::function<void(::grpc::Status)>) override;
      void WaitForAllTasks(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::WaitForAllTasksResponse* response, std::function<void(::grpc::Status)>) override;
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      void WaitForAllTasks(::grpc::ClientContext* context, const ::tensorflow::WaitForAllTasksRequest* request, ::tensorflow::WaitForAllTasksResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      #else
      void WaitForAllTasks(::grpc::ClientContext* context, const ::tensorflow::WaitForAllTasksRequest* request, ::tensorflow::WaitForAllTasksResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) override;
      #endif
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      void WaitForAllTasks(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::WaitForAllTasksResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      #else
      void WaitForAllTasks(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::WaitForAllTasksResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) override;
      #endif
      void ShutdownTask(::grpc::ClientContext* context, const ::tensorflow::ShutdownTaskRequest* request, ::tensorflow::ShutdownTaskResponse* response, std::function<void(::grpc::Status)>) override;
      void ShutdownTask(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::ShutdownTaskResponse* response, std::function<void(::grpc::Status)>) override;
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      void ShutdownTask(::grpc::ClientContext* context, const ::tensorflow::ShutdownTaskRequest* request, ::tensorflow::ShutdownTaskResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      #else
      void ShutdownTask(::grpc::ClientContext* context, const ::tensorflow::ShutdownTaskRequest* request, ::tensorflow::ShutdownTaskResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) override;
      #endif
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      void ShutdownTask(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::ShutdownTaskResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      #else
      void ShutdownTask(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::ShutdownTaskResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) override;
      #endif
      void ResetTask(::grpc::ClientContext* context, const ::tensorflow::ResetTaskRequest* request, ::tensorflow::ResetTaskResponse* response, std::function<void(::grpc::Status)>) override;
      void ResetTask(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::ResetTaskResponse* response, std::function<void(::grpc::Status)>) override;
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      void ResetTask(::grpc::ClientContext* context, const ::tensorflow::ResetTaskRequest* request, ::tensorflow::ResetTaskResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      #else
      void ResetTask(::grpc::ClientContext* context, const ::tensorflow::ResetTaskRequest* request, ::tensorflow::ResetTaskResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) override;
      #endif
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      void ResetTask(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::ResetTaskResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      #else
      void ResetTask(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::ResetTaskResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) override;
      #endif
      void ReportErrorToTask(::grpc::ClientContext* context, const ::tensorflow::ReportErrorToTaskRequest* request, ::tensorflow::ReportErrorToTaskResponse* response, std::function<void(::grpc::Status)>) override;
      void ReportErrorToTask(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::ReportErrorToTaskResponse* response, std::function<void(::grpc::Status)>) override;
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      void ReportErrorToTask(::grpc::ClientContext* context, const ::tensorflow::ReportErrorToTaskRequest* request, ::tensorflow::ReportErrorToTaskResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      #else
      void ReportErrorToTask(::grpc::ClientContext* context, const ::tensorflow::ReportErrorToTaskRequest* request, ::tensorflow::ReportErrorToTaskResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) override;
      #endif
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      void ReportErrorToTask(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::ReportErrorToTaskResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      #else
      void ReportErrorToTask(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::ReportErrorToTaskResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) override;
      #endif
      void ReportErrorToService(::grpc::ClientContext* context, const ::tensorflow::ReportErrorToServiceRequest* request, ::tensorflow::ReportErrorToServiceResponse* response, std::function<void(::grpc::Status)>) override;
      void ReportErrorToService(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::ReportErrorToServiceResponse* response, std::function<void(::grpc::Status)>) override;
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      void ReportErrorToService(::grpc::ClientContext* context, const ::tensorflow::ReportErrorToServiceRequest* request, ::tensorflow::ReportErrorToServiceResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      #else
      void ReportErrorToService(::grpc::ClientContext* context, const ::tensorflow::ReportErrorToServiceRequest* request, ::tensorflow::ReportErrorToServiceResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) override;
      #endif
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      void ReportErrorToService(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::ReportErrorToServiceResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      #else
      void ReportErrorToService(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::ReportErrorToServiceResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) override;
      #endif
      void GetTaskState(::grpc::ClientContext* context, const ::tensorflow::GetTaskStateRequest* request, ::tensorflow::GetTaskStateResponse* response, std::function<void(::grpc::Status)>) override;
      void GetTaskState(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::GetTaskStateResponse* response, std::function<void(::grpc::Status)>) override;
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      void GetTaskState(::grpc::ClientContext* context, const ::tensorflow::GetTaskStateRequest* request, ::tensorflow::GetTaskStateResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      #else
      void GetTaskState(::grpc::ClientContext* context, const ::tensorflow::GetTaskStateRequest* request, ::tensorflow::GetTaskStateResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) override;
      #endif
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      void GetTaskState(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::GetTaskStateResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      #else
      void GetTaskState(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::GetTaskStateResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) override;
      #endif
      void InsertKeyValue(::grpc::ClientContext* context, const ::tensorflow::InsertKeyValueRequest* request, ::tensorflow::InsertKeyValueResponse* response, std::function<void(::grpc::Status)>) override;
      void InsertKeyValue(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::InsertKeyValueResponse* response, std::function<void(::grpc::Status)>) override;
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      void InsertKeyValue(::grpc::ClientContext* context, const ::tensorflow::InsertKeyValueRequest* request, ::tensorflow::InsertKeyValueResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      #else
      void InsertKeyValue(::grpc::ClientContext* context, const ::tensorflow::InsertKeyValueRequest* request, ::tensorflow::InsertKeyValueResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) override;
      #endif
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      void InsertKeyValue(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::InsertKeyValueResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      #else
      void InsertKeyValue(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::InsertKeyValueResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) override;
      #endif
      void GetKeyValue(::grpc::ClientContext* context, const ::tensorflow::GetKeyValueRequest* request, ::tensorflow::GetKeyValueResponse* response, std::function<void(::grpc::Status)>) override;
      void GetKeyValue(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::GetKeyValueResponse* response, std::function<void(::grpc::Status)>) override;
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      void GetKeyValue(::grpc::ClientContext* context, const ::tensorflow::GetKeyValueRequest* request, ::tensorflow::GetKeyValueResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      #else
      void GetKeyValue(::grpc::ClientContext* context, const ::tensorflow::GetKeyValueRequest* request, ::tensorflow::GetKeyValueResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) override;
      #endif
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      void GetKeyValue(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::GetKeyValueResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      #else
      void GetKeyValue(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::GetKeyValueResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) override;
      #endif
      void TryGetKeyValue(::grpc::ClientContext* context, const ::tensorflow::TryGetKeyValueRequest* request, ::tensorflow::TryGetKeyValueResponse* response, std::function<void(::grpc::Status)>) override;
      void TryGetKeyValue(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::TryGetKeyValueResponse* response, std::function<void(::grpc::Status)>) override;
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      void TryGetKeyValue(::grpc::ClientContext* context, const ::tensorflow::TryGetKeyValueRequest* request, ::tensorflow::TryGetKeyValueResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      #else
      void TryGetKeyValue(::grpc::ClientContext* context, const ::tensorflow::TryGetKeyValueRequest* request, ::tensorflow::TryGetKeyValueResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) override;
      #endif
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      void TryGetKeyValue(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::TryGetKeyValueResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      #else
      void TryGetKeyValue(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::TryGetKeyValueResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) override;
      #endif
      void GetKeyValueDir(::grpc::ClientContext* context, const ::tensorflow::GetKeyValueDirRequest* request, ::tensorflow::GetKeyValueDirResponse* response, std::function<void(::grpc::Status)>) override;
      void GetKeyValueDir(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::GetKeyValueDirResponse* response, std::function<void(::grpc::Status)>) override;
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      void GetKeyValueDir(::grpc::ClientContext* context, const ::tensorflow::GetKeyValueDirRequest* request, ::tensorflow::GetKeyValueDirResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      #else
      void GetKeyValueDir(::grpc::ClientContext* context, const ::tensorflow::GetKeyValueDirRequest* request, ::tensorflow::GetKeyValueDirResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) override;
      #endif
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      void GetKeyValueDir(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::GetKeyValueDirResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      #else
      void GetKeyValueDir(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::GetKeyValueDirResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) override;
      #endif
      void DeleteKeyValue(::grpc::ClientContext* context, const ::tensorflow::DeleteKeyValueRequest* request, ::tensorflow::DeleteKeyValueResponse* response, std::function<void(::grpc::Status)>) override;
      void DeleteKeyValue(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::DeleteKeyValueResponse* response, std::function<void(::grpc::Status)>) override;
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      void DeleteKeyValue(::grpc::ClientContext* context, const ::tensorflow::DeleteKeyValueRequest* request, ::tensorflow::DeleteKeyValueResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      #else
      void DeleteKeyValue(::grpc::ClientContext* context, const ::tensorflow::DeleteKeyValueRequest* request, ::tensorflow::DeleteKeyValueResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) override;
      #endif
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      void DeleteKeyValue(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::DeleteKeyValueResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      #else
      void DeleteKeyValue(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::DeleteKeyValueResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) override;
      #endif
      void Barrier(::grpc::ClientContext* context, const ::tensorflow::BarrierRequest* request, ::tensorflow::BarrierResponse* response, std::function<void(::grpc::Status)>) override;
      void Barrier(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::BarrierResponse* response, std::function<void(::grpc::Status)>) override;
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      void Barrier(::grpc::ClientContext* context, const ::tensorflow::BarrierRequest* request, ::tensorflow::BarrierResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      #else
      void Barrier(::grpc::ClientContext* context, const ::tensorflow::BarrierRequest* request, ::tensorflow::BarrierResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) override;
      #endif
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      void Barrier(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::BarrierResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      #else
      void Barrier(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::BarrierResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) override;
      #endif
      void CancelBarrier(::grpc::ClientContext* context, const ::tensorflow::CancelBarrierRequest* request, ::tensorflow::CancelBarrierResponse* response, std::function<void(::grpc::Status)>) override;
      void CancelBarrier(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::CancelBarrierResponse* response, std::function<void(::grpc::Status)>) override;
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      void CancelBarrier(::grpc::ClientContext* context, const ::tensorflow::CancelBarrierRequest* request, ::tensorflow::CancelBarrierResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      #else
      void CancelBarrier(::grpc::ClientContext* context, const ::tensorflow::CancelBarrierRequest* request, ::tensorflow::CancelBarrierResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) override;
      #endif
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      void CancelBarrier(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::CancelBarrierResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      #else
      void CancelBarrier(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::CancelBarrierResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) override;
      #endif
      void GetAliveTasks(::grpc::ClientContext* context, const ::tensorflow::GetAliveTasksRequest* request, ::tensorflow::GetAliveTasksResponse* response, std::function<void(::grpc::Status)>) override;
      void GetAliveTasks(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::GetAliveTasksResponse* response, std::function<void(::grpc::Status)>) override;
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      void GetAliveTasks(::grpc::ClientContext* context, const ::tensorflow::GetAliveTasksRequest* request, ::tensorflow::GetAliveTasksResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      #else
      void GetAliveTasks(::grpc::ClientContext* context, const ::tensorflow::GetAliveTasksRequest* request, ::tensorflow::GetAliveTasksResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) override;
      #endif
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      void GetAliveTasks(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::GetAliveTasksResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      #else
      void GetAliveTasks(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::GetAliveTasksResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) override;
      #endif
      void PollForError(::grpc::ClientContext* context, const ::tensorflow::PollForErrorRequest* request, ::tensorflow::PollForErrorResponse* response, std::function<void(::grpc::Status)>) override;
      void PollForError(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::PollForErrorResponse* response, std::function<void(::grpc::Status)>) override;
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      void PollForError(::grpc::ClientContext* context, const ::tensorflow::PollForErrorRequest* request, ::tensorflow::PollForErrorResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      #else
      void PollForError(::grpc::ClientContext* context, const ::tensorflow::PollForErrorRequest* request, ::tensorflow::PollForErrorResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) override;
      #endif
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      void PollForError(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::PollForErrorResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      #else
      void PollForError(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::PollForErrorResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) override;
      #endif
     private:
      friend class Stub;
      explicit experimental_async(Stub* stub): stub_(stub) { }
      Stub* stub() { return stub_; }
      Stub* stub_;
    };
    class experimental_async_interface* experimental_async() override { return &async_stub_; }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    class experimental_async async_stub_{this};
    ::grpc::ClientAsyncResponseReader< ::tensorflow::RegisterTaskResponse>* AsyncRegisterTaskRaw(::grpc::ClientContext* context, const ::tensorflow::RegisterTaskRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::tensorflow::RegisterTaskResponse>* PrepareAsyncRegisterTaskRaw(::grpc::ClientContext* context, const ::tensorflow::RegisterTaskRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::tensorflow::HeartbeatResponse>* AsyncHeartbeatRaw(::grpc::ClientContext* context, const ::tensorflow::HeartbeatRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::tensorflow::HeartbeatResponse>* PrepareAsyncHeartbeatRaw(::grpc::ClientContext* context, const ::tensorflow::HeartbeatRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::tensorflow::WaitForAllTasksResponse>* AsyncWaitForAllTasksRaw(::grpc::ClientContext* context, const ::tensorflow::WaitForAllTasksRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::tensorflow::WaitForAllTasksResponse>* PrepareAsyncWaitForAllTasksRaw(::grpc::ClientContext* context, const ::tensorflow::WaitForAllTasksRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::tensorflow::ShutdownTaskResponse>* AsyncShutdownTaskRaw(::grpc::ClientContext* context, const ::tensorflow::ShutdownTaskRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::tensorflow::ShutdownTaskResponse>* PrepareAsyncShutdownTaskRaw(::grpc::ClientContext* context, const ::tensorflow::ShutdownTaskRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::tensorflow::ResetTaskResponse>* AsyncResetTaskRaw(::grpc::ClientContext* context, const ::tensorflow::ResetTaskRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::tensorflow::ResetTaskResponse>* PrepareAsyncResetTaskRaw(::grpc::ClientContext* context, const ::tensorflow::ResetTaskRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::tensorflow::ReportErrorToTaskResponse>* AsyncReportErrorToTaskRaw(::grpc::ClientContext* context, const ::tensorflow::ReportErrorToTaskRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::tensorflow::ReportErrorToTaskResponse>* PrepareAsyncReportErrorToTaskRaw(::grpc::ClientContext* context, const ::tensorflow::ReportErrorToTaskRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::tensorflow::ReportErrorToServiceResponse>* AsyncReportErrorToServiceRaw(::grpc::ClientContext* context, const ::tensorflow::ReportErrorToServiceRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::tensorflow::ReportErrorToServiceResponse>* PrepareAsyncReportErrorToServiceRaw(::grpc::ClientContext* context, const ::tensorflow::ReportErrorToServiceRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::tensorflow::GetTaskStateResponse>* AsyncGetTaskStateRaw(::grpc::ClientContext* context, const ::tensorflow::GetTaskStateRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::tensorflow::GetTaskStateResponse>* PrepareAsyncGetTaskStateRaw(::grpc::ClientContext* context, const ::tensorflow::GetTaskStateRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::tensorflow::InsertKeyValueResponse>* AsyncInsertKeyValueRaw(::grpc::ClientContext* context, const ::tensorflow::InsertKeyValueRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::tensorflow::InsertKeyValueResponse>* PrepareAsyncInsertKeyValueRaw(::grpc::ClientContext* context, const ::tensorflow::InsertKeyValueRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::tensorflow::GetKeyValueResponse>* AsyncGetKeyValueRaw(::grpc::ClientContext* context, const ::tensorflow::GetKeyValueRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::tensorflow::GetKeyValueResponse>* PrepareAsyncGetKeyValueRaw(::grpc::ClientContext* context, const ::tensorflow::GetKeyValueRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::tensorflow::TryGetKeyValueResponse>* AsyncTryGetKeyValueRaw(::grpc::ClientContext* context, const ::tensorflow::TryGetKeyValueRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::tensorflow::TryGetKeyValueResponse>* PrepareAsyncTryGetKeyValueRaw(::grpc::ClientContext* context, const ::tensorflow::TryGetKeyValueRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::tensorflow::GetKeyValueDirResponse>* AsyncGetKeyValueDirRaw(::grpc::ClientContext* context, const ::tensorflow::GetKeyValueDirRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::tensorflow::GetKeyValueDirResponse>* PrepareAsyncGetKeyValueDirRaw(::grpc::ClientContext* context, const ::tensorflow::GetKeyValueDirRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::tensorflow::DeleteKeyValueResponse>* AsyncDeleteKeyValueRaw(::grpc::ClientContext* context, const ::tensorflow::DeleteKeyValueRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::tensorflow::DeleteKeyValueResponse>* PrepareAsyncDeleteKeyValueRaw(::grpc::ClientContext* context, const ::tensorflow::DeleteKeyValueRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::tensorflow::BarrierResponse>* AsyncBarrierRaw(::grpc::ClientContext* context, const ::tensorflow::BarrierRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::tensorflow::BarrierResponse>* PrepareAsyncBarrierRaw(::grpc::ClientContext* context, const ::tensorflow::BarrierRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::tensorflow::CancelBarrierResponse>* AsyncCancelBarrierRaw(::grpc::ClientContext* context, const ::tensorflow::CancelBarrierRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::tensorflow::CancelBarrierResponse>* PrepareAsyncCancelBarrierRaw(::grpc::ClientContext* context, const ::tensorflow::CancelBarrierRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::tensorflow::GetAliveTasksResponse>* AsyncGetAliveTasksRaw(::grpc::ClientContext* context, const ::tensorflow::GetAliveTasksRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::tensorflow::GetAliveTasksResponse>* PrepareAsyncGetAliveTasksRaw(::grpc::ClientContext* context, const ::tensorflow::GetAliveTasksRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::tensorflow::PollForErrorResponse>* AsyncPollForErrorRaw(::grpc::ClientContext* context, const ::tensorflow::PollForErrorRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::tensorflow::PollForErrorResponse>* PrepareAsyncPollForErrorRaw(::grpc::ClientContext* context, const ::tensorflow::PollForErrorRequest& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_RegisterTask_;
    const ::grpc::internal::RpcMethod rpcmethod_Heartbeat_;
    const ::grpc::internal::RpcMethod rpcmethod_WaitForAllTasks_;
    const ::grpc::internal::RpcMethod rpcmethod_ShutdownTask_;
    const ::grpc::internal::RpcMethod rpcmethod_ResetTask_;
    const ::grpc::internal::RpcMethod rpcmethod_ReportErrorToTask_;
    const ::grpc::internal::RpcMethod rpcmethod_ReportErrorToService_;
    const ::grpc::internal::RpcMethod rpcmethod_GetTaskState_;
    const ::grpc::internal::RpcMethod rpcmethod_InsertKeyValue_;
    const ::grpc::internal::RpcMethod rpcmethod_GetKeyValue_;
    const ::grpc::internal::RpcMethod rpcmethod_TryGetKeyValue_;
    const ::grpc::internal::RpcMethod rpcmethod_GetKeyValueDir_;
    const ::grpc::internal::RpcMethod rpcmethod_DeleteKeyValue_;
    const ::grpc::internal::RpcMethod rpcmethod_Barrier_;
    const ::grpc::internal::RpcMethod rpcmethod_CancelBarrier_;
    const ::grpc::internal::RpcMethod rpcmethod_GetAliveTasks_;
    const ::grpc::internal::RpcMethod rpcmethod_PollForError_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    // Register task to coordination service so that the service starts to track
    // liveness of the task. RPC blocks and returns only when it registers to
    // the service successfully, or error happens in the registering process.
    virtual ::grpc::Status RegisterTask(::grpc::ServerContext* context, const ::tensorflow::RegisterTaskRequest* request, ::tensorflow::RegisterTaskResponse* response);
    // [AUTOMATION]: Internal rpc option goes here.
    // Heartbeat message from task to coordination service. Heartbeat is sent from
    // a task to refresh its timestamp on leader to avoid it becoming stale.
    // RPC responds immediately after refreshing the timestamp on leader.
    virtual ::grpc::Status Heartbeat(::grpc::ServerContext* context, const ::tensorflow::HeartbeatRequest* request, ::tensorflow::HeartbeatResponse* response);
    // [AUTOMATION]: Internal rpc option goes here.
    // Wait for all tasks in the cluster to be up and running. The RPC request
    // only gets responded when all tasks have registered, or some error occurs.
    virtual ::grpc::Status WaitForAllTasks(::grpc::ServerContext* context, const ::tensorflow::WaitForAllTasksRequest* request, ::tensorflow::WaitForAllTasksResponse* response);
    // Disconnects task from the service. If `shutdown_barrier_timeout_in_ms` is
    // specified in the config, blocks until all tasks reach the barrier before
    // disconnecting together. If the barrier times out, tasks at the barrier will
    // still disconnect, while an error is reported to tasks that did not reach
    // the barrier on time.
    virtual ::grpc::Status ShutdownTask(::grpc::ServerContext* context, const ::tensorflow::ShutdownTaskRequest* request, ::tensorflow::ShutdownTaskResponse* response);
    // [AUTOMATION]: Internal rpc option goes here.
    // Disconnects task from the service if it is in an ERROR state, thereby
    // allowing it to reconnect via RegisterTask() in the future.
    virtual ::grpc::Status ResetTask(::grpc::ServerContext* context, const ::tensorflow::ResetTaskRequest* request, ::tensorflow::ResetTaskResponse* response);
    // Report error to the task. RPC sets the receiving instance of coordination
    // service agent to error state permanently.
    // TODO(b/195990880): Consider splitting this into a different RPC service.
    virtual ::grpc::Status ReportErrorToTask(::grpc::ServerContext* context, const ::tensorflow::ReportErrorToTaskRequest* request, ::tensorflow::ReportErrorToTaskResponse* response);
    // Report task error to coordination service. RPC sets the service-side task
    // state to error, and propagate the error to other tasks in the cluster.
    virtual ::grpc::Status ReportErrorToService(::grpc::ServerContext* context, const ::tensorflow::ReportErrorToServiceRequest* request, ::tensorflow::ReportErrorToServiceResponse* response);
    // Get the state of a remote task. Specifically, RPC returns a
    // CoordinatedTaskState, and if the task is in an error status, returns a
    // non-OK error code, non-empty error message and error payload.
    virtual ::grpc::Status GetTaskState(::grpc::ServerContext* context, const ::tensorflow::GetTaskStateRequest* request, ::tensorflow::GetTaskStateResponse* response);
    // Insert configuration key-value that will be accessible to all cluster
    // tasks. The key can be formatted as Unix file path with hierarchy. The
    // coordination service key-value store should only be used for cluster
    // configuration data.
    virtual ::grpc::Status InsertKeyValue(::grpc::ServerContext* context, const ::tensorflow::InsertKeyValueRequest* request, ::tensorflow::InsertKeyValueResponse* response);
    // [AUTOMATION]: Internal rpc option goes here.
    // Get configuration key-value. The request blocks until the key-value data
    // becomes available (i.e., set by a task in the cluster).
    virtual ::grpc::Status GetKeyValue(::grpc::ServerContext* context, const ::tensorflow::GetKeyValueRequest* request, ::tensorflow::GetKeyValueResponse* response);
    // [AUTOMATION]: Internal rpc option goes here.
    // Get configuration key-value. The request does not block, but returns an
    // error if the requested key does not exist.
    virtual ::grpc::Status TryGetKeyValue(::grpc::ServerContext* context, const ::tensorflow::TryGetKeyValueRequest* request, ::tensorflow::TryGetKeyValueResponse* response);
    // Same as GetKeyValue, but returns all values that have keys which are
    // prefixed with the directory key.
    virtual ::grpc::Status GetKeyValueDir(::grpc::ServerContext* context, const ::tensorflow::GetKeyValueDirRequest* request, ::tensorflow::GetKeyValueDirResponse* response);
    // [AUTOMATION]: Internal rpc option goes here.
    // Delete configuration key-value. If is_directory is set in request,
    // recursively clean up all key-values under the path specified by `key`.
    virtual ::grpc::Status DeleteKeyValue(::grpc::ServerContext* context, const ::tensorflow::DeleteKeyValueRequest* request, ::tensorflow::DeleteKeyValueResponse* response);
    // Blocks until all (or a subset of) tasks are at the barrier or the barrier
    // fails.
    //
    // `barrier_id` should be unique across barriers. Once the barrier has passed
    // or failed, subsequent calls will not block, and immediately respond with
    // the previous response.
    //
    // The first WaitAtBarrier() call received by the service for a particular
    // barrier id is special in that it determines the barrier deadline based on
    // timeout duration.
    // However, if subsequent calls by different agents specify a different set of
    // `tasks` for the same `barrier_id`, the barrier will fail instantly.
    //
    // If no tasks are specified (default), the barrier will block for all the
    // connected tasks.
    //
    // Possible service errors:
    //   - DeadlineExceeded: Timed out waiting for specified tasks at the barrier.
    //      Deadline is determined by the server timestamp when it receives the
    //      first WaitAtBarrier() + timeout duration.
    //   - Cancelled: One of the tasks called CancelBarrier().
    //   - Aborted: Service is shutting down.
    //   - Internal: Any participating task is in ERROR state.
    //   - InvalidArgument: (1) Conflicting tasks specified by different agents
    //       for the same barrier, (2) one of the participating tasks is not in
    //       the cluster, or (3) task making the request is not included in the
    //       list of participating tasks.
    virtual ::grpc::Status Barrier(::grpc::ServerContext* context, const ::tensorflow::BarrierRequest* request, ::tensorflow::BarrierResponse* response);
    // [AUTOMATION]: Internal rpc option goes here.
    // Aborts the barrier if it is ongoing.
    // Current and future WaitAtBarrier() calls with the same id will return a
    // CANCELLED error status.
    // Possible service errors:
    //   - FailedPrecondition: Barrier has already been passed.
    virtual ::grpc::Status CancelBarrier(::grpc::ServerContext* context, const ::tensorflow::CancelBarrierRequest* request, ::tensorflow::CancelBarrierResponse* response);
    // Returns the set of currently alive tasks. More specifically, given a set of
    // tasks T, GetAliveTasks(T) returns the subset T of alive tasks.
    //
    // # Barrier Semantics
    //
    // If multiple tasks call GetAliveTasks concurrently, it's important that they
    // all agree on which tasks are alive. Otherwise, the tasks' behavior might
    // diverge. For example, imagine a set of tasks trying to run an AllGather,
    // but they all disagree on which tasks should be participating in the
    // AllGather. This is buggy.
    //
    // To ensure that every task agrees on which tasks are alive, the
    // GetAliveTasks RPC has barrier-like semantics. Consider an invocation
    // GetAliveTasks(T) for a set of tasks T. The invocation acts as a barrier,
    // waiting for every task in T to call GetAliveTasks(T). Afterwards,
    // GetAliveTasks returns the same set of alive tasks A to all the tasks in T.
    // This ensures that every task agrees which tasks are alive.
    //
    // One small correction. GetAliveTasks doesn't act as a barrier for *every*
    // task in T. Some tasks in T might have failed, so we should not wait for
    // them. Instead, the GetAliveTasks RPC waits only for the returned tasks A.
    //
    // # An Example
    //
    // Imagine we have four tasks: A, B, C, and D. Further imagine that task D
    // has failed and that every task calls GetAliveTasks([A, B, C, D]). The
    // invocation will return tasks [A, B, C]. The GetAliveTasks call acts as a
    // barrier across tasks A, B, and C. Task D, which failed, is ignored.
    virtual ::grpc::Status GetAliveTasks(::grpc::ServerContext* context, const ::tensorflow::GetAliveTasksRequest* request, ::tensorflow::GetAliveTasksResponse* response);
    // Polls the service for errors.
    //
    // This RPC is used by the coordination service agent to send long polling
    // request to service for errors. The call will block until an error is
    // reported by the service.
    //
    // Possible service errors:
    //   - Aborted: Service is shutting down.
    virtual ::grpc::Status PollForError(::grpc::ServerContext* context, const ::tensorflow::PollForErrorRequest* request, ::tensorflow::PollForErrorResponse* response);
    // [AUTOMATION]: Internal rpc option goes here.
  };
  template <class BaseClass>
  class WithAsyncMethod_RegisterTask : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_RegisterTask() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_RegisterTask() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status RegisterTask(::grpc::ServerContext* /*context*/, const ::tensorflow::RegisterTaskRequest* /*request*/, ::tensorflow::RegisterTaskResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestRegisterTask(::grpc::ServerContext* context, ::tensorflow::RegisterTaskRequest* request, ::grpc::ServerAsyncResponseWriter< ::tensorflow::RegisterTaskResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_Heartbeat : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_Heartbeat() {
      ::grpc::Service::MarkMethodAsync(1);
    }
    ~WithAsyncMethod_Heartbeat() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Heartbeat(::grpc::ServerContext* /*context*/, const ::tensorflow::HeartbeatRequest* /*request*/, ::tensorflow::HeartbeatResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestHeartbeat(::grpc::ServerContext* context, ::tensorflow::HeartbeatRequest* request, ::grpc::ServerAsyncResponseWriter< ::tensorflow::HeartbeatResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_WaitForAllTasks : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_WaitForAllTasks() {
      ::grpc::Service::MarkMethodAsync(2);
    }
    ~WithAsyncMethod_WaitForAllTasks() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status WaitForAllTasks(::grpc::ServerContext* /*context*/, const ::tensorflow::WaitForAllTasksRequest* /*request*/, ::tensorflow::WaitForAllTasksResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestWaitForAllTasks(::grpc::ServerContext* context, ::tensorflow::WaitForAllTasksRequest* request, ::grpc::ServerAsyncResponseWriter< ::tensorflow::WaitForAllTasksResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_ShutdownTask : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_ShutdownTask() {
      ::grpc::Service::MarkMethodAsync(3);
    }
    ~WithAsyncMethod_ShutdownTask() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ShutdownTask(::grpc::ServerContext* /*context*/, const ::tensorflow::ShutdownTaskRequest* /*request*/, ::tensorflow::ShutdownTaskResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestShutdownTask(::grpc::ServerContext* context, ::tensorflow::ShutdownTaskRequest* request, ::grpc::ServerAsyncResponseWriter< ::tensorflow::ShutdownTaskResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(3, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_ResetTask : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_ResetTask() {
      ::grpc::Service::MarkMethodAsync(4);
    }
    ~WithAsyncMethod_ResetTask() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ResetTask(::grpc::ServerContext* /*context*/, const ::tensorflow::ResetTaskRequest* /*request*/, ::tensorflow::ResetTaskResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestResetTask(::grpc::ServerContext* context, ::tensorflow::ResetTaskRequest* request, ::grpc::ServerAsyncResponseWriter< ::tensorflow::ResetTaskResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(4, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_ReportErrorToTask : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_ReportErrorToTask() {
      ::grpc::Service::MarkMethodAsync(5);
    }
    ~WithAsyncMethod_ReportErrorToTask() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ReportErrorToTask(::grpc::ServerContext* /*context*/, const ::tensorflow::ReportErrorToTaskRequest* /*request*/, ::tensorflow::ReportErrorToTaskResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestReportErrorToTask(::grpc::ServerContext* context, ::tensorflow::ReportErrorToTaskRequest* request, ::grpc::ServerAsyncResponseWriter< ::tensorflow::ReportErrorToTaskResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(5, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_ReportErrorToService : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_ReportErrorToService() {
      ::grpc::Service::MarkMethodAsync(6);
    }
    ~WithAsyncMethod_ReportErrorToService() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ReportErrorToService(::grpc::ServerContext* /*context*/, const ::tensorflow::ReportErrorToServiceRequest* /*request*/, ::tensorflow::ReportErrorToServiceResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestReportErrorToService(::grpc::ServerContext* context, ::tensorflow::ReportErrorToServiceRequest* request, ::grpc::ServerAsyncResponseWriter< ::tensorflow::ReportErrorToServiceResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(6, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetTaskState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetTaskState() {
      ::grpc::Service::MarkMethodAsync(7);
    }
    ~WithAsyncMethod_GetTaskState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetTaskState(::grpc::ServerContext* /*context*/, const ::tensorflow::GetTaskStateRequest* /*request*/, ::tensorflow::GetTaskStateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetTaskState(::grpc::ServerContext* context, ::tensorflow::GetTaskStateRequest* request, ::grpc::ServerAsyncResponseWriter< ::tensorflow::GetTaskStateResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(7, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_InsertKeyValue : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_InsertKeyValue() {
      ::grpc::Service::MarkMethodAsync(8);
    }
    ~WithAsyncMethod_InsertKeyValue() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status InsertKeyValue(::grpc::ServerContext* /*context*/, const ::tensorflow::InsertKeyValueRequest* /*request*/, ::tensorflow::InsertKeyValueResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestInsertKeyValue(::grpc::ServerContext* context, ::tensorflow::InsertKeyValueRequest* request, ::grpc::ServerAsyncResponseWriter< ::tensorflow::InsertKeyValueResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(8, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetKeyValue : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetKeyValue() {
      ::grpc::Service::MarkMethodAsync(9);
    }
    ~WithAsyncMethod_GetKeyValue() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetKeyValue(::grpc::ServerContext* /*context*/, const ::tensorflow::GetKeyValueRequest* /*request*/, ::tensorflow::GetKeyValueResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetKeyValue(::grpc::ServerContext* context, ::tensorflow::GetKeyValueRequest* request, ::grpc::ServerAsyncResponseWriter< ::tensorflow::GetKeyValueResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(9, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_TryGetKeyValue : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_TryGetKeyValue() {
      ::grpc::Service::MarkMethodAsync(10);
    }
    ~WithAsyncMethod_TryGetKeyValue() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status TryGetKeyValue(::grpc::ServerContext* /*context*/, const ::tensorflow::TryGetKeyValueRequest* /*request*/, ::tensorflow::TryGetKeyValueResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestTryGetKeyValue(::grpc::ServerContext* context, ::tensorflow::TryGetKeyValueRequest* request, ::grpc::ServerAsyncResponseWriter< ::tensorflow::TryGetKeyValueResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(10, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetKeyValueDir : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetKeyValueDir() {
      ::grpc::Service::MarkMethodAsync(11);
    }
    ~WithAsyncMethod_GetKeyValueDir() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetKeyValueDir(::grpc::ServerContext* /*context*/, const ::tensorflow::GetKeyValueDirRequest* /*request*/, ::tensorflow::GetKeyValueDirResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetKeyValueDir(::grpc::ServerContext* context, ::tensorflow::GetKeyValueDirRequest* request, ::grpc::ServerAsyncResponseWriter< ::tensorflow::GetKeyValueDirResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(11, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_DeleteKeyValue : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_DeleteKeyValue() {
      ::grpc::Service::MarkMethodAsync(12);
    }
    ~WithAsyncMethod_DeleteKeyValue() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DeleteKeyValue(::grpc::ServerContext* /*context*/, const ::tensorflow::DeleteKeyValueRequest* /*request*/, ::tensorflow::DeleteKeyValueResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestDeleteKeyValue(::grpc::ServerContext* context, ::tensorflow::DeleteKeyValueRequest* request, ::grpc::ServerAsyncResponseWriter< ::tensorflow::DeleteKeyValueResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(12, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_Barrier : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_Barrier() {
      ::grpc::Service::MarkMethodAsync(13);
    }
    ~WithAsyncMethod_Barrier() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Barrier(::grpc::ServerContext* /*context*/, const ::tensorflow::BarrierRequest* /*request*/, ::tensorflow::BarrierResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestBarrier(::grpc::ServerContext* context, ::tensorflow::BarrierRequest* request, ::grpc::ServerAsyncResponseWriter< ::tensorflow::BarrierResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(13, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_CancelBarrier : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_CancelBarrier() {
      ::grpc::Service::MarkMethodAsync(14);
    }
    ~WithAsyncMethod_CancelBarrier() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CancelBarrier(::grpc::ServerContext* /*context*/, const ::tensorflow::CancelBarrierRequest* /*request*/, ::tensorflow::CancelBarrierResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestCancelBarrier(::grpc::ServerContext* context, ::tensorflow::CancelBarrierRequest* request, ::grpc::ServerAsyncResponseWriter< ::tensorflow::CancelBarrierResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(14, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetAliveTasks : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetAliveTasks() {
      ::grpc::Service::MarkMethodAsync(15);
    }
    ~WithAsyncMethod_GetAliveTasks() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetAliveTasks(::grpc::ServerContext* /*context*/, const ::tensorflow::GetAliveTasksRequest* /*request*/, ::tensorflow::GetAliveTasksResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetAliveTasks(::grpc::ServerContext* context, ::tensorflow::GetAliveTasksRequest* request, ::grpc::ServerAsyncResponseWriter< ::tensorflow::GetAliveTasksResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(15, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_PollForError : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_PollForError() {
      ::grpc::Service::MarkMethodAsync(16);
    }
    ~WithAsyncMethod_PollForError() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status PollForError(::grpc::ServerContext* /*context*/, const ::tensorflow::PollForErrorRequest* /*request*/, ::tensorflow::PollForErrorResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestPollForError(::grpc::ServerContext* context, ::tensorflow::PollForErrorRequest* request, ::grpc::ServerAsyncResponseWriter< ::tensorflow::PollForErrorResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(16, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_RegisterTask<WithAsyncMethod_Heartbeat<WithAsyncMethod_WaitForAllTasks<WithAsyncMethod_ShutdownTask<WithAsyncMethod_ResetTask<WithAsyncMethod_ReportErrorToTask<WithAsyncMethod_ReportErrorToService<WithAsyncMethod_GetTaskState<WithAsyncMethod_InsertKeyValue<WithAsyncMethod_GetKeyValue<WithAsyncMethod_TryGetKeyValue<WithAsyncMethod_GetKeyValueDir<WithAsyncMethod_DeleteKeyValue<WithAsyncMethod_Barrier<WithAsyncMethod_CancelBarrier<WithAsyncMethod_GetAliveTasks<WithAsyncMethod_PollForError<Service > > > > > > > > > > > > > > > > > AsyncService;
  template <class BaseClass>
  class ExperimentalWithCallbackMethod_RegisterTask : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithCallbackMethod_RegisterTask() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodCallback(0,
          new ::grpc_impl::internal::CallbackUnaryHandler< ::tensorflow::RegisterTaskRequest, ::tensorflow::RegisterTaskResponse>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context, const ::tensorflow::RegisterTaskRequest* request, ::tensorflow::RegisterTaskResponse* response) { return this->RegisterTask(context, request, response); }));}
    void SetMessageAllocatorFor_RegisterTask(
        ::grpc::experimental::MessageAllocator< ::tensorflow::RegisterTaskRequest, ::tensorflow::RegisterTaskResponse>* allocator) {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(0);
    #else
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::experimental().GetHandler(0);
    #endif
      static_cast<::grpc_impl::internal::CallbackUnaryHandler< ::tensorflow::RegisterTaskRequest, ::tensorflow::RegisterTaskResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~ExperimentalWithCallbackMethod_RegisterTask() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status RegisterTask(::grpc::ServerContext* /*context*/, const ::tensorflow::RegisterTaskRequest* /*request*/, ::tensorflow::RegisterTaskResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerUnaryReactor* RegisterTask(
      ::grpc::CallbackServerContext* /*context*/, const ::tensorflow::RegisterTaskRequest* /*request*/, ::tensorflow::RegisterTaskResponse* /*response*/)
    #else
    virtual ::grpc::experimental::ServerUnaryReactor* RegisterTask(
      ::grpc::experimental::CallbackServerContext* /*context*/, const ::tensorflow::RegisterTaskRequest* /*request*/, ::tensorflow::RegisterTaskResponse* /*response*/)
    #endif
      { return nullptr; }
  };
  template <class BaseClass>
  class ExperimentalWithCallbackMethod_Heartbeat : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithCallbackMethod_Heartbeat() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodCallback(1,
          new ::grpc_impl::internal::CallbackUnaryHandler< ::tensorflow::HeartbeatRequest, ::tensorflow::HeartbeatResponse>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context, const ::tensorflow::HeartbeatRequest* request, ::tensorflow::HeartbeatResponse* response) { return this->Heartbeat(context, request, response); }));}
    void SetMessageAllocatorFor_Heartbeat(
        ::grpc::experimental::MessageAllocator< ::tensorflow::HeartbeatRequest, ::tensorflow::HeartbeatResponse>* allocator) {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(1);
    #else
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::experimental().GetHandler(1);
    #endif
      static_cast<::grpc_impl::internal::CallbackUnaryHandler< ::tensorflow::HeartbeatRequest, ::tensorflow::HeartbeatResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~ExperimentalWithCallbackMethod_Heartbeat() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Heartbeat(::grpc::ServerContext* /*context*/, const ::tensorflow::HeartbeatRequest* /*request*/, ::tensorflow::HeartbeatResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerUnaryReactor* Heartbeat(
      ::grpc::CallbackServerContext* /*context*/, const ::tensorflow::HeartbeatRequest* /*request*/, ::tensorflow::HeartbeatResponse* /*response*/)
    #else
    virtual ::grpc::experimental::ServerUnaryReactor* Heartbeat(
      ::grpc::experimental::CallbackServerContext* /*context*/, const ::tensorflow::HeartbeatRequest* /*request*/, ::tensorflow::HeartbeatResponse* /*response*/)
    #endif
      { return nullptr; }
  };
  template <class BaseClass>
  class ExperimentalWithCallbackMethod_WaitForAllTasks : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithCallbackMethod_WaitForAllTasks() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodCallback(2,
          new ::grpc_impl::internal::CallbackUnaryHandler< ::tensorflow::WaitForAllTasksRequest, ::tensorflow::WaitForAllTasksResponse>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context, const ::tensorflow::WaitForAllTasksRequest* request, ::tensorflow::WaitForAllTasksResponse* response) { return this->WaitForAllTasks(context, request, response); }));}
    void SetMessageAllocatorFor_WaitForAllTasks(
        ::grpc::experimental::MessageAllocator< ::tensorflow::WaitForAllTasksRequest, ::tensorflow::WaitForAllTasksResponse>* allocator) {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(2);
    #else
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::experimental().GetHandler(2);
    #endif
      static_cast<::grpc_impl::internal::CallbackUnaryHandler< ::tensorflow::WaitForAllTasksRequest, ::tensorflow::WaitForAllTasksResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~ExperimentalWithCallbackMethod_WaitForAllTasks() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status WaitForAllTasks(::grpc::ServerContext* /*context*/, const ::tensorflow::WaitForAllTasksRequest* /*request*/, ::tensorflow::WaitForAllTasksResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerUnaryReactor* WaitForAllTasks(
      ::grpc::CallbackServerContext* /*context*/, const ::tensorflow::WaitForAllTasksRequest* /*request*/, ::tensorflow::WaitForAllTasksResponse* /*response*/)
    #else
    virtual ::grpc::experimental::ServerUnaryReactor* WaitForAllTasks(
      ::grpc::experimental::CallbackServerContext* /*context*/, const ::tensorflow::WaitForAllTasksRequest* /*request*/, ::tensorflow::WaitForAllTasksResponse* /*response*/)
    #endif
      { return nullptr; }
  };
  template <class BaseClass>
  class ExperimentalWithCallbackMethod_ShutdownTask : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithCallbackMethod_ShutdownTask() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodCallback(3,
          new ::grpc_impl::internal::CallbackUnaryHandler< ::tensorflow::ShutdownTaskRequest, ::tensorflow::ShutdownTaskResponse>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context, const ::tensorflow::ShutdownTaskRequest* request, ::tensorflow::ShutdownTaskResponse* response) { return this->ShutdownTask(context, request, response); }));}
    void SetMessageAllocatorFor_ShutdownTask(
        ::grpc::experimental::MessageAllocator< ::tensorflow::ShutdownTaskRequest, ::tensorflow::ShutdownTaskResponse>* allocator) {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(3);
    #else
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::experimental().GetHandler(3);
    #endif
      static_cast<::grpc_impl::internal::CallbackUnaryHandler< ::tensorflow::ShutdownTaskRequest, ::tensorflow::ShutdownTaskResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~ExperimentalWithCallbackMethod_ShutdownTask() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ShutdownTask(::grpc::ServerContext* /*context*/, const ::tensorflow::ShutdownTaskRequest* /*request*/, ::tensorflow::ShutdownTaskResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerUnaryReactor* ShutdownTask(
      ::grpc::CallbackServerContext* /*context*/, const ::tensorflow::ShutdownTaskRequest* /*request*/, ::tensorflow::ShutdownTaskResponse* /*response*/)
    #else
    virtual ::grpc::experimental::ServerUnaryReactor* ShutdownTask(
      ::grpc::experimental::CallbackServerContext* /*context*/, const ::tensorflow::ShutdownTaskRequest* /*request*/, ::tensorflow::ShutdownTaskResponse* /*response*/)
    #endif
      { return nullptr; }
  };
  template <class BaseClass>
  class ExperimentalWithCallbackMethod_ResetTask : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithCallbackMethod_ResetTask() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodCallback(4,
          new ::grpc_impl::internal::CallbackUnaryHandler< ::tensorflow::ResetTaskRequest, ::tensorflow::ResetTaskResponse>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context, const ::tensorflow::ResetTaskRequest* request, ::tensorflow::ResetTaskResponse* response) { return this->ResetTask(context, request, response); }));}
    void SetMessageAllocatorFor_ResetTask(
        ::grpc::experimental::MessageAllocator< ::tensorflow::ResetTaskRequest, ::tensorflow::ResetTaskResponse>* allocator) {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(4);
    #else
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::experimental().GetHandler(4);
    #endif
      static_cast<::grpc_impl::internal::CallbackUnaryHandler< ::tensorflow::ResetTaskRequest, ::tensorflow::ResetTaskResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~ExperimentalWithCallbackMethod_ResetTask() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ResetTask(::grpc::ServerContext* /*context*/, const ::tensorflow::ResetTaskRequest* /*request*/, ::tensorflow::ResetTaskResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerUnaryReactor* ResetTask(
      ::grpc::CallbackServerContext* /*context*/, const ::tensorflow::ResetTaskRequest* /*request*/, ::tensorflow::ResetTaskResponse* /*response*/)
    #else
    virtual ::grpc::experimental::ServerUnaryReactor* ResetTask(
      ::grpc::experimental::CallbackServerContext* /*context*/, const ::tensorflow::ResetTaskRequest* /*request*/, ::tensorflow::ResetTaskResponse* /*response*/)
    #endif
      { return nullptr; }
  };
  template <class BaseClass>
  class ExperimentalWithCallbackMethod_ReportErrorToTask : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithCallbackMethod_ReportErrorToTask() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodCallback(5,
          new ::grpc_impl::internal::CallbackUnaryHandler< ::tensorflow::ReportErrorToTaskRequest, ::tensorflow::ReportErrorToTaskResponse>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context, const ::tensorflow::ReportErrorToTaskRequest* request, ::tensorflow::ReportErrorToTaskResponse* response) { return this->ReportErrorToTask(context, request, response); }));}
    void SetMessageAllocatorFor_ReportErrorToTask(
        ::grpc::experimental::MessageAllocator< ::tensorflow::ReportErrorToTaskRequest, ::tensorflow::ReportErrorToTaskResponse>* allocator) {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(5);
    #else
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::experimental().GetHandler(5);
    #endif
      static_cast<::grpc_impl::internal::CallbackUnaryHandler< ::tensorflow::ReportErrorToTaskRequest, ::tensorflow::ReportErrorToTaskResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~ExperimentalWithCallbackMethod_ReportErrorToTask() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ReportErrorToTask(::grpc::ServerContext* /*context*/, const ::tensorflow::ReportErrorToTaskRequest* /*request*/, ::tensorflow::ReportErrorToTaskResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerUnaryReactor* ReportErrorToTask(
      ::grpc::CallbackServerContext* /*context*/, const ::tensorflow::ReportErrorToTaskRequest* /*request*/, ::tensorflow::ReportErrorToTaskResponse* /*response*/)
    #else
    virtual ::grpc::experimental::ServerUnaryReactor* ReportErrorToTask(
      ::grpc::experimental::CallbackServerContext* /*context*/, const ::tensorflow::ReportErrorToTaskRequest* /*request*/, ::tensorflow::ReportErrorToTaskResponse* /*response*/)
    #endif
      { return nullptr; }
  };
  template <class BaseClass>
  class ExperimentalWithCallbackMethod_ReportErrorToService : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithCallbackMethod_ReportErrorToService() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodCallback(6,
          new ::grpc_impl::internal::CallbackUnaryHandler< ::tensorflow::ReportErrorToServiceRequest, ::tensorflow::ReportErrorToServiceResponse>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context, const ::tensorflow::ReportErrorToServiceRequest* request, ::tensorflow::ReportErrorToServiceResponse* response) { return this->ReportErrorToService(context, request, response); }));}
    void SetMessageAllocatorFor_ReportErrorToService(
        ::grpc::experimental::MessageAllocator< ::tensorflow::ReportErrorToServiceRequest, ::tensorflow::ReportErrorToServiceResponse>* allocator) {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(6);
    #else
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::experimental().GetHandler(6);
    #endif
      static_cast<::grpc_impl::internal::CallbackUnaryHandler< ::tensorflow::ReportErrorToServiceRequest, ::tensorflow::ReportErrorToServiceResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~ExperimentalWithCallbackMethod_ReportErrorToService() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ReportErrorToService(::grpc::ServerContext* /*context*/, const ::tensorflow::ReportErrorToServiceRequest* /*request*/, ::tensorflow::ReportErrorToServiceResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerUnaryReactor* ReportErrorToService(
      ::grpc::CallbackServerContext* /*context*/, const ::tensorflow::ReportErrorToServiceRequest* /*request*/, ::tensorflow::ReportErrorToServiceResponse* /*response*/)
    #else
    virtual ::grpc::experimental::ServerUnaryReactor* ReportErrorToService(
      ::grpc::experimental::CallbackServerContext* /*context*/, const ::tensorflow::ReportErrorToServiceRequest* /*request*/, ::tensorflow::ReportErrorToServiceResponse* /*response*/)
    #endif
      { return nullptr; }
  };
  template <class BaseClass>
  class ExperimentalWithCallbackMethod_GetTaskState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithCallbackMethod_GetTaskState() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodCallback(7,
          new ::grpc_impl::internal::CallbackUnaryHandler< ::tensorflow::GetTaskStateRequest, ::tensorflow::GetTaskStateResponse>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context, const ::tensorflow::GetTaskStateRequest* request, ::tensorflow::GetTaskStateResponse* response) { return this->GetTaskState(context, request, response); }));}
    void SetMessageAllocatorFor_GetTaskState(
        ::grpc::experimental::MessageAllocator< ::tensorflow::GetTaskStateRequest, ::tensorflow::GetTaskStateResponse>* allocator) {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(7);
    #else
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::experimental().GetHandler(7);
    #endif
      static_cast<::grpc_impl::internal::CallbackUnaryHandler< ::tensorflow::GetTaskStateRequest, ::tensorflow::GetTaskStateResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~ExperimentalWithCallbackMethod_GetTaskState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetTaskState(::grpc::ServerContext* /*context*/, const ::tensorflow::GetTaskStateRequest* /*request*/, ::tensorflow::GetTaskStateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerUnaryReactor* GetTaskState(
      ::grpc::CallbackServerContext* /*context*/, const ::tensorflow::GetTaskStateRequest* /*request*/, ::tensorflow::GetTaskStateResponse* /*response*/)
    #else
    virtual ::grpc::experimental::ServerUnaryReactor* GetTaskState(
      ::grpc::experimental::CallbackServerContext* /*context*/, const ::tensorflow::GetTaskStateRequest* /*request*/, ::tensorflow::GetTaskStateResponse* /*response*/)
    #endif
      { return nullptr; }
  };
  template <class BaseClass>
  class ExperimentalWithCallbackMethod_InsertKeyValue : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithCallbackMethod_InsertKeyValue() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodCallback(8,
          new ::grpc_impl::internal::CallbackUnaryHandler< ::tensorflow::InsertKeyValueRequest, ::tensorflow::InsertKeyValueResponse>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context, const ::tensorflow::InsertKeyValueRequest* request, ::tensorflow::InsertKeyValueResponse* response) { return this->InsertKeyValue(context, request, response); }));}
    void SetMessageAllocatorFor_InsertKeyValue(
        ::grpc::experimental::MessageAllocator< ::tensorflow::InsertKeyValueRequest, ::tensorflow::InsertKeyValueResponse>* allocator) {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(8);
    #else
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::experimental().GetHandler(8);
    #endif
      static_cast<::grpc_impl::internal::CallbackUnaryHandler< ::tensorflow::InsertKeyValueRequest, ::tensorflow::InsertKeyValueResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~ExperimentalWithCallbackMethod_InsertKeyValue() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status InsertKeyValue(::grpc::ServerContext* /*context*/, const ::tensorflow::InsertKeyValueRequest* /*request*/, ::tensorflow::InsertKeyValueResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerUnaryReactor* InsertKeyValue(
      ::grpc::CallbackServerContext* /*context*/, const ::tensorflow::InsertKeyValueRequest* /*request*/, ::tensorflow::InsertKeyValueResponse* /*response*/)
    #else
    virtual ::grpc::experimental::ServerUnaryReactor* InsertKeyValue(
      ::grpc::experimental::CallbackServerContext* /*context*/, const ::tensorflow::InsertKeyValueRequest* /*request*/, ::tensorflow::InsertKeyValueResponse* /*response*/)
    #endif
      { return nullptr; }
  };
  template <class BaseClass>
  class ExperimentalWithCallbackMethod_GetKeyValue : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithCallbackMethod_GetKeyValue() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodCallback(9,
          new ::grpc_impl::internal::CallbackUnaryHandler< ::tensorflow::GetKeyValueRequest, ::tensorflow::GetKeyValueResponse>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context, const ::tensorflow::GetKeyValueRequest* request, ::tensorflow::GetKeyValueResponse* response) { return this->GetKeyValue(context, request, response); }));}
    void SetMessageAllocatorFor_GetKeyValue(
        ::grpc::experimental::MessageAllocator< ::tensorflow::GetKeyValueRequest, ::tensorflow::GetKeyValueResponse>* allocator) {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(9);
    #else
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::experimental().GetHandler(9);
    #endif
      static_cast<::grpc_impl::internal::CallbackUnaryHandler< ::tensorflow::GetKeyValueRequest, ::tensorflow::GetKeyValueResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~ExperimentalWithCallbackMethod_GetKeyValue() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetKeyValue(::grpc::ServerContext* /*context*/, const ::tensorflow::GetKeyValueRequest* /*request*/, ::tensorflow::GetKeyValueResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerUnaryReactor* GetKeyValue(
      ::grpc::CallbackServerContext* /*context*/, const ::tensorflow::GetKeyValueRequest* /*request*/, ::tensorflow::GetKeyValueResponse* /*response*/)
    #else
    virtual ::grpc::experimental::ServerUnaryReactor* GetKeyValue(
      ::grpc::experimental::CallbackServerContext* /*context*/, const ::tensorflow::GetKeyValueRequest* /*request*/, ::tensorflow::GetKeyValueResponse* /*response*/)
    #endif
      { return nullptr; }
  };
  template <class BaseClass>
  class ExperimentalWithCallbackMethod_TryGetKeyValue : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithCallbackMethod_TryGetKeyValue() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodCallback(10,
          new ::grpc_impl::internal::CallbackUnaryHandler< ::tensorflow::TryGetKeyValueRequest, ::tensorflow::TryGetKeyValueResponse>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context, const ::tensorflow::TryGetKeyValueRequest* request, ::tensorflow::TryGetKeyValueResponse* response) { return this->TryGetKeyValue(context, request, response); }));}
    void SetMessageAllocatorFor_TryGetKeyValue(
        ::grpc::experimental::MessageAllocator< ::tensorflow::TryGetKeyValueRequest, ::tensorflow::TryGetKeyValueResponse>* allocator) {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(10);
    #else
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::experimental().GetHandler(10);
    #endif
      static_cast<::grpc_impl::internal::CallbackUnaryHandler< ::tensorflow::TryGetKeyValueRequest, ::tensorflow::TryGetKeyValueResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~ExperimentalWithCallbackMethod_TryGetKeyValue() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status TryGetKeyValue(::grpc::ServerContext* /*context*/, const ::tensorflow::TryGetKeyValueRequest* /*request*/, ::tensorflow::TryGetKeyValueResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerUnaryReactor* TryGetKeyValue(
      ::grpc::CallbackServerContext* /*context*/, const ::tensorflow::TryGetKeyValueRequest* /*request*/, ::tensorflow::TryGetKeyValueResponse* /*response*/)
    #else
    virtual ::grpc::experimental::ServerUnaryReactor* TryGetKeyValue(
      ::grpc::experimental::CallbackServerContext* /*context*/, const ::tensorflow::TryGetKeyValueRequest* /*request*/, ::tensorflow::TryGetKeyValueResponse* /*response*/)
    #endif
      { return nullptr; }
  };
  template <class BaseClass>
  class ExperimentalWithCallbackMethod_GetKeyValueDir : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithCallbackMethod_GetKeyValueDir() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodCallback(11,
          new ::grpc_impl::internal::CallbackUnaryHandler< ::tensorflow::GetKeyValueDirRequest, ::tensorflow::GetKeyValueDirResponse>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context, const ::tensorflow::GetKeyValueDirRequest* request, ::tensorflow::GetKeyValueDirResponse* response) { return this->GetKeyValueDir(context, request, response); }));}
    void SetMessageAllocatorFor_GetKeyValueDir(
        ::grpc::experimental::MessageAllocator< ::tensorflow::GetKeyValueDirRequest, ::tensorflow::GetKeyValueDirResponse>* allocator) {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(11);
    #else
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::experimental().GetHandler(11);
    #endif
      static_cast<::grpc_impl::internal::CallbackUnaryHandler< ::tensorflow::GetKeyValueDirRequest, ::tensorflow::GetKeyValueDirResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~ExperimentalWithCallbackMethod_GetKeyValueDir() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetKeyValueDir(::grpc::ServerContext* /*context*/, const ::tensorflow::GetKeyValueDirRequest* /*request*/, ::tensorflow::GetKeyValueDirResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerUnaryReactor* GetKeyValueDir(
      ::grpc::CallbackServerContext* /*context*/, const ::tensorflow::GetKeyValueDirRequest* /*request*/, ::tensorflow::GetKeyValueDirResponse* /*response*/)
    #else
    virtual ::grpc::experimental::ServerUnaryReactor* GetKeyValueDir(
      ::grpc::experimental::CallbackServerContext* /*context*/, const ::tensorflow::GetKeyValueDirRequest* /*request*/, ::tensorflow::GetKeyValueDirResponse* /*response*/)
    #endif
      { return nullptr; }
  };
  template <class BaseClass>
  class ExperimentalWithCallbackMethod_DeleteKeyValue : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithCallbackMethod_DeleteKeyValue() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodCallback(12,
          new ::grpc_impl::internal::CallbackUnaryHandler< ::tensorflow::DeleteKeyValueRequest, ::tensorflow::DeleteKeyValueResponse>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context, const ::tensorflow::DeleteKeyValueRequest* request, ::tensorflow::DeleteKeyValueResponse* response) { return this->DeleteKeyValue(context, request, response); }));}
    void SetMessageAllocatorFor_DeleteKeyValue(
        ::grpc::experimental::MessageAllocator< ::tensorflow::DeleteKeyValueRequest, ::tensorflow::DeleteKeyValueResponse>* allocator) {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(12);
    #else
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::experimental().GetHandler(12);
    #endif
      static_cast<::grpc_impl::internal::CallbackUnaryHandler< ::tensorflow::DeleteKeyValueRequest, ::tensorflow::DeleteKeyValueResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~ExperimentalWithCallbackMethod_DeleteKeyValue() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DeleteKeyValue(::grpc::ServerContext* /*context*/, const ::tensorflow::DeleteKeyValueRequest* /*request*/, ::tensorflow::DeleteKeyValueResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerUnaryReactor* DeleteKeyValue(
      ::grpc::CallbackServerContext* /*context*/, const ::tensorflow::DeleteKeyValueRequest* /*request*/, ::tensorflow::DeleteKeyValueResponse* /*response*/)
    #else
    virtual ::grpc::experimental::ServerUnaryReactor* DeleteKeyValue(
      ::grpc::experimental::CallbackServerContext* /*context*/, const ::tensorflow::DeleteKeyValueRequest* /*request*/, ::tensorflow::DeleteKeyValueResponse* /*response*/)
    #endif
      { return nullptr; }
  };
  template <class BaseClass>
  class ExperimentalWithCallbackMethod_Barrier : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithCallbackMethod_Barrier() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodCallback(13,
          new ::grpc_impl::internal::CallbackUnaryHandler< ::tensorflow::BarrierRequest, ::tensorflow::BarrierResponse>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context, const ::tensorflow::BarrierRequest* request, ::tensorflow::BarrierResponse* response) { return this->Barrier(context, request, response); }));}
    void SetMessageAllocatorFor_Barrier(
        ::grpc::experimental::MessageAllocator< ::tensorflow::BarrierRequest, ::tensorflow::BarrierResponse>* allocator) {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(13);
    #else
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::experimental().GetHandler(13);
    #endif
      static_cast<::grpc_impl::internal::CallbackUnaryHandler< ::tensorflow::BarrierRequest, ::tensorflow::BarrierResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~ExperimentalWithCallbackMethod_Barrier() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Barrier(::grpc::ServerContext* /*context*/, const ::tensorflow::BarrierRequest* /*request*/, ::tensorflow::BarrierResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerUnaryReactor* Barrier(
      ::grpc::CallbackServerContext* /*context*/, const ::tensorflow::BarrierRequest* /*request*/, ::tensorflow::BarrierResponse* /*response*/)
    #else
    virtual ::grpc::experimental::ServerUnaryReactor* Barrier(
      ::grpc::experimental::CallbackServerContext* /*context*/, const ::tensorflow::BarrierRequest* /*request*/, ::tensorflow::BarrierResponse* /*response*/)
    #endif
      { return nullptr; }
  };
  template <class BaseClass>
  class ExperimentalWithCallbackMethod_CancelBarrier : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithCallbackMethod_CancelBarrier() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodCallback(14,
          new ::grpc_impl::internal::CallbackUnaryHandler< ::tensorflow::CancelBarrierRequest, ::tensorflow::CancelBarrierResponse>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context, const ::tensorflow::CancelBarrierRequest* request, ::tensorflow::CancelBarrierResponse* response) { return this->CancelBarrier(context, request, response); }));}
    void SetMessageAllocatorFor_CancelBarrier(
        ::grpc::experimental::MessageAllocator< ::tensorflow::CancelBarrierRequest, ::tensorflow::CancelBarrierResponse>* allocator) {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(14);
    #else
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::experimental().GetHandler(14);
    #endif
      static_cast<::grpc_impl::internal::CallbackUnaryHandler< ::tensorflow::CancelBarrierRequest, ::tensorflow::CancelBarrierResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~ExperimentalWithCallbackMethod_CancelBarrier() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CancelBarrier(::grpc::ServerContext* /*context*/, const ::tensorflow::CancelBarrierRequest* /*request*/, ::tensorflow::CancelBarrierResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerUnaryReactor* CancelBarrier(
      ::grpc::CallbackServerContext* /*context*/, const ::tensorflow::CancelBarrierRequest* /*request*/, ::tensorflow::CancelBarrierResponse* /*response*/)
    #else
    virtual ::grpc::experimental::ServerUnaryReactor* CancelBarrier(
      ::grpc::experimental::CallbackServerContext* /*context*/, const ::tensorflow::CancelBarrierRequest* /*request*/, ::tensorflow::CancelBarrierResponse* /*response*/)
    #endif
      { return nullptr; }
  };
  template <class BaseClass>
  class ExperimentalWithCallbackMethod_GetAliveTasks : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithCallbackMethod_GetAliveTasks() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodCallback(15,
          new ::grpc_impl::internal::CallbackUnaryHandler< ::tensorflow::GetAliveTasksRequest, ::tensorflow::GetAliveTasksResponse>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context, const ::tensorflow::GetAliveTasksRequest* request, ::tensorflow::GetAliveTasksResponse* response) { return this->GetAliveTasks(context, request, response); }));}
    void SetMessageAllocatorFor_GetAliveTasks(
        ::grpc::experimental::MessageAllocator< ::tensorflow::GetAliveTasksRequest, ::tensorflow::GetAliveTasksResponse>* allocator) {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(15);
    #else
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::experimental().GetHandler(15);
    #endif
      static_cast<::grpc_impl::internal::CallbackUnaryHandler< ::tensorflow::GetAliveTasksRequest, ::tensorflow::GetAliveTasksResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~ExperimentalWithCallbackMethod_GetAliveTasks() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetAliveTasks(::grpc::ServerContext* /*context*/, const ::tensorflow::GetAliveTasksRequest* /*request*/, ::tensorflow::GetAliveTasksResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerUnaryReactor* GetAliveTasks(
      ::grpc::CallbackServerContext* /*context*/, const ::tensorflow::GetAliveTasksRequest* /*request*/, ::tensorflow::GetAliveTasksResponse* /*response*/)
    #else
    virtual ::grpc::experimental::ServerUnaryReactor* GetAliveTasks(
      ::grpc::experimental::CallbackServerContext* /*context*/, const ::tensorflow::GetAliveTasksRequest* /*request*/, ::tensorflow::GetAliveTasksResponse* /*response*/)
    #endif
      { return nullptr; }
  };
  template <class BaseClass>
  class ExperimentalWithCallbackMethod_PollForError : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithCallbackMethod_PollForError() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodCallback(16,
          new ::grpc_impl::internal::CallbackUnaryHandler< ::tensorflow::PollForErrorRequest, ::tensorflow::PollForErrorResponse>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context, const ::tensorflow::PollForErrorRequest* request, ::tensorflow::PollForErrorResponse* response) { return this->PollForError(context, request, response); }));}
    void SetMessageAllocatorFor_PollForError(
        ::grpc::experimental::MessageAllocator< ::tensorflow::PollForErrorRequest, ::tensorflow::PollForErrorResponse>* allocator) {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(16);
    #else
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::experimental().GetHandler(16);
    #endif
      static_cast<::grpc_impl::internal::CallbackUnaryHandler< ::tensorflow::PollForErrorRequest, ::tensorflow::PollForErrorResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~ExperimentalWithCallbackMethod_PollForError() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status PollForError(::grpc::ServerContext* /*context*/, const ::tensorflow::PollForErrorRequest* /*request*/, ::tensorflow::PollForErrorResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerUnaryReactor* PollForError(
      ::grpc::CallbackServerContext* /*context*/, const ::tensorflow::PollForErrorRequest* /*request*/, ::tensorflow::PollForErrorResponse* /*response*/)
    #else
    virtual ::grpc::experimental::ServerUnaryReactor* PollForError(
      ::grpc::experimental::CallbackServerContext* /*context*/, const ::tensorflow::PollForErrorRequest* /*request*/, ::tensorflow::PollForErrorResponse* /*response*/)
    #endif
      { return nullptr; }
  };
  #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
  typedef ExperimentalWithCallbackMethod_RegisterTask<ExperimentalWithCallbackMethod_Heartbeat<ExperimentalWithCallbackMethod_WaitForAllTasks<ExperimentalWithCallbackMethod_ShutdownTask<ExperimentalWithCallbackMethod_ResetTask<ExperimentalWithCallbackMethod_ReportErrorToTask<ExperimentalWithCallbackMethod_ReportErrorToService<ExperimentalWithCallbackMethod_GetTaskState<ExperimentalWithCallbackMethod_InsertKeyValue<ExperimentalWithCallbackMethod_GetKeyValue<ExperimentalWithCallbackMethod_TryGetKeyValue<ExperimentalWithCallbackMethod_GetKeyValueDir<ExperimentalWithCallbackMethod_DeleteKeyValue<ExperimentalWithCallbackMethod_Barrier<ExperimentalWithCallbackMethod_CancelBarrier<ExperimentalWithCallbackMethod_GetAliveTasks<ExperimentalWithCallbackMethod_PollForError<Service > > > > > > > > > > > > > > > > > CallbackService;
  #endif

  typedef ExperimentalWithCallbackMethod_RegisterTask<ExperimentalWithCallbackMethod_Heartbeat<ExperimentalWithCallbackMethod_WaitForAllTasks<ExperimentalWithCallbackMethod_ShutdownTask<ExperimentalWithCallbackMethod_ResetTask<ExperimentalWithCallbackMethod_ReportErrorToTask<ExperimentalWithCallbackMethod_ReportErrorToService<ExperimentalWithCallbackMethod_GetTaskState<ExperimentalWithCallbackMethod_InsertKeyValue<ExperimentalWithCallbackMethod_GetKeyValue<ExperimentalWithCallbackMethod_TryGetKeyValue<ExperimentalWithCallbackMethod_GetKeyValueDir<ExperimentalWithCallbackMethod_DeleteKeyValue<ExperimentalWithCallbackMethod_Barrier<ExperimentalWithCallbackMethod_CancelBarrier<ExperimentalWithCallbackMethod_GetAliveTasks<ExperimentalWithCallbackMethod_PollForError<Service > > > > > > > > > > > > > > > > > ExperimentalCallbackService;
  template <class BaseClass>
  class WithGenericMethod_RegisterTask : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_RegisterTask() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_RegisterTask() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status RegisterTask(::grpc::ServerContext* /*context*/, const ::tensorflow::RegisterTaskRequest* /*request*/, ::tensorflow::RegisterTaskResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_Heartbeat : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_Heartbeat() {
      ::grpc::Service::MarkMethodGeneric(1);
    }
    ~WithGenericMethod_Heartbeat() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Heartbeat(::grpc::ServerContext* /*context*/, const ::tensorflow::HeartbeatRequest* /*request*/, ::tensorflow::HeartbeatResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_WaitForAllTasks : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_WaitForAllTasks() {
      ::grpc::Service::MarkMethodGeneric(2);
    }
    ~WithGenericMethod_WaitForAllTasks() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status WaitForAllTasks(::grpc::ServerContext* /*context*/, const ::tensorflow::WaitForAllTasksRequest* /*request*/, ::tensorflow::WaitForAllTasksResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_ShutdownTask : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_ShutdownTask() {
      ::grpc::Service::MarkMethodGeneric(3);
    }
    ~WithGenericMethod_ShutdownTask() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ShutdownTask(::grpc::ServerContext* /*context*/, const ::tensorflow::ShutdownTaskRequest* /*request*/, ::tensorflow::ShutdownTaskResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_ResetTask : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_ResetTask() {
      ::grpc::Service::MarkMethodGeneric(4);
    }
    ~WithGenericMethod_ResetTask() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ResetTask(::grpc::ServerContext* /*context*/, const ::tensorflow::ResetTaskRequest* /*request*/, ::tensorflow::ResetTaskResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_ReportErrorToTask : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_ReportErrorToTask() {
      ::grpc::Service::MarkMethodGeneric(5);
    }
    ~WithGenericMethod_ReportErrorToTask() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ReportErrorToTask(::grpc::ServerContext* /*context*/, const ::tensorflow::ReportErrorToTaskRequest* /*request*/, ::tensorflow::ReportErrorToTaskResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_ReportErrorToService : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_ReportErrorToService() {
      ::grpc::Service::MarkMethodGeneric(6);
    }
    ~WithGenericMethod_ReportErrorToService() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ReportErrorToService(::grpc::ServerContext* /*context*/, const ::tensorflow::ReportErrorToServiceRequest* /*request*/, ::tensorflow::ReportErrorToServiceResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetTaskState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetTaskState() {
      ::grpc::Service::MarkMethodGeneric(7);
    }
    ~WithGenericMethod_GetTaskState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetTaskState(::grpc::ServerContext* /*context*/, const ::tensorflow::GetTaskStateRequest* /*request*/, ::tensorflow::GetTaskStateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_InsertKeyValue : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_InsertKeyValue() {
      ::grpc::Service::MarkMethodGeneric(8);
    }
    ~WithGenericMethod_InsertKeyValue() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status InsertKeyValue(::grpc::ServerContext* /*context*/, const ::tensorflow::InsertKeyValueRequest* /*request*/, ::tensorflow::InsertKeyValueResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetKeyValue : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetKeyValue() {
      ::grpc::Service::MarkMethodGeneric(9);
    }
    ~WithGenericMethod_GetKeyValue() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetKeyValue(::grpc::ServerContext* /*context*/, const ::tensorflow::GetKeyValueRequest* /*request*/, ::tensorflow::GetKeyValueResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_TryGetKeyValue : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_TryGetKeyValue() {
      ::grpc::Service::MarkMethodGeneric(10);
    }
    ~WithGenericMethod_TryGetKeyValue() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status TryGetKeyValue(::grpc::ServerContext* /*context*/, const ::tensorflow::TryGetKeyValueRequest* /*request*/, ::tensorflow::TryGetKeyValueResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetKeyValueDir : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetKeyValueDir() {
      ::grpc::Service::MarkMethodGeneric(11);
    }
    ~WithGenericMethod_GetKeyValueDir() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetKeyValueDir(::grpc::ServerContext* /*context*/, const ::tensorflow::GetKeyValueDirRequest* /*request*/, ::tensorflow::GetKeyValueDirResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_DeleteKeyValue : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_DeleteKeyValue() {
      ::grpc::Service::MarkMethodGeneric(12);
    }
    ~WithGenericMethod_DeleteKeyValue() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DeleteKeyValue(::grpc::ServerContext* /*context*/, const ::tensorflow::DeleteKeyValueRequest* /*request*/, ::tensorflow::DeleteKeyValueResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_Barrier : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_Barrier() {
      ::grpc::Service::MarkMethodGeneric(13);
    }
    ~WithGenericMethod_Barrier() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Barrier(::grpc::ServerContext* /*context*/, const ::tensorflow::BarrierRequest* /*request*/, ::tensorflow::BarrierResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_CancelBarrier : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_CancelBarrier() {
      ::grpc::Service::MarkMethodGeneric(14);
    }
    ~WithGenericMethod_CancelBarrier() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CancelBarrier(::grpc::ServerContext* /*context*/, const ::tensorflow::CancelBarrierRequest* /*request*/, ::tensorflow::CancelBarrierResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetAliveTasks : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetAliveTasks() {
      ::grpc::Service::MarkMethodGeneric(15);
    }
    ~WithGenericMethod_GetAliveTasks() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetAliveTasks(::grpc::ServerContext* /*context*/, const ::tensorflow::GetAliveTasksRequest* /*request*/, ::tensorflow::GetAliveTasksResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_PollForError : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_PollForError() {
      ::grpc::Service::MarkMethodGeneric(16);
    }
    ~WithGenericMethod_PollForError() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status PollForError(::grpc::ServerContext* /*context*/, const ::tensorflow::PollForErrorRequest* /*request*/, ::tensorflow::PollForErrorResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_RegisterTask : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_RegisterTask() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_RegisterTask() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status RegisterTask(::grpc::ServerContext* /*context*/, const ::tensorflow::RegisterTaskRequest* /*request*/, ::tensorflow::RegisterTaskResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestRegisterTask(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_Heartbeat : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_Heartbeat() {
      ::grpc::Service::MarkMethodRaw(1);
    }
    ~WithRawMethod_Heartbeat() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Heartbeat(::grpc::ServerContext* /*context*/, const ::tensorflow::HeartbeatRequest* /*request*/, ::tensorflow::HeartbeatResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestHeartbeat(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_WaitForAllTasks : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_WaitForAllTasks() {
      ::grpc::Service::MarkMethodRaw(2);
    }
    ~WithRawMethod_WaitForAllTasks() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status WaitForAllTasks(::grpc::ServerContext* /*context*/, const ::tensorflow::WaitForAllTasksRequest* /*request*/, ::tensorflow::WaitForAllTasksResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestWaitForAllTasks(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_ShutdownTask : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_ShutdownTask() {
      ::grpc::Service::MarkMethodRaw(3);
    }
    ~WithRawMethod_ShutdownTask() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ShutdownTask(::grpc::ServerContext* /*context*/, const ::tensorflow::ShutdownTaskRequest* /*request*/, ::tensorflow::ShutdownTaskResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestShutdownTask(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(3, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_ResetTask : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_ResetTask() {
      ::grpc::Service::MarkMethodRaw(4);
    }
    ~WithRawMethod_ResetTask() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ResetTask(::grpc::ServerContext* /*context*/, const ::tensorflow::ResetTaskRequest* /*request*/, ::tensorflow::ResetTaskResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestResetTask(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(4, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_ReportErrorToTask : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_ReportErrorToTask() {
      ::grpc::Service::MarkMethodRaw(5);
    }
    ~WithRawMethod_ReportErrorToTask() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ReportErrorToTask(::grpc::ServerContext* /*context*/, const ::tensorflow::ReportErrorToTaskRequest* /*request*/, ::tensorflow::ReportErrorToTaskResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestReportErrorToTask(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(5, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_ReportErrorToService : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_ReportErrorToService() {
      ::grpc::Service::MarkMethodRaw(6);
    }
    ~WithRawMethod_ReportErrorToService() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ReportErrorToService(::grpc::ServerContext* /*context*/, const ::tensorflow::ReportErrorToServiceRequest* /*request*/, ::tensorflow::ReportErrorToServiceResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestReportErrorToService(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(6, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetTaskState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetTaskState() {
      ::grpc::Service::MarkMethodRaw(7);
    }
    ~WithRawMethod_GetTaskState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetTaskState(::grpc::ServerContext* /*context*/, const ::tensorflow::GetTaskStateRequest* /*request*/, ::tensorflow::GetTaskStateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetTaskState(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(7, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_InsertKeyValue : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_InsertKeyValue() {
      ::grpc::Service::MarkMethodRaw(8);
    }
    ~WithRawMethod_InsertKeyValue() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status InsertKeyValue(::grpc::ServerContext* /*context*/, const ::tensorflow::InsertKeyValueRequest* /*request*/, ::tensorflow::InsertKeyValueResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestInsertKeyValue(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(8, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetKeyValue : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetKeyValue() {
      ::grpc::Service::MarkMethodRaw(9);
    }
    ~WithRawMethod_GetKeyValue() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetKeyValue(::grpc::ServerContext* /*context*/, const ::tensorflow::GetKeyValueRequest* /*request*/, ::tensorflow::GetKeyValueResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetKeyValue(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(9, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_TryGetKeyValue : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_TryGetKeyValue() {
      ::grpc::Service::MarkMethodRaw(10);
    }
    ~WithRawMethod_TryGetKeyValue() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status TryGetKeyValue(::grpc::ServerContext* /*context*/, const ::tensorflow::TryGetKeyValueRequest* /*request*/, ::tensorflow::TryGetKeyValueResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestTryGetKeyValue(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(10, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetKeyValueDir : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetKeyValueDir() {
      ::grpc::Service::MarkMethodRaw(11);
    }
    ~WithRawMethod_GetKeyValueDir() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetKeyValueDir(::grpc::ServerContext* /*context*/, const ::tensorflow::GetKeyValueDirRequest* /*request*/, ::tensorflow::GetKeyValueDirResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetKeyValueDir(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(11, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_DeleteKeyValue : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_DeleteKeyValue() {
      ::grpc::Service::MarkMethodRaw(12);
    }
    ~WithRawMethod_DeleteKeyValue() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DeleteKeyValue(::grpc::ServerContext* /*context*/, const ::tensorflow::DeleteKeyValueRequest* /*request*/, ::tensorflow::DeleteKeyValueResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestDeleteKeyValue(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(12, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_Barrier : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_Barrier() {
      ::grpc::Service::MarkMethodRaw(13);
    }
    ~WithRawMethod_Barrier() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Barrier(::grpc::ServerContext* /*context*/, const ::tensorflow::BarrierRequest* /*request*/, ::tensorflow::BarrierResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestBarrier(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(13, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_CancelBarrier : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_CancelBarrier() {
      ::grpc::Service::MarkMethodRaw(14);
    }
    ~WithRawMethod_CancelBarrier() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CancelBarrier(::grpc::ServerContext* /*context*/, const ::tensorflow::CancelBarrierRequest* /*request*/, ::tensorflow::CancelBarrierResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestCancelBarrier(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(14, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetAliveTasks : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetAliveTasks() {
      ::grpc::Service::MarkMethodRaw(15);
    }
    ~WithRawMethod_GetAliveTasks() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetAliveTasks(::grpc::ServerContext* /*context*/, const ::tensorflow::GetAliveTasksRequest* /*request*/, ::tensorflow::GetAliveTasksResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetAliveTasks(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(15, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_PollForError : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_PollForError() {
      ::grpc::Service::MarkMethodRaw(16);
    }
    ~WithRawMethod_PollForError() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status PollForError(::grpc::ServerContext* /*context*/, const ::tensorflow::PollForErrorRequest* /*request*/, ::tensorflow::PollForErrorResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestPollForError(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(16, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class ExperimentalWithRawCallbackMethod_RegisterTask : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithRawCallbackMethod_RegisterTask() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodRawCallback(0,
          new ::grpc_impl::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->RegisterTask(context, request, response); }));
    }
    ~ExperimentalWithRawCallbackMethod_RegisterTask() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status RegisterTask(::grpc::ServerContext* /*context*/, const ::tensorflow::RegisterTaskRequest* /*request*/, ::tensorflow::RegisterTaskResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerUnaryReactor* RegisterTask(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)
    #else
    virtual ::grpc::experimental::ServerUnaryReactor* RegisterTask(
      ::grpc::experimental::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)
    #endif
      { return nullptr; }
  };
  template <class BaseClass>
  class ExperimentalWithRawCallbackMethod_Heartbeat : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithRawCallbackMethod_Heartbeat() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodRawCallback(1,
          new ::grpc_impl::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->Heartbeat(context, request, response); }));
    }
    ~ExperimentalWithRawCallbackMethod_Heartbeat() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Heartbeat(::grpc::ServerContext* /*context*/, const ::tensorflow::HeartbeatRequest* /*request*/, ::tensorflow::HeartbeatResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerUnaryReactor* Heartbeat(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)
    #else
    virtual ::grpc::experimental::ServerUnaryReactor* Heartbeat(
      ::grpc::experimental::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)
    #endif
      { return nullptr; }
  };
  template <class BaseClass>
  class ExperimentalWithRawCallbackMethod_WaitForAllTasks : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithRawCallbackMethod_WaitForAllTasks() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodRawCallback(2,
          new ::grpc_impl::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->WaitForAllTasks(context, request, response); }));
    }
    ~ExperimentalWithRawCallbackMethod_WaitForAllTasks() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status WaitForAllTasks(::grpc::ServerContext* /*context*/, const ::tensorflow::WaitForAllTasksRequest* /*request*/, ::tensorflow::WaitForAllTasksResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerUnaryReactor* WaitForAllTasks(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)
    #else
    virtual ::grpc::experimental::ServerUnaryReactor* WaitForAllTasks(
      ::grpc::experimental::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)
    #endif
      { return nullptr; }
  };
  template <class BaseClass>
  class ExperimentalWithRawCallbackMethod_ShutdownTask : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithRawCallbackMethod_ShutdownTask() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodRawCallback(3,
          new ::grpc_impl::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->ShutdownTask(context, request, response); }));
    }
    ~ExperimentalWithRawCallbackMethod_ShutdownTask() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ShutdownTask(::grpc::ServerContext* /*context*/, const ::tensorflow::ShutdownTaskRequest* /*request*/, ::tensorflow::ShutdownTaskResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerUnaryReactor* ShutdownTask(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)
    #else
    virtual ::grpc::experimental::ServerUnaryReactor* ShutdownTask(
      ::grpc::experimental::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)
    #endif
      { return nullptr; }
  };
  template <class BaseClass>
  class ExperimentalWithRawCallbackMethod_ResetTask : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithRawCallbackMethod_ResetTask() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodRawCallback(4,
          new ::grpc_impl::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->ResetTask(context, request, response); }));
    }
    ~ExperimentalWithRawCallbackMethod_ResetTask() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ResetTask(::grpc::ServerContext* /*context*/, const ::tensorflow::ResetTaskRequest* /*request*/, ::tensorflow::ResetTaskResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerUnaryReactor* ResetTask(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)
    #else
    virtual ::grpc::experimental::ServerUnaryReactor* ResetTask(
      ::grpc::experimental::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)
    #endif
      { return nullptr; }
  };
  template <class BaseClass>
  class ExperimentalWithRawCallbackMethod_ReportErrorToTask : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithRawCallbackMethod_ReportErrorToTask() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodRawCallback(5,
          new ::grpc_impl::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->ReportErrorToTask(context, request, response); }));
    }
    ~ExperimentalWithRawCallbackMethod_ReportErrorToTask() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ReportErrorToTask(::grpc::ServerContext* /*context*/, const ::tensorflow::ReportErrorToTaskRequest* /*request*/, ::tensorflow::ReportErrorToTaskResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerUnaryReactor* ReportErrorToTask(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)
    #else
    virtual ::grpc::experimental::ServerUnaryReactor* ReportErrorToTask(
      ::grpc::experimental::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)
    #endif
      { return nullptr; }
  };
  template <class BaseClass>
  class ExperimentalWithRawCallbackMethod_ReportErrorToService : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithRawCallbackMethod_ReportErrorToService() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodRawCallback(6,
          new ::grpc_impl::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->ReportErrorToService(context, request, response); }));
    }
    ~ExperimentalWithRawCallbackMethod_ReportErrorToService() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ReportErrorToService(::grpc::ServerContext* /*context*/, const ::tensorflow::ReportErrorToServiceRequest* /*request*/, ::tensorflow::ReportErrorToServiceResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerUnaryReactor* ReportErrorToService(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)
    #else
    virtual ::grpc::experimental::ServerUnaryReactor* ReportErrorToService(
      ::grpc::experimental::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)
    #endif
      { return nullptr; }
  };
  template <class BaseClass>
  class ExperimentalWithRawCallbackMethod_GetTaskState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithRawCallbackMethod_GetTaskState() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodRawCallback(7,
          new ::grpc_impl::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetTaskState(context, request, response); }));
    }
    ~ExperimentalWithRawCallbackMethod_GetTaskState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetTaskState(::grpc::ServerContext* /*context*/, const ::tensorflow::GetTaskStateRequest* /*request*/, ::tensorflow::GetTaskStateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerUnaryReactor* GetTaskState(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)
    #else
    virtual ::grpc::experimental::ServerUnaryReactor* GetTaskState(
      ::grpc::experimental::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)
    #endif
      { return nullptr; }
  };
  template <class BaseClass>
  class ExperimentalWithRawCallbackMethod_InsertKeyValue : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithRawCallbackMethod_InsertKeyValue() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodRawCallback(8,
          new ::grpc_impl::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->InsertKeyValue(context, request, response); }));
    }
    ~ExperimentalWithRawCallbackMethod_InsertKeyValue() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status InsertKeyValue(::grpc::ServerContext* /*context*/, const ::tensorflow::InsertKeyValueRequest* /*request*/, ::tensorflow::InsertKeyValueResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerUnaryReactor* InsertKeyValue(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)
    #else
    virtual ::grpc::experimental::ServerUnaryReactor* InsertKeyValue(
      ::grpc::experimental::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)
    #endif
      { return nullptr; }
  };
  template <class BaseClass>
  class ExperimentalWithRawCallbackMethod_GetKeyValue : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithRawCallbackMethod_GetKeyValue() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodRawCallback(9,
          new ::grpc_impl::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetKeyValue(context, request, response); }));
    }
    ~ExperimentalWithRawCallbackMethod_GetKeyValue() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetKeyValue(::grpc::ServerContext* /*context*/, const ::tensorflow::GetKeyValueRequest* /*request*/, ::tensorflow::GetKeyValueResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerUnaryReactor* GetKeyValue(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)
    #else
    virtual ::grpc::experimental::ServerUnaryReactor* GetKeyValue(
      ::grpc::experimental::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)
    #endif
      { return nullptr; }
  };
  template <class BaseClass>
  class ExperimentalWithRawCallbackMethod_TryGetKeyValue : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithRawCallbackMethod_TryGetKeyValue() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodRawCallback(10,
          new ::grpc_impl::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->TryGetKeyValue(context, request, response); }));
    }
    ~ExperimentalWithRawCallbackMethod_TryGetKeyValue() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status TryGetKeyValue(::grpc::ServerContext* /*context*/, const ::tensorflow::TryGetKeyValueRequest* /*request*/, ::tensorflow::TryGetKeyValueResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerUnaryReactor* TryGetKeyValue(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)
    #else
    virtual ::grpc::experimental::ServerUnaryReactor* TryGetKeyValue(
      ::grpc::experimental::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)
    #endif
      { return nullptr; }
  };
  template <class BaseClass>
  class ExperimentalWithRawCallbackMethod_GetKeyValueDir : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithRawCallbackMethod_GetKeyValueDir() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodRawCallback(11,
          new ::grpc_impl::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetKeyValueDir(context, request, response); }));
    }
    ~ExperimentalWithRawCallbackMethod_GetKeyValueDir() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetKeyValueDir(::grpc::ServerContext* /*context*/, const ::tensorflow::GetKeyValueDirRequest* /*request*/, ::tensorflow::GetKeyValueDirResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerUnaryReactor* GetKeyValueDir(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)
    #else
    virtual ::grpc::experimental::ServerUnaryReactor* GetKeyValueDir(
      ::grpc::experimental::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)
    #endif
      { return nullptr; }
  };
  template <class BaseClass>
  class ExperimentalWithRawCallbackMethod_DeleteKeyValue : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithRawCallbackMethod_DeleteKeyValue() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodRawCallback(12,
          new ::grpc_impl::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->DeleteKeyValue(context, request, response); }));
    }
    ~ExperimentalWithRawCallbackMethod_DeleteKeyValue() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DeleteKeyValue(::grpc::ServerContext* /*context*/, const ::tensorflow::DeleteKeyValueRequest* /*request*/, ::tensorflow::DeleteKeyValueResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerUnaryReactor* DeleteKeyValue(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)
    #else
    virtual ::grpc::experimental::ServerUnaryReactor* DeleteKeyValue(
      ::grpc::experimental::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)
    #endif
      { return nullptr; }
  };
  template <class BaseClass>
  class ExperimentalWithRawCallbackMethod_Barrier : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithRawCallbackMethod_Barrier() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodRawCallback(13,
          new ::grpc_impl::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->Barrier(context, request, response); }));
    }
    ~ExperimentalWithRawCallbackMethod_Barrier() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Barrier(::grpc::ServerContext* /*context*/, const ::tensorflow::BarrierRequest* /*request*/, ::tensorflow::BarrierResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerUnaryReactor* Barrier(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)
    #else
    virtual ::grpc::experimental::ServerUnaryReactor* Barrier(
      ::grpc::experimental::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)
    #endif
      { return nullptr; }
  };
  template <class BaseClass>
  class ExperimentalWithRawCallbackMethod_CancelBarrier : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithRawCallbackMethod_CancelBarrier() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodRawCallback(14,
          new ::grpc_impl::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->CancelBarrier(context, request, response); }));
    }
    ~ExperimentalWithRawCallbackMethod_CancelBarrier() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CancelBarrier(::grpc::ServerContext* /*context*/, const ::tensorflow::CancelBarrierRequest* /*request*/, ::tensorflow::CancelBarrierResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerUnaryReactor* CancelBarrier(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)
    #else
    virtual ::grpc::experimental::ServerUnaryReactor* CancelBarrier(
      ::grpc::experimental::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)
    #endif
      { return nullptr; }
  };
  template <class BaseClass>
  class ExperimentalWithRawCallbackMethod_GetAliveTasks : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithRawCallbackMethod_GetAliveTasks() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodRawCallback(15,
          new ::grpc_impl::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetAliveTasks(context, request, response); }));
    }
    ~ExperimentalWithRawCallbackMethod_GetAliveTasks() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetAliveTasks(::grpc::ServerContext* /*context*/, const ::tensorflow::GetAliveTasksRequest* /*request*/, ::tensorflow::GetAliveTasksResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerUnaryReactor* GetAliveTasks(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)
    #else
    virtual ::grpc::experimental::ServerUnaryReactor* GetAliveTasks(
      ::grpc::experimental::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)
    #endif
      { return nullptr; }
  };
  template <class BaseClass>
  class ExperimentalWithRawCallbackMethod_PollForError : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithRawCallbackMethod_PollForError() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodRawCallback(16,
          new ::grpc_impl::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->PollForError(context, request, response); }));
    }
    ~ExperimentalWithRawCallbackMethod_PollForError() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status PollForError(::grpc::ServerContext* /*context*/, const ::tensorflow::PollForErrorRequest* /*request*/, ::tensorflow::PollForErrorResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerUnaryReactor* PollForError(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)
    #else
    virtual ::grpc::experimental::ServerUnaryReactor* PollForError(
      ::grpc::experimental::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)
    #endif
      { return nullptr; }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_RegisterTask : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_RegisterTask() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler< ::tensorflow::RegisterTaskRequest, ::tensorflow::RegisterTaskResponse>(std::bind(&WithStreamedUnaryMethod_RegisterTask<BaseClass>::StreamedRegisterTask, this, std::placeholders::_1, std::placeholders::_2)));
    }
    ~WithStreamedUnaryMethod_RegisterTask() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status RegisterTask(::grpc::ServerContext* /*context*/, const ::tensorflow::RegisterTaskRequest* /*request*/, ::tensorflow::RegisterTaskResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedRegisterTask(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::tensorflow::RegisterTaskRequest,::tensorflow::RegisterTaskResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_Heartbeat : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_Heartbeat() {
      ::grpc::Service::MarkMethodStreamed(1,
        new ::grpc::internal::StreamedUnaryHandler< ::tensorflow::HeartbeatRequest, ::tensorflow::HeartbeatResponse>(std::bind(&WithStreamedUnaryMethod_Heartbeat<BaseClass>::StreamedHeartbeat, this, std::placeholders::_1, std::placeholders::_2)));
    }
    ~WithStreamedUnaryMethod_Heartbeat() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status Heartbeat(::grpc::ServerContext* /*context*/, const ::tensorflow::HeartbeatRequest* /*request*/, ::tensorflow::HeartbeatResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedHeartbeat(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::tensorflow::HeartbeatRequest,::tensorflow::HeartbeatResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_WaitForAllTasks : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_WaitForAllTasks() {
      ::grpc::Service::MarkMethodStreamed(2,
        new ::grpc::internal::StreamedUnaryHandler< ::tensorflow::WaitForAllTasksRequest, ::tensorflow::WaitForAllTasksResponse>(std::bind(&WithStreamedUnaryMethod_WaitForAllTasks<BaseClass>::StreamedWaitForAllTasks, this, std::placeholders::_1, std::placeholders::_2)));
    }
    ~WithStreamedUnaryMethod_WaitForAllTasks() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status WaitForAllTasks(::grpc::ServerContext* /*context*/, const ::tensorflow::WaitForAllTasksRequest* /*request*/, ::tensorflow::WaitForAllTasksResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedWaitForAllTasks(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::tensorflow::WaitForAllTasksRequest,::tensorflow::WaitForAllTasksResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_ShutdownTask : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_ShutdownTask() {
      ::grpc::Service::MarkMethodStreamed(3,
        new ::grpc::internal::StreamedUnaryHandler< ::tensorflow::ShutdownTaskRequest, ::tensorflow::ShutdownTaskResponse>(std::bind(&WithStreamedUnaryMethod_ShutdownTask<BaseClass>::StreamedShutdownTask, this, std::placeholders::_1, std::placeholders::_2)));
    }
    ~WithStreamedUnaryMethod_ShutdownTask() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status ShutdownTask(::grpc::ServerContext* /*context*/, const ::tensorflow::ShutdownTaskRequest* /*request*/, ::tensorflow::ShutdownTaskResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedShutdownTask(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::tensorflow::ShutdownTaskRequest,::tensorflow::ShutdownTaskResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_ResetTask : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_ResetTask() {
      ::grpc::Service::MarkMethodStreamed(4,
        new ::grpc::internal::StreamedUnaryHandler< ::tensorflow::ResetTaskRequest, ::tensorflow::ResetTaskResponse>(std::bind(&WithStreamedUnaryMethod_ResetTask<BaseClass>::StreamedResetTask, this, std::placeholders::_1, std::placeholders::_2)));
    }
    ~WithStreamedUnaryMethod_ResetTask() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status ResetTask(::grpc::ServerContext* /*context*/, const ::tensorflow::ResetTaskRequest* /*request*/, ::tensorflow::ResetTaskResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedResetTask(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::tensorflow::ResetTaskRequest,::tensorflow::ResetTaskResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_ReportErrorToTask : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_ReportErrorToTask() {
      ::grpc::Service::MarkMethodStreamed(5,
        new ::grpc::internal::StreamedUnaryHandler< ::tensorflow::ReportErrorToTaskRequest, ::tensorflow::ReportErrorToTaskResponse>(std::bind(&WithStreamedUnaryMethod_ReportErrorToTask<BaseClass>::StreamedReportErrorToTask, this, std::placeholders::_1, std::placeholders::_2)));
    }
    ~WithStreamedUnaryMethod_ReportErrorToTask() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status ReportErrorToTask(::grpc::ServerContext* /*context*/, const ::tensorflow::ReportErrorToTaskRequest* /*request*/, ::tensorflow::ReportErrorToTaskResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedReportErrorToTask(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::tensorflow::ReportErrorToTaskRequest,::tensorflow::ReportErrorToTaskResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_ReportErrorToService : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_ReportErrorToService() {
      ::grpc::Service::MarkMethodStreamed(6,
        new ::grpc::internal::StreamedUnaryHandler< ::tensorflow::ReportErrorToServiceRequest, ::tensorflow::ReportErrorToServiceResponse>(std::bind(&WithStreamedUnaryMethod_ReportErrorToService<BaseClass>::StreamedReportErrorToService, this, std::placeholders::_1, std::placeholders::_2)));
    }
    ~WithStreamedUnaryMethod_ReportErrorToService() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status ReportErrorToService(::grpc::ServerContext* /*context*/, const ::tensorflow::ReportErrorToServiceRequest* /*request*/, ::tensorflow::ReportErrorToServiceResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedReportErrorToService(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::tensorflow::ReportErrorToServiceRequest,::tensorflow::ReportErrorToServiceResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetTaskState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetTaskState() {
      ::grpc::Service::MarkMethodStreamed(7,
        new ::grpc::internal::StreamedUnaryHandler< ::tensorflow::GetTaskStateRequest, ::tensorflow::GetTaskStateResponse>(std::bind(&WithStreamedUnaryMethod_GetTaskState<BaseClass>::StreamedGetTaskState, this, std::placeholders::_1, std::placeholders::_2)));
    }
    ~WithStreamedUnaryMethod_GetTaskState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetTaskState(::grpc::ServerContext* /*context*/, const ::tensorflow::GetTaskStateRequest* /*request*/, ::tensorflow::GetTaskStateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetTaskState(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::tensorflow::GetTaskStateRequest,::tensorflow::GetTaskStateResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_InsertKeyValue : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_InsertKeyValue() {
      ::grpc::Service::MarkMethodStreamed(8,
        new ::grpc::internal::StreamedUnaryHandler< ::tensorflow::InsertKeyValueRequest, ::tensorflow::InsertKeyValueResponse>(std::bind(&WithStreamedUnaryMethod_InsertKeyValue<BaseClass>::StreamedInsertKeyValue, this, std::placeholders::_1, std::placeholders::_2)));
    }
    ~WithStreamedUnaryMethod_InsertKeyValue() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status InsertKeyValue(::grpc::ServerContext* /*context*/, const ::tensorflow::InsertKeyValueRequest* /*request*/, ::tensorflow::InsertKeyValueResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedInsertKeyValue(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::tensorflow::InsertKeyValueRequest,::tensorflow::InsertKeyValueResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetKeyValue : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetKeyValue() {
      ::grpc::Service::MarkMethodStreamed(9,
        new ::grpc::internal::StreamedUnaryHandler< ::tensorflow::GetKeyValueRequest, ::tensorflow::GetKeyValueResponse>(std::bind(&WithStreamedUnaryMethod_GetKeyValue<BaseClass>::StreamedGetKeyValue, this, std::placeholders::_1, std::placeholders::_2)));
    }
    ~WithStreamedUnaryMethod_GetKeyValue() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetKeyValue(::grpc::ServerContext* /*context*/, const ::tensorflow::GetKeyValueRequest* /*request*/, ::tensorflow::GetKeyValueResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetKeyValue(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::tensorflow::GetKeyValueRequest,::tensorflow::GetKeyValueResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_TryGetKeyValue : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_TryGetKeyValue() {
      ::grpc::Service::MarkMethodStreamed(10,
        new ::grpc::internal::StreamedUnaryHandler< ::tensorflow::TryGetKeyValueRequest, ::tensorflow::TryGetKeyValueResponse>(std::bind(&WithStreamedUnaryMethod_TryGetKeyValue<BaseClass>::StreamedTryGetKeyValue, this, std::placeholders::_1, std::placeholders::_2)));
    }
    ~WithStreamedUnaryMethod_TryGetKeyValue() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status TryGetKeyValue(::grpc::ServerContext* /*context*/, const ::tensorflow::TryGetKeyValueRequest* /*request*/, ::tensorflow::TryGetKeyValueResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedTryGetKeyValue(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::tensorflow::TryGetKeyValueRequest,::tensorflow::TryGetKeyValueResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetKeyValueDir : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetKeyValueDir() {
      ::grpc::Service::MarkMethodStreamed(11,
        new ::grpc::internal::StreamedUnaryHandler< ::tensorflow::GetKeyValueDirRequest, ::tensorflow::GetKeyValueDirResponse>(std::bind(&WithStreamedUnaryMethod_GetKeyValueDir<BaseClass>::StreamedGetKeyValueDir, this, std::placeholders::_1, std::placeholders::_2)));
    }
    ~WithStreamedUnaryMethod_GetKeyValueDir() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetKeyValueDir(::grpc::ServerContext* /*context*/, const ::tensorflow::GetKeyValueDirRequest* /*request*/, ::tensorflow::GetKeyValueDirResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetKeyValueDir(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::tensorflow::GetKeyValueDirRequest,::tensorflow::GetKeyValueDirResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_DeleteKeyValue : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_DeleteKeyValue() {
      ::grpc::Service::MarkMethodStreamed(12,
        new ::grpc::internal::StreamedUnaryHandler< ::tensorflow::DeleteKeyValueRequest, ::tensorflow::DeleteKeyValueResponse>(std::bind(&WithStreamedUnaryMethod_DeleteKeyValue<BaseClass>::StreamedDeleteKeyValue, this, std::placeholders::_1, std::placeholders::_2)));
    }
    ~WithStreamedUnaryMethod_DeleteKeyValue() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status DeleteKeyValue(::grpc::ServerContext* /*context*/, const ::tensorflow::DeleteKeyValueRequest* /*request*/, ::tensorflow::DeleteKeyValueResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedDeleteKeyValue(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::tensorflow::DeleteKeyValueRequest,::tensorflow::DeleteKeyValueResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_Barrier : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_Barrier() {
      ::grpc::Service::MarkMethodStreamed(13,
        new ::grpc::internal::StreamedUnaryHandler< ::tensorflow::BarrierRequest, ::tensorflow::BarrierResponse>(std::bind(&WithStreamedUnaryMethod_Barrier<BaseClass>::StreamedBarrier, this, std::placeholders::_1, std::placeholders::_2)));
    }
    ~WithStreamedUnaryMethod_Barrier() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status Barrier(::grpc::ServerContext* /*context*/, const ::tensorflow::BarrierRequest* /*request*/, ::tensorflow::BarrierResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedBarrier(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::tensorflow::BarrierRequest,::tensorflow::BarrierResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_CancelBarrier : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_CancelBarrier() {
      ::grpc::Service::MarkMethodStreamed(14,
        new ::grpc::internal::StreamedUnaryHandler< ::tensorflow::CancelBarrierRequest, ::tensorflow::CancelBarrierResponse>(std::bind(&WithStreamedUnaryMethod_CancelBarrier<BaseClass>::StreamedCancelBarrier, this, std::placeholders::_1, std::placeholders::_2)));
    }
    ~WithStreamedUnaryMethod_CancelBarrier() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status CancelBarrier(::grpc::ServerContext* /*context*/, const ::tensorflow::CancelBarrierRequest* /*request*/, ::tensorflow::CancelBarrierResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedCancelBarrier(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::tensorflow::CancelBarrierRequest,::tensorflow::CancelBarrierResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetAliveTasks : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetAliveTasks() {
      ::grpc::Service::MarkMethodStreamed(15,
        new ::grpc::internal::StreamedUnaryHandler< ::tensorflow::GetAliveTasksRequest, ::tensorflow::GetAliveTasksResponse>(std::bind(&WithStreamedUnaryMethod_GetAliveTasks<BaseClass>::StreamedGetAliveTasks, this, std::placeholders::_1, std::placeholders::_2)));
    }
    ~WithStreamedUnaryMethod_GetAliveTasks() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetAliveTasks(::grpc::ServerContext* /*context*/, const ::tensorflow::GetAliveTasksRequest* /*request*/, ::tensorflow::GetAliveTasksResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetAliveTasks(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::tensorflow::GetAliveTasksRequest,::tensorflow::GetAliveTasksResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_PollForError : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_PollForError() {
      ::grpc::Service::MarkMethodStreamed(16,
        new ::grpc::internal::StreamedUnaryHandler< ::tensorflow::PollForErrorRequest, ::tensorflow::PollForErrorResponse>(std::bind(&WithStreamedUnaryMethod_PollForError<BaseClass>::StreamedPollForError, this, std::placeholders::_1, std::placeholders::_2)));
    }
    ~WithStreamedUnaryMethod_PollForError() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status PollForError(::grpc::ServerContext* /*context*/, const ::tensorflow::PollForErrorRequest* /*request*/, ::tensorflow::PollForErrorResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedPollForError(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::tensorflow::PollForErrorRequest,::tensorflow::PollForErrorResponse>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_RegisterTask<WithStreamedUnaryMethod_Heartbeat<WithStreamedUnaryMethod_WaitForAllTasks<WithStreamedUnaryMethod_ShutdownTask<WithStreamedUnaryMethod_ResetTask<WithStreamedUnaryMethod_ReportErrorToTask<WithStreamedUnaryMethod_ReportErrorToService<WithStreamedUnaryMethod_GetTaskState<WithStreamedUnaryMethod_InsertKeyValue<WithStreamedUnaryMethod_GetKeyValue<WithStreamedUnaryMethod_TryGetKeyValue<WithStreamedUnaryMethod_GetKeyValueDir<WithStreamedUnaryMethod_DeleteKeyValue<WithStreamedUnaryMethod_Barrier<WithStreamedUnaryMethod_CancelBarrier<WithStreamedUnaryMethod_GetAliveTasks<WithStreamedUnaryMethod_PollForError<Service > > > > > > > > > > > > > > > > > StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef WithStreamedUnaryMethod_RegisterTask<WithStreamedUnaryMethod_Heartbeat<WithStreamedUnaryMethod_WaitForAllTasks<WithStreamedUnaryMethod_ShutdownTask<WithStreamedUnaryMethod_ResetTask<WithStreamedUnaryMethod_ReportErrorToTask<WithStreamedUnaryMethod_ReportErrorToService<WithStreamedUnaryMethod_GetTaskState<WithStreamedUnaryMethod_InsertKeyValue<WithStreamedUnaryMethod_GetKeyValue<WithStreamedUnaryMethod_TryGetKeyValue<WithStreamedUnaryMethod_GetKeyValueDir<WithStreamedUnaryMethod_DeleteKeyValue<WithStreamedUnaryMethod_Barrier<WithStreamedUnaryMethod_CancelBarrier<WithStreamedUnaryMethod_GetAliveTasks<WithStreamedUnaryMethod_PollForError<Service > > > > > > > > > > > > > > > > > StreamedService;
};

}  // namespace grpc

}  // namespace tensorflow


#endif  // GRPC_xla_2ftsl_2fprotobuf_2fcoordination_5fservice_2eproto__INCLUDED
