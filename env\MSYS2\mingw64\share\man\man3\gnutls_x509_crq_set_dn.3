.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crq_set_dn" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crq_set_dn \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crq_set_dn(gnutls_x509_crq_t " crq ", const char * " dn ", const char ** " err ");"
.SH ARGUMENTS
.IP "gnutls_x509_crq_t crq" 12
a certificate of type \fBgnutls_x509_crq_t\fP
.IP "const char * dn" 12
a comma separated DN string (RFC4514)
.IP "const char ** err" 12
indicates the error position (if any)
.SH "DESCRIPTION"
This function will set the DN on the provided certificate.
The input string should be plain ASCII or UTF\-8 encoded. On
DN parsing error \fBGNUTLS_E_PARSING_ERROR\fP is returned.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
