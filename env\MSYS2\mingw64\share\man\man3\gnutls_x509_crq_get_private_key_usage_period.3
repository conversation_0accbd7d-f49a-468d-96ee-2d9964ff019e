.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crq_get_private_key_usage_period" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crq_get_private_key_usage_period \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crq_get_private_key_usage_period(gnutls_x509_crq_t " crq ", time_t * " activation ", time_t * " expiration ", unsigned int * " critical ");"
.SH ARGUMENTS
.IP "gnutls_x509_crq_t crq" 12
should contain a \fBgnutls_x509_crq_t\fP type
.IP "time_t * activation" 12
The activation time
.IP "time_t * expiration" 12
The expiration time
.IP "unsigned int * critical" 12
the extension status
.SH "DESCRIPTION"
This function will return the expiration and activation
times of the private key of the certificate.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, \fBGNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE\fP
if the extension is not present, otherwise a negative error value.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
