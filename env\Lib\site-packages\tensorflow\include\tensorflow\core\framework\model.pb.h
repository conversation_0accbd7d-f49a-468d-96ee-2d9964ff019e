// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/framework/model.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fmodel_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fmodel_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fframework_2fmodel_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fframework_2fmodel_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fframework_2fmodel_2eproto;
namespace tensorflow {
namespace data {
namespace model {
class ModelProto;
struct ModelProtoDefaultTypeInternal;
extern ModelProtoDefaultTypeInternal _ModelProto_default_instance_;
class ModelProto_Node;
struct ModelProto_NodeDefaultTypeInternal;
extern ModelProto_NodeDefaultTypeInternal _ModelProto_Node_default_instance_;
class ModelProto_Node_Parameter;
struct ModelProto_Node_ParameterDefaultTypeInternal;
extern ModelProto_Node_ParameterDefaultTypeInternal _ModelProto_Node_Parameter_default_instance_;
class ModelProto_NodesEntry_DoNotUse;
struct ModelProto_NodesEntry_DoNotUseDefaultTypeInternal;
extern ModelProto_NodesEntry_DoNotUseDefaultTypeInternal _ModelProto_NodesEntry_DoNotUse_default_instance_;
class ModelProto_OptimizationParams;
struct ModelProto_OptimizationParamsDefaultTypeInternal;
extern ModelProto_OptimizationParamsDefaultTypeInternal _ModelProto_OptimizationParams_default_instance_;
}  // namespace model
}  // namespace data
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::data::model::ModelProto* Arena::CreateMaybeMessage<::tensorflow::data::model::ModelProto>(Arena*);
template<> ::tensorflow::data::model::ModelProto_Node* Arena::CreateMaybeMessage<::tensorflow::data::model::ModelProto_Node>(Arena*);
template<> ::tensorflow::data::model::ModelProto_Node_Parameter* Arena::CreateMaybeMessage<::tensorflow::data::model::ModelProto_Node_Parameter>(Arena*);
template<> ::tensorflow::data::model::ModelProto_NodesEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::data::model::ModelProto_NodesEntry_DoNotUse>(Arena*);
template<> ::tensorflow::data::model::ModelProto_OptimizationParams* Arena::CreateMaybeMessage<::tensorflow::data::model::ModelProto_OptimizationParams>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {
namespace data {
namespace model {

enum NodeClass : int {
  UNKNOWN = 0,
  INTERLEAVE_MANY = 1,
  ASYNC_INTERLEAVE_MANY = 2,
  KNOWN_RATIO = 3,
  ASYNC_KNOWN_RATIO = 4,
  UNKNOWN_RATIO = 5,
  ASYNC_UNKNOWN_RATIO = 6,
  NodeClass_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  NodeClass_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool NodeClass_IsValid(int value);
constexpr NodeClass NodeClass_MIN = UNKNOWN;
constexpr NodeClass NodeClass_MAX = ASYNC_UNKNOWN_RATIO;
constexpr int NodeClass_ARRAYSIZE = NodeClass_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* NodeClass_descriptor();
template<typename T>
inline const std::string& NodeClass_Name(T enum_t_value) {
  static_assert(::std::is_same<T, NodeClass>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function NodeClass_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    NodeClass_descriptor(), enum_t_value);
}
inline bool NodeClass_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, NodeClass* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<NodeClass>(
    NodeClass_descriptor(), name, value);
}
enum AutotuneAlgorithm : int {
  DEFAULT = 0,
  HILL_CLIMB = 1,
  GRADIENT_DESCENT = 2,
  MAX_PARALLELISM = 3,
  STAGE_BASED = 4,
  AutotuneAlgorithm_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  AutotuneAlgorithm_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool AutotuneAlgorithm_IsValid(int value);
constexpr AutotuneAlgorithm AutotuneAlgorithm_MIN = DEFAULT;
constexpr AutotuneAlgorithm AutotuneAlgorithm_MAX = STAGE_BASED;
constexpr int AutotuneAlgorithm_ARRAYSIZE = AutotuneAlgorithm_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* AutotuneAlgorithm_descriptor();
template<typename T>
inline const std::string& AutotuneAlgorithm_Name(T enum_t_value) {
  static_assert(::std::is_same<T, AutotuneAlgorithm>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function AutotuneAlgorithm_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    AutotuneAlgorithm_descriptor(), enum_t_value);
}
inline bool AutotuneAlgorithm_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, AutotuneAlgorithm* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<AutotuneAlgorithm>(
    AutotuneAlgorithm_descriptor(), name, value);
}
// ===================================================================

class ModelProto_Node_Parameter final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.data.model.ModelProto.Node.Parameter) */ {
 public:
  inline ModelProto_Node_Parameter() : ModelProto_Node_Parameter(nullptr) {}
  ~ModelProto_Node_Parameter() override;
  explicit PROTOBUF_CONSTEXPR ModelProto_Node_Parameter(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ModelProto_Node_Parameter(const ModelProto_Node_Parameter& from);
  ModelProto_Node_Parameter(ModelProto_Node_Parameter&& from) noexcept
    : ModelProto_Node_Parameter() {
    *this = ::std::move(from);
  }

  inline ModelProto_Node_Parameter& operator=(const ModelProto_Node_Parameter& from) {
    CopyFrom(from);
    return *this;
  }
  inline ModelProto_Node_Parameter& operator=(ModelProto_Node_Parameter&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ModelProto_Node_Parameter& default_instance() {
    return *internal_default_instance();
  }
  static inline const ModelProto_Node_Parameter* internal_default_instance() {
    return reinterpret_cast<const ModelProto_Node_Parameter*>(
               &_ModelProto_Node_Parameter_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(ModelProto_Node_Parameter& a, ModelProto_Node_Parameter& b) {
    a.Swap(&b);
  }
  inline void Swap(ModelProto_Node_Parameter* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ModelProto_Node_Parameter* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ModelProto_Node_Parameter* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ModelProto_Node_Parameter>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ModelProto_Node_Parameter& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ModelProto_Node_Parameter& from) {
    ModelProto_Node_Parameter::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ModelProto_Node_Parameter* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.data.model.ModelProto.Node.Parameter";
  }
  protected:
  explicit ModelProto_Node_Parameter(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
    kValueFieldNumber = 2,
    kStateValueFieldNumber = 3,
    kMinFieldNumber = 4,
    kMaxFieldNumber = 5,
    kTunableFieldNumber = 6,
  };
  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // double value = 2;
  void clear_value();
  double value() const;
  void set_value(double value);
  private:
  double _internal_value() const;
  void _internal_set_value(double value);
  public:

  // double state_value = 3;
  void clear_state_value();
  double state_value() const;
  void set_state_value(double value);
  private:
  double _internal_state_value() const;
  void _internal_set_state_value(double value);
  public:

  // double min = 4;
  void clear_min();
  double min() const;
  void set_min(double value);
  private:
  double _internal_min() const;
  void _internal_set_min(double value);
  public:

  // double max = 5;
  void clear_max();
  double max() const;
  void set_max(double value);
  private:
  double _internal_max() const;
  void _internal_set_max(double value);
  public:

  // bool tunable = 6;
  void clear_tunable();
  bool tunable() const;
  void set_tunable(bool value);
  private:
  bool _internal_tunable() const;
  void _internal_set_tunable(bool value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.data.model.ModelProto.Node.Parameter)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    double value_;
    double state_value_;
    double min_;
    double max_;
    bool tunable_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fmodel_2eproto;
};
// -------------------------------------------------------------------

class ModelProto_Node final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.data.model.ModelProto.Node) */ {
 public:
  inline ModelProto_Node() : ModelProto_Node(nullptr) {}
  ~ModelProto_Node() override;
  explicit PROTOBUF_CONSTEXPR ModelProto_Node(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ModelProto_Node(const ModelProto_Node& from);
  ModelProto_Node(ModelProto_Node&& from) noexcept
    : ModelProto_Node() {
    *this = ::std::move(from);
  }

  inline ModelProto_Node& operator=(const ModelProto_Node& from) {
    CopyFrom(from);
    return *this;
  }
  inline ModelProto_Node& operator=(ModelProto_Node&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ModelProto_Node& default_instance() {
    return *internal_default_instance();
  }
  static inline const ModelProto_Node* internal_default_instance() {
    return reinterpret_cast<const ModelProto_Node*>(
               &_ModelProto_Node_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(ModelProto_Node& a, ModelProto_Node& b) {
    a.Swap(&b);
  }
  inline void Swap(ModelProto_Node* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ModelProto_Node* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ModelProto_Node* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ModelProto_Node>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ModelProto_Node& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ModelProto_Node& from) {
    ModelProto_Node::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ModelProto_Node* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.data.model.ModelProto.Node";
  }
  protected:
  explicit ModelProto_Node(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef ModelProto_Node_Parameter Parameter;

  // accessors -------------------------------------------------------

  enum : int {
    kParametersFieldNumber = 11,
    kInputsFieldNumber = 14,
    kNameFieldNumber = 2,
    kIdFieldNumber = 1,
    kBufferedBytesFieldNumber = 4,
    kBufferedElementsFieldNumber = 5,
    kBytesConsumedFieldNumber = 6,
    kBytesProducedFieldNumber = 7,
    kNumElementsFieldNumber = 8,
    kProcessingTimeFieldNumber = 9,
    kAutotuneFieldNumber = 3,
    kRecordMetricsFieldNumber = 10,
    kNodeClassFieldNumber = 15,
    kInputProcessingTimeSumFieldNumber = 12,
    kInputProcessingTimeCountFieldNumber = 13,
    kRatioFieldNumber = 16,
    kMemoryRatioFieldNumber = 17,
  };
  // repeated .tensorflow.data.model.ModelProto.Node.Parameter parameters = 11;
  int parameters_size() const;
  private:
  int _internal_parameters_size() const;
  public:
  void clear_parameters();
  ::tensorflow::data::model::ModelProto_Node_Parameter* mutable_parameters(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::data::model::ModelProto_Node_Parameter >*
      mutable_parameters();
  private:
  const ::tensorflow::data::model::ModelProto_Node_Parameter& _internal_parameters(int index) const;
  ::tensorflow::data::model::ModelProto_Node_Parameter* _internal_add_parameters();
  public:
  const ::tensorflow::data::model::ModelProto_Node_Parameter& parameters(int index) const;
  ::tensorflow::data::model::ModelProto_Node_Parameter* add_parameters();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::data::model::ModelProto_Node_Parameter >&
      parameters() const;

  // repeated int64 inputs = 14;
  int inputs_size() const;
  private:
  int _internal_inputs_size() const;
  public:
  void clear_inputs();
  private:
  int64_t _internal_inputs(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      _internal_inputs() const;
  void _internal_add_inputs(int64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      _internal_mutable_inputs();
  public:
  int64_t inputs(int index) const;
  void set_inputs(int index, int64_t value);
  void add_inputs(int64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      inputs() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      mutable_inputs();

  // string name = 2;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // int64 id = 1;
  void clear_id();
  int64_t id() const;
  void set_id(int64_t value);
  private:
  int64_t _internal_id() const;
  void _internal_set_id(int64_t value);
  public:

  // int64 buffered_bytes = 4;
  void clear_buffered_bytes();
  int64_t buffered_bytes() const;
  void set_buffered_bytes(int64_t value);
  private:
  int64_t _internal_buffered_bytes() const;
  void _internal_set_buffered_bytes(int64_t value);
  public:

  // int64 buffered_elements = 5;
  void clear_buffered_elements();
  int64_t buffered_elements() const;
  void set_buffered_elements(int64_t value);
  private:
  int64_t _internal_buffered_elements() const;
  void _internal_set_buffered_elements(int64_t value);
  public:

  // int64 bytes_consumed = 6;
  void clear_bytes_consumed();
  int64_t bytes_consumed() const;
  void set_bytes_consumed(int64_t value);
  private:
  int64_t _internal_bytes_consumed() const;
  void _internal_set_bytes_consumed(int64_t value);
  public:

  // int64 bytes_produced = 7;
  void clear_bytes_produced();
  int64_t bytes_produced() const;
  void set_bytes_produced(int64_t value);
  private:
  int64_t _internal_bytes_produced() const;
  void _internal_set_bytes_produced(int64_t value);
  public:

  // int64 num_elements = 8;
  void clear_num_elements();
  int64_t num_elements() const;
  void set_num_elements(int64_t value);
  private:
  int64_t _internal_num_elements() const;
  void _internal_set_num_elements(int64_t value);
  public:

  // int64 processing_time = 9;
  void clear_processing_time();
  int64_t processing_time() const;
  void set_processing_time(int64_t value);
  private:
  int64_t _internal_processing_time() const;
  void _internal_set_processing_time(int64_t value);
  public:

  // bool autotune = 3;
  void clear_autotune();
  bool autotune() const;
  void set_autotune(bool value);
  private:
  bool _internal_autotune() const;
  void _internal_set_autotune(bool value);
  public:

  // bool record_metrics = 10;
  void clear_record_metrics();
  bool record_metrics() const;
  void set_record_metrics(bool value);
  private:
  bool _internal_record_metrics() const;
  void _internal_set_record_metrics(bool value);
  public:

  // .tensorflow.data.model.NodeClass node_class = 15;
  void clear_node_class();
  ::tensorflow::data::model::NodeClass node_class() const;
  void set_node_class(::tensorflow::data::model::NodeClass value);
  private:
  ::tensorflow::data::model::NodeClass _internal_node_class() const;
  void _internal_set_node_class(::tensorflow::data::model::NodeClass value);
  public:

  // double input_processing_time_sum = 12;
  void clear_input_processing_time_sum();
  double input_processing_time_sum() const;
  void set_input_processing_time_sum(double value);
  private:
  double _internal_input_processing_time_sum() const;
  void _internal_set_input_processing_time_sum(double value);
  public:

  // int64 input_processing_time_count = 13;
  void clear_input_processing_time_count();
  int64_t input_processing_time_count() const;
  void set_input_processing_time_count(int64_t value);
  private:
  int64_t _internal_input_processing_time_count() const;
  void _internal_set_input_processing_time_count(int64_t value);
  public:

  // double ratio = 16;
  void clear_ratio();
  double ratio() const;
  void set_ratio(double value);
  private:
  double _internal_ratio() const;
  void _internal_set_ratio(double value);
  public:

  // double memory_ratio = 17;
  void clear_memory_ratio();
  double memory_ratio() const;
  void set_memory_ratio(double value);
  private:
  double _internal_memory_ratio() const;
  void _internal_set_memory_ratio(double value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.data.model.ModelProto.Node)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::data::model::ModelProto_Node_Parameter > parameters_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t > inputs_;
    mutable std::atomic<int> _inputs_cached_byte_size_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    int64_t id_;
    int64_t buffered_bytes_;
    int64_t buffered_elements_;
    int64_t bytes_consumed_;
    int64_t bytes_produced_;
    int64_t num_elements_;
    int64_t processing_time_;
    bool autotune_;
    bool record_metrics_;
    int node_class_;
    double input_processing_time_sum_;
    int64_t input_processing_time_count_;
    double ratio_;
    double memory_ratio_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fmodel_2eproto;
};
// -------------------------------------------------------------------

class ModelProto_NodesEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ModelProto_NodesEntry_DoNotUse, 
    int64_t, ::tensorflow::data::model::ModelProto_Node,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ModelProto_NodesEntry_DoNotUse, 
    int64_t, ::tensorflow::data::model::ModelProto_Node,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> SuperType;
  ModelProto_NodesEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR ModelProto_NodesEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit ModelProto_NodesEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const ModelProto_NodesEntry_DoNotUse& other);
  static const ModelProto_NodesEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const ModelProto_NodesEntry_DoNotUse*>(&_ModelProto_NodesEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(void*) { return true; }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fmodel_2eproto;
};

// -------------------------------------------------------------------

class ModelProto_OptimizationParams final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.data.model.ModelProto.OptimizationParams) */ {
 public:
  inline ModelProto_OptimizationParams() : ModelProto_OptimizationParams(nullptr) {}
  ~ModelProto_OptimizationParams() override;
  explicit PROTOBUF_CONSTEXPR ModelProto_OptimizationParams(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ModelProto_OptimizationParams(const ModelProto_OptimizationParams& from);
  ModelProto_OptimizationParams(ModelProto_OptimizationParams&& from) noexcept
    : ModelProto_OptimizationParams() {
    *this = ::std::move(from);
  }

  inline ModelProto_OptimizationParams& operator=(const ModelProto_OptimizationParams& from) {
    CopyFrom(from);
    return *this;
  }
  inline ModelProto_OptimizationParams& operator=(ModelProto_OptimizationParams&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ModelProto_OptimizationParams& default_instance() {
    return *internal_default_instance();
  }
  static inline const ModelProto_OptimizationParams* internal_default_instance() {
    return reinterpret_cast<const ModelProto_OptimizationParams*>(
               &_ModelProto_OptimizationParams_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(ModelProto_OptimizationParams& a, ModelProto_OptimizationParams& b) {
    a.Swap(&b);
  }
  inline void Swap(ModelProto_OptimizationParams* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ModelProto_OptimizationParams* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ModelProto_OptimizationParams* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ModelProto_OptimizationParams>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ModelProto_OptimizationParams& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ModelProto_OptimizationParams& from) {
    ModelProto_OptimizationParams::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ModelProto_OptimizationParams* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.data.model.ModelProto.OptimizationParams";
  }
  protected:
  explicit ModelProto_OptimizationParams(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCpuBudgetFieldNumber = 2,
    kRamBudgetFieldNumber = 3,
    kModelInputTimeFieldNumber = 4,
    kAlgorithmFieldNumber = 1,
  };
  // int64 cpu_budget = 2;
  void clear_cpu_budget();
  int64_t cpu_budget() const;
  void set_cpu_budget(int64_t value);
  private:
  int64_t _internal_cpu_budget() const;
  void _internal_set_cpu_budget(int64_t value);
  public:

  // int64 ram_budget = 3;
  void clear_ram_budget();
  int64_t ram_budget() const;
  void set_ram_budget(int64_t value);
  private:
  int64_t _internal_ram_budget() const;
  void _internal_set_ram_budget(int64_t value);
  public:

  // double model_input_time = 4;
  void clear_model_input_time();
  double model_input_time() const;
  void set_model_input_time(double value);
  private:
  double _internal_model_input_time() const;
  void _internal_set_model_input_time(double value);
  public:

  // .tensorflow.data.model.AutotuneAlgorithm algorithm = 1;
  void clear_algorithm();
  ::tensorflow::data::model::AutotuneAlgorithm algorithm() const;
  void set_algorithm(::tensorflow::data::model::AutotuneAlgorithm value);
  private:
  ::tensorflow::data::model::AutotuneAlgorithm _internal_algorithm() const;
  void _internal_set_algorithm(::tensorflow::data::model::AutotuneAlgorithm value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.data.model.ModelProto.OptimizationParams)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int64_t cpu_budget_;
    int64_t ram_budget_;
    double model_input_time_;
    int algorithm_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fmodel_2eproto;
};
// -------------------------------------------------------------------

class ModelProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.data.model.ModelProto) */ {
 public:
  inline ModelProto() : ModelProto(nullptr) {}
  ~ModelProto() override;
  explicit PROTOBUF_CONSTEXPR ModelProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ModelProto(const ModelProto& from);
  ModelProto(ModelProto&& from) noexcept
    : ModelProto() {
    *this = ::std::move(from);
  }

  inline ModelProto& operator=(const ModelProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline ModelProto& operator=(ModelProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ModelProto& default_instance() {
    return *internal_default_instance();
  }
  static inline const ModelProto* internal_default_instance() {
    return reinterpret_cast<const ModelProto*>(
               &_ModelProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(ModelProto& a, ModelProto& b) {
    a.Swap(&b);
  }
  inline void Swap(ModelProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ModelProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ModelProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ModelProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ModelProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ModelProto& from) {
    ModelProto::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ModelProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.data.model.ModelProto";
  }
  protected:
  explicit ModelProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef ModelProto_Node Node;
  typedef ModelProto_OptimizationParams OptimizationParams;

  // accessors -------------------------------------------------------

  enum : int {
    kNodesFieldNumber = 1,
    kGapTimesFieldNumber = 6,
    kDatasetNameFieldNumber = 7,
    kOptimizationParamsFieldNumber = 5,
    kOutputFieldNumber = 2,
    kIdCounterFieldNumber = 3,
  };
  // map<int64, .tensorflow.data.model.ModelProto.Node> nodes = 1;
  int nodes_size() const;
  private:
  int _internal_nodes_size() const;
  public:
  void clear_nodes();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< int64_t, ::tensorflow::data::model::ModelProto_Node >&
      _internal_nodes() const;
  ::PROTOBUF_NAMESPACE_ID::Map< int64_t, ::tensorflow::data::model::ModelProto_Node >*
      _internal_mutable_nodes();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< int64_t, ::tensorflow::data::model::ModelProto_Node >&
      nodes() const;
  ::PROTOBUF_NAMESPACE_ID::Map< int64_t, ::tensorflow::data::model::ModelProto_Node >*
      mutable_nodes();

  // repeated uint64 gap_times = 6;
  int gap_times_size() const;
  private:
  int _internal_gap_times_size() const;
  public:
  void clear_gap_times();
  private:
  uint64_t _internal_gap_times(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
      _internal_gap_times() const;
  void _internal_add_gap_times(uint64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
      _internal_mutable_gap_times();
  public:
  uint64_t gap_times(int index) const;
  void set_gap_times(int index, uint64_t value);
  void add_gap_times(uint64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
      gap_times() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
      mutable_gap_times();

  // string dataset_name = 7;
  void clear_dataset_name();
  const std::string& dataset_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_dataset_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_dataset_name();
  PROTOBUF_NODISCARD std::string* release_dataset_name();
  void set_allocated_dataset_name(std::string* dataset_name);
  private:
  const std::string& _internal_dataset_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_dataset_name(const std::string& value);
  std::string* _internal_mutable_dataset_name();
  public:

  // .tensorflow.data.model.ModelProto.OptimizationParams optimization_params = 5;
  bool has_optimization_params() const;
  private:
  bool _internal_has_optimization_params() const;
  public:
  void clear_optimization_params();
  const ::tensorflow::data::model::ModelProto_OptimizationParams& optimization_params() const;
  PROTOBUF_NODISCARD ::tensorflow::data::model::ModelProto_OptimizationParams* release_optimization_params();
  ::tensorflow::data::model::ModelProto_OptimizationParams* mutable_optimization_params();
  void set_allocated_optimization_params(::tensorflow::data::model::ModelProto_OptimizationParams* optimization_params);
  private:
  const ::tensorflow::data::model::ModelProto_OptimizationParams& _internal_optimization_params() const;
  ::tensorflow::data::model::ModelProto_OptimizationParams* _internal_mutable_optimization_params();
  public:
  void unsafe_arena_set_allocated_optimization_params(
      ::tensorflow::data::model::ModelProto_OptimizationParams* optimization_params);
  ::tensorflow::data::model::ModelProto_OptimizationParams* unsafe_arena_release_optimization_params();

  // int64 output = 2;
  void clear_output();
  int64_t output() const;
  void set_output(int64_t value);
  private:
  int64_t _internal_output() const;
  void _internal_set_output(int64_t value);
  public:

  // int64 id_counter = 3;
  void clear_id_counter();
  int64_t id_counter() const;
  void set_id_counter(int64_t value);
  private:
  int64_t _internal_id_counter() const;
  void _internal_set_id_counter(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.data.model.ModelProto)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        ModelProto_NodesEntry_DoNotUse,
        int64_t, ::tensorflow::data::model::ModelProto_Node,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> nodes_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t > gap_times_;
    mutable std::atomic<int> _gap_times_cached_byte_size_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr dataset_name_;
    ::tensorflow::data::model::ModelProto_OptimizationParams* optimization_params_;
    int64_t output_;
    int64_t id_counter_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fmodel_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// ModelProto_Node_Parameter

// string name = 1;
inline void ModelProto_Node_Parameter::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& ModelProto_Node_Parameter::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.model.ModelProto.Node.Parameter.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ModelProto_Node_Parameter::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.data.model.ModelProto.Node.Parameter.name)
}
inline std::string* ModelProto_Node_Parameter::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.data.model.ModelProto.Node.Parameter.name)
  return _s;
}
inline const std::string& ModelProto_Node_Parameter::_internal_name() const {
  return _impl_.name_.Get();
}
inline void ModelProto_Node_Parameter::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* ModelProto_Node_Parameter::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* ModelProto_Node_Parameter::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.data.model.ModelProto.Node.Parameter.name)
  return _impl_.name_.Release();
}
inline void ModelProto_Node_Parameter::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.data.model.ModelProto.Node.Parameter.name)
}

// double value = 2;
inline void ModelProto_Node_Parameter::clear_value() {
  _impl_.value_ = 0;
}
inline double ModelProto_Node_Parameter::_internal_value() const {
  return _impl_.value_;
}
inline double ModelProto_Node_Parameter::value() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.model.ModelProto.Node.Parameter.value)
  return _internal_value();
}
inline void ModelProto_Node_Parameter::_internal_set_value(double value) {
  
  _impl_.value_ = value;
}
inline void ModelProto_Node_Parameter::set_value(double value) {
  _internal_set_value(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.model.ModelProto.Node.Parameter.value)
}

// double state_value = 3;
inline void ModelProto_Node_Parameter::clear_state_value() {
  _impl_.state_value_ = 0;
}
inline double ModelProto_Node_Parameter::_internal_state_value() const {
  return _impl_.state_value_;
}
inline double ModelProto_Node_Parameter::state_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.model.ModelProto.Node.Parameter.state_value)
  return _internal_state_value();
}
inline void ModelProto_Node_Parameter::_internal_set_state_value(double value) {
  
  _impl_.state_value_ = value;
}
inline void ModelProto_Node_Parameter::set_state_value(double value) {
  _internal_set_state_value(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.model.ModelProto.Node.Parameter.state_value)
}

// double min = 4;
inline void ModelProto_Node_Parameter::clear_min() {
  _impl_.min_ = 0;
}
inline double ModelProto_Node_Parameter::_internal_min() const {
  return _impl_.min_;
}
inline double ModelProto_Node_Parameter::min() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.model.ModelProto.Node.Parameter.min)
  return _internal_min();
}
inline void ModelProto_Node_Parameter::_internal_set_min(double value) {
  
  _impl_.min_ = value;
}
inline void ModelProto_Node_Parameter::set_min(double value) {
  _internal_set_min(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.model.ModelProto.Node.Parameter.min)
}

// double max = 5;
inline void ModelProto_Node_Parameter::clear_max() {
  _impl_.max_ = 0;
}
inline double ModelProto_Node_Parameter::_internal_max() const {
  return _impl_.max_;
}
inline double ModelProto_Node_Parameter::max() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.model.ModelProto.Node.Parameter.max)
  return _internal_max();
}
inline void ModelProto_Node_Parameter::_internal_set_max(double value) {
  
  _impl_.max_ = value;
}
inline void ModelProto_Node_Parameter::set_max(double value) {
  _internal_set_max(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.model.ModelProto.Node.Parameter.max)
}

// bool tunable = 6;
inline void ModelProto_Node_Parameter::clear_tunable() {
  _impl_.tunable_ = false;
}
inline bool ModelProto_Node_Parameter::_internal_tunable() const {
  return _impl_.tunable_;
}
inline bool ModelProto_Node_Parameter::tunable() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.model.ModelProto.Node.Parameter.tunable)
  return _internal_tunable();
}
inline void ModelProto_Node_Parameter::_internal_set_tunable(bool value) {
  
  _impl_.tunable_ = value;
}
inline void ModelProto_Node_Parameter::set_tunable(bool value) {
  _internal_set_tunable(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.model.ModelProto.Node.Parameter.tunable)
}

// -------------------------------------------------------------------

// ModelProto_Node

// int64 id = 1;
inline void ModelProto_Node::clear_id() {
  _impl_.id_ = int64_t{0};
}
inline int64_t ModelProto_Node::_internal_id() const {
  return _impl_.id_;
}
inline int64_t ModelProto_Node::id() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.model.ModelProto.Node.id)
  return _internal_id();
}
inline void ModelProto_Node::_internal_set_id(int64_t value) {
  
  _impl_.id_ = value;
}
inline void ModelProto_Node::set_id(int64_t value) {
  _internal_set_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.model.ModelProto.Node.id)
}

// string name = 2;
inline void ModelProto_Node::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& ModelProto_Node::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.model.ModelProto.Node.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ModelProto_Node::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.data.model.ModelProto.Node.name)
}
inline std::string* ModelProto_Node::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.data.model.ModelProto.Node.name)
  return _s;
}
inline const std::string& ModelProto_Node::_internal_name() const {
  return _impl_.name_.Get();
}
inline void ModelProto_Node::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* ModelProto_Node::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* ModelProto_Node::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.data.model.ModelProto.Node.name)
  return _impl_.name_.Release();
}
inline void ModelProto_Node::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.data.model.ModelProto.Node.name)
}

// bool autotune = 3;
inline void ModelProto_Node::clear_autotune() {
  _impl_.autotune_ = false;
}
inline bool ModelProto_Node::_internal_autotune() const {
  return _impl_.autotune_;
}
inline bool ModelProto_Node::autotune() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.model.ModelProto.Node.autotune)
  return _internal_autotune();
}
inline void ModelProto_Node::_internal_set_autotune(bool value) {
  
  _impl_.autotune_ = value;
}
inline void ModelProto_Node::set_autotune(bool value) {
  _internal_set_autotune(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.model.ModelProto.Node.autotune)
}

// int64 buffered_bytes = 4;
inline void ModelProto_Node::clear_buffered_bytes() {
  _impl_.buffered_bytes_ = int64_t{0};
}
inline int64_t ModelProto_Node::_internal_buffered_bytes() const {
  return _impl_.buffered_bytes_;
}
inline int64_t ModelProto_Node::buffered_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.model.ModelProto.Node.buffered_bytes)
  return _internal_buffered_bytes();
}
inline void ModelProto_Node::_internal_set_buffered_bytes(int64_t value) {
  
  _impl_.buffered_bytes_ = value;
}
inline void ModelProto_Node::set_buffered_bytes(int64_t value) {
  _internal_set_buffered_bytes(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.model.ModelProto.Node.buffered_bytes)
}

// int64 buffered_elements = 5;
inline void ModelProto_Node::clear_buffered_elements() {
  _impl_.buffered_elements_ = int64_t{0};
}
inline int64_t ModelProto_Node::_internal_buffered_elements() const {
  return _impl_.buffered_elements_;
}
inline int64_t ModelProto_Node::buffered_elements() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.model.ModelProto.Node.buffered_elements)
  return _internal_buffered_elements();
}
inline void ModelProto_Node::_internal_set_buffered_elements(int64_t value) {
  
  _impl_.buffered_elements_ = value;
}
inline void ModelProto_Node::set_buffered_elements(int64_t value) {
  _internal_set_buffered_elements(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.model.ModelProto.Node.buffered_elements)
}

// int64 bytes_consumed = 6;
inline void ModelProto_Node::clear_bytes_consumed() {
  _impl_.bytes_consumed_ = int64_t{0};
}
inline int64_t ModelProto_Node::_internal_bytes_consumed() const {
  return _impl_.bytes_consumed_;
}
inline int64_t ModelProto_Node::bytes_consumed() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.model.ModelProto.Node.bytes_consumed)
  return _internal_bytes_consumed();
}
inline void ModelProto_Node::_internal_set_bytes_consumed(int64_t value) {
  
  _impl_.bytes_consumed_ = value;
}
inline void ModelProto_Node::set_bytes_consumed(int64_t value) {
  _internal_set_bytes_consumed(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.model.ModelProto.Node.bytes_consumed)
}

// int64 bytes_produced = 7;
inline void ModelProto_Node::clear_bytes_produced() {
  _impl_.bytes_produced_ = int64_t{0};
}
inline int64_t ModelProto_Node::_internal_bytes_produced() const {
  return _impl_.bytes_produced_;
}
inline int64_t ModelProto_Node::bytes_produced() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.model.ModelProto.Node.bytes_produced)
  return _internal_bytes_produced();
}
inline void ModelProto_Node::_internal_set_bytes_produced(int64_t value) {
  
  _impl_.bytes_produced_ = value;
}
inline void ModelProto_Node::set_bytes_produced(int64_t value) {
  _internal_set_bytes_produced(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.model.ModelProto.Node.bytes_produced)
}

// int64 num_elements = 8;
inline void ModelProto_Node::clear_num_elements() {
  _impl_.num_elements_ = int64_t{0};
}
inline int64_t ModelProto_Node::_internal_num_elements() const {
  return _impl_.num_elements_;
}
inline int64_t ModelProto_Node::num_elements() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.model.ModelProto.Node.num_elements)
  return _internal_num_elements();
}
inline void ModelProto_Node::_internal_set_num_elements(int64_t value) {
  
  _impl_.num_elements_ = value;
}
inline void ModelProto_Node::set_num_elements(int64_t value) {
  _internal_set_num_elements(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.model.ModelProto.Node.num_elements)
}

// int64 processing_time = 9;
inline void ModelProto_Node::clear_processing_time() {
  _impl_.processing_time_ = int64_t{0};
}
inline int64_t ModelProto_Node::_internal_processing_time() const {
  return _impl_.processing_time_;
}
inline int64_t ModelProto_Node::processing_time() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.model.ModelProto.Node.processing_time)
  return _internal_processing_time();
}
inline void ModelProto_Node::_internal_set_processing_time(int64_t value) {
  
  _impl_.processing_time_ = value;
}
inline void ModelProto_Node::set_processing_time(int64_t value) {
  _internal_set_processing_time(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.model.ModelProto.Node.processing_time)
}

// bool record_metrics = 10;
inline void ModelProto_Node::clear_record_metrics() {
  _impl_.record_metrics_ = false;
}
inline bool ModelProto_Node::_internal_record_metrics() const {
  return _impl_.record_metrics_;
}
inline bool ModelProto_Node::record_metrics() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.model.ModelProto.Node.record_metrics)
  return _internal_record_metrics();
}
inline void ModelProto_Node::_internal_set_record_metrics(bool value) {
  
  _impl_.record_metrics_ = value;
}
inline void ModelProto_Node::set_record_metrics(bool value) {
  _internal_set_record_metrics(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.model.ModelProto.Node.record_metrics)
}

// repeated .tensorflow.data.model.ModelProto.Node.Parameter parameters = 11;
inline int ModelProto_Node::_internal_parameters_size() const {
  return _impl_.parameters_.size();
}
inline int ModelProto_Node::parameters_size() const {
  return _internal_parameters_size();
}
inline void ModelProto_Node::clear_parameters() {
  _impl_.parameters_.Clear();
}
inline ::tensorflow::data::model::ModelProto_Node_Parameter* ModelProto_Node::mutable_parameters(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.data.model.ModelProto.Node.parameters)
  return _impl_.parameters_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::data::model::ModelProto_Node_Parameter >*
ModelProto_Node::mutable_parameters() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.data.model.ModelProto.Node.parameters)
  return &_impl_.parameters_;
}
inline const ::tensorflow::data::model::ModelProto_Node_Parameter& ModelProto_Node::_internal_parameters(int index) const {
  return _impl_.parameters_.Get(index);
}
inline const ::tensorflow::data::model::ModelProto_Node_Parameter& ModelProto_Node::parameters(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.data.model.ModelProto.Node.parameters)
  return _internal_parameters(index);
}
inline ::tensorflow::data::model::ModelProto_Node_Parameter* ModelProto_Node::_internal_add_parameters() {
  return _impl_.parameters_.Add();
}
inline ::tensorflow::data::model::ModelProto_Node_Parameter* ModelProto_Node::add_parameters() {
  ::tensorflow::data::model::ModelProto_Node_Parameter* _add = _internal_add_parameters();
  // @@protoc_insertion_point(field_add:tensorflow.data.model.ModelProto.Node.parameters)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::data::model::ModelProto_Node_Parameter >&
ModelProto_Node::parameters() const {
  // @@protoc_insertion_point(field_list:tensorflow.data.model.ModelProto.Node.parameters)
  return _impl_.parameters_;
}

// double input_processing_time_sum = 12;
inline void ModelProto_Node::clear_input_processing_time_sum() {
  _impl_.input_processing_time_sum_ = 0;
}
inline double ModelProto_Node::_internal_input_processing_time_sum() const {
  return _impl_.input_processing_time_sum_;
}
inline double ModelProto_Node::input_processing_time_sum() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.model.ModelProto.Node.input_processing_time_sum)
  return _internal_input_processing_time_sum();
}
inline void ModelProto_Node::_internal_set_input_processing_time_sum(double value) {
  
  _impl_.input_processing_time_sum_ = value;
}
inline void ModelProto_Node::set_input_processing_time_sum(double value) {
  _internal_set_input_processing_time_sum(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.model.ModelProto.Node.input_processing_time_sum)
}

// int64 input_processing_time_count = 13;
inline void ModelProto_Node::clear_input_processing_time_count() {
  _impl_.input_processing_time_count_ = int64_t{0};
}
inline int64_t ModelProto_Node::_internal_input_processing_time_count() const {
  return _impl_.input_processing_time_count_;
}
inline int64_t ModelProto_Node::input_processing_time_count() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.model.ModelProto.Node.input_processing_time_count)
  return _internal_input_processing_time_count();
}
inline void ModelProto_Node::_internal_set_input_processing_time_count(int64_t value) {
  
  _impl_.input_processing_time_count_ = value;
}
inline void ModelProto_Node::set_input_processing_time_count(int64_t value) {
  _internal_set_input_processing_time_count(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.model.ModelProto.Node.input_processing_time_count)
}

// repeated int64 inputs = 14;
inline int ModelProto_Node::_internal_inputs_size() const {
  return _impl_.inputs_.size();
}
inline int ModelProto_Node::inputs_size() const {
  return _internal_inputs_size();
}
inline void ModelProto_Node::clear_inputs() {
  _impl_.inputs_.Clear();
}
inline int64_t ModelProto_Node::_internal_inputs(int index) const {
  return _impl_.inputs_.Get(index);
}
inline int64_t ModelProto_Node::inputs(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.data.model.ModelProto.Node.inputs)
  return _internal_inputs(index);
}
inline void ModelProto_Node::set_inputs(int index, int64_t value) {
  _impl_.inputs_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.data.model.ModelProto.Node.inputs)
}
inline void ModelProto_Node::_internal_add_inputs(int64_t value) {
  _impl_.inputs_.Add(value);
}
inline void ModelProto_Node::add_inputs(int64_t value) {
  _internal_add_inputs(value);
  // @@protoc_insertion_point(field_add:tensorflow.data.model.ModelProto.Node.inputs)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
ModelProto_Node::_internal_inputs() const {
  return _impl_.inputs_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
ModelProto_Node::inputs() const {
  // @@protoc_insertion_point(field_list:tensorflow.data.model.ModelProto.Node.inputs)
  return _internal_inputs();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
ModelProto_Node::_internal_mutable_inputs() {
  return &_impl_.inputs_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
ModelProto_Node::mutable_inputs() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.data.model.ModelProto.Node.inputs)
  return _internal_mutable_inputs();
}

// .tensorflow.data.model.NodeClass node_class = 15;
inline void ModelProto_Node::clear_node_class() {
  _impl_.node_class_ = 0;
}
inline ::tensorflow::data::model::NodeClass ModelProto_Node::_internal_node_class() const {
  return static_cast< ::tensorflow::data::model::NodeClass >(_impl_.node_class_);
}
inline ::tensorflow::data::model::NodeClass ModelProto_Node::node_class() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.model.ModelProto.Node.node_class)
  return _internal_node_class();
}
inline void ModelProto_Node::_internal_set_node_class(::tensorflow::data::model::NodeClass value) {
  
  _impl_.node_class_ = value;
}
inline void ModelProto_Node::set_node_class(::tensorflow::data::model::NodeClass value) {
  _internal_set_node_class(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.model.ModelProto.Node.node_class)
}

// double ratio = 16;
inline void ModelProto_Node::clear_ratio() {
  _impl_.ratio_ = 0;
}
inline double ModelProto_Node::_internal_ratio() const {
  return _impl_.ratio_;
}
inline double ModelProto_Node::ratio() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.model.ModelProto.Node.ratio)
  return _internal_ratio();
}
inline void ModelProto_Node::_internal_set_ratio(double value) {
  
  _impl_.ratio_ = value;
}
inline void ModelProto_Node::set_ratio(double value) {
  _internal_set_ratio(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.model.ModelProto.Node.ratio)
}

// double memory_ratio = 17;
inline void ModelProto_Node::clear_memory_ratio() {
  _impl_.memory_ratio_ = 0;
}
inline double ModelProto_Node::_internal_memory_ratio() const {
  return _impl_.memory_ratio_;
}
inline double ModelProto_Node::memory_ratio() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.model.ModelProto.Node.memory_ratio)
  return _internal_memory_ratio();
}
inline void ModelProto_Node::_internal_set_memory_ratio(double value) {
  
  _impl_.memory_ratio_ = value;
}
inline void ModelProto_Node::set_memory_ratio(double value) {
  _internal_set_memory_ratio(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.model.ModelProto.Node.memory_ratio)
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// ModelProto_OptimizationParams

// .tensorflow.data.model.AutotuneAlgorithm algorithm = 1;
inline void ModelProto_OptimizationParams::clear_algorithm() {
  _impl_.algorithm_ = 0;
}
inline ::tensorflow::data::model::AutotuneAlgorithm ModelProto_OptimizationParams::_internal_algorithm() const {
  return static_cast< ::tensorflow::data::model::AutotuneAlgorithm >(_impl_.algorithm_);
}
inline ::tensorflow::data::model::AutotuneAlgorithm ModelProto_OptimizationParams::algorithm() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.model.ModelProto.OptimizationParams.algorithm)
  return _internal_algorithm();
}
inline void ModelProto_OptimizationParams::_internal_set_algorithm(::tensorflow::data::model::AutotuneAlgorithm value) {
  
  _impl_.algorithm_ = value;
}
inline void ModelProto_OptimizationParams::set_algorithm(::tensorflow::data::model::AutotuneAlgorithm value) {
  _internal_set_algorithm(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.model.ModelProto.OptimizationParams.algorithm)
}

// int64 cpu_budget = 2;
inline void ModelProto_OptimizationParams::clear_cpu_budget() {
  _impl_.cpu_budget_ = int64_t{0};
}
inline int64_t ModelProto_OptimizationParams::_internal_cpu_budget() const {
  return _impl_.cpu_budget_;
}
inline int64_t ModelProto_OptimizationParams::cpu_budget() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.model.ModelProto.OptimizationParams.cpu_budget)
  return _internal_cpu_budget();
}
inline void ModelProto_OptimizationParams::_internal_set_cpu_budget(int64_t value) {
  
  _impl_.cpu_budget_ = value;
}
inline void ModelProto_OptimizationParams::set_cpu_budget(int64_t value) {
  _internal_set_cpu_budget(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.model.ModelProto.OptimizationParams.cpu_budget)
}

// int64 ram_budget = 3;
inline void ModelProto_OptimizationParams::clear_ram_budget() {
  _impl_.ram_budget_ = int64_t{0};
}
inline int64_t ModelProto_OptimizationParams::_internal_ram_budget() const {
  return _impl_.ram_budget_;
}
inline int64_t ModelProto_OptimizationParams::ram_budget() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.model.ModelProto.OptimizationParams.ram_budget)
  return _internal_ram_budget();
}
inline void ModelProto_OptimizationParams::_internal_set_ram_budget(int64_t value) {
  
  _impl_.ram_budget_ = value;
}
inline void ModelProto_OptimizationParams::set_ram_budget(int64_t value) {
  _internal_set_ram_budget(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.model.ModelProto.OptimizationParams.ram_budget)
}

// double model_input_time = 4;
inline void ModelProto_OptimizationParams::clear_model_input_time() {
  _impl_.model_input_time_ = 0;
}
inline double ModelProto_OptimizationParams::_internal_model_input_time() const {
  return _impl_.model_input_time_;
}
inline double ModelProto_OptimizationParams::model_input_time() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.model.ModelProto.OptimizationParams.model_input_time)
  return _internal_model_input_time();
}
inline void ModelProto_OptimizationParams::_internal_set_model_input_time(double value) {
  
  _impl_.model_input_time_ = value;
}
inline void ModelProto_OptimizationParams::set_model_input_time(double value) {
  _internal_set_model_input_time(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.model.ModelProto.OptimizationParams.model_input_time)
}

// -------------------------------------------------------------------

// ModelProto

// string dataset_name = 7;
inline void ModelProto::clear_dataset_name() {
  _impl_.dataset_name_.ClearToEmpty();
}
inline const std::string& ModelProto::dataset_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.model.ModelProto.dataset_name)
  return _internal_dataset_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ModelProto::set_dataset_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.dataset_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.data.model.ModelProto.dataset_name)
}
inline std::string* ModelProto::mutable_dataset_name() {
  std::string* _s = _internal_mutable_dataset_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.data.model.ModelProto.dataset_name)
  return _s;
}
inline const std::string& ModelProto::_internal_dataset_name() const {
  return _impl_.dataset_name_.Get();
}
inline void ModelProto::_internal_set_dataset_name(const std::string& value) {
  
  _impl_.dataset_name_.Set(value, GetArenaForAllocation());
}
inline std::string* ModelProto::_internal_mutable_dataset_name() {
  
  return _impl_.dataset_name_.Mutable(GetArenaForAllocation());
}
inline std::string* ModelProto::release_dataset_name() {
  // @@protoc_insertion_point(field_release:tensorflow.data.model.ModelProto.dataset_name)
  return _impl_.dataset_name_.Release();
}
inline void ModelProto::set_allocated_dataset_name(std::string* dataset_name) {
  if (dataset_name != nullptr) {
    
  } else {
    
  }
  _impl_.dataset_name_.SetAllocated(dataset_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.dataset_name_.IsDefault()) {
    _impl_.dataset_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.data.model.ModelProto.dataset_name)
}

// map<int64, .tensorflow.data.model.ModelProto.Node> nodes = 1;
inline int ModelProto::_internal_nodes_size() const {
  return _impl_.nodes_.size();
}
inline int ModelProto::nodes_size() const {
  return _internal_nodes_size();
}
inline void ModelProto::clear_nodes() {
  _impl_.nodes_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< int64_t, ::tensorflow::data::model::ModelProto_Node >&
ModelProto::_internal_nodes() const {
  return _impl_.nodes_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< int64_t, ::tensorflow::data::model::ModelProto_Node >&
ModelProto::nodes() const {
  // @@protoc_insertion_point(field_map:tensorflow.data.model.ModelProto.nodes)
  return _internal_nodes();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< int64_t, ::tensorflow::data::model::ModelProto_Node >*
ModelProto::_internal_mutable_nodes() {
  return _impl_.nodes_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< int64_t, ::tensorflow::data::model::ModelProto_Node >*
ModelProto::mutable_nodes() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.data.model.ModelProto.nodes)
  return _internal_mutable_nodes();
}

// int64 output = 2;
inline void ModelProto::clear_output() {
  _impl_.output_ = int64_t{0};
}
inline int64_t ModelProto::_internal_output() const {
  return _impl_.output_;
}
inline int64_t ModelProto::output() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.model.ModelProto.output)
  return _internal_output();
}
inline void ModelProto::_internal_set_output(int64_t value) {
  
  _impl_.output_ = value;
}
inline void ModelProto::set_output(int64_t value) {
  _internal_set_output(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.model.ModelProto.output)
}

// int64 id_counter = 3;
inline void ModelProto::clear_id_counter() {
  _impl_.id_counter_ = int64_t{0};
}
inline int64_t ModelProto::_internal_id_counter() const {
  return _impl_.id_counter_;
}
inline int64_t ModelProto::id_counter() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.model.ModelProto.id_counter)
  return _internal_id_counter();
}
inline void ModelProto::_internal_set_id_counter(int64_t value) {
  
  _impl_.id_counter_ = value;
}
inline void ModelProto::set_id_counter(int64_t value) {
  _internal_set_id_counter(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.model.ModelProto.id_counter)
}

// .tensorflow.data.model.ModelProto.OptimizationParams optimization_params = 5;
inline bool ModelProto::_internal_has_optimization_params() const {
  return this != internal_default_instance() && _impl_.optimization_params_ != nullptr;
}
inline bool ModelProto::has_optimization_params() const {
  return _internal_has_optimization_params();
}
inline void ModelProto::clear_optimization_params() {
  if (GetArenaForAllocation() == nullptr && _impl_.optimization_params_ != nullptr) {
    delete _impl_.optimization_params_;
  }
  _impl_.optimization_params_ = nullptr;
}
inline const ::tensorflow::data::model::ModelProto_OptimizationParams& ModelProto::_internal_optimization_params() const {
  const ::tensorflow::data::model::ModelProto_OptimizationParams* p = _impl_.optimization_params_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::data::model::ModelProto_OptimizationParams&>(
      ::tensorflow::data::model::_ModelProto_OptimizationParams_default_instance_);
}
inline const ::tensorflow::data::model::ModelProto_OptimizationParams& ModelProto::optimization_params() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.model.ModelProto.optimization_params)
  return _internal_optimization_params();
}
inline void ModelProto::unsafe_arena_set_allocated_optimization_params(
    ::tensorflow::data::model::ModelProto_OptimizationParams* optimization_params) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.optimization_params_);
  }
  _impl_.optimization_params_ = optimization_params;
  if (optimization_params) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.data.model.ModelProto.optimization_params)
}
inline ::tensorflow::data::model::ModelProto_OptimizationParams* ModelProto::release_optimization_params() {
  
  ::tensorflow::data::model::ModelProto_OptimizationParams* temp = _impl_.optimization_params_;
  _impl_.optimization_params_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::data::model::ModelProto_OptimizationParams* ModelProto::unsafe_arena_release_optimization_params() {
  // @@protoc_insertion_point(field_release:tensorflow.data.model.ModelProto.optimization_params)
  
  ::tensorflow::data::model::ModelProto_OptimizationParams* temp = _impl_.optimization_params_;
  _impl_.optimization_params_ = nullptr;
  return temp;
}
inline ::tensorflow::data::model::ModelProto_OptimizationParams* ModelProto::_internal_mutable_optimization_params() {
  
  if (_impl_.optimization_params_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::data::model::ModelProto_OptimizationParams>(GetArenaForAllocation());
    _impl_.optimization_params_ = p;
  }
  return _impl_.optimization_params_;
}
inline ::tensorflow::data::model::ModelProto_OptimizationParams* ModelProto::mutable_optimization_params() {
  ::tensorflow::data::model::ModelProto_OptimizationParams* _msg = _internal_mutable_optimization_params();
  // @@protoc_insertion_point(field_mutable:tensorflow.data.model.ModelProto.optimization_params)
  return _msg;
}
inline void ModelProto::set_allocated_optimization_params(::tensorflow::data::model::ModelProto_OptimizationParams* optimization_params) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.optimization_params_;
  }
  if (optimization_params) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(optimization_params);
    if (message_arena != submessage_arena) {
      optimization_params = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, optimization_params, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.optimization_params_ = optimization_params;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.data.model.ModelProto.optimization_params)
}

// repeated uint64 gap_times = 6;
inline int ModelProto::_internal_gap_times_size() const {
  return _impl_.gap_times_.size();
}
inline int ModelProto::gap_times_size() const {
  return _internal_gap_times_size();
}
inline void ModelProto::clear_gap_times() {
  _impl_.gap_times_.Clear();
}
inline uint64_t ModelProto::_internal_gap_times(int index) const {
  return _impl_.gap_times_.Get(index);
}
inline uint64_t ModelProto::gap_times(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.data.model.ModelProto.gap_times)
  return _internal_gap_times(index);
}
inline void ModelProto::set_gap_times(int index, uint64_t value) {
  _impl_.gap_times_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.data.model.ModelProto.gap_times)
}
inline void ModelProto::_internal_add_gap_times(uint64_t value) {
  _impl_.gap_times_.Add(value);
}
inline void ModelProto::add_gap_times(uint64_t value) {
  _internal_add_gap_times(value);
  // @@protoc_insertion_point(field_add:tensorflow.data.model.ModelProto.gap_times)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
ModelProto::_internal_gap_times() const {
  return _impl_.gap_times_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
ModelProto::gap_times() const {
  // @@protoc_insertion_point(field_list:tensorflow.data.model.ModelProto.gap_times)
  return _internal_gap_times();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
ModelProto::_internal_mutable_gap_times() {
  return &_impl_.gap_times_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
ModelProto::mutable_gap_times() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.data.model.ModelProto.gap_times)
  return _internal_mutable_gap_times();
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace data
}  // namespace tensorflow

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::tensorflow::data::model::NodeClass> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::data::model::NodeClass>() {
  return ::tensorflow::data::model::NodeClass_descriptor();
}
template <> struct is_proto_enum< ::tensorflow::data::model::AutotuneAlgorithm> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::data::model::AutotuneAlgorithm>() {
  return ::tensorflow::data::model::AutotuneAlgorithm_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fmodel_2eproto
