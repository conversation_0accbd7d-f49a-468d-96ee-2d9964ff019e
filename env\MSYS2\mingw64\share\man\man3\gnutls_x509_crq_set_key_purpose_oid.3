.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crq_set_key_purpose_oid" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crq_set_key_purpose_oid \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crq_set_key_purpose_oid(gnutls_x509_crq_t " crq ", const void * " oid ", unsigned int " critical ");"
.SH ARGUMENTS
.IP "gnutls_x509_crq_t crq" 12
a certificate of type \fBgnutls_x509_crq_t\fP
.IP "const void * oid" 12
a pointer to a null\-terminated string that holds the OID
.IP "unsigned int critical" 12
Whether this extension will be critical or not
.SH "DESCRIPTION"
This function will set the key purpose OIDs of the Certificate.
These are stored in the Extended Key Usage extension (*********)
See the GNUTLS_KP_* definitions for human readable names.

Subsequent calls to this function will append OIDs to the OID list.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "SINCE"
2.8.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
