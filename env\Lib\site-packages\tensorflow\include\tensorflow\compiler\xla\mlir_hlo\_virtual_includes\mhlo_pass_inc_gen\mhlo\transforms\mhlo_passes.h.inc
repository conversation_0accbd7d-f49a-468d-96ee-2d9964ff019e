/* Autogenerated by mlir-tblgen; don't manually edit */

#ifdef GEN_PASS_DECL
// Generate declarations for all passes.
#define GEN_PASS_DECL_BROADCASTPROPAGATIONPASS
#define GEN_PASS_DECL_CHLOLEGALIZETOHIGHLEVELMHLOPASS
#define GEN_PASS_DECL_CHLOLEGALIZETOHLOPASS
#define GEN_PASS_DECL_COLLAPSEELEMENTWISEMAPPASS
#define GEN_PASS_DECL_CONVERTTOSIGNLESSPASS
#define GEN_PASS_DECL_EXPANDHLOTUPLESPASS
#define GEN_PASS_DECL_FLATTENTUPLEPASS
#define GEN_PASS_DECL_HLOLEGALIZETOARITHMETICPASS
#define GEN_PASS_DECL_HLOLEGALIZETOLINALGPASS
#define GEN_PASS_DECL_HLOLEGALIZETOSTABLEHLOPASS
#define GEN_PASS_DECL_LEGALIZEDOTTODOTGENERALPASS
#define GEN_PASS_DECL_LEGALIZEEINSUMTODOTGENERALPASS
#define GEN_PASS_DECL_LEGALIZETANHTOAPPROXIMATIONPASS
#define GEN_PASS_DECL_LEGALIZETORCHINDEXSELECTTOGATHERPASS
#define GEN_PASS_DECL_MERGEASSUMINGOPSPASS
#define GEN_PASS_DECL_PREPAREFOREXPORTPASS
#define GEN_PASS_DECL_SHAPELEGALIZETOHLOPASS
#define GEN_PASS_DECL_SHAPESIMPLIFICATION
#define GEN_PASS_DECL_SINKCONSTANTSTOCONTROLFLOWPASS
#define GEN_PASS_DECL_STABLEHLOLEGALIZETOHLOPASS
#define GEN_PASS_DECL_SYMBOLICSHAPEOPTIMIZATION
#define GEN_PASS_DECL_TESTINFERSHAPEDTYPEMETHODSPASS
#define GEN_PASS_DECL_TESTMATERIALIZEBROADCASTSPASS
#define GEN_PASS_DECL_TESTUNFUSEBATCHNORMPASS
#undef GEN_PASS_DECL
#endif // GEN_PASS_DECL

//===----------------------------------------------------------------------===//
// BroadcastPropagationPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_BROADCASTPROPAGATIONPASS
#undef GEN_PASS_DECL_BROADCASTPROPAGATIONPASS
#endif // GEN_PASS_DECL_BROADCASTPROPAGATIONPASS
#ifdef GEN_PASS_DEF_BROADCASTPROPAGATIONPASS
namespace impl {

template <typename DerivedT>
class BroadcastPropagationPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = BroadcastPropagationPassBase;

  BroadcastPropagationPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  BroadcastPropagationPassBase(const BroadcastPropagationPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  BroadcastPropagationPassBase& operator=(const BroadcastPropagationPassBase &) = delete;
  BroadcastPropagationPassBase(BroadcastPropagationPassBase &&) = delete;
  BroadcastPropagationPassBase& operator=(BroadcastPropagationPassBase &&) = delete;
  ~BroadcastPropagationPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("mhlo-broadcast-propagation");
  }
  ::llvm::StringRef getArgument() const override { return "mhlo-broadcast-propagation"; }

  ::llvm::StringRef getDescription() const override { return "Move dynamic broadcasts up over element-wise operations and broadcast the operands rather than the result. This will eventually allow for larger fusions."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("BroadcastPropagationPass");
  }
  ::llvm::StringRef getName() const override { return "BroadcastPropagationPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(BroadcastPropagationPassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_BROADCASTPROPAGATIONPASS
#endif // GEN_PASS_DEF_BROADCASTPROPAGATIONPASS

//===----------------------------------------------------------------------===//
// ChloLegalizeToHighLevelMhloPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CHLOLEGALIZETOHIGHLEVELMHLOPASS
std::unique_ptr<::mlir::Pass> createChloLegalizeToHighLevelMhloPass();
#undef GEN_PASS_DECL_CHLOLEGALIZETOHIGHLEVELMHLOPASS
#endif // GEN_PASS_DECL_CHLOLEGALIZETOHIGHLEVELMHLOPASS
#ifdef GEN_PASS_DEF_CHLOLEGALIZETOHIGHLEVELMHLOPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createChloLegalizeToHighLevelMhloPass();
} // namespace impl
namespace impl {

template <typename DerivedT>
class ChloLegalizeToHighLevelMhloPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = ChloLegalizeToHighLevelMhloPassBase;

  ChloLegalizeToHighLevelMhloPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  ChloLegalizeToHighLevelMhloPassBase(const ChloLegalizeToHighLevelMhloPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  ChloLegalizeToHighLevelMhloPassBase& operator=(const ChloLegalizeToHighLevelMhloPassBase &) = delete;
  ChloLegalizeToHighLevelMhloPassBase(ChloLegalizeToHighLevelMhloPassBase &&) = delete;
  ChloLegalizeToHighLevelMhloPassBase& operator=(ChloLegalizeToHighLevelMhloPassBase &&) = delete;
  ~ChloLegalizeToHighLevelMhloPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("chlo-legalize-to-high-level-mhlo");
  }
  ::llvm::StringRef getArgument() const override { return "chlo-legalize-to-high-level-mhlo"; }

  ::llvm::StringRef getDescription() const override { return "Legalize CHLO's with XLA counterparts, like TopK and Erf."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ChloLegalizeToHighLevelMhloPass");
  }
  ::llvm::StringRef getName() const override { return "ChloLegalizeToHighLevelMhloPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mhlo::MhloDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ChloLegalizeToHighLevelMhloPassBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createChloLegalizeToHighLevelMhloPass() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createChloLegalizeToHighLevelMhloPass() {
  return impl::createChloLegalizeToHighLevelMhloPass();
}
#undef GEN_PASS_DEF_CHLOLEGALIZETOHIGHLEVELMHLOPASS
#endif // GEN_PASS_DEF_CHLOLEGALIZETOHIGHLEVELMHLOPASS

//===----------------------------------------------------------------------===//
// ChloLegalizeToHloPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CHLOLEGALIZETOHLOPASS
std::unique_ptr<::mlir::Pass> createChloLegalizeToHloPass();
#undef GEN_PASS_DECL_CHLOLEGALIZETOHLOPASS
#endif // GEN_PASS_DECL_CHLOLEGALIZETOHLOPASS
#ifdef GEN_PASS_DEF_CHLOLEGALIZETOHLOPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createChloLegalizeToHloPass();
} // namespace impl
namespace impl {

template <typename DerivedT>
class ChloLegalizeToHloPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = ChloLegalizeToHloPassBase;

  ChloLegalizeToHloPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  ChloLegalizeToHloPassBase(const ChloLegalizeToHloPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  ChloLegalizeToHloPassBase& operator=(const ChloLegalizeToHloPassBase &) = delete;
  ChloLegalizeToHloPassBase(ChloLegalizeToHloPassBase &&) = delete;
  ChloLegalizeToHloPassBase& operator=(ChloLegalizeToHloPassBase &&) = delete;
  ~ChloLegalizeToHloPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("chlo-legalize-to-hlo");
  }
  ::llvm::StringRef getArgument() const override { return "chlo-legalize-to-hlo"; }

  ::llvm::StringRef getDescription() const override { return "Legalize CHLO to MHLO with XLA-supported ops."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ChloLegalizeToHloPass");
  }
  ::llvm::StringRef getName() const override { return "ChloLegalizeToHloPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mhlo::MhloDialect>();
    registry.insert<mlir::shape::ShapeDialect>();
    registry.insert<mlir::stablehlo::StablehloDialect>();
    registry.insert<mlir::tensor::TensorDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ChloLegalizeToHloPassBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createChloLegalizeToHloPass() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createChloLegalizeToHloPass() {
  return impl::createChloLegalizeToHloPass();
}
#undef GEN_PASS_DEF_CHLOLEGALIZETOHLOPASS
#endif // GEN_PASS_DEF_CHLOLEGALIZETOHLOPASS

//===----------------------------------------------------------------------===//
// CollapseElementwiseMapPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_COLLAPSEELEMENTWISEMAPPASS
#undef GEN_PASS_DECL_COLLAPSEELEMENTWISEMAPPASS
#endif // GEN_PASS_DECL_COLLAPSEELEMENTWISEMAPPASS
#ifdef GEN_PASS_DEF_COLLAPSEELEMENTWISEMAPPASS
namespace impl {

template <typename DerivedT>
class CollapseElementwiseMapPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = CollapseElementwiseMapPassBase;

  CollapseElementwiseMapPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  CollapseElementwiseMapPassBase(const CollapseElementwiseMapPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  CollapseElementwiseMapPassBase& operator=(const CollapseElementwiseMapPassBase &) = delete;
  CollapseElementwiseMapPassBase(CollapseElementwiseMapPassBase &&) = delete;
  CollapseElementwiseMapPassBase& operator=(CollapseElementwiseMapPassBase &&) = delete;
  ~CollapseElementwiseMapPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("mhlo-collapse-elementwise-map");
  }
  ::llvm::StringRef getArgument() const override { return "mhlo-collapse-elementwise-map"; }

  ::llvm::StringRef getDescription() const override { return "Collapse the mhlo.map if the map only has elementwise ops."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("CollapseElementwiseMapPass");
  }
  ::llvm::StringRef getName() const override { return "CollapseElementwiseMapPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(CollapseElementwiseMapPassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_COLLAPSEELEMENTWISEMAPPASS
#endif // GEN_PASS_DEF_COLLAPSEELEMENTWISEMAPPASS

//===----------------------------------------------------------------------===//
// ConvertToSignlessPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONVERTTOSIGNLESSPASS
#undef GEN_PASS_DECL_CONVERTTOSIGNLESSPASS
#endif // GEN_PASS_DECL_CONVERTTOSIGNLESSPASS
#ifdef GEN_PASS_DEF_CONVERTTOSIGNLESSPASS
namespace impl {

template <typename DerivedT>
class ConvertToSignlessPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertToSignlessPassBase;

  ConvertToSignlessPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertToSignlessPassBase(const ConvertToSignlessPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  ConvertToSignlessPassBase& operator=(const ConvertToSignlessPassBase &) = delete;
  ConvertToSignlessPassBase(ConvertToSignlessPassBase &&) = delete;
  ConvertToSignlessPassBase& operator=(ConvertToSignlessPassBase &&) = delete;
  ~ConvertToSignlessPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-to-signless");
  }
  ::llvm::StringRef getArgument() const override { return "convert-to-signless"; }

  ::llvm::StringRef getDescription() const override { return "Pass to transform the IR to be on signless integers."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertToSignlessPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertToSignlessPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertToSignlessPassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_CONVERTTOSIGNLESSPASS
#endif // GEN_PASS_DEF_CONVERTTOSIGNLESSPASS

//===----------------------------------------------------------------------===//
// ExpandHloTuplesPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_EXPANDHLOTUPLESPASS
struct ExpandHloTuplesPassOptions {
  std::string entry_function_name_;
};
#undef GEN_PASS_DECL_EXPANDHLOTUPLESPASS
#endif // GEN_PASS_DECL_EXPANDHLOTUPLESPASS
#ifdef GEN_PASS_DEF_EXPANDHLOTUPLESPASS
namespace impl {

template <typename DerivedT>
class ExpandHloTuplesPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ExpandHloTuplesPassBase;

  ExpandHloTuplesPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ExpandHloTuplesPassBase(const ExpandHloTuplesPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  ExpandHloTuplesPassBase& operator=(const ExpandHloTuplesPassBase &) = delete;
  ExpandHloTuplesPassBase(ExpandHloTuplesPassBase &&) = delete;
  ExpandHloTuplesPassBase& operator=(ExpandHloTuplesPassBase &&) = delete;
  ~ExpandHloTuplesPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("expand-hlo-tuples");
  }
  ::llvm::StringRef getArgument() const override { return "expand-hlo-tuples"; }

  ::llvm::StringRef getDescription() const override { return "Expand HLO tuple for the entry function of the module."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ExpandHloTuplesPass");
  }
  ::llvm::StringRef getName() const override { return "ExpandHloTuplesPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mhlo::MhloDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ExpandHloTuplesPassBase<DerivedT>)

  ExpandHloTuplesPassBase(ExpandHloTuplesPassOptions options) : ExpandHloTuplesPassBase() {
    entry_function_name_ = std::move(options.entry_function_name_);
  }
protected:
  ::mlir::Pass::Option<std::string> entry_function_name_{*this, "entry-function", ::llvm::cl::desc("the name of entry function of the module")};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_EXPANDHLOTUPLESPASS
#endif // GEN_PASS_DEF_EXPANDHLOTUPLESPASS

//===----------------------------------------------------------------------===//
// FlattenTuplePass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_FLATTENTUPLEPASS
#undef GEN_PASS_DECL_FLATTENTUPLEPASS
#endif // GEN_PASS_DECL_FLATTENTUPLEPASS
#ifdef GEN_PASS_DEF_FLATTENTUPLEPASS
namespace impl {

template <typename DerivedT>
class FlattenTuplePassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = FlattenTuplePassBase;

  FlattenTuplePassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  FlattenTuplePassBase(const FlattenTuplePassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  FlattenTuplePassBase& operator=(const FlattenTuplePassBase &) = delete;
  FlattenTuplePassBase(FlattenTuplePassBase &&) = delete;
  FlattenTuplePassBase& operator=(FlattenTuplePassBase &&) = delete;
  ~FlattenTuplePassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("mhlo-flatten-tuple");
  }
  ::llvm::StringRef getArgument() const override { return "mhlo-flatten-tuple"; }

  ::llvm::StringRef getDescription() const override { return "Flatten tuples in operands and results of operators that support both tuple and variadic type."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("FlattenTuplePass");
  }
  ::llvm::StringRef getName() const override { return "FlattenTuplePass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(FlattenTuplePassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_FLATTENTUPLEPASS
#endif // GEN_PASS_DEF_FLATTENTUPLEPASS

//===----------------------------------------------------------------------===//
// HloLegalizeToArithmeticPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_HLOLEGALIZETOARITHMETICPASS
#undef GEN_PASS_DECL_HLOLEGALIZETOARITHMETICPASS
#endif // GEN_PASS_DECL_HLOLEGALIZETOARITHMETICPASS
#ifdef GEN_PASS_DEF_HLOLEGALIZETOARITHMETICPASS
namespace impl {

template <typename DerivedT>
class HloLegalizeToArithmeticPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = HloLegalizeToArithmeticPassBase;

  HloLegalizeToArithmeticPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  HloLegalizeToArithmeticPassBase(const HloLegalizeToArithmeticPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  HloLegalizeToArithmeticPassBase& operator=(const HloLegalizeToArithmeticPassBase &) = delete;
  HloLegalizeToArithmeticPassBase(HloLegalizeToArithmeticPassBase &&) = delete;
  HloLegalizeToArithmeticPassBase& operator=(HloLegalizeToArithmeticPassBase &&) = delete;
  ~HloLegalizeToArithmeticPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("hlo-legalize-to-arithmetic");
  }
  ::llvm::StringRef getArgument() const override { return "hlo-legalize-to-arithmetic"; }

  ::llvm::StringRef getDescription() const override { return "Legalize from HLO dialect to arithmetic dialect."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("HloLegalizeToArithmeticPass");
  }
  ::llvm::StringRef getName() const override { return "HloLegalizeToArithmeticPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(HloLegalizeToArithmeticPassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_HLOLEGALIZETOARITHMETICPASS
#endif // GEN_PASS_DEF_HLOLEGALIZETOARITHMETICPASS

//===----------------------------------------------------------------------===//
// HloLegalizeToLinalgPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_HLOLEGALIZETOLINALGPASS
struct HloLegalizeToLinalgPassOptions {
  bool enablePrimitiveOps = false;
};
#undef GEN_PASS_DECL_HLOLEGALIZETOLINALGPASS
#endif // GEN_PASS_DECL_HLOLEGALIZETOLINALGPASS
#ifdef GEN_PASS_DEF_HLOLEGALIZETOLINALGPASS
namespace impl {

template <typename DerivedT>
class HloLegalizeToLinalgPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = HloLegalizeToLinalgPassBase;

  HloLegalizeToLinalgPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  HloLegalizeToLinalgPassBase(const HloLegalizeToLinalgPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  HloLegalizeToLinalgPassBase& operator=(const HloLegalizeToLinalgPassBase &) = delete;
  HloLegalizeToLinalgPassBase(HloLegalizeToLinalgPassBase &&) = delete;
  HloLegalizeToLinalgPassBase& operator=(HloLegalizeToLinalgPassBase &&) = delete;
  ~HloLegalizeToLinalgPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("hlo-legalize-to-linalg");
  }
  ::llvm::StringRef getArgument() const override { return "hlo-legalize-to-linalg"; }

  ::llvm::StringRef getDescription() const override { return "Legalize from HLO dialect to Linalg dialect."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("HloLegalizeToLinalgPass");
  }
  ::llvm::StringRef getName() const override { return "HloLegalizeToLinalgPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(HloLegalizeToLinalgPassBase<DerivedT>)

  HloLegalizeToLinalgPassBase(HloLegalizeToLinalgPassOptions options) : HloLegalizeToLinalgPassBase() {
    enablePrimitiveOps = std::move(options.enablePrimitiveOps);
  }
protected:
  ::mlir::Pass::Option<bool> enablePrimitiveOps{*this, "enable-primitive-ops", ::llvm::cl::desc("Lower to primitive Linalg ops (map, reduce and transpose) when possible, instead of linalg.generic"), ::llvm::cl::init(false)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_HLOLEGALIZETOLINALGPASS
#endif // GEN_PASS_DEF_HLOLEGALIZETOLINALGPASS

//===----------------------------------------------------------------------===//
// HloLegalizeToStablehloPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_HLOLEGALIZETOSTABLEHLOPASS
struct HloLegalizeToStablehloPassOptions {
  bool allow_experimental_features_ = false;
};
#undef GEN_PASS_DECL_HLOLEGALIZETOSTABLEHLOPASS
#endif // GEN_PASS_DECL_HLOLEGALIZETOSTABLEHLOPASS
#ifdef GEN_PASS_DEF_HLOLEGALIZETOSTABLEHLOPASS
namespace impl {

template <typename DerivedT>
class HloLegalizeToStablehloPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = HloLegalizeToStablehloPassBase;

  HloLegalizeToStablehloPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  HloLegalizeToStablehloPassBase(const HloLegalizeToStablehloPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  HloLegalizeToStablehloPassBase& operator=(const HloLegalizeToStablehloPassBase &) = delete;
  HloLegalizeToStablehloPassBase(HloLegalizeToStablehloPassBase &&) = delete;
  HloLegalizeToStablehloPassBase& operator=(HloLegalizeToStablehloPassBase &&) = delete;
  ~HloLegalizeToStablehloPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("hlo-legalize-to-stablehlo");
  }
  ::llvm::StringRef getArgument() const override { return "hlo-legalize-to-stablehlo"; }

  ::llvm::StringRef getDescription() const override { return "Legalize HLO to StableHLO."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("HloLegalizeToStablehloPass");
  }
  ::llvm::StringRef getName() const override { return "HloLegalizeToStablehloPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<stablehlo::StablehloDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(HloLegalizeToStablehloPassBase<DerivedT>)

  HloLegalizeToStablehloPassBase(HloLegalizeToStablehloPassOptions options) : HloLegalizeToStablehloPassBase() {
    allow_experimental_features_ = std::move(options.allow_experimental_features_);
  }
protected:
  ::mlir::Pass::Option<bool> allow_experimental_features_{*this, "allow-experimental-features", ::llvm::cl::desc("Allow legalization of experimental MHLO features via StableHLO custom_call"), ::llvm::cl::init(false)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_HLOLEGALIZETOSTABLEHLOPASS
#endif // GEN_PASS_DEF_HLOLEGALIZETOSTABLEHLOPASS

//===----------------------------------------------------------------------===//
// LegalizeDotToDotGeneralPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_LEGALIZEDOTTODOTGENERALPASS
#undef GEN_PASS_DECL_LEGALIZEDOTTODOTGENERALPASS
#endif // GEN_PASS_DECL_LEGALIZEDOTTODOTGENERALPASS
#ifdef GEN_PASS_DEF_LEGALIZEDOTTODOTGENERALPASS
namespace impl {

template <typename DerivedT>
class LegalizeDotToDotGeneralPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = LegalizeDotToDotGeneralPassBase;

  LegalizeDotToDotGeneralPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  LegalizeDotToDotGeneralPassBase(const LegalizeDotToDotGeneralPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  LegalizeDotToDotGeneralPassBase& operator=(const LegalizeDotToDotGeneralPassBase &) = delete;
  LegalizeDotToDotGeneralPassBase(LegalizeDotToDotGeneralPassBase &&) = delete;
  LegalizeDotToDotGeneralPassBase& operator=(LegalizeDotToDotGeneralPassBase &&) = delete;
  ~LegalizeDotToDotGeneralPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("mhlo-legalize-dot-to-dot-general");
  }
  ::llvm::StringRef getArgument() const override { return "mhlo-legalize-dot-to-dot-general"; }

  ::llvm::StringRef getDescription() const override { return "Legalizes dot ops to dot_general ops."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LegalizeDotToDotGeneralPass");
  }
  ::llvm::StringRef getName() const override { return "LegalizeDotToDotGeneralPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LegalizeDotToDotGeneralPassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_LEGALIZEDOTTODOTGENERALPASS
#endif // GEN_PASS_DEF_LEGALIZEDOTTODOTGENERALPASS

//===----------------------------------------------------------------------===//
// LegalizeEinsumToDotGeneralPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_LEGALIZEEINSUMTODOTGENERALPASS
#undef GEN_PASS_DECL_LEGALIZEEINSUMTODOTGENERALPASS
#endif // GEN_PASS_DECL_LEGALIZEEINSUMTODOTGENERALPASS
#ifdef GEN_PASS_DEF_LEGALIZEEINSUMTODOTGENERALPASS
namespace impl {

template <typename DerivedT>
class LegalizeEinsumToDotGeneralPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = LegalizeEinsumToDotGeneralPassBase;

  LegalizeEinsumToDotGeneralPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  LegalizeEinsumToDotGeneralPassBase(const LegalizeEinsumToDotGeneralPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  LegalizeEinsumToDotGeneralPassBase& operator=(const LegalizeEinsumToDotGeneralPassBase &) = delete;
  LegalizeEinsumToDotGeneralPassBase(LegalizeEinsumToDotGeneralPassBase &&) = delete;
  LegalizeEinsumToDotGeneralPassBase& operator=(LegalizeEinsumToDotGeneralPassBase &&) = delete;
  ~LegalizeEinsumToDotGeneralPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("mhlo-legalize-einsum-to-dot-general");
  }
  ::llvm::StringRef getArgument() const override { return "mhlo-legalize-einsum-to-dot-general"; }

  ::llvm::StringRef getDescription() const override { return "Legalizes einsum ops to dot_general ops."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LegalizeEinsumToDotGeneralPass");
  }
  ::llvm::StringRef getName() const override { return "LegalizeEinsumToDotGeneralPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LegalizeEinsumToDotGeneralPassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_LEGALIZEEINSUMTODOTGENERALPASS
#endif // GEN_PASS_DEF_LEGALIZEEINSUMTODOTGENERALPASS

//===----------------------------------------------------------------------===//
// LegalizeTanhToApproximationPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_LEGALIZETANHTOAPPROXIMATIONPASS
#undef GEN_PASS_DECL_LEGALIZETANHTOAPPROXIMATIONPASS
#endif // GEN_PASS_DECL_LEGALIZETANHTOAPPROXIMATIONPASS
#ifdef GEN_PASS_DEF_LEGALIZETANHTOAPPROXIMATIONPASS
namespace impl {

template <typename DerivedT>
class LegalizeTanhToApproximationPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = LegalizeTanhToApproximationPassBase;

  LegalizeTanhToApproximationPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  LegalizeTanhToApproximationPassBase(const LegalizeTanhToApproximationPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  LegalizeTanhToApproximationPassBase& operator=(const LegalizeTanhToApproximationPassBase &) = delete;
  LegalizeTanhToApproximationPassBase(LegalizeTanhToApproximationPassBase &&) = delete;
  LegalizeTanhToApproximationPassBase& operator=(LegalizeTanhToApproximationPassBase &&) = delete;
  ~LegalizeTanhToApproximationPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("mhlo-legalize-trigonometric-to-approximation");
  }
  ::llvm::StringRef getArgument() const override { return "mhlo-legalize-trigonometric-to-approximation"; }

  ::llvm::StringRef getDescription() const override { return "Legalize trigonometric operations from standard dialect to an approximation."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LegalizeTanhToApproximationPass");
  }
  ::llvm::StringRef getName() const override { return "LegalizeTanhToApproximationPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LegalizeTanhToApproximationPassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_LEGALIZETANHTOAPPROXIMATIONPASS
#endif // GEN_PASS_DEF_LEGALIZETANHTOAPPROXIMATIONPASS

//===----------------------------------------------------------------------===//
// LegalizeTorchIndexSelectToGatherPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_LEGALIZETORCHINDEXSELECTTOGATHERPASS
#undef GEN_PASS_DECL_LEGALIZETORCHINDEXSELECTTOGATHERPASS
#endif // GEN_PASS_DECL_LEGALIZETORCHINDEXSELECTTOGATHERPASS
#ifdef GEN_PASS_DEF_LEGALIZETORCHINDEXSELECTTOGATHERPASS
namespace impl {

template <typename DerivedT>
class LegalizeTorchIndexSelectToGatherPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = LegalizeTorchIndexSelectToGatherPassBase;

  LegalizeTorchIndexSelectToGatherPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  LegalizeTorchIndexSelectToGatherPassBase(const LegalizeTorchIndexSelectToGatherPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  LegalizeTorchIndexSelectToGatherPassBase& operator=(const LegalizeTorchIndexSelectToGatherPassBase &) = delete;
  LegalizeTorchIndexSelectToGatherPassBase(LegalizeTorchIndexSelectToGatherPassBase &&) = delete;
  LegalizeTorchIndexSelectToGatherPassBase& operator=(LegalizeTorchIndexSelectToGatherPassBase &&) = delete;
  ~LegalizeTorchIndexSelectToGatherPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("mhlo-legalize-torch-index-select-to-gather");
  }
  ::llvm::StringRef getArgument() const override { return "mhlo-legalize-torch-index-select-to-gather"; }

  ::llvm::StringRef getDescription() const override { return "Legalizes torch index select to a gather."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LegalizeTorchIndexSelectToGatherPass");
  }
  ::llvm::StringRef getName() const override { return "LegalizeTorchIndexSelectToGatherPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LegalizeTorchIndexSelectToGatherPassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_LEGALIZETORCHINDEXSELECTTOGATHERPASS
#endif // GEN_PASS_DEF_LEGALIZETORCHINDEXSELECTTOGATHERPASS

//===----------------------------------------------------------------------===//
// MergeAssumingOpsPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_MERGEASSUMINGOPSPASS
#undef GEN_PASS_DECL_MERGEASSUMINGOPSPASS
#endif // GEN_PASS_DECL_MERGEASSUMINGOPSPASS
#ifdef GEN_PASS_DEF_MERGEASSUMINGOPSPASS
namespace impl {

template <typename DerivedT>
class MergeAssumingOpsPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = MergeAssumingOpsPassBase;

  MergeAssumingOpsPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  MergeAssumingOpsPassBase(const MergeAssumingOpsPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  MergeAssumingOpsPassBase& operator=(const MergeAssumingOpsPassBase &) = delete;
  MergeAssumingOpsPassBase(MergeAssumingOpsPassBase &&) = delete;
  MergeAssumingOpsPassBase& operator=(MergeAssumingOpsPassBase &&) = delete;
  ~MergeAssumingOpsPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("mhlo-merge-assuming-ops");
  }
  ::llvm::StringRef getArgument() const override { return "mhlo-merge-assuming-ops"; }

  ::llvm::StringRef getDescription() const override { return "Prepare moving dynamic broadcasts up over element-wise operations and broadcast the operands rather than the result. This will eventually allow for larger fusions."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("MergeAssumingOpsPass");
  }
  ::llvm::StringRef getName() const override { return "MergeAssumingOpsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(MergeAssumingOpsPassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_MERGEASSUMINGOPSPASS
#endif // GEN_PASS_DEF_MERGEASSUMINGOPSPASS

//===----------------------------------------------------------------------===//
// PrepareForExportPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_PREPAREFOREXPORTPASS
std::unique_ptr<::mlir::Pass> createPrepareForExportPass();
#undef GEN_PASS_DECL_PREPAREFOREXPORTPASS
#endif // GEN_PASS_DECL_PREPAREFOREXPORTPASS
#ifdef GEN_PASS_DEF_PREPAREFOREXPORTPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createPrepareForExportPass();
} // namespace impl
namespace impl {

template <typename DerivedT>
class PrepareForExportPassBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = PrepareForExportPassBase;

  PrepareForExportPassBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  PrepareForExportPassBase(const PrepareForExportPassBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}
  PrepareForExportPassBase& operator=(const PrepareForExportPassBase &) = delete;
  PrepareForExportPassBase(PrepareForExportPassBase &&) = delete;
  PrepareForExportPassBase& operator=(PrepareForExportPassBase &&) = delete;
  ~PrepareForExportPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("xla-prepare-for-export");
  }
  ::llvm::StringRef getArgument() const override { return "xla-prepare-for-export"; }

  ::llvm::StringRef getDescription() const override { return "Prepare for XLA export"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("PrepareForExportPass");
  }
  ::llvm::StringRef getName() const override { return "PrepareForExportPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(PrepareForExportPassBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createPrepareForExportPass() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createPrepareForExportPass() {
  return impl::createPrepareForExportPass();
}
#undef GEN_PASS_DEF_PREPAREFOREXPORTPASS
#endif // GEN_PASS_DEF_PREPAREFOREXPORTPASS

//===----------------------------------------------------------------------===//
// ShapeLegalizeToHloPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_SHAPELEGALIZETOHLOPASS
struct ShapeLegalizeToHloPassOptions {
  bool legalize_constraints_ = false;
};
#undef GEN_PASS_DECL_SHAPELEGALIZETOHLOPASS
#endif // GEN_PASS_DECL_SHAPELEGALIZETOHLOPASS
#ifdef GEN_PASS_DEF_SHAPELEGALIZETOHLOPASS
namespace impl {

template <typename DerivedT>
class ShapeLegalizeToHloPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = ShapeLegalizeToHloPassBase;

  ShapeLegalizeToHloPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  ShapeLegalizeToHloPassBase(const ShapeLegalizeToHloPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  ShapeLegalizeToHloPassBase& operator=(const ShapeLegalizeToHloPassBase &) = delete;
  ShapeLegalizeToHloPassBase(ShapeLegalizeToHloPassBase &&) = delete;
  ShapeLegalizeToHloPassBase& operator=(ShapeLegalizeToHloPassBase &&) = delete;
  ~ShapeLegalizeToHloPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("shape-legalize-to-hlo");
  }
  ::llvm::StringRef getArgument() const override { return "shape-legalize-to-hlo"; }

  ::llvm::StringRef getDescription() const override { return "Legalize shape-related ops to HLO."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ShapeLegalizeToHloPass");
  }
  ::llvm::StringRef getName() const override { return "ShapeLegalizeToHloPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mhlo::MhloDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ShapeLegalizeToHloPassBase<DerivedT>)

  ShapeLegalizeToHloPassBase(ShapeLegalizeToHloPassOptions options) : ShapeLegalizeToHloPassBase() {
    legalize_constraints_ = std::move(options.legalize_constraints_);
  }
protected:
  ::mlir::Pass::Option<bool> legalize_constraints_{*this, "legalize-constraints", ::llvm::cl::desc("Whether to legalize Cstr Ops to shape_assertion custom_call"), ::llvm::cl::init(false)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_SHAPELEGALIZETOHLOPASS
#endif // GEN_PASS_DEF_SHAPELEGALIZETOHLOPASS

//===----------------------------------------------------------------------===//
// ShapeSimplification
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_SHAPESIMPLIFICATION
#undef GEN_PASS_DECL_SHAPESIMPLIFICATION
#endif // GEN_PASS_DECL_SHAPESIMPLIFICATION
#ifdef GEN_PASS_DEF_SHAPESIMPLIFICATION
namespace impl {

template <typename DerivedT>
class ShapeSimplificationBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = ShapeSimplificationBase;

  ShapeSimplificationBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  ShapeSimplificationBase(const ShapeSimplificationBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}
  ShapeSimplificationBase& operator=(const ShapeSimplificationBase &) = delete;
  ShapeSimplificationBase(ShapeSimplificationBase &&) = delete;
  ShapeSimplificationBase& operator=(ShapeSimplificationBase &&) = delete;
  ~ShapeSimplificationBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("shape-simplification");
  }
  ::llvm::StringRef getArgument() const override { return "shape-simplification"; }

  ::llvm::StringRef getDescription() const override { return "Simplify shape ops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ShapeSimplification");
  }
  ::llvm::StringRef getName() const override { return "ShapeSimplification"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ShapeSimplificationBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_SHAPESIMPLIFICATION
#endif // GEN_PASS_DEF_SHAPESIMPLIFICATION

//===----------------------------------------------------------------------===//
// SinkConstantsToControlFlowPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_SINKCONSTANTSTOCONTROLFLOWPASS
#undef GEN_PASS_DECL_SINKCONSTANTSTOCONTROLFLOWPASS
#endif // GEN_PASS_DECL_SINKCONSTANTSTOCONTROLFLOWPASS
#ifdef GEN_PASS_DEF_SINKCONSTANTSTOCONTROLFLOWPASS
namespace impl {

template <typename DerivedT>
class SinkConstantsToControlFlowPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = SinkConstantsToControlFlowPassBase;

  SinkConstantsToControlFlowPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  SinkConstantsToControlFlowPassBase(const SinkConstantsToControlFlowPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  SinkConstantsToControlFlowPassBase& operator=(const SinkConstantsToControlFlowPassBase &) = delete;
  SinkConstantsToControlFlowPassBase(SinkConstantsToControlFlowPassBase &&) = delete;
  SinkConstantsToControlFlowPassBase& operator=(SinkConstantsToControlFlowPassBase &&) = delete;
  ~SinkConstantsToControlFlowPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("mhlo-sink-constants-to-control-flow");
  }
  ::llvm::StringRef getArgument() const override { return "mhlo-sink-constants-to-control-flow"; }

  ::llvm::StringRef getDescription() const override { return "Sink constants implicitly captured in control flow regions. This is necessary to export to XLA."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SinkConstantsToControlFlowPass");
  }
  ::llvm::StringRef getName() const override { return "SinkConstantsToControlFlowPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SinkConstantsToControlFlowPassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_SINKCONSTANTSTOCONTROLFLOWPASS
#endif // GEN_PASS_DEF_SINKCONSTANTSTOCONTROLFLOWPASS

//===----------------------------------------------------------------------===//
// StablehloLegalizeToHloPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_STABLEHLOLEGALIZETOHLOPASS
#undef GEN_PASS_DECL_STABLEHLOLEGALIZETOHLOPASS
#endif // GEN_PASS_DECL_STABLEHLOLEGALIZETOHLOPASS
#ifdef GEN_PASS_DEF_STABLEHLOLEGALIZETOHLOPASS
namespace impl {

template <typename DerivedT>
class StablehloLegalizeToHloPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = StablehloLegalizeToHloPassBase;

  StablehloLegalizeToHloPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  StablehloLegalizeToHloPassBase(const StablehloLegalizeToHloPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  StablehloLegalizeToHloPassBase& operator=(const StablehloLegalizeToHloPassBase &) = delete;
  StablehloLegalizeToHloPassBase(StablehloLegalizeToHloPassBase &&) = delete;
  StablehloLegalizeToHloPassBase& operator=(StablehloLegalizeToHloPassBase &&) = delete;
  ~StablehloLegalizeToHloPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("stablehlo-legalize-to-hlo");
  }
  ::llvm::StringRef getArgument() const override { return "stablehlo-legalize-to-hlo"; }

  ::llvm::StringRef getDescription() const override { return "Legalize StableHLO to HLO."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("StablehloLegalizeToHloPass");
  }
  ::llvm::StringRef getName() const override { return "StablehloLegalizeToHloPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mhlo::MhloDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(StablehloLegalizeToHloPassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_STABLEHLOLEGALIZETOHLOPASS
#endif // GEN_PASS_DEF_STABLEHLOLEGALIZETOHLOPASS

//===----------------------------------------------------------------------===//
// SymbolicShapeOptimization
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_SYMBOLICSHAPEOPTIMIZATION
#undef GEN_PASS_DECL_SYMBOLICSHAPEOPTIMIZATION
#endif // GEN_PASS_DECL_SYMBOLICSHAPEOPTIMIZATION
#ifdef GEN_PASS_DEF_SYMBOLICSHAPEOPTIMIZATION
namespace impl {

template <typename DerivedT>
class SymbolicShapeOptimizationBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = SymbolicShapeOptimizationBase;

  SymbolicShapeOptimizationBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  SymbolicShapeOptimizationBase(const SymbolicShapeOptimizationBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  SymbolicShapeOptimizationBase& operator=(const SymbolicShapeOptimizationBase &) = delete;
  SymbolicShapeOptimizationBase(SymbolicShapeOptimizationBase &&) = delete;
  SymbolicShapeOptimizationBase& operator=(SymbolicShapeOptimizationBase &&) = delete;
  ~SymbolicShapeOptimizationBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("symbolic-shape-optimization");
  }
  ::llvm::StringRef getArgument() const override { return "symbolic-shape-optimization"; }

  ::llvm::StringRef getDescription() const override { return "Analyzes shapes and performs shape-related optimizations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SymbolicShapeOptimization");
  }
  ::llvm::StringRef getName() const override { return "SymbolicShapeOptimization"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SymbolicShapeOptimizationBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_SYMBOLICSHAPEOPTIMIZATION
#endif // GEN_PASS_DEF_SYMBOLICSHAPEOPTIMIZATION

//===----------------------------------------------------------------------===//
// TestInferShapedTypeMethodsPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_TESTINFERSHAPEDTYPEMETHODSPASS
#undef GEN_PASS_DECL_TESTINFERSHAPEDTYPEMETHODSPASS
#endif // GEN_PASS_DECL_TESTINFERSHAPEDTYPEMETHODSPASS
#ifdef GEN_PASS_DEF_TESTINFERSHAPEDTYPEMETHODSPASS
namespace impl {

template <typename DerivedT>
class TestInferShapedTypeMethodsPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = TestInferShapedTypeMethodsPassBase;

  TestInferShapedTypeMethodsPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  TestInferShapedTypeMethodsPassBase(const TestInferShapedTypeMethodsPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  TestInferShapedTypeMethodsPassBase& operator=(const TestInferShapedTypeMethodsPassBase &) = delete;
  TestInferShapedTypeMethodsPassBase(TestInferShapedTypeMethodsPassBase &&) = delete;
  TestInferShapedTypeMethodsPassBase& operator=(TestInferShapedTypeMethodsPassBase &&) = delete;
  ~TestInferShapedTypeMethodsPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("mhlo-test-infer-shaped-type-methods");
  }
  ::llvm::StringRef getArgument() const override { return "mhlo-test-infer-shaped-type-methods"; }

  ::llvm::StringRef getDescription() const override { return "Uses test ops to invoke InferShapedTypeOpInterface methods."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TestInferShapedTypeMethodsPass");
  }
  ::llvm::StringRef getName() const override { return "TestInferShapedTypeMethodsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TestInferShapedTypeMethodsPassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_TESTINFERSHAPEDTYPEMETHODSPASS
#endif // GEN_PASS_DEF_TESTINFERSHAPEDTYPEMETHODSPASS

//===----------------------------------------------------------------------===//
// TestMaterializeBroadcastsPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_TESTMATERIALIZEBROADCASTSPASS
#undef GEN_PASS_DECL_TESTMATERIALIZEBROADCASTSPASS
#endif // GEN_PASS_DECL_TESTMATERIALIZEBROADCASTSPASS
#ifdef GEN_PASS_DEF_TESTMATERIALIZEBROADCASTSPASS
namespace impl {

template <typename DerivedT>
class TestMaterializeBroadcastsPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = TestMaterializeBroadcastsPassBase;

  TestMaterializeBroadcastsPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  TestMaterializeBroadcastsPassBase(const TestMaterializeBroadcastsPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  TestMaterializeBroadcastsPassBase& operator=(const TestMaterializeBroadcastsPassBase &) = delete;
  TestMaterializeBroadcastsPassBase(TestMaterializeBroadcastsPassBase &&) = delete;
  TestMaterializeBroadcastsPassBase& operator=(TestMaterializeBroadcastsPassBase &&) = delete;
  ~TestMaterializeBroadcastsPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("mhlo-test-materialize-broadcasts");
  }
  ::llvm::StringRef getArgument() const override { return "mhlo-test-materialize-broadcasts"; }

  ::llvm::StringRef getDescription() const override { return "Test pass for materializing 'broadcast_dimensions' attributes."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TestMaterializeBroadcastsPass");
  }
  ::llvm::StringRef getName() const override { return "TestMaterializeBroadcastsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TestMaterializeBroadcastsPassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_TESTMATERIALIZEBROADCASTSPASS
#endif // GEN_PASS_DEF_TESTMATERIALIZEBROADCASTSPASS

//===----------------------------------------------------------------------===//
// TestUnfuseBatchNormPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_TESTUNFUSEBATCHNORMPASS
#undef GEN_PASS_DECL_TESTUNFUSEBATCHNORMPASS
#endif // GEN_PASS_DECL_TESTUNFUSEBATCHNORMPASS
#ifdef GEN_PASS_DEF_TESTUNFUSEBATCHNORMPASS
namespace impl {

template <typename DerivedT>
class TestUnfuseBatchNormPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = TestUnfuseBatchNormPassBase;

  TestUnfuseBatchNormPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  TestUnfuseBatchNormPassBase(const TestUnfuseBatchNormPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  TestUnfuseBatchNormPassBase& operator=(const TestUnfuseBatchNormPassBase &) = delete;
  TestUnfuseBatchNormPassBase(TestUnfuseBatchNormPassBase &&) = delete;
  TestUnfuseBatchNormPassBase& operator=(TestUnfuseBatchNormPassBase &&) = delete;
  ~TestUnfuseBatchNormPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("mhlo-test-unfuse-batch-norm");
  }
  ::llvm::StringRef getArgument() const override { return "mhlo-test-unfuse-batch-norm"; }

  ::llvm::StringRef getDescription() const override { return "Test pass for materializing 'broadcast_dimensions' attributes."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TestUnfuseBatchNormPass");
  }
  ::llvm::StringRef getName() const override { return "TestUnfuseBatchNormPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<arith::ArithDialect>();
    registry.insert<shape::ShapeDialect>();
    registry.insert<tensor::TensorDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TestUnfuseBatchNormPassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_TESTUNFUSEBATCHNORMPASS
#endif // GEN_PASS_DEF_TESTUNFUSEBATCHNORMPASS
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// BroadcastPropagationPass Registration
//===----------------------------------------------------------------------===//

inline void registerBroadcastPropagationPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createBroadcastPropagationPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerBroadcastPropagationPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createBroadcastPropagationPass();
  });
}

//===----------------------------------------------------------------------===//
// ChloLegalizeToHighLevelMhloPass Registration
//===----------------------------------------------------------------------===//

inline void registerChloLegalizeToHighLevelMhloPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createChloLegalizeToHighLevelMhloPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerChloLegalizeToHighLevelMhloPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createChloLegalizeToHighLevelMhloPass();
  });
}

//===----------------------------------------------------------------------===//
// ChloLegalizeToHloPass Registration
//===----------------------------------------------------------------------===//

inline void registerChloLegalizeToHloPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createChloLegalizeToHloPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerChloLegalizeToHloPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createChloLegalizeToHloPass();
  });
}

//===----------------------------------------------------------------------===//
// CollapseElementwiseMapPass Registration
//===----------------------------------------------------------------------===//

inline void registerCollapseElementwiseMapPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createCollapseElementwiseMapPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerCollapseElementwiseMapPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createCollapseElementwiseMapPass();
  });
}

//===----------------------------------------------------------------------===//
// ConvertToSignlessPass Registration
//===----------------------------------------------------------------------===//

inline void registerConvertToSignlessPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertToSignlessPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConvertToSignlessPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConvertToSignlessPass();
  });
}

//===----------------------------------------------------------------------===//
// ExpandHloTuplesPass Registration
//===----------------------------------------------------------------------===//

inline void registerExpandHloTuplesPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createExpandHloTuplesPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerExpandHloTuplesPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createExpandHloTuplesPass();
  });
}

//===----------------------------------------------------------------------===//
// FlattenTuplePass Registration
//===----------------------------------------------------------------------===//

inline void registerFlattenTuplePass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createFlattenTuplePass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerFlattenTuplePassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createFlattenTuplePass();
  });
}

//===----------------------------------------------------------------------===//
// HloLegalizeToArithmeticPass Registration
//===----------------------------------------------------------------------===//

inline void registerHloLegalizeToArithmeticPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createLegalizeToArithmeticPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerHloLegalizeToArithmeticPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createLegalizeToArithmeticPass();
  });
}

//===----------------------------------------------------------------------===//
// HloLegalizeToLinalgPass Registration
//===----------------------------------------------------------------------===//

inline void registerHloLegalizeToLinalgPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createLegalizeHloToLinalgPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerHloLegalizeToLinalgPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createLegalizeHloToLinalgPass();
  });
}

//===----------------------------------------------------------------------===//
// HloLegalizeToStablehloPass Registration
//===----------------------------------------------------------------------===//

inline void registerHloLegalizeToStablehloPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createHloLegalizeToStablehloPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerHloLegalizeToStablehloPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createHloLegalizeToStablehloPass();
  });
}

//===----------------------------------------------------------------------===//
// LegalizeDotToDotGeneralPass Registration
//===----------------------------------------------------------------------===//

inline void registerLegalizeDotToDotGeneralPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createLegalizeDotToDotGeneralPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerLegalizeDotToDotGeneralPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createLegalizeDotToDotGeneralPass();
  });
}

//===----------------------------------------------------------------------===//
// LegalizeEinsumToDotGeneralPass Registration
//===----------------------------------------------------------------------===//

inline void registerLegalizeEinsumToDotGeneralPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createLegalizeEinsumToDotGeneralPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerLegalizeEinsumToDotGeneralPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createLegalizeEinsumToDotGeneralPass();
  });
}

//===----------------------------------------------------------------------===//
// LegalizeTanhToApproximationPass Registration
//===----------------------------------------------------------------------===//

inline void registerLegalizeTanhToApproximationPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createLegalizeTrigonometricToApproximationPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerLegalizeTanhToApproximationPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createLegalizeTrigonometricToApproximationPass();
  });
}

//===----------------------------------------------------------------------===//
// LegalizeTorchIndexSelectToGatherPass Registration
//===----------------------------------------------------------------------===//

inline void registerLegalizeTorchIndexSelectToGatherPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createLegalizeTorchIndexSelectToGatherPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerLegalizeTorchIndexSelectToGatherPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createLegalizeTorchIndexSelectToGatherPass();
  });
}

//===----------------------------------------------------------------------===//
// MergeAssumingOpsPass Registration
//===----------------------------------------------------------------------===//

inline void registerMergeAssumingOpsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createMergeAssumingOpsPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerMergeAssumingOpsPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createMergeAssumingOpsPass();
  });
}

//===----------------------------------------------------------------------===//
// PrepareForExportPass Registration
//===----------------------------------------------------------------------===//

inline void registerPrepareForExportPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createPrepareForExportPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerPrepareForExportPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createPrepareForExportPass();
  });
}

//===----------------------------------------------------------------------===//
// ShapeLegalizeToHloPass Registration
//===----------------------------------------------------------------------===//

inline void registerShapeLegalizeToHloPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createShapeLegalizeToHloPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerShapeLegalizeToHloPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createShapeLegalizeToHloPass();
  });
}

//===----------------------------------------------------------------------===//
// ShapeSimplification Registration
//===----------------------------------------------------------------------===//

inline void registerShapeSimplification() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createShapeSimplification();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerShapeSimplificationPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createShapeSimplification();
  });
}

//===----------------------------------------------------------------------===//
// SinkConstantsToControlFlowPass Registration
//===----------------------------------------------------------------------===//

inline void registerSinkConstantsToControlFlowPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createSinkConstantsToControlFlowPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerSinkConstantsToControlFlowPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createSinkConstantsToControlFlowPass();
  });
}

//===----------------------------------------------------------------------===//
// StablehloLegalizeToHloPass Registration
//===----------------------------------------------------------------------===//

inline void registerStablehloLegalizeToHloPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createStablehloLegalizeToHloPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerStablehloLegalizeToHloPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createStablehloLegalizeToHloPass();
  });
}

//===----------------------------------------------------------------------===//
// SymbolicShapeOptimization Registration
//===----------------------------------------------------------------------===//

inline void registerSymbolicShapeOptimization() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createSymbolicShapeOptimizationPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerSymbolicShapeOptimizationPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createSymbolicShapeOptimizationPass();
  });
}

//===----------------------------------------------------------------------===//
// TestInferShapedTypeMethodsPass Registration
//===----------------------------------------------------------------------===//

inline void registerTestInferShapedTypeMethodsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createTestInferShapedTypeMethodsPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerTestInferShapedTypeMethodsPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createTestInferShapedTypeMethodsPass();
  });
}

//===----------------------------------------------------------------------===//
// TestMaterializeBroadcastsPass Registration
//===----------------------------------------------------------------------===//

inline void registerTestMaterializeBroadcastsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createTestMaterializeBroadcastsPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerTestMaterializeBroadcastsPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createTestMaterializeBroadcastsPass();
  });
}

//===----------------------------------------------------------------------===//
// TestUnfuseBatchNormPass Registration
//===----------------------------------------------------------------------===//

inline void registerTestUnfuseBatchNormPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createTestUnfuseBatchNormPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerTestUnfuseBatchNormPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createTestUnfuseBatchNormPass();
  });
}

//===----------------------------------------------------------------------===//
// AllMhlo Registration
//===----------------------------------------------------------------------===//

inline void registerAllMhloPasses() {
  registerBroadcastPropagationPass();
  registerChloLegalizeToHighLevelMhloPass();
  registerChloLegalizeToHloPass();
  registerCollapseElementwiseMapPass();
  registerConvertToSignlessPass();
  registerExpandHloTuplesPass();
  registerFlattenTuplePass();
  registerHloLegalizeToArithmeticPass();
  registerHloLegalizeToLinalgPass();
  registerHloLegalizeToStablehloPass();
  registerLegalizeDotToDotGeneralPass();
  registerLegalizeEinsumToDotGeneralPass();
  registerLegalizeTanhToApproximationPass();
  registerLegalizeTorchIndexSelectToGatherPass();
  registerMergeAssumingOpsPass();
  registerPrepareForExportPass();
  registerShapeLegalizeToHloPass();
  registerShapeSimplification();
  registerSinkConstantsToControlFlowPass();
  registerStablehloLegalizeToHloPass();
  registerSymbolicShapeOptimization();
  registerTestInferShapedTypeMethodsPass();
  registerTestMaterializeBroadcastsPass();
  registerTestUnfuseBatchNormPass();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
// Deprecated. Please use the new per-pass macros.
#ifdef GEN_PASS_CLASSES

template <typename DerivedT>
class BroadcastPropagationPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = BroadcastPropagationPassBase;

  BroadcastPropagationPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  BroadcastPropagationPassBase(const BroadcastPropagationPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  BroadcastPropagationPassBase& operator=(const BroadcastPropagationPassBase &) = delete;
  BroadcastPropagationPassBase(BroadcastPropagationPassBase &&) = delete;
  BroadcastPropagationPassBase& operator=(BroadcastPropagationPassBase &&) = delete;
  ~BroadcastPropagationPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("mhlo-broadcast-propagation");
  }
  ::llvm::StringRef getArgument() const override { return "mhlo-broadcast-propagation"; }

  ::llvm::StringRef getDescription() const override { return "Move dynamic broadcasts up over element-wise operations and broadcast the operands rather than the result. This will eventually allow for larger fusions."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("BroadcastPropagationPass");
  }
  ::llvm::StringRef getName() const override { return "BroadcastPropagationPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(BroadcastPropagationPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ChloLegalizeToHighLevelMhloPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = ChloLegalizeToHighLevelMhloPassBase;

  ChloLegalizeToHighLevelMhloPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  ChloLegalizeToHighLevelMhloPassBase(const ChloLegalizeToHighLevelMhloPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  ChloLegalizeToHighLevelMhloPassBase& operator=(const ChloLegalizeToHighLevelMhloPassBase &) = delete;
  ChloLegalizeToHighLevelMhloPassBase(ChloLegalizeToHighLevelMhloPassBase &&) = delete;
  ChloLegalizeToHighLevelMhloPassBase& operator=(ChloLegalizeToHighLevelMhloPassBase &&) = delete;
  ~ChloLegalizeToHighLevelMhloPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("chlo-legalize-to-high-level-mhlo");
  }
  ::llvm::StringRef getArgument() const override { return "chlo-legalize-to-high-level-mhlo"; }

  ::llvm::StringRef getDescription() const override { return "Legalize CHLO's with XLA counterparts, like TopK and Erf."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ChloLegalizeToHighLevelMhloPass");
  }
  ::llvm::StringRef getName() const override { return "ChloLegalizeToHighLevelMhloPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mhlo::MhloDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ChloLegalizeToHighLevelMhloPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ChloLegalizeToHloPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = ChloLegalizeToHloPassBase;

  ChloLegalizeToHloPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  ChloLegalizeToHloPassBase(const ChloLegalizeToHloPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  ChloLegalizeToHloPassBase& operator=(const ChloLegalizeToHloPassBase &) = delete;
  ChloLegalizeToHloPassBase(ChloLegalizeToHloPassBase &&) = delete;
  ChloLegalizeToHloPassBase& operator=(ChloLegalizeToHloPassBase &&) = delete;
  ~ChloLegalizeToHloPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("chlo-legalize-to-hlo");
  }
  ::llvm::StringRef getArgument() const override { return "chlo-legalize-to-hlo"; }

  ::llvm::StringRef getDescription() const override { return "Legalize CHLO to MHLO with XLA-supported ops."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ChloLegalizeToHloPass");
  }
  ::llvm::StringRef getName() const override { return "ChloLegalizeToHloPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mhlo::MhloDialect>();
    registry.insert<mlir::shape::ShapeDialect>();
    registry.insert<mlir::stablehlo::StablehloDialect>();
    registry.insert<mlir::tensor::TensorDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ChloLegalizeToHloPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class CollapseElementwiseMapPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = CollapseElementwiseMapPassBase;

  CollapseElementwiseMapPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  CollapseElementwiseMapPassBase(const CollapseElementwiseMapPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  CollapseElementwiseMapPassBase& operator=(const CollapseElementwiseMapPassBase &) = delete;
  CollapseElementwiseMapPassBase(CollapseElementwiseMapPassBase &&) = delete;
  CollapseElementwiseMapPassBase& operator=(CollapseElementwiseMapPassBase &&) = delete;
  ~CollapseElementwiseMapPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("mhlo-collapse-elementwise-map");
  }
  ::llvm::StringRef getArgument() const override { return "mhlo-collapse-elementwise-map"; }

  ::llvm::StringRef getDescription() const override { return "Collapse the mhlo.map if the map only has elementwise ops."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("CollapseElementwiseMapPass");
  }
  ::llvm::StringRef getName() const override { return "CollapseElementwiseMapPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(CollapseElementwiseMapPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ConvertToSignlessPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ConvertToSignlessPassBase;

  ConvertToSignlessPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConvertToSignlessPassBase(const ConvertToSignlessPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  ConvertToSignlessPassBase& operator=(const ConvertToSignlessPassBase &) = delete;
  ConvertToSignlessPassBase(ConvertToSignlessPassBase &&) = delete;
  ConvertToSignlessPassBase& operator=(ConvertToSignlessPassBase &&) = delete;
  ~ConvertToSignlessPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("convert-to-signless");
  }
  ::llvm::StringRef getArgument() const override { return "convert-to-signless"; }

  ::llvm::StringRef getDescription() const override { return "Pass to transform the IR to be on signless integers."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConvertToSignlessPass");
  }
  ::llvm::StringRef getName() const override { return "ConvertToSignlessPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConvertToSignlessPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ExpandHloTuplesPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ExpandHloTuplesPassBase;

  ExpandHloTuplesPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ExpandHloTuplesPassBase(const ExpandHloTuplesPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  ExpandHloTuplesPassBase& operator=(const ExpandHloTuplesPassBase &) = delete;
  ExpandHloTuplesPassBase(ExpandHloTuplesPassBase &&) = delete;
  ExpandHloTuplesPassBase& operator=(ExpandHloTuplesPassBase &&) = delete;
  ~ExpandHloTuplesPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("expand-hlo-tuples");
  }
  ::llvm::StringRef getArgument() const override { return "expand-hlo-tuples"; }

  ::llvm::StringRef getDescription() const override { return "Expand HLO tuple for the entry function of the module."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ExpandHloTuplesPass");
  }
  ::llvm::StringRef getName() const override { return "ExpandHloTuplesPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mhlo::MhloDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ExpandHloTuplesPassBase<DerivedT>)

protected:
  ::mlir::Pass::Option<std::string> entry_function_name_{*this, "entry-function", ::llvm::cl::desc("the name of entry function of the module")};
};

template <typename DerivedT>
class FlattenTuplePassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = FlattenTuplePassBase;

  FlattenTuplePassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  FlattenTuplePassBase(const FlattenTuplePassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  FlattenTuplePassBase& operator=(const FlattenTuplePassBase &) = delete;
  FlattenTuplePassBase(FlattenTuplePassBase &&) = delete;
  FlattenTuplePassBase& operator=(FlattenTuplePassBase &&) = delete;
  ~FlattenTuplePassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("mhlo-flatten-tuple");
  }
  ::llvm::StringRef getArgument() const override { return "mhlo-flatten-tuple"; }

  ::llvm::StringRef getDescription() const override { return "Flatten tuples in operands and results of operators that support both tuple and variadic type."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("FlattenTuplePass");
  }
  ::llvm::StringRef getName() const override { return "FlattenTuplePass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(FlattenTuplePassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class HloLegalizeToArithmeticPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = HloLegalizeToArithmeticPassBase;

  HloLegalizeToArithmeticPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  HloLegalizeToArithmeticPassBase(const HloLegalizeToArithmeticPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  HloLegalizeToArithmeticPassBase& operator=(const HloLegalizeToArithmeticPassBase &) = delete;
  HloLegalizeToArithmeticPassBase(HloLegalizeToArithmeticPassBase &&) = delete;
  HloLegalizeToArithmeticPassBase& operator=(HloLegalizeToArithmeticPassBase &&) = delete;
  ~HloLegalizeToArithmeticPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("hlo-legalize-to-arithmetic");
  }
  ::llvm::StringRef getArgument() const override { return "hlo-legalize-to-arithmetic"; }

  ::llvm::StringRef getDescription() const override { return "Legalize from HLO dialect to arithmetic dialect."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("HloLegalizeToArithmeticPass");
  }
  ::llvm::StringRef getName() const override { return "HloLegalizeToArithmeticPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(HloLegalizeToArithmeticPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class HloLegalizeToLinalgPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = HloLegalizeToLinalgPassBase;

  HloLegalizeToLinalgPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  HloLegalizeToLinalgPassBase(const HloLegalizeToLinalgPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  HloLegalizeToLinalgPassBase& operator=(const HloLegalizeToLinalgPassBase &) = delete;
  HloLegalizeToLinalgPassBase(HloLegalizeToLinalgPassBase &&) = delete;
  HloLegalizeToLinalgPassBase& operator=(HloLegalizeToLinalgPassBase &&) = delete;
  ~HloLegalizeToLinalgPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("hlo-legalize-to-linalg");
  }
  ::llvm::StringRef getArgument() const override { return "hlo-legalize-to-linalg"; }

  ::llvm::StringRef getDescription() const override { return "Legalize from HLO dialect to Linalg dialect."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("HloLegalizeToLinalgPass");
  }
  ::llvm::StringRef getName() const override { return "HloLegalizeToLinalgPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(HloLegalizeToLinalgPassBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> enablePrimitiveOps{*this, "enable-primitive-ops", ::llvm::cl::desc("Lower to primitive Linalg ops (map, reduce and transpose) when possible, instead of linalg.generic"), ::llvm::cl::init(false)};
};

template <typename DerivedT>
class HloLegalizeToStablehloPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = HloLegalizeToStablehloPassBase;

  HloLegalizeToStablehloPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  HloLegalizeToStablehloPassBase(const HloLegalizeToStablehloPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  HloLegalizeToStablehloPassBase& operator=(const HloLegalizeToStablehloPassBase &) = delete;
  HloLegalizeToStablehloPassBase(HloLegalizeToStablehloPassBase &&) = delete;
  HloLegalizeToStablehloPassBase& operator=(HloLegalizeToStablehloPassBase &&) = delete;
  ~HloLegalizeToStablehloPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("hlo-legalize-to-stablehlo");
  }
  ::llvm::StringRef getArgument() const override { return "hlo-legalize-to-stablehlo"; }

  ::llvm::StringRef getDescription() const override { return "Legalize HLO to StableHLO."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("HloLegalizeToStablehloPass");
  }
  ::llvm::StringRef getName() const override { return "HloLegalizeToStablehloPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<stablehlo::StablehloDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(HloLegalizeToStablehloPassBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> allow_experimental_features_{*this, "allow-experimental-features", ::llvm::cl::desc("Allow legalization of experimental MHLO features via StableHLO custom_call"), ::llvm::cl::init(false)};
};

template <typename DerivedT>
class LegalizeDotToDotGeneralPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = LegalizeDotToDotGeneralPassBase;

  LegalizeDotToDotGeneralPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  LegalizeDotToDotGeneralPassBase(const LegalizeDotToDotGeneralPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  LegalizeDotToDotGeneralPassBase& operator=(const LegalizeDotToDotGeneralPassBase &) = delete;
  LegalizeDotToDotGeneralPassBase(LegalizeDotToDotGeneralPassBase &&) = delete;
  LegalizeDotToDotGeneralPassBase& operator=(LegalizeDotToDotGeneralPassBase &&) = delete;
  ~LegalizeDotToDotGeneralPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("mhlo-legalize-dot-to-dot-general");
  }
  ::llvm::StringRef getArgument() const override { return "mhlo-legalize-dot-to-dot-general"; }

  ::llvm::StringRef getDescription() const override { return "Legalizes dot ops to dot_general ops."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LegalizeDotToDotGeneralPass");
  }
  ::llvm::StringRef getName() const override { return "LegalizeDotToDotGeneralPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LegalizeDotToDotGeneralPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class LegalizeEinsumToDotGeneralPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = LegalizeEinsumToDotGeneralPassBase;

  LegalizeEinsumToDotGeneralPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  LegalizeEinsumToDotGeneralPassBase(const LegalizeEinsumToDotGeneralPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  LegalizeEinsumToDotGeneralPassBase& operator=(const LegalizeEinsumToDotGeneralPassBase &) = delete;
  LegalizeEinsumToDotGeneralPassBase(LegalizeEinsumToDotGeneralPassBase &&) = delete;
  LegalizeEinsumToDotGeneralPassBase& operator=(LegalizeEinsumToDotGeneralPassBase &&) = delete;
  ~LegalizeEinsumToDotGeneralPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("mhlo-legalize-einsum-to-dot-general");
  }
  ::llvm::StringRef getArgument() const override { return "mhlo-legalize-einsum-to-dot-general"; }

  ::llvm::StringRef getDescription() const override { return "Legalizes einsum ops to dot_general ops."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LegalizeEinsumToDotGeneralPass");
  }
  ::llvm::StringRef getName() const override { return "LegalizeEinsumToDotGeneralPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LegalizeEinsumToDotGeneralPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class LegalizeTanhToApproximationPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = LegalizeTanhToApproximationPassBase;

  LegalizeTanhToApproximationPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  LegalizeTanhToApproximationPassBase(const LegalizeTanhToApproximationPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  LegalizeTanhToApproximationPassBase& operator=(const LegalizeTanhToApproximationPassBase &) = delete;
  LegalizeTanhToApproximationPassBase(LegalizeTanhToApproximationPassBase &&) = delete;
  LegalizeTanhToApproximationPassBase& operator=(LegalizeTanhToApproximationPassBase &&) = delete;
  ~LegalizeTanhToApproximationPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("mhlo-legalize-trigonometric-to-approximation");
  }
  ::llvm::StringRef getArgument() const override { return "mhlo-legalize-trigonometric-to-approximation"; }

  ::llvm::StringRef getDescription() const override { return "Legalize trigonometric operations from standard dialect to an approximation."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LegalizeTanhToApproximationPass");
  }
  ::llvm::StringRef getName() const override { return "LegalizeTanhToApproximationPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LegalizeTanhToApproximationPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class LegalizeTorchIndexSelectToGatherPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = LegalizeTorchIndexSelectToGatherPassBase;

  LegalizeTorchIndexSelectToGatherPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  LegalizeTorchIndexSelectToGatherPassBase(const LegalizeTorchIndexSelectToGatherPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  LegalizeTorchIndexSelectToGatherPassBase& operator=(const LegalizeTorchIndexSelectToGatherPassBase &) = delete;
  LegalizeTorchIndexSelectToGatherPassBase(LegalizeTorchIndexSelectToGatherPassBase &&) = delete;
  LegalizeTorchIndexSelectToGatherPassBase& operator=(LegalizeTorchIndexSelectToGatherPassBase &&) = delete;
  ~LegalizeTorchIndexSelectToGatherPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("mhlo-legalize-torch-index-select-to-gather");
  }
  ::llvm::StringRef getArgument() const override { return "mhlo-legalize-torch-index-select-to-gather"; }

  ::llvm::StringRef getDescription() const override { return "Legalizes torch index select to a gather."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LegalizeTorchIndexSelectToGatherPass");
  }
  ::llvm::StringRef getName() const override { return "LegalizeTorchIndexSelectToGatherPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LegalizeTorchIndexSelectToGatherPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class MergeAssumingOpsPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = MergeAssumingOpsPassBase;

  MergeAssumingOpsPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  MergeAssumingOpsPassBase(const MergeAssumingOpsPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  MergeAssumingOpsPassBase& operator=(const MergeAssumingOpsPassBase &) = delete;
  MergeAssumingOpsPassBase(MergeAssumingOpsPassBase &&) = delete;
  MergeAssumingOpsPassBase& operator=(MergeAssumingOpsPassBase &&) = delete;
  ~MergeAssumingOpsPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("mhlo-merge-assuming-ops");
  }
  ::llvm::StringRef getArgument() const override { return "mhlo-merge-assuming-ops"; }

  ::llvm::StringRef getDescription() const override { return "Prepare moving dynamic broadcasts up over element-wise operations and broadcast the operands rather than the result. This will eventually allow for larger fusions."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("MergeAssumingOpsPass");
  }
  ::llvm::StringRef getName() const override { return "MergeAssumingOpsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(MergeAssumingOpsPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class PrepareForExportPassBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = PrepareForExportPassBase;

  PrepareForExportPassBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  PrepareForExportPassBase(const PrepareForExportPassBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}
  PrepareForExportPassBase& operator=(const PrepareForExportPassBase &) = delete;
  PrepareForExportPassBase(PrepareForExportPassBase &&) = delete;
  PrepareForExportPassBase& operator=(PrepareForExportPassBase &&) = delete;
  ~PrepareForExportPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("xla-prepare-for-export");
  }
  ::llvm::StringRef getArgument() const override { return "xla-prepare-for-export"; }

  ::llvm::StringRef getDescription() const override { return "Prepare for XLA export"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("PrepareForExportPass");
  }
  ::llvm::StringRef getName() const override { return "PrepareForExportPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(PrepareForExportPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ShapeLegalizeToHloPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = ShapeLegalizeToHloPassBase;

  ShapeLegalizeToHloPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  ShapeLegalizeToHloPassBase(const ShapeLegalizeToHloPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  ShapeLegalizeToHloPassBase& operator=(const ShapeLegalizeToHloPassBase &) = delete;
  ShapeLegalizeToHloPassBase(ShapeLegalizeToHloPassBase &&) = delete;
  ShapeLegalizeToHloPassBase& operator=(ShapeLegalizeToHloPassBase &&) = delete;
  ~ShapeLegalizeToHloPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("shape-legalize-to-hlo");
  }
  ::llvm::StringRef getArgument() const override { return "shape-legalize-to-hlo"; }

  ::llvm::StringRef getDescription() const override { return "Legalize shape-related ops to HLO."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ShapeLegalizeToHloPass");
  }
  ::llvm::StringRef getName() const override { return "ShapeLegalizeToHloPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mhlo::MhloDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ShapeLegalizeToHloPassBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> legalize_constraints_{*this, "legalize-constraints", ::llvm::cl::desc("Whether to legalize Cstr Ops to shape_assertion custom_call"), ::llvm::cl::init(false)};
};

template <typename DerivedT>
class ShapeSimplificationBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = ShapeSimplificationBase;

  ShapeSimplificationBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  ShapeSimplificationBase(const ShapeSimplificationBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}
  ShapeSimplificationBase& operator=(const ShapeSimplificationBase &) = delete;
  ShapeSimplificationBase(ShapeSimplificationBase &&) = delete;
  ShapeSimplificationBase& operator=(ShapeSimplificationBase &&) = delete;
  ~ShapeSimplificationBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("shape-simplification");
  }
  ::llvm::StringRef getArgument() const override { return "shape-simplification"; }

  ::llvm::StringRef getDescription() const override { return "Simplify shape ops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ShapeSimplification");
  }
  ::llvm::StringRef getName() const override { return "ShapeSimplification"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ShapeSimplificationBase<DerivedT>)

protected:
};

template <typename DerivedT>
class SinkConstantsToControlFlowPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = SinkConstantsToControlFlowPassBase;

  SinkConstantsToControlFlowPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  SinkConstantsToControlFlowPassBase(const SinkConstantsToControlFlowPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  SinkConstantsToControlFlowPassBase& operator=(const SinkConstantsToControlFlowPassBase &) = delete;
  SinkConstantsToControlFlowPassBase(SinkConstantsToControlFlowPassBase &&) = delete;
  SinkConstantsToControlFlowPassBase& operator=(SinkConstantsToControlFlowPassBase &&) = delete;
  ~SinkConstantsToControlFlowPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("mhlo-sink-constants-to-control-flow");
  }
  ::llvm::StringRef getArgument() const override { return "mhlo-sink-constants-to-control-flow"; }

  ::llvm::StringRef getDescription() const override { return "Sink constants implicitly captured in control flow regions. This is necessary to export to XLA."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SinkConstantsToControlFlowPass");
  }
  ::llvm::StringRef getName() const override { return "SinkConstantsToControlFlowPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SinkConstantsToControlFlowPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class StablehloLegalizeToHloPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = StablehloLegalizeToHloPassBase;

  StablehloLegalizeToHloPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  StablehloLegalizeToHloPassBase(const StablehloLegalizeToHloPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  StablehloLegalizeToHloPassBase& operator=(const StablehloLegalizeToHloPassBase &) = delete;
  StablehloLegalizeToHloPassBase(StablehloLegalizeToHloPassBase &&) = delete;
  StablehloLegalizeToHloPassBase& operator=(StablehloLegalizeToHloPassBase &&) = delete;
  ~StablehloLegalizeToHloPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("stablehlo-legalize-to-hlo");
  }
  ::llvm::StringRef getArgument() const override { return "stablehlo-legalize-to-hlo"; }

  ::llvm::StringRef getDescription() const override { return "Legalize StableHLO to HLO."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("StablehloLegalizeToHloPass");
  }
  ::llvm::StringRef getName() const override { return "StablehloLegalizeToHloPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mhlo::MhloDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(StablehloLegalizeToHloPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class SymbolicShapeOptimizationBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = SymbolicShapeOptimizationBase;

  SymbolicShapeOptimizationBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  SymbolicShapeOptimizationBase(const SymbolicShapeOptimizationBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  SymbolicShapeOptimizationBase& operator=(const SymbolicShapeOptimizationBase &) = delete;
  SymbolicShapeOptimizationBase(SymbolicShapeOptimizationBase &&) = delete;
  SymbolicShapeOptimizationBase& operator=(SymbolicShapeOptimizationBase &&) = delete;
  ~SymbolicShapeOptimizationBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("symbolic-shape-optimization");
  }
  ::llvm::StringRef getArgument() const override { return "symbolic-shape-optimization"; }

  ::llvm::StringRef getDescription() const override { return "Analyzes shapes and performs shape-related optimizations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SymbolicShapeOptimization");
  }
  ::llvm::StringRef getName() const override { return "SymbolicShapeOptimization"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SymbolicShapeOptimizationBase<DerivedT>)

protected:
};

template <typename DerivedT>
class TestInferShapedTypeMethodsPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = TestInferShapedTypeMethodsPassBase;

  TestInferShapedTypeMethodsPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  TestInferShapedTypeMethodsPassBase(const TestInferShapedTypeMethodsPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  TestInferShapedTypeMethodsPassBase& operator=(const TestInferShapedTypeMethodsPassBase &) = delete;
  TestInferShapedTypeMethodsPassBase(TestInferShapedTypeMethodsPassBase &&) = delete;
  TestInferShapedTypeMethodsPassBase& operator=(TestInferShapedTypeMethodsPassBase &&) = delete;
  ~TestInferShapedTypeMethodsPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("mhlo-test-infer-shaped-type-methods");
  }
  ::llvm::StringRef getArgument() const override { return "mhlo-test-infer-shaped-type-methods"; }

  ::llvm::StringRef getDescription() const override { return "Uses test ops to invoke InferShapedTypeOpInterface methods."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TestInferShapedTypeMethodsPass");
  }
  ::llvm::StringRef getName() const override { return "TestInferShapedTypeMethodsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TestInferShapedTypeMethodsPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class TestMaterializeBroadcastsPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = TestMaterializeBroadcastsPassBase;

  TestMaterializeBroadcastsPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  TestMaterializeBroadcastsPassBase(const TestMaterializeBroadcastsPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  TestMaterializeBroadcastsPassBase& operator=(const TestMaterializeBroadcastsPassBase &) = delete;
  TestMaterializeBroadcastsPassBase(TestMaterializeBroadcastsPassBase &&) = delete;
  TestMaterializeBroadcastsPassBase& operator=(TestMaterializeBroadcastsPassBase &&) = delete;
  ~TestMaterializeBroadcastsPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("mhlo-test-materialize-broadcasts");
  }
  ::llvm::StringRef getArgument() const override { return "mhlo-test-materialize-broadcasts"; }

  ::llvm::StringRef getDescription() const override { return "Test pass for materializing 'broadcast_dimensions' attributes."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TestMaterializeBroadcastsPass");
  }
  ::llvm::StringRef getName() const override { return "TestMaterializeBroadcastsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TestMaterializeBroadcastsPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class TestUnfuseBatchNormPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = TestUnfuseBatchNormPassBase;

  TestUnfuseBatchNormPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  TestUnfuseBatchNormPassBase(const TestUnfuseBatchNormPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  TestUnfuseBatchNormPassBase& operator=(const TestUnfuseBatchNormPassBase &) = delete;
  TestUnfuseBatchNormPassBase(TestUnfuseBatchNormPassBase &&) = delete;
  TestUnfuseBatchNormPassBase& operator=(TestUnfuseBatchNormPassBase &&) = delete;
  ~TestUnfuseBatchNormPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("mhlo-test-unfuse-batch-norm");
  }
  ::llvm::StringRef getArgument() const override { return "mhlo-test-unfuse-batch-norm"; }

  ::llvm::StringRef getDescription() const override { return "Test pass for materializing 'broadcast_dimensions' attributes."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TestUnfuseBatchNormPass");
  }
  ::llvm::StringRef getName() const override { return "TestUnfuseBatchNormPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<arith::ArithDialect>();
    registry.insert<shape::ShapeDialect>();
    registry.insert<tensor::TensorDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TestUnfuseBatchNormPassBase<DerivedT>)

protected:
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
