@echo off
echo 正在处理RNNoise训练数据...

REM 设置数据路径
set CLEAN_DATA=..\data\clean_PCM_data
set NOISE_DATA=..\data\wind_PCM_data
set OUTPUT_FILE=output.f32

REM 检查数据目录是否存在
if not exist "%CLEAN_DATA%" (
    echo 错误：找不到干净语音数据目录 %CLEAN_DATA%
    pause
    exit /b 1
)

if not exist "%NOISE_DATA%" (
    echo 错误：找不到噪声数据目录 %NOISE_DATA%
    pause
    exit /b 1
)

REM 检查是否有可执行文件
if not exist "denoise_training.exe" (
    echo 错误：找不到denoise_training.exe，请先编译程序
    echo 运行 compile_windows.bat 来编译程序
    pause
    exit /b 1
)

REM 使用具体的PCM文件
set SPEECH_FILE=%CLEAN_DATA%\000_001.pcm
set NOISE_FILE=%NOISE_DATA%\000_001.pcm
set FG_NOISE_FILE=%NOISE_DATA%\000_001.pcm

REM 检查文件是否存在
if not exist "%SPEECH_FILE%" (
    echo 错误：找不到语音文件 %SPEECH_FILE%
    pause
    exit /b 1
)

if not exist "%NOISE_FILE%" (
    echo 错误：找不到噪声文件 %NOISE_FILE%
    pause
    exit /b 1
)

echo 使用文件：
echo 语音文件: %SPEECH_FILE%
echo 噪声文件: %NOISE_FILE%
echo 前景噪声文件: %FG_NOISE_FILE%
echo 输出文件: %OUTPUT_FILE%
echo 处理数量: 500000 (约5分钟)
echo.

REM 运行denoise_training
echo 开始处理...
denoise_training.exe "%SPEECH_FILE%" "%NOISE_FILE%" "%FG_NOISE_FILE%" "%OUTPUT_FILE%" 500000

if %ERRORLEVEL% EQU 0 (
    echo.
    echo 处理完成！
    echo 生成的特征文件: %OUTPUT_FILE%
    echo 文件大小:
    dir "%OUTPUT_FILE%"
) else (
    echo.
    echo 处理失败！错误代码: %ERRORLEVEL%
)

pause
