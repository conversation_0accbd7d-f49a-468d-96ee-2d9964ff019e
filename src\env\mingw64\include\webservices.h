/*
 * Copyright 2015 Nikolay Sivov for CodeWeavers
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

#ifndef __WINE_WEBSERVICES_H
#define __WINE_WEBSERVICES_H

#ifdef __cplusplus
extern "C" {
#endif  /* __cplusplus */

typedef struct _WS_ERROR WS_ERROR;
typedef struct _WS_HEAP WS_HEAP;
typedef struct _WS_XML_BUFFER WS_XML_BUFFER;
typedef struct _WS_XML_READER WS_XML_READER;
typedef struct _WS_XML_WRITER WS_XML_WRITER;
typedef struct _WS_PROXY_PROPERTY WS_PROXY_PROPERTY;
typedef struct _WS_SECURITY_DESCRIPTION WS_SECURITY_DESCRIPTION;
typedef struct _WS_CHANNEL_PROPERTY WS_CHANNEL_PROPERTY;
typedef struct _WS_SERVICE_PROXY WS_SERVICE_PROXY;
typedef struct _WS_SECURITY_BINDING_PROPERTY WS_SECURITY_BINDING_PROPERTY;
typedef struct _WS_SECURITY_PROPERTY WS_SECURITY_PROPERTY;
typedef struct _WS_SECURITY_PROPERTIES WS_SECURITY_PROPERTIES;
typedef struct _WS_SECURITY_BINDING WS_SECURITY_BINDING;
typedef struct _WS_CHANNEL WS_CHANNEL;
typedef struct _WS_MESSAGE_PROPERTY WS_MESSAGE_PROPERTY;
typedef struct _WS_MESSAGE_PROPERTIES WS_MESSAGE_PROPERTIES;
typedef struct _WS_HTTP_BINDING_TEMPLATE WS_HTTP_BINDING_TEMPLATE;
typedef struct _WS_HTTP_SSL_BINDING_TEMPLATE WS_HTTP_SSL_BINDING_TEMPLATE;
typedef struct _WS_CHANNEL_PROPERTIES WS_CHANNEL_PROPERTIES;
typedef struct _WS_SSL_TRANSPORT_SECURITY_BINDING_TEMPLATE WS_SSL_TRANSPORT_SECURITY_BINDING_TEMPLATE;
typedef struct _WS_SECURITY_BINDING_PROPERTIES WS_SECURITY_BINDING_PROPERTIES;
typedef struct _WS_CERT_CREDENTIAL WS_CERT_CREDENTIAL;
typedef struct _WS_ENDPOINT_ADDRESS WS_ENDPOINT_ADDRESS;
typedef struct _WS_ENDPOINT_IDENTITY WS_ENDPOINT_IDENTITY;
typedef struct _WS_ENUM_DESCRIPTION WS_ENUM_DESCRIPTION;
typedef struct _WS_ENUM_VALUE WS_ENUM_VALUE;
typedef struct _WS_HTTP_POLICY_DESCRIPTION WS_HTTP_POLICY_DESCRIPTION;
typedef struct _WS_MESSAGE WS_MESSAGE;
typedef struct _WS_MESSAGE_DESCRIPTION WS_MESSAGE_DESCRIPTION;
typedef struct _WS_OPERATION_DESCRIPTION WS_OPERATION_DESCRIPTION;
typedef struct _WS_PARAMETER_DESCRIPTION WS_PARAMETER_DESCRIPTION;
typedef struct _WS_OPERATION_CONTEXT WS_OPERATION_CONTEXT;
typedef struct _WS_CALL_PROPERTY WS_CALL_PROPERTY;
typedef struct _WS_FLOAT_DESCRIPTION WS_FLOAT_DESCRIPTION;
typedef struct _WS_DOUBLE_DESCRIPTION WS_DOUBLE_DESCRIPTION;
typedef struct _WS_DATETIME WS_DATETIME;
typedef struct _WS_XML_DATETIME_TEXT WS_XML_DATETIME_TEXT;
typedef struct _WS_XML_BASE64_TEXT WS_XML_BASE64_TEXT;
typedef struct _WS_DATETIME_DESCRIPTION WS_DATETIME_DESCRIPTION;
typedef struct _WS_GUID_DESCRIPTION WS_GUID_DESCRIPTION;
typedef struct _WS_UNIQUE_ID_DESCRIPTION WS_UNIQUE_ID_DESCRIPTION;
typedef struct _WS_BYTES_DESCRIPTION WS_BYTES_DESCRIPTION;
typedef struct _WS_URL WS_URL;
typedef struct _WS_HTTP_URL WS_HTTP_URL;
typedef struct _WS_HTTPS_URL WS_HTTPS_URL;
typedef struct _WS_NETTCP_URL WS_NETTCP_URL;
typedef struct _WS_SOAPUDP_URL WS_SOAPUDP_URL;
typedef struct _WS_NETPIPE_URL WS_NETPIPE_URL;
typedef struct _WS_CUSTOM_CHANNEL_CALLBACKS WS_CUSTOM_CHANNEL_CALLBACKS;
typedef struct _WS_CHANNEL_ENCODER WS_CHANNEL_ENCODER;
typedef struct _WS_CHANNEL_DECODER WS_CHANNEL_DECODER;
typedef struct _WS_CUSTOM_HTTP_PROXY WS_CUSTOM_HTTP_PROXY;
typedef struct _WS_HTTP_MESSAGE_MAPPING WS_HTTP_MESSAGE_MAPPING;
typedef struct _WS_HTTP_HEADER_MAPPING WS_HTTP_HEADER_MAPPING;
typedef struct _WS_HTTP_REDIRECT_CALLBACK_CONTEXT WS_HTTP_REDIRECT_CALLBACK_CONTEXT;
typedef struct _WS_PROXY_MESSAGE_CALLBACK_CONTEXT WS_PROXY_MESSAGE_CALLBACK_CONTEXT;
typedef struct _WS_LISTENER WS_LISTENER;
typedef struct _WS_LISTENER_PROPERTY WS_LISTENER_PROPERTY;
typedef struct _WS_DISALLOWED_USER_AGENT_SUBSTRINGS WS_DISALLOWED_USER_AGENT_SUBSTRINGS;
typedef struct _WS_LISTENER_PROPERTIES WS_LISTENER_PROPERTIES;
typedef struct _WS_CUSTOM_LISTENER_CALLBACKS WS_CUSTOM_LISTENER_CALLBACKS;

struct _WS_STRUCT_DESCRIPTION;
struct _WS_XML_STRING;

typedef enum {
    WS_ERROR_PROPERTY_STRING_COUNT,
    WS_ERROR_PROPERTY_ORIGINAL_ERROR_CODE,
    WS_ERROR_PROPERTY_LANGID
} WS_ERROR_PROPERTY_ID;

typedef struct _WS_ERROR_PROPERTY {
    WS_ERROR_PROPERTY_ID id;
    void                *value;
    ULONG                valueSize;
} WS_ERROR_PROPERTY;

typedef enum {
    WS_HEAP_PROPERTY_MAX_SIZE,
    WS_HEAP_PROPERTY_TRIM_SIZE,
    WS_HEAP_PROPERTY_REQUESTED_SIZE,
    WS_HEAP_PROPERTY_ACTUAL_SIZE
} WS_HEAP_PROPERTY_ID;

typedef struct _WS_HEAP_PROPERTY {
    WS_HEAP_PROPERTY_ID id;
    void               *value;
    ULONG               valueSize;
} WS_HEAP_PROPERTY;

typedef struct _WS_HEAP_PROPERTIES {
    WS_HEAP_PROPERTY *properties;
    ULONG             propertyCount;
} WS_HEAP_PROPERTIES;

typedef ULONG WS_XML_BUFFER_PROPERTY_ID;

typedef struct _WS_XML_BUFFER_PROPERTY {
    WS_XML_BUFFER_PROPERTY_ID id;
    void                     *value;
    ULONG                     valueSize;
} WS_XML_BUFFER_PROPERTY;

typedef enum {
    WS_XML_READER_PROPERTY_MAX_DEPTH,
    WS_XML_READER_PROPERTY_ALLOW_FRAGMENT,
    WS_XML_READER_PROPERTY_MAX_ATTRIBUTES,
    WS_XML_READER_PROPERTY_READ_DECLARATION,
    WS_XML_READER_PROPERTY_CHARSET,
    WS_XML_READER_PROPERTY_ROW,
    WS_XML_READER_PROPERTY_COLUMN,
    WS_XML_READER_PROPERTY_UTF8_TRIM_SIZE,
    WS_XML_READER_PROPERTY_STREAM_BUFFER_SIZE,
    WS_XML_READER_PROPERTY_IN_ATTRIBUTE,
    WS_XML_READER_PROPERTY_STREAM_MAX_ROOT_MIME_PART_SIZE,
    WS_XML_READER_PROPERTY_STREAM_MAX_MIME_HEADERS_SIZE,
    WS_XML_READER_PROPERTY_MAX_MIME_PARTS,
    WS_XML_READER_PROPERTY_ALLOW_INVALID_CHARACTER_REFERENCES,
    WS_XML_READER_PROPERTY_MAX_NAMESPACES
} WS_XML_READER_PROPERTY_ID;

typedef struct _WS_XML_READER_PROPERTY {
    WS_XML_READER_PROPERTY_ID id;
    void                     *value;
    ULONG                     valueSize;
} WS_XML_READER_PROPERTY;

typedef struct _WS_XML_READER_PROPERTIES {
    WS_XML_READER_PROPERTY *properties;
    ULONG                   propertyCount;
} WS_XML_READER_PROPERTIES;

typedef enum {
    WS_XML_WRITER_PROPERTY_MAX_DEPTH,
    WS_XML_WRITER_PROPERTY_ALLOW_FRAGMENT,
    WS_XML_WRITER_PROPERTY_MAX_ATTRIBUTES,
    WS_XML_WRITER_PROPERTY_WRITE_DECLARATION,
    WS_XML_WRITER_PROPERTY_INDENT,
    WS_XML_WRITER_PROPERTY_BUFFER_TRIM_SIZE,
    WS_XML_WRITER_PROPERTY_CHARSET,
    WS_XML_WRITER_PROPERTY_BUFFERS,
    WS_XML_WRITER_PROPERTY_BUFFER_MAX_SIZE,
    WS_XML_WRITER_PROPERTY_BYTES,
    WS_XML_WRITER_PROPERTY_IN_ATTRIBUTE,
    WS_XML_WRITER_PROPERTY_MAX_MIME_PARTS_BUFFER_SIZE,
    WS_XML_WRITER_PROPERTY_INITIAL_BUFFER,
    WS_XML_WRITER_PROPERTY_ALLOW_INVALID_CHARACTER_REFERENCES,
    WS_XML_WRITER_PROPERTY_MAX_NAMESPACES,
    WS_XML_WRITER_PROPERTY_BYTES_WRITTEN,
    WS_XML_WRITER_PROPERTY_BYTES_TO_CLOSE,
    WS_XML_WRITER_PROPERTY_COMPRESS_EMPTY_ELEMENTS,
    WS_XML_WRITER_PROPERTY_EMIT_UNCOMPRESSED_EMPTY_ELEMENTS
} WS_XML_WRITER_PROPERTY_ID;

typedef struct _WS_XML_WRITER_PROPERTY {
    WS_XML_WRITER_PROPERTY_ID id;
    void                     *value;
    ULONG                     valueSize;
} WS_XML_WRITER_PROPERTY;

typedef struct _WS_XML_WRITER_PROPERTIES {
    WS_XML_WRITER_PROPERTY *properties;
    ULONG                   propertyCount;
} WS_XML_WRITER_PROPERTIES;

typedef struct _WS_BYTES {
    ULONG length;
    BYTE *bytes;
} WS_BYTES;

typedef struct _WS_BUFFERS {
    ULONG bufferCount;
    WS_BYTES *buffers;
} WS_BUFFERS;

typedef enum {
    WS_XML_READER_ENCODING_TYPE_TEXT   = 1,
    WS_XML_READER_ENCODING_TYPE_BINARY = 2,
    WS_XML_READER_ENCODING_TYPE_MTOM   = 3,
    WS_XML_READER_ENCODING_TYPE_RAW    = 4
} WS_XML_READER_ENCODING_TYPE;

typedef struct _WS_XML_READER_ENCODING {
    WS_XML_READER_ENCODING_TYPE encodingType;
} WS_XML_READER_ENCODING;

typedef enum {
    WS_XML_WRITER_ENCODING_TYPE_TEXT   = 1,
    WS_XML_WRITER_ENCODING_TYPE_BINARY = 2,
    WS_XML_WRITER_ENCODING_TYPE_MTOM   = 3,
    WS_XML_WRITER_ENCODING_TYPE_RAW    = 4
} WS_XML_WRITER_ENCODING_TYPE;

typedef struct _WS_XML_WRITER_ENCODING {
    WS_XML_WRITER_ENCODING_TYPE encodingType;
} WS_XML_WRITER_ENCODING;

typedef enum {
    WS_CHARSET_AUTO,
    WS_CHARSET_UTF8,
    WS_CHARSET_UTF16LE,
    WS_CHARSET_UTF16BE
} WS_CHARSET;

typedef struct _WS_XML_DICTIONARY {
    GUID                   guid;
    struct _WS_XML_STRING *strings;
    ULONG                  stringCount;
    WINBOOL                isConst;
} WS_XML_DICTIONARY;

typedef struct _WS_XML_STRING {
    ULONG              length;
    BYTE              *bytes;
    WS_XML_DICTIONARY *dictionary;
    ULONG              id;
} WS_XML_STRING;

typedef struct _WS_XML_READER_TEXT_ENCODING {
    WS_XML_READER_ENCODING encoding;
    WS_CHARSET charSet;
} WS_XML_READER_TEXT_ENCODING;

typedef struct _WS_XML_READER_BINARY_ENCODING {
    WS_XML_READER_ENCODING encoding;
    WS_XML_DICTIONARY *staticDictionary;
    WS_XML_DICTIONARY *dynamicDictionary;
} WS_XML_READER_BINARY_ENCODING;

typedef struct _WS_XML_WRITER_TEXT_ENCODING {
    WS_XML_WRITER_ENCODING encoding;
    WS_CHARSET charSet;
} WS_XML_WRITER_TEXT_ENCODING;

typedef HRESULT (CALLBACK *WS_DYNAMIC_STRING_CALLBACK)
    (void*, const WS_XML_STRING*, WINBOOL*, ULONG*, WS_ERROR*);

typedef struct _WS_XML_WRITER_BINARY_ENCODING {
    WS_XML_WRITER_ENCODING encoding;
    WS_XML_DICTIONARY *staticDictionary;
    WS_DYNAMIC_STRING_CALLBACK dynamicStringCallback;
    void *dynamicStringCallbackState;
} WS_XML_WRITER_BINARY_ENCODING;

typedef enum {
    WS_XML_READER_INPUT_TYPE_BUFFER = 1,
    WS_XML_READER_INPUT_TYPE_STREAM = 2
} WS_XML_READER_INPUT_TYPE;

typedef enum {
    WS_XML_WRITER_OUTPUT_TYPE_BUFFER = 1,
    WS_XML_WRITER_OUTPUT_TYPE_STREAM = 2
} WS_XML_WRITER_OUTPUT_TYPE;

typedef struct _WS_XML_READER_INPUT {
    WS_XML_READER_INPUT_TYPE inputType;
} WS_XML_READER_INPUT;

typedef struct _WS_XML_WRITER_OUTPUT {
    WS_XML_WRITER_OUTPUT_TYPE outputType;
} WS_XML_WRITER_OUTPUT;

typedef struct _WS_XML_READER_BUFFER_INPUT {
    WS_XML_READER_INPUT input;
    void *encodedData;
    ULONG encodedDataSize;
} WS_XML_READER_BUFFER_INPUT;

typedef struct _WS_XML_WRITER_BUFFER_OUTPUT {
    WS_XML_WRITER_OUTPUT output;
} WS_XML_WRITER_BUFFER_OUTPUT;

typedef enum {
    WS_SHORT_CALLBACK,
    WS_LONG_CALLBACK
} WS_CALLBACK_MODEL;

typedef void (CALLBACK *WS_ASYNC_CALLBACK)
    (HRESULT, WS_CALLBACK_MODEL, void *);

typedef struct _WS_ASYNC_CONTEXT {
    WS_ASYNC_CALLBACK callback;
    void             *callbackState;
} WS_ASYNC_CONTEXT;

typedef HRESULT (CALLBACK *WS_READ_CALLBACK)
    (void*, void*, ULONG, ULONG*, const WS_ASYNC_CONTEXT*, WS_ERROR*);

typedef struct _WS_XML_READER_STREAM_INPUT {
    WS_XML_READER_INPUT input;
    WS_READ_CALLBACK readCallback;
    void *readCallbackState;
} WS_XML_READER_STREAM_INPUT;

typedef HRESULT (CALLBACK *WS_WRITE_CALLBACK)
    (void*, const WS_BYTES*, ULONG, const WS_ASYNC_CONTEXT*, WS_ERROR*);

typedef struct _WS_XML_WRITER_STREAM_OUTPUT {
    WS_XML_WRITER_OUTPUT output;
    WS_WRITE_CALLBACK writeCallback;
    void *writeCallbackState;
} WS_XML_WRITER_STREAM_OUTPUT;

typedef enum {
    WS_ELEMENT_TYPE_MAPPING         = 1,
    WS_ATTRIBUTE_TYPE_MAPPING       = 2,
    WS_ELEMENT_CONTENT_TYPE_MAPPING = 3,
    WS_ANY_ELEMENT_TYPE_MAPPING     = 4
} WS_TYPE_MAPPING;

typedef enum {
    WS_BOOL_TYPE,
    WS_INT8_TYPE,
    WS_INT16_TYPE,
    WS_INT32_TYPE,
    WS_INT64_TYPE,
    WS_UINT8_TYPE,
    WS_UINT16_TYPE,
    WS_UINT32_TYPE,
    WS_UINT64_TYPE,
    WS_FLOAT_TYPE,
    WS_DOUBLE_TYPE,
    WS_DECIMAL_TYPE,
    WS_DATETIME_TYPE,
    WS_TIMESPAN_TYPE,
    WS_GUID_TYPE,
    WS_UNIQUE_ID_TYPE,
    WS_STRING_TYPE,
    WS_WSZ_TYPE,
    WS_BYTES_TYPE,
    WS_XML_STRING_TYPE,
    WS_XML_QNAME_TYPE,
    WS_XML_BUFFER_TYPE,
    WS_CHAR_ARRAY_TYPE,
    WS_UTF8_ARRAY_TYPE,
    WS_BYTE_ARRAY_TYPE,
    WS_DESCRIPTION_TYPE,
    WS_STRUCT_TYPE,
    WS_CUSTOM_TYPE,
    WS_ENDPOINT_ADDRESS_TYPE,
    WS_FAULT_TYPE,
    WS_VOID_TYPE,
    WS_ENUM_TYPE,
    WS_DURATION_TYPE,
    WS_UNION_TYPE,
    WS_ANY_ATTRIBUTES_TYPE
} WS_TYPE;

typedef enum {
    WS_READ_REQUIRED_VALUE   = 1,
    WS_READ_REQUIRED_POINTER = 2,
    WS_READ_OPTIONAL_POINTER = 3,
    WS_READ_NILLABLE_POINTER = 4,
    WS_READ_NILLABLE_VALUE   = 5
} WS_READ_OPTION;

typedef enum {
    WS_WRITE_REQUIRED_VALUE   = 1,
    WS_WRITE_REQUIRED_POINTER = 2,
    WS_WRITE_NILLABLE_VALUE   = 3,
    WS_WRITE_NILLABLE_POINTER = 4
} WS_WRITE_OPTION;

typedef struct _WS_BOOL_DESCRIPTION {
    WINBOOL value;
} WS_BOOL_DESCRIPTION;

typedef struct _WS_INT8_DESCRIPTION {
    char minValue;
    char maxValue;
} WS_INT8_DESCRIPTION;

typedef struct _WS_INT16_DESCRIPTION {
    short minValue;
    short maxValue;
} WS_INT16_DESCRIPTION;

typedef struct _WS_INT32_DESCRIPTION {
    int minValue;
    int maxValue;
} WS_INT32_DESCRIPTION;

typedef struct _WS_INT64_DESCRIPTION {
    __int64 DECLSPEC_ALIGN(8) minValue;
    __int64 DECLSPEC_ALIGN(8) maxValue;
} WS_INT64_DESCRIPTION;

typedef struct _WS_UINT8_DESCRIPTION {
    BYTE minValue;
    BYTE maxValue;
} WS_UINT8_DESCRIPTION;

typedef struct _WS_UINT16_DESCRIPTION {
    USHORT minValue;
    USHORT maxValue;
} WS_UINT16_DESCRIPTION;

typedef struct _WS_UINT32_DESCRIPTION {
    ULONG minValue;
    ULONG maxValue;
} WS_UINT32_DESCRIPTION;

typedef struct _WS_UINT64_DESCRIPTION {
    unsigned __int64 DECLSPEC_ALIGN(8) minValue;
    unsigned __int64 DECLSPEC_ALIGN(8) maxValue;
} WS_UINT64_DESCRIPTION;

typedef struct _WS_WSZ_DESCRIPTION {
    ULONG minCharCount;
    ULONG maxCharCount;
} WS_WSZ_DESCRIPTION;

typedef struct _WS_STRING_DESCRIPTION {
    ULONG minCharCount;
    ULONG maxCharCount;
} WS_STRING_DESCRIPTION;

typedef struct _WS_XML_STRING_DESCRIPTION {
    ULONG minByteCount;
    ULONG maxByteCount;
} WS_XML_STRING_DESCRIPTION;

typedef struct _WS_XML_QNAME_DESCRIPTION {
    ULONG minLocalNameByteCount;
    ULONG maxLocalNameByteCount;
    ULONG minNsByteCount;
    ULONG maxNsByteCount;
} WS_XML_QNAME_DESCRIPTION;

struct _WS_ENUM_VALUE {
    int value;
    WS_XML_STRING *name;
};

struct _WS_ENUM_DESCRIPTION {
    WS_ENUM_VALUE *values;
    ULONG valueCount;
    ULONG maxByteCount;
    ULONG *nameIndices;
};

struct _WS_FLOAT_DESCRIPTION {
    float minValue;
    float maxValue;
};

struct _WS_DOUBLE_DESCRIPTION {
    double DECLSPEC_ALIGN(8) minValue;
    double DECLSPEC_ALIGN(8) maxValue;
};

struct _WS_GUID_DESCRIPTION {
    GUID value;
};

struct _WS_UNIQUE_ID_DESCRIPTION {
    ULONG minCharCount;
    ULONG maxCharCount;
};

struct _WS_BYTES_DESCRIPTION {
    ULONG minByteCount;
    ULONG maxByteCount;
};

typedef enum {
    WS_TYPE_ATTRIBUTE_FIELD_MAPPING,
    WS_ATTRIBUTE_FIELD_MAPPING,
    WS_ELEMENT_FIELD_MAPPING,
    WS_REPEATING_ELEMENT_FIELD_MAPPING,
    WS_TEXT_FIELD_MAPPING,
    WS_NO_FIELD_MAPPING,
    WS_XML_ATTRIBUTE_FIELD_MAPPING,
    WS_ELEMENT_CHOICE_FIELD_MAPPING,
    WS_REPEATING_ELEMENT_CHOICE_FIELD_MAPPING,
    WS_ANY_ELEMENT_FIELD_MAPPING,
    WS_REPEATING_ANY_ELEMENT_FIELD_MAPPING,
    WS_ANY_CONTENT_FIELD_MAPPING,
    WS_ANY_ATTRIBUTES_FIELD_MAPPING
} WS_FIELD_MAPPING;

typedef struct _WS_DEFAULT_VALUE {
    void *value;
    ULONG valueSize;
} WS_DEFAULT_VALUE;

typedef struct _WS_ITEM_RANGE {
    ULONG minItemCount;
    ULONG maxItemCount;
} WS_ITEM_RANGE;

enum
{
    WS_FIELD_POINTER         = 0x1,
    WS_FIELD_OPTIONAL        = 0x2,
    WS_FIELD_NILLABLE        = 0x4,
    WS_FIELD_NILLABLE_ITEM   = 0x8,
    WS_FIELD_OTHER_NAMESPACE = 0x10
};

typedef struct _WS_FIELD_DESCRIPTION {
    WS_FIELD_MAPPING mapping;
    WS_XML_STRING *localName;
    WS_XML_STRING *ns;
    WS_TYPE type;
    void *typeDescription;
    ULONG offset;
    ULONG options;
    WS_DEFAULT_VALUE *defaultValue;
    ULONG countOffset;
    WS_XML_STRING *itemLocalName;
    WS_XML_STRING *itemNs;
    WS_ITEM_RANGE *itemRange;
} WS_FIELD_DESCRIPTION;

enum
{
    WS_STRUCT_ABSTRACT                        = 0x1,
    WS_STRUCT_IGNORE_TRAILING_ELEMENT_CONTENT = 0x2,
    WS_STRUCT_IGNORE_UNHANDLED_ATTRIBUTES     = 0x4
};

typedef struct _WS_STRUCT_DESCRIPTION {
    ULONG size;
    ULONG alignment;
    WS_FIELD_DESCRIPTION **fields;
    ULONG fieldCount;
    WS_XML_STRING *typeLocalName;
    WS_XML_STRING *typeNs;
    struct _WS_STRUCT_DESCRIPTION *parentType;
    struct _WS_STRUCT_DESCRIPTION **subTypes;
    ULONG subTypeCount;
    ULONG structOptions;
} WS_STRUCT_DESCRIPTION;

typedef struct _WS_UNION_FIELD_DESCRIPTION {
    int value;
    WS_FIELD_DESCRIPTION field;
} WS_UNION_FIELD_DESCRIPTION;

typedef struct _WS_UNION_DESCRIPTION {
    ULONG size;
    ULONG alignment;
    WS_UNION_FIELD_DESCRIPTION **fields;
    ULONG fieldCount;
    ULONG enumOffset;
    int noneEnumValue;
    ULONG *valueIndices;
} WS_UNION_DESCRIPTION;

typedef struct _WS_ATTRIBUTE_DESCRIPTION {
    WS_XML_STRING *attributeLocalName;
    WS_XML_STRING *attributeNs;
    WS_TYPE type;
    void *typeDescription;
} WS_ATTRIBUTE_DESCRIPTION;

typedef struct _WS_ELEMENT_DESCRIPTION {
    WS_XML_STRING *elementLocalName;
    WS_XML_STRING *elementNs;
    WS_TYPE type;
    void *typeDescription;
} WS_ELEMENT_DESCRIPTION;

typedef struct _WS_STRING {
    ULONG length;
    WCHAR *chars;
} WS_STRING;

typedef struct _WS_UNIQUE_ID {
    WS_STRING uri;
    GUID guid;
} WS_UNIQUE_ID;

typedef enum {
    WS_XML_NODE_TYPE_ELEMENT     = 1,
    WS_XML_NODE_TYPE_TEXT        = 2,
    WS_XML_NODE_TYPE_END_ELEMENT = 3,
    WS_XML_NODE_TYPE_COMMENT     = 4,
    WS_XML_NODE_TYPE_CDATA       = 6,
    WS_XML_NODE_TYPE_END_CDATA   = 7,
    WS_XML_NODE_TYPE_EOF         = 8,
    WS_XML_NODE_TYPE_BOF         = 9
} WS_XML_NODE_TYPE;

typedef struct _WS_XML_NODE {
    WS_XML_NODE_TYPE nodeType;
} WS_XML_NODE;

typedef enum {
    WS_MOVE_TO_ROOT_ELEMENT,
    WS_MOVE_TO_NEXT_ELEMENT,
    WS_MOVE_TO_PREVIOUS_ELEMENT,
    WS_MOVE_TO_CHILD_ELEMENT,
    WS_MOVE_TO_END_ELEMENT,
    WS_MOVE_TO_PARENT_ELEMENT,
    WS_MOVE_TO_NEXT_NODE,
    WS_MOVE_TO_PREVIOUS_NODE,
    WS_MOVE_TO_FIRST_NODE,
    WS_MOVE_TO_BOF,
    WS_MOVE_TO_EOF,
    WS_MOVE_TO_CHILD_NODE
} WS_MOVE_TO;

typedef enum {
    WS_XML_TEXT_TYPE_UTF8      = 1,
    WS_XML_TEXT_TYPE_UTF16     = 2,
    WS_XML_TEXT_TYPE_BASE64    = 3,
    WS_XML_TEXT_TYPE_BOOL      = 4,
    WS_XML_TEXT_TYPE_INT32     = 5,
    WS_XML_TEXT_TYPE_INT64     = 6,
    WS_XML_TEXT_TYPE_UINT64    = 7,
    WS_XML_TEXT_TYPE_FLOAT     = 8,
    WS_XML_TEXT_TYPE_DOUBLE    = 9,
    WS_XML_TEXT_TYPE_DECIMAL   = 10,
    WS_XML_TEXT_TYPE_GUID      = 11,
    WS_XML_TEXT_TYPE_UNIQUE_ID = 12,
    WS_XML_TEXT_TYPE_DATETIME  = 13,
    WS_XML_TEXT_TYPE_TIMESPAN  = 14,
    WS_XML_TEXT_TYPE_QNAME     = 15,
    WS_XML_TEXT_TYPE_LIST      = 16
} WS_XML_TEXT_TYPE;

typedef struct _WS_XML_TEXT {
    WS_XML_TEXT_TYPE textType;
} WS_XML_TEXT;

typedef struct _WS_XML_UTF8_TEXT {
    WS_XML_TEXT text;
    WS_XML_STRING value;
} WS_XML_UTF8_TEXT;

typedef struct _WS_XML_UTF16_TEXT {
    WS_XML_TEXT text;
    BYTE *bytes;
    ULONG byteCount;
} WS_XML_UTF16_TEXT;

typedef struct _WS_XML_BOOL_TEXT {
    WS_XML_TEXT text;
    WINBOOL value;
} WS_XML_BOOL_TEXT;

typedef struct _WS_XML_INT32_TEXT {
    WS_XML_TEXT text;
    __int32 value;
} WS_XML_INT32_TEXT;

typedef struct _WS_XML_INT64_TEXT {
    WS_XML_TEXT text;
    __int64 DECLSPEC_ALIGN(8) value;
} WS_XML_INT64_TEXT;

typedef struct _WS_XML_UINT64_TEXT {
    WS_XML_TEXT text;
    unsigned __int64 DECLSPEC_ALIGN(8) value;
} WS_XML_UINT64_TEXT;

typedef struct _WS_XML_FLOAT_TEXT {
    WS_XML_TEXT text;
    float value;
} WS_XML_FLOAT_TEXT;

typedef struct _WS_XML_DOUBLE_TEXT {
    WS_XML_TEXT text;
    double DECLSPEC_ALIGN(8) value;
} WS_XML_DOUBLE_TEXT;

typedef struct _WS_XML_GUID_TEXT {
    WS_XML_TEXT text;
    GUID value;
} WS_XML_GUID_TEXT;

typedef struct _WS_XML_UNIQUE_ID_TEXT {
    WS_XML_TEXT text;
    GUID value;
} WS_XML_UNIQUE_ID_TEXT;

typedef struct _WS_XML_QNAME_TEXT {
    WS_XML_TEXT text;
    WS_XML_STRING *prefix;
    WS_XML_STRING *localName;
    WS_XML_STRING *ns;
} WS_XML_QNAME_TEXT;

typedef enum {
    WS_BOOL_VALUE_TYPE,
    WS_INT8_VALUE_TYPE,
    WS_INT16_VALUE_TYPE,
    WS_INT32_VALUE_TYPE,
    WS_INT64_VALUE_TYPE,
    WS_UINT8_VALUE_TYPE,
    WS_UINT16_VALUE_TYPE,
    WS_UINT32_VALUE_TYPE,
    WS_UINT64_VALUE_TYPE,
    WS_FLOAT_VALUE_TYPE,
    WS_DOUBLE_VALUE_TYPE,
    WS_DECIMAL_VALUE_TYPE,
    WS_DATETIME_VALUE_TYPE,
    WS_TIMESPAN_VALUE_TYPE,
    WS_GUID_VALUE_TYPE,
    WS_DURATION_VALUE_TYPE
} WS_VALUE_TYPE;

typedef struct _WS_XML_ATTRIBUTE {
    BYTE singleQuote;
    BYTE isXmlNs;
    WS_XML_STRING *prefix;
    WS_XML_STRING *localName;
    WS_XML_STRING *ns;
    WS_XML_TEXT *value;
} WS_XML_ATTRIBUTE;

typedef struct _WS_XML_ELEMENT_NODE {
    WS_XML_NODE node;
    WS_XML_STRING *prefix;
    WS_XML_STRING *localName;
    WS_XML_STRING *ns;
    ULONG attributeCount;
    WS_XML_ATTRIBUTE **attributes;
    WINBOOL isEmpty;
} WS_XML_ELEMENT_NODE;

typedef struct _WS_XML_TEXT_NODE {
    WS_XML_NODE node;
    WS_XML_TEXT *text;
} WS_XML_TEXT_NODE;

typedef struct _WS_XML_COMMENT_NODE {
    WS_XML_NODE node;
    WS_XML_STRING value;
} WS_XML_COMMENT_NODE;

typedef struct _WS_XML_NODE_POSITION {
    WS_XML_BUFFER *buffer;
    void *node;
} WS_XML_NODE_POSITION;

typedef struct _WS_XML_QNAME {
    WS_XML_STRING localName;
    WS_XML_STRING ns;
} WS_XML_QNAME;

typedef enum {
    WS_SERVICE_PROXY_STATE_CREATED,
    WS_SERVICE_PROXY_STATE_OPENING,
    WS_SERVICE_PROXY_STATE_OPEN,
    WS_SERVICE_PROXY_STATE_CLOSING,
    WS_SERVICE_PROXY_STATE_CLOSED,
    WS_SERVICE_PROXY_STATE_FAULTED
} WS_SERVICE_PROXY_STATE;

typedef enum {
    WS_PROXY_PROPERTY_CALL_TIMEOUT,
    WS_PROXY_PROPERTY_MESSAGE_PROPERTIES,
    WS_PROXY_PROPERTY_MAX_CALL_POOL_SIZE,
    WS_PROXY_PROPERTY_STATE,
    WS_PROXY_PROPERTY_MAX_PENDING_CALLS,
    WS_PROXY_PROPERTY_MAX_CLOSE_TIMEOUT,
    WS_PROXY_FAULT_LANG_ID
} WS_PROXY_PROPERTY_ID;

struct _WS_PROXY_PROPERTY {
    WS_PROXY_PROPERTY_ID id;
    void *value;
    ULONG valueSize;
};

typedef enum {
    WS_CHANNEL_TYPE_INPUT           = 0x1,
    WS_CHANNEL_TYPE_OUTPUT          = 0x2,
    WS_CHANNEL_TYPE_SESSION         = 0x4,
    WS_CHANNEL_TYPE_INPUT_SESSION   = (WS_CHANNEL_TYPE_INPUT  | WS_CHANNEL_TYPE_SESSION),
    WS_CHANNEL_TYPE_OUTPUT_SESSION  = (WS_CHANNEL_TYPE_OUTPUT | WS_CHANNEL_TYPE_SESSION),
    WS_CHANNEL_TYPE_DUPLEX          = (WS_CHANNEL_TYPE_INPUT  | WS_CHANNEL_TYPE_OUTPUT),
    WS_CHANNEL_TYPE_DUPLEX_SESSION  = (WS_CHANNEL_TYPE_INPUT  | WS_CHANNEL_TYPE_OUTPUT | WS_CHANNEL_TYPE_SESSION),
    WS_CHANNEL_TYPE_REQUEST         = 0x8,
    WS_CHANNEL_TYPE_REPLY           = 0x10
} WS_CHANNEL_TYPE;

typedef enum {
    WS_ENCODING_XML_BINARY_1,
    WS_ENCODING_XML_BINARY_SESSION_1,
    WS_ENCODING_XML_MTOM_UTF8,
    WS_ENCODING_XML_MTOM_UTF16BE,
    WS_ENCODING_XML_MTOM_UTF16LE,
    WS_ENCODING_XML_UTF8,
    WS_ENCODING_XML_UTF16BE,
    WS_ENCODING_XML_UTF16LE,
    WS_ENCODING_RAW
} WS_ENCODING;

typedef enum {
    WS_CHANNEL_STATE_CREATED,
    WS_CHANNEL_STATE_OPENING,
    WS_CHANNEL_STATE_ACCEPTING,
    WS_CHANNEL_STATE_OPEN,
    WS_CHANNEL_STATE_FAULTED,
    WS_CHANNEL_STATE_CLOSING,
    WS_CHANNEL_STATE_CLOSED
} WS_CHANNEL_STATE;

typedef enum {
    WS_CHANNEL_PROPERTY_MAX_BUFFERED_MESSAGE_SIZE               = 0,
    WS_CHANNEL_PROPERTY_MAX_STREAMED_MESSAGE_SIZE               = 1,
    WS_CHANNEL_PROPERTY_MAX_STREAMED_START_SIZE                 = 2,
    WS_CHANNEL_PROPERTY_MAX_STREAMED_FLUSH_SIZE                 = 3,
    WS_CHANNEL_PROPERTY_ENCODING                                = 4,
    WS_CHANNEL_PROPERTY_ENVELOPE_VERSION                        = 5,
    WS_CHANNEL_PROPERTY_ADDRESSING_VERSION                      = 6,
    WS_CHANNEL_PROPERTY_MAX_SESSION_DICTIONARY_SIZE             = 7,
    WS_CHANNEL_PROPERTY_STATE                                   = 8,
    WS_CHANNEL_PROPERTY_ASYNC_CALLBACK_MODEL                    = 9,
    WS_CHANNEL_PROPERTY_IP_VERSION                              = 10,
    WS_CHANNEL_PROPERTY_RESOLVE_TIMEOUT                         = 11,
    WS_CHANNEL_PROPERTY_CONNECT_TIMEOUT                         = 12,
    WS_CHANNEL_PROPERTY_SEND_TIMEOUT                            = 13,
    WS_CHANNEL_PROPERTY_RECEIVE_RESPONSE_TIMEOUT                = 14,
    WS_CHANNEL_PROPERTY_RECEIVE_TIMEOUT                         = 15,
    WS_CHANNEL_PROPERTY_CLOSE_TIMEOUT                           = 16,
    WS_CHANNEL_PROPERTY_ENABLE_TIMEOUTS                         = 17,
    WS_CHANNEL_PROPERTY_TRANSFER_MODE                           = 18,
    WS_CHANNEL_PROPERTY_MULTICAST_INTERFACE                     = 19,
    WS_CHANNEL_PROPERTY_MULTICAST_HOPS                          = 20,
    WS_CHANNEL_PROPERTY_REMOTE_ADDRESS                          = 21,
    WS_CHANNEL_PROPERTY_REMOTE_IP_ADDRESS                       = 22,
    WS_CHANNEL_PROPERTY_HTTP_CONNECTION_ID                      = 23,
    WS_CHANNEL_PROPERTY_CUSTOM_CHANNEL_CALLBACKS                = 24,
    WS_CHANNEL_PROPERTY_CUSTOM_CHANNEL_PARAMETERS               = 25,
    WS_CHANNEL_PROPERTY_CUSTOM_CHANNEL_INSTANCE                 = 26,
    WS_CHANNEL_PROPERTY_TRANSPORT_URL                           = 27,
    WS_CHANNEL_PROPERTY_NO_DELAY                                = 28,
    WS_CHANNEL_PROPERTY_SEND_KEEP_ALIVES                        = 29,
    WS_CHANNEL_PROPERTY_KEEP_ALIVE_TIME                         = 30,
    WS_CHANNEL_PROPERTY_KEEP_ALIVE_INTERVAL                     = 31,
    WS_CHANNEL_PROPERTY_MAX_HTTP_SERVER_CONNECTIONS             = 32,
    WS_CHANNEL_PROPERTY_IS_SESSION_SHUT_DOWN                    = 33,
    WS_CHANNEL_PROPERTY_CHANNEL_TYPE                            = 34,
    WS_CHANNEL_PROPERTY_TRIM_BUFFERED_MESSAGE_SIZE              = 35,
    WS_CHANNEL_PROPERTY_ENCODER                                 = 36,
    WS_CHANNEL_PROPERTY_DECODER                                 = 37,
    WS_CHANNEL_PROPERTY_PROTECTION_LEVEL                        = 38,
    WS_CHANNEL_PROPERTY_COOKIE_MODE                             = 39,
    WS_CHANNEL_PROPERTY_HTTP_PROXY_SETTING_MODE                 = 40,
    WS_CHANNEL_PROPERTY_CUSTOM_HTTP_PROXY                       = 41,
    WS_CHANNEL_PROPERTY_HTTP_MESSAGE_MAPPING                    = 42,
    WS_CHANNEL_PROPERTY_ENABLE_HTTP_REDIRECT                    = 43,
    WS_CHANNEL_PROPERTY_HTTP_REDIRECT_CALLBACK_CONTEXT          = 44,
    WS_CHANNEL_PROPERTY_FAULTS_AS_ERRORS                        = 45,
    WS_CHANNEL_PROPERTY_ALLOW_UNSECURED_FAULTS                  = 46,
    WS_CHANNEL_PROPERTY_HTTP_SERVER_SPN                         = 47,
    WS_CHANNEL_PROPERTY_HTTP_PROXY_SPN                          = 48,
    WS_CHANNEL_PROPERTY_MAX_HTTP_REQUEST_HEADERS_BUFFER_SIZE    = 49
} WS_CHANNEL_PROPERTY_ID;

struct _WS_CHANNEL_PROPERTY {
    WS_CHANNEL_PROPERTY_ID id;
    void *value;
    ULONG valueSize;
};

struct _WS_CHANNEL_PROPERTIES {
    WS_CHANNEL_PROPERTY *properties;
    ULONG propertyCount;
};

typedef enum {
    WS_HTTP_CHANNEL_BINDING,
    WS_TCP_CHANNEL_BINDING,
    WS_UDP_CHANNEL_BINDING,
    WS_CUSTOM_CHANNEL_BINDING,
    WS_NAMEDPIPE_CHANNEL_BINDING
} WS_CHANNEL_BINDING;

typedef enum {
    WS_SSL_TRANSPORT_SECURITY_BINDING_TYPE,
    WS_TCP_SSPI_TRANSPORT_SECURITY_BINDING_TYPE,
    WS_HTTP_HEADER_AUTH_SECURITY_BINDING_TYPE,
    WS_USERNAME_MESSAGE_SECURITY_BINDING_TYPE,
    WS_KERBEROS_APREQ_MESSAGE_SECURITY_BINDING_TYPE,
    WS_XML_TOKEN_MESSAGE_SECURITY_BINDING_TYPE,
    WS_SAML_MESSAGE_SECURITY_BINDING_TYPE,
    WS_SECURITY_CONTEXT_MESSAGE_SECURITY_BINDING_TYPE,
    WS_NAMEDPIPE_SSPI_TRANSPORT_SECURITY_BINDING_TYPE
} WS_SECURITY_BINDING_TYPE;

typedef enum {
    WS_SECURITY_BINDING_PROPERTY_REQUIRE_SSL_CLIENT_CERT                    = 1,
    WS_SECURITY_BINDING_PROPERTY_WINDOWS_INTEGRATED_AUTH_PACKAGE            = 2,
    WS_SECURITY_BINDING_PROPERTY_REQUIRE_SERVER_AUTH                        = 3,
    WS_SECURITY_BINDING_PROPERTY_ALLOW_ANONYMOUS_CLIENTS                    = 4,
    WS_SECURITY_BINDING_PROPERTY_ALLOWED_IMPERSONATION_LEVEL                = 5,
    WS_SECURITY_BINDING_PROPERTY_HTTP_HEADER_AUTH_SCHEME                    = 6,
    WS_SECURITY_BINDING_PROPERTY_HTTP_HEADER_AUTH_TARGET                    = 7,
    WS_SECURITY_BINDING_PROPERTY_HTTP_HEADER_AUTH_BASIC_REALM               = 8,
    WS_SECURITY_BINDING_PROPERTY_HTTP_HEADER_AUTH_DIGEST_REALM              = 9,
    WS_SECURITY_BINDING_PROPERTY_HTTP_HEADER_AUTH_DIGEST_DOMAIN             = 10,
    WS_SECURITY_BINDING_PROPERTY_SECURITY_CONTEXT_KEY_SIZE                  = 11,
    WS_SECURITY_BINDING_PROPERTY_SECURITY_CONTEXT_KEY_ENTROPY_MODE          = 12,
    WS_SECURITY_BINDING_PROPERTY_MESSAGE_PROPERTIES                         = 13,
    WS_SECURITY_BINDING_PROPERTY_SECURITY_CONTEXT_MAX_PENDING_CONTEXTS      = 14,
    WS_SECURITY_BINDING_PROPERTY_SECURITY_CONTEXT_MAX_ACTIVE_CONTEXTS       = 15,
    WS_SECURITY_BINDING_PROPERTY_SECURE_CONVERSATION_VERSION                = 16,
    WS_SECURITY_BINDING_PROPERTY_SECURITY_CONTEXT_SUPPORT_RENEW             = 17,
    WS_SECURITY_BINDING_PROPERTY_SECURITY_CONTEXT_RENEWAL_INTERVAL          = 18,
    WS_SECURITY_BINDING_PROPERTY_SECURITY_CONTEXT_ROLLOVER_INTERVAL         = 19,
    WS_SECURITY_BINDING_PROPERTY_CERT_FAILURES_TO_IGNORE                    = 20,
    WS_SECURITY_BINDING_PROPERTY_DISABLE_CERT_REVOCATION_CHECK              = 21,
    WS_SECURITY_BINDING_PROPERTY_DISALLOWED_SECURE_PROTOCOLS                = 22,
    WS_SECURITY_BINDING_PROPERTY_CERTIFICATE_VALIDATION_CALLBACK_CONTEXT    = 23
} WS_SECURITY_BINDING_PROPERTY_ID;

struct _WS_SECURITY_BINDING_PROPERTY {
    WS_SECURITY_BINDING_PROPERTY_ID id;
    void *value;
    ULONG valueSize;
};

struct _WS_SECURITY_BINDING_PROPERTIES {
    WS_SECURITY_BINDING_PROPERTY *properties;
    ULONG propertyCount;
};

struct _WS_SECURITY_BINDING {
    WS_SECURITY_BINDING_TYPE bindingType;
    WS_SECURITY_BINDING_PROPERTY *properties;
    ULONG propertyCount;
};

typedef enum {
    WS_SECURITY_PROPERTY_TRANSPORT_PROTECTION_LEVEL     = 1,
    WS_SECURITY_PROPERTY_ALGORITHM_SUITE                = 2,
    WS_SECURITY_PROPERTY_ALGORITHM_SUITE_NAME           = 3,
    WS_SECURITY_PROPERTY_MAX_ALLOWED_LATENCY            = 4,
    WS_SECURITY_PROPERTY_TIMESTAMP_VALIDITY_DURATION    = 5,
    WS_SECURITY_PROPERTY_MAX_ALLOWED_CLOCK_SKEW         = 6,
    WS_SECURITY_PROPERTY_TIMESTAMP_USAGE                = 7,
    WS_SECURITY_PROPERTY_SECURITY_HEADER_LAYOUT         = 8,
    WS_SECURITY_PROPERTY_SECURITY_HEADER_VERSION        = 9,
    WS_SECURITY_PROPERTY_EXTENDED_PROTECTION_POLICY     = 10,
    WS_SECURITY_PROPERTY_EXTENDED_PROTECTION_SCENARIO   = 11,
    WS_SECURITY_PROPERTY_SERVICE_IDENTITIES             = 12
} WS_SECURITY_PROPERTY_ID;

struct _WS_SECURITY_PROPERTY {
    WS_SECURITY_PROPERTY_ID id;
    void *value;
    ULONG valueSize;
};

struct _WS_SECURITY_PROPERTIES {
    WS_SECURITY_PROPERTY *properties;
    ULONG propertyCount;
};

struct _WS_SECURITY_DESCRIPTION {
    WS_SECURITY_BINDING **securityBindings;
    ULONG securityBindingCount;
    WS_SECURITY_PROPERTY *properties;
    ULONG propertyCount;
};

typedef enum {
    WS_HTTP_BINDING_TEMPLATE_TYPE,
    WS_HTTP_SSL_BINDING_TEMPLATE_TYPE,
    WS_HTTP_HEADER_AUTH_BINDING_TEMPLATE_TYPE,
    WS_HTTP_SSL_HEADER_AUTH_BINDING_TEMPLATE_TYPE,
    WS_HTTP_SSL_USERNAME_BINDING_TEMPLATE_TYPE,
    WS_HTTP_SSL_KERBEROS_APREQ_BINDING_TEMPLATE_TYPE,
    WS_TCP_BINDING_TEMPLATE_TYPE,
    WS_TCP_SSPI_BINDING_TEMPLATE_TYPE,
    WS_TCP_SSPI_USERNAME_BINDING_TEMPLATE_TYPE,
    WS_TCP_SSPI_KERBEROS_APREQ_BINDING_TEMPLATE_TYPE,
    WS_HTTP_SSL_USERNAME_SECURITY_CONTEXT_BINDING_TEMPLATE_TYPE,
    WS_HTTP_SSL_KERBEROS_APREQ_SECURITY_CONTEXT_BINDING_TEMPLATE_TYPE,
    WS_TCP_SSPI_USERNAME_SECURITY_CONTEXT_BINDING_TEMPLATE_TYPE,
    WS_TCP_SSPI_KERBEROS_APREQ_SECURITY_CONTEXT_BINDING_TEMPLATE_TYPE
} WS_BINDING_TEMPLATE_TYPE;

typedef enum {
    WS_SUBJECT_NAME_CERT_CREDENTIAL_TYPE    = 1,
    WS_THUMBPRINT_CERT_CREDENTIAL_TYPE      = 2,
    WS_CUSTOM_CERT_CREDENTIAL_TYPE          = 3
} WS_CERT_CREDENTIAL_TYPE;

struct _WS_CERT_CREDENTIAL {
    WS_CERT_CREDENTIAL_TYPE credentialType;
};

struct _WS_SSL_TRANSPORT_SECURITY_BINDING_TEMPLATE {
    WS_SECURITY_BINDING_PROPERTIES securityBindingProperties;
    WS_CERT_CREDENTIAL *localCertCredential;
};

struct _WS_HTTP_BINDING_TEMPLATE {
    WS_CHANNEL_PROPERTIES channelProperties;
};

struct _WS_HTTP_SSL_BINDING_TEMPLATE {
    WS_CHANNEL_PROPERTIES channelProperties;
    WS_SECURITY_PROPERTIES securityProperties;
    WS_SSL_TRANSPORT_SECURITY_BINDING_TEMPLATE sslTransportSecurityBinding;
};

enum {
    WS_MUST_UNDERSTAND_HEADER_ATTRIBUTE = 0x1,
    WS_RELAY_HEADER_ATTRIBUTE           = 0x2
};

typedef enum {
    WS_ADDRESSING_VERSION_0_9       = 1,
    WS_ADDRESSING_VERSION_1_0       = 2,
    WS_ADDRESSING_VERSION_TRANSPORT = 3
} WS_ADDRESSING_VERSION;

typedef enum {
    WS_ENVELOPE_VERSION_SOAP_1_1    = 1,
    WS_ENVELOPE_VERSION_SOAP_1_2    = 2,
    WS_ENVELOPE_VERSION_NONE        = 3
} WS_ENVELOPE_VERSION;

typedef enum {
    WS_MESSAGE_PROPERTY_STATE,
    WS_MESSAGE_PROPERTY_HEAP,
    WS_MESSAGE_PROPERTY_ENVELOPE_VERSION,
    WS_MESSAGE_PROPERTY_ADDRESSING_VERSION,
    WS_MESSAGE_PROPERTY_HEADER_BUFFER,
    WS_MESSAGE_PROPERTY_HEADER_POSITION,
    WS_MESSAGE_PROPERTY_BODY_READER,
    WS_MESSAGE_PROPERTY_BODY_WRITER,
    WS_MESSAGE_PROPERTY_IS_ADDRESSED,
    WS_MESSAGE_PROPERTY_HEAP_PROPERTIES,
    WS_MESSAGE_PROPERTY_XML_READER_PROPERTIES,
    WS_MESSAGE_PROPERTY_XML_WRITER_PROPERTIES,
    WS_MESSAGE_PROPERTY_IS_FAULT,
    WS_MESSAGE_PROPERTY_MAX_PROCESSED_HEADERS,
    WS_MESSAGE_PROPERTY_USERNAME,
    WS_MESSAGE_PROPERTY_ENCODED_CERT,
    WS_MESSAGE_PROPERTY_TRANSPORT_SECURITY_WINDOWS_TOKEN,
    WS_MESSAGE_PROPERTY_HTTP_HEADER_AUTH_WINDOWS_TOKEN,
    WS_MESSAGE_PROPERTY_MESSAGE_SECURITY_WINDOWS_TOKEN,
    WS_MESSAGE_PROPERTY_SAML_ASSERTION,
    WS_MESSAGE_PROPERTY_SECURITY_CONTEXT,
    WS_MESSAGE_PROPERTY_PROTECTION_LEVEL
} WS_MESSAGE_PROPERTY_ID;

struct _WS_MESSAGE_PROPERTY {
    WS_MESSAGE_PROPERTY_ID id;
    void *value;
    ULONG valueSize;
};

struct _WS_MESSAGE_PROPERTIES {
    WS_MESSAGE_PROPERTY *properties;
    ULONG propertyCount;
};

typedef enum {
    WS_MESSAGE_STATE_EMPTY          = 1,
    WS_MESSAGE_STATE_INITIALIZED    = 2,
    WS_MESSAGE_STATE_READING        = 3,
    WS_MESSAGE_STATE_WRITING        = 4,
    WS_MESSAGE_STATE_DONE           = 5
} WS_MESSAGE_STATE;

typedef enum {
    WS_BLANK_MESSAGE,
    WS_DUPLICATE_MESSAGE,
    WS_REQUEST_MESSAGE,
    WS_REPLY_MESSAGE,
    WS_FAULT_MESSAGE
} WS_MESSAGE_INITIALIZATION;

typedef enum {
    WS_ACTION_HEADER        = 1,
    WS_TO_HEADER            = 2,
    WS_MESSAGE_ID_HEADER    = 3,
    WS_RELATES_TO_HEADER    = 4,
    WS_FROM_HEADER          = 5,
    WS_REPLY_TO_HEADER      = 6,
    WS_FAULT_TO_HEADER      = 7
} WS_HEADER_TYPE;

typedef enum {
    WS_REPEATING_HEADER = 1,
    WS_SINGLETON_HEADER = 2
} WS_REPEATING_HEADER_OPTION;

typedef enum {
    WS_DNS_ENDPOINT_IDENTITY_TYPE       = 1,
    WS_UPN_ENDPOINT_IDENTITY_TYPE       = 2,
    WS_SPN_ENDPOINT_IDENTITY_TYPE       = 3,
    WS_RSA_ENDPOINT_IDENTITY_TYPE       = 4,
    WS_CERT_ENDPOINT_IDENTITY_TYPE      = 5,
    WS_UNKNOWN_ENDPOINT_IDENTITY_TYPE   = 6
} WS_ENDPOINT_IDENTITY_TYPE;

struct _WS_ENDPOINT_IDENTITY {
    WS_ENDPOINT_IDENTITY_TYPE identityType;
};

struct _WS_ENDPOINT_ADDRESS {
    WS_STRING url;
    WS_XML_BUFFER *headers;
    WS_XML_BUFFER *extensions;
    WS_ENDPOINT_IDENTITY *identity;
};

typedef struct _WS_FAULT_CODE {
    WS_XML_QNAME           value;
    struct _WS_FAULT_CODE *subCode;
} WS_FAULT_CODE;

typedef struct _WS_FAULT_REASON {
    WS_STRING text;
    WS_STRING lang;
} WS_FAULT_REASON;

typedef struct _WS_FAULT {
    WS_FAULT_CODE   *code;
    WS_FAULT_REASON *reasons;
    ULONG            reasonCount;
    WS_STRING        actor;
    WS_STRING        node;
    WS_XML_BUFFER   *detail;
} WS_FAULT;

typedef struct _WS_FAULT_DESCRIPTION {
    WS_ENVELOPE_VERSION envelopeVersion;
} WS_FAULT_DESCRIPTION;

typedef struct _WS_FAULT_DETAIL_DESCRIPTION {
    WS_XML_STRING          *action;
    WS_ELEMENT_DESCRIPTION *detailElementDescription;
} WS_FAULT_DETAIL_DESCRIPTION;

typedef enum  {
    WS_FAULT_ERROR_PROPERTY_FAULT = 0,
    WS_FAULT_ERROR_PROPERTY_ACTION = 1,
    WS_FAULT_ERROR_PROPERTY_HEADER = 2
} WS_FAULT_ERROR_PROPERTY_ID;


struct _WS_HTTP_POLICY_DESCRIPTION {
    WS_CHANNEL_PROPERTIES channelProperties;
};

struct _WS_MESSAGE_DESCRIPTION {
    WS_XML_STRING *action;
    WS_ELEMENT_DESCRIPTION *bodyElementDescription;
};

typedef enum {
    WS_PARAMETER_TYPE_NORMAL,
    WS_PARAMETER_TYPE_ARRAY,
    WS_PARAMETER_TYPE_ARRAY_COUNT,
    WS_PARAMETER_TYPE_MESSAGES
} WS_PARAMETER_TYPE;

struct _WS_PARAMETER_DESCRIPTION {
    WS_PARAMETER_TYPE parameterType;
    USHORT inputMessageIndex;
    USHORT outputMessageIndex;
};

typedef HRESULT (CALLBACK *WS_SERVICE_STUB_CALLBACK)(
    const WS_OPERATION_CONTEXT *context,
    void *frame,
    const void *callback,
    const WS_ASYNC_CONTEXT *asyncContext,
    WS_ERROR *error );

typedef enum {
    WS_NON_RPC_LITERAL_OPERATION,
    WS_RPC_LITERAL_OPERATION
} WS_OPERATION_STYLE;

struct _WS_OPERATION_DESCRIPTION {
    ULONG versionInfo;
    WS_MESSAGE_DESCRIPTION *inputMessageDescription;
    WS_MESSAGE_DESCRIPTION *outputMessageDescription;
    ULONG inputMessageOptions;
    ULONG outputMessageOptions;
    USHORT parameterCount;
    WS_PARAMETER_DESCRIPTION *parameterDescription;
    WS_SERVICE_STUB_CALLBACK stubCallback;
    WS_OPERATION_STYLE style;
};

typedef enum {
    WS_CALL_PROPERTY_CHECK_MUST_UNDERSTAND,
    WS_CALL_PROPERTY_SEND_MESSAGE_CONTEXT,
    WS_CALL_PROPERTY_RECEIVE_MESSAGE_CONTEXT,
    WS_CALL_PROPERTY_CALL_ID
} WS_CALL_PROPERTY_ID;

struct _WS_CALL_PROPERTY {
    WS_CALL_PROPERTY_ID id;
    void *value;
    ULONG valueSize;
};

typedef enum {
    WS_DATETIME_FORMAT_UTC,
    WS_DATETIME_FORMAT_LOCAL,
    WS_DATETIME_FORMAT_NONE
} WS_DATETIME_FORMAT;

struct _WS_DATETIME {
    unsigned __int64 DECLSPEC_ALIGN(8) ticks;
    WS_DATETIME_FORMAT format;
};

struct _WS_DATETIME_DESCRIPTION {
    WS_DATETIME minValue;
    WS_DATETIME maxValue;
};

struct _WS_XML_DATETIME_TEXT {
    WS_XML_TEXT text;
    WS_DATETIME value;
};

struct _WS_XML_BASE64_TEXT {
    WS_XML_TEXT text;
    BYTE *bytes;
    ULONG length;
};

typedef enum {
    WS_URL_HTTP_SCHEME_TYPE,
    WS_URL_HTTPS_SCHEME_TYPE,
    WS_URL_NETTCP_SCHEME_TYPE,
    WS_URL_SOAPUDP_SCHEME_TYPE,
    WS_URL_NETPIPE_SCHEME_TYPE
} WS_URL_SCHEME_TYPE;

enum {
    WS_URL_FLAGS_ALLOW_HOST_WILDCARDS   = 0x1,
    WS_URL_FLAGS_NO_PATH_COLLAPSE       = 0x2,
    WS_URL_FLAGS_ZERO_TERMINATE         = 0x4
};

struct _WS_URL {
    WS_URL_SCHEME_TYPE scheme;
};

struct _WS_HTTP_URL {
    WS_URL url;
    WS_STRING host;
    USHORT port;
    WS_STRING portAsString;
    WS_STRING path;
    WS_STRING query;
    WS_STRING fragment;
};

struct _WS_HTTPS_URL {
    WS_URL url;
    WS_STRING host;
    USHORT port;
    WS_STRING portAsString;
    WS_STRING path;
    WS_STRING query;
    WS_STRING fragment;
};

struct _WS_NETTCP_URL {
    WS_URL url;
    WS_STRING host;
    USHORT port;
    WS_STRING portAsString;
    WS_STRING path;
    WS_STRING query;
    WS_STRING fragment;
};

struct _WS_SOAPUDP_URL {
    WS_URL url;
    WS_STRING host;
    USHORT port;
    WS_STRING portAsString;
    WS_STRING path;
    WS_STRING query;
    WS_STRING fragment;
};

struct _WS_NETPIPE_URL {
    WS_URL url;
    WS_STRING host;
    USHORT port;
    WS_STRING portAsString;
    WS_STRING path;
    WS_STRING query;
    WS_STRING fragment;
};

typedef enum
{
    WS_IP_VERSION_4    = 1,
    WS_IP_VERSION_6    = 2,
    WS_IP_VERSION_AUTO = 3
} WS_IP_VERSION;

typedef enum
{
    WS_BUFFERED_TRANSFER_MODE        = 0x0,
    WS_STREAMED_INPUT_TRANSFER_MODE  = 0x1,
    WS_STREAMED_OUTPUT_TRANSFER_MODE = 0x2,
    WS_STREAMED_TRANSFER_MODE        = (WS_STREAMED_INPUT_TRANSFER_MODE|WS_STREAMED_OUTPUT_TRANSFER_MODE)
} WS_TRANSFER_MODE;

typedef HRESULT (CALLBACK *WS_CREATE_CHANNEL_CALLBACK)
    (WS_CHANNEL_TYPE, const void*, ULONG, void**, WS_ERROR*);

typedef void (CALLBACK *WS_FREE_CHANNEL_CALLBACK)
    (void*);

typedef HRESULT (CALLBACK *WS_RESET_CHANNEL_CALLBACK)
    (void*, WS_ERROR*);

typedef HRESULT (CALLBACK *WS_OPEN_CHANNEL_CALLBACK)
    (void*, const WS_ENDPOINT_ADDRESS*, const WS_ASYNC_CONTEXT*, WS_ERROR*);

typedef HRESULT (CALLBACK *WS_CLOSE_CHANNEL_CALLBACK)
    (void*, const WS_ASYNC_CONTEXT*, WS_ERROR*);

typedef HRESULT (CALLBACK *WS_ABORT_CHANNEL_CALLBACK)
    (void*, WS_ERROR*);

typedef HRESULT (CALLBACK *WS_GET_CHANNEL_PROPERTY_CALLBACK)
    (void*, WS_CHANNEL_PROPERTY_ID, void*, ULONG, WS_ERROR*);

typedef HRESULT (CALLBACK *WS_SET_CHANNEL_PROPERTY_CALLBACK)
    (void*, WS_CHANNEL_PROPERTY_ID, const void*, ULONG, WS_ERROR*);

typedef HRESULT (CALLBACK *WS_WRITE_MESSAGE_START_CALLBACK)
    (void*, WS_MESSAGE*, const WS_ASYNC_CONTEXT*, WS_ERROR*);

typedef HRESULT (CALLBACK *WS_WRITE_MESSAGE_END_CALLBACK)
    (void*, WS_MESSAGE*, const WS_ASYNC_CONTEXT*, WS_ERROR*);

typedef HRESULT (CALLBACK *WS_READ_MESSAGE_START_CALLBACK)
    (void*, WS_MESSAGE*, const WS_ASYNC_CONTEXT*, WS_ERROR*);

typedef HRESULT (CALLBACK *WS_READ_MESSAGE_END_CALLBACK)
    (void*, WS_MESSAGE*, const WS_ASYNC_CONTEXT*, WS_ERROR*);

typedef HRESULT (CALLBACK *WS_ABANDON_MESSAGE_CALLBACK)
    (void*, WS_MESSAGE*, WS_ERROR*);

typedef HRESULT (CALLBACK *WS_SHUTDOWN_SESSION_CHANNEL_CALLBACK)
    (void*, const WS_ASYNC_CONTEXT*, WS_ERROR*);

struct _WS_CUSTOM_CHANNEL_CALLBACKS
{
    WS_CREATE_CHANNEL_CALLBACK createChannelCallback;
    WS_FREE_CHANNEL_CALLBACK freeChannelCallback;
    WS_RESET_CHANNEL_CALLBACK resetChannelCallback;
    WS_OPEN_CHANNEL_CALLBACK openChannelCallback;
    WS_CLOSE_CHANNEL_CALLBACK closeChannelCallback;
    WS_ABORT_CHANNEL_CALLBACK abortChannelCallback;
    WS_GET_CHANNEL_PROPERTY_CALLBACK getChannelPropertyCallback;
    WS_SET_CHANNEL_PROPERTY_CALLBACK setChannelPropertyCallback;
    WS_WRITE_MESSAGE_START_CALLBACK writeMessageStartCallback;
    WS_WRITE_MESSAGE_END_CALLBACK writeMessageEndCallback;
    WS_READ_MESSAGE_START_CALLBACK readMessageStartCallback;
    WS_READ_MESSAGE_END_CALLBACK readMessageEndCallback;
    WS_ABANDON_MESSAGE_CALLBACK abandonMessageCallback;
    WS_SHUTDOWN_SESSION_CHANNEL_CALLBACK shutdownSessionChannelCallback;
};

typedef HRESULT (CALLBACK *WS_CREATE_ENCODER_CALLBACK)
    (void*, WS_WRITE_CALLBACK, void*, void**, WS_ERROR*);

typedef HRESULT (CALLBACK *WS_ENCODER_GET_CONTENT_TYPE_CALLBACK)
    (void*, const WS_STRING*, WS_STRING*, WS_STRING*, WS_ERROR*);

typedef HRESULT (CALLBACK *WS_ENCODER_START_CALLBACK)
    (void*, const WS_ASYNC_CONTEXT*, WS_ERROR*);

typedef HRESULT (CALLBACK *WS_ENCODER_ENCODE_CALLBACK)
    (void*, const WS_BYTES*, ULONG, const WS_ASYNC_CONTEXT*, WS_ERROR*);

typedef HRESULT (CALLBACK *WS_ENCODER_END_CALLBACK)
    (void*, const WS_ASYNC_CONTEXT*, WS_ERROR*);

typedef void (CALLBACK *WS_FREE_ENCODER_CALLBACK)
    (void*);

struct _WS_CHANNEL_ENCODER
{
    void *createContext;
    WS_CREATE_ENCODER_CALLBACK createEncoderCallback;
    WS_ENCODER_GET_CONTENT_TYPE_CALLBACK encoderGetContentTypeCallback;
    WS_ENCODER_START_CALLBACK encoderStartCallback;
    WS_ENCODER_ENCODE_CALLBACK encoderEncodeCallback;
    WS_ENCODER_END_CALLBACK encoderEndCallback;
    WS_FREE_ENCODER_CALLBACK freeEncoderCallback;
};

typedef HRESULT (CALLBACK *WS_CREATE_DECODER_CALLBACK)
    (void*, WS_READ_CALLBACK, void*, void**, WS_ERROR*);

typedef HRESULT (CALLBACK *WS_DECODER_GET_CONTENT_TYPE_CALLBACK)
    (void*, const WS_STRING*, const WS_STRING*, WS_STRING*, WS_ERROR*);

typedef HRESULT (CALLBACK *WS_DECODER_START_CALLBACK)
    (void*, const WS_ASYNC_CONTEXT*, WS_ERROR*);

typedef HRESULT (CALLBACK *WS_DECODER_DECODE_CALLBACK)
    (void*, void*, ULONG, ULONG*, const WS_ASYNC_CONTEXT*, WS_ERROR*);

typedef HRESULT (CALLBACK *WS_DECODER_END_CALLBACK)
    (void*, const WS_ASYNC_CONTEXT*, WS_ERROR*);

typedef void (CALLBACK *WS_FREE_DECODER_CALLBACK)
    (void*);

struct _WS_CHANNEL_DECODER
{
    void *createContext;
    WS_CREATE_DECODER_CALLBACK createDecoderCallback;
    WS_DECODER_GET_CONTENT_TYPE_CALLBACK decoderGetContentTypeCallback;
    WS_DECODER_START_CALLBACK decoderStartCallback;
    WS_DECODER_DECODE_CALLBACK decoderDecodeCallback;
    WS_DECODER_END_CALLBACK decoderEndCallback;
    WS_FREE_DECODER_CALLBACK freeDecoderCallback;
};

typedef enum
{
    WS_PROTECTION_LEVEL_NONE             = 1,
    WS_PROTECTION_LEVEL_SIGN             = 2,
    WS_PROTECTION_LEVEL_SIGN_AND_ENCRYPT = 3
} WS_PROTECTION_LEVEL;

typedef enum
{
    WS_MANUAL_COOKIE_MODE = 1,
    WS_AUTO_COOKIE_MODE   = 2
} WS_COOKIE_MODE;

typedef enum
{
    WS_HTTP_PROXY_SETTING_MODE_AUTO   = 0x1,
    WS_HTTP_PROXY_SETTING_MODE_NONE   = 0x2,
    WS_HTTP_PROXY_SETTING_MODE_CUSTOM = 0x3
} WS_HTTP_PROXY_SETTING_MODE;

struct _WS_CUSTOM_HTTP_PROXY
{
    WS_STRING servers;
    WS_STRING bypass;
};

struct _WS_HTTP_HEADER_MAPPING
{
    WS_XML_STRING headerName;
    ULONG headerMappingOptions;
};

struct _WS_HTTP_MESSAGE_MAPPING
{
    ULONG requestMappingOptions;
    ULONG responseMappingOptions;
    WS_HTTP_HEADER_MAPPING **requestHeaderMappings;
    ULONG requestHeaderMappingCount;
    WS_HTTP_HEADER_MAPPING **responseHeaderMappings;
    ULONG responseHeaderMappingCount;
};

typedef HRESULT (CALLBACK *WS_HTTP_REDIRECT_CALLBACK)
    (void*, const WS_STRING*, const WS_STRING*);

struct _WS_HTTP_REDIRECT_CALLBACK_CONTEXT
{
    WS_HTTP_REDIRECT_CALLBACK callback;
    void *state;
};

typedef enum
{
    WS_RECEIVE_REQUIRED_MESSAGE = 1,
    WS_RECEIVE_OPTIONAL_MESSAGE = 2
} WS_RECEIVE_OPTION;

typedef void (CALLBACK *WS_MESSAGE_DONE_CALLBACK)
    (void*);

typedef HRESULT (CALLBACK *WS_PROXY_MESSAGE_CALLBACK)
    (WS_MESSAGE*, WS_HEAP*, void*, WS_ERROR*);

struct _WS_PROXY_MESSAGE_CALLBACK_CONTEXT
{
    WS_PROXY_MESSAGE_CALLBACK callback;
    void *state;
};

typedef enum
{
    WS_LISTENER_STATE_CREATED,
    WS_LISTENER_STATE_OPENING,
    WS_LISTENER_STATE_OPEN,
    WS_LISTENER_STATE_FAULTED,
    WS_LISTENER_STATE_CLOSING,
    WS_LISTENER_STATE_CLOSED
} WS_LISTENER_STATE;

typedef enum
{
    WS_LISTENER_PROPERTY_LISTEN_BACKLOG,
    WS_LISTENER_PROPERTY_IP_VERSION,
    WS_LISTENER_PROPERTY_STATE,
    WS_LISTENER_PROPERTY_ASYNC_CALLBACK_MODEL,
    WS_LISTENER_PROPERTY_CHANNEL_TYPE,
    WS_LISTENER_PROPERTY_CHANNEL_BINDING,
    WS_LISTENER_PROPERTY_CONNECT_TIMEOUT,
    WS_LISTENER_PROPERTY_IS_MULTICAST,
    WS_LISTENER_PROPERTY_MULTICAST_INTERFACES,
    WS_LISTENER_PROPERTY_MULTICAST_LOOPBACK,
    WS_LISTENER_PROPERTY_CLOSE_TIMEOUT,
    WS_LISTENER_PROPERTY_TO_HEADER_MATCHING_OPTIONS,
    WS_LISTENER_PROPERTY_TRANSPORT_URL_MATCHING_OPTIONS,
    WS_LISTENER_PROPERTY_CUSTOM_LISTENER_CALLBACKS,
    WS_LISTENER_PROPERTY_CUSTOM_LISTENER_PARAMETERS,
    WS_LISTENER_PROPERTY_CUSTOM_LISTENER_INSTANCE,
    WS_LISTENER_PROPERTY_DISALLOWED_USER_AGENT
} WS_LISTENER_PROPERTY_ID;

struct _WS_LISTENER_PROPERTY
{
    WS_LISTENER_PROPERTY_ID id;
    void *value;
    ULONG valueSize;
};

struct _WS_DISALLOWED_USER_AGENT_SUBSTRINGS
{
    ULONG subStringCount;
    WS_STRING **subStrings;
};

struct _WS_LISTENER_PROPERTIES
{
    WS_LISTENER_PROPERTY *properties;
    ULONG propertyCount;
};

typedef HRESULT (CALLBACK *WS_CREATE_LISTENER_CALLBACK)
    (WS_CHANNEL_TYPE, const void*, ULONG, void**, WS_ERROR*);

typedef void (CALLBACK *WS_FREE_LISTENER_CALLBACK)
    (void*);

typedef HRESULT (CALLBACK *WS_RESET_LISTENER_CALLBACK)
    (void*, WS_ERROR*);

typedef HRESULT (CALLBACK *WS_OPEN_LISTENER_CALLBACK)
    (void*, const WS_STRING*, const WS_ASYNC_CONTEXT*, WS_ERROR*);

typedef HRESULT (CALLBACK *WS_CLOSE_LISTENER_CALLBACK)
    (void*, const WS_ASYNC_CONTEXT*, WS_ERROR*);

typedef HRESULT (CALLBACK *WS_ABORT_LISTENER_CALLBACK)
    (void*, WS_ERROR*);

typedef HRESULT (CALLBACK *WS_GET_LISTENER_PROPERTY_CALLBACK)
    (void*, WS_LISTENER_PROPERTY_ID, void*, ULONG, WS_ERROR*);

typedef HRESULT (CALLBACK *WS_SET_LISTENER_PROPERTY_CALLBACK)
    (void*, WS_LISTENER_PROPERTY_ID, const void*, ULONG, WS_ERROR*);

typedef HRESULT (CALLBACK *WS_CREATE_CHANNEL_FOR_LISTENER_CALLBACK)
    (void*, const void*, ULONG, void**, WS_ERROR*);

typedef HRESULT (CALLBACK *WS_ACCEPT_CHANNEL_CALLBACK)
    (void*, void*, const WS_ASYNC_CONTEXT*, WS_ERROR*);

struct _WS_CUSTOM_LISTENER_CALLBACKS
{
    WS_CREATE_LISTENER_CALLBACK createListenerCallback;
    WS_FREE_LISTENER_CALLBACK freeListenerCallback;
    WS_RESET_LISTENER_CALLBACK resetListenerCallback;
    WS_OPEN_LISTENER_CALLBACK openListenerCallback;
    WS_CLOSE_LISTENER_CALLBACK closeListenerCallback;
    WS_ABORT_LISTENER_CALLBACK abortListenerCallback;
    WS_GET_LISTENER_PROPERTY_CALLBACK getListenerPropertyCallback;
    WS_SET_LISTENER_PROPERTY_CALLBACK setListenerPropertyCallback;
    WS_CREATE_CHANNEL_FOR_LISTENER_CALLBACK createChannelForListenerCallback;
    WS_ACCEPT_CHANNEL_CALLBACK acceptChannelCallback;
};

enum
{
    WS_MATCH_URL_DNS_HOST                 = 0x1,
    WS_MATCH_URL_DNS_FULLY_QUALIFIED_HOST = 0x2,
    WS_MATCH_URL_NETBIOS_HOST             = 0x4,
    WS_MATCH_URL_LOCAL_HOST               = 0x8,
    WS_MATCH_URL_HOST_ADDRESSES           = 0x10,
    WS_MATCH_URL_THIS_HOST                = (WS_MATCH_URL_DNS_HOST |
                                             WS_MATCH_URL_DNS_FULLY_QUALIFIED_HOST |
                                             WS_MATCH_URL_NETBIOS_HOST |
                                             WS_MATCH_URL_LOCAL_HOST |
                                             WS_MATCH_URL_HOST_ADDRESSES),
    WS_MATCH_URL_PORT                     = 0x20,
    WS_MATCH_URL_EXACT_PATH               = 0x40,
    WS_MATCH_URL_PREFIX_PATH              = 0x80,
    WS_MATCH_URL_NO_QUERY                 = 0x100
};

HRESULT WINAPI WsAbortServiceProxy(WS_SERVICE_PROXY*, WS_ERROR*);
HRESULT WINAPI WsAcceptChannel(WS_LISTENER*, WS_CHANNEL*, const WS_ASYNC_CONTEXT*, WS_ERROR*);
HRESULT WINAPI WsAddCustomHeader(WS_MESSAGE*, const WS_ELEMENT_DESCRIPTION*, WS_WRITE_OPTION,
                                 const void*, ULONG, ULONG, WS_ERROR*);
HRESULT WINAPI WsAddErrorString(WS_ERROR*, const WS_STRING*);
HRESULT WINAPI WsAddMappedHeader(WS_MESSAGE*, const WS_XML_STRING*, WS_TYPE, WS_WRITE_OPTION,
                                 const void*, ULONG, WS_ERROR*);
HRESULT WINAPI WsAddressMessage(WS_MESSAGE*, const WS_ENDPOINT_ADDRESS*, WS_ERROR*);
HRESULT WINAPI WsAlloc(WS_HEAP*, SIZE_T, void**, WS_ERROR*);
HRESULT WINAPI WsCall(WS_SERVICE_PROXY*, const WS_OPERATION_DESCRIPTION*, const void**,
                      WS_HEAP*, const WS_CALL_PROPERTY*, const ULONG, const WS_ASYNC_CONTEXT*,
                      WS_ERROR*);
HRESULT WINAPI WsCloseChannel(WS_CHANNEL*, const WS_ASYNC_CONTEXT*, WS_ERROR*);
HRESULT WINAPI WsCloseListener(WS_LISTENER*, const WS_ASYNC_CONTEXT*, WS_ERROR*);
HRESULT WINAPI WsCloseServiceProxy(WS_SERVICE_PROXY*, const WS_ASYNC_CONTEXT*, WS_ERROR*);
HRESULT WINAPI WsCombineUrl(const WS_STRING*, const WS_STRING*, ULONG, WS_HEAP*, WS_STRING*, WS_ERROR*);
HRESULT WINAPI WsCopyNode(WS_XML_WRITER*, WS_XML_READER*, WS_ERROR*);
HRESULT WINAPI WsCreateChannel(WS_CHANNEL_TYPE, WS_CHANNEL_BINDING, const WS_CHANNEL_PROPERTY*,
                               ULONG, const WS_SECURITY_DESCRIPTION*, WS_CHANNEL**, WS_ERROR*);
HRESULT WINAPI WsCreateChannelForListener(WS_LISTENER*, const WS_CHANNEL_PROPERTY*, ULONG, WS_CHANNEL**,
                                          WS_ERROR*);
HRESULT WINAPI WsCreateError(const WS_ERROR_PROPERTY*, ULONG, WS_ERROR**);
HRESULT WINAPI WsCreateHeap(SIZE_T, SIZE_T, const WS_HEAP_PROPERTY*, ULONG, WS_HEAP**, WS_ERROR*);
HRESULT WINAPI WsCreateListener(WS_CHANNEL_TYPE, WS_CHANNEL_BINDING, const WS_LISTENER_PROPERTY*,
                                ULONG, const WS_SECURITY_DESCRIPTION*, WS_LISTENER**, WS_ERROR*);
HRESULT WINAPI WsCreateMessage(WS_ENVELOPE_VERSION, WS_ADDRESSING_VERSION, const WS_MESSAGE_PROPERTY*,
                               ULONG, WS_MESSAGE**, WS_ERROR*);
HRESULT WINAPI WsCreateMessageForChannel(WS_CHANNEL*, const WS_MESSAGE_PROPERTY*, ULONG, WS_MESSAGE**,
                                         WS_ERROR*);
HRESULT WINAPI WsCreateReader(const WS_XML_READER_PROPERTY*, ULONG, WS_XML_READER**, WS_ERROR*);
HRESULT WINAPI WsCreateServiceProxy(const WS_CHANNEL_TYPE, const WS_CHANNEL_BINDING,
                                    const WS_SECURITY_DESCRIPTION*, const WS_PROXY_PROPERTY*,
                                    const ULONG, const WS_CHANNEL_PROPERTY*, const ULONG,
                                    WS_SERVICE_PROXY**, WS_ERROR*);
HRESULT WINAPI WsCreateServiceProxyFromTemplate(WS_CHANNEL_TYPE, const WS_PROXY_PROPERTY*,
                                                const ULONG, WS_BINDING_TEMPLATE_TYPE,
                                                void*, ULONG, const void*, ULONG,
                                                WS_SERVICE_PROXY**, WS_ERROR*);
HRESULT WINAPI WsCreateWriter(const WS_XML_WRITER_PROPERTY*, ULONG, WS_XML_WRITER**, WS_ERROR*);
HRESULT WINAPI WsCreateXmlBuffer(WS_HEAP*, const WS_XML_BUFFER_PROPERTY*, ULONG, WS_XML_BUFFER**,
                                 WS_ERROR*);
HRESULT WINAPI WsDateTimeToFileTime(const WS_DATETIME*, FILETIME*, WS_ERROR*);
HRESULT WINAPI WsDecodeUrl(const WS_STRING*, ULONG, WS_HEAP*, WS_URL**, WS_ERROR*);
HRESULT WINAPI WsEncodeUrl(const WS_URL*, ULONG, WS_HEAP*, WS_STRING*, WS_ERROR*);
HRESULT WINAPI WsFileTimeToDateTime(const FILETIME*, WS_DATETIME*, WS_ERROR*);
HRESULT WINAPI WsFillBody(WS_MESSAGE*, ULONG, const WS_ASYNC_CONTEXT*, WS_ERROR*);
HRESULT WINAPI WsFillReader(WS_XML_READER*, ULONG, const WS_ASYNC_CONTEXT*, WS_ERROR*);
HRESULT WINAPI WsFindAttribute(WS_XML_READER*, const WS_XML_STRING*, const WS_XML_STRING*, WINBOOL,
                               ULONG*, WS_ERROR*);
HRESULT WINAPI WsFlushBody(WS_MESSAGE*, ULONG, const WS_ASYNC_CONTEXT*, WS_ERROR*);
HRESULT WINAPI WsFlushWriter(WS_XML_WRITER*, ULONG, const WS_ASYNC_CONTEXT*, WS_ERROR*);
void WINAPI WsFreeChannel(WS_CHANNEL*);
void WINAPI WsFreeError(WS_ERROR*);
void WINAPI WsFreeHeap(WS_HEAP*);
void WINAPI WsFreeListener(WS_LISTENER*);
void WINAPI WsFreeMessage(WS_MESSAGE*);
void WINAPI WsFreeReader(WS_XML_READER*);
void WINAPI WsFreeServiceProxy(WS_SERVICE_PROXY*);
void WINAPI WsFreeWriter(WS_XML_WRITER*);
HRESULT WINAPI WsGetChannelProperty(WS_CHANNEL*, WS_CHANNEL_PROPERTY_ID, void*, ULONG, WS_ERROR*);
HRESULT WINAPI WsGetCustomHeader(WS_MESSAGE*, const WS_ELEMENT_DESCRIPTION*, WS_REPEATING_HEADER_OPTION,
                                 ULONG, WS_READ_OPTION, WS_HEAP*, void*, ULONG, ULONG*, WS_ERROR*);
HRESULT WINAPI WsGetDictionary(WS_ENCODING, WS_XML_DICTIONARY**, WS_ERROR*);
HRESULT WINAPI WsGetErrorProperty(WS_ERROR*, WS_ERROR_PROPERTY_ID, void*, ULONG);
HRESULT WINAPI WsGetErrorString(WS_ERROR*, ULONG, WS_STRING*);
HRESULT WINAPI WsGetFaultErrorDetail(WS_ERROR*, const WS_FAULT_DETAIL_DESCRIPTION*, WS_READ_OPTION,
                                     WS_HEAP*, void*, ULONG);
HRESULT WINAPI WsGetFaultErrorProperty(WS_ERROR*, WS_FAULT_ERROR_PROPERTY_ID, void*, ULONG);
HRESULT WINAPI WsGetHeader(WS_MESSAGE*, WS_HEADER_TYPE, WS_TYPE, WS_READ_OPTION, WS_HEAP*, void*,
                           ULONG, WS_ERROR*);
HRESULT WINAPI WsGetHeapProperty(WS_HEAP*, WS_HEAP_PROPERTY_ID, void*, ULONG, WS_ERROR*);
HRESULT WINAPI WsGetListenerProperty(WS_LISTENER*, WS_LISTENER_PROPERTY_ID, void*, ULONG, WS_ERROR*);
HRESULT WINAPI WsGetMappedHeader(WS_MESSAGE*, const WS_XML_STRING*, WS_REPEATING_HEADER_OPTION,
                                 ULONG, WS_TYPE, WS_READ_OPTION, WS_HEAP*, void*, ULONG, WS_ERROR*);
HRESULT WINAPI WsGetMessageProperty(WS_MESSAGE*, WS_MESSAGE_PROPERTY_ID, void*, ULONG, WS_ERROR*);
HRESULT WINAPI WsGetNamespaceFromPrefix(WS_XML_READER*, const WS_XML_STRING*, WINBOOL,
                                        const WS_XML_STRING**, WS_ERROR*);
HRESULT WINAPI WsGetPrefixFromNamespace(WS_XML_WRITER*, const WS_XML_STRING*, WINBOOL,
                                        const WS_XML_STRING**, WS_ERROR*);
HRESULT WINAPI WsGetReaderNode(WS_XML_READER*, const WS_XML_NODE**, WS_ERROR*);
HRESULT WINAPI WsGetReaderPosition(WS_XML_READER*, WS_XML_NODE_POSITION*, WS_ERROR*);
HRESULT WINAPI WsGetReaderProperty(WS_XML_READER*, WS_XML_READER_PROPERTY_ID, void*, ULONG, WS_ERROR*);
HRESULT WINAPI WsGetServiceProxyProperty(WS_SERVICE_PROXY*, const WS_PROXY_PROPERTY_ID, void*,
                                         ULONG, WS_ERROR*);
HRESULT WINAPI WsGetWriterPosition(WS_XML_WRITER*, WS_XML_NODE_POSITION*, WS_ERROR*);
HRESULT WINAPI WsGetWriterProperty(WS_XML_WRITER*, WS_XML_WRITER_PROPERTY_ID, void*, ULONG, WS_ERROR*);
HRESULT WINAPI WsGetXmlAttribute(WS_XML_READER*, const WS_XML_STRING*, WS_HEAP*, WCHAR**,
                                 ULONG*, WS_ERROR*);
HRESULT WINAPI WsInitializeMessage(WS_MESSAGE*, WS_MESSAGE_INITIALIZATION, WS_MESSAGE*, WS_ERROR*);
HRESULT WINAPI WsMoveReader(WS_XML_READER*, WS_MOVE_TO, WINBOOL*, WS_ERROR*);
HRESULT WINAPI WsMoveWriter(WS_XML_WRITER*, WS_MOVE_TO, WINBOOL*, WS_ERROR*);
HRESULT WINAPI WsOpenChannel(WS_CHANNEL*, const WS_ENDPOINT_ADDRESS*, const WS_ASYNC_CONTEXT*, WS_ERROR*);
HRESULT WINAPI WsOpenListener(WS_LISTENER*, const WS_STRING*, const WS_ASYNC_CONTEXT*, WS_ERROR*);
HRESULT WINAPI WsOpenServiceProxy(WS_SERVICE_PROXY*, const WS_ENDPOINT_ADDRESS*, const WS_ASYNC_CONTEXT*,
                                  WS_ERROR*);
HRESULT WINAPI WsReadAttribute(WS_XML_READER*, const WS_ATTRIBUTE_DESCRIPTION*, WS_READ_OPTION,
                               WS_HEAP*, void*, ULONG, WS_ERROR*);
HRESULT WINAPI WsReadBody(WS_MESSAGE*, const WS_ELEMENT_DESCRIPTION*, WS_READ_OPTION, WS_HEAP*, void*,
                          ULONG, WS_ERROR*);
HRESULT WINAPI WsReadBytes(WS_XML_READER*, void*, ULONG, ULONG*, WS_ERROR*);
HRESULT WINAPI WsReadChars(WS_XML_READER*, WCHAR*, ULONG, ULONG*, WS_ERROR*);
HRESULT WINAPI WsReadCharsUtf8(WS_XML_READER*, BYTE*, ULONG, ULONG*, WS_ERROR*);
HRESULT WINAPI WsReadElement(WS_XML_READER*, const WS_ELEMENT_DESCRIPTION*, WS_READ_OPTION,
                             WS_HEAP*, void*, ULONG, WS_ERROR*);
HRESULT WINAPI WsReadEndAttribute(WS_XML_READER*, WS_ERROR*);
HRESULT WINAPI WsReadEndElement(WS_XML_READER*, WS_ERROR*);
HRESULT WINAPI WsReadEnvelopeEnd(WS_MESSAGE*, WS_ERROR*);
HRESULT WINAPI WsReadEnvelopeStart(WS_MESSAGE*, WS_XML_READER*, WS_MESSAGE_DONE_CALLBACK, void*,
                                   WS_ERROR*);
HRESULT WINAPI WsReadMessageEnd(WS_CHANNEL*, WS_MESSAGE*, const WS_ASYNC_CONTEXT*, WS_ERROR*);
HRESULT WINAPI WsReadMessageStart(WS_CHANNEL*, WS_MESSAGE*, const WS_ASYNC_CONTEXT*, WS_ERROR*);
HRESULT WINAPI WsReadNode(WS_XML_READER*, WS_ERROR*);
HRESULT WINAPI WsReadQualifiedName(WS_XML_READER*, WS_HEAP*, WS_XML_STRING*, WS_XML_STRING*,
                                   WS_XML_STRING*, WS_ERROR*);
HRESULT WINAPI WsReadStartAttribute(WS_XML_READER*, ULONG, WS_ERROR*);
HRESULT WINAPI WsReadStartElement(WS_XML_READER*, WS_ERROR*);
HRESULT WINAPI WsReadToStartElement(WS_XML_READER*, const WS_XML_STRING*, const WS_XML_STRING*,
                                    WINBOOL*, WS_ERROR*);
HRESULT WINAPI WsReadType(WS_XML_READER*, WS_TYPE_MAPPING, WS_TYPE, const void*, WS_READ_OPTION,
                          WS_HEAP*, void*, ULONG, WS_ERROR*);
HRESULT WINAPI WsReadValue(WS_XML_READER*, WS_VALUE_TYPE, void*, ULONG, WS_ERROR*);
HRESULT WINAPI WsReadXmlBuffer(WS_XML_READER*, WS_HEAP*, WS_XML_BUFFER**, WS_ERROR*);
HRESULT WINAPI WsReceiveMessage(WS_CHANNEL*, WS_MESSAGE*, const WS_MESSAGE_DESCRIPTION**, ULONG,
                                WS_RECEIVE_OPTION, WS_READ_OPTION, WS_HEAP*, void*, ULONG, ULONG*,
                                const WS_ASYNC_CONTEXT*, WS_ERROR*);
HRESULT WINAPI WsRemoveCustomHeader(WS_MESSAGE*, const WS_XML_STRING*, const WS_XML_STRING*,
                                    WS_ERROR*);
HRESULT WINAPI WsRemoveHeader(WS_MESSAGE*, WS_HEADER_TYPE, WS_ERROR*);
HRESULT WINAPI WsRemoveMappedHeader(WS_MESSAGE*, const WS_XML_STRING*, WS_ERROR*);
HRESULT WINAPI WsRemoveNode(const WS_XML_NODE_POSITION*, WS_ERROR*);
HRESULT WINAPI WsRequestReply(WS_CHANNEL*, WS_MESSAGE*, const WS_MESSAGE_DESCRIPTION*, WS_WRITE_OPTION,
                              const void*, ULONG, WS_MESSAGE*, const WS_MESSAGE_DESCRIPTION*, WS_READ_OPTION,
                              WS_HEAP*, void*, ULONG, const WS_ASYNC_CONTEXT*, WS_ERROR*);
HRESULT WINAPI WsResetChannel(WS_CHANNEL*, WS_ERROR*);
HRESULT WINAPI WsResetMessage(WS_MESSAGE*, WS_ERROR*);
HRESULT WINAPI WsResetError(WS_ERROR*);
HRESULT WINAPI WsResetHeap(WS_HEAP*, WS_ERROR*);
HRESULT WINAPI WsResetListener(WS_LISTENER*, WS_ERROR*);
HRESULT WINAPI WsResetMessage(WS_MESSAGE*, WS_ERROR*);
HRESULT WINAPI WsResetServiceProxy(WS_SERVICE_PROXY*, WS_ERROR*);
HRESULT WINAPI WsRequestReply(WS_CHANNEL*, WS_MESSAGE*, const WS_MESSAGE_DESCRIPTION*, WS_WRITE_OPTION,
                              const void*, ULONG, WS_MESSAGE*, const WS_MESSAGE_DESCRIPTION*,
                              WS_READ_OPTION, WS_HEAP*, void*, ULONG, const WS_ASYNC_CONTEXT*, WS_ERROR*);
HRESULT WINAPI WsSendMessage(WS_CHANNEL*, WS_MESSAGE*, const WS_MESSAGE_DESCRIPTION*, WS_WRITE_OPTION,
                             const void*, ULONG, const WS_ASYNC_CONTEXT*, WS_ERROR*);
HRESULT WINAPI WsSendReplyMessage(WS_CHANNEL*, WS_MESSAGE*, const WS_MESSAGE_DESCRIPTION*,
                                  WS_WRITE_OPTION, const void*, ULONG, WS_MESSAGE*,
                                  const WS_ASYNC_CONTEXT*, WS_ERROR*);
HRESULT WINAPI WsSetChannelProperty(WS_CHANNEL*, WS_CHANNEL_PROPERTY_ID, const void*, ULONG, WS_ERROR*);
HRESULT WINAPI WsSetErrorProperty(WS_ERROR*, WS_ERROR_PROPERTY_ID, const void*, ULONG);
HRESULT WINAPI WsSetFaultErrorProperty(WS_ERROR*, WS_FAULT_ERROR_PROPERTY_ID, const void*, ULONG);
HRESULT WINAPI WsSetHeader(WS_MESSAGE*, WS_HEADER_TYPE, WS_TYPE, WS_WRITE_OPTION, const void*, ULONG,
                           WS_ERROR*);
HRESULT WINAPI WsSetInput(WS_XML_READER*, const WS_XML_READER_ENCODING*, const WS_XML_READER_INPUT*,
                          const WS_XML_READER_PROPERTY*, ULONG, WS_ERROR*);
HRESULT WINAPI WsSetInputToBuffer(WS_XML_READER*, WS_XML_BUFFER*, const WS_XML_READER_PROPERTY*,
                                  ULONG, WS_ERROR*);
HRESULT WINAPI WsSetListenerProperty(WS_LISTENER*, WS_LISTENER_PROPERTY_ID, const void*, ULONG, WS_ERROR*);
HRESULT WINAPI WsSetMessageProperty(WS_MESSAGE*, WS_MESSAGE_PROPERTY_ID, const void*, ULONG, WS_ERROR*);
HRESULT WINAPI WsSetOutput(WS_XML_WRITER*, const WS_XML_WRITER_ENCODING*, const WS_XML_WRITER_OUTPUT*,
                           const WS_XML_WRITER_PROPERTY*, ULONG, WS_ERROR*);
HRESULT WINAPI WsSetOutputToBuffer(WS_XML_WRITER*, WS_XML_BUFFER*, const WS_XML_WRITER_PROPERTY*,
                                   ULONG, WS_ERROR*);
HRESULT WINAPI WsSetReaderPosition(WS_XML_READER*, const WS_XML_NODE_POSITION*, WS_ERROR*);
HRESULT WINAPI WsSetWriterPosition(WS_XML_WRITER*, const WS_XML_NODE_POSITION*, WS_ERROR*);
HRESULT WINAPI WsShutdownSessionChannel(WS_CHANNEL*, const WS_ASYNC_CONTEXT*, WS_ERROR*);
HRESULT WINAPI WsSkipNode(WS_XML_READER*, WS_ERROR*);
HRESULT WINAPI WsWriteArray(WS_XML_WRITER*, const WS_XML_STRING*, const WS_XML_STRING*, WS_VALUE_TYPE,
                            const void*, ULONG, ULONG, ULONG, WS_ERROR*);
HRESULT WINAPI WsWriteAttribute(WS_XML_WRITER*, const WS_ATTRIBUTE_DESCRIPTION*, WS_WRITE_OPTION,
                                const void*, ULONG, WS_ERROR*);
HRESULT WINAPI WsWriteBody(WS_MESSAGE*, const WS_ELEMENT_DESCRIPTION*, WS_WRITE_OPTION, const void*,
                           ULONG, WS_ERROR*);
HRESULT WINAPI WsWriteBytes(WS_XML_WRITER*, const void*, ULONG, WS_ERROR*);
HRESULT WINAPI WsWriteChars(WS_XML_WRITER*, const WCHAR*, ULONG, WS_ERROR*);
HRESULT WINAPI WsWriteCharsUtf8(WS_XML_WRITER*, const BYTE*, ULONG, WS_ERROR*);
HRESULT WINAPI WsWriteElement(WS_XML_WRITER*, const WS_ELEMENT_DESCRIPTION*, WS_WRITE_OPTION,
                              const void*, ULONG, WS_ERROR*);
HRESULT WINAPI WsWriteEndAttribute(WS_XML_WRITER*, WS_ERROR*);
HRESULT WINAPI WsWriteEndCData(WS_XML_WRITER*, WS_ERROR*);
HRESULT WINAPI WsWriteEndElement(WS_XML_WRITER*, WS_ERROR*);
HRESULT WINAPI WsWriteEndStartElement(WS_XML_WRITER*, WS_ERROR*);
HRESULT WINAPI WsWriteEnvelopeEnd(WS_MESSAGE*, WS_ERROR*);
HRESULT WINAPI WsWriteEnvelopeStart(WS_MESSAGE*, WS_XML_WRITER*, WS_MESSAGE_DONE_CALLBACK, void*, WS_ERROR*);
HRESULT WINAPI WsWriteMessageStart(WS_CHANNEL*, WS_MESSAGE*, const WS_ASYNC_CONTEXT*, WS_ERROR*);
HRESULT WINAPI WsWriteMessageEnd(WS_CHANNEL*, WS_MESSAGE*, const WS_ASYNC_CONTEXT*, WS_ERROR*);
HRESULT WINAPI WsWriteNode(WS_XML_WRITER*, const WS_XML_NODE*, WS_ERROR*);
HRESULT WINAPI WsWriteQualifiedName(WS_XML_WRITER*, const WS_XML_STRING*, const WS_XML_STRING*,
                                    const WS_XML_STRING*, WS_ERROR*);
HRESULT WINAPI WsWriteStartAttribute(WS_XML_WRITER*, const WS_XML_STRING*, const WS_XML_STRING*,
                                     const WS_XML_STRING*, WINBOOL, WS_ERROR*);
HRESULT WINAPI WsWriteStartCData(WS_XML_WRITER*, WS_ERROR*);
HRESULT WINAPI WsWriteStartElement(WS_XML_WRITER*, const WS_XML_STRING*, const WS_XML_STRING*,
                                   const WS_XML_STRING*, WS_ERROR*);
HRESULT WINAPI WsWriteText(WS_XML_WRITER*, const WS_XML_TEXT*, WS_ERROR*);
HRESULT WINAPI WsWriteType(WS_XML_WRITER*, WS_TYPE_MAPPING, WS_TYPE, const void*, WS_WRITE_OPTION,
                           const void*, ULONG, WS_ERROR*);
HRESULT WINAPI WsWriteValue(WS_XML_WRITER*, WS_VALUE_TYPE, const void*, ULONG, WS_ERROR*);
HRESULT WINAPI WsWriteXmlBuffer(WS_XML_WRITER*, WS_XML_BUFFER*, WS_ERROR*);
HRESULT WINAPI WsWriteXmlBufferToBytes(WS_XML_WRITER*, WS_XML_BUFFER*, const WS_XML_WRITER_ENCODING*,
                                       const WS_XML_WRITER_PROPERTY*, ULONG, WS_HEAP*, void**,
                                       ULONG*, WS_ERROR*);
HRESULT WINAPI WsWriteXmlnsAttribute(WS_XML_WRITER*, const WS_XML_STRING*, const WS_XML_STRING*,
                                     WINBOOL, WS_ERROR*);
HRESULT WINAPI WsXmlStringEquals(const WS_XML_STRING*, const WS_XML_STRING*, WS_ERROR*);

#ifndef FACILITY_WEBSERVICES
#define FACILITY_WEBSERVICES 61
#define WS_S_ASYNC                          0x003d0000
#define WS_S_END                            0x003d0001
#define WS_E_INVALID_FORMAT                 0x803d0000
#define WS_E_OBJECT_FAULTED                 0x803d0001
#define WS_E_NUMERIC_OVERFLOW               0x803d0002
#define WS_E_INVALID_OPERATION              0x803d0003
#define WS_E_OPERATION_ABORTED              0x803d0004
#define WS_E_ENDPOINT_ACCESS_DENIED         0x803d0005
#define WS_E_OPERATION_TIMED_OUT            0x803d0006
#define WS_E_OPERATION_ABANDONED            0x803d0007
#define WS_E_QUOTA_EXCEEDED                 0x803d0008
#define WS_E_NO_TRANSLATION_AVAILABLE       0x803d0009
#define WS_E_SECURITY_VERIFICATION_FAILURE  0x803d000a
#define WS_E_ADDRESS_IN_USE                 0x803d000b
#define WS_E_ADDRESS_NOT_AVAILABLE          0x803d000c
#define WS_E_ENDPOINT_NOT_FOUND             0x803d000d
#define WS_E_ENDPOINT_NOT_AVAILABLE         0x803d000e
#define WS_E_ENDPOINT_FAILURE               0x803d000f
#define WS_E_ENDPOINT_UNREACHABLE           0x803d0010
#define WS_E_ENDPOINT_ACTION_NOT_SUPPORTED  0x803d0011
#define WS_E_ENDPOINT_TOO_BUSY              0x803d0012
#define WS_E_ENDPOINT_FAULT_RECEIVED        0x803d0013
#define WS_E_ENDPOINT_DISCONNECTED          0x803d0014
#define WS_E_PROXY_FAILURE                  0x803d0015
#define WS_E_PROXY_ACCESS_DENIED            0x803d0016
#define WS_E_NOT_SUPPORTED                  0x803d0017
#define WS_E_PROXY_REQUIRES_BASIC_AUTH      0x803d0018
#define WS_E_PROXY_REQUIRES_DIGEST_AUTH     0x803d0019
#define WS_E_PROXY_REQUIRES_NTLM_AUTH       0x803d001a
#define WS_E_PROXY_REQUIRES_NEGOTIATE_AUTH  0x803d001b
#define WS_E_SERVER_REQUIRES_BASIC_AUTH     0x803d001c
#define WS_E_SERVER_REQUIRES_DIGEST_AUTH    0x803d001d
#define WS_E_SERVER_REQUIRES_NTLM_AUTH      0x803d001e
#define WS_E_SERVER_REQUIRES_NEGOTIATE_AUTH 0x803d001f
#define WS_E_INVALID_ENDPOINT_URL           0x803d0020
#define WS_E_OTHER                          0x803d0021
#define WS_E_SECURITY_TOKEN_EXPIRED         0x803d0022
#define WS_E_SECURITY_SYSTEM_FAILURE        0x803d0023
#endif

#define WS_STRING_VALUE(s) { sizeof(s) / sizeof((s)[0]) - 1, (WCHAR *)(s) }
#define WS_XML_STRING_VALUE(s) { sizeof(s) - 1, (BYTE *)(s) }

#ifdef __cplusplus
}
#endif  /* __cplusplus */

#endif /* __WINE_WEBSERVICES_H */
