.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_name_constraints_get_permitted" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_name_constraints_get_permitted \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_name_constraints_get_permitted(gnutls_x509_name_constraints_t " nc ", unsigned " idx ", unsigned * " type ", gnutls_datum_t * " name ");"
.SH ARGUMENTS
.IP "gnutls_x509_name_constraints_t nc" 12
the extracted name constraints
.IP "unsigned idx" 12
the index of the constraint
.IP "unsigned * type" 12
the type of the constraint (of type gnutls_x509_subject_alt_name_t)
.IP "gnutls_datum_t * name" 12
the name in the constraint (of the specific type)
.SH "DESCRIPTION"
This function will return an intermediate type containing
the name constraints of the provided CA certificate. That
structure can be used in combination with \fBgnutls_x509_name_constraints_check()\fP
to verify whether a server's name is in accordance with the constraints.

The name should be treated as constant and valid for the lifetime of  \fInc\fP .
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, \fBGNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE\fP
if the extension is not present, otherwise a negative error value.
.SH "SINCE"
3.3.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
