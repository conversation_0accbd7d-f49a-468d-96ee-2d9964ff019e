" Vim syntax file
" Program:      CMake - Cross-Platform Makefile Generator
" Version:      cmake version 3.27.20230713-gdc88dd5
" Language:     CMake
" Author:       <PERSON> <<EMAIL>>,
"               <PERSON> <<EMAIL>>,
"               <PERSON> <<EMAIL>>
" Maintainer:   <PERSON> <<EMAIL>>
" Former Maintainer: <PERSON><PERSON><PERSON> <<EMAIL>>
" Last Change:  2023 Jul 13
"
" License:      The CMake license applies to this file. See
"               https://cmake.org/licensing
"               This implies that distribution with Vim is allowed

if exists("b:current_syntax")
  finish
endif
let s:keepcpo= &cpo
set cpo&vim

syn region cmakeBracketArgument start="\[\z(=*\)\[" end="\]\z1\]" contains=cmakeTodo,@Spell

syn region cmakeComment start="#\(\[=*\[\)\@!" end="$" contains=cmakeTodo,@Spell
syn region cmakeBracketComment start="#\[\z(=*\)\[" end="\]\z1\]" contains=cmakeTodo,@Spell

syn match cmakeEscaped /\(\\\\\|\\"\|\\n\|\\t\)/ contained
syn region cmakeRegistry start="\[" end="]" contained oneline contains=cmakeTodo,cmakeEscaped

syn region cmakeGeneratorExpression start="$<" end=">" contained oneline contains=cmakeVariableValue,cmakeProperty,cmakeGeneratorExpressions,cmakeTodo

syn region cmakeString start='"' end='"' contained contains=cmakeTodo,cmakeVariableValue,cmakeEscaped,@Spell

syn region cmakeVariableValue start="${" end="}" contained oneline contains=cmakeVariable,cmakeTodo,cmakeVariableValue

syn region cmakeEnvironment start="$ENV{" end="}" contained oneline contains=cmakeTodo

syn region cmakeArguments start="(" end=")" contains=ALLBUT,cmakeGeneratorExpressions,cmakeCommand,cmakeCommandConditional,cmakeCommandRepeat,cmakeCommandDeprecated,cmakeCommandManuallyAdded,cmakeArguments,cmakeTodo,@Spell

syn case match

syn keyword cmakeProperty contained
            \ ABSTRACT
            \ ADDITIONAL_CLEAN_FILES
            \ ADDITIONAL_MAKE_CLEAN_FILES
            \ ADVANCED
            \ AIX_EXPORT_ALL_SYMBOLS
            \ ALIASED_TARGET
            \ ALIAS_GLOBAL
            \ ALLOW_DUPLICATE_CUSTOM_TARGETS
            \ ANDROID_ANT_ADDITIONAL_OPTIONS
            \ ANDROID_API
            \ ANDROID_API_MIN
            \ ANDROID_ARCH
            \ ANDROID_ASSETS_DIRECTORIES
            \ ANDROID_GUI
            \ ANDROID_JAR_DEPENDENCIES
            \ ANDROID_JAR_DIRECTORIES
            \ ANDROID_JAVA_SOURCE_DIR
            \ ANDROID_NATIVE_LIB_DEPENDENCIES
            \ ANDROID_NATIVE_LIB_DIRECTORIES
            \ ANDROID_PROCESS_MAX
            \ ANDROID_PROGUARD
            \ ANDROID_PROGUARD_CONFIG_PATH
            \ ANDROID_SECURE_PROPS_PATH
            \ ANDROID_SKIP_ANT_STEP
            \ ANDROID_STL_TYPE
            \ ARCHIVE_OUTPUT_DIRECTORY
            \ ARCHIVE_OUTPUT_NAME
            \ ATTACHED_FILES
            \ ATTACHED_FILES_ON_FAIL
            \ AUTOGEN_BUILD_DIR
            \ AUTOGEN_COMMAND_LINE_LENGTH_MAX
            \ AUTOGEN_ORIGIN_DEPENDS
            \ AUTOGEN_PARALLEL
            \ AUTOGEN_SOURCE_GROUP
            \ AUTOGEN_USE_SYSTEM_INCLUDE
            \ AUTOGEN_TARGETS_FOLDER
            \ AUTOGEN_TARGET_DEPENDS
            \ AUTOGEN_USE_SYSTEM_INCLUDE
            \ AUTOGEN_BETTER_GRAPH_MULTI_CONFIG
            \ AUTOMOC
            \ AUTOMOC_COMPILER_PREDEFINES
            \ AUTOMOC_DEPEND_FILTERS
            \ AUTOMOC_EXECUTABLE
            \ AUTOMOC_MACRO_NAMES
            \ AUTOMOC_MOC_OPTIONS
            \ AUTOMOC_PATH_PREFIX
            \ AUTOMOC_SOURCE_GROUP
            \ AUTOMOC_TARGETS_FOLDER
            \ AUTORCC
            \ AUTORCC_EXECUTABLE
            \ AUTORCC_OPTIONS
            \ AUTORCC_SOURCE_GROUP
            \ AUTOUIC
            \ AUTOUIC_EXECUTABLE
            \ AUTOUIC_OPTIONS
            \ AUTOUIC_SEARCH_PATHS
            \ AUTOUIC_SOURCE_GROUP
            \ BINARY_DIR
            \ BUILDSYSTEM_TARGETS
            \ BUILD_RPATH
            \ BUILD_RPATH_USE_ORIGIN
            \ BUILD_WITH_INSTALL_NAME_DIR
            \ BUILD_WITH_INSTALL_RPATH
            \ BUNDLE
            \ BUNDLE_EXTENSION
            \ CACHE_VARIABLES
            \ CLEAN_NO_CUSTOM
            \ CMAKE_CONFIGURE_DEPENDS
            \ CMAKE_CUDA_KNOWN_FEATURES
            \ CMAKE_CXX_KNOWN_FEATURES
            \ CMAKE_C_KNOWN_FEATURES
            \ CMAKE_ROLE
            \ COMMON_LANGUAGE_RUNTIME
            \ COMPATIBLE_INTERFACE_BOOL
            \ COMPATIBLE_INTERFACE_NUMBER_MAX
            \ COMPATIBLE_INTERFACE_NUMBER_MIN
            \ COMPATIBLE_INTERFACE_STRING
            \ COMPILE_DEFINITIONS
            \ COMPILE_FEATURES
            \ COMPILE_FLAGS
            \ COMPILE_OPTIONS
            \ COMPILE_PDB_NAME
            \ COMPILE_PDB_OUTPUT_DIRECTORY
            \ COMPILE_WARNING_AS_ERROR
            \ COST
            \ CPACK_DESKTOP_SHORTCUTS
            \ CPACK_NEVER_OVERWRITE
            \ CPACK_PERMANENT
            \ CPACK_STARTUP_SHORTCUTS
            \ CPACK_START_MENU_SHORTCUTS
            \ CPACK_WIX_ACL
            \ CROSSCOMPILING_EMULATOR
            \ CUDA_ARCHITECTURES
            \ CUDA_CUBIN_COMPILATION
            \ CUDA_EXTENSIONS
            \ CUDA_FATBIN_COMPILATION
            \ CUDA_OPTIX_COMPILATION
            \ CUDA_PTX_COMPILATION
            \ CUDA_RESOLVE_DEVICE_SYMBOLS
            \ CUDA_RUNTIME_LIBRARY
            \ CUDA_SEPARABLE_COMPILATION
            \ CUDA_STANDARD
            \ CUDA_STANDARD_REQUIRED
            \ CXX_EXTENSIONS
            \ CXX_MODULE_DIRS
            \ CXX_MODULE_SET
            \ CXX_MODULE_SETS
            \ CXX_SCAN_FOR_MODULES
            \ CXX_STANDARD
            \ CXX_STANDARD_REQUIRED
            \ C_EXTENSIONS
            \ C_STANDARD
            \ C_STANDARD_REQUIRED
            \ DEBUG_CONFIGURATIONS
            \ DEBUG_POSTFIX
            \ DEBUGGER_WORKING_DIRECTORY
            \ DEFINE_SYMBOL
            \ DEFINITIONS
            \ DEPENDS
            \ DEPLOYMENT_ADDITIONAL_FILES
            \ DEPLOYMENT_REMOTE_DIRECTORY
            \ DEPRECATION
            \ DISABLED
            \ DISABLED_FEATURES
            \ DISABLE_PRECOMPILE_HEADERS
            \ DLL_NAME_WITH_SOVERSION
            \ DOTNET_SDK
            \ DOTNET_TARGET_FRAMEWORK
            \ DOTNET_TARGET_FRAMEWORK_VERSION
            \ ECLIPSE_EXTRA_CPROJECT_CONTENTS
            \ ECLIPSE_EXTRA_NATURES
            \ ENABLED_FEATURES
            \ ENABLED_LANGUAGES
            \ ENABLE_EXPORTS
            \ ENVIRONMENT
            \ ENVIRONMENT_MODIFICATION
            \ EXCLUDE_FROM_ALL
            \ EXCLUDE_FROM_DEFAULT_BUILD
            \ EXPORT_COMPILE_COMMANDS
            \ EXPORT_NAME
            \ EXPORT_NO_SYSTEM
            \ EXPORT_PROPERTIES
            \ EXTERNAL_OBJECT
            \ EchoString
            \ FAIL_REGULAR_EXPRESSION
            \ FIND_LIBRARY_USE_LIB32_PATHS
            \ FIND_LIBRARY_USE_LIB64_PATHS
            \ FIND_LIBRARY_USE_LIBX32_PATHS
            \ FIND_LIBRARY_USE_OPENBSD_VERSIONING
            \ FIXTURES_CLEANUP
            \ FIXTURES_REQUIRED
            \ FIXTURES_SETUP
            \ FOLDER
            \ FRAMEWORK
            \ FRAMEWORK_VERSION
            \ Fortran_BUILDING_INTRINSIC_MODULES
            \ Fortran_FORMAT
            \ Fortran_MODULE_DIRECTORY
            \ Fortran_PREPROCESS
            \ GENERATED
            \ GENERATOR_FILE_NAME
            \ GENERATOR_IS_MULTI_CONFIG
            \ GHS_INTEGRITY_APP
            \ GHS_NO_SOURCE_GROUP_FILE
            \ GLOBAL_DEPENDS_DEBUG_MODE
            \ GLOBAL_DEPENDS_NO_CYCLES
            \ GNUtoMS
            \ HAS_CXX
            \ HEADER_DIRS
            \ HEADER_FILE_ONLY
            \ HEADER_SET
            \ HEADER_SETS
            \ HELPSTRING
            \ HIP_ARCHITECTURES
            \ HIP_EXTENSIONS
            \ HIP_STANDARD
            \ HIP_STANDARD_REQUIRED
            \ IMPLICIT_DEPENDS_INCLUDE_TRANSFORM
            \ IMPORTED
            \ IMPORTED_COMMON_LANGUAGE_RUNTIME
            \ IMPORTED_CONFIGURATIONS
            \ IMPORTED_GLOBAL
            \ IMPORTED_IMPLIB
            \ IMPORTED_LIBNAME
            \ IMPORTED_LINK_DEPENDENT_LIBRARIES
            \ IMPORTED_LINK_INTERFACE_LANGUAGES
            \ IMPORTED_LINK_INTERFACE_LIBRARIES
            \ IMPORTED_LINK_INTERFACE_MULTIPLICITY
            \ IMPORTED_LOCATION
            \ IMPORTED_NO_SONAME
            \ IMPORTED_NO_SYSTEM
            \ IMPORTED_OBJECTS
            \ IMPORTED_SONAME
            \ IMPORTED_TARGETS
            \ IMPORT_PREFIX
            \ IMPORT_SUFFIX
            \ INCLUDE_DIRECTORIES
            \ INCLUDE_REGULAR_EXPRESSION
            \ INSTALL_NAME_DIR
            \ INSTALL_REMOVE_ENVIRONMENT_RPATH
            \ INSTALL_RPATH
            \ INSTALL_RPATH_USE_LINK_PATH
            \ INTERFACE_AUTOMOC_MACRO_NAMES
            \ INTERFACE_AUTOUIC_OPTIONS
            \ INTERFACE_AUTOMOC_MACRO_NAMES
            \ INTERFACE_COMPILE_DEFINITIONS
            \ INTERFACE_COMPILE_FEATURES
            \ INTERFACE_COMPILE_OPTIONS
            \ INTERFACE_CXX_MODULE_SETS
            \ INTERFACE_HEADER_SETS
            \ INTERFACE_HEADER_SETS_TO_VERIFY
            \ INTERFACE_INCLUDE_DIRECTORIES
            \ INTERFACE_LINK_DEPENDS
            \ INTERFACE_LINK_DIRECTORIES
            \ INTERFACE_LINK_LIBRARIES
            \ INTERFACE_LINK_LIBRARIES_DIRECT
            \ INTERFACE_LINK_LIBRARIES_DIRECT_EXCLUDE
            \ INTERFACE_LINK_OPTIONS
            \ INTERFACE_POSITION_INDEPENDENT_CODE
            \ INTERFACE_PRECOMPILE_HEADERS
            \ INTERFACE_SOURCES
            \ INTERFACE_SYSTEM_INCLUDE_DIRECTORIES
            \ INTERPROCEDURAL_OPTIMIZATION
            \ IN_TRY_COMPILE
            \ IOS_INSTALL_COMBINED
            \ ISPC_HEADER_DIRECTORY
            \ ISPC_HEADER_SUFFIX
            \ ISPC_INSTRUCTION_SETS
            \ JOB_POOLS
            \ JOB_POOL_COMPILE
            \ JOB_POOL_LINK
            \ JOB_POOL_PRECOMPILE_HEADER
            \ KEEP_EXTENSION
            \ LABELS
            \ LANGUAGE
            \ LIBRARY_OUTPUT_DIRECTORY
            \ LIBRARY_OUTPUT_NAME
            \ LINKER_LANGUAGE
            \ LINK_DEPENDS
            \ LINK_DEPENDS_NO_SHARED
            \ LINK_DIRECTORIES
            \ LINK_FLAGS
            \ LINK_INTERFACE_LIBRARIES
            \ LINK_INTERFACE_MULTIPLICITY
            \ LINK_LIBRARIES
            \ LINK_LIBRARIES_ONLY_TARGETS
            \ LINK_LIBRARY_OVERRIDE
            \ LINK_OPTIONS
            \ LINK_SEARCH_END_STATIC
            \ LINK_SEARCH_START_STATIC
            \ LINK_WHAT_YOU_USE
            \ LISTFILE_STACK
            \ LOCATION
            \ MACHO_COMPATIBILITY_VERSION
            \ MACHO_CURRENT_VERSION
            \ MACOSX_BUNDLE
            \ MACOSX_BUNDLE_INFO_PLIST
            \ MACOSX_FRAMEWORK_INFO_PLIST
            \ MACOSX_PACKAGE_LOCATION
            \ MACOSX_RPATH
            \ MACROS
            \ MANUALLY_ADDED_DEPENDENCIES
            \ MEASUREMENT
            \ MODIFIED
            \ MSVC_DEBUG_INFORMATION_FORMAT
            \ MSVC_RUNTIME_CHECKS
            \ MSVC_RUNTIME_LIBRARY
            \ NAME
            \ NO_SONAME
            \ NO_SYSTEM_FROM_IMPORTED
            \ OBJCXX_EXTENSIONS
            \ OBJCXX_STANDARD
            \ OBJCXX_STANDARD_REQUIRED
            \ OBJC_EXTENSIONS
            \ OBJC_STANDARD
            \ OBJC_STANDARD_REQUIRED
            \ OBJECT_DEPENDS
            \ OBJECT_OUTPUTS
            \ OPTIMIZE_DEPENDENCIES
            \ OSX_ARCHITECTURES
            \ OUTPUT_NAME
            \ PACKAGES_FOUND
            \ PACKAGES_NOT_FOUND
            \ PARENT_DIRECTORY
            \ PASS_REGULAR_EXPRESSION
            \ PCH_INSTANTIATE_TEMPLATES
            \ PCH_WARN_INVALID
            \ PDB_NAME
            \ PDB_OUTPUT_DIRECTORY
            \ POSITION_INDEPENDENT_CODE
            \ POST_INSTALL_SCRIPT
            \ PRECOMPILE_HEADERS
            \ PRECOMPILE_HEADERS_REUSE_FROM
            \ PREDEFINED_TARGETS_FOLDER
            \ PREFIX
            \ PRE_INSTALL_SCRIPT
            \ PRIVATE_HEADER
            \ PROCESSORS
            \ PROCESSOR_AFFINITY
            \ PROJECT_LABEL
            \ PUBLIC_HEADER
            \ REPORT_UNDEFINED_PROPERTIES
            \ REQUIRED_FILES
            \ RESOURCE
            \ RESOURCE_GROUPS
            \ RESOURCE_LOCK
            \ RULE_LAUNCH_COMPILE
            \ RULE_LAUNCH_CUSTOM
            \ RULE_LAUNCH_LINK
            \ RULE_MESSAGES
            \ RUNTIME_OUTPUT_DIRECTORY
            \ RUNTIME_OUTPUT_NAME
            \ RUN_SERIAL
            \ SKIP_AUTOGEN
            \ SKIP_AUTOMOC
            \ SKIP_AUTORCC
            \ SKIP_AUTOUIC
            \ SKIP_BUILD_RPATH
            \ SKIP_LINTING
            \ SKIP_PRECOMPILE_HEADERS
            \ SKIP_REGULAR_EXPRESSION
            \ SKIP_RETURN_CODE
            \ SKIP_UNITY_BUILD_INCLUSION
            \ SOURCES
            \ SOURCE_DIR
            \ SOVERSION
            \ STATIC_LIBRARY_FLAGS
            \ STATIC_LIBRARY_OPTIONS
            \ STRINGS
            \ SUBDIRECTORIES
            \ SUFFIX
            \ SYMBOLIC
            \ SYSTEM
            \ Swift_DEPENDENCIES_FILE
            \ Swift_DIAGNOSTICS_FILE
            \ Swift_LANGUAGE_VERSION
            \ Swift_MODULE_DIRECTORY
            \ Swift_MODULE_NAME
            \ Swift_COMPILATION_MODE
            \ TARGET_ARCHIVES_MAY_BE_SHARED_LIBS
            \ TARGET_MESSAGES
            \ TARGET_SUPPORTS_SHARED_LIBS
            \ TESTS
            \ TEST_INCLUDE_FILE
            \ TEST_INCLUDE_FILES
            \ TIMEOUT
            \ TIMEOUT_AFTER_MATCH
            \ TIMEOUT_SIGNAL_GRACE_PERIOD
            \ TIMEOUT_SIGNAL_NAME
            \ TYPE
            \ UNITY_BUILD
            \ UNITY_BUILD_BATCH_SIZE
            \ UNITY_BUILD_CODE_AFTER_INCLUDE
            \ UNITY_BUILD_CODE_BEFORE_INCLUDE
            \ UNITY_BUILD_MODE
            \ UNITY_BUILD_UNIQUE_ID
            \ UNITY_GROUP
            \ USE_FOLDERS
            \ VALUE
            \ VARIABLES
            \ VERIFY_INTERFACE_HEADER_SETS
            \ VERSION
            \ VISIBILITY_INLINES_HIDDEN
            \ VS_CONFIGURATION_TYPE
            \ VS_COPY_TO_OUT_DIR
            \ VS_DEBUGGER_COMMAND
            \ VS_DEBUGGER_COMMAND_ARGUMENTS
            \ VS_DEBUGGER_ENVIRONMENT
            \ VS_DEBUGGER_WORKING_DIRECTORY
            \ VS_DEPLOYMENT_CONTENT
            \ VS_DEPLOYMENT_LOCATION
            \ VS_DESKTOP_EXTENSIONS_VERSION
            \ VS_DOTNET_DOCUMENTATION_FILE
            \ VS_DOTNET_REFERENCES
            \ VS_DOTNET_REFERENCES_COPY_LOCAL
            \ VS_DOTNET_STARTUP_OBJECT
            \ VS_DOTNET_TARGET_FRAMEWORK_VERSION
            \ VS_DPI_AWARE
            \ VS_FRAMEWORK_REFERENCES
            \ VS_GLOBAL_KEYWORD
            \ VS_GLOBAL_PROJECT_TYPES
            \ VS_GLOBAL_ROOTNAMESPACE
            \ VS_INCLUDE_IN_VSIX
            \ VS_IOT_EXTENSIONS_VERSION
            \ VS_IOT_STARTUP_TASK
            \ VS_JUST_MY_CODE_DEBUGGING
            \ VS_KEYWORD
            \ VS_MOBILE_EXTENSIONS_VERSION
            \ VS_NO_COMPILE_BATCHING
            \ VS_NO_SOLUTION_DEPLOY
            \ VS_PACKAGE_REFERENCES
            \ VS_PLATFORM_TOOLSET
            \ VS_PROJECT_IMPORT
            \ VS_RESOURCE_GENERATOR
            \ VS_SCC_AUXPATH
            \ VS_SCC_LOCALPATH
            \ VS_SCC_PROJECTNAME
            \ VS_SCC_PROVIDER
            \ VS_SDK_REFERENCES
            \ VS_SETTINGS
            \ VS_SHADER_DISABLE_OPTIMIZATIONS
            \ VS_SHADER_ENABLE_DEBUG
            \ VS_SHADER_ENTRYPOINT
            \ VS_SHADER_FLAGS
            \ VS_SHADER_MODEL
            \ VS_SHADER_OBJECT_FILE_NAME
            \ VS_SHADER_OUTPUT_HEADER_FILE
            \ VS_SHADER_TYPE
            \ VS_SHADER_VARIABLE_NAME
            \ VS_SOLUTION_DEPLOY
            \ VS_STARTUP_PROJECT
            \ VS_TOOL_OVERRIDE
            \ VS_USER_PROPS
            \ VS_FILTER_PROPS
            \ VS_WINDOWS_TARGET_PLATFORM_MIN_VERSION
            \ VS_WINRT_COMPONENT
            \ VS_WINRT_EXTENSIONS
            \ VS_WINRT_REFERENCES
            \ VS_XAML_TYPE
            \ WATCOM_RUNTIME_LIBRARY
            \ WILL_FAIL
            \ WIN32_EXECUTABLE
            \ WINDOWS_EXPORT_ALL_SYMBOLS
            \ WORKING_DIRECTORY
            \ WRAP_EXCLUDE
            \ XCODE_EMBED_FRAMEWORKS_CODE_SIGN_ON_COPY
            \ XCODE_EMBED_FRAMEWORKS_REMOVE_HEADERS_ON_COPY
            \ XCODE_EMIT_EFFECTIVE_PLATFORM_NAME
            \ XCODE_EXPLICIT_FILE_TYPE
            \ XCODE_FILE_ATTRIBUTES
            \ XCODE_GENERATE_SCHEME
            \ XCODE_LAST_KNOWN_FILE_TYPE
            \ XCODE_LINK_BUILD_PHASE_MODE
            \ XCODE_PRODUCT_TYPE
            \ XCODE_SCHEME_ADDRESS_SANITIZER
            \ XCODE_SCHEME_ADDRESS_SANITIZER_USE_AFTER_RETURN
            \ XCODE_SCHEME_ARGUMENTS
            \ XCODE_SCHEME_DEBUG_AS_ROOT
            \ XCODE_SCHEME_DEBUG_DOCUMENT_VERSIONING
            \ XCODE_SCHEME_ENABLE_GPU_FRAME_CAPTURE_MODE
            \ XCODE_SCHEME_DISABLE_MAIN_THREAD_CHECKER
            \ XCODE_SCHEME_DYNAMIC_LIBRARY_LOADS
            \ XCODE_SCHEME_DYNAMIC_LINKER_API_USAGE
            \ XCODE_SCHEME_ENABLE_GPU_API_VALIDATION
            \ XCODE_SCHEME_ENABLE_GPU_SHADER_VALIDATION
            \ XCODE_SCHEME_ENVIRONMENT
            \ XCODE_SCHEME_EXECUTABLE
            \ XCODE_SCHEME_GUARD_MALLOC
            \ XCODE_SCHEME_LAUNCH_MODE
            \ XCODE_SCHEME_LLDB_INIT_FILE
            \ XCODE_SCHEME_MAIN_THREAD_CHECKER_STOP
            \ XCODE_SCHEME_MALLOC_GUARD_EDGES
            \ XCODE_SCHEME_MALLOC_SCRIBBLE
            \ XCODE_SCHEME_MALLOC_STACK
            \ XCODE_SCHEME_THREAD_SANITIZER
            \ XCODE_SCHEME_THREAD_SANITIZER_STOP
            \ XCODE_SCHEME_UNDEFINED_BEHAVIOUR_SANITIZER
            \ XCODE_SCHEME_UNDEFINED_BEHAVIOUR_SANITIZER_STOP
            \ XCODE_SCHEME_ENABLE_GPU_API_VALIDATION
            \ XCODE_SCHEME_ENABLE_GPU_SHADER_VALIDATION
            \ XCODE_SCHEME_LAUNCH_CONFIGURATION
            \ XCODE_SCHEME_TEST_CONFIGURATION
            \ XCODE_SCHEME_WORKING_DIRECTORY
            \ XCODE_SCHEME_ZOMBIE_OBJECTS
            \ XCODE_XCCONFIG
            \ XCTEST

syn keyword cmakeVariable contained
            \ ANDROID
            \ APPLE
            \ BORLAND
            \ BSD
            \ BUILD_SHARED_LIBS
            \ CACHE
            \ CMAKE_ABSOLUTE_DESTINATION_FILES
            \ CMAKE_ADD_CUSTOM_COMMAND_DEPENDS_EXPLICIT_ONLY
            \ CMAKE_ADSP_ROOT
            \ CMAKE_AIX_EXPORT_ALL_SYMBOLS
            \ CMAKE_ANDROID_ANT_ADDITIONAL_OPTIONS
            \ CMAKE_ANDROID_API
            \ CMAKE_ANDROID_API_MIN
            \ CMAKE_ANDROID_ARCH
            \ CMAKE_ANDROID_ARCH_ABI
            \ CMAKE_ANDROID_ARM_MODE
            \ CMAKE_ANDROID_ARM_NEON
            \ CMAKE_ANDROID_ASSETS_DIRECTORIES
            \ CMAKE_ANDROID_EXCEPTIONS
            \ CMAKE_ANDROID_GUI
            \ CMAKE_ANDROID_JAR_DEPENDENCIES
            \ CMAKE_ANDROID_JAR_DIRECTORIES
            \ CMAKE_ANDROID_JAVA_SOURCE_DIR
            \ CMAKE_ANDROID_NATIVE_LIB_DEPENDENCIES
            \ CMAKE_ANDROID_NATIVE_LIB_DIRECTORIES
            \ CMAKE_ANDROID_NDK
            \ CMAKE_ANDROID_NDK_DEPRECATED_HEADERS
            \ CMAKE_ANDROID_NDK_TOOLCHAIN_HOST_TAG
            \ CMAKE_ANDROID_NDK_TOOLCHAIN_VERSION
            \ CMAKE_ANDROID_NDK_VERSION
            \ CMAKE_ANDROID_PROCESS_MAX
            \ CMAKE_ANDROID_PROGUARD
            \ CMAKE_ANDROID_PROGUARD_CONFIG_PATH
            \ CMAKE_ANDROID_RTTI
            \ CMAKE_ANDROID_SECURE_PROPS_PATH
            \ CMAKE_ANDROID_SKIP_ANT_STEP
            \ CMAKE_ANDROID_STANDALONE_TOOLCHAIN
            \ CMAKE_ANDROID_STL_TYPE
            \ CMAKE_APPBUNDLE_PATH
            \ CMAKE_APPLE_SILICON_PROCESSOR
            \ CMAKE_AR
            \ CMAKE_ARCHIVE_OUTPUT_DIRECTORY
            \ CMAKE_ARGC
            \ CMAKE_ARGV0
            \ CMAKE_ASM
            \ CMAKE_ASM_ANDROID_TOOLCHAIN_MACHINE
            \ CMAKE_ASM_ANDROID_TOOLCHAIN_PREFIX
            \ CMAKE_ASM_ANDROID_TOOLCHAIN_SUFFIX
            \ CMAKE_ASM_ARCHIVE_APPEND
            \ CMAKE_ASM_ARCHIVE_CREATE
            \ CMAKE_ASM_ARCHIVE_FINISH
            \ CMAKE_ASM_BYTE_ORDER
            \ CMAKE_ASM_CLANG_TIDY
            \ CMAKE_ASM_CLANG_TIDY_EXPORT_FIXES_DIR
            \ CMAKE_ASM_COMPILER
            \ CMAKE_ASM_COMPILER_ABI
            \ CMAKE_ASM_COMPILER_AR
            \ CMAKE_ASM_COMPILER_ARCHITECTURE_ID
            \ CMAKE_ASM_COMPILER_EXTERNAL_TOOLCHAIN
            \ CMAKE_ASM_COMPILER_FRONTEND_VARIANT
            \ CMAKE_ASM_COMPILER_ID
            \ CMAKE_ASM_COMPILER_LAUNCHER
            \ CMAKE_ASM_COMPILER_LOADED
            \ CMAKE_ASM_COMPILER_PREDEFINES_COMMAND
            \ CMAKE_ASM_COMPILER_RANLIB
            \ CMAKE_ASM_COMPILER_TARGET
            \ CMAKE_ASM_COMPILER_VERSION
            \ CMAKE_ASM_COMPILER_VERSION_INTERNAL
            \ CMAKE_ASM_COMPILE_OBJECT
            \ CMAKE_ASM_CPPCHECK
            \ CMAKE_ASM_CPPLINT
            \ CMAKE_ASM_CREATE_SHARED_LIBRARY
            \ CMAKE_ASM_CREATE_SHARED_MODULE
            \ CMAKE_ASM_CREATE_STATIC_LIBRARY
            \ CMAKE_ASM_EXTENSIONS
            \ CMAKE_ASM_EXTENSIONS_DEFAULT
            \ CMAKE_ASM_FLAGS
            \ CMAKE_ASM_FLAGS_DEBUG
            \ CMAKE_ASM_FLAGS_DEBUG_INIT
            \ CMAKE_ASM_FLAGS_INIT
            \ CMAKE_ASM_FLAGS_MINSIZEREL
            \ CMAKE_ASM_FLAGS_MINSIZEREL_INIT
            \ CMAKE_ASM_FLAGS_RELEASE
            \ CMAKE_ASM_FLAGS_RELEASE_INIT
            \ CMAKE_ASM_FLAGS_RELWITHDEBINFO
            \ CMAKE_ASM_FLAGS_RELWITHDEBINFO_INIT
            \ CMAKE_ASM_IGNORE_EXTENSIONS
            \ CMAKE_ASM_IMPLICIT_INCLUDE_DIRECTORIES
            \ CMAKE_ASM_IMPLICIT_LINK_DIRECTORIES
            \ CMAKE_ASM_IMPLICIT_LINK_FRAMEWORK_DIRECTORIES
            \ CMAKE_ASM_IMPLICIT_LINK_LIBRARIES
            \ CMAKE_ASM_INCLUDE_WHAT_YOU_USE
            \ CMAKE_ASM_INIT
            \ CMAKE_ASM_LIBRARY_ARCHITECTURE
            \ CMAKE_ASM_LINKER_LAUNCHER
            \ CMAKE_ASM_LINKER_PREFERENCE
            \ CMAKE_ASM_LINKER_PREFERENCE_PROPAGATES
            \ CMAKE_ASM_LINKER_WRAPPER_FLAG
            \ CMAKE_ASM_LINKER_WRAPPER_FLAG_SEP
            \ CMAKE_ASM_LINK_EXECUTABLE
            \ CMAKE_ASM_LINK_LIBRARY_FILE_FLAG
            \ CMAKE_ASM_LINK_LIBRARY_FLAG
            \ CMAKE_ASM_LINK_LIBRARY_SUFFIX
            \ CMAKE_ASM_LINK_WHAT_YOU_USE_FLAG
            \ CMAKE_ASM_MASM
            \ CMAKE_ASM_MASM_ANDROID_TOOLCHAIN_MACHINE
            \ CMAKE_ASM_MASM_ANDROID_TOOLCHAIN_PREFIX
            \ CMAKE_ASM_MASM_ANDROID_TOOLCHAIN_SUFFIX
            \ CMAKE_ASM_MASM_ARCHIVE_APPEND
            \ CMAKE_ASM_MASM_ARCHIVE_CREATE
            \ CMAKE_ASM_MASM_ARCHIVE_FINISH
            \ CMAKE_ASM_MASM_BYTE_ORDER
            \ CMAKE_ASM_MASM_CLANG_TIDY
            \ CMAKE_ASM_MASM_CLANG_TIDY_EXPORT_FIXES_DIR
            \ CMAKE_ASM_MASM_COMPILER
            \ CMAKE_ASM_MASM_COMPILER_ABI
            \ CMAKE_ASM_MASM_COMPILER_AR
            \ CMAKE_ASM_MASM_COMPILER_ARCHITECTURE_ID
            \ CMAKE_ASM_MASM_COMPILER_EXTERNAL_TOOLCHAIN
            \ CMAKE_ASM_MASM_COMPILER_FRONTEND_VARIANT
            \ CMAKE_ASM_MASM_COMPILER_ID
            \ CMAKE_ASM_MASM_COMPILER_LAUNCHER
            \ CMAKE_ASM_MASM_COMPILER_LOADED
            \ CMAKE_ASM_MASM_COMPILER_PREDEFINES_COMMAND
            \ CMAKE_ASM_MASM_COMPILER_RANLIB
            \ CMAKE_ASM_MASM_COMPILER_TARGET
            \ CMAKE_ASM_MASM_COMPILER_VERSION
            \ CMAKE_ASM_MASM_COMPILER_VERSION_INTERNAL
            \ CMAKE_ASM_MASM_COMPILE_OBJECT
            \ CMAKE_ASM_MASM_CPPCHECK
            \ CMAKE_ASM_MASM_CPPLINT
            \ CMAKE_ASM_MASM_CREATE_SHARED_LIBRARY
            \ CMAKE_ASM_MASM_CREATE_SHARED_MODULE
            \ CMAKE_ASM_MASM_CREATE_STATIC_LIBRARY
            \ CMAKE_ASM_MASM_EXTENSIONS
            \ CMAKE_ASM_MASM_EXTENSIONS_DEFAULT
            \ CMAKE_ASM_MASM_FLAGS
            \ CMAKE_ASM_MASM_FLAGS_DEBUG
            \ CMAKE_ASM_MASM_FLAGS_DEBUG_INIT
            \ CMAKE_ASM_MASM_FLAGS_INIT
            \ CMAKE_ASM_MASM_FLAGS_MINSIZEREL
            \ CMAKE_ASM_MASM_FLAGS_MINSIZEREL_INIT
            \ CMAKE_ASM_MASM_FLAGS_RELEASE
            \ CMAKE_ASM_MASM_FLAGS_RELEASE_INIT
            \ CMAKE_ASM_MASM_FLAGS_RELWITHDEBINFO
            \ CMAKE_ASM_MASM_FLAGS_RELWITHDEBINFO_INIT
            \ CMAKE_ASM_MASM_IGNORE_EXTENSIONS
            \ CMAKE_ASM_MASM_IMPLICIT_INCLUDE_DIRECTORIES
            \ CMAKE_ASM_MASM_IMPLICIT_LINK_DIRECTORIES
            \ CMAKE_ASM_MASM_IMPLICIT_LINK_FRAMEWORK_DIRECTORIES
            \ CMAKE_ASM_MASM_IMPLICIT_LINK_LIBRARIES
            \ CMAKE_ASM_MASM_INCLUDE_WHAT_YOU_USE
            \ CMAKE_ASM_MASM_INIT
            \ CMAKE_ASM_MASM_LIBRARY_ARCHITECTURE
            \ CMAKE_ASM_MASM_LINKER_LAUNCHER
            \ CMAKE_ASM_MASM_LINKER_PREFERENCE
            \ CMAKE_ASM_MASM_LINKER_PREFERENCE_PROPAGATES
            \ CMAKE_ASM_MASM_LINKER_WRAPPER_FLAG
            \ CMAKE_ASM_MASM_LINKER_WRAPPER_FLAG_SEP
            \ CMAKE_ASM_MASM_LINK_EXECUTABLE
            \ CMAKE_ASM_MASM_LINK_LIBRARY_FILE_FLAG
            \ CMAKE_ASM_MASM_LINK_LIBRARY_FLAG
            \ CMAKE_ASM_MASM_LINK_LIBRARY_SUFFIX
            \ CMAKE_ASM_MASM_LINK_WHAT_YOU_USE_FLAG
            \ CMAKE_ASM_MASM_OUTPUT_EXTENSION
            \ CMAKE_ASM_MASM_PLATFORM_ID
            \ CMAKE_ASM_MASM_SIMULATE_ID
            \ CMAKE_ASM_MASM_SIMULATE_VERSION
            \ CMAKE_ASM_MASM_SIZEOF_DATA_PTR
            \ CMAKE_ASM_MASM_SOURCE_FILE_EXTENSIONS
            \ CMAKE_ASM_MASM_STANDARD
            \ CMAKE_ASM_MASM_STANDARD_DEFAULT
            \ CMAKE_ASM_MASM_STANDARD_INCLUDE_DIRECTORIES
            \ CMAKE_ASM_MASM_STANDARD_LIBRARIES
            \ CMAKE_ASM_MASM_STANDARD_REQUIRED
            \ CMAKE_ASM_MASM_SUPPORTED
            \ CMAKE_ASM_MASM_VISIBILITY_PRESET
            \ CMAKE_ASM_NASM
            \ CMAKE_ASM_NASM_ANDROID_TOOLCHAIN_MACHINE
            \ CMAKE_ASM_NASM_ANDROID_TOOLCHAIN_PREFIX
            \ CMAKE_ASM_NASM_ANDROID_TOOLCHAIN_SUFFIX
            \ CMAKE_ASM_NASM_ARCHIVE_APPEND
            \ CMAKE_ASM_NASM_ARCHIVE_CREATE
            \ CMAKE_ASM_NASM_ARCHIVE_FINISH
            \ CMAKE_ASM_NASM_BYTE_ORDER
            \ CMAKE_ASM_NASM_CLANG_TIDY
            \ CMAKE_ASM_NASM_CLANG_TIDY_EXPORT_FIXES_DIR
            \ CMAKE_ASM_NASM_COMPILER
            \ CMAKE_ASM_NASM_COMPILER_ABI
            \ CMAKE_ASM_NASM_COMPILER_AR
            \ CMAKE_ASM_NASM_COMPILER_ARCHITECTURE_ID
            \ CMAKE_ASM_NASM_COMPILER_EXTERNAL_TOOLCHAIN
            \ CMAKE_ASM_NASM_COMPILER_FRONTEND_VARIANT
            \ CMAKE_ASM_NASM_COMPILER_ID
            \ CMAKE_ASM_NASM_COMPILER_LAUNCHER
            \ CMAKE_ASM_NASM_COMPILER_LOADED
            \ CMAKE_ASM_NASM_COMPILER_PREDEFINES_COMMAND
            \ CMAKE_ASM_NASM_COMPILER_RANLIB
            \ CMAKE_ASM_NASM_COMPILER_TARGET
            \ CMAKE_ASM_NASM_COMPILER_VERSION
            \ CMAKE_ASM_NASM_COMPILER_VERSION_INTERNAL
            \ CMAKE_ASM_NASM_COMPILE_OBJECT
            \ CMAKE_ASM_NASM_CPPCHECK
            \ CMAKE_ASM_NASM_CPPLINT
            \ CMAKE_ASM_NASM_CREATE_SHARED_LIBRARY
            \ CMAKE_ASM_NASM_CREATE_SHARED_MODULE
            \ CMAKE_ASM_NASM_CREATE_STATIC_LIBRARY
            \ CMAKE_ASM_NASM_EXTENSIONS
            \ CMAKE_ASM_NASM_EXTENSIONS_DEFAULT
            \ CMAKE_ASM_NASM_FLAGS
            \ CMAKE_ASM_NASM_FLAGS_DEBUG
            \ CMAKE_ASM_NASM_FLAGS_DEBUG_INIT
            \ CMAKE_ASM_NASM_FLAGS_INIT
            \ CMAKE_ASM_NASM_FLAGS_MINSIZEREL
            \ CMAKE_ASM_NASM_FLAGS_MINSIZEREL_INIT
            \ CMAKE_ASM_NASM_FLAGS_RELEASE
            \ CMAKE_ASM_NASM_FLAGS_RELEASE_INIT
            \ CMAKE_ASM_NASM_FLAGS_RELWITHDEBINFO
            \ CMAKE_ASM_NASM_FLAGS_RELWITHDEBINFO_INIT
            \ CMAKE_ASM_NASM_IGNORE_EXTENSIONS
            \ CMAKE_ASM_NASM_IMPLICIT_INCLUDE_DIRECTORIES
            \ CMAKE_ASM_NASM_IMPLICIT_LINK_DIRECTORIES
            \ CMAKE_ASM_NASM_IMPLICIT_LINK_FRAMEWORK_DIRECTORIES
            \ CMAKE_ASM_NASM_IMPLICIT_LINK_LIBRARIES
            \ CMAKE_ASM_NASM_INCLUDE_WHAT_YOU_USE
            \ CMAKE_ASM_NASM_INIT
            \ CMAKE_ASM_NASM_LIBRARY_ARCHITECTURE
            \ CMAKE_ASM_NASM_LINKER_LAUNCHER
            \ CMAKE_ASM_NASM_LINKER_PREFERENCE
            \ CMAKE_ASM_NASM_LINKER_PREFERENCE_PROPAGATES
            \ CMAKE_ASM_NASM_LINKER_WRAPPER_FLAG
            \ CMAKE_ASM_NASM_LINKER_WRAPPER_FLAG_SEP
            \ CMAKE_ASM_NASM_LINK_EXECUTABLE
            \ CMAKE_ASM_NASM_LINK_LIBRARY_FILE_FLAG
            \ CMAKE_ASM_NASM_LINK_LIBRARY_FLAG
            \ CMAKE_ASM_NASM_LINK_LIBRARY_SUFFIX
            \ CMAKE_ASM_NASM_LINK_WHAT_YOU_USE_FLAG
            \ CMAKE_ASM_NASM_OUTPUT_EXTENSION
            \ CMAKE_ASM_NASM_PLATFORM_ID
            \ CMAKE_ASM_NASM_SIMULATE_ID
            \ CMAKE_ASM_NASM_SIMULATE_VERSION
            \ CMAKE_ASM_NASM_SIZEOF_DATA_PTR
            \ CMAKE_ASM_NASM_SOURCE_FILE_EXTENSIONS
            \ CMAKE_ASM_NASM_STANDARD
            \ CMAKE_ASM_NASM_STANDARD_DEFAULT
            \ CMAKE_ASM_NASM_STANDARD_INCLUDE_DIRECTORIES
            \ CMAKE_ASM_NASM_STANDARD_LIBRARIES
            \ CMAKE_ASM_NASM_STANDARD_REQUIRED
            \ CMAKE_ASM_NASM_SUPPORTED
            \ CMAKE_ASM_NASM_VISIBILITY_PRESET
            \ CMAKE_ASM_OUTPUT_EXTENSION
            \ CMAKE_ASM_PLATFORM_ID
            \ CMAKE_ASM_SIMULATE_ID
            \ CMAKE_ASM_SIMULATE_VERSION
            \ CMAKE_ASM_SIZEOF_DATA_PTR
            \ CMAKE_ASM_SOURCE_FILE_EXTENSIONS
            \ CMAKE_ASM_STANDARD
            \ CMAKE_ASM_STANDARD_DEFAULT
            \ CMAKE_ASM_STANDARD_INCLUDE_DIRECTORIES
            \ CMAKE_ASM_STANDARD_LIBRARIES
            \ CMAKE_ASM_STANDARD_REQUIRED
            \ CMAKE_ASM_SUPPORTED
            \ CMAKE_ASM_VISIBILITY_PRESET
            \ CMAKE_AUTOGEN_BETTER_GRAPH_MULTI_CONFIG
            \ CMAKE_AUTOGEN_COMMAND_LINE_LENGTH_MAX
            \ CMAKE_AUTOGEN_ORIGIN_DEPENDS
            \ CMAKE_AUTOGEN_PARALLEL
            \ CMAKE_AUTOGEN_USE_SYSTEM_INCLUDE
            \ CMAKE_AUTOGEN_VERBOSE
            \ CMAKE_AUTOMOC
            \ CMAKE_AUTOMOC_COMPILER_PREDEFINES
            \ CMAKE_AUTOMOC_DEPEND_FILTERS
            \ CMAKE_AUTOMOC_EXECUTABLE
            \ CMAKE_AUTOMOC_MACRO_NAMES
            \ CMAKE_AUTOMOC_MOC_OPTIONS
            \ CMAKE_AUTOMOC_PATH_PREFIX
            \ CMAKE_AUTOMOC_RELAXED_MODE
            \ CMAKE_AUTOMOC_EXECUTABLE
            \ CMAKE_AUTORCC
            \ CMAKE_AUTORCC_EXECUTABLE
            \ CMAKE_AUTORCC_OPTIONS
            \ CMAKE_AUTORCC_EXECUTABLE
            \ CMAKE_AUTOUIC
            \ CMAKE_AUTOUIC_EXECUTABLE
            \ CMAKE_AUTOUIC_OPTIONS
            \ CMAKE_AUTOUIC_SEARCH_PATHS
            \ CMAKE_AUTOUIC_EXECUTABLE
            \ CMAKE_BACKWARDS_COMPATIBILITY
            \ CMAKE_BINARY_DIR
            \ CMAKE_BUILD_RPATH
            \ CMAKE_BUILD_RPATH_USE_ORIGIN
            \ CMAKE_BUILD_TOOL
            \ CMAKE_BUILD_TYPE
            \ CMAKE_BUILD_WITH_INSTALL_NAME_DIR
            \ CMAKE_BUILD_WITH_INSTALL_RPATH
            \ CMAKE_C
            \ CMAKE_CACHEFILE_DIR
            \ CMAKE_CACHE_MAJOR_VERSION
            \ CMAKE_CACHE_MINOR_VERSION
            \ CMAKE_CACHE_PATCH_VERSION
            \ CMAKE_CFG_INTDIR
            \ CMAKE_CLANG_VFS_OVERLAY
            \ CMAKE_CL_64
            \ CMAKE_CODEBLOCKS_COMPILER_ID
            \ CMAKE_CODEBLOCKS_EXCLUDE_EXTERNAL_FILES
            \ CMAKE_CODELITE_USE_TARGETS
            \ CMAKE_COLOR_DIAGNOSTICS
            \ CMAKE_COLOR_MAKEFILE
            \ CMAKE_COMMAND
            \ CMAKE_COMPILER_2005
            \ CMAKE_COMPILER_IS_GNUCC
            \ CMAKE_COMPILER_IS_GNUCXX
            \ CMAKE_COMPILER_IS_GNUG77
            \ CMAKE_COMPILE_PDB_OUTPUT_DIRECTORY
            \ CMAKE_COMPILE_WARNING_AS_ERROR
            \ CMAKE_CONFIGURATION_TYPES
            \ CMAKE_CPACK_COMMAND
            \ CMAKE_CROSSCOMPILING
            \ CMAKE_CROSSCOMPILING_EMULATOR
            \ CMAKE_CROSS_CONFIGS
            \ CMAKE_CSharp
            \ CMAKE_CSharp_ANDROID_TOOLCHAIN_MACHINE
            \ CMAKE_CSharp_ANDROID_TOOLCHAIN_PREFIX
            \ CMAKE_CSharp_ANDROID_TOOLCHAIN_SUFFIX
            \ CMAKE_CSharp_ARCHIVE_APPEND
            \ CMAKE_CSharp_ARCHIVE_CREATE
            \ CMAKE_CSharp_ARCHIVE_FINISH
            \ CMAKE_CSharp_BYTE_ORDER
            \ CMAKE_CSharp_CLANG_TIDY
            \ CMAKE_CSharp_CLANG_TIDY_EXPORT_FIXES_DIR
            \ CMAKE_CSharp_COMPILER
            \ CMAKE_CSharp_COMPILER_ABI
            \ CMAKE_CSharp_COMPILER_AR
            \ CMAKE_CSharp_COMPILER_ARCHITECTURE_ID
            \ CMAKE_CSharp_COMPILER_EXTERNAL_TOOLCHAIN
            \ CMAKE_CSharp_COMPILER_FRONTEND_VARIANT
            \ CMAKE_CSharp_COMPILER_ID
            \ CMAKE_CSharp_COMPILER_LAUNCHER
            \ CMAKE_CSharp_COMPILER_LOADED
            \ CMAKE_CSharp_COMPILER_PREDEFINES_COMMAND
            \ CMAKE_CSharp_COMPILER_RANLIB
            \ CMAKE_CSharp_COMPILER_TARGET
            \ CMAKE_CSharp_COMPILER_VERSION
            \ CMAKE_CSharp_COMPILER_VERSION_INTERNAL
            \ CMAKE_CSharp_COMPILE_OBJECT
            \ CMAKE_CSharp_CPPCHECK
            \ CMAKE_CSharp_CPPLINT
            \ CMAKE_CSharp_CREATE_SHARED_LIBRARY
            \ CMAKE_CSharp_CREATE_SHARED_MODULE
            \ CMAKE_CSharp_CREATE_STATIC_LIBRARY
            \ CMAKE_CSharp_EXTENSIONS
            \ CMAKE_CSharp_EXTENSIONS_DEFAULT
            \ CMAKE_CSharp_FLAGS
            \ CMAKE_CSharp_FLAGS_DEBUG
            \ CMAKE_CSharp_FLAGS_DEBUG_INIT
            \ CMAKE_CSharp_FLAGS_INIT
            \ CMAKE_CSharp_FLAGS_MINSIZEREL
            \ CMAKE_CSharp_FLAGS_MINSIZEREL_INIT
            \ CMAKE_CSharp_FLAGS_RELEASE
            \ CMAKE_CSharp_FLAGS_RELEASE_INIT
            \ CMAKE_CSharp_FLAGS_RELWITHDEBINFO
            \ CMAKE_CSharp_FLAGS_RELWITHDEBINFO_INIT
            \ CMAKE_CSharp_IGNORE_EXTENSIONS
            \ CMAKE_CSharp_IMPLICIT_INCLUDE_DIRECTORIES
            \ CMAKE_CSharp_IMPLICIT_LINK_DIRECTORIES
            \ CMAKE_CSharp_IMPLICIT_LINK_FRAMEWORK_DIRECTORIES
            \ CMAKE_CSharp_IMPLICIT_LINK_LIBRARIES
            \ CMAKE_CSharp_INCLUDE_WHAT_YOU_USE
            \ CMAKE_CSharp_INIT
            \ CMAKE_CSharp_LIBRARY_ARCHITECTURE
            \ CMAKE_CSharp_LINKER_LAUNCHER
            \ CMAKE_CSharp_LINKER_PREFERENCE
            \ CMAKE_CSharp_LINKER_PREFERENCE_PROPAGATES
            \ CMAKE_CSharp_LINKER_WRAPPER_FLAG
            \ CMAKE_CSharp_LINKER_WRAPPER_FLAG_SEP
            \ CMAKE_CSharp_LINK_EXECUTABLE
            \ CMAKE_CSharp_LINK_LIBRARY_FILE_FLAG
            \ CMAKE_CSharp_LINK_LIBRARY_FLAG
            \ CMAKE_CSharp_LINK_LIBRARY_SUFFIX
            \ CMAKE_CSharp_LINK_WHAT_YOU_USE_FLAG
            \ CMAKE_CSharp_OUTPUT_EXTENSION
            \ CMAKE_CSharp_PLATFORM_ID
            \ CMAKE_CSharp_SIMULATE_ID
            \ CMAKE_CSharp_SIMULATE_VERSION
            \ CMAKE_CSharp_SIZEOF_DATA_PTR
            \ CMAKE_CSharp_SOURCE_FILE_EXTENSIONS
            \ CMAKE_CSharp_STANDARD
            \ CMAKE_CSharp_STANDARD_DEFAULT
            \ CMAKE_CSharp_STANDARD_INCLUDE_DIRECTORIES
            \ CMAKE_CSharp_STANDARD_LIBRARIES
            \ CMAKE_CSharp_STANDARD_REQUIRED
            \ CMAKE_CSharp_SUPPORTED
            \ CMAKE_CSharp_VISIBILITY_PRESET
            \ CMAKE_CTEST_ARGUMENTS
            \ CMAKE_CTEST_COMMAND
            \ CMAKE_CUDA
            \ CMAKE_CUDA_ANDROID_TOOLCHAIN_MACHINE
            \ CMAKE_CUDA_ANDROID_TOOLCHAIN_PREFIX
            \ CMAKE_CUDA_ANDROID_TOOLCHAIN_SUFFIX
            \ CMAKE_CUDA_ARCHITECTURES
            \ CMAKE_CUDA_ARCHIVE_APPEND
            \ CMAKE_CUDA_ARCHIVE_CREATE
            \ CMAKE_CUDA_ARCHIVE_FINISH
            \ CMAKE_CUDA_BYTE_ORDER
            \ CMAKE_CUDA_CLANG_TIDY
            \ CMAKE_CUDA_CLANG_TIDY_EXPORT_FIXES_DIR
            \ CMAKE_CUDA_COMPILER
            \ CMAKE_CUDA_COMPILER_ABI
            \ CMAKE_CUDA_COMPILER_AR
            \ CMAKE_CUDA_COMPILER_ARCHITECTURE_ID
            \ CMAKE_CUDA_COMPILER_EXTERNAL_TOOLCHAIN
            \ CMAKE_CUDA_COMPILER_FRONTEND_VARIANT
            \ CMAKE_CUDA_COMPILER_ID
            \ CMAKE_CUDA_COMPILER_LAUNCHER
            \ CMAKE_CUDA_COMPILER_LOADED
            \ CMAKE_CUDA_COMPILER_PREDEFINES_COMMAND
            \ CMAKE_CUDA_COMPILER_RANLIB
            \ CMAKE_CUDA_COMPILER_TARGET
            \ CMAKE_CUDA_COMPILER_VERSION
            \ CMAKE_CUDA_COMPILER_VERSION_INTERNAL
            \ CMAKE_CUDA_COMPILE_FEATURES
            \ CMAKE_CUDA_COMPILE_OBJECT
            \ CMAKE_CUDA_CPPCHECK
            \ CMAKE_CUDA_CPPLINT
            \ CMAKE_CUDA_CREATE_SHARED_LIBRARY
            \ CMAKE_CUDA_CREATE_SHARED_MODULE
            \ CMAKE_CUDA_CREATE_STATIC_LIBRARY
            \ CMAKE_CUDA_EXTENSIONS
            \ CMAKE_CUDA_EXTENSIONS_DEFAULT
            \ CMAKE_CUDA_FLAGS
            \ CMAKE_CUDA_FLAGS_DEBUG
            \ CMAKE_CUDA_FLAGS_DEBUG_INIT
            \ CMAKE_CUDA_FLAGS_INIT
            \ CMAKE_CUDA_FLAGS_MINSIZEREL
            \ CMAKE_CUDA_FLAGS_MINSIZEREL_INIT
            \ CMAKE_CUDA_FLAGS_RELEASE
            \ CMAKE_CUDA_FLAGS_RELEASE_INIT
            \ CMAKE_CUDA_FLAGS_RELWITHDEBINFO
            \ CMAKE_CUDA_FLAGS_RELWITHDEBINFO_INIT
            \ CMAKE_CUDA_HOST_COMPILER
            \ CMAKE_CUDA_IGNORE_EXTENSIONS
            \ CMAKE_CUDA_IMPLICIT_INCLUDE_DIRECTORIES
            \ CMAKE_CUDA_IMPLICIT_LINK_DIRECTORIES
            \ CMAKE_CUDA_IMPLICIT_LINK_FRAMEWORK_DIRECTORIES
            \ CMAKE_CUDA_IMPLICIT_LINK_LIBRARIES
            \ CMAKE_CUDA_INCLUDE_WHAT_YOU_USE
            \ CMAKE_CUDA_INIT
            \ CMAKE_CUDA_LIBRARY_ARCHITECTURE
            \ CMAKE_CUDA_LINKER_LAUNCHER
            \ CMAKE_CUDA_LINKER_PREFERENCE
            \ CMAKE_CUDA_LINKER_PREFERENCE_PROPAGATES
            \ CMAKE_CUDA_LINKER_WRAPPER_FLAG
            \ CMAKE_CUDA_LINKER_WRAPPER_FLAG_SEP
            \ CMAKE_CUDA_LINK_EXECUTABLE
            \ CMAKE_CUDA_LINK_LIBRARY_FILE_FLAG
            \ CMAKE_CUDA_LINK_LIBRARY_FLAG
            \ CMAKE_CUDA_LINK_LIBRARY_SUFFIX
            \ CMAKE_CUDA_LINK_WHAT_YOU_USE_FLAG
            \ CMAKE_CUDA_OUTPUT_EXTENSION
            \ CMAKE_CUDA_PLATFORM_ID
            \ CMAKE_CUDA_RESOLVE_DEVICE_SYMBOLS
            \ CMAKE_CUDA_RUNTIME_LIBRARY
            \ CMAKE_CUDA_SEPARABLE_COMPILATION
            \ CMAKE_CUDA_SIMULATE_ID
            \ CMAKE_CUDA_SIMULATE_VERSION
            \ CMAKE_CUDA_SIZEOF_DATA_PTR
            \ CMAKE_CUDA_SOURCE_FILE_EXTENSIONS
            \ CMAKE_CUDA_STANDARD
            \ CMAKE_CUDA_STANDARD_DEFAULT
            \ CMAKE_CUDA_STANDARD_INCLUDE_DIRECTORIES
            \ CMAKE_CUDA_STANDARD_LIBRARIES
            \ CMAKE_CUDA_STANDARD_REQUIRED
            \ CMAKE_CUDA_SUPPORTED
            \ CMAKE_CUDA_TOOLKIT_INCLUDE_DIRECTORIES
            \ CMAKE_CUDA_VISIBILITY_PRESET
            \ CMAKE_CURRENT_BINARY_DIR
            \ CMAKE_CURRENT_FUNCTION
            \ CMAKE_CURRENT_FUNCTION_LIST_DIR
            \ CMAKE_CURRENT_FUNCTION_LIST_FILE
            \ CMAKE_CURRENT_FUNCTION_LIST_LINE
            \ CMAKE_CURRENT_LIST_DIR
            \ CMAKE_CURRENT_LIST_FILE
            \ CMAKE_CURRENT_LIST_LINE
            \ CMAKE_CURRENT_SOURCE_DIR
            \ CMAKE_CXX
            \ CMAKE_CXX_ANDROID_TOOLCHAIN_MACHINE
            \ CMAKE_CXX_ANDROID_TOOLCHAIN_PREFIX
            \ CMAKE_CXX_ANDROID_TOOLCHAIN_SUFFIX
            \ CMAKE_CXX_ARCHIVE_APPEND
            \ CMAKE_CXX_ARCHIVE_CREATE
            \ CMAKE_CXX_ARCHIVE_FINISH
            \ CMAKE_CXX_BYTE_ORDER
            \ CMAKE_CXX_CLANG_TIDY
            \ CMAKE_CXX_CLANG_TIDY_EXPORT_FIXES_DIR
            \ CMAKE_CXX_COMPILER
            \ CMAKE_CXX_COMPILER_ABI
            \ CMAKE_CXX_COMPILER_AR
            \ CMAKE_CXX_COMPILER_ARCHITECTURE_ID
            \ CMAKE_CXX_COMPILER_EXTERNAL_TOOLCHAIN
            \ CMAKE_CXX_COMPILER_FRONTEND_VARIANT
            \ CMAKE_CXX_COMPILER_ID
            \ CMAKE_CXX_COMPILER_LAUNCHER
            \ CMAKE_CXX_COMPILER_LOADED
            \ CMAKE_CXX_COMPILER_PREDEFINES_COMMAND
            \ CMAKE_CXX_COMPILER_RANLIB
            \ CMAKE_CXX_COMPILER_TARGET
            \ CMAKE_CXX_COMPILER_VERSION
            \ CMAKE_CXX_COMPILER_VERSION_INTERNAL
            \ CMAKE_CXX_COMPILE_FEATURES
            \ CMAKE_CXX_COMPILE_OBJECT
            \ CMAKE_CXX_CPPCHECK
            \ CMAKE_CXX_CPPLINT
            \ CMAKE_CXX_CREATE_SHARED_LIBRARY
            \ CMAKE_CXX_CREATE_SHARED_MODULE
            \ CMAKE_CXX_CREATE_STATIC_LIBRARY
            \ CMAKE_CXX_EXTENSIONS
            \ CMAKE_CXX_EXTENSIONS_DEFAULT
            \ CMAKE_CXX_FLAGS
            \ CMAKE_CXX_FLAGS_DEBUG
            \ CMAKE_CXX_FLAGS_DEBUG_INIT
            \ CMAKE_CXX_FLAGS_INIT
            \ CMAKE_CXX_FLAGS_MINSIZEREL
            \ CMAKE_CXX_FLAGS_MINSIZEREL_INIT
            \ CMAKE_CXX_FLAGS_RELEASE
            \ CMAKE_CXX_FLAGS_RELEASE_INIT
            \ CMAKE_CXX_FLAGS_RELWITHDEBINFO
            \ CMAKE_CXX_FLAGS_RELWITHDEBINFO_INIT
            \ CMAKE_CXX_IGNORE_EXTENSIONS
            \ CMAKE_CXX_IMPLICIT_INCLUDE_DIRECTORIES
            \ CMAKE_CXX_IMPLICIT_LINK_DIRECTORIES
            \ CMAKE_CXX_IMPLICIT_LINK_FRAMEWORK_DIRECTORIES
            \ CMAKE_CXX_IMPLICIT_LINK_LIBRARIES
            \ CMAKE_CXX_INCLUDE_WHAT_YOU_USE
            \ CMAKE_CXX_INIT
            \ CMAKE_CXX_LIBRARY_ARCHITECTURE
            \ CMAKE_CXX_LINKER_LAUNCHER
            \ CMAKE_CXX_LINKER_PREFERENCE
            \ CMAKE_CXX_LINKER_PREFERENCE_PROPAGATES
            \ CMAKE_CXX_LINKER_WRAPPER_FLAG
            \ CMAKE_CXX_LINKER_WRAPPER_FLAG_SEP
            \ CMAKE_CXX_LINK_EXECUTABLE
            \ CMAKE_CXX_LINK_LIBRARY_FILE_FLAG
            \ CMAKE_CXX_LINK_LIBRARY_FLAG
            \ CMAKE_CXX_LINK_LIBRARY_SUFFIX
            \ CMAKE_CXX_LINK_WHAT_YOU_USE_FLAG
            \ CMAKE_CXX_OUTPUT_EXTENSION
            \ CMAKE_CXX_PLATFORM_ID
            \ CMAKE_CXX_SCAN_FOR_MODULES
            \ CMAKE_CXX_SIMULATE_ID
            \ CMAKE_CXX_SIMULATE_VERSION
            \ CMAKE_CXX_SIZEOF_DATA_PTR
            \ CMAKE_CXX_SOURCE_FILE_EXTENSIONS
            \ CMAKE_CXX_STANDARD
            \ CMAKE_CXX_STANDARD_DEFAULT
            \ CMAKE_CXX_STANDARD_INCLUDE_DIRECTORIES
            \ CMAKE_CXX_STANDARD_LIBRARIES
            \ CMAKE_CXX_STANDARD_REQUIRED
            \ CMAKE_CXX_SUPPORTED
            \ CMAKE_CXX_VISIBILITY_PRESET
            \ CMAKE_C_ANDROID_TOOLCHAIN_MACHINE
            \ CMAKE_C_ANDROID_TOOLCHAIN_PREFIX
            \ CMAKE_C_ANDROID_TOOLCHAIN_SUFFIX
            \ CMAKE_C_ARCHIVE_APPEND
            \ CMAKE_C_ARCHIVE_CREATE
            \ CMAKE_C_ARCHIVE_FINISH
            \ CMAKE_C_BYTE_ORDER
            \ CMAKE_C_CLANG_TIDY
            \ CMAKE_C_CLANG_TIDY_EXPORT_FIXES_DIR
            \ CMAKE_C_COMPILER
            \ CMAKE_C_COMPILER_ABI
            \ CMAKE_C_COMPILER_AR
            \ CMAKE_C_COMPILER_ARCHITECTURE_ID
            \ CMAKE_C_COMPILER_EXTERNAL_TOOLCHAIN
            \ CMAKE_C_COMPILER_FRONTEND_VARIANT
            \ CMAKE_C_COMPILER_ID
            \ CMAKE_C_COMPILER_LAUNCHER
            \ CMAKE_C_COMPILER_LOADED
            \ CMAKE_C_COMPILER_PREDEFINES_COMMAND
            \ CMAKE_C_COMPILER_RANLIB
            \ CMAKE_C_COMPILER_TARGET
            \ CMAKE_C_COMPILER_VERSION
            \ CMAKE_C_COMPILER_VERSION_INTERNAL
            \ CMAKE_C_COMPILE_FEATURES
            \ CMAKE_C_COMPILE_OBJECT
            \ CMAKE_C_CPPCHECK
            \ CMAKE_C_CPPLINT
            \ CMAKE_C_CREATE_SHARED_LIBRARY
            \ CMAKE_C_CREATE_SHARED_MODULE
            \ CMAKE_C_CREATE_STATIC_LIBRARY
            \ CMAKE_C_EXTENSIONS
            \ CMAKE_C_EXTENSIONS_DEFAULT
            \ CMAKE_C_FLAGS
            \ CMAKE_C_FLAGS_DEBUG
            \ CMAKE_C_FLAGS_DEBUG_INIT
            \ CMAKE_C_FLAGS_INIT
            \ CMAKE_C_FLAGS_MINSIZEREL
            \ CMAKE_C_FLAGS_MINSIZEREL_INIT
            \ CMAKE_C_FLAGS_RELEASE
            \ CMAKE_C_FLAGS_RELEASE_INIT
            \ CMAKE_C_FLAGS_RELWITHDEBINFO
            \ CMAKE_C_FLAGS_RELWITHDEBINFO_INIT
            \ CMAKE_C_IGNORE_EXTENSIONS
            \ CMAKE_C_IMPLICIT_INCLUDE_DIRECTORIES
            \ CMAKE_C_IMPLICIT_LINK_DIRECTORIES
            \ CMAKE_C_IMPLICIT_LINK_FRAMEWORK_DIRECTORIES
            \ CMAKE_C_IMPLICIT_LINK_LIBRARIES
            \ CMAKE_C_INCLUDE_WHAT_YOU_USE
            \ CMAKE_C_INIT
            \ CMAKE_C_LIBRARY_ARCHITECTURE
            \ CMAKE_C_LINKER_LAUNCHER
            \ CMAKE_C_LINKER_PREFERENCE
            \ CMAKE_C_LINKER_PREFERENCE_PROPAGATES
            \ CMAKE_C_LINKER_WRAPPER_FLAG
            \ CMAKE_C_LINKER_WRAPPER_FLAG_SEP
            \ CMAKE_C_LINK_EXECUTABLE
            \ CMAKE_C_LINK_LIBRARY_FILE_FLAG
            \ CMAKE_C_LINK_LIBRARY_FLAG
            \ CMAKE_C_LINK_LIBRARY_SUFFIX
            \ CMAKE_C_LINK_WHAT_YOU_USE_FLAG
            \ CMAKE_C_OUTPUT_EXTENSION
            \ CMAKE_C_PLATFORM_ID
            \ CMAKE_C_SIMULATE_ID
            \ CMAKE_C_SIMULATE_VERSION
            \ CMAKE_C_SIZEOF_DATA_PTR
            \ CMAKE_C_SOURCE_FILE_EXTENSIONS
            \ CMAKE_C_STANDARD
            \ CMAKE_C_STANDARD_DEFAULT
            \ CMAKE_C_STANDARD_INCLUDE_DIRECTORIES
            \ CMAKE_C_STANDARD_LIBRARIES
            \ CMAKE_C_STANDARD_REQUIRED
            \ CMAKE_C_SUPPORTED
            \ CMAKE_C_VISIBILITY_PRESET
            \ CMAKE_DEBUG_POSTFIX
            \ CMAKE_DEBUG_TARGET_PROPERTIES
            \ CMAKE_DEBUGGER_WORKING_DIRECTORY
            \ CMAKE_DEFAULT_BUILD_TYPE
            \ CMAKE_DEFAULT_CONFIGS
            \ CMAKE_DEPENDS_IN_PROJECT_ONLY
            \ CMAKE_DEPENDS_USE_COMPILER
            \ CMAKE_DIRECTORY_LABELS
            \ CMAKE_DISABLE_PRECOMPILE_HEADERS
            \ CMAKE_DLL_NAME_WITH_SOVERSION
            \ CMAKE_DL_LIBS
            \ CMAKE_DOTNET_SDK
            \ CMAKE_DOTNET_TARGET_FRAMEWORK
            \ CMAKE_DOTNET_TARGET_FRAMEWORK_VERSION
            \ CMAKE_ECLIPSE_GENERATE_LINKED_RESOURCES
            \ CMAKE_ECLIPSE_GENERATE_SOURCE_PROJECT
            \ CMAKE_ECLIPSE_MAKE_ARGUMENTS
            \ CMAKE_ECLIPSE_RESOURCE_ENCODING
            \ CMAKE_ECLIPSE_VERSION
            \ CMAKE_EDIT_COMMAND
            \ CMAKE_ENABLE_EXPORTS
            \ CMAKE_ERROR_DEPRECATED
            \ CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION
            \ CMAKE_EXECUTABLE_ENABLE_EXPORTS
            \ CMAKE_EXECUTABLE_SUFFIX
            \ CMAKE_EXECUTABLE_SUFFIX_ASM
            \ CMAKE_EXECUTABLE_SUFFIX_ASM_MASM
            \ CMAKE_EXECUTABLE_SUFFIX_ASM_NASM
            \ CMAKE_EXECUTABLE_SUFFIX_C
            \ CMAKE_EXECUTABLE_SUFFIX_CSharp
            \ CMAKE_EXECUTABLE_SUFFIX_CUDA
            \ CMAKE_EXECUTABLE_SUFFIX_CXX
            \ CMAKE_EXECUTABLE_SUFFIX_Fortran
            \ CMAKE_EXECUTABLE_SUFFIX_HIP
            \ CMAKE_EXECUTABLE_SUFFIX_Java
            \ CMAKE_EXECUTABLE_SUFFIX_RC
            \ CMAKE_EXECUTABLE_SUFFIX_Swift
            \ CMAKE_EXECUTE_PROCESS_COMMAND_ECHO
            \ CMAKE_EXE_LINKER_FLAGS
            \ CMAKE_EXE_LINKER_FLAGS_INIT
            \ CMAKE_EXPORT_COMPILE_COMMANDS
            \ CMAKE_EXPORT_SARIF
            \ CMAKE_EXPORT_NO_PACKAGE_REGISTRY
            \ CMAKE_EXPORT_PACKAGE_REGISTRY
            \ CMAKE_EXTRA_GENERATOR
            \ CMAKE_EXTRA_SHARED_LIBRARY_SUFFIXES
            \ CMAKE_FIND_APPBUNDLE
            \ CMAKE_FIND_DEBUG_MODE
            \ CMAKE_FIND_FRAMEWORK
            \ CMAKE_FIND_LIBRARY_CUSTOM_LIB_SUFFIX
            \ CMAKE_FIND_LIBRARY_PREFIXES
            \ CMAKE_FIND_LIBRARY_SUFFIXES
            \ CMAKE_FIND_NO_INSTALL_PREFIX
            \ CMAKE_FIND_PACKAGE_NAME
            \ CMAKE_FIND_PACKAGE_NO_PACKAGE_REGISTRY
            \ CMAKE_FIND_PACKAGE_NO_SYSTEM_PACKAGE_REGISTRY
            \ CMAKE_FIND_PACKAGE_PREFER_CONFIG
            \ CMAKE_FIND_PACKAGE_REDIRECTS_DIR
            \ CMAKE_FIND_PACKAGE_RESOLVE_SYMLINKS
            \ CMAKE_FIND_PACKAGE_SORT_DIRECTION
            \ CMAKE_FIND_PACKAGE_SORT_ORDER
            \ CMAKE_FIND_PACKAGE_TARGETS_GLOBAL
            \ CMAKE_FIND_PACKAGE_WARN_NO_MODULE
            \ CMAKE_FIND_ROOT_PATH
            \ CMAKE_FIND_ROOT_PATH_MODE_INCLUDE
            \ CMAKE_FIND_ROOT_PATH_MODE_LIBRARY
            \ CMAKE_FIND_ROOT_PATH_MODE_PACKAGE
            \ CMAKE_FIND_ROOT_PATH_MODE_PROGRAM
            \ CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH
            \ CMAKE_FIND_USE_INSTALL_PREFIX
            \ CMAKE_FIND_USE_CMAKE_PATH
            \ CMAKE_FIND_USE_CMAKE_SYSTEM_PATH
            \ CMAKE_FIND_USE_INSTALL_PREFIX
            \ CMAKE_FIND_USE_PACKAGE_REGISTRY
            \ CMAKE_FIND_USE_PACKAGE_ROOT_PATH
            \ CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH
            \ CMAKE_FIND_USE_SYSTEM_PACKAGE_REGISTRY
            \ CMAKE_FOLDER
            \ CMAKE_FRAMEWORK
            \ CMAKE_FRAMEWORK_PATH
            \ CMAKE_Fortran
            \ CMAKE_Fortran_ANDROID_TOOLCHAIN_MACHINE
            \ CMAKE_Fortran_ANDROID_TOOLCHAIN_PREFIX
            \ CMAKE_Fortran_ANDROID_TOOLCHAIN_SUFFIX
            \ CMAKE_Fortran_ARCHIVE_APPEND
            \ CMAKE_Fortran_ARCHIVE_CREATE
            \ CMAKE_Fortran_ARCHIVE_FINISH
            \ CMAKE_Fortran_BYTE_ORDER
            \ CMAKE_Fortran_CLANG_TIDY
            \ CMAKE_Fortran_CLANG_TIDY_EXPORT_FIXES_DIR
            \ CMAKE_Fortran_COMPILER
            \ CMAKE_Fortran_COMPILER_ABI
            \ CMAKE_Fortran_COMPILER_AR
            \ CMAKE_Fortran_COMPILER_ARCHITECTURE_ID
            \ CMAKE_Fortran_COMPILER_EXTERNAL_TOOLCHAIN
            \ CMAKE_Fortran_COMPILER_FRONTEND_VARIANT
            \ CMAKE_Fortran_COMPILER_ID
            \ CMAKE_Fortran_COMPILER_LAUNCHER
            \ CMAKE_Fortran_COMPILER_LOADED
            \ CMAKE_Fortran_COMPILER_PREDEFINES_COMMAND
            \ CMAKE_Fortran_COMPILER_RANLIB
            \ CMAKE_Fortran_COMPILER_TARGET
            \ CMAKE_Fortran_COMPILER_VERSION
            \ CMAKE_Fortran_COMPILER_VERSION_INTERNAL
            \ CMAKE_Fortran_COMPILE_OBJECT
            \ CMAKE_Fortran_CPPCHECK
            \ CMAKE_Fortran_CPPLINT
            \ CMAKE_Fortran_CREATE_SHARED_LIBRARY
            \ CMAKE_Fortran_CREATE_SHARED_MODULE
            \ CMAKE_Fortran_CREATE_STATIC_LIBRARY
            \ CMAKE_Fortran_EXTENSIONS
            \ CMAKE_Fortran_EXTENSIONS_DEFAULT
            \ CMAKE_Fortran_FLAGS
            \ CMAKE_Fortran_FLAGS_DEBUG
            \ CMAKE_Fortran_FLAGS_DEBUG_INIT
            \ CMAKE_Fortran_FLAGS_INIT
            \ CMAKE_Fortran_FLAGS_MINSIZEREL
            \ CMAKE_Fortran_FLAGS_MINSIZEREL_INIT
            \ CMAKE_Fortran_FLAGS_RELEASE
            \ CMAKE_Fortran_FLAGS_RELEASE_INIT
            \ CMAKE_Fortran_FLAGS_RELWITHDEBINFO
            \ CMAKE_Fortran_FLAGS_RELWITHDEBINFO_INIT
            \ CMAKE_Fortran_FORMAT
            \ CMAKE_Fortran_IGNORE_EXTENSIONS
            \ CMAKE_Fortran_IMPLICIT_INCLUDE_DIRECTORIES
            \ CMAKE_Fortran_IMPLICIT_LINK_DIRECTORIES
            \ CMAKE_Fortran_IMPLICIT_LINK_FRAMEWORK_DIRECTORIES
            \ CMAKE_Fortran_IMPLICIT_LINK_LIBRARIES
            \ CMAKE_Fortran_INCLUDE_WHAT_YOU_USE
            \ CMAKE_Fortran_INIT
            \ CMAKE_Fortran_LIBRARY_ARCHITECTURE
            \ CMAKE_Fortran_LINKER_LAUNCHER
            \ CMAKE_Fortran_LINKER_PREFERENCE
            \ CMAKE_Fortran_LINKER_PREFERENCE_PROPAGATES
            \ CMAKE_Fortran_LINKER_WRAPPER_FLAG
            \ CMAKE_Fortran_LINKER_WRAPPER_FLAG_SEP
            \ CMAKE_Fortran_LINK_EXECUTABLE
            \ CMAKE_Fortran_LINK_LIBRARY_FILE_FLAG
            \ CMAKE_Fortran_LINK_LIBRARY_FLAG
            \ CMAKE_Fortran_LINK_LIBRARY_SUFFIX
            \ CMAKE_Fortran_LINK_WHAT_YOU_USE_FLAG
            \ CMAKE_Fortran_MODDIR_DEFAULT
            \ CMAKE_Fortran_MODDIR_FLAG
            \ CMAKE_Fortran_MODOUT_FLAG
            \ CMAKE_Fortran_MODULE_DIRECTORY
            \ CMAKE_Fortran_OUTPUT_EXTENSION
            \ CMAKE_Fortran_PLATFORM_ID
            \ CMAKE_Fortran_PREPROCESS
            \ CMAKE_Fortran_SIMULATE_ID
            \ CMAKE_Fortran_SIMULATE_VERSION
            \ CMAKE_Fortran_SIZEOF_DATA_PTR
            \ CMAKE_Fortran_SOURCE_FILE_EXTENSIONS
            \ CMAKE_Fortran_STANDARD
            \ CMAKE_Fortran_STANDARD_DEFAULT
            \ CMAKE_Fortran_STANDARD_INCLUDE_DIRECTORIES
            \ CMAKE_Fortran_STANDARD_LIBRARIES
            \ CMAKE_Fortran_STANDARD_REQUIRED
            \ CMAKE_Fortran_SUPPORTED
            \ CMAKE_Fortran_VISIBILITY_PRESET
            \ CMAKE_GENERATOR
            \ CMAKE_GENERATOR_INSTANCE
            \ CMAKE_GENERATOR_PLATFORM
            \ CMAKE_GENERATOR_TOOLSET
            \ CMAKE_GHS_NO_SOURCE_GROUP_FILE
            \ CMAKE_GLOBAL_AUTOGEN_TARGET
            \ CMAKE_GLOBAL_AUTOGEN_TARGET_NAME
            \ CMAKE_GLOBAL_AUTORCC_TARGET
            \ CMAKE_GLOBAL_AUTORCC_TARGET_NAME
            \ CMAKE_GNUtoMS
            \ CMAKE_HIP
            \ CMAKE_HIP_ANDROID_TOOLCHAIN_MACHINE
            \ CMAKE_HIP_ANDROID_TOOLCHAIN_PREFIX
            \ CMAKE_HIP_ANDROID_TOOLCHAIN_SUFFIX
            \ CMAKE_HIP_ARCHITECTURES
            \ CMAKE_HIP_ARCHIVE_APPEND
            \ CMAKE_HIP_ARCHIVE_CREATE
            \ CMAKE_HIP_ARCHIVE_FINISH
            \ CMAKE_HIP_BYTE_ORDER
            \ CMAKE_HIP_CLANG_TIDY
            \ CMAKE_HIP_CLANG_TIDY_EXPORT_FIXES_DIR
            \ CMAKE_HIP_COMPILER
            \ CMAKE_HIP_COMPILER_ABI
            \ CMAKE_HIP_COMPILER_AR
            \ CMAKE_HIP_COMPILER_ARCHITECTURE_ID
            \ CMAKE_HIP_COMPILER_EXTERNAL_TOOLCHAIN
            \ CMAKE_HIP_COMPILER_FRONTEND_VARIANT
            \ CMAKE_HIP_COMPILER_ID
            \ CMAKE_HIP_COMPILER_LAUNCHER
            \ CMAKE_HIP_COMPILER_LOADED
            \ CMAKE_HIP_COMPILER_PREDEFINES_COMMAND
            \ CMAKE_HIP_COMPILER_RANLIB
            \ CMAKE_HIP_COMPILER_TARGET
            \ CMAKE_HIP_COMPILER_VERSION
            \ CMAKE_HIP_COMPILER_VERSION_INTERNAL
            \ CMAKE_HIP_COMPILE_FEATURES
            \ CMAKE_HIP_COMPILE_OBJECT
            \ CMAKE_HIP_CPPCHECK
            \ CMAKE_HIP_CPPLINT
            \ CMAKE_HIP_CREATE_SHARED_LIBRARY
            \ CMAKE_HIP_CREATE_SHARED_MODULE
            \ CMAKE_HIP_CREATE_STATIC_LIBRARY
            \ CMAKE_HIP_EXTENSIONS
            \ CMAKE_HIP_EXTENSIONS_DEFAULT
            \ CMAKE_HIP_FLAGS
            \ CMAKE_HIP_FLAGS_DEBUG
            \ CMAKE_HIP_FLAGS_DEBUG_INIT
            \ CMAKE_HIP_FLAGS_INIT
            \ CMAKE_HIP_FLAGS_MINSIZEREL
            \ CMAKE_HIP_FLAGS_MINSIZEREL_INIT
            \ CMAKE_HIP_FLAGS_RELEASE
            \ CMAKE_HIP_FLAGS_RELEASE_INIT
            \ CMAKE_HIP_FLAGS_RELWITHDEBINFO
            \ CMAKE_HIP_FLAGS_RELWITHDEBINFO_INIT
            \ CMAKE_HIP_IGNORE_EXTENSIONS
            \ CMAKE_HIP_IMPLICIT_INCLUDE_DIRECTORIES
            \ CMAKE_HIP_IMPLICIT_LINK_DIRECTORIES
            \ CMAKE_HIP_IMPLICIT_LINK_FRAMEWORK_DIRECTORIES
            \ CMAKE_HIP_IMPLICIT_LINK_LIBRARIES
            \ CMAKE_HIP_INCLUDE_WHAT_YOU_USE
            \ CMAKE_HIP_INIT
            \ CMAKE_HIP_LIBRARY_ARCHITECTURE
            \ CMAKE_HIP_LINKER_LAUNCHER
            \ CMAKE_HIP_LINKER_PREFERENCE
            \ CMAKE_HIP_LINKER_PREFERENCE_PROPAGATES
            \ CMAKE_HIP_LINKER_WRAPPER_FLAG
            \ CMAKE_HIP_LINKER_WRAPPER_FLAG_SEP
            \ CMAKE_HIP_LINK_EXECUTABLE
            \ CMAKE_HIP_LINK_LIBRARY_FILE_FLAG
            \ CMAKE_HIP_LINK_LIBRARY_FLAG
            \ CMAKE_HIP_LINK_LIBRARY_SUFFIX
            \ CMAKE_HIP_LINK_WHAT_YOU_USE_FLAG
            \ CMAKE_HIP_OUTPUT_EXTENSION
            \ CMAKE_HIP_PLATFORM_ID
            \ CMAKE_HIP_SIMULATE_ID
            \ CMAKE_HIP_SIMULATE_VERSION
            \ CMAKE_HIP_SIZEOF_DATA_PTR
            \ CMAKE_HIP_SOURCE_FILE_EXTENSIONS
            \ CMAKE_HIP_STANDARD
            \ CMAKE_HIP_STANDARD_DEFAULT
            \ CMAKE_HIP_STANDARD_INCLUDE_DIRECTORIES
            \ CMAKE_HIP_STANDARD_LIBRARIES
            \ CMAKE_HIP_STANDARD_REQUIRED
            \ CMAKE_HIP_SUPPORTED
            \ CMAKE_HIP_VISIBILITY_PRESET
            \ CMAKE_HOME_DIRECTORY
            \ CMAKE_HOST_APPLE
            \ CMAKE_HOST_BSD
            \ CMAKE_HOST_LINUX
            \ CMAKE_HOST_SOLARIS
            \ CMAKE_HOST_SYSTEM
            \ CMAKE_HOST_SYSTEM_NAME
            \ CMAKE_HOST_SYSTEM_PROCESSOR
            \ CMAKE_HOST_SYSTEM_VERSION
            \ CMAKE_HOST_UNIX
            \ CMAKE_HOST_WIN32
            \ CMAKE_IGNORE_PATH
            \ CMAKE_IGNORE_PREFIX_PATH
            \ CMAKE_IMPORT_LIBRARY_PREFIX
            \ CMAKE_IMPORT_LIBRARY_SUFFIX
            \ CMAKE_INCLUDE_CURRENT_DIR
            \ CMAKE_INCLUDE_CURRENT_DIR_IN_INTERFACE
            \ CMAKE_INCLUDE_DIRECTORIES_BEFORE
            \ CMAKE_INCLUDE_DIRECTORIES_PROJECT_BEFORE
            \ CMAKE_INCLUDE_PATH
            \ CMAKE_INSTALL_DEFAULT_COMPONENT_NAME
            \ CMAKE_INSTALL_DEFAULT_DIRECTORY_PERMISSIONS
            \ CMAKE_INSTALL_MESSAGE
            \ CMAKE_INSTALL_NAME_DIR
            \ CMAKE_INSTALL_PREFIX
            \ CMAKE_INSTALL_PREFIX_INITIALIZED_TO_DEFAULT
            \ CMAKE_INSTALL_REMOVE_ENVIRONMENT_RPATH
            \ CMAKE_INSTALL_RPATH
            \ CMAKE_INSTALL_RPATH_USE_LINK_PATH
            \ CMAKE_INTERNAL_PLATFORM_ABI
            \ CMAKE_INTERPROCEDURAL_OPTIMIZATION
            \ CMAKE_IOS_INSTALL_COMBINED
            \ CMAKE_ISPC_HEADER_DIRECTORY
            \ CMAKE_ISPC_HEADER_SUFFIX
            \ CMAKE_ISPC_INSTRUCTION_SETS
            \ CMAKE_JOB_POOLS
            \ CMAKE_JOB_POOL_COMPILE
            \ CMAKE_JOB_POOL_LINK
            \ CMAKE_JOB_POOL_PRECOMPILE_HEADER
            \ CMAKE_Java
            \ CMAKE_Java_ANDROID_TOOLCHAIN_MACHINE
            \ CMAKE_Java_ANDROID_TOOLCHAIN_PREFIX
            \ CMAKE_Java_ANDROID_TOOLCHAIN_SUFFIX
            \ CMAKE_Java_ARCHIVE_APPEND
            \ CMAKE_Java_ARCHIVE_CREATE
            \ CMAKE_Java_ARCHIVE_FINISH
            \ CMAKE_Java_BYTE_ORDER
            \ CMAKE_Java_CLANG_TIDY
            \ CMAKE_Java_CLANG_TIDY_EXPORT_FIXES_DIR
            \ CMAKE_Java_COMPILER
            \ CMAKE_Java_COMPILER_ABI
            \ CMAKE_Java_COMPILER_AR
            \ CMAKE_Java_COMPILER_ARCHITECTURE_ID
            \ CMAKE_Java_COMPILER_EXTERNAL_TOOLCHAIN
            \ CMAKE_Java_COMPILER_FRONTEND_VARIANT
            \ CMAKE_Java_COMPILER_ID
            \ CMAKE_Java_COMPILER_LAUNCHER
            \ CMAKE_Java_COMPILER_LOADED
            \ CMAKE_Java_COMPILER_PREDEFINES_COMMAND
            \ CMAKE_Java_COMPILER_RANLIB
            \ CMAKE_Java_COMPILER_TARGET
            \ CMAKE_Java_COMPILER_VERSION
            \ CMAKE_Java_COMPILER_VERSION_INTERNAL
            \ CMAKE_Java_COMPILE_OBJECT
            \ CMAKE_Java_CPPCHECK
            \ CMAKE_Java_CPPLINT
            \ CMAKE_Java_CREATE_SHARED_LIBRARY
            \ CMAKE_Java_CREATE_SHARED_MODULE
            \ CMAKE_Java_CREATE_STATIC_LIBRARY
            \ CMAKE_Java_EXTENSIONS
            \ CMAKE_Java_EXTENSIONS_DEFAULT
            \ CMAKE_Java_FLAGS
            \ CMAKE_Java_FLAGS_DEBUG
            \ CMAKE_Java_FLAGS_DEBUG_INIT
            \ CMAKE_Java_FLAGS_INIT
            \ CMAKE_Java_FLAGS_MINSIZEREL
            \ CMAKE_Java_FLAGS_MINSIZEREL_INIT
            \ CMAKE_Java_FLAGS_RELEASE
            \ CMAKE_Java_FLAGS_RELEASE_INIT
            \ CMAKE_Java_FLAGS_RELWITHDEBINFO
            \ CMAKE_Java_FLAGS_RELWITHDEBINFO_INIT
            \ CMAKE_Java_IGNORE_EXTENSIONS
            \ CMAKE_Java_IMPLICIT_INCLUDE_DIRECTORIES
            \ CMAKE_Java_IMPLICIT_LINK_DIRECTORIES
            \ CMAKE_Java_IMPLICIT_LINK_FRAMEWORK_DIRECTORIES
            \ CMAKE_Java_IMPLICIT_LINK_LIBRARIES
            \ CMAKE_Java_INCLUDE_WHAT_YOU_USE
            \ CMAKE_Java_INIT
            \ CMAKE_Java_LIBRARY_ARCHITECTURE
            \ CMAKE_Java_LINKER_LAUNCHER
            \ CMAKE_Java_LINKER_PREFERENCE
            \ CMAKE_Java_LINKER_PREFERENCE_PROPAGATES
            \ CMAKE_Java_LINKER_WRAPPER_FLAG
            \ CMAKE_Java_LINKER_WRAPPER_FLAG_SEP
            \ CMAKE_Java_LINK_EXECUTABLE
            \ CMAKE_Java_LINK_LIBRARY_FILE_FLAG
            \ CMAKE_Java_LINK_LIBRARY_FLAG
            \ CMAKE_Java_LINK_LIBRARY_SUFFIX
            \ CMAKE_Java_LINK_WHAT_YOU_USE_FLAG
            \ CMAKE_Java_OUTPUT_EXTENSION
            \ CMAKE_Java_PLATFORM_ID
            \ CMAKE_Java_SIMULATE_ID
            \ CMAKE_Java_SIMULATE_VERSION
            \ CMAKE_Java_SIZEOF_DATA_PTR
            \ CMAKE_Java_SOURCE_FILE_EXTENSIONS
            \ CMAKE_Java_STANDARD
            \ CMAKE_Java_STANDARD_DEFAULT
            \ CMAKE_Java_STANDARD_INCLUDE_DIRECTORIES
            \ CMAKE_Java_STANDARD_LIBRARIES
            \ CMAKE_Java_STANDARD_REQUIRED
            \ CMAKE_Java_SUPPORTED
            \ CMAKE_Java_VISIBILITY_PRESET
            \ CMAKE_KATE_FILES_MODE
            \ CMAKE_KATE_MAKE_ARGUMENTS
            \ CMAKE_LIBRARY_ARCHITECTURE
            \ CMAKE_LIBRARY_ARCHITECTURE_REGEX
            \ CMAKE_LIBRARY_OUTPUT_DIRECTORY
            \ CMAKE_LIBRARY_PATH
            \ CMAKE_LIBRARY_PATH_FLAG
            \ CMAKE_LINK_DEF_FILE_FLAG
            \ CMAKE_LINK_DEPENDS_NO_SHARED
            \ CMAKE_LINK_DEPENDS_USE_LINKER
            \ CMAKE_LINK_DIRECTORIES_BEFORE
            \ CMAKE_LINK_INTERFACE_LIBRARIES
            \ CMAKE_LINK_LIBRARIES_ONLY_TARGETS
            \ CMAKE_LINK_LIBRARY_FILE_FLAG
            \ CMAKE_LINK_LIBRARY_FLAG
            \ CMAKE_LINK_LIBRARY_SUFFIX
            \ CMAKE_LINK_SEARCH_END_STATIC
            \ CMAKE_LINK_SEARCH_START_STATIC
            \ CMAKE_LINK_WHAT_YOU_USE
            \ CMAKE_LINK_WHAT_YOU_USE_CHECK
            \ CMAKE_MACOSX_BUNDLE
            \ CMAKE_MACOSX_RPATH
            \ CMAKE_MAJOR_VERSION
            \ CMAKE_MAKE_PROGRAM
            \ CMAKE_MATCH_COUNT
            \ CMAKE_MAXIMUM_RECURSION_DEPTH
            \ CMAKE_MESSAGE_CONTEXT
            \ CMAKE_MESSAGE_CONTEXT_SHOW
            \ CMAKE_MESSAGE_INDENT
            \ CMAKE_MESSAGE_LOG_LEVEL
            \ CMAKE_MFC_FLAG
            \ CMAKE_MINIMUM_REQUIRED_VERSION
            \ CMAKE_MINOR_VERSION
            \ CMAKE_MODULE_LINKER_FLAGS
            \ CMAKE_MODULE_LINKER_FLAGS_INIT
            \ CMAKE_MODULE_PATH
            \ CMAKE_MSVCIDE_RUN_PATH
            \ CMAKE_MSVC_DEBUG_INFORMATION_FORMAT
            \ CMAKE_MSVC_RUNTIME_CHECKS
            \ CMAKE_MSVC_RUNTIME_LIBRARY
            \ CMAKE_NETRC
            \ CMAKE_NETRC_FILE
            \ CMAKE_NINJA_OUTPUT_PATH_PREFIX
            \ CMAKE_NOT_USING_CONFIG_FLAGS
            \ CMAKE_NO_BUILTIN_CHRPATH
            \ CMAKE_NO_SYSTEM_FROM_IMPORTED
            \ CMAKE_OBJCXX_CLANG_TIDY
            \ CMAKE_OBJCXX_EXTENSIONS
            \ CMAKE_OBJCXX_LINKER_LAUNCHER
            \ CMAKE_OBJCXX_STANDARD
            \ CMAKE_OBJCXX_STANDARD_REQUIRED
            \ CMAKE_OBJC_CLANG_TIDY
            \ CMAKE_OBJC_EXTENSIONS
            \ CMAKE_OBJC_LINKER_LAUNCHER
            \ CMAKE_OBJC_STANDARD
            \ CMAKE_OBJC_STANDARD_REQUIRED
            \ CMAKE_OBJECT_PATH_MAX
            \ CMAKE_OPTIMIZE_DEPENDENCIES
            \ CMAKE_OSX_ARCHITECTURES
            \ CMAKE_OSX_DEPLOYMENT_TARGET
            \ CMAKE_OSX_SYSROOT
            \ CMAKE_PARENT_LIST_FILE
            \ CMAKE_PATCH_VERSION
            \ CMAKE_PCH_INSTANTIATE_TEMPLATES
            \ CMAKE_PCH_WARN_INVALID
            \ CMAKE_PDB_OUTPUT_DIRECTORY
            \ CMAKE_PLATFORM_NO_VERSIONED_SONAME
            \ CMAKE_POSITION_INDEPENDENT_CODE
            \ CMAKE_PREFIX_PATH
            \ CMAKE_PROGRAM_PATH
            \ CMAKE_PROJECT_DESCRIPTION
            \ CMAKE_PROJECT_HOMEPAGE_URL
            \ CMAKE_PROJECT_INCLUDE
            \ CMAKE_PROJECT_INCLUDE_BEFORE
            \ CMAKE_PROJECT_NAME
            \ CMAKE_PROJECT_TOP_LEVEL_INCLUDES
            \ CMAKE_PROJECT_VERSION
            \ CMAKE_PROJECT_VERSION_MAJOR
            \ CMAKE_PROJECT_VERSION_MINOR
            \ CMAKE_PROJECT_VERSION_PATCH
            \ CMAKE_PROJECT_VERSION_TWEAK
            \ CMAKE_RANLIB
            \ CMAKE_RC
            \ CMAKE_RC_ANDROID_TOOLCHAIN_MACHINE
            \ CMAKE_RC_ANDROID_TOOLCHAIN_PREFIX
            \ CMAKE_RC_ANDROID_TOOLCHAIN_SUFFIX
            \ CMAKE_RC_ARCHIVE_APPEND
            \ CMAKE_RC_ARCHIVE_CREATE
            \ CMAKE_RC_ARCHIVE_FINISH
            \ CMAKE_RC_BYTE_ORDER
            \ CMAKE_RC_CLANG_TIDY
            \ CMAKE_RC_CLANG_TIDY_EXPORT_FIXES_DIR
            \ CMAKE_RC_COMPILER
            \ CMAKE_RC_COMPILER_ABI
            \ CMAKE_RC_COMPILER_AR
            \ CMAKE_RC_COMPILER_ARCHITECTURE_ID
            \ CMAKE_RC_COMPILER_EXTERNAL_TOOLCHAIN
            \ CMAKE_RC_COMPILER_FRONTEND_VARIANT
            \ CMAKE_RC_COMPILER_ID
            \ CMAKE_RC_COMPILER_LAUNCHER
            \ CMAKE_RC_COMPILER_LOADED
            \ CMAKE_RC_COMPILER_PREDEFINES_COMMAND
            \ CMAKE_RC_COMPILER_RANLIB
            \ CMAKE_RC_COMPILER_TARGET
            \ CMAKE_RC_COMPILER_VERSION
            \ CMAKE_RC_COMPILER_VERSION_INTERNAL
            \ CMAKE_RC_COMPILE_OBJECT
            \ CMAKE_RC_CPPCHECK
            \ CMAKE_RC_CPPLINT
            \ CMAKE_RC_CREATE_SHARED_LIBRARY
            \ CMAKE_RC_CREATE_SHARED_MODULE
            \ CMAKE_RC_CREATE_STATIC_LIBRARY
            \ CMAKE_RC_EXTENSIONS
            \ CMAKE_RC_EXTENSIONS_DEFAULT
            \ CMAKE_RC_FLAGS
            \ CMAKE_RC_FLAGS_DEBUG
            \ CMAKE_RC_FLAGS_DEBUG_INIT
            \ CMAKE_RC_FLAGS_INIT
            \ CMAKE_RC_FLAGS_MINSIZEREL
            \ CMAKE_RC_FLAGS_MINSIZEREL_INIT
            \ CMAKE_RC_FLAGS_RELEASE
            \ CMAKE_RC_FLAGS_RELEASE_INIT
            \ CMAKE_RC_FLAGS_RELWITHDEBINFO
            \ CMAKE_RC_FLAGS_RELWITHDEBINFO_INIT
            \ CMAKE_RC_IGNORE_EXTENSIONS
            \ CMAKE_RC_IMPLICIT_INCLUDE_DIRECTORIES
            \ CMAKE_RC_IMPLICIT_LINK_DIRECTORIES
            \ CMAKE_RC_IMPLICIT_LINK_FRAMEWORK_DIRECTORIES
            \ CMAKE_RC_IMPLICIT_LINK_LIBRARIES
            \ CMAKE_RC_INCLUDE_WHAT_YOU_USE
            \ CMAKE_RC_INIT
            \ CMAKE_RC_LIBRARY_ARCHITECTURE
            \ CMAKE_RC_LINKER_LAUNCHER
            \ CMAKE_RC_LINKER_PREFERENCE
            \ CMAKE_RC_LINKER_PREFERENCE_PROPAGATES
            \ CMAKE_RC_LINKER_WRAPPER_FLAG
            \ CMAKE_RC_LINKER_WRAPPER_FLAG_SEP
            \ CMAKE_RC_LINK_EXECUTABLE
            \ CMAKE_RC_LINK_LIBRARY_FILE_FLAG
            \ CMAKE_RC_LINK_LIBRARY_FLAG
            \ CMAKE_RC_LINK_LIBRARY_SUFFIX
            \ CMAKE_RC_LINK_WHAT_YOU_USE_FLAG
            \ CMAKE_RC_OUTPUT_EXTENSION
            \ CMAKE_RC_PLATFORM_ID
            \ CMAKE_RC_SIMULATE_ID
            \ CMAKE_RC_SIMULATE_VERSION
            \ CMAKE_RC_SIZEOF_DATA_PTR
            \ CMAKE_RC_SOURCE_FILE_EXTENSIONS
            \ CMAKE_RC_STANDARD
            \ CMAKE_RC_STANDARD_DEFAULT
            \ CMAKE_RC_STANDARD_INCLUDE_DIRECTORIES
            \ CMAKE_RC_STANDARD_LIBRARIES
            \ CMAKE_RC_STANDARD_REQUIRED
            \ CMAKE_RC_SUPPORTED
            \ CMAKE_RC_VISIBILITY_PRESET
            \ CMAKE_ROOT
            \ CMAKE_RULE_MESSAGES
            \ CMAKE_RUNTIME_OUTPUT_DIRECTORY
            \ CMAKE_SCRIPT_MODE_FILE
            \ CMAKE_SHARED_LIBRARY_ENABLE_EXPORTS
            \ CMAKE_SHARED_LIBRARY_PREFIX
            \ CMAKE_SHARED_LIBRARY_SUFFIX
            \ CMAKE_SHARED_LINKER_FLAGS
            \ CMAKE_SHARED_LINKER_FLAGS_INIT
            \ CMAKE_SHARED_MODULE_PREFIX
            \ CMAKE_SHARED_MODULE_SUFFIX
            \ CMAKE_SIZEOF_VOID_P
            \ CMAKE_SKIP_BUILD_RPATH
            \ CMAKE_SKIP_INSTALL_ALL_DEPENDENCY
            \ CMAKE_SKIP_INSTALL_RPATH
            \ CMAKE_SKIP_INSTALL_RULES
            \ CMAKE_SKIP_RPATH
            \ CMAKE_SKIP_TEST_ALL_DEPENDENCY
            \ CMAKE_SOURCE_DIR
            \ CMAKE_STAGING_PREFIX
            \ CMAKE_STATIC_LIBRARY_PREFIX
            \ CMAKE_STATIC_LIBRARY_SUFFIX
            \ CMAKE_STATIC_LINKER_FLAGS
            \ CMAKE_STATIC_LINKER_FLAGS_INIT
            \ CMAKE_SUBLIME_TEXT_2_ENV_SETTINGS
            \ CMAKE_SUBLIME_TEXT_2_EXCLUDE_BUILD_TREE
            \ CMAKE_SUPPRESS_REGENERATION
            \ CMAKE_SYSROOT
            \ CMAKE_SYSROOT_COMPILE
            \ CMAKE_SYSROOT_LINK
            \ CMAKE_SYSTEM
            \ CMAKE_SYSTEM_APPBUNDLE_PATH
            \ CMAKE_SYSTEM_FRAMEWORK_PATH
            \ CMAKE_SYSTEM_IGNORE_PATH
            \ CMAKE_SYSTEM_IGNORE_PREFIX_PATH
            \ CMAKE_SYSTEM_INCLUDE_PATH
            \ CMAKE_SYSTEM_LIBRARY_PATH
            \ CMAKE_SYSTEM_NAME
            \ CMAKE_SYSTEM_PREFIX_PATH
            \ CMAKE_SYSTEM_PROCESSOR
            \ CMAKE_SYSTEM_PROGRAM_PATH
            \ CMAKE_SYSTEM_VERSION
            \ CMAKE_Swift
            \ CMAKE_Swift_ANDROID_TOOLCHAIN_MACHINE
            \ CMAKE_Swift_ANDROID_TOOLCHAIN_PREFIX
            \ CMAKE_Swift_ANDROID_TOOLCHAIN_SUFFIX
            \ CMAKE_Swift_ARCHIVE_APPEND
            \ CMAKE_Swift_ARCHIVE_CREATE
            \ CMAKE_Swift_ARCHIVE_FINISH
            \ CMAKE_Swift_BYTE_ORDER
            \ CMAKE_Swift_CLANG_TIDY
            \ CMAKE_Swift_CLANG_TIDY_EXPORT_FIXES_DIR
            \ CMAKE_Swift_COMPILER
            \ CMAKE_Swift_COMPILER_ABI
            \ CMAKE_Swift_COMPILER_AR
            \ CMAKE_Swift_COMPILER_ARCHITECTURE_ID
            \ CMAKE_Swift_COMPILER_EXTERNAL_TOOLCHAIN
            \ CMAKE_Swift_COMPILER_FRONTEND_VARIANT
            \ CMAKE_Swift_COMPILER_ID
            \ CMAKE_Swift_COMPILER_LAUNCHER
            \ CMAKE_Swift_COMPILER_LOADED
            \ CMAKE_Swift_COMPILER_PREDEFINES_COMMAND
            \ CMAKE_Swift_COMPILER_RANLIB
            \ CMAKE_Swift_COMPILER_TARGET
            \ CMAKE_Swift_COMPILER_VERSION
            \ CMAKE_Swift_COMPILER_VERSION_INTERNAL
            \ CMAKE_Swift_COMPILE_OBJECT
            \ CMAKE_Swift_CPPCHECK
            \ CMAKE_Swift_CPPLINT
            \ CMAKE_Swift_CREATE_SHARED_LIBRARY
            \ CMAKE_Swift_CREATE_SHARED_MODULE
            \ CMAKE_Swift_CREATE_STATIC_LIBRARY
            \ CMAKE_Swift_EXTENSIONS
            \ CMAKE_Swift_EXTENSIONS_DEFAULT
            \ CMAKE_Swift_FLAGS
            \ CMAKE_Swift_FLAGS_DEBUG
            \ CMAKE_Swift_FLAGS_DEBUG_INIT
            \ CMAKE_Swift_FLAGS_INIT
            \ CMAKE_Swift_FLAGS_MINSIZEREL
            \ CMAKE_Swift_FLAGS_MINSIZEREL_INIT
            \ CMAKE_Swift_FLAGS_RELEASE
            \ CMAKE_Swift_FLAGS_RELEASE_INIT
            \ CMAKE_Swift_FLAGS_RELWITHDEBINFO
            \ CMAKE_Swift_FLAGS_RELWITHDEBINFO_INIT
            \ CMAKE_Swift_IGNORE_EXTENSIONS
            \ CMAKE_Swift_IMPLICIT_INCLUDE_DIRECTORIES
            \ CMAKE_Swift_IMPLICIT_LINK_DIRECTORIES
            \ CMAKE_Swift_IMPLICIT_LINK_FRAMEWORK_DIRECTORIES
            \ CMAKE_Swift_IMPLICIT_LINK_LIBRARIES
            \ CMAKE_Swift_INCLUDE_WHAT_YOU_USE
            \ CMAKE_Swift_INIT
            \ CMAKE_Swift_LANGUAGE_VERSION
            \ CMAKE_Swift_LIBRARY_ARCHITECTURE
            \ CMAKE_Swift_LINKER_LAUNCHER
            \ CMAKE_Swift_LINKER_PREFERENCE
            \ CMAKE_Swift_LINKER_PREFERENCE_PROPAGATES
            \ CMAKE_Swift_LINKER_WRAPPER_FLAG
            \ CMAKE_Swift_LINKER_WRAPPER_FLAG_SEP
            \ CMAKE_Swift_LINK_EXECUTABLE
            \ CMAKE_Swift_LINK_LIBRARY_FILE_FLAG
            \ CMAKE_Swift_LINK_LIBRARY_FLAG
            \ CMAKE_Swift_LINK_LIBRARY_SUFFIX
            \ CMAKE_Swift_LINK_WHAT_YOU_USE_FLAG
            \ CMAKE_Swift_MODULE_DIRECTORY
            \ CMAKE_Swift_NUM_THREADS
            \ CMAKE_Swift_OUTPUT_EXTENSION
            \ CMAKE_Swift_PLATFORM_ID
            \ CMAKE_Swift_SIMULATE_ID
            \ CMAKE_Swift_SIMULATE_VERSION
            \ CMAKE_Swift_SIZEOF_DATA_PTR
            \ CMAKE_Swift_SOURCE_FILE_EXTENSIONS
            \ CMAKE_Swift_STANDARD
            \ CMAKE_Swift_STANDARD_DEFAULT
            \ CMAKE_Swift_STANDARD_INCLUDE_DIRECTORIES
            \ CMAKE_Swift_STANDARD_LIBRARIES
            \ CMAKE_Swift_STANDARD_REQUIRED
            \ CMAKE_Swift_SUPPORTED
            \ CMAKE_Swift_VISIBILITY_PRESET
            \ CMAKE_TASKING_TOOLSET
            \ CMAKE_TLS_CAINFO
            \ CMAKE_TLS_VERIFY
            \ CMAKE_TOOLCHAIN_FILE
            \ CMAKE_TRY_COMPILE_CONFIGURATION
            \ CMAKE_TRY_COMPILE_NO_PLATFORM_VARIABLES
            \ CMAKE_TRY_COMPILE_PLATFORM_VARIABLES
            \ CMAKE_TRY_COMPILE_TARGET_TYPE
            \ CMAKE_TWEAK_VERSION
            \ CMAKE_UNITY_BUILD
            \ CMAKE_UNITY_BUILD_BATCH_SIZE
            \ CMAKE_UNITY_BUILD_UNIQUE_ID
            \ CMAKE_USER_MAKE_RULES_OVERRIDE
            \ CMAKE_USER_MAKE_RULES_OVERRIDE_ASM
            \ CMAKE_USER_MAKE_RULES_OVERRIDE_ASM_MASM
            \ CMAKE_USER_MAKE_RULES_OVERRIDE_ASM_NASM
            \ CMAKE_USER_MAKE_RULES_OVERRIDE_C
            \ CMAKE_USER_MAKE_RULES_OVERRIDE_CSharp
            \ CMAKE_USER_MAKE_RULES_OVERRIDE_CUDA
            \ CMAKE_USER_MAKE_RULES_OVERRIDE_CXX
            \ CMAKE_USER_MAKE_RULES_OVERRIDE_Fortran
            \ CMAKE_USER_MAKE_RULES_OVERRIDE_HIP
            \ CMAKE_USER_MAKE_RULES_OVERRIDE_Java
            \ CMAKE_USER_MAKE_RULES_OVERRIDE_RC
            \ CMAKE_USER_MAKE_RULES_OVERRIDE_Swift
            \ CMAKE_USE_RELATIVE_PATHS
            \ CMAKE_VERBOSE_MAKEFILE
            \ CMAKE_VERIFY_INTERFACE_HEADER_SETS
            \ CMAKE_VERSION
            \ CMAKE_VISIBILITY_INLINES_HIDDEN
            \ CMAKE_VS_DEBUGGER_COMMAND
            \ CMAKE_VS_DEBUGGER_COMMAND_ARGUMENTS
            \ CMAKE_VS_DEBUGGER_ENVIRONMENT
            \ CMAKE_VS_DEBUGGER_WORKING_DIRECTORY
            \ CMAKE_VS_DEVENV_COMMAND
            \ CMAKE_VS_GLOBALS
            \ CMAKE_VS_INCLUDE_INSTALL_TO_DEFAULT_BUILD
            \ CMAKE_VS_INCLUDE_PACKAGE_TO_DEFAULT_BUILD
            \ CMAKE_VS_INTEL_Fortran_PROJECT_VERSION
            \ CMAKE_VS_JUST_MY_CODE_DEBUGGING
            \ CMAKE_VS_MSBUILD_COMMAND
            \ CMAKE_VS_NO_COMPILE_BATCHING
            \ CMAKE_VS_NUGET_PACKAGE_RESTORE
            \ CMAKE_VS_NsightTegra_VERSION
            \ CMAKE_VS_PLATFORM_NAME
            \ CMAKE_VS_PLATFORM_NAME_DEFAULT
            \ CMAKE_VS_PLATFORM_TOOLSET
            \ CMAKE_VS_PLATFORM_TOOLSET_CUDA
            \ CMAKE_VS_PLATFORM_TOOLSET_CUDA_CUSTOM_DIR
            \ CMAKE_VS_PLATFORM_TOOLSET_HOST_ARCHITECTURE
            \ CMAKE_VS_PLATFORM_TOOLSET_VERSION
            \ CMAKE_VS_SDK_EXCLUDE_DIRECTORIES
            \ CMAKE_VS_SDK_EXECUTABLE_DIRECTORIES
            \ CMAKE_VS_SDK_INCLUDE_DIRECTORIES
            \ CMAKE_VS_SDK_LIBRARY_DIRECTORIES
            \ CMAKE_VS_SDK_LIBRARY_WINRT_DIRECTORIES
            \ CMAKE_VS_SDK_REFERENCE_DIRECTORIES
            \ CMAKE_VS_SDK_SOURCE_DIRECTORIES
            \ CMAKE_VS_TARGET_FRAMEWORK_IDENTIFIER
            \ CMAKE_VS_TARGET_FRAMEWORK_TARGETS_VERSION
            \ CMAKE_VS_TARGET_FRAMEWORK_VERSION
            \ CMAKE_VS_VERSION_BUILD_NUMBER
            \ CMAKE_VS_WINDOWS_TARGET_PLATFORM_MIN_VERSION
            \ CMAKE_VS_WINDOWS_TARGET_PLATFORM_VERSION
            \ CMAKE_VS_WINDOWS_TARGET_PLATFORM_VERSION_MAXIMUM
            \ CMAKE_VS_WINRT_BY_DEFAULT
            \ CMAKE_WARN_DEPRECATED
            \ CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION
            \ CMAKE_WATCOM_RUNTIME_LIBRARY
            \ CMAKE_WIN32_EXECUTABLE
            \ CMAKE_WINDOWS_EXPORT_ALL_SYMBOLS
            \ CMAKE_XCODE_BUILD_SYSTEM
            \ CMAKE_XCODE_GENERATE_SCHEME
            \ CMAKE_XCODE_GENERATE_TOP_LEVEL_PROJECT_ONLY
            \ CMAKE_XCODE_LINK_BUILD_PHASE_MODE
            \ CMAKE_XCODE_PLATFORM_TOOLSET
            \ CMAKE_XCODE_SCHEME_ADDRESS_SANITIZER
            \ CMAKE_XCODE_SCHEME_ADDRESS_SANITIZER_USE_AFTER_RETURN
            \ CMAKE_XCODE_SCHEME_DEBUG_DOCUMENT_VERSIONING
            \ CMAKE_XCODE_SCHEME_ENABLE_GPU_FRAME_CAPTURE_MODE
            \ CMAKE_XCODE_SCHEME_DISABLE_MAIN_THREAD_CHECKER
            \ CMAKE_XCODE_SCHEME_DYNAMIC_LIBRARY_LOADS
            \ CMAKE_XCODE_SCHEME_DYNAMIC_LINKER_API_USAGE
            \ CMAKE_XCODE_SCHEME_ENABLE_GPU_API_VALIDATION
            \ CMAKE_XCODE_SCHEME_ENABLE_GPU_SHADER_VALIDATION
            \ CMAKE_XCODE_SCHEME_ENVIRONMENT
            \ CMAKE_XCODE_SCHEME_GUARD_MALLOC
            \ CMAKE_XCODE_SCHEME_LAUNCH_MODE
            \ CMAKE_XCODE_SCHEME_LLDB_INIT_FILE
            \ CMAKE_XCODE_SCHEME_MAIN_THREAD_CHECKER_STOP
            \ CMAKE_XCODE_SCHEME_MALLOC_GUARD_EDGES
            \ CMAKE_XCODE_SCHEME_MALLOC_SCRIBBLE
            \ CMAKE_XCODE_SCHEME_MALLOC_STACK
            \ CMAKE_XCODE_SCHEME_THREAD_SANITIZER
            \ CMAKE_XCODE_SCHEME_THREAD_SANITIZER_STOP
            \ CMAKE_XCODE_SCHEME_UNDEFINED_BEHAVIOUR_SANITIZER
            \ CMAKE_XCODE_SCHEME_UNDEFINED_BEHAVIOUR_SANITIZER_STOP
            \ CMAKE_XCODE_SCHEME_ENABLE_GPU_API_VALIDATION
            \ CMAKE_XCODE_SCHEME_ENABLE_GPU_SHADER_VALIDATION
            \ CMAKE_XCODE_SCHEME_LAUNCH_CONFIGURATION
            \ CMAKE_XCODE_SCHEME_TEST_CONFIGURATION
            \ CMAKE_XCODE_SCHEME_WORKING_DIRECTORY
            \ CMAKE_XCODE_SCHEME_ZOMBIE_OBJECTS
            \ CMAKE_XCODE_XCCONFIG
            \ CPACK_ABSOLUTE_DESTINATION_FILES
            \ CPACK_COMPONENT_INCLUDE_TOPLEVEL_DIRECTORY
            \ CPACK_CUSTOM_INSTALL_VARIABLES
            \ CPACK_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION
            \ CPACK_INCLUDE_TOPLEVEL_DIRECTORY
            \ CPACK_INSTALL_DEFAULT_DIRECTORY_PERMISSIONS
            \ CPACK_PACKAGING_INSTALL_PREFIX
            \ CPACK_SET_DESTDIR
            \ CPACK_WARN_ON_ABSOLUTE_INSTALL_DESTINATION
            \ CTEST_BINARY_DIRECTORY
            \ CTEST_BUILD_COMMAND
            \ CTEST_BUILD_NAME
            \ CTEST_BZR_COMMAND
            \ CTEST_BZR_UPDATE_OPTIONS
            \ CTEST_CHANGE_ID
            \ CTEST_CHECKOUT_COMMAND
            \ CTEST_CONFIGURATION_TYPE
            \ CTEST_CONFIGURE_COMMAND
            \ CTEST_COVERAGE_COMMAND
            \ CTEST_COVERAGE_EXTRA_FLAGS
            \ CTEST_CURL_OPTIONS
            \ CTEST_CUSTOM_COVERAGE_EXCLUDE
            \ CTEST_CUSTOM_ERROR_EXCEPTION
            \ CTEST_CUSTOM_ERROR_MATCH
            \ CTEST_CUSTOM_ERROR_POST_CONTEXT
            \ CTEST_CUSTOM_ERROR_PRE_CONTEXT
            \ CTEST_CUSTOM_MAXIMUM_FAILED_TEST_OUTPUT_SIZE
            \ CTEST_CUSTOM_MAXIMUM_NUMBER_OF_ERRORS
            \ CTEST_CUSTOM_MAXIMUM_NUMBER_OF_WARNINGS
            \ CTEST_CUSTOM_MAXIMUM_PASSED_TEST_OUTPUT_SIZE
            \ CTEST_CUSTOM_TEST_OUTPUT_TRUNCATION
            \ CTEST_CUSTOM_MEMCHECK_IGNORE
            \ CTEST_CUSTOM_POST_MEMCHECK
            \ CTEST_CUSTOM_POST_TEST
            \ CTEST_CUSTOM_PRE_MEMCHECK
            \ CTEST_CUSTOM_PRE_TEST
            \ CTEST_CUSTOM_TESTS_IGNORE
            \ CTEST_CUSTOM_TEST_OUTPUT_TRUNCATION
            \ CTEST_CUSTOM_WARNING_EXCEPTION
            \ CTEST_CUSTOM_WARNING_MATCH
            \ CTEST_CVS_CHECKOUT
            \ CTEST_CVS_COMMAND
            \ CTEST_CVS_UPDATE_OPTIONS
            \ CTEST_DROP_LOCATION
            \ CTEST_DROP_METHOD
            \ CTEST_DROP_SITE
            \ CTEST_DROP_SITE_CDASH
            \ CTEST_DROP_SITE_PASSWORD
            \ CTEST_DROP_SITE_USER
            \ CTEST_EXTRA_COVERAGE_GLOB
            \ CTEST_GIT_COMMAND
            \ CTEST_GIT_INIT_SUBMODULES
            \ CTEST_GIT_UPDATE_CUSTOM
            \ CTEST_GIT_UPDATE_OPTIONS
            \ CTEST_HG_COMMAND
            \ CTEST_HG_UPDATE_OPTIONS
            \ CTEST_LABELS_FOR_SUBPROJECTS
            \ CTEST_MEMORYCHECK_COMMAND
            \ CTEST_MEMORYCHECK_COMMAND_OPTIONS
            \ CTEST_MEMORYCHECK_SANITIZER_OPTIONS
            \ CTEST_MEMORYCHECK_SUPPRESSIONS_FILE
            \ CTEST_MEMORYCHECK_TYPE
            \ CTEST_NIGHTLY_START_TIME
            \ CTEST_P4_CLIENT
            \ CTEST_P4_COMMAND
            \ CTEST_P4_OPTIONS
            \ CTEST_P4_UPDATE_OPTIONS
            \ CTEST_RESOURCE_SPEC_FILE
            \ CTEST_RUN_CURRENT_SCRIPT
            \ CTEST_SCP_COMMAND
            \ CTEST_SCRIPT_DIRECTORY
            \ CTEST_SITE
            \ CTEST_SOURCE_DIRECTORY
            \ CTEST_SUBMIT_INACTIVITY_TIMEOUT
            \ CTEST_SUBMIT_URL
            \ CTEST_SVN_COMMAND
            \ CTEST_SVN_OPTIONS
            \ CTEST_SVN_UPDATE_OPTIONS
            \ CTEST_TEST_LOAD
            \ CTEST_TEST_TIMEOUT
            \ CTEST_TRIGGER_SITE
            \ CTEST_UPDATE_COMMAND
            \ CTEST_UPDATE_OPTIONS
            \ CTEST_UPDATE_VERSION_ONLY
            \ CTEST_UPDATE_VERSION_OVERRIDE
            \ CTEST_USE_LAUNCHERS
            \ CYGWIN
            \ DOXYGEN_ABBREVIATE_BRIEF
            \ DOXYGEN_ALIASES
            \ DOXYGEN_ALLEXTERNALS
            \ DOXYGEN_ALLOW_UNICODE_NAMES
            \ DOXYGEN_ALPHABETICAL_INDEX
            \ DOXYGEN_ALWAYS_DETAILED_SEC
            \ DOXYGEN_AUTOLINK_SUPPORT
            \ DOXYGEN_BINARY_TOC
            \ DOXYGEN_BRIEF_MEMBER_DESC
            \ DOXYGEN_BUILTIN_STL_SUPPORT
            \ DOXYGEN_CALLER_GRAPH
            \ DOXYGEN_CALL_GRAPH
            \ DOXYGEN_CASE_SENSE_NAMES
            \ DOXYGEN_CHM_FILE
            \ DOXYGEN_CHM_INDEX_ENCODING
            \ DOXYGEN_CITE_BIB_FILES
            \ DOXYGEN_CLANG_ASSISTED_PARSING
            \ DOXYGEN_CLANG_DATABASE_PATH
            \ DOXYGEN_CLANG_OPTIONS
            \ DOXYGEN_CLASS_DIAGRAMS
            \ DOXYGEN_CLASS_GRAPH
            \ DOXYGEN_COLLABORATION_GRAPH
            \ DOXYGEN_COLS_IN_ALPHA_INDEX
            \ DOXYGEN_COMPACT_LATEX
            \ DOXYGEN_COMPACT_RTF
            \ DOXYGEN_CPP_CLI_SUPPORT
            \ DOXYGEN_CREATE_SUBDIRS
            \ DOXYGEN_DIAFILE_DIRS
            \ DOXYGEN_DIA_PATH
            \ DOXYGEN_DIRECTORY_GRAPH
            \ DOXYGEN_DISABLE_INDEX
            \ DOXYGEN_DISTRIBUTE_GROUP_DOC
            \ DOXYGEN_DOCBOOK_OUTPUT
            \ DOXYGEN_DOCBOOK_PROGRAMLISTING
            \ DOXYGEN_DOCSET_BUNDLE_ID
            \ DOXYGEN_DOCSET_FEEDNAME
            \ DOXYGEN_DOCSET_PUBLISHER_ID
            \ DOXYGEN_DOCSET_PUBLISHER_NAME
            \ DOXYGEN_DOTFILE_DIRS
            \ DOXYGEN_DOT_CLEANUP
            \ DOXYGEN_DOT_FONTNAME
            \ DOXYGEN_DOT_FONTPATH
            \ DOXYGEN_DOT_FONTSIZE
            \ DOXYGEN_DOT_GRAPH_MAX_NODES
            \ DOXYGEN_DOT_IMAGE_FORMAT
            \ DOXYGEN_DOT_MULTI_TARGETS
            \ DOXYGEN_DOT_NUM_THREADS
            \ DOXYGEN_DOT_PATH
            \ DOXYGEN_DOT_TRANSPARENT
            \ DOXYGEN_DOXYFILE_ENCODING
            \ DOXYGEN_ECLIPSE_DOC_ID
            \ DOXYGEN_ENABLED_SECTIONS
            \ DOXYGEN_ENABLE_PREPROCESSING
            \ DOXYGEN_ENUM_VALUES_PER_LINE
            \ DOXYGEN_EXAMPLE_PATH
            \ DOXYGEN_EXAMPLE_PATTERNS
            \ DOXYGEN_EXAMPLE_RECURSIVE
            \ DOXYGEN_EXCLUDE
            \ DOXYGEN_EXCLUDE_PATTERNS
            \ DOXYGEN_EXCLUDE_SYMBOLS
            \ DOXYGEN_EXCLUDE_SYMLINKS
            \ DOXYGEN_EXPAND_AS_DEFINED
            \ DOXYGEN_EXPAND_ONLY_PREDEF
            \ DOXYGEN_EXTENSION_MAPPING
            \ DOXYGEN_EXTERNAL_GROUPS
            \ DOXYGEN_EXTERNAL_PAGES
            \ DOXYGEN_EXTERNAL_SEARCH
            \ DOXYGEN_EXTERNAL_SEARCH_ID
            \ DOXYGEN_EXTRACT_ALL
            \ DOXYGEN_EXTRACT_ANON_NSPACES
            \ DOXYGEN_EXTRACT_LOCAL_CLASSES
            \ DOXYGEN_EXTRACT_LOCAL_METHODS
            \ DOXYGEN_EXTRACT_PACKAGE
            \ DOXYGEN_EXTRACT_PRIVATE
            \ DOXYGEN_EXTRACT_PRIV_VIRTUAL
            \ DOXYGEN_EXTRACT_STATIC
            \ DOXYGEN_EXTRA_PACKAGES
            \ DOXYGEN_EXTRA_SEARCH_MAPPINGS
            \ DOXYGEN_EXT_LINKS_IN_WINDOW
            \ DOXYGEN_FILE_PATTERNS
            \ DOXYGEN_FILE_VERSION_FILTER
            \ DOXYGEN_FILTER_PATTERNS
            \ DOXYGEN_FILTER_SOURCE_FILES
            \ DOXYGEN_FILTER_SOURCE_PATTERNS
            \ DOXYGEN_FORCE_LOCAL_INCLUDES
            \ DOXYGEN_FORMULA_FONTSIZE
            \ DOXYGEN_FORMULA_TRANSPARENT
            \ DOXYGEN_FULL_PATH_NAMES
            \ DOXYGEN_GENERATE_AUTOGEN_DEF
            \ DOXYGEN_GENERATE_BUGLIST
            \ DOXYGEN_GENERATE_CHI
            \ DOXYGEN_GENERATE_DEPRECATEDLIST
            \ DOXYGEN_GENERATE_DOCBOOK
            \ DOXYGEN_GENERATE_DOCSET
            \ DOXYGEN_GENERATE_ECLIPSEHELP
            \ DOXYGEN_GENERATE_HTML
            \ DOXYGEN_GENERATE_HTMLHELP
            \ DOXYGEN_GENERATE_LATEX
            \ DOXYGEN_GENERATE_LEGEND
            \ DOXYGEN_GENERATE_MAN
            \ DOXYGEN_GENERATE_PERLMOD
            \ DOXYGEN_GENERATE_QHP
            \ DOXYGEN_GENERATE_RTF
            \ DOXYGEN_GENERATE_TAGFILE
            \ DOXYGEN_GENERATE_TESTLIST
            \ DOXYGEN_GENERATE_TODOLIST
            \ DOXYGEN_GENERATE_TREEVIEW
            \ DOXYGEN_GENERATE_XML
            \ DOXYGEN_GRAPHICAL_HIERARCHY
            \ DOXYGEN_GROUP_GRAPHS
            \ DOXYGEN_GROUP_NESTED_COMPOUNDS
            \ DOXYGEN_HAVE_DOT
            \ DOXYGEN_HHC_LOCATION
            \ DOXYGEN_HIDE_COMPOUND_REFERENCE
            \ DOXYGEN_HIDE_FRIEND_COMPOUNDS
            \ DOXYGEN_HIDE_IN_BODY_DOCS
            \ DOXYGEN_HIDE_SCOPE_NAMES
            \ DOXYGEN_HIDE_UNDOC_CLASSES
            \ DOXYGEN_HIDE_UNDOC_MEMBERS
            \ DOXYGEN_HIDE_UNDOC_RELATIONS
            \ DOXYGEN_HTML_COLORSTYLE_GAMMA
            \ DOXYGEN_HTML_COLORSTYLE_HUE
            \ DOXYGEN_HTML_COLORSTYLE_SAT
            \ DOXYGEN_HTML_DYNAMIC_MENUS
            \ DOXYGEN_HTML_DYNAMIC_SECTIONS
            \ DOXYGEN_HTML_EXTRA_FILES
            \ DOXYGEN_HTML_EXTRA_STYLESHEET
            \ DOXYGEN_HTML_FILE_EXTENSION
            \ DOXYGEN_HTML_FOOTER
            \ DOXYGEN_HTML_HEADER
            \ DOXYGEN_HTML_INDEX_NUM_ENTRIES
            \ DOXYGEN_HTML_OUTPUT
            \ DOXYGEN_HTML_STYLESHEET
            \ DOXYGEN_HTML_TIMESTAMP
            \ DOXYGEN_IDL_PROPERTY_SUPPORT
            \ DOXYGEN_IGNORE_PREFIX
            \ DOXYGEN_IMAGE_PATH
            \ DOXYGEN_INCLUDED_BY_GRAPH
            \ DOXYGEN_INCLUDE_FILE_PATTERNS
            \ DOXYGEN_INCLUDE_GRAPH
            \ DOXYGEN_INCLUDE_PATH
            \ DOXYGEN_INHERIT_DOCS
            \ DOXYGEN_INLINE_GROUPED_CLASSES
            \ DOXYGEN_INLINE_INFO
            \ DOXYGEN_INLINE_INHERITED_MEMB
            \ DOXYGEN_INLINE_SIMPLE_STRUCTS
            \ DOXYGEN_INLINE_SOURCES
            \ DOXYGEN_INPUT
            \ DOXYGEN_INPUT_ENCODING
            \ DOXYGEN_INPUT_FILTER
            \ DOXYGEN_INTERACTIVE_SVG
            \ DOXYGEN_INTERNAL_DOCS
            \ DOXYGEN_JAVADOC_AUTOBRIEF
            \ DOXYGEN_JAVADOC_BANNER
            \ DOXYGEN_LATEX_BATCHMODE
            \ DOXYGEN_LATEX_BIB_STYLE
            \ DOXYGEN_LATEX_CMD_NAME
            \ DOXYGEN_LATEX_EMOJI_DIRECTORY
            \ DOXYGEN_LATEX_EXTRA_FILES
            \ DOXYGEN_LATEX_EXTRA_STYLESHEET
            \ DOXYGEN_LATEX_FOOTER
            \ DOXYGEN_LATEX_HEADER
            \ DOXYGEN_LATEX_HIDE_INDICES
            \ DOXYGEN_LATEX_MAKEINDEX_CMD
            \ DOXYGEN_LATEX_OUTPUT
            \ DOXYGEN_LATEX_SOURCE_CODE
            \ DOXYGEN_LATEX_TIMESTAMP
            \ DOXYGEN_LAYOUT_FILE
            \ DOXYGEN_LOOKUP_CACHE_SIZE
            \ DOXYGEN_MACRO_EXPANSION
            \ DOXYGEN_MAKEINDEX_CMD_NAME
            \ DOXYGEN_MAN_EXTENSION
            \ DOXYGEN_MAN_LINKS
            \ DOXYGEN_MAN_OUTPUT
            \ DOXYGEN_MAN_SUBDIR
            \ DOXYGEN_MARKDOWN_SUPPORT
            \ DOXYGEN_MATHJAX_CODEFILE
            \ DOXYGEN_MATHJAX_EXTENSIONS
            \ DOXYGEN_MATHJAX_FORMAT
            \ DOXYGEN_MATHJAX_RELPATH
            \ DOXYGEN_MAX_DOT_GRAPH_DEPTH
            \ DOXYGEN_MAX_INITIALIZER_LINES
            \ DOXYGEN_MSCFILE_DIRS
            \ DOXYGEN_MULTILINE_CPP_IS_BRIEF
            \ DOXYGEN_OPTIMIZE_FOR_FORTRAN
            \ DOXYGEN_OPTIMIZE_OUTPUT_FOR_C
            \ DOXYGEN_OPTIMIZE_OUTPUT_JAVA
            \ DOXYGEN_OPTIMIZE_OUTPUT_SLICE
            \ DOXYGEN_OPTIMIZE_OUTPUT_VHDL
            \ DOXYGEN_OUTPUT_DIRECTORY
            \ DOXYGEN_OUTPUT_LANGUAGE
            \ DOXYGEN_OUTPUT_TEXT_DIRECTION
            \ DOXYGEN_PAPER_TYPE
            \ DOXYGEN_PDF_HYPERLINKS
            \ DOXYGEN_PERLMOD_LATEX
            \ DOXYGEN_PERLMOD_MAKEVAR_PREFIX
            \ DOXYGEN_PERLMOD_PRETTY
            \ DOXYGEN_PLANTUML_CFG_FILE
            \ DOXYGEN_PLANTUML_INCLUDE_PATH
            \ DOXYGEN_PLANTUML_JAR_PATH
            \ DOXYGEN_PREDEFINED
            \ DOXYGEN_PROJECT_BRIEF
            \ DOXYGEN_PROJECT_LOGO
            \ DOXYGEN_PROJECT_NAME
            \ DOXYGEN_PROJECT_NUMBER
            \ DOXYGEN_QCH_FILE
            \ DOXYGEN_QHG_LOCATION
            \ DOXYGEN_QHP_CUST_FILTER_ATTRS
            \ DOXYGEN_QHP_CUST_FILTER_NAME
            \ DOXYGEN_QHP_NAMESPACE
            \ DOXYGEN_QHP_SECT_FILTER_ATTRS
            \ DOXYGEN_QHP_VIRTUAL_FOLDER
            \ DOXYGEN_QT_AUTOBRIEF
            \ DOXYGEN_QUIET
            \ DOXYGEN_RECURSIVE
            \ DOXYGEN_REFERENCED_BY_RELATION
            \ DOXYGEN_REFERENCES_LINK_SOURCE
            \ DOXYGEN_REFERENCES_RELATION
            \ DOXYGEN_REPEAT_BRIEF
            \ DOXYGEN_RTF_EXTENSIONS_FILE
            \ DOXYGEN_RTF_HYPERLINKS
            \ DOXYGEN_RTF_OUTPUT
            \ DOXYGEN_RTF_SOURCE_CODE
            \ DOXYGEN_RTF_STYLESHEET_FILE
            \ DOXYGEN_SEARCHDATA_FILE
            \ DOXYGEN_SEARCHENGINE
            \ DOXYGEN_SEARCHENGINE_URL
            \ DOXYGEN_SEARCH_INCLUDES
            \ DOXYGEN_SEPARATE_MEMBER_PAGES
            \ DOXYGEN_SERVER_BASED_SEARCH
            \ DOXYGEN_SHORT_NAMES
            \ DOXYGEN_SHOW_FILES
            \ DOXYGEN_SHOW_GROUPED_MEMB_INC
            \ DOXYGEN_SHOW_INCLUDE_FILES
            \ DOXYGEN_SHOW_NAMESPACES
            \ DOXYGEN_SHOW_USED_FILES
            \ DOXYGEN_SIP_SUPPORT
            \ DOXYGEN_SKIP_FUNCTION_MACROS
            \ DOXYGEN_SORT_BRIEF_DOCS
            \ DOXYGEN_SORT_BY_SCOPE_NAME
            \ DOXYGEN_SORT_GROUP_NAMES
            \ DOXYGEN_SORT_MEMBERS_CTORS_1ST
            \ DOXYGEN_SORT_MEMBER_DOCS
            \ DOXYGEN_SOURCE_BROWSER
            \ DOXYGEN_SOURCE_TOOLTIPS
            \ DOXYGEN_STRICT_PROTO_MATCHING
            \ DOXYGEN_STRIP_CODE_COMMENTS
            \ DOXYGEN_STRIP_FROM_INC_PATH
            \ DOXYGEN_STRIP_FROM_PATH
            \ DOXYGEN_SUBGROUPING
            \ DOXYGEN_TAB_SIZE
            \ DOXYGEN_TAGFILES
            \ DOXYGEN_TCL_SUBST
            \ DOXYGEN_TEMPLATE_RELATIONS
            \ DOXYGEN_TOC_EXPAND
            \ DOXYGEN_TOC_INCLUDE_HEADINGS
            \ DOXYGEN_TREEVIEW_WIDTH
            \ DOXYGEN_TYPEDEF_HIDES_STRUCT
            \ DOXYGEN_UML_LIMIT_NUM_FIELDS
            \ DOXYGEN_UML_LOOK
            \ DOXYGEN_USE_HTAGS
            \ DOXYGEN_USE_MATHJAX
            \ DOXYGEN_USE_MDFILE_AS_MAINPAGE
            \ DOXYGEN_USE_PDFLATEX
            \ DOXYGEN_VERBATIM_HEADERS
            \ DOXYGEN_VERBATIM_VARS
            \ DOXYGEN_VERSION
            \ DOXYGEN_WARNINGS
            \ DOXYGEN_WARN_AS_ERROR
            \ DOXYGEN_WARN_FORMAT
            \ DOXYGEN_WARN_IF_DOC_ERROR
            \ DOXYGEN_WARN_IF_UNDOCUMENTED
            \ DOXYGEN_WARN_LOGFILE
            \ DOXYGEN_WARN_NO_PARAMDOC
            \ DOXYGEN_XML_NS_MEMB_FILE_SCOPE
            \ DOXYGEN_XML_OUTPUT
            \ DOXYGEN_XML_PROGRAMLISTING
            \ ENV
            \ EXECUTABLE_OUTPUT_PATH
            \ GHSMULTI
            \ IOS
            \ LIBRARY_OUTPUT_PATH
            \ LINUX
            \ MINGW
            \ MSVC
            \ MSVC10
            \ MSVC11
            \ MSVC12
            \ MSVC14
            \ MSVC60
            \ MSVC70
            \ MSVC71
            \ MSVC80
            \ MSVC90
            \ MSVC_IDE
            \ MSVC_TOOLSET_VERSION
            \ MSVC_VERSION
            \ MSYS
            \ PROJECT_BINARY_DIR
            \ PROJECT_DESCRIPTION
            \ PROJECT_HOMEPAGE_URL
            \ PROJECT_IS_TOP_LEVEL
            \ PROJECT_NAME
            \ PROJECT_SOURCE_DIR
            \ PROJECT_VERSION
            \ PROJECT_VERSION_MAJOR
            \ PROJECT_VERSION_MINOR
            \ PROJECT_VERSION_PATCH
            \ PROJECT_VERSION_TWEAK
            \ UNIX
            \ WIN32
            \ WINCE
            \ WINDOWS_PHONE
            \ WINDOWS_STORE
            \ XCODE
            \ XCODE_VERSION

syn keyword cmakeModule contained
            \ ExternalProject
            \ FetchContent

syn keyword cmakeKWExternalProject contained
            \ AWS
            \ BINARY_DIR
            \ BUILD_ALWAYS
            \ BUILD_BYPRODUCTS
            \ BUILD_COMMAND
            \ BUILD_IN_SOURCE
            \ CHECKOUT
            \ CMAKE_ARGS
            \ CMAKE_CACHE_ARGS
            \ CMAKE_CACHE_DEFAULT_ARGS
            \ CMAKE_EP_GIT_REMOTE_UPDATE_STRATEGY
            \ CMAKE_INSTALL_MODE
            \ COMMENT
            \ CONFIGURE_COMMAND
            \ CONFIGURE_HANDLED_BY_BUILD
            \ CVS
            \ CVSROOT
            \ CVS_MODULE
            \ CVS_REPOSITORY
            \ CVS_TAG
            \ DEPENDEES
            \ DEPENDERS
            \ DEPENDS
            \ DOWNLOADED_FILE
            \ DOWNLOAD_COMMAND
            \ DOWNLOAD_DIR
            \ DOWNLOAD_EXTRACT_TIMESTAMP
            \ DOWNLOAD_NAME
            \ DOWNLOAD_NO_EXTRACT
            \ DOWNLOAD_NO_PROGRESS
            \ EP_BASE
            \ EP_INDEPENDENT_STEP_TARGETS
            \ EP_PREFIX
            \ EP_STEP_TARGETS
            \ EP_UPDATE_DISCONNECTED
            \ EXCLUDE_FROM_ALL
            \ FALSE
            \ FORCE
            \ GHS
            \ GIT_CONFIG
            \ GIT_PROGRESS
            \ GIT_REMOTE_NAME
            \ GIT_REMOTE_UPDATE_STRATEGY
            \ GIT_REPOSITORY
            \ GIT_SHALLOW
            \ GIT_SUBMODULES
            \ GIT_SUBMODULES_RECURSE
            \ GIT_TAG
            \ HG_REPOSITORY
            \ HG_TAG
            \ HTTP_HEADER
            \ HTTP_PASSWORD
            \ HTTP_USERNAME
            \ IGNORED
            \ INACTIVITY_TIMEOUT
            \ INDEPENDENT_STEP_TARGETS
            \ INSTALL_BYPRODUCTS
            \ INSTALL_COMMAND
            \ INSTALL_DIR
            \ JOB_POOLS
            \ LIST_SEPARATOR
            \ LOG_BUILD
            \ LOG_CONFIGURE
            \ LOG_DIR
            \ LOG_DOWNLOAD
            \ LOG_INSTALL
            \ LOG_MERGED_STDOUTERR
            \ LOG_OUTPUT_ON_FAILURE
            \ LOG_PATCH
            \ LOG_TEST
            \ LOG_UPDATE
            \ MAKE_EXE
            \ MULTI
            \ NAMES
            \ NETRC
            \ NETRC_FILE
            \ NOTE
            \ NO_DEPENDS
            \ OPTIONAL
            \ PATCH_COMMAND
            \ PREFIX
            \ PROPERTY
            \ REBASE
            \ REBASE_CHECKOUT
            \ REQUIRED
            \ SOURCE_DIR
            \ SOURCE_SUBDIR
            \ STAMP_DIR
            \ STEP_TARGETS
            \ STRING
            \ SVN_PASSWORD
            \ SVN_REPOSITORY
            \ SVN_REVISION
            \ SVN_TRUST_CERT
            \ SVN_USERNAME
            \ TEST_AFTER_INSTALL
            \ TEST_BEFORE_INSTALL
            \ TEST_COMMAND
            \ TEST_EXCLUDE_FROM_MAIN
            \ TIMEOUT
            \ TLS_CAINFO
            \ TLS_VERIFY
            \ TMP_DIR
            \ TRUE
            \ UPDATE_COMMAND
            \ UPDATE_DISCONNECTED
            \ URL
            \ URL_HASH
            \ URL_MD5
            \ USES_TERMINAL_BUILD
            \ USES_TERMINAL_CONFIGURE
            \ USES_TERMINAL_DOWNLOAD
            \ USES_TERMINAL_INSTALL
            \ USES_TERMINAL_PATCH
            \ USES_TERMINAL_TEST
            \ USES_TERMINAL_UPDATE
            \ WORKING_DIRECTORY

syn keyword cmakeKWFetchContent contained
            \ ALWAYS
            \ BINARY_DIR
            \ BUILD_COMMAND
            \ BYPASS_PROVIDER
            \ CMAKE_PROJECT_
            \ CONFIGURE_COMMAND
            \ COPY
            \ CORRECT
            \ DCMAKE_TOOLCHAIN_FILE
            \ DESTINATION
            \ DOWNLOAD_NO_EXTRACT
            \ EXISTS
            \ FETCHCONTENT_BASE_DIR
            \ FETCHCONTENT_FULLY_DISCONNECTED
            \ FETCHCONTENT_MAKEAVAILABLE_SERIAL
            \ FETCHCONTENT_QUIET
            \ FETCHCONTENT_SOURCE_DIR_
            \ FETCHCONTENT_TRY_FIND_PACKAGE_MODE
            \ FETCHCONTENT_UPDATES_DISCONNECTED
            \ FETCHCONTENT_UPDATES_DISCONNECTED_
            \ FIND_PACKAGE_ARGS
            \ GIT_REPOSITORY
            \ GIT_TAG
            \ GLOBAL
            \ GTEST_BOTH_LIBRARIES
            \ GTEST_LIBRARIES
            \ GTEST_MAIN_LIBRARIES
            \ INSTALL_COMMAND
            \ INTERNAL
            \ NAME
            \ NAMES
            \ NEVER
            \ NOTE
            \ OFF
            \ OPTIONAL
            \ OPT_IN
            \ OVERRIDE_FIND_PACKAGE
            \ PACKAGE_VERSION_COMPATIBLE
            \ PACKAGE_VERSION_EXACT
            \ QUIET
            \ SOURCE_SUBDIR
            \ STREQUAL
            \ SUBBUILD_DIR
            \ SVN_REPOSITORY
            \ SVN_REVISION
            \ SYSTEM
            \ TARGET
            \ TEST_COMMAND
            \ TRUE
            \ URL
            \ URL_HASH
            \ VERIFY_INTERFACE_HEADER_SETS
            \ WRITE
            \ WRONG
            \ _BINARY_DIR
            \ _INCLUDE
            \ _POPULATED
            \ _SOURCE_DIR

syn keyword cmakeKWadd_compile_definitions contained
            \ COMPILE_DEFINITIONS
            \ VAR

syn keyword cmakeKWadd_compile_options contained
            \ CMAKE_
            \ COMPILE_LANGUAGE
            \ COMPILE_OPTIONS
            \ CONFIG
            \ SHELL
            \ UNIX_COMMAND
            \ _FLAGS
            \ _FLAGS_

syn keyword cmakeKWadd_custom_command contained
            \ APPEND
            \ ARGS
            \ BNF
            \ BYPRODUCTS
            \ CC
            \ COMMAND
            \ COMMAND_EXPAND_LISTS
            \ COMMENT
            \ CONFIG
            \ CROSSCOMPILING_EMULATOR
            \ DEPENDS
            \ DEPENDS_EXPLICIT_ONLY
            \ DEPFILE
            \ GENERATED
            \ IMPLICIT_DEPENDS
            \ INCLUDE_DIRECTORIES
            \ JOB_POOL
            \ JOB_POOLS
            \ JOIN
            \ MAIN_DEPENDENCY
            \ MODULE
            \ NOT
            \ OUTPUT
            \ PATH
            \ POST_BUILD
            \ PRE_BUILD
            \ PRE_LINK
            \ SYMBOLIC
            \ TARGET_FILE
            \ TARGET_LINKER_FILE
            \ TARGET_PDB_FILE
            \ TARGET_PROPERTY
            \ TARGET_SONAME_FILE
            \ USES_TERMINAL
            \ VERBATIM
            \ WORKING_DIRECTORY

syn keyword cmakeKWadd_custom_target contained
            \ ALL
            \ BYPRODUCTS
            \ CC
            \ COMMAND
            \ COMMAND_EXPAND_LISTS
            \ COMMENT
            \ CROSSCOMPILING_EMULATOR
            \ DEPENDS
            \ GENERATED
            \ INCLUDE_DIRECTORIES
            \ JOB_POOL
            \ JOB_POOLS
            \ JOIN
            \ PATH
            \ SOURCES
            \ TARGET_FILE
            \ TARGET_LINKER_FILE
            \ TARGET_PDB_FILE
            \ TARGET_PROPERTY
            \ TARGET_SONAME_FILE
            \ USES_TERMINAL
            \ VERBATIM
            \ WORKING_DIRECTORY

syn keyword cmakeKWadd_definitions contained
            \ COMPILE_DEFINITIONS

syn keyword cmakeKWadd_dependencies contained
            \ DEPENDS
            \ OBJECT_DEPENDS

syn keyword cmakeKWadd_executable contained
            \ ALIAS
            \ ALIAS_GLOBAL
            \ CONFIG
            \ EXCLUDE_FROM_ALL
            \ GLOBAL
            \ HEADER_FILE_ONLY
            \ IMPORTED
            \ IMPORTED_
            \ IMPORTED_LOCATION
            \ IMPORTED_LOCATION_
            \ MACOSX_BUNDLE
            \ OUTPUT_NAME
            \ RUNTIME_OUTPUT_DIRECTORY
            \ TARGET

syn keyword cmakeKWadd_library contained
            \ ALIAS
            \ ALIAS_GLOBAL
            \ ARCHIVE_OUTPUT_DIRECTORY
            \ CLI
            \ CONFIG
            \ DLL
            \ EXCLUDE_FROM_ALL
            \ FRAMEWORK
            \ GLOBAL
            \ HEADER_FILE_ONLY
            \ HEADER_SETS
            \ IMPORTED
            \ IMPORTED_
            \ IMPORTED_IMPLIB
            \ IMPORTED_IMPLIB_
            \ IMPORTED_LOCATION
            \ IMPORTED_LOCATION_
            \ IMPORTED_NO_SONAME
            \ IMPORTED_OBJECTS
            \ IMPORTED_OBJECTS_
            \ IMPORTED_SONAME
            \ INTERFACE
            \ INTERFACE_
            \ INTERFACE_SOURCES
            \ LC_ID_DYLIB
            \ LIBRARY_OUTPUT_DIRECTORY
            \ MODULE
            \ OBJECT
            \ ON
            \ OUTPUT_NAME
            \ POSITION_INDEPENDENT_CODE
            \ POST_BUILD
            \ PRE_BUILD
            \ PRE_LINK
            \ PRIVATE_HEADER
            \ PUBLIC_HEADER
            \ RUNTIME_OUTPUT_DIRECTORY
            \ SHARED
            \ SONAME
            \ SOURCES
            \ STATIC
            \ TARGETS
            \ TARGET_OBJECTS
            \ TARGET_RUNTIME_DLLS
            \ UNKNOWN

syn keyword cmakeKWadd_link_options contained
            \ CMAKE_
            \ CONFIG
            \ CUDA_RESOLVE_DEVICE_SYMBOLS
            \ CUDA_SEPARABLE_COMPILATION
            \ DEVICE_LINK
            \ GCC
            \ HOST_LINK
            \ LANG
            \ LINKER
            \ LINK_OPTIONS
            \ SHELL
            \ STATIC_LIBRARY_OPTIONS
            \ UNIX_COMMAND
            \ _FLAGS
            \ _FLAGS_
            \ _LINKER_WRAPPER_FLAG
            \ _LINKER_WRAPPER_FLAG_SEP

syn keyword cmakeKWadd_subdirectory contained
            \ EXCLUDE_FROM_ALL
            \ SYSTEM

syn keyword cmakeKWadd_test contained
            \ BUILD_TESTING
            \ COMMAND
            \ COMMAND_EXPAND_LISTS
            \ CONFIGURATIONS
            \ FAIL_REGULAR_EXPRESSION
            \ NAME
            \ OFF
            \ PASS_REGULAR_EXPRESSION
            \ SKIP_REGULAR_EXPRESSION
            \ TARGET_FILE
            \ WILL_FAIL
            \ WORKING_DIRECTORY

syn keyword cmakeKWblock contained
            \ PARENT_SCOPE
            \ POLICIES
            \ PROPAGATE
            \ PUSH
            \ SCOPE_FOR
            \ TRUE
            \ VARIABLES

syn keyword cmakeKWbuild_command contained
            \ CONFIGURATION
            \ PARALLEL_LEVEL
            \ TARGET

syn keyword cmakeKWcmake_file_api contained
            \ API
            \ API_VERSION
            \ BUILD_DIR
            \ CMAKEFILES
            \ CODEMODEL
            \ COMMAND
            \ CONFIG
            \ QUERY
            \ TOOLCHAINS

syn keyword cmakeKWcmake_host_system_information contained
            \ APPEND
            \ AVAILABLE_PHYSICAL_MEMORY
            \ AVAILABLE_VIRTUAL_MEMORY
            \ BOTH
            \ CMAKE_GET_OS_RELEASE_FALLBACK_CONTENT
            \ CMAKE_GET_OS_RELEASE_FALLBACK_RESULT_
            \ CMAKE_GET_OS_RELEASE_FALLBACK_RESULT_ID
            \ CMAKE_GET_OS_RELEASE_FALLBACK_RESULT_NAME
            \ CMAKE_GET_OS_RELEASE_FALLBACK_RESULT_PRETTY_NAME
            \ CMAKE_GET_OS_RELEASE_FALLBACK_RESULT_VERSION
            \ CMAKE_GET_OS_RELEASE_FALLBACK_RESULT_VERSION_ID
            \ CMAKE_GET_OS_RELEASE_FALLBACK_SCRIPTS
            \ DISTRIB_INFO
            \ DISTRIB_PRETTY_NAME
            \ DISTRO
            \ DISTRO_BUG_REPORT_URL
            \ DISTRO_HOME_URL
            \ DISTRO_ID
            \ DISTRO_ID_LIKE
            \ DISTRO_NAME
            \ DISTRO_PRETTY_NAME
            \ DISTRO_PRIVACY_POLICY_URL
            \ DISTRO_SUPPORT_URL
            \ DISTRO_UBUNTU_CODENAME
            \ DISTRO_VERSION
            \ DISTRO_VERSION_CODENAME
            \ DISTRO_VERSION_ID
            \ ERROR_VARIABLE
            \ EXISTS
            \ FQDN
            \ HAS_FPU
            \ HAS_MMX
            \ HAS_MMX_PLUS
            \ HAS_SERIAL_NUMBER
            \ HAS_SSE
            \ HAS_SSE_FP
            \ HAS_SSE_MMX
            \ HKCC
            \ HKCR
            \ HKCU
            \ HKEY_CLASSES_ROOT
            \ HKEY_CURRENT_CONFIG
            \ HKEY_CURRENT_USER
            \ HKEY_LOCAL_MACHINE
            \ HKEY_USERS
            \ HKLM
            \ HKU
            \ HOSTNAME
            \ ID
            \ LIMIT_COUNT
            \ LISTS
            \ LTS
            \ MATCHES
            \ NNN
            \ NOT
            \ NUMBER_OF_LOGICAL_CORES
            \ NUMBER_OF_PHYSICAL_CORES
            \ OS_NAME
            \ OS_PLATFORM
            \ OS_RELEASE
            \ OS_VERSION
            \ PRETTY_NAME
            \ PROCESSOR_DESCRIPTION
            \ PROCESSOR_NAME
            \ PROCESSOR_SERIAL_NUMBER
            \ QUERY
            \ REG_DWORD
            \ REG_EXPAND_SZ
            \ REG_MULTI_SZ
            \ REG_QWORD
            \ REG_SZ
            \ RESULT
            \ SEPARATOR
            \ SOFTWARE
            \ STATUS
            \ STRINGS
            \ SUBKEYS
            \ TARGET
            \ TOTAL_PHYSICAL_MEMORY
            \ TOTAL_VIRTUAL_MEMORY
            \ VALUE_NAMES
            \ VAR
            \ VIEW
            \ WINDOWS_REGISTRY

syn keyword cmakeKWcmake_language contained
            \ AND
            \ ANY
            \ APPEND
            \ ARGN
            \ BINARY_DIR
            \ BYPASS_PROVIDER
            \ CALL
            \ CANCEL_CALL
            \ CODE
            \ COMMAND
            \ COMMAND_ERROR_IS_FATAL
            \ DCMAKE_PROJECT_TOP_LEVEL_INCLUDES
            \ DEFER
            \ DIRECTORY
            \ EVAL
            \ FALSE
            \ FETCHCONTENT_MAKEAVAILABLE_SERIAL
            \ FETCHCONTENT_SOURCE_DIR_
            \ FETCHCONTENT_TRY_FIND_PACKAGE_MODE
            \ FIND_PACKAGE
            \ FIND_PACKAGE_ARGS
            \ GET_CALL_IDS
            \ GET_MESSAGE_LOG_LEVEL
            \ GIT_REPOSITORY
            \ GIT_SUBMODULES
            \ GIT_TAG
            \ ID_VAR
            \ MATCHES
            \ MYCOMP_PROVIDER_INSTALL_DIR
            \ NEVER
            \ NOT
            \ OVERRIDE_FIND_PACKAGE
            \ PATH
            \ POP_BACK
            \ QUIET
            \ SET_DEPENDENCY_PROVIDER
            \ SOURCE_DIR
            \ STATUS
            \ STREQUAL
            \ SUPPORTED_METHODS
            \ TRUE
            \ VERSION
            \ WRITE
            \ _FOUND
            \ _PATH

syn keyword cmakeKWcmake_minimum_required contained
            \ FATAL_ERROR
            \ VERSION

syn keyword cmakeKWcmake_parse_arguments contained
            \ ARGN
            \ CONFIGURATIONS
            \ DESTINATION
            \ FALSE
            \ FAST
            \ FILES
            \ MY_INSTALL
            \ MY_INSTALL_CONFIGURATIONS
            \ MY_INSTALL_DESTINATION
            \ MY_INSTALL_FAST
            \ MY_INSTALL_KEYWORDS_MISSING_VALUES
            \ MY_INSTALL_OPTIONAL
            \ MY_INSTALL_RENAME
            \ MY_INSTALL_TARGETS
            \ MY_INSTALL_UNPARSED_ARGUMENTS
            \ OPTIONAL
            \ PARSE_ARGV
            \ RENAME
            \ TARGETS
            \ TRUE
            \ UNDEFINED
            \ _KEYWORDS_MISSING_VALUES
            \ _UNPARSED_ARGUMENTS

syn keyword cmakeKWcmake_path contained
            \ ABSOLUTE_PATH
            \ AND
            \ APPEND
            \ APPEND_STRING
            \ BASE_DIRECTORY
            \ COMPARE
            \ CONVERT
            \ EQUAL
            \ EXTENSION
            \ EXTENSION_DEF
            \ FALSE
            \ FILENAME
            \ FILENAME_DEF
            \ GET
            \ GET_EXTENSION
            \ GET_FILENAME
            \ GET_PARENT_PATH
            \ GET_RELATIVE_PART
            \ GET_ROOT_DIRECTORY
            \ GET_ROOT_NAME
            \ GET_ROOT_PATH
            \ GET_STEM
            \ HASH
            \ HAS_EXTENSION
            \ HAS_FILENAME
            \ HAS_PARENT_PATH
            \ HAS_RELATIVE_PART
            \ HAS_ROOT_DIRECTORY
            \ HAS_ROOT_NAME
            \ HAS_ROOT_PATH
            \ HAS_STEM
            \ IS_ABSOLUTE
            \ IS_PREFIX
            \ IS_RELATIVE
            \ LAST_ONLY
            \ MATCHES
            \ NATIVE_PATH
            \ NORMALIZE
            \ NORMAL_PATH
            \ NOT_EQUAL
            \ OP
            \ OUTPUT_VARIABLE
            \ PARENT_PATH
            \ REAL_PATH
            \ RELATIVE_PART
            \ RELATIVE_PATH
            \ REMOVE_EXTENSION
            \ REMOVE_FILENAME
            \ REPLACE_EXTENSION
            \ REPLACE_FILENAME
            \ ROOT_DIRECTORY
            \ ROOT_NAME
            \ ROOT_PATH
            \ SET
            \ STEM
            \ STREQUAL
            \ TO_CMAKE_PATH_LIST
            \ TO_NATIVE_PATH_LIST
            \ TRUE
            \ XOR

syn keyword cmakeKWcmake_policy contained
            \ CMAKE_POLICY_DEFAULT_CMP
            \ CMP
            \ GET
            \ NNNN
            \ NO_POLICY_SCOPE
            \ OLD
            \ POLICIES
            \ POP
            \ PUSH
            \ SCOPE_FOR
            \ SET
            \ VERSION

syn keyword cmakeKWconfigure_file contained
            \ COPYONLY
            \ CRLF
            \ DOS
            \ ESCAPE_QUOTES
            \ FILE_PERMISSIONS
            \ FOO_ENABLE
            \ FOO_STRING
            \ GENERATE
            \ INTERFACE
            \ LF
            \ NEWLINE_STYLE
            \ NO_SOURCE_PERMISSIONS
            \ PRIVATE
            \ PUBLIC
            \ SYSTEM
            \ USE_SOURCE_PERMISSIONS
            \ VAR

syn keyword cmakeKWcreate_test_sourcelist contained
            \ CMAKE_TESTDRIVER_AFTER_TESTMAIN
            \ CMAKE_TESTDRIVER_BEFORE_TESTMAIN
            \ EXTRA_INCLUDE
            \ FUNCTION

syn keyword cmakeKWctest_build contained
            \ ALL_BUILD
            \ APPEND
            \ BUILD
            \ CAPTURE_CMAKE_ERROR
            \ CMAKE_BUILD_PARALLEL_LEVEL
            \ CONFIGURATION
            \ CTEST_BUILD_CONFIGURATION
            \ CTEST_BUILD_FLAGS
            \ CTEST_BUILD_TARGET
            \ FLAGS
            \ NUMBER_ERRORS
            \ NUMBER_WARNINGS
            \ PARALLEL_LEVEL
            \ QUIET
            \ RETURN_VALUE
            \ TARGET

syn keyword cmakeKWctest_configure contained
            \ APPEND
            \ BUILD
            \ CAPTURE_CMAKE_ERROR
            \ OPTIONS
            \ QUIET
            \ RETURN_VALUE
            \ SOURCE

syn keyword cmakeKWctest_coverage contained
            \ APPEND
            \ BUILD
            \ CAPTURE_CMAKE_ERROR
            \ LABELS
            \ QUIET
            \ RETURN_VALUE

syn keyword cmakeKWctest_memcheck contained
            \ APPEND
            \ BUILD
            \ CAPTURE_CMAKE_ERROR
            \ DEFECT_COUNT
            \ EXCLUDE
            \ EXCLUDE_FIXTURE
            \ EXCLUDE_FIXTURE_CLEANUP
            \ EXCLUDE_FIXTURE_SETUP
            \ EXCLUDE_LABEL
            \ INCLUDE
            \ INCLUDE_LABEL
            \ OFF
            \ ON
            \ OUTPUT_JUNIT
            \ PARALLEL_LEVEL
            \ QUIET
            \ REPEAT
            \ RESOURCE_SPEC_FILE
            \ RETURN_VALUE
            \ SCHEDULE_RANDOM
            \ START
            \ STOP_ON_FAILURE
            \ STOP_TIME
            \ STRIDE
            \ TEST_LOAD

syn keyword cmakeKWctest_run_script contained
            \ NEW_PROCESS
            \ RETURN_VALUE

syn keyword cmakeKWctest_start contained
            \ APPEND
            \ GROUP
            \ QUIET
            \ TAG
            \ TRACK

syn keyword cmakeKWctest_submit contained
            \ API
            \ BUILD_ID
            \ CAPTURE_CMAKE_ERROR
            \ CDASH_UPLOAD
            \ CDASH_UPLOAD_TYPE
            \ CTEST_EXTRA_SUBMIT_FILES
            \ CTEST_NOTES_FILES
            \ FILES
            \ HTTPHEADER
            \ PARTS
            \ QUIET
            \ RETRY_COUNT
            \ RETRY_DELAY
            \ RETURN_VALUE
            \ SUBMIT_URL

syn keyword cmakeKWctest_test contained
            \ AFTER_TIMEOUT
            \ APPEND
            \ ATTACHED_FILES
            \ ATTACHED_FILES_ON_FAIL
            \ BUILD
            \ CAPTURE_CMAKE_ERROR
            \ CPU
            \ EXCLUDE
            \ EXCLUDE_FIXTURE
            \ EXCLUDE_FIXTURE_CLEANUP
            \ EXCLUDE_FIXTURE_SETUP
            \ EXCLUDE_LABEL
            \ INCLUDE
            \ INCLUDE_LABEL
            \ LABELS
            \ OFF
            \ ON
            \ OUTPUT_JUNIT
            \ PARALLEL_LEVEL
            \ QUIET
            \ REPEAT
            \ RESOURCE_SPEC_FILE
            \ RETURN_VALUE
            \ SCHEDULE_RANDOM
            \ START
            \ STOP_ON_FAILURE
            \ STOP_TIME
            \ STRIDE
            \ TEST_LOAD
            \ UNTIL_FAIL
            \ UNTIL_PASS
            \ URL
            \ XML

syn keyword cmakeKWctest_update contained
            \ CAPTURE_CMAKE_ERROR
            \ QUIET
            \ RETURN_VALUE
            \ SOURCE

syn keyword cmakeKWctest_upload contained
            \ CAPTURE_CMAKE_ERROR
            \ FILES
            \ QUIET

syn keyword cmakeKWdefine_property contained
            \ APPEND
            \ APPEND_STRING
            \ BRIEF_DOCS
            \ CACHED_VARIABLE
            \ CMAKE_
            \ DIRECTORY
            \ FULL_DOCS
            \ GLOBAL
            \ INHERITED
            \ INITIALIZE_FROM_VARIABLE
            \ PROPERTY
            \ SOURCE
            \ TARGET
            \ TEST
            \ VARIABLE
            \ _CMAKE_

syn keyword cmakeKWdoxygen_add_docs contained
            \ ALL
            \ COMMENT
            \ USE_STAMP_FILE
            \ WORKING_DIRECTORY

syn keyword cmakeKWenable_language contained
            \ ASM
            \ ASM_MARMASM
            \ ASM_MASM
            \ ASM_NASM
            \ ATT
            \ CUDA
            \ HIP
            \ ISPC
            \ OBJC
            \ OBJCXX
            \ OPTIONAL

syn keyword cmakeKWenable_testing contained
            \ BUILD_TESTING

syn keyword cmakeKWexec_program contained
            \ ARGS
            \ OUTPUT_VARIABLE
            \ RETURN_VALUE

syn keyword cmakeKWexecute_process contained
            \ ANSI
            \ ANY
            \ AUTO
            \ COMMAND
            \ COMMAND_ECHO
            \ COMMAND_ERROR_IS_FATAL
            \ ECHO_ERROR_VARIABLE
            \ ECHO_OUTPUT_VARIABLE
            \ ENCODING
            \ ERROR_FILE
            \ ERROR_QUIET
            \ ERROR_STRIP_TRAILING_WHITESPACE
            \ ERROR_VARIABLE
            \ INPUT_FILE
            \ LAST
            \ NONE
            \ OEM
            \ OUTPUT_FILE
            \ OUTPUT_QUIET
            \ OUTPUT_STRIP_TRAILING_WHITESPACE
            \ OUTPUT_VARIABLE
            \ POSIX
            \ RESULTS_VARIABLE
            \ RESULT_VARIABLE
            \ RFC
            \ STDERR
            \ STDOUT
            \ TIMEOUT
            \ UTF
            \ WORKING_DIRECTORY

syn keyword cmakeKWexport contained
            \ ANDROID_MK
            \ APPEND
            \ CONFIG
            \ CXX_MODULES_DIRECTORY
            \ EXPORT
            \ EXPORT_LINK_INTERFACE_LIBRARIES
            \ FILE
            \ IMPORTED_
            \ NAMESPACE
            \ NDK
            \ OLD
            \ PACKAGE
            \ TARGETS

syn keyword cmakeKWexport_library_dependencies contained
            \ APPEND
            \ EXPORT
            \ INCLUDE
            \ LINK_INTERFACE_LIBRARIES
            \ SET

syn keyword cmakeKWfile contained
            \ APPEND
            \ ARCHIVE_CREATE
            \ ARCHIVE_EXTRACT
            \ ASCII
            \ BASE_DIRECTORY
            \ BUNDLE_EXECUTABLE
            \ CHMOD
            \ CHMOD_RECURSE
            \ CMAKE_GET_RUNTIME_DEPENDENCIES_COMMAND
            \ CMAKE_GET_RUNTIME_DEPENDENCIES_PLATFORM
            \ CMAKE_GET_RUNTIME_DEPENDENCIES_TOOL
            \ CMAKE_INSTALL_MODE
            \ CMAKE_OBJDUMP
            \ CODE
            \ COMPILE_FEATURES
            \ COMPRESSION
            \ COMPRESSION_LEVEL
            \ CONDITION
            \ CONFIGURE
            \ CONFIGURE_DEPENDS
            \ CONFLICTING_DEPENDENCIES_PREFIX
            \ CONTENT
            \ CONVERT
            \ COPYONLY
            \ COPY_FILE
            \ COPY_ON_ERROR
            \ CREATE_LINK
            \ CRLF
            \ DESTINATION
            \ DIRECTORIES
            \ DIRECTORY_PERMISSIONS
            \ DLL
            \ DOS
            \ DOWNLOAD
            \ ENCODING
            \ ESCAPE_QUOTES
            \ EXECUTABLES
            \ EXPAND_TILDE
            \ EXPECTED_HASH
            \ FILES_MATCHING
            \ FILE_PERMISSIONS
            \ FOLLOW_SYMLINKS
            \ FOLLOW_SYMLINK_CHAIN
            \ FORMAT
            \ FUNCTION
            \ GENERATE
            \ GET_RUNTIME_DEPENDENCIES
            \ GLOB
            \ GLOB_RECURSE
            \ GROUP_EXECUTE
            \ GROUP_READ
            \ GROUP_WRITE
            \ GUARD
            \ HASH
            \ HEX
            \ HOME
            \ HTTPHEADER
            \ IGNORED
            \ INACTIVITY_TIMEOUT
            \ INPUT
            \ INPUT_MAY_BE_RECENT
            \ INSTALL
            \ IS_ABSOLUTE
            \ LENGTH_MAXIMUM
            \ LENGTH_MINIMUM
            \ LF
            \ LIBRARIES
            \ LIMIT
            \ LIMIT_COUNT
            \ LIMIT_INPUT
            \ LIMIT_OUTPUT
            \ LIST_DIRECTORIES
            \ LIST_ONLY
            \ LOCK
            \ LOG
            \ MAKE_DIRECTORY
            \ MODULES
            \ MTIME
            \ MYLIBRARY
            \ NETRC
            \ NETRC_FILE
            \ NEWLINE_CONSUME
            \ NEWLINE_STYLE
            \ NOT
            \ NO_HEX_CONVERSION
            \ NO_REPLACE
            \ NO_SOURCE_PERMISSIONS
            \ OFFSET
            \ ONLY
            \ ONLY_IF_DIFFERENT
            \ OPTIONAL
            \ OUTPUT
            \ OWNER_EXECUTE
            \ OWNER_READ
            \ OWNER_WRITE
            \ PATHS
            \ PATTERN
            \ PATTERNS
            \ PERMISSIONS
            \ POST_EXCLUDE_FILES
            \ POST_EXCLUDE_REGEXES
            \ POST_INCLUDE_FILES
            \ POST_INCLUDE_REGEXES
            \ PRE_EXCLUDE_REGEXES
            \ PRE_INCLUDE_REGEXES
            \ PROCESS
            \ RANGE_END
            \ RANGE_START
            \ READ
            \ READ_SYMLINK
            \ REAL_PATH
            \ REGEX
            \ RELATIVE
            \ RELATIVE_PATH
            \ RELEASE
            \ REMOVE
            \ REMOVE_RECURSE
            \ RENAME
            \ REQUIRED
            \ RESOLVED_DEPENDENCIES_VAR
            \ RESULT
            \ RESULT_VARIABLE
            \ RPATH
            \ RUNPATH
            \ RUNTIME_DEPENDENCY_SET
            \ SCRIPT
            \ SETGID
            \ SETUID
            \ SHARED
            \ SHOW_PROGRESS
            \ SIZE
            \ SSL
            \ STATIC
            \ STATUS
            \ STRINGS
            \ SYMBOLIC
            \ TARGET
            \ TARGET_PROPERTY
            \ TIMESTAMP
            \ TLS_CAINFO
            \ TLS_VERIFY
            \ TOUCH
            \ TOUCH_NOCREATE
            \ TO_CMAKE_PATH
            \ TO_CMAKE_PATH_LIST
            \ TO_NATIVE_PATH
            \ TO_NATIVE_PATH_LIST
            \ UNRESOLVED_DEPENDENCIES_VAR
            \ UPLOAD
            \ URL
            \ USERPROFILE
            \ USERPWD
            \ USE_SOURCE_PERMISSIONS
            \ UTC
            \ UTF
            \ VERBOSE
            \ WORLD_EXECUTE
            \ WORLD_READ
            \ WORLD_WRITE
            \ WRITE
            \ XZ
            \ _FILENAMES

syn keyword cmakeKWfind_file contained
            \ BOTH
            \ CATEGORY
            \ CMAKE_FIND_ROOT_PATH_BOTH
            \ CMAKE_FIND_USE_
            \ DOC
            \ DVAR
            \ FALSE
            \ FIND_XXX_REGISTRY_VIEW
            \ HINTS
            \ HOST
            \ INCLUDE
            \ MATCHES
            \ NAMES
            \ NOT
            \ NO_CACHE
            \ NO_CMAKE_ENVIRONMENT_PATH
            \ NO_CMAKE_FIND_ROOT_PATH
            \ NO_CMAKE_INSTALL_PREFIX
            \ NO_CMAKE_PATH
            \ NO_CMAKE_SYSTEM_PATH
            \ NO_DEFAULT_PATH
            \ NO_PACKAGE_ROOT_PATH
            \ NO_SYSTEM_ENVIRONMENT_PATH
            \ ONLY_CMAKE_FIND_ROOT_PATH
            \ PACKAGENAME
            \ PARENT_SCOPE
            \ PATHS
            \ PATH_SUFFIXES
            \ REGISTRY_VIEW
            \ REQUIRED
            \ TARGET
            \ VALIDATOR
            \ VAR

syn keyword cmakeKWfind_library contained
            \ BOTH
            \ CATEGORY
            \ CMAKE_FIND_ROOT_PATH_BOTH
            \ CMAKE_FIND_USE_
            \ DOC
            \ DVAR
            \ FALSE
            \ FIND_XXX_REGISTRY_VIEW
            \ HINTS
            \ HOST
            \ LIB
            \ MATCHES
            \ NAMES
            \ NAMES_PER_DIR
            \ NOT
            \ NO_CACHE
            \ NO_CMAKE_ENVIRONMENT_PATH
            \ NO_CMAKE_FIND_ROOT_PATH
            \ NO_CMAKE_INSTALL_PREFIX
            \ NO_CMAKE_PATH
            \ NO_CMAKE_SYSTEM_PATH
            \ NO_DEFAULT_PATH
            \ NO_PACKAGE_ROOT_PATH
            \ NO_SYSTEM_ENVIRONMENT_PATH
            \ ONLY_CMAKE_FIND_ROOT_PATH
            \ PACKAGENAME
            \ PARENT_SCOPE
            \ PATHS
            \ PATH_SUFFIXES
            \ REGISTRY_VIEW
            \ REQUIRED
            \ TARGET
            \ VALIDATOR
            \ VAR

syn keyword cmakeKWfind_package contained
            \ ABI
            \ BOTH
            \ BUNDLE
            \ BYPASS_PROVIDER
            \ CATEGORY
            \ CMAKE_DISABLE_FIND_PACKAGE_
            \ CMAKE_REQUIRE_FIND_PACKAGE_
            \ CMAKE_FIND_ROOT_PATH_BOTH
            \ CMAKE_FIND_USE_
            \ CMAKE_REQUIRE_FIND_PACKAGE_
            \ COMPONENTS
            \ CONFIG
            \ CONFIGS
            \ DEC
            \ DVAR
            \ EXACT
            \ EXCLUDE
            \ FALSE
            \ FIND_PACKAGE_VERSION_FORMAT
            \ FRAMEWORK
            \ GLOBAL
            \ HINTS
            \ HOST
            \ INCLUDE
            \ MODULE
            \ NAMES
            \ NATURAL
            \ NO_CMAKE_BUILDS_PATH
            \ NO_CMAKE_ENVIRONMENT_PATH
            \ NO_CMAKE_FIND_ROOT_PATH
            \ NO_CMAKE_INSTALL_PREFIX
            \ NO_CMAKE_PACKAGE_REGISTRY
            \ NO_CMAKE_PATH
            \ NO_CMAKE_SYSTEM_PACKAGE_REGISTRY
            \ NO_CMAKE_SYSTEM_PATH
            \ NO_DEFAULT_PATH
            \ NO_MODULE
            \ NO_PACKAGE_ROOT_PATH
            \ NO_POLICY_SCOPE
            \ NO_SYSTEM_ENVIRONMENT_PATH
            \ OLD
            \ ONLY_CMAKE_FIND_ROOT_PATH
            \ OPTIONAL_COMPONENTS
            \ PACKAGENAME
            \ PACKAGE_FIND_NAME
            \ PACKAGE_FIND_VERSION
            \ PACKAGE_FIND_VERSION_COMPLETE
            \ PACKAGE_FIND_VERSION_COUNT
            \ PACKAGE_FIND_VERSION_MAJOR
            \ PACKAGE_FIND_VERSION_MAX
            \ PACKAGE_FIND_VERSION_MAX_COUNT
            \ PACKAGE_FIND_VERSION_MAX_MAJOR
            \ PACKAGE_FIND_VERSION_MAX_MINOR
            \ PACKAGE_FIND_VERSION_MAX_PATCH
            \ PACKAGE_FIND_VERSION_MAX_TWEAK
            \ PACKAGE_FIND_VERSION_MINOR
            \ PACKAGE_FIND_VERSION_MIN_COUNT
            \ PACKAGE_FIND_VERSION_MIN_MAJOR
            \ PACKAGE_FIND_VERSION_MIN_MINOR
            \ PACKAGE_FIND_VERSION_MIN_PATCH
            \ PACKAGE_FIND_VERSION_MIN_TWEAK
            \ PACKAGE_FIND_VERSION_PATCH
            \ PACKAGE_FIND_VERSION_RANGE
            \ PACKAGE_FIND_VERSION_RANGE_MAX
            \ PACKAGE_FIND_VERSION_RANGE_MIN
            \ PACKAGE_FIND_VERSION_TWEAK
            \ PACKAGE_VERSION_COMPATIBLE
            \ PACKAGE_VERSION_EXACT
            \ PACKAGE_VERSION_UNSUITABLE
            \ PATHS
            \ PATH_SUFFIXES
            \ QUIET
            \ REGISTRY_VIEW
            \ REQUIRED
            \ SET
            \ TARGET
            \ TRUE
            \ VALUE
            \ _CONFIG
            \ _CONSIDERED_CONFIGS
            \ _CONSIDERED_VERSIONS
            \ _DIR
            \ _FIND_COMPONENTS
            \ _FIND_QUIETLY
            \ _FIND_REGISTRY_VIEW
            \ _FIND_REQUIRED
            \ _FIND_REQUIRED_
            \ _FIND_VERSION_EXACT
            \ _FOUND

syn keyword cmakeKWfind_path contained
            \ BOTH
            \ CATEGORY
            \ CMAKE_FIND_ROOT_PATH_BOTH
            \ CMAKE_FIND_USE_
            \ DOC
            \ DVAR
            \ FALSE
            \ FIND_XXX_REGISTRY_VIEW
            \ HINTS
            \ HOST
            \ INCLUDE
            \ MATCHES
            \ NAMES
            \ NOT
            \ NO_CACHE
            \ NO_CMAKE_ENVIRONMENT_PATH
            \ NO_CMAKE_FIND_ROOT_PATH
            \ NO_CMAKE_INSTALL_PREFIX
            \ NO_CMAKE_PATH
            \ NO_CMAKE_SYSTEM_PATH
            \ NO_DEFAULT_PATH
            \ NO_PACKAGE_ROOT_PATH
            \ NO_SYSTEM_ENVIRONMENT_PATH
            \ ONLY_CMAKE_FIND_ROOT_PATH
            \ PACKAGENAME
            \ PARENT_SCOPE
            \ PATHS
            \ PATH_SUFFIXES
            \ REGISTRY_VIEW
            \ REQUIRED
            \ TARGET
            \ VALIDATOR
            \ VAR

syn keyword cmakeKWfind_program contained
            \ BOTH
            \ CATEGORY
            \ CMAKE_FIND_ROOT_PATH_BOTH
            \ CMAKE_FIND_USE_
            \ DOC
            \ DVAR
            \ FALSE
            \ FIND_XXX_REGISTRY_VIEW
            \ HINTS
            \ HOST
            \ MATCHES
            \ NAMES
            \ NAMES_PER_DIR
            \ NOT
            \ NO_CACHE
            \ NO_CMAKE_ENVIRONMENT_PATH
            \ NO_CMAKE_FIND_ROOT_PATH
            \ NO_CMAKE_INSTALL_PREFIX
            \ NO_CMAKE_PATH
            \ NO_CMAKE_SYSTEM_PATH
            \ NO_DEFAULT_PATH
            \ NO_PACKAGE_ROOT_PATH
            \ NO_SYSTEM_ENVIRONMENT_PATH
            \ ONLY_CMAKE_FIND_ROOT_PATH
            \ PACKAGENAME
            \ PARENT_SCOPE
            \ PATHS
            \ PATH_SUFFIXES
            \ REGISTRY_VIEW
            \ REQUIRED
            \ TARGET
            \ VALIDATOR
            \ VAR

syn keyword cmakeKWfltk_wrap_ui contained
            \ FLTK

syn keyword cmakeKWforeach contained
            \ APPEND
            \ IN
            \ ITEMS
            \ LISTS
            \ RANGE
            \ STATUS
            \ ZIP_LISTS

syn keyword cmakeKWfunction contained
            \ ARGC
            \ ARGN
            \ ARGV
            \ CALL
            \ FOO
            \ PARENT_SCOPE

syn keyword cmakeKWget_cmake_property contained
            \ COMPONENTS
            \ GLOBAL
            \ MACROS
            \ VARIABLES

syn keyword cmakeKWget_directory_property contained
            \ DEFINITION
            \ DIRECTORY
            \ INHERITED

syn keyword cmakeKWget_filename_component contained
            \ ABSOLUTE
            \ BASE_DIR
            \ DIRECTORY
            \ EXT
            \ LAST_EXT
            \ NAME
            \ NAME_WE
            \ NAME_WLE
            \ PROGRAM
            \ PROGRAM_ARGS
            \ QUERY
            \ REALPATH
            \ REAL_PATH
            \ WINDOWS_REGISTRY

syn keyword cmakeKWget_property contained
            \ BRIEF_DOCS
            \ DEFINED
            \ DIRECTORY
            \ FULL_DOCS
            \ GENERATED
            \ GLOBAL
            \ INSTALL
            \ PROPERTY
            \ SET
            \ SOURCE
            \ TARGET
            \ TARGET_DIRECTORY
            \ TEST
            \ VARIABLE

syn keyword cmakeKWget_source_file_property contained
            \ DIRECTORY
            \ GENERATED
            \ INHERITED
            \ LOCATION
            \ TARGET_DIRECTORY

syn keyword cmakeKWget_target_property contained
            \ INHERITED
            \ VAR

syn keyword cmakeKWget_test_property contained
            \ INHERITED
            \ VAR

syn keyword cmakeKWif contained
            \ CMAKE_MATCH_
            \ CMP
            \ COMMAND
            \ COMPARE
            \ DEFINED
            \ EQUAL
            \ EXISTS
            \ FALSE
            \ GREATER
            \ GREATER_EQUAL
            \ IGNORE
            \ IN_LIST
            \ IS_ABSOLUTE
            \ IS_DIRECTORY
            \ IS_NEWER_THAN
            \ IS_SYMLINK
            \ LESS
            \ LESS_EQUAL
            \ MATCHES
            \ NNNN
            \ NOT
            \ OFF
            \ OR
            \ PATH_EQUAL
            \ POLICY
            \ STREQUAL
            \ STRGREATER
            \ STRGREATER_EQUAL
            \ STRLESS
            \ STRLESS_EQUAL
            \ TARGET
            \ TEST
            \ TRUE
            \ VERSION_EQUAL
            \ VERSION_GREATER
            \ VERSION_GREATER_EQUAL
            \ VERSION_LESS
            \ VERSION_LESS_EQUAL
            \ YES

syn keyword cmakeKWinclude contained
            \ NO_POLICY_SCOPE
            \ OPTIONAL
            \ RESULT_VARIABLE

syn keyword cmakeKWinclude_directories contained
            \ AFTER
            \ BEFORE
            \ INCLUDE_DIRECTORIES
            \ ON
            \ SYSTEM

syn keyword cmakeKWinclude_external_msproject contained
            \ GUID
            \ MAP_IMPORTED_CONFIG_
            \ PLATFORM
            \ TYPE
            \ WIX

syn keyword cmakeKWinclude_guard contained
            \ DIRECTORY
            \ GLOBAL
            \ TRUE
            \ __CURRENT_FILE_VAR__

syn keyword cmakeKWinstall contained
            \ AFTER
            \ AIX
            \ ALL_COMPONENTS
            \ APT
            \ ARCHIVE
            \ BEFORE
            \ BUILD_TYPE
            \ BUNDLE
            \ BUNDLE_EXECUTABLE
            \ CMAKE_INSTALL_BINDIR
            \ CMAKE_INSTALL_DATADIR
            \ CMAKE_INSTALL_DATAROOTDIR
            \ CMAKE_INSTALL_DOCDIR
            \ CMAKE_INSTALL_INCLUDEDIR
            \ CMAKE_INSTALL_INFODIR
            \ CMAKE_INSTALL_LIBDIR
            \ CMAKE_INSTALL_LOCALEDIR
            \ CMAKE_INSTALL_LOCALSTATEDIR
            \ CMAKE_INSTALL_MANDIR
            \ CMAKE_INSTALL_MODE
            \ CMAKE_INSTALL_RUNSTATEDIR
            \ CMAKE_INSTALL_SBINDIR
            \ CMAKE_INSTALL_SHARESTATEDIR
            \ CMAKE_INSTALL_SYSCONFDIR
            \ CODE
            \ COMPONENT
            \ CONFIGURATIONS
            \ CVS
            \ CXX_MODULES_BMI
            \ CXX_MODULES_DIRECTORY
            \ DATA
            \ DATAROOT
            \ DBUILD_TYPE
            \ DCOMPONENT
            \ DESTDIR
            \ DESTINATION
            \ DIRECTORY
            \ DIRECTORY_PERMISSIONS
            \ DLL
            \ DOC
            \ ENABLE_EXPORTS
            \ EXCLUDE_FROM_ALL
            \ EXECUTABLES
            \ EXPORT
            \ EXPORT_ANDROID_MK
            \ EXPORT_LINK_INTERFACE_LIBRARIES
            \ EXPORT_NAME
            \ FILES
            \ FILES_MATCHING
            \ FILE_PERMISSIONS
            \ FILE_SET
            \ FRAMEWORK
            \ GET_RUNTIME_DEPENDENCIES
            \ GROUP_EXECUTE
            \ GROUP_READ
            \ GROUP_WRITE
            \ HEADERS
            \ IMPORTED_RUNTIME_ARTIFACTS
            \ INCLUDES
            \ INFO
            \ INSTALL_PREFIX
            \ INTERFACE
            \ INTERFACE_INCLUDE_DIRECTORIES
            \ LIBRARY
            \ LOCALE
            \ LOCALSTATE
            \ MACOSX_BUNDLE
            \ MAN
            \ MESSAGE_NEVER
            \ NAMELINK_COMPONENT
            \ NAMELINK_ONLY
            \ NAMELINK_SKIP
            \ NAMESPACE
            \ NDK
            \ OBJECTS
            \ OPTIONAL
            \ OWNER_EXECUTE
            \ OWNER_READ
            \ OWNER_WRITE
            \ PATTERN
            \ PERMISSIONS
            \ POST_EXCLUDE_FILES
            \ POST_EXCLUDE_REGEXES
            \ POST_INCLUDE_FILES
            \ POST_INCLUDE_REGEXES
            \ POST_INSTALL_SCRIPT
            \ PRE_EXCLUDE_REGEXES
            \ PRE_INCLUDE_REGEXES
            \ PRE_INSTALL_SCRIPT
            \ PRIVATE_HEADER
            \ PROGRAMS
            \ PROPERTIES
            \ PUBLIC_HEADER
            \ RENAME
            \ RESOURCE
            \ RPM
            \ RUNSTATE
            \ RUNTIME_DEPENDENCIES
            \ RUNTIME_DEPENDENCY_SET
            \ SBIN
            \ SCRIPT
            \ SETGID
            \ SETUID
            \ SHAREDSTATE
            \ SOVERSION
            \ STATIC
            \ SYSCONF
            \ TARGETS
            \ TRUE
            \ TYPE
            \ USE_SOURCE_PERMISSIONS
            \ VERSION
            \ WORLD_EXECUTE
            \ WORLD_READ
            \ WORLD_WRITE

syn keyword cmakeKWinstall_files contained
            \ FILES
            \ GLOB

syn keyword cmakeKWinstall_programs contained
            \ FILES
            \ GLOB
            \ PROGRAMS
            \ TARGETS

syn keyword cmakeKWinstall_targets contained
            \ DLL
            \ RUNTIME_DIRECTORY

syn keyword cmakeKWlink_directories contained
            \ AFTER
            \ BEFORE
            \ LINK_DIRECTORIES
            \ ON
            \ ORIGIN
            \ RPATH

syn keyword cmakeKWlist contained
            \ ACTION
            \ APPEND
            \ ASCENDING
            \ CASE
            \ COMPARE
            \ DESCENDING
            \ EXCLUDE
            \ FILE_BASENAME
            \ FILTER
            \ FIND
            \ GENEX_STRIP
            \ GET
            \ INCLUDE
            \ INSENSITIVE
            \ INSERT
            \ INTERNAL
            \ JOIN
            \ LENGTH
            \ NATURAL
            \ ORDER
            \ OUTPUT_VARIABLE
            \ PARENT_SCOPE
            \ POP_BACK
            \ POP_FRONT
            \ PREPEND
            \ REGEX
            \ REMOVE_AT
            \ REMOVE_DUPLICATES
            \ REMOVE_ITEM
            \ REPLACE
            \ REVERSE
            \ SELECTOR
            \ SENSITIVE
            \ SORT
            \ STRING
            \ STRIP
            \ SUBLIST
            \ TOLOWER
            \ TOUPPER
            \ TRANSFORM
            \ TRANSFORM_APPEND
            \ TRANSFORM_GENEX_STRIP
            \ TRANSFORM_REPLACE
            \ TRANSFORM_STRIP
            \ TRANSFORM_TOLOWER

syn keyword cmakeKWload_cache contained
            \ EXCLUDE
            \ INCLUDE_INTERNALS
            \ READ_WITH_PREFIX

syn keyword cmakeKWload_command contained
            \ CMAKE_LOADED_COMMAND_
            \ COMMAND_NAME

syn keyword cmakeKWmacro contained
            \ ARGC
            \ ARGN
            \ ARGV
            \ CALL
            \ DEFINED
            \ FOO
            \ GREATER
            \ LISTS
            \ NOT

syn keyword cmakeKWmark_as_advanced contained
            \ CLEAR
            \ FORCE

syn keyword cmakeKWmath contained
            \ EXPR
            \ HEXADECIMAL
            \ OUTPUT_FORMAT

syn keyword cmakeKWmessage contained
            \ APPEND
            \ AUTHOR_WARNING
            \ CHECK_
            \ CHECK_FAIL
            \ CHECK_PASS
            \ CHECK_START
            \ CONFIGURE_LOG
            \ DEBUG
            \ DEFINED
            \ DEPRECATION
            \ FATAL_ERROR
            \ GET_MESSAGE_LOG_LEVEL
            \ GUI
            \ INTERNAL
            \ MY_CHECK_RESULT
            \ NOTICE
            \ POP_BACK
            \ SEND_ERROR
            \ STATUS
            \ TRACE
            \ VERBOSE
            \ WARNING

syn keyword cmakeKWoption contained
            \ OFF

syn keyword cmakeKWproject contained
            \ ASM
            \ ASM_MARMASM
            \ ASM_MASM
            \ ASM_NASM
            \ ATT
            \ CMAKE_PROJECT_
            \ CUDA
            \ DESCRIPTION
            \ HIP
            \ HOMEPAGE_URL
            \ ISPC
            \ LANGUAGES
            \ NAME
            \ NONE
            \ OBJC
            \ OBJCXX
            \ PROJECT
            \ VERSION
            \ _BINARY_DIR
            \ _DESCRIPTION
            \ _HOMEPAGE_URL
            \ _INCLUDE_BEFORE
            \ _IS_TOP_LEVEL
            \ _SOURCE_DIR
            \ _VERSION
            \ _VERSION_MAJOR
            \ _VERSION_MINOR
            \ _VERSION_PATCH
            \ _VERSION_TWEAK

syn keyword cmakeKWqt_wrap_cpp contained
            \ AUTOMOC

syn keyword cmakeKWqt_wrap_ui contained
            \ AUTOUIC

syn keyword cmakeKWremove contained
            \ VALUE
            \ VAR

syn keyword cmakeKWreturn contained
            \ DEFER
            \ PARENT_SCOPE
            \ PROPAGATE
            \ SCOPE_FOR
            \ VARIABLES
            \ VERSION

syn keyword cmakeKWseparate_arguments contained
            \ MSDN
            \ NATIVE_COMMAND
            \ PROGRAM
            \ SEPARATE_ARGS
            \ UNIX_COMMAND
            \ WINDOWS_COMMAND

syn keyword cmakeKWset contained
            \ BOOL
            \ FILEPATH
            \ FORCE
            \ INTERNAL
            \ OFF
            \ OLD
            \ ON
            \ PARENT_SCOPE
            \ PROPAGATE
            \ STRING
            \ STRINGS
            \ VAR

syn keyword cmakeKWset_directory_properties contained
            \ DIRECTORY
            \ PROPERTIES

syn keyword cmakeKWset_property contained
            \ APPEND
            \ APPEND_STRING
            \ DIRECTORY
            \ GENERATED
            \ GLOBAL
            \ INHERITED
            \ INSTALL
            \ NAME
            \ PROPERTY
            \ SOURCE
            \ TARGET
            \ TARGET_DIRECTORY
            \ TEST
            \ WIX

syn keyword cmakeKWset_source_files_properties contained
            \ DIRECTORY
            \ GENERATED
            \ PROPERTIES
            \ SOURCE
            \ TARGET_DIRECTORY

syn keyword cmakeKWset_target_properties contained
            \ PROPERTIES

syn keyword cmakeKWset_tests_properties contained
            \ NAME
            \ PROPERTIES

syn keyword cmakeKWsite_name contained
            \ HOSTNAME

syn keyword cmakeKWsource_group contained
            \ FILES
            \ PREFIX
            \ REGULAR_EXPRESSION
            \ TREE

syn keyword cmakeKWstring contained
            \ ALPHABET
            \ APPEND
            \ ARRAY
            \ ASCII
            \ BOOLEAN
            \ CMAKE_MATCH_
            \ COMPARE
            \ CONCAT
            \ CONFIGURE
            \ EQUAL
            \ ERROR_VARIABLE
            \ ESCAPE_QUOTES
            \ FIND
            \ GENEX_STRIP
            \ GET
            \ GREATER
            \ GREATER_EQUAL
            \ GUID
            \ HASH
            \ HEX
            \ ISO
            \ JOIN
            \ JSON
            \ LENGTH
            \ LESS
            \ LESS_EQUAL
            \ MAKE_C_IDENTIFIER
            \ MATCH
            \ MATCHALL
            \ MATCHES
            \ MEMBER
            \ NAMESPACE
            \ NOTEQUAL
            \ NULL
            \ NUMBER
            \ OBJECT
            \ OFF
            \ ONLY
            \ PREPEND
            \ RANDOM
            \ RANDOM_SEED
            \ REGEX
            \ REMOVE
            \ REPEAT
            \ REPLACE
            \ REVERSE
            \ RFC
            \ SET
            \ SHA
            \ SOURCE_DATE_EPOCH
            \ STRIP
            \ SUBSTRING
            \ SZ
            \ TIMESTAMP
            \ TOLOWER
            \ TOUPPER
            \ TYPE
            \ US
            \ UTC
            \ UUID

syn keyword cmakeKWsubdirs contained
            \ EXCLUDE_FROM_ALL
            \ PREORDER

syn keyword cmakeKWtarget_compile_definitions contained
            \ ALIAS
            \ COMPILE_DEFINITIONS
            \ FOO
            \ IMPORTED
            \ INTERFACE
            \ INTERFACE_COMPILE_DEFINITIONS
            \ PRIVATE
            \ PUBLIC

syn keyword cmakeKWtarget_compile_features contained
            \ ALIAS
            \ COMPILE_FEATURES
            \ IMPORTED
            \ INTERFACE
            \ INTERFACE_COMPILE_FEATURES
            \ PRIVATE
            \ PUBLIC

syn keyword cmakeKWtarget_compile_options contained
            \ ALIAS
            \ BEFORE
            \ CMAKE_
            \ COMPILE_LANGUAGE
            \ COMPILE_OPTIONS
            \ CONFIG
            \ IMPORTED
            \ INTERFACE
            \ INTERFACE_COMPILE_OPTIONS
            \ PRIVATE
            \ PUBLIC
            \ SHELL
            \ UNIX_COMMAND
            \ _FLAGS
            \ _FLAGS_

syn keyword cmakeKWtarget_include_directories contained
            \ AFTER
            \ ALIAS
            \ BEFORE
            \ BUILD_INTERFACE
            \ IMPORTED
            \ INCLUDE_DIRECTORIES
            \ INSTALL_INTERFACE
            \ INTERFACE
            \ INTERFACE_INCLUDE_DIRECTORIES
            \ INTERFACE_LINK_LIBRARIES
            \ INTERFACE_SYSTEM_INCLUDE_DIRECTORIES
            \ PRIVATE
            \ PUBLIC
            \ SYSTEM

syn keyword cmakeKWtarget_link_directories contained
            \ ALIAS
            \ BEFORE
            \ IMPORTED
            \ INTERFACE
            \ INTERFACE_LINK_DIRECTORIES
            \ LINK_DIRECTORIES
            \ ORIGIN
            \ PRIVATE
            \ PUBLIC
            \ RPATH

syn keyword cmakeKWtarget_link_libraries contained
            \ ALIAS
            \ DA
            \ DAG
            \ DEBUG_CONFIGURATIONS
            \ DOBJ
            \ IMPORTED
            \ IMPORTED_NO_SONAME
            \ INTERFACE
            \ INTERFACE_LINK_LIBRARIES
            \ LINK_FLAGS
            \ LINK_INTERFACE_LIBRARIES
            \ LINK_INTERFACE_LIBRARIES_DEBUG
            \ LINK_INTERFACE_MULTIPLICITY
            \ LINK_OPTIONS
            \ LINK_PRIVATE
            \ LINK_PUBLIC
            \ OBJECT
            \ OLD
            \ PRIVATE
            \ PUBLIC
            \ SHARED
            \ STATIC
            \ TARGET_OBJECTS

syn keyword cmakeKWtarget_link_options contained
            \ ALIAS
            \ BEFORE
            \ CMAKE_
            \ CONFIG
            \ CUDA_RESOLVE_DEVICE_SYMBOLS
            \ CUDA_SEPARABLE_COMPILATION
            \ DEVICE_LINK
            \ GCC
            \ HOST_LINK
            \ IMPORTED
            \ INTERFACE
            \ INTERFACE_LINK_OPTIONS
            \ LANG
            \ LINKER
            \ LINK_OPTIONS
            \ PRIVATE
            \ PUBLIC
            \ SHELL
            \ STATIC_LIBRARY_OPTIONS
            \ UNIX_COMMAND
            \ _FLAGS
            \ _FLAGS_
            \ _LINKER_WRAPPER_FLAG
            \ _LINKER_WRAPPER_FLAG_SEP

syn keyword cmakeKWtarget_precompile_headers contained
            \ ALIAS
            \ ANGLE
            \ BUILD_INTERFACE
            \ COMPILE_LANGUAGE
            \ DISABLE_PRECOMPILE_HEADERS
            \ EXPORT
            \ FI
            \ GCC
            \ IMPORTED
            \ INTERFACE
            \ INTERFACE_PRECOMPILE_HEADERS
            \ PRECOMPILE_HEADERS
            \ PRECOMPILE_HEADERS_REUSE_FROM
            \ PRIVATE
            \ PUBLIC
            \ REUSE_FROM
            \ SKIP_PRECOMPILE_HEADERS

syn keyword cmakeKWtarget_sources contained
            \ ALIAS
            \ BASE_DIRS
            \ BUILD_INTERFACE
            \ CONFIG
            \ CORRECT
            \ CXX_MODULES
            \ CXX_MODULE_DIRS
            \ CXX_MODULE_DIRS_
            \ CXX_MODULE_SETS
            \ CXX_MODULE_SET_
            \ EXPORT
            \ FILES
            \ FILE_SET
            \ FRAMEWORK
            \ HEADERS
            \ HEADER_DIRS
            \ HEADER_DIRS_
            \ HEADER_FILE_ONLY
            \ HEADER_SETS
            \ HEADER_SET_
            \ IMPORTED
            \ INCLUDE_DIRECTORIES
            \ INTERFACE
            \ INTERFACE_CXX_MODULE_SETS
            \ INTERFACE_HEADER_SETS
            \ INTERFACE_INCLUDE_DIRECTORIES
            \ INTERFACE_SOURCES
            \ NAME
            \ PRIVATE
            \ PUBLIC
            \ SOURCES
            \ SOURCE_DIR
            \ TARGETS
            \ TRUE
            \ TYPE
            \ WRONG

syn keyword cmakeKWtry_compile contained
            \ ALL_BUILD
            \ BINARY_DIR
            \ CMAKE_FLAGS
            \ COMPILE_DEFINITIONS
            \ COPY_FILE
            \ COPY_FILE_ERROR
            \ CUDA_EXTENSIONS
            \ CUDA_STANDARD
            \ CUDA_STANDARD_REQUIRED
            \ CXX_EXTENSIONS
            \ CXX_STANDARD
            \ CXX_STANDARD_REQUIRED
            \ C_EXTENSIONS
            \ C_STANDARD
            \ C_STANDARD_REQUIRED
            \ DEFINED
            \ DLINK_LIBRARIES
            \ DVAR
            \ EXECUTABLE
            \ FALSE
            \ GHS
            \ HIP_EXTENSIONS
            \ HIP_STANDARD
            \ HIP_STANDARD_REQUIRED
            \ INCLUDE_DIRECTORIES
            \ LANG
            \ LINK_DIRECTORIES
            \ LINK_LIBRARIES
            \ LINK_OPTIONS
            \ LOG_DESCRIPTION
            \ MULTI
            \ NOT
            \ NO_CACHE
            \ NO_LOG
            \ OBJCXX_EXTENSIONS
            \ OBJCXX_STANDARD
            \ OBJCXX_STANDARD_REQUIRED
            \ OBJC_EXTENSIONS
            \ OBJC_STANDARD
            \ OBJC_STANDARD_REQUIRED
            \ OUTPUT_VARIABLE
            \ PRIVATE
            \ PROJECT
            \ RESULTVAR
            \ SOURCES
            \ SOURCE_DIR
            \ SOURCE_FROM_CONTENT
            \ SOURCE_FROM_FILE
            \ SOURCE_FROM_VAR
            \ STATIC_LIBRARY
            \ STATIC_LIBRARY_OPTIONS
            \ TARGET
            \ TRUE
            \ TYPE
            \ VALUE
            \ _EXTENSIONS
            \ _STANDARD
            \ _STANDARD_REQUIRED

syn keyword cmakeKWtry_run contained
            \ ARGS
            \ CMAKE_FLAGS
            \ COMPILE_DEFINITIONS
            \ COMPILE_OUTPUT_VARIABLE
            \ COPY_FILE
            \ COPY_FILE_ERROR
            \ FAILED_TO_RUN
            \ FALSE
            \ LANG
            \ LINK_LIBRARIES
            \ LINK_OPTIONS
            \ LOG_DESCRIPTION
            \ NO_CACHE
            \ NO_LOG
            \ RUN_OUTPUT_STDERR_VARIABLE
            \ RUN_OUTPUT_STDOUT_VARIABLE
            \ RUN_OUTPUT_VARIABLE
            \ SOURCES
            \ SOURCE_FROM_CONTENT
            \ SOURCE_FROM_FILE
            \ SOURCE_FROM_VAR
            \ TRUE
            \ WORKING_DIRECTORY
            \ _EXTENSIONS
            \ _STANDARD
            \ _STANDARD_REQUIRED
            \ __TRYRUN_OUTPUT

syn keyword cmakeKWunset contained
            \ PARENT_SCOPE
            \ VAR

syn keyword cmakeKWuse_mangled_mesa contained
            \ GL
            \ OUTPUT_DIRECTORY
            \ PATH_TO_MESA

syn keyword cmakeKWvariable_requires contained
            \ RESULT_VARIABLE
            \ TEST_VARIABLE

syn keyword cmakeKWvariable_watch contained
            \ APPEND
            \ COMMAND
            \ DEFINED
            \ MODIFIED_ACCESS
            \ READ_ACCESS
            \ REMOVED_ACCESS
            \ UNKNOWN_MODIFIED_ACCESS
            \ UNKNOWN_READ_ACCESS

syn keyword cmakeKWwrite_file contained
            \ APPEND
            \ CONFIGURE_FILE
            \ NOTE
            \ WRITE


syn keyword cmakeGeneratorExpressions contained
            \ ABSOLUTE_PATH
            \ ACTION
            \ AIX
            \ ANGLE
            \ APPEND
            \ ARCHIVE_OUTPUT_NAME
            \ ARCHIVE_OUTPUT_NAME_
            \ ASCENDING
            \ BAR
            \ BOOL
            \ BUILD_INTERFACE
            \ BUILD_LOCAL_INTERFACE
            \ CMAKE_LINK_GROUP_USING_
            \ CMAKE_LINK_LIBRARY_USING_
            \ CMAKE_PATH
            \ CODE
            \ COMMAND_CONFIG
            \ COMMAND_EXPAND_LISTS
            \ COMPARE
            \ COMPILE_DEFINITIONS
            \ COMPILE_FEATURES
            \ COMPILE_LANGUAGE
            \ COMPILE_LANG_AND_ID
            \ COMPILE_ONLY
            \ COMPILING_CUDA
            \ COMPILING_CXX
            \ COMPILING_CXX_WITH_CLANG
            \ COMPILING_CXX_WITH_INTEL
            \ COMPILING_C_WITH_CLANG
            \ CONFIG
            \ CONFIGURATION
            \ CONTENT
            \ CUDA_COMPILER_ID
            \ CUDA_COMPILER_VERSION
            \ CUDA_RESOLVE_DEVICE_SYMBOLS
            \ CUDA_SEPARABLE_COMPILATION
            \ CUSTOM_KEYS
            \ CXX_COMPILER_ID
            \ CXX_COMPILER_VERSION
            \ CXX_CONFIG
            \ CXX_STANDARD
            \ C_COMPILER_ID
            \ C_COMPILER_VERSION
            \ C_STANDARD
            \ DEBUG_MODE
            \ DEBUG_POSTFIX
            \ DENABLE_SOME_FEATURE
            \ DESCENDING
            \ DEVICE_LINK
            \ DLL
            \ ENABLE_EXPORTS
            \ EXCLUDE
            \ EXPORT
            \ EXTENSION_DEF
            \ FALSE
            \ FILENAME_DEF
            \ FILE_BASENAME
            \ FILTER
            \ FIND
            \ FOO_EXTRA_THINGS
            \ GENERATE
            \ GENEX_EVAL
            \ GET_EXTENSION
            \ GET_FILENAME
            \ GET_PARENT_PATH
            \ GET_RELATIVE_PART
            \ GET_ROOT_DIRECTORY
            \ GET_ROOT_NAME
            \ GET_ROOT_PATH
            \ GET_STEM
            \ HAS_
            \ HAS_EXTENSION
            \ HAS_FILENAME
            \ HAS_PARENT_PATH
            \ HAS_RELATIVE_PART
            \ HAS_ROOT_DIRECTORY
            \ HAS_ROOT_NAME
            \ HAS_ROOT_PATH
            \ HAS_STEM
            \ HAVE_SOME_FEATURE
            \ HIP_COMPILER_ID
            \ HIP_COMPILER_VERSION
            \ HIP_STANDARD
            \ HOST_LINK
            \ IF
            \ IGNORE
            \ IMPORTED_LOCATION
            \ IMPORT_PREFIX
            \ IMPORT_SUFFIX
            \ INCLUDE_DIRECTORIES
            \ INSENSITIVE
            \ INSERT
            \ INSTALL_INTERFACE
            \ INSTALL_NAME_DIR
            \ INSTALL_PREFIX
            \ INSTALL_RPATH
            \ INTERFACE_LINK_LIBRARIES
            \ INTERFACE_LINK_LIBRARIES_DIRECT
            \ IN_LIST
            \ ISPC_COMPILER_ID
            \ ISPC_COMPILER_VERSION
            \ IS_ABSOLUTE
            \ IS_PREFIX
            \ IS_RELATIVE
            \ JOIN
            \ LANG
            \ LANG_COMPILER_ID
            \ LAST_ONLY
            \ LENGTH
            \ LIBRARY_OUTPUT_NAME
            \ LIBRARY_OUTPUT_NAME_
            \ LINK_GROUP
            \ LINK_GROUP_PREDEFINED_FEATURES
            \ LINK_LANGUAGE
            \ LINK_LANG_AND_ID
            \ LINK_LIBRARIES
            \ LINK_LIBRARY
            \ LINK_LIBRARY_OVERRIDE
            \ LINK_LIBRARY_OVERRIDE_
            \ LINK_LIBRARY_PREDEFINED_FEATURES
            \ LINK_ONLY
            \ LOWER_CASE
            \ MAKE_C_IDENTIFIER
            \ MAP_IMPORTED_CONFIG_
            \ MODULE
            \ NATURAL
            \ NO
            \ NORMALIZE
            \ NORMAL_PATH
            \ NOT
            \ OBJCXX_COMPILER_ID
            \ OBJCXX_COMPILER_VERSION
            \ OBJC_COMPILER_ID
            \ OBJC_COMPILER_VERSION
            \ OBJECT
            \ OFF
            \ OLD_COMPILER
            \ ORDER
            \ OUTPUT
            \ OUTPUT_CONFIG
            \ OUTPUT_NAME
            \ OUTPUT_NAME_
            \ PATH
            \ PATH_EQUAL
            \ PDB_NAME
            \ PDB_NAME_
            \ PDB_OUTPUT_DIRECTORY
            \ PDB_OUTPUT_DIRECTORY_
            \ PLATFORM_ID
            \ POP_BACK
            \ POP_FRONT
            \ POSIX
            \ POST_BUILD
            \ PREPEND
            \ PRIVATE
            \ PUBLIC
            \ REGEX
            \ RELATIVE_PATH
            \ REMOVE_AT
            \ REMOVE_DUPLICATES
            \ REMOVE_EXTENSION
            \ REMOVE_FILENAME
            \ REMOVE_ITEM
            \ REPLACE
            \ REPLACE_EXTENSION
            \ REPLACE_FILENAME
            \ REQUIRED
            \ RESCAN
            \ REVERSE
            \ RPATH
            \ RUNTIME_DEPENDENCY_SET
            \ RUNTIME_OUTPUT_NAME
            \ RUNTIME_OUTPUT_NAME_
            \ SCRIPT
            \ SDK
            \ SELECTOR
            \ SEMICOLON
            \ SENSITIVE
            \ SHARED
            \ SHELL_PATH
            \ SORT
            \ STATIC
            \ STREQUAL
            \ STRING
            \ STRIP
            \ SUBLIST
            \ TARGET_BUNDLE_CONTENT_DIR
            \ TARGET_BUNDLE_DIR
            \ TARGET_BUNDLE_DIR_NAME
            \ TARGET_EXISTS
            \ TARGET_FILE
            \ TARGET_FILE_BASE_NAME
            \ TARGET_FILE_DIR
            \ TARGET_FILE_NAME
            \ TARGET_FILE_PREFIX
            \ TARGET_FILE_SUFFIX
            \ TARGET_GENEX_EVAL
            \ TARGET_IMPORT_FILE
            \ TARGET_IMPORT_FILE_BASE_NAME
            \ TARGET_IMPORT_FILE_DIR
            \ TARGET_IMPORT_FILE_NAME
            \ TARGET_IMPORT_FILE_PREFIX
            \ TARGET_IMPORT_FILE_SUFFIX
            \ TARGET_LINKER_FILE
            \ TARGET_LINKER_FILE_BASE_NAME
            \ TARGET_LINKER_FILE_DIR
            \ TARGET_LINKER_FILE_NAME
            \ TARGET_LINKER_FILE_PREFIX
            \ TARGET_LINKER_FILE_SUFFIX
            \ TARGET_LINKER_IMPORT_FILE
            \ TARGET_LINKER_IMPORT_FILE_BASE_NAME
            \ TARGET_LINKER_IMPORT_FILE_DIR
            \ TARGET_LINKER_IMPORT_FILE_NAME
            \ TARGET_LINKER_IMPORT_FILE_PREFIX
            \ TARGET_LINKER_IMPORT_FILE_SUFFIX
            \ TARGET_LINKER_LIBRARY_FILE
            \ TARGET_LINKER_LIBRARY_FILE_BASE_NAME
            \ TARGET_LINKER_LIBRARY_FILE_DIR
            \ TARGET_LINKER_LIBRARY_FILE_NAME
            \ TARGET_LINKER_LIBRARY_FILE_PREFIX
            \ TARGET_LINKER_LIBRARY_FILE_SUFFIX
            \ TARGET_NAME_IF_EXISTS
            \ TARGET_OBJECTS
            \ TARGET_PDB_FILE
            \ TARGET_PDB_FILE_BASE_NAME
            \ TARGET_PDB_FILE_DIR
            \ TARGET_PDB_FILE_NAME
            \ TARGET_POLICY
            \ TARGET_PROPERTY
            \ TARGET_RUNTIME_DLLS
            \ TARGET_RUNTIME_DLL_DIRS
            \ TARGET_SONAME_FILE
            \ TARGET_SONAME_FILE_DIR
            \ TARGET_SONAME_FILE_NAME
            \ TARGET_SONAME_IMPORT_FILE
            \ TARGET_SONAME_IMPORT_FILE_DIR
            \ TARGET_SONAME_IMPORT_FILE_NAME
            \ TOLOWER
            \ TOUPPER
            \ TRANSFORM
            \ TRANSFORM_APPEND
            \ TRANSFORM_REPLACE
            \ TRANSFORM_STRIP
            \ TRANSFORM_TOLOWER
            \ UNKNOWN
            \ UPPER_CASE
            \ VERBATIM
            \ VERSION_EQUAL
            \ VERSION_GREATER_EQUAL
            \ VERSION_LESS
            \ VERSION_LESS_EQUAL
            \ WHOLE_ARCHIVE
            \ WRONG
            \ _LINK_GROUP_USING_
            \ _LINK_LIBRARY_USING_
            \ _POSTFIX
            \ _SUPPORTED

syn case ignore

syn keyword cmakeCommand
            \ add_compile_definitions
            \ add_compile_options
            \ add_custom_command
            \ add_custom_target
            \ add_definitions
            \ add_dependencies
            \ add_executable
            \ add_library
            \ add_link_options
            \ add_subdirectory
            \ add_test
            \ aux_source_directory
            \ block
            \ break
            \ build_command
            \ cmake_file_api
            \ cmake_host_system_information
            \ cmake_language
            \ cmake_minimum_required
            \ cmake_parse_arguments
            \ cmake_path
            \ cmake_policy
            \ configure_file
            \ continue
            \ create_test_sourcelist
            \ ctest_build
            \ ctest_configure
            \ ctest_coverage
            \ ctest_empty_binary_directory
            \ ctest_memcheck
            \ ctest_read_custom_files
            \ ctest_run_script
            \ ctest_sleep
            \ ctest_start
            \ ctest_submit
            \ ctest_test
            \ ctest_update
            \ ctest_upload
            \ define_property
            \ enable_language
            \ enable_testing
            \ endblock
            \ endfunction
            \ endmacro
            \ execute_process
            \ export
            \ file
            \ find_file
            \ find_library
            \ find_package
            \ find_path
            \ find_program
            \ fltk_wrap_ui
            \ function
            \ get_cmake_property
            \ get_directory_property
            \ get_filename_component
            \ get_property
            \ get_source_file_property
            \ get_target_property
            \ get_test_property
            \ include
            \ include_directories
            \ include_external_msproject
            \ include_guard
            \ include_regular_expression
            \ install
            \ link_directories
            \ list
            \ load_cache
            \ load_command
            \ macro
            \ mark_as_advanced
            \ math
            \ message
            \ option
            \ project
            \ qt_wrap_cpp
            \ qt_wrap_ui
            \ remove_definitions
            \ return
            \ separate_arguments
            \ set
            \ set_directory_properties
            \ set_property
            \ set_source_files_properties
            \ set_target_properties
            \ set_tests_properties
            \ site_name
            \ source_group
            \ string
            \ target_compile_definitions
            \ target_compile_features
            \ target_compile_options
            \ target_include_directories
            \ target_link_directories
            \ target_link_libraries
            \ target_link_options
            \ target_precompile_headers
            \ target_sources
            \ try_compile
            \ try_run
            \ unset
            \ variable_watch
            \ nextgroup=cmakeArguments

syn keyword cmakeCommandConditional
            \ else
            \ elseif
            \ endif
            \ if
            \ nextgroup=cmakeArguments

syn keyword cmakeCommandRepeat
            \ endforeach
            \ endwhile
            \ foreach
            \ while
            \ nextgroup=cmakeArguments

syn keyword cmakeCommandDeprecated
            \ build_name
            \ exec_program
            \ export_library_dependencies
            \ install_files
            \ install_programs
            \ install_targets
            \ link_libraries
            \ make_directory
            \ output_required_files
            \ remove
            \ subdir_depends
            \ subdirs
            \ use_mangled_mesa
            \ utility_source
            \ variable_requires
            \ write_file
            \ nextgroup=cmakeArguments

syn case match

syn keyword cmakeTodo
            \ TODO FIXME XXX
            \ contained

hi def link cmakeBracketArgument String
hi def link cmakeBracketComment Comment
hi def link cmakeCommand Function
hi def link cmakeCommandConditional Conditional
hi def link cmakeCommandDeprecated WarningMsg
hi def link cmakeCommandRepeat Repeat
hi def link cmakeComment Comment
hi def link cmakeEnvironment Special
hi def link cmakeEscaped Special
hi def link cmakeGeneratorExpression WarningMsg
hi def link cmakeGeneratorExpressions Constant
hi def link cmakeModule Include
hi def link cmakeProperty Constant
hi def link cmakeRegistry Underlined
hi def link cmakeString String
hi def link cmakeTodo TODO
hi def link cmakeVariableValue Type
hi def link cmakeVariable Identifier

hi def link cmakeKWExternalProject ModeMsg
hi def link cmakeKWFetchContent ModeMsg
hi def link cmakeKWadd_compile_definitions ModeMsg
hi def link cmakeKWadd_compile_options ModeMsg
hi def link cmakeKWadd_custom_command ModeMsg
hi def link cmakeKWadd_custom_target ModeMsg
hi def link cmakeKWadd_definitions ModeMsg
hi def link cmakeKWadd_dependencies ModeMsg
hi def link cmakeKWadd_executable ModeMsg
hi def link cmakeKWadd_library ModeMsg
hi def link cmakeKWadd_link_options ModeMsg
hi def link cmakeKWadd_subdirectory ModeMsg
hi def link cmakeKWadd_test ModeMsg
hi def link cmakeKWblock ModeMsg
hi def link cmakeKWbuild_command ModeMsg
hi def link cmakeKWcmake_file_api ModeMsg
hi def link cmakeKWcmake_host_system_information ModeMsg
hi def link cmakeKWcmake_language ModeMsg
hi def link cmakeKWcmake_minimum_required ModeMsg
hi def link cmakeKWcmake_parse_arguments ModeMsg
hi def link cmakeKWcmake_path ModeMsg
hi def link cmakeKWcmake_policy ModeMsg
hi def link cmakeKWconfigure_file ModeMsg
hi def link cmakeKWcreate_test_sourcelist ModeMsg
hi def link cmakeKWctest_build ModeMsg
hi def link cmakeKWctest_configure ModeMsg
hi def link cmakeKWctest_coverage ModeMsg
hi def link cmakeKWctest_memcheck ModeMsg
hi def link cmakeKWctest_run_script ModeMsg
hi def link cmakeKWctest_start ModeMsg
hi def link cmakeKWctest_submit ModeMsg
hi def link cmakeKWctest_test ModeMsg
hi def link cmakeKWctest_update ModeMsg
hi def link cmakeKWctest_upload ModeMsg
hi def link cmakeKWdefine_property ModeMsg
hi def link cmakeKWdoxygen_add_docs ModeMsg
hi def link cmakeKWenable_language ModeMsg
hi def link cmakeKWenable_testing ModeMsg
hi def link cmakeKWexec_program ModeMsg
hi def link cmakeKWexecute_process ModeMsg
hi def link cmakeKWexport ModeMsg
hi def link cmakeKWexport_library_dependencies ModeMsg
hi def link cmakeKWfile ModeMsg
hi def link cmakeKWfind_file ModeMsg
hi def link cmakeKWfind_library ModeMsg
hi def link cmakeKWfind_package ModeMsg
hi def link cmakeKWfind_path ModeMsg
hi def link cmakeKWfind_program ModeMsg
hi def link cmakeKWfltk_wrap_ui ModeMsg
hi def link cmakeKWforeach ModeMsg
hi def link cmakeKWfunction ModeMsg
hi def link cmakeKWget_cmake_property ModeMsg
hi def link cmakeKWget_directory_property ModeMsg
hi def link cmakeKWget_filename_component ModeMsg
hi def link cmakeKWget_property ModeMsg
hi def link cmakeKWget_source_file_property ModeMsg
hi def link cmakeKWget_target_property ModeMsg
hi def link cmakeKWget_test_property ModeMsg
hi def link cmakeKWif ModeMsg
hi def link cmakeKWinclude ModeMsg
hi def link cmakeKWinclude_directories ModeMsg
hi def link cmakeKWinclude_external_msproject ModeMsg
hi def link cmakeKWinclude_guard ModeMsg
hi def link cmakeKWinstall ModeMsg
hi def link cmakeKWinstall_files ModeMsg
hi def link cmakeKWinstall_programs ModeMsg
hi def link cmakeKWinstall_targets ModeMsg
hi def link cmakeKWlink_directories ModeMsg
hi def link cmakeKWlist ModeMsg
hi def link cmakeKWload_cache ModeMsg
hi def link cmakeKWload_command ModeMsg
hi def link cmakeKWmacro ModeMsg
hi def link cmakeKWmark_as_advanced ModeMsg
hi def link cmakeKWmath ModeMsg
hi def link cmakeKWmessage ModeMsg
hi def link cmakeKWoption ModeMsg
hi def link cmakeKWproject ModeMsg
hi def link cmakeKWqt_wrap_cpp ModeMsg
hi def link cmakeKWqt_wrap_ui ModeMsg
hi def link cmakeKWremove ModeMsg
hi def link cmakeKWreturn ModeMsg
hi def link cmakeKWseparate_arguments ModeMsg
hi def link cmakeKWset ModeMsg
hi def link cmakeKWset_directory_properties ModeMsg
hi def link cmakeKWset_property ModeMsg
hi def link cmakeKWset_source_files_properties ModeMsg
hi def link cmakeKWset_target_properties ModeMsg
hi def link cmakeKWset_tests_properties ModeMsg
hi def link cmakeKWsite_name ModeMsg
hi def link cmakeKWsource_group ModeMsg
hi def link cmakeKWstring ModeMsg
hi def link cmakeKWsubdirs ModeMsg
hi def link cmakeKWtarget_compile_definitions ModeMsg
hi def link cmakeKWtarget_compile_features ModeMsg
hi def link cmakeKWtarget_compile_options ModeMsg
hi def link cmakeKWtarget_include_directories ModeMsg
hi def link cmakeKWtarget_link_directories ModeMsg
hi def link cmakeKWtarget_link_libraries ModeMsg
hi def link cmakeKWtarget_link_options ModeMsg
hi def link cmakeKWtarget_precompile_headers ModeMsg
hi def link cmakeKWtarget_sources ModeMsg
hi def link cmakeKWtry_compile ModeMsg
hi def link cmakeKWtry_run ModeMsg
hi def link cmakeKWunset ModeMsg
hi def link cmakeKWuse_mangled_mesa ModeMsg
hi def link cmakeKWvariable_requires ModeMsg
hi def link cmakeKWvariable_watch ModeMsg
hi def link cmakeKWwrite_file ModeMsg

" Manually added - difficult to parse out of documentation
syn case ignore

syn keyword cmakeCommandManuallyAdded
            \ configure_package_config_file write_basic_package_version_file
            \ nextgroup=cmakeArguments

syn case match

syn keyword cmakeKWconfigure_package_config_file contained
            \ INSTALL_DESTINATION PATH_VARS NO_SET_AND_CHECK_MACRO NO_CHECK_REQUIRED_COMPONENTS_MACRO INSTALL_PREFIX

syn keyword cmakeKWconfigure_package_config_file_constants contained
            \ AnyNewerVersion SameMajorVersion SameMinorVersion ExactVersion

syn keyword cmakeKWwrite_basic_package_version_file contained
            \ VERSION COMPATIBILITY

hi def link cmakeCommandManuallyAdded Function

hi def link cmakeKWconfigure_package_config_file ModeMsg
hi def link cmakeKWwrite_basic_package_version_file ModeMsg
hi def link cmakeKWconfigure_package_config_file_constants Constant

let b:current_syntax = "cmake"

let &cpo = s:keepcpo
unlet s:keepcpo

" vim: set nowrap:
