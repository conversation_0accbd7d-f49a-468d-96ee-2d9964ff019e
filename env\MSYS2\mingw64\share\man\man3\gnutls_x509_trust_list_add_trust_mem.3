.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_trust_list_add_trust_mem" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_trust_list_add_trust_mem \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_trust_list_add_trust_mem(gnutls_x509_trust_list_t " list ", const gnutls_datum_t * " cas ", const gnutls_datum_t * " crls ", gnutls_x509_crt_fmt_t " type ", unsigned int " tl_flags ", unsigned int " tl_vflags ");"
.SH ARGUMENTS
.IP "gnutls_x509_trust_list_t list" 12
The list
.IP "const gnutls_datum_t * cas" 12
A buffer containing a list of CAs (optional)
.IP "const gnutls_datum_t * crls" 12
A buffer containing a list of CRLs (optional)
.IP "gnutls_x509_crt_fmt_t type" 12
The format of the certificates
.IP "unsigned int tl_flags" 12
flags from \fBgnutls_trust_list_flags_t\fP
.IP "unsigned int tl_vflags" 12
gnutls_certificate_verify_flags if flags specifies GNUTLS_TL_VERIFY_CRL
.SH "DESCRIPTION"
This function will add the given certificate authorities
to the trusted list. 

If this function is used \fBgnutls_x509_trust_list_deinit()\fP must be called
with parameter  \fIall\fP being 1.
.SH "RETURNS"
The number of added elements is returned.
.SH "SINCE"
3.1
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
