/*** Autogenerated by WIDL 10.8 from include/mfmediaengine.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __mfmediaengine_h__
#define __mfmediaengine_h__

/* Forward declarations */

#ifndef __IMFMediaError_FWD_DEFINED__
#define __IMFMediaError_FWD_DEFINED__
typedef interface IMFMediaError IMFMediaError;
#ifdef __cplusplus
interface IMFMediaError;
#endif /* __cplusplus */
#endif

#ifndef __IMFMediaEngineSrcElements_FWD_DEFINED__
#define __IMFMediaEngineSrcElements_FWD_DEFINED__
typedef interface IMFMediaEngineSrcElements IMFMediaEngineSrcElements;
#ifdef __cplusplus
interface IMFMediaEngineSrcElements;
#endif /* __cplusplus */
#endif

#ifndef __IMFMediaTimeRange_FWD_DEFINED__
#define __IMFMediaTimeRange_FWD_DEFINED__
typedef interface IMFMediaTimeRange IMFMediaTimeRange;
#ifdef __cplusplus
interface IMFMediaTimeRange;
#endif /* __cplusplus */
#endif

#ifndef __IMFMediaEngine_FWD_DEFINED__
#define __IMFMediaEngine_FWD_DEFINED__
typedef interface IMFMediaEngine IMFMediaEngine;
#ifdef __cplusplus
interface IMFMediaEngine;
#endif /* __cplusplus */
#endif

#ifndef __IMFMediaEngineEx_FWD_DEFINED__
#define __IMFMediaEngineEx_FWD_DEFINED__
typedef interface IMFMediaEngineEx IMFMediaEngineEx;
#ifdef __cplusplus
interface IMFMediaEngineEx;
#endif /* __cplusplus */
#endif

#ifndef __IMFMediaEngineClassFactory_FWD_DEFINED__
#define __IMFMediaEngineClassFactory_FWD_DEFINED__
typedef interface IMFMediaEngineClassFactory IMFMediaEngineClassFactory;
#ifdef __cplusplus
interface IMFMediaEngineClassFactory;
#endif /* __cplusplus */
#endif

#ifndef __IMFMediaEngineNotify_FWD_DEFINED__
#define __IMFMediaEngineNotify_FWD_DEFINED__
typedef interface IMFMediaEngineNotify IMFMediaEngineNotify;
#ifdef __cplusplus
interface IMFMediaEngineNotify;
#endif /* __cplusplus */
#endif

#ifndef __IMFMediaEngineAudioEndpointId_FWD_DEFINED__
#define __IMFMediaEngineAudioEndpointId_FWD_DEFINED__
typedef interface IMFMediaEngineAudioEndpointId IMFMediaEngineAudioEndpointId;
#ifdef __cplusplus
interface IMFMediaEngineAudioEndpointId;
#endif /* __cplusplus */
#endif

#ifndef __IMFMediaEngineExtension_FWD_DEFINED__
#define __IMFMediaEngineExtension_FWD_DEFINED__
typedef interface IMFMediaEngineExtension IMFMediaEngineExtension;
#ifdef __cplusplus
interface IMFMediaEngineExtension;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <mfidl.h>

#ifdef __cplusplus
extern "C" {
#endif

EXTERN_GUID(CLSID_MFMediaEngineClassFactory, 0xb44392da, 0x499b, 0x446b, 0xa4, 0xcb, 0x00, 0x5f, 0xea, 0xd0, 0xe6, 0xd5);
EXTERN_GUID(MF_MEDIA_ENGINE_AUDIO_CATEGORY,                     0xc8d4c51d, 0x350e, 0x41f2, 0xba, 0x46, 0xfa, 0xeb, 0xbb, 0x08, 0x57, 0xf6);
EXTERN_GUID(MF_MEDIA_ENGINE_AUDIO_ENDPOINT_ROLE,                0xd2cb93d1, 0x116a, 0x44f2, 0x93, 0x85, 0xf7, 0xd0, 0xfd, 0xa2, 0xfb, 0x46);
EXTERN_GUID(MF_MEDIA_ENGINE_BROWSER_COMPATIBILITY_MODE,         0x4e0212e2, 0xe18f, 0x41e1, 0x95, 0xe5, 0xc0, 0xe7, 0xe9, 0x23, 0x5b, 0xc3);
EXTERN_GUID(MF_MEDIA_ENGINE_BROWSER_COMPATIBILITY_MODE_IE9,     0x052c2d39, 0x40c0, 0x4188, 0xab, 0x86, 0xf8, 0x28, 0x27, 0x3b, 0x75, 0x22);
EXTERN_GUID(MF_MEDIA_ENGINE_BROWSER_COMPATIBILITY_MODE_IE10,    0x11a47afd, 0x6589, 0x4124, 0xb3, 0x12, 0x61, 0x58, 0xec, 0x51, 0x7f, 0xc3);
EXTERN_GUID(MF_MEDIA_ENGINE_BROWSER_COMPATIBILITY_MODE_IE11,    0x1cf1315f, 0xce3f, 0x4035, 0x93, 0x91, 0x16, 0x14, 0x2f, 0x77, 0x51, 0x89);
EXTERN_GUID(MF_MEDIA_ENGINE_BROWSER_COMPATIBILITY_MODE_IE_EDGE, 0xa6f3e465, 0x3aca, 0x442c, 0xa3, 0xf0, 0xad, 0x6d, 0xda, 0xd8, 0x39, 0xae);
EXTERN_GUID(MF_MEDIA_ENGINE_CALLBACK,                           0xc60381b8, 0x83a4, 0x41f8, 0xa3, 0xd0, 0xde, 0x05, 0x07, 0x68, 0x49, 0xa9);
EXTERN_GUID(MF_MEDIA_ENGINE_COMPATIBILITY_MODE,                 0x3ef26ad4, 0xdc54, 0x45de, 0xb9, 0xaf, 0x76, 0xc8, 0xc6, 0x6b, 0xfa, 0x8e);
EXTERN_GUID(MF_MEDIA_ENGINE_COMPATIBILITY_MODE_WIN10,           0x5b25e089, 0x6ca7, 0x4139, 0xa2, 0xcb, 0xfc, 0xaa, 0xb3, 0x95, 0x52, 0xa3);
EXTERN_GUID(MF_MEDIA_ENGINE_COMPATIBILITY_MODE_WWA_EDGE,        0x15b29098, 0x9f01, 0x4e4d, 0xb6, 0x5a, 0xc0, 0x6c, 0x6c, 0x89, 0xda, 0x2a);
EXTERN_GUID(MF_MEDIA_ENGINE_CONTENT_PROTECTION_FLAGS,           0xe0350223, 0x5aaf, 0x4d76, 0xa7, 0xc3, 0x06, 0xde, 0x70, 0x89, 0x4d, 0xb4);
EXTERN_GUID(MF_MEDIA_ENGINE_CONTENT_PROTECTION_MANAGER,         0xfdd6dfaa, 0xbd85, 0x4af3, 0x9e, 0x0f, 0xa0, 0x1d, 0x53, 0x9d, 0x87, 0x6a);
EXTERN_GUID(MF_MEDIA_ENGINE_CONTINUE_ON_CODEC_ERROR,            0xdbcdb7f9, 0x48e4, 0x4295, 0xb7, 0x0d, 0xd5, 0x18, 0x23, 0x4e, 0xeb, 0x38);
EXTERN_GUID(MF_MEDIA_ENGINE_COREWINDOW,                         0xfccae4dc, 0x0b7f, 0x41c2, 0x9f, 0x96, 0x46, 0x59, 0x94, 0x8a, 0xcd, 0xdc);
EXTERN_GUID(MF_MEDIA_ENGINE_DXGI_MANAGER,                       0x065702da, 0x1094, 0x486d, 0x86, 0x17, 0xee, 0x7c, 0xc4, 0xee, 0x46, 0x48);
EXTERN_GUID(MF_MEDIA_ENGINE_EME_CALLBACK,                       0x494553a7, 0xa481, 0x4cb7, 0xbe, 0xc5, 0x38, 0x09, 0x03, 0x51, 0x37, 0x31);
EXTERN_GUID(MF_MEDIA_ENGINE_EXTENSION,                          0x3109fd46, 0x060d, 0x4b62, 0x8d, 0xcf, 0xfa, 0xff, 0x81, 0x13, 0x18, 0xd2);
EXTERN_GUID(MF_MEDIA_ENGINE_MEDIA_PLAYER_MODE,                  0x3ddd8d45, 0x5aa1, 0x4112, 0x82, 0xe5, 0x36, 0xf6, 0xa2, 0x19, 0x7e, 0x6e);
EXTERN_GUID(MF_MEDIA_ENGINE_NEEDKEY_CALLBACK,                   0x7ea80843, 0xb6e4, 0x432c, 0x8e, 0xa4, 0x78, 0x48, 0xff, 0xe4, 0x22, 0x0e);
EXTERN_GUID(MF_MEDIA_ENGINE_OPM_HWND,                           0xa0be8ee7, 0x0572, 0x4f2c, 0xa8, 0x01, 0x2a, 0x15, 0x1b, 0xd3, 0xe7, 0x26);
EXTERN_GUID(MF_MEDIA_ENGINE_PLAYBACK_HWND,                      0xd988879b, 0x67c9, 0x4d92, 0xba, 0xa7, 0x6e, 0xad, 0xd4, 0x46, 0x03, 0x9d);
EXTERN_GUID(MF_MEDIA_ENGINE_PLAYBACK_VISUAL,                    0x6debd26f, 0x6ab9, 0x4d7e, 0xb0, 0xee, 0xc6, 0x1a, 0x73, 0xff, 0xad, 0x15);
EXTERN_GUID(MF_MEDIA_ENGINE_SOURCE_RESOLVER_CONFIG_STORE,       0x0ac0c497, 0xb3c4, 0x48c9, 0x9c, 0xde, 0xbb, 0x8c, 0xa2, 0x44, 0x2c, 0xa3);
EXTERN_GUID(MF_MEDIA_ENGINE_STREAM_CONTAINS_ALPHA_CHANNEL,      0x5cbfaf44, 0xd2b2, 0x4cfb, 0x80, 0xa7, 0xd4, 0x29, 0xc7, 0x4c, 0x78, 0x9d);
EXTERN_GUID(MF_MEDIA_ENGINE_SYNCHRONOUS_CLOSE,                  0xc3c2e12f, 0x7e0e, 0x4e43, 0xb9, 0x1c, 0xdc, 0x99, 0x2c, 0xcd, 0xfa, 0x5e);
EXTERN_GUID(MF_MEDIA_ENGINE_TELEMETRY_APPLICATION_ID,           0x1e7b273b, 0xa7e4, 0x402a, 0x8f, 0x51, 0xc4, 0x8e, 0x88, 0xa2, 0xca, 0xbc);
EXTERN_GUID(MF_MEDIA_ENGINE_TRACK_ID,                           0x65bea312, 0x4043, 0x4815, 0x8e, 0xab, 0x44, 0xdc, 0xe2, 0xef, 0x8f, 0x2a);
EXTERN_GUID(MF_MEDIA_ENGINE_VIDEO_OUTPUT_FORMAT,                0x5066893c, 0x8cf9, 0x42bc, 0x8b, 0x8a, 0x47, 0x22, 0x12, 0xe5, 0x27, 0x26);
typedef enum MF_MEDIA_ENGINE_NETWORK {
    MF_MEDIA_ENGINE_NETWORK_EMPTY = 0,
    MF_MEDIA_ENGINE_NETWORK_IDLE = 1,
    MF_MEDIA_ENGINE_NETWORK_LOADING = 2,
    MF_MEDIA_ENGINE_NETWORK_NO_SOURCE = 3
} MF_MEDIA_ENGINE_NETWORK;
typedef enum MF_MEDIA_ENGINE_ERR {
    MF_MEDIA_ENGINE_ERR_NOERROR = 0,
    MF_MEDIA_ENGINE_ERR_ABORTED = 1,
    MF_MEDIA_ENGINE_ERR_NETWORK = 2,
    MF_MEDIA_ENGINE_ERR_DECODE = 3,
    MF_MEDIA_ENGINE_ERR_SRC_NOT_SUPPORTED = 4,
    MF_MEDIA_ENGINE_ERR_ENCRYPTED = 5
} MF_MEDIA_ENGINE_ERR;
typedef enum MF_MEDIA_ENGINE_PRELOAD {
    MF_MEDIA_ENGINE_PRELOAD_MISSING = 0,
    MF_MEDIA_ENGINE_PRELOAD_EMPTY = 1,
    MF_MEDIA_ENGINE_PRELOAD_NONE = 2,
    MF_MEDIA_ENGINE_PRELOAD_METADATA = 3,
    MF_MEDIA_ENGINE_PRELOAD_AUTOMATIC = 4
} MF_MEDIA_ENGINE_PRELOAD;
typedef enum MF_MEDIA_ENGINE_CANPLAY {
    MF_MEDIA_ENGINE_CANPLAY_NOT_SUPPORTED = 0,
    MF_MEDIA_ENGINE_CANPLAY_MAYBE = 1,
    MF_MEDIA_ENGINE_CANPLAY_PROBABLY = 2
} MF_MEDIA_ENGINE_CANPLAY;
#ifndef _MFVideoNormalizedRect_
#define _MFVideoNormalizedRect_
typedef struct MFVideoNormalizedRect {
    float left;
    float top;
    float right;
    float bottom;
} MFVideoNormalizedRect;
#endif
typedef enum MF_MEDIA_ENGINE_CREATEFLAGS {
    MF_MEDIA_ENGINE_AUDIOONLY = 0x1,
    MF_MEDIA_ENGINE_WAITFORSTABLE_STATE = 0x2,
    MF_MEDIA_ENGINE_FORCEMUTE = 0x4,
    MF_MEDIA_ENGINE_REAL_TIME_MODE = 0x8,
    MF_MEDIA_ENGINE_DISABLE_LOCAL_PLUGINS = 0x10,
    MF_MEDIA_ENGINE_CREATEFLAGS_MASK = 0x1f
} MF_MEDIA_ENGINE_CREATEFLAGS;
typedef enum MF_MEDIA_ENGINE_EVENT {
    MF_MEDIA_ENGINE_EVENT_LOADSTART = 1,
    MF_MEDIA_ENGINE_EVENT_PROGRESS = 2,
    MF_MEDIA_ENGINE_EVENT_SUSPEND = 3,
    MF_MEDIA_ENGINE_EVENT_ABORT = 4,
    MF_MEDIA_ENGINE_EVENT_ERROR = 5,
    MF_MEDIA_ENGINE_EVENT_EMPTIED = 6,
    MF_MEDIA_ENGINE_EVENT_STALLED = 7,
    MF_MEDIA_ENGINE_EVENT_PLAY = 8,
    MF_MEDIA_ENGINE_EVENT_PAUSE = 9,
    MF_MEDIA_ENGINE_EVENT_LOADEDMETADATA = 10,
    MF_MEDIA_ENGINE_EVENT_LOADEDDATA = 11,
    MF_MEDIA_ENGINE_EVENT_WAITING = 12,
    MF_MEDIA_ENGINE_EVENT_PLAYING = 13,
    MF_MEDIA_ENGINE_EVENT_CANPLAY = 14,
    MF_MEDIA_ENGINE_EVENT_CANPLAYTHROUGH = 15,
    MF_MEDIA_ENGINE_EVENT_SEEKING = 16,
    MF_MEDIA_ENGINE_EVENT_SEEKED = 17,
    MF_MEDIA_ENGINE_EVENT_TIMEUPDATE = 18,
    MF_MEDIA_ENGINE_EVENT_ENDED = 19,
    MF_MEDIA_ENGINE_EVENT_RATECHANGE = 20,
    MF_MEDIA_ENGINE_EVENT_DURATIONCHANGE = 21,
    MF_MEDIA_ENGINE_EVENT_VOLUMECHANGE = 22,
    MF_MEDIA_ENGINE_EVENT_FORMATCHANGE = 1000,
    MF_MEDIA_ENGINE_EVENT_PURGEQUEUEDEVENTS = 1001,
    MF_MEDIA_ENGINE_EVENT_TIMELINE_MARKER = 1002,
    MF_MEDIA_ENGINE_EVENT_BALANCECHANGE = 1003,
    MF_MEDIA_ENGINE_EVENT_DOWNLOADCOMPLETE = 1004,
    MF_MEDIA_ENGINE_EVENT_BUFFERINGSTARTED = 1005,
    MF_MEDIA_ENGINE_EVENT_BUFFERINGENDED = 1006,
    MF_MEDIA_ENGINE_EVENT_FRAMESTEPCOMPLETED = 1007,
    MF_MEDIA_ENGINE_EVENT_NOTIFYSTABLESTATE = 1008,
    MF_MEDIA_ENGINE_EVENT_FIRSTFRAMEREADY = 1009,
    MF_MEDIA_ENGINE_EVENT_TRACKSCHANGE = 1010,
    MF_MEDIA_ENGINE_EVENT_OPMINFO = 1011,
    MF_MEDIA_ENGINE_EVENT_RESOURCELOST = 1012,
    MF_MEDIA_ENGINE_EVENT_DELAYLOADEVENT_CHANGED = 1013,
    MF_MEDIA_ENGINE_EVENT_STREAMRENDERINGERROR = 1014,
    MF_MEDIA_ENGINE_EVENT_SUPPORTEDRATES_CHANGED = 1015,
    MF_MEDIA_ENGINE_EVENT_AUDIOENDPOINTCHANGE = 1016
} MF_MEDIA_ENGINE_EVENT;
typedef enum MF_MEDIA_ENGINE_READY {
    MF_MEDIA_ENGINE_READY_HAVE_NOTHING = 0,
    MF_MEDIA_ENGINE_READY_HAVE_METADATA = 1,
    MF_MEDIA_ENGINE_READY_HAVE_CURRENT_DATA = 2,
    MF_MEDIA_ENGINE_READY_HAVE_FUTURE_DATA = 3,
    MF_MEDIA_ENGINE_READY_HAVE_ENOUGH_DATA = 4
} MF_MEDIA_ENGINE_READY;
/*****************************************************************************
 * IMFMediaError interface
 */
#ifndef __IMFMediaError_INTERFACE_DEFINED__
#define __IMFMediaError_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFMediaError, 0xfc0e10d2, 0xab2a, 0x4501, 0xa9,0x51, 0x06,0xbb,0x10,0x75,0x18,0x4c);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("fc0e10d2-ab2a-4501-a951-06bb1075184c")
IMFMediaError : public IUnknown
{
    virtual USHORT STDMETHODCALLTYPE GetErrorCode(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetExtendedErrorCode(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetErrorCode(
        MF_MEDIA_ENGINE_ERR error) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetExtendedErrorCode(
        HRESULT error) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFMediaError, 0xfc0e10d2, 0xab2a, 0x4501, 0xa9,0x51, 0x06,0xbb,0x10,0x75,0x18,0x4c)
#endif
#else
typedef struct IMFMediaErrorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFMediaError *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFMediaError *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFMediaError *This);

    /*** IMFMediaError methods ***/
    USHORT (STDMETHODCALLTYPE *GetErrorCode)(
        IMFMediaError *This);

    HRESULT (STDMETHODCALLTYPE *GetExtendedErrorCode)(
        IMFMediaError *This);

    HRESULT (STDMETHODCALLTYPE *SetErrorCode)(
        IMFMediaError *This,
        MF_MEDIA_ENGINE_ERR error);

    HRESULT (STDMETHODCALLTYPE *SetExtendedErrorCode)(
        IMFMediaError *This,
        HRESULT error);

    END_INTERFACE
} IMFMediaErrorVtbl;

interface IMFMediaError {
    CONST_VTBL IMFMediaErrorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFMediaError_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFMediaError_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFMediaError_Release(This) (This)->lpVtbl->Release(This)
/*** IMFMediaError methods ***/
#define IMFMediaError_GetErrorCode(This) (This)->lpVtbl->GetErrorCode(This)
#define IMFMediaError_GetExtendedErrorCode(This) (This)->lpVtbl->GetExtendedErrorCode(This)
#define IMFMediaError_SetErrorCode(This,error) (This)->lpVtbl->SetErrorCode(This,error)
#define IMFMediaError_SetExtendedErrorCode(This,error) (This)->lpVtbl->SetExtendedErrorCode(This,error)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFMediaError_QueryInterface(IMFMediaError* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFMediaError_AddRef(IMFMediaError* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFMediaError_Release(IMFMediaError* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFMediaError methods ***/
static inline USHORT IMFMediaError_GetErrorCode(IMFMediaError* This) {
    return This->lpVtbl->GetErrorCode(This);
}
static inline HRESULT IMFMediaError_GetExtendedErrorCode(IMFMediaError* This) {
    return This->lpVtbl->GetExtendedErrorCode(This);
}
static inline HRESULT IMFMediaError_SetErrorCode(IMFMediaError* This,MF_MEDIA_ENGINE_ERR error) {
    return This->lpVtbl->SetErrorCode(This,error);
}
static inline HRESULT IMFMediaError_SetExtendedErrorCode(IMFMediaError* This,HRESULT error) {
    return This->lpVtbl->SetExtendedErrorCode(This,error);
}
#endif
#endif

#endif


#endif  /* __IMFMediaError_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMFMediaEngineSrcElements interface
 */
#ifndef __IMFMediaEngineSrcElements_INTERFACE_DEFINED__
#define __IMFMediaEngineSrcElements_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFMediaEngineSrcElements, 0x7a5e5354, 0xb114, 0x4c72, 0xb9,0x91, 0x31,0x31,0xd7,0x50,0x32,0xea);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("7a5e5354-b114-4c72-b991-3131d75032ea")
IMFMediaEngineSrcElements : public IUnknown
{
    virtual DWORD STDMETHODCALLTYPE GetLength(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetURL(
        DWORD index,
        BSTR *url) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetType(
        DWORD index,
        BSTR *type) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMedia(
        DWORD index,
        BSTR *media) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddElement(
        BSTR url,
        BSTR type,
        BSTR media) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveAllElements(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFMediaEngineSrcElements, 0x7a5e5354, 0xb114, 0x4c72, 0xb9,0x91, 0x31,0x31,0xd7,0x50,0x32,0xea)
#endif
#else
typedef struct IMFMediaEngineSrcElementsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFMediaEngineSrcElements *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFMediaEngineSrcElements *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFMediaEngineSrcElements *This);

    /*** IMFMediaEngineSrcElements methods ***/
    DWORD (STDMETHODCALLTYPE *GetLength)(
        IMFMediaEngineSrcElements *This);

    HRESULT (STDMETHODCALLTYPE *GetURL)(
        IMFMediaEngineSrcElements *This,
        DWORD index,
        BSTR *url);

    HRESULT (STDMETHODCALLTYPE *GetType)(
        IMFMediaEngineSrcElements *This,
        DWORD index,
        BSTR *type);

    HRESULT (STDMETHODCALLTYPE *GetMedia)(
        IMFMediaEngineSrcElements *This,
        DWORD index,
        BSTR *media);

    HRESULT (STDMETHODCALLTYPE *AddElement)(
        IMFMediaEngineSrcElements *This,
        BSTR url,
        BSTR type,
        BSTR media);

    HRESULT (STDMETHODCALLTYPE *RemoveAllElements)(
        IMFMediaEngineSrcElements *This);

    END_INTERFACE
} IMFMediaEngineSrcElementsVtbl;

interface IMFMediaEngineSrcElements {
    CONST_VTBL IMFMediaEngineSrcElementsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFMediaEngineSrcElements_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFMediaEngineSrcElements_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFMediaEngineSrcElements_Release(This) (This)->lpVtbl->Release(This)
/*** IMFMediaEngineSrcElements methods ***/
#define IMFMediaEngineSrcElements_GetLength(This) (This)->lpVtbl->GetLength(This)
#define IMFMediaEngineSrcElements_GetURL(This,index,url) (This)->lpVtbl->GetURL(This,index,url)
#define IMFMediaEngineSrcElements_GetType(This,index,type) (This)->lpVtbl->GetType(This,index,type)
#define IMFMediaEngineSrcElements_GetMedia(This,index,media) (This)->lpVtbl->GetMedia(This,index,media)
#define IMFMediaEngineSrcElements_AddElement(This,url,type,media) (This)->lpVtbl->AddElement(This,url,type,media)
#define IMFMediaEngineSrcElements_RemoveAllElements(This) (This)->lpVtbl->RemoveAllElements(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFMediaEngineSrcElements_QueryInterface(IMFMediaEngineSrcElements* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFMediaEngineSrcElements_AddRef(IMFMediaEngineSrcElements* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFMediaEngineSrcElements_Release(IMFMediaEngineSrcElements* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFMediaEngineSrcElements methods ***/
static inline DWORD IMFMediaEngineSrcElements_GetLength(IMFMediaEngineSrcElements* This) {
    return This->lpVtbl->GetLength(This);
}
static inline HRESULT IMFMediaEngineSrcElements_GetURL(IMFMediaEngineSrcElements* This,DWORD index,BSTR *url) {
    return This->lpVtbl->GetURL(This,index,url);
}
static inline HRESULT IMFMediaEngineSrcElements_GetType(IMFMediaEngineSrcElements* This,DWORD index,BSTR *type) {
    return This->lpVtbl->GetType(This,index,type);
}
static inline HRESULT IMFMediaEngineSrcElements_GetMedia(IMFMediaEngineSrcElements* This,DWORD index,BSTR *media) {
    return This->lpVtbl->GetMedia(This,index,media);
}
static inline HRESULT IMFMediaEngineSrcElements_AddElement(IMFMediaEngineSrcElements* This,BSTR url,BSTR type,BSTR media) {
    return This->lpVtbl->AddElement(This,url,type,media);
}
static inline HRESULT IMFMediaEngineSrcElements_RemoveAllElements(IMFMediaEngineSrcElements* This) {
    return This->lpVtbl->RemoveAllElements(This);
}
#endif
#endif

#endif


#endif  /* __IMFMediaEngineSrcElements_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMFMediaTimeRange interface
 */
#ifndef __IMFMediaTimeRange_INTERFACE_DEFINED__
#define __IMFMediaTimeRange_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFMediaTimeRange, 0xdb71a2fc, 0x078a, 0x414e, 0x9d,0xf9, 0x8c,0x25,0x31,0xb0,0xaa,0x6c);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("db71a2fc-078a-414e-9df9-8c2531b0aa6c")
IMFMediaTimeRange : public IUnknown
{
    virtual DWORD STDMETHODCALLTYPE GetLength(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStart(
        DWORD index,
        double *start) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetEnd(
        DWORD index,
        double *end) = 0;

    virtual WINBOOL STDMETHODCALLTYPE ContainsTime(
        double time) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddRange(
        double start,
        double end) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clear(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFMediaTimeRange, 0xdb71a2fc, 0x078a, 0x414e, 0x9d,0xf9, 0x8c,0x25,0x31,0xb0,0xaa,0x6c)
#endif
#else
typedef struct IMFMediaTimeRangeVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFMediaTimeRange *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFMediaTimeRange *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFMediaTimeRange *This);

    /*** IMFMediaTimeRange methods ***/
    DWORD (STDMETHODCALLTYPE *GetLength)(
        IMFMediaTimeRange *This);

    HRESULT (STDMETHODCALLTYPE *GetStart)(
        IMFMediaTimeRange *This,
        DWORD index,
        double *start);

    HRESULT (STDMETHODCALLTYPE *GetEnd)(
        IMFMediaTimeRange *This,
        DWORD index,
        double *end);

    WINBOOL (STDMETHODCALLTYPE *ContainsTime)(
        IMFMediaTimeRange *This,
        double time);

    HRESULT (STDMETHODCALLTYPE *AddRange)(
        IMFMediaTimeRange *This,
        double start,
        double end);

    HRESULT (STDMETHODCALLTYPE *Clear)(
        IMFMediaTimeRange *This);

    END_INTERFACE
} IMFMediaTimeRangeVtbl;

interface IMFMediaTimeRange {
    CONST_VTBL IMFMediaTimeRangeVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFMediaTimeRange_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFMediaTimeRange_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFMediaTimeRange_Release(This) (This)->lpVtbl->Release(This)
/*** IMFMediaTimeRange methods ***/
#define IMFMediaTimeRange_GetLength(This) (This)->lpVtbl->GetLength(This)
#define IMFMediaTimeRange_GetStart(This,index,start) (This)->lpVtbl->GetStart(This,index,start)
#define IMFMediaTimeRange_GetEnd(This,index,end) (This)->lpVtbl->GetEnd(This,index,end)
#define IMFMediaTimeRange_ContainsTime(This,time) (This)->lpVtbl->ContainsTime(This,time)
#define IMFMediaTimeRange_AddRange(This,start,end) (This)->lpVtbl->AddRange(This,start,end)
#define IMFMediaTimeRange_Clear(This) (This)->lpVtbl->Clear(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFMediaTimeRange_QueryInterface(IMFMediaTimeRange* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFMediaTimeRange_AddRef(IMFMediaTimeRange* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFMediaTimeRange_Release(IMFMediaTimeRange* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFMediaTimeRange methods ***/
static inline DWORD IMFMediaTimeRange_GetLength(IMFMediaTimeRange* This) {
    return This->lpVtbl->GetLength(This);
}
static inline HRESULT IMFMediaTimeRange_GetStart(IMFMediaTimeRange* This,DWORD index,double *start) {
    return This->lpVtbl->GetStart(This,index,start);
}
static inline HRESULT IMFMediaTimeRange_GetEnd(IMFMediaTimeRange* This,DWORD index,double *end) {
    return This->lpVtbl->GetEnd(This,index,end);
}
static inline WINBOOL IMFMediaTimeRange_ContainsTime(IMFMediaTimeRange* This,double time) {
    return This->lpVtbl->ContainsTime(This,time);
}
static inline HRESULT IMFMediaTimeRange_AddRange(IMFMediaTimeRange* This,double start,double end) {
    return This->lpVtbl->AddRange(This,start,end);
}
static inline HRESULT IMFMediaTimeRange_Clear(IMFMediaTimeRange* This) {
    return This->lpVtbl->Clear(This);
}
#endif
#endif

#endif


#endif  /* __IMFMediaTimeRange_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMFMediaEngine interface
 */
#ifndef __IMFMediaEngine_INTERFACE_DEFINED__
#define __IMFMediaEngine_INTERFACE_DEFINED__

#undef GetCurrentTime
DEFINE_GUID(IID_IMFMediaEngine, 0x98a1b0bb, 0x03eb, 0x4935, 0xae,0x7c, 0x93,0xc1,0xfa,0x0e,0x1c,0x93);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("98a1b0bb-03eb-4935-ae7c-93c1fa0e1c93")
IMFMediaEngine : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetError(
        IMFMediaError **error) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetErrorCode(
        MF_MEDIA_ENGINE_ERR error) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSourceElements(
        IMFMediaEngineSrcElements *elements) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSource(
        BSTR url) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCurrentSource(
        BSTR *url) = 0;

    virtual USHORT STDMETHODCALLTYPE GetNetworkState(
        ) = 0;

    virtual MF_MEDIA_ENGINE_PRELOAD STDMETHODCALLTYPE GetPreload(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetPreload(
        MF_MEDIA_ENGINE_PRELOAD preload) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetBuffered(
        IMFMediaTimeRange **buffered) = 0;

    virtual HRESULT STDMETHODCALLTYPE Load(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE CanPlayType(
        BSTR type,
        MF_MEDIA_ENGINE_CANPLAY *answer) = 0;

    virtual USHORT STDMETHODCALLTYPE GetReadyState(
        ) = 0;

    virtual WINBOOL STDMETHODCALLTYPE IsSeeking(
        ) = 0;

    virtual double STDMETHODCALLTYPE GetCurrentTime(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetCurrentTime(
        double time) = 0;

    virtual double STDMETHODCALLTYPE GetStartTime(
        ) = 0;

    virtual double STDMETHODCALLTYPE GetDuration(
        ) = 0;

    virtual WINBOOL STDMETHODCALLTYPE IsPaused(
        ) = 0;

    virtual double STDMETHODCALLTYPE GetDefaultPlaybackRate(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDefaultPlaybackRate(
        double rate) = 0;

    virtual double STDMETHODCALLTYPE GetPlaybackRate(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetPlaybackRate(
        double rate) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPlayed(
        IMFMediaTimeRange **played) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSeekable(
        IMFMediaTimeRange **seekable) = 0;

    virtual WINBOOL STDMETHODCALLTYPE IsEnded(
        ) = 0;

    virtual WINBOOL STDMETHODCALLTYPE GetAutoPlay(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetAutoPlay(
        WINBOOL autoplay) = 0;

    virtual WINBOOL STDMETHODCALLTYPE GetLoop(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetLoop(
        WINBOOL loop) = 0;

    virtual HRESULT STDMETHODCALLTYPE Play(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Pause(
        ) = 0;

    virtual WINBOOL STDMETHODCALLTYPE GetMuted(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetMuted(
        WINBOOL muted) = 0;

    virtual double STDMETHODCALLTYPE GetVolume(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetVolume(
        double volume) = 0;

    virtual WINBOOL STDMETHODCALLTYPE HasVideo(
        ) = 0;

    virtual WINBOOL STDMETHODCALLTYPE HasAudio(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetNativeVideoSize(
        DWORD *cx,
        DWORD *cy) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetVideoAspectRatio(
        DWORD *cx,
        DWORD *cy) = 0;

    virtual HRESULT STDMETHODCALLTYPE Shutdown(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE TransferVideoFrame(
        IUnknown *surface,
        const MFVideoNormalizedRect *src,
        const RECT *dst,
        const MFARGB *color) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnVideoStreamTick(
        LONGLONG *time) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFMediaEngine, 0x98a1b0bb, 0x03eb, 0x4935, 0xae,0x7c, 0x93,0xc1,0xfa,0x0e,0x1c,0x93)
#endif
#else
typedef struct IMFMediaEngineVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFMediaEngine *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFMediaEngine *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFMediaEngine *This);

    /*** IMFMediaEngine methods ***/
    HRESULT (STDMETHODCALLTYPE *GetError)(
        IMFMediaEngine *This,
        IMFMediaError **error);

    HRESULT (STDMETHODCALLTYPE *SetErrorCode)(
        IMFMediaEngine *This,
        MF_MEDIA_ENGINE_ERR error);

    HRESULT (STDMETHODCALLTYPE *SetSourceElements)(
        IMFMediaEngine *This,
        IMFMediaEngineSrcElements *elements);

    HRESULT (STDMETHODCALLTYPE *SetSource)(
        IMFMediaEngine *This,
        BSTR url);

    HRESULT (STDMETHODCALLTYPE *GetCurrentSource)(
        IMFMediaEngine *This,
        BSTR *url);

    USHORT (STDMETHODCALLTYPE *GetNetworkState)(
        IMFMediaEngine *This);

    MF_MEDIA_ENGINE_PRELOAD (STDMETHODCALLTYPE *GetPreload)(
        IMFMediaEngine *This);

    HRESULT (STDMETHODCALLTYPE *SetPreload)(
        IMFMediaEngine *This,
        MF_MEDIA_ENGINE_PRELOAD preload);

    HRESULT (STDMETHODCALLTYPE *GetBuffered)(
        IMFMediaEngine *This,
        IMFMediaTimeRange **buffered);

    HRESULT (STDMETHODCALLTYPE *Load)(
        IMFMediaEngine *This);

    HRESULT (STDMETHODCALLTYPE *CanPlayType)(
        IMFMediaEngine *This,
        BSTR type,
        MF_MEDIA_ENGINE_CANPLAY *answer);

    USHORT (STDMETHODCALLTYPE *GetReadyState)(
        IMFMediaEngine *This);

    WINBOOL (STDMETHODCALLTYPE *IsSeeking)(
        IMFMediaEngine *This);

    double (STDMETHODCALLTYPE *GetCurrentTime)(
        IMFMediaEngine *This);

    HRESULT (STDMETHODCALLTYPE *SetCurrentTime)(
        IMFMediaEngine *This,
        double time);

    double (STDMETHODCALLTYPE *GetStartTime)(
        IMFMediaEngine *This);

    double (STDMETHODCALLTYPE *GetDuration)(
        IMFMediaEngine *This);

    WINBOOL (STDMETHODCALLTYPE *IsPaused)(
        IMFMediaEngine *This);

    double (STDMETHODCALLTYPE *GetDefaultPlaybackRate)(
        IMFMediaEngine *This);

    HRESULT (STDMETHODCALLTYPE *SetDefaultPlaybackRate)(
        IMFMediaEngine *This,
        double rate);

    double (STDMETHODCALLTYPE *GetPlaybackRate)(
        IMFMediaEngine *This);

    HRESULT (STDMETHODCALLTYPE *SetPlaybackRate)(
        IMFMediaEngine *This,
        double rate);

    HRESULT (STDMETHODCALLTYPE *GetPlayed)(
        IMFMediaEngine *This,
        IMFMediaTimeRange **played);

    HRESULT (STDMETHODCALLTYPE *GetSeekable)(
        IMFMediaEngine *This,
        IMFMediaTimeRange **seekable);

    WINBOOL (STDMETHODCALLTYPE *IsEnded)(
        IMFMediaEngine *This);

    WINBOOL (STDMETHODCALLTYPE *GetAutoPlay)(
        IMFMediaEngine *This);

    HRESULT (STDMETHODCALLTYPE *SetAutoPlay)(
        IMFMediaEngine *This,
        WINBOOL autoplay);

    WINBOOL (STDMETHODCALLTYPE *GetLoop)(
        IMFMediaEngine *This);

    HRESULT (STDMETHODCALLTYPE *SetLoop)(
        IMFMediaEngine *This,
        WINBOOL loop);

    HRESULT (STDMETHODCALLTYPE *Play)(
        IMFMediaEngine *This);

    HRESULT (STDMETHODCALLTYPE *Pause)(
        IMFMediaEngine *This);

    WINBOOL (STDMETHODCALLTYPE *GetMuted)(
        IMFMediaEngine *This);

    HRESULT (STDMETHODCALLTYPE *SetMuted)(
        IMFMediaEngine *This,
        WINBOOL muted);

    double (STDMETHODCALLTYPE *GetVolume)(
        IMFMediaEngine *This);

    HRESULT (STDMETHODCALLTYPE *SetVolume)(
        IMFMediaEngine *This,
        double volume);

    WINBOOL (STDMETHODCALLTYPE *HasVideo)(
        IMFMediaEngine *This);

    WINBOOL (STDMETHODCALLTYPE *HasAudio)(
        IMFMediaEngine *This);

    HRESULT (STDMETHODCALLTYPE *GetNativeVideoSize)(
        IMFMediaEngine *This,
        DWORD *cx,
        DWORD *cy);

    HRESULT (STDMETHODCALLTYPE *GetVideoAspectRatio)(
        IMFMediaEngine *This,
        DWORD *cx,
        DWORD *cy);

    HRESULT (STDMETHODCALLTYPE *Shutdown)(
        IMFMediaEngine *This);

    HRESULT (STDMETHODCALLTYPE *TransferVideoFrame)(
        IMFMediaEngine *This,
        IUnknown *surface,
        const MFVideoNormalizedRect *src,
        const RECT *dst,
        const MFARGB *color);

    HRESULT (STDMETHODCALLTYPE *OnVideoStreamTick)(
        IMFMediaEngine *This,
        LONGLONG *time);

    END_INTERFACE
} IMFMediaEngineVtbl;

interface IMFMediaEngine {
    CONST_VTBL IMFMediaEngineVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFMediaEngine_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFMediaEngine_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFMediaEngine_Release(This) (This)->lpVtbl->Release(This)
/*** IMFMediaEngine methods ***/
#define IMFMediaEngine_GetError(This,error) (This)->lpVtbl->GetError(This,error)
#define IMFMediaEngine_SetErrorCode(This,error) (This)->lpVtbl->SetErrorCode(This,error)
#define IMFMediaEngine_SetSourceElements(This,elements) (This)->lpVtbl->SetSourceElements(This,elements)
#define IMFMediaEngine_SetSource(This,url) (This)->lpVtbl->SetSource(This,url)
#define IMFMediaEngine_GetCurrentSource(This,url) (This)->lpVtbl->GetCurrentSource(This,url)
#define IMFMediaEngine_GetNetworkState(This) (This)->lpVtbl->GetNetworkState(This)
#define IMFMediaEngine_GetPreload(This) (This)->lpVtbl->GetPreload(This)
#define IMFMediaEngine_SetPreload(This,preload) (This)->lpVtbl->SetPreload(This,preload)
#define IMFMediaEngine_GetBuffered(This,buffered) (This)->lpVtbl->GetBuffered(This,buffered)
#define IMFMediaEngine_Load(This) (This)->lpVtbl->Load(This)
#define IMFMediaEngine_CanPlayType(This,type,answer) (This)->lpVtbl->CanPlayType(This,type,answer)
#define IMFMediaEngine_GetReadyState(This) (This)->lpVtbl->GetReadyState(This)
#define IMFMediaEngine_IsSeeking(This) (This)->lpVtbl->IsSeeking(This)
#define IMFMediaEngine_GetCurrentTime(This) (This)->lpVtbl->GetCurrentTime(This)
#define IMFMediaEngine_SetCurrentTime(This,time) (This)->lpVtbl->SetCurrentTime(This,time)
#define IMFMediaEngine_GetStartTime(This) (This)->lpVtbl->GetStartTime(This)
#define IMFMediaEngine_GetDuration(This) (This)->lpVtbl->GetDuration(This)
#define IMFMediaEngine_IsPaused(This) (This)->lpVtbl->IsPaused(This)
#define IMFMediaEngine_GetDefaultPlaybackRate(This) (This)->lpVtbl->GetDefaultPlaybackRate(This)
#define IMFMediaEngine_SetDefaultPlaybackRate(This,rate) (This)->lpVtbl->SetDefaultPlaybackRate(This,rate)
#define IMFMediaEngine_GetPlaybackRate(This) (This)->lpVtbl->GetPlaybackRate(This)
#define IMFMediaEngine_SetPlaybackRate(This,rate) (This)->lpVtbl->SetPlaybackRate(This,rate)
#define IMFMediaEngine_GetPlayed(This,played) (This)->lpVtbl->GetPlayed(This,played)
#define IMFMediaEngine_GetSeekable(This,seekable) (This)->lpVtbl->GetSeekable(This,seekable)
#define IMFMediaEngine_IsEnded(This) (This)->lpVtbl->IsEnded(This)
#define IMFMediaEngine_GetAutoPlay(This) (This)->lpVtbl->GetAutoPlay(This)
#define IMFMediaEngine_SetAutoPlay(This,autoplay) (This)->lpVtbl->SetAutoPlay(This,autoplay)
#define IMFMediaEngine_GetLoop(This) (This)->lpVtbl->GetLoop(This)
#define IMFMediaEngine_SetLoop(This,loop) (This)->lpVtbl->SetLoop(This,loop)
#define IMFMediaEngine_Play(This) (This)->lpVtbl->Play(This)
#define IMFMediaEngine_Pause(This) (This)->lpVtbl->Pause(This)
#define IMFMediaEngine_GetMuted(This) (This)->lpVtbl->GetMuted(This)
#define IMFMediaEngine_SetMuted(This,muted) (This)->lpVtbl->SetMuted(This,muted)
#define IMFMediaEngine_GetVolume(This) (This)->lpVtbl->GetVolume(This)
#define IMFMediaEngine_SetVolume(This,volume) (This)->lpVtbl->SetVolume(This,volume)
#define IMFMediaEngine_HasVideo(This) (This)->lpVtbl->HasVideo(This)
#define IMFMediaEngine_HasAudio(This) (This)->lpVtbl->HasAudio(This)
#define IMFMediaEngine_GetNativeVideoSize(This,cx,cy) (This)->lpVtbl->GetNativeVideoSize(This,cx,cy)
#define IMFMediaEngine_GetVideoAspectRatio(This,cx,cy) (This)->lpVtbl->GetVideoAspectRatio(This,cx,cy)
#define IMFMediaEngine_Shutdown(This) (This)->lpVtbl->Shutdown(This)
#define IMFMediaEngine_TransferVideoFrame(This,surface,src,dst,color) (This)->lpVtbl->TransferVideoFrame(This,surface,src,dst,color)
#define IMFMediaEngine_OnVideoStreamTick(This,time) (This)->lpVtbl->OnVideoStreamTick(This,time)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFMediaEngine_QueryInterface(IMFMediaEngine* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFMediaEngine_AddRef(IMFMediaEngine* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFMediaEngine_Release(IMFMediaEngine* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFMediaEngine methods ***/
static inline HRESULT IMFMediaEngine_GetError(IMFMediaEngine* This,IMFMediaError **error) {
    return This->lpVtbl->GetError(This,error);
}
static inline HRESULT IMFMediaEngine_SetErrorCode(IMFMediaEngine* This,MF_MEDIA_ENGINE_ERR error) {
    return This->lpVtbl->SetErrorCode(This,error);
}
static inline HRESULT IMFMediaEngine_SetSourceElements(IMFMediaEngine* This,IMFMediaEngineSrcElements *elements) {
    return This->lpVtbl->SetSourceElements(This,elements);
}
static inline HRESULT IMFMediaEngine_SetSource(IMFMediaEngine* This,BSTR url) {
    return This->lpVtbl->SetSource(This,url);
}
static inline HRESULT IMFMediaEngine_GetCurrentSource(IMFMediaEngine* This,BSTR *url) {
    return This->lpVtbl->GetCurrentSource(This,url);
}
static inline USHORT IMFMediaEngine_GetNetworkState(IMFMediaEngine* This) {
    return This->lpVtbl->GetNetworkState(This);
}
static inline MF_MEDIA_ENGINE_PRELOAD IMFMediaEngine_GetPreload(IMFMediaEngine* This) {
    return This->lpVtbl->GetPreload(This);
}
static inline HRESULT IMFMediaEngine_SetPreload(IMFMediaEngine* This,MF_MEDIA_ENGINE_PRELOAD preload) {
    return This->lpVtbl->SetPreload(This,preload);
}
static inline HRESULT IMFMediaEngine_GetBuffered(IMFMediaEngine* This,IMFMediaTimeRange **buffered) {
    return This->lpVtbl->GetBuffered(This,buffered);
}
static inline HRESULT IMFMediaEngine_Load(IMFMediaEngine* This) {
    return This->lpVtbl->Load(This);
}
static inline HRESULT IMFMediaEngine_CanPlayType(IMFMediaEngine* This,BSTR type,MF_MEDIA_ENGINE_CANPLAY *answer) {
    return This->lpVtbl->CanPlayType(This,type,answer);
}
static inline USHORT IMFMediaEngine_GetReadyState(IMFMediaEngine* This) {
    return This->lpVtbl->GetReadyState(This);
}
static inline WINBOOL IMFMediaEngine_IsSeeking(IMFMediaEngine* This) {
    return This->lpVtbl->IsSeeking(This);
}
static inline double IMFMediaEngine_GetCurrentTime(IMFMediaEngine* This) {
    return This->lpVtbl->GetCurrentTime(This);
}
static inline HRESULT IMFMediaEngine_SetCurrentTime(IMFMediaEngine* This,double time) {
    return This->lpVtbl->SetCurrentTime(This,time);
}
static inline double IMFMediaEngine_GetStartTime(IMFMediaEngine* This) {
    return This->lpVtbl->GetStartTime(This);
}
static inline double IMFMediaEngine_GetDuration(IMFMediaEngine* This) {
    return This->lpVtbl->GetDuration(This);
}
static inline WINBOOL IMFMediaEngine_IsPaused(IMFMediaEngine* This) {
    return This->lpVtbl->IsPaused(This);
}
static inline double IMFMediaEngine_GetDefaultPlaybackRate(IMFMediaEngine* This) {
    return This->lpVtbl->GetDefaultPlaybackRate(This);
}
static inline HRESULT IMFMediaEngine_SetDefaultPlaybackRate(IMFMediaEngine* This,double rate) {
    return This->lpVtbl->SetDefaultPlaybackRate(This,rate);
}
static inline double IMFMediaEngine_GetPlaybackRate(IMFMediaEngine* This) {
    return This->lpVtbl->GetPlaybackRate(This);
}
static inline HRESULT IMFMediaEngine_SetPlaybackRate(IMFMediaEngine* This,double rate) {
    return This->lpVtbl->SetPlaybackRate(This,rate);
}
static inline HRESULT IMFMediaEngine_GetPlayed(IMFMediaEngine* This,IMFMediaTimeRange **played) {
    return This->lpVtbl->GetPlayed(This,played);
}
static inline HRESULT IMFMediaEngine_GetSeekable(IMFMediaEngine* This,IMFMediaTimeRange **seekable) {
    return This->lpVtbl->GetSeekable(This,seekable);
}
static inline WINBOOL IMFMediaEngine_IsEnded(IMFMediaEngine* This) {
    return This->lpVtbl->IsEnded(This);
}
static inline WINBOOL IMFMediaEngine_GetAutoPlay(IMFMediaEngine* This) {
    return This->lpVtbl->GetAutoPlay(This);
}
static inline HRESULT IMFMediaEngine_SetAutoPlay(IMFMediaEngine* This,WINBOOL autoplay) {
    return This->lpVtbl->SetAutoPlay(This,autoplay);
}
static inline WINBOOL IMFMediaEngine_GetLoop(IMFMediaEngine* This) {
    return This->lpVtbl->GetLoop(This);
}
static inline HRESULT IMFMediaEngine_SetLoop(IMFMediaEngine* This,WINBOOL loop) {
    return This->lpVtbl->SetLoop(This,loop);
}
static inline HRESULT IMFMediaEngine_Play(IMFMediaEngine* This) {
    return This->lpVtbl->Play(This);
}
static inline HRESULT IMFMediaEngine_Pause(IMFMediaEngine* This) {
    return This->lpVtbl->Pause(This);
}
static inline WINBOOL IMFMediaEngine_GetMuted(IMFMediaEngine* This) {
    return This->lpVtbl->GetMuted(This);
}
static inline HRESULT IMFMediaEngine_SetMuted(IMFMediaEngine* This,WINBOOL muted) {
    return This->lpVtbl->SetMuted(This,muted);
}
static inline double IMFMediaEngine_GetVolume(IMFMediaEngine* This) {
    return This->lpVtbl->GetVolume(This);
}
static inline HRESULT IMFMediaEngine_SetVolume(IMFMediaEngine* This,double volume) {
    return This->lpVtbl->SetVolume(This,volume);
}
static inline WINBOOL IMFMediaEngine_HasVideo(IMFMediaEngine* This) {
    return This->lpVtbl->HasVideo(This);
}
static inline WINBOOL IMFMediaEngine_HasAudio(IMFMediaEngine* This) {
    return This->lpVtbl->HasAudio(This);
}
static inline HRESULT IMFMediaEngine_GetNativeVideoSize(IMFMediaEngine* This,DWORD *cx,DWORD *cy) {
    return This->lpVtbl->GetNativeVideoSize(This,cx,cy);
}
static inline HRESULT IMFMediaEngine_GetVideoAspectRatio(IMFMediaEngine* This,DWORD *cx,DWORD *cy) {
    return This->lpVtbl->GetVideoAspectRatio(This,cx,cy);
}
static inline HRESULT IMFMediaEngine_Shutdown(IMFMediaEngine* This) {
    return This->lpVtbl->Shutdown(This);
}
static inline HRESULT IMFMediaEngine_TransferVideoFrame(IMFMediaEngine* This,IUnknown *surface,const MFVideoNormalizedRect *src,const RECT *dst,const MFARGB *color) {
    return This->lpVtbl->TransferVideoFrame(This,surface,src,dst,color);
}
static inline HRESULT IMFMediaEngine_OnVideoStreamTick(IMFMediaEngine* This,LONGLONG *time) {
    return This->lpVtbl->OnVideoStreamTick(This,time);
}
#endif
#endif

#endif


#endif  /* __IMFMediaEngine_INTERFACE_DEFINED__ */

typedef enum MF_MEDIA_ENGINE_STATISTIC {
    MF_MEDIA_ENGINE_STATISTIC_FRAMES_RENDERED = 0,
    MF_MEDIA_ENGINE_STATISTIC_FRAMES_DROPPED = 1,
    MF_MEDIA_ENGINE_STATISTIC_BYTES_DOWNLOADED = 2,
    MF_MEDIA_ENGINE_STATISTIC_BUFFER_PROGRESS = 3,
    MF_MEDIA_ENGINE_STATISTIC_FRAMES_PER_SECOND = 4,
    MF_MEDIA_ENGINE_STATISTIC_PLAYBACK_JITTER = 5,
    MF_MEDIA_ENGINE_STATISTIC_FRAMES_CORRUPTED = 6,
    MF_MEDIA_ENGINE_STATISTIC_TOTAL_FRAME_DELAY = 7
} MF_MEDIA_ENGINE_STATISTIC;
typedef enum MF_MEDIA_ENGINE_S3D_PACKING_MODE {
    MF_MEDIA_ENGINE_S3D_PACKING_MODE_NONE = 0,
    MF_MEDIA_ENGINE_S3D_PACKING_MODE_SIDE_BY_SIDE = 1,
    MF_MEDIA_ENGINE_S3D_PACKING_MODE_TOP_BOTTOM = 2
} MF_MEDIA_ENGINE_S3D_PACKING_MODE;
typedef enum MF_MEDIA_ENGINE_SEEK_MODE {
    MF_MEDIA_ENGINE_SEEK_MODE_NORMAL = 0,
    MF_MEDIA_ENGINE_SEEK_MODE_APPROXIMATE = 1
} MF_MEDIA_ENGINE_SEEK_MODE;
/*****************************************************************************
 * IMFMediaEngineEx interface
 */
#ifndef __IMFMediaEngineEx_INTERFACE_DEFINED__
#define __IMFMediaEngineEx_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFMediaEngineEx, 0x83015ead, 0xb1e6, 0x40d0, 0xa9,0x8a, 0x37,0x14,0x5f,0xfe,0x1a,0xd1);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("83015ead-b1e6-40d0-a98a-37145ffe1ad1")
IMFMediaEngineEx : public IMFMediaEngine
{
    virtual HRESULT STDMETHODCALLTYPE SetSourceFromByteStream(
        IMFByteStream *bytestream,
        BSTR url) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStatistics(
        MF_MEDIA_ENGINE_STATISTIC stat_id,
        PROPVARIANT *stat) = 0;

    virtual HRESULT STDMETHODCALLTYPE UpdateVideoStream(
        const MFVideoNormalizedRect *src,
        const RECT *dst,
        const MFARGB *border_color) = 0;

    virtual double STDMETHODCALLTYPE GetBalance(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetBalance(
        double balance) = 0;

    virtual WINBOOL STDMETHODCALLTYPE IsPlaybackRateSupported(
        double rate) = 0;

    virtual HRESULT STDMETHODCALLTYPE FrameStep(
        WINBOOL forward) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetResourceCharacteristics(
        DWORD *flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPresentationAttribute(
        REFGUID attribute,
        PROPVARIANT *value) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetNumberOfStreams(
        DWORD *stream_count) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStreamAttribute(
        DWORD stream_index,
        REFGUID attribute,
        PROPVARIANT *value) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStreamSelection(
        DWORD stream_index,
        WINBOOL *enabled) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetStreamSelection(
        DWORD stream_index,
        WINBOOL enabled) = 0;

    virtual HRESULT STDMETHODCALLTYPE ApplyStreamSelections(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsProtected(
        WINBOOL *is_protected) = 0;

    virtual HRESULT STDMETHODCALLTYPE InsertVideoEffect(
        IUnknown *effect,
        WINBOOL is_optional) = 0;

    virtual HRESULT STDMETHODCALLTYPE InsertAudioEffect(
        IUnknown *effect,
        WINBOOL is_optional) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveAllEffects(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetTimelineMarkerTimer(
        double timeout) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTimelineMarkerTimer(
        double *timeout) = 0;

    virtual HRESULT STDMETHODCALLTYPE CancelTimelineMarkerTimer(
        ) = 0;

    virtual WINBOOL STDMETHODCALLTYPE IsStereo3D(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStereo3DFramePackingMode(
        MF_MEDIA_ENGINE_S3D_PACKING_MODE *mode) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetStereo3DFramePackingMode(
        MF_MEDIA_ENGINE_S3D_PACKING_MODE mode) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStereo3DRenderMode(
        MF3DVideoOutputType *output_type) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetStereo3DRenderMode(
        MF3DVideoOutputType output_type) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnableWindowlessSwapchainMode(
        WINBOOL enable) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetVideoSwapchainHandle(
        HANDLE *swapchain) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnableHorizontalMirrorMode(
        WINBOOL enable) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAudioStreamCategory(
        UINT32 *category) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetAudioStreamCategory(
        UINT32 category) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAudioEndpointRole(
        UINT32 *role) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetAudioEndpointRole(
        UINT32 role) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRealTimeMode(
        WINBOOL *enabled) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetRealTimeMode(
        WINBOOL enable) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetCurrentTimeEx(
        double seektime,
        MF_MEDIA_ENGINE_SEEK_MODE mode) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnableTimeUpdateTimer(
        WINBOOL enable) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFMediaEngineEx, 0x83015ead, 0xb1e6, 0x40d0, 0xa9,0x8a, 0x37,0x14,0x5f,0xfe,0x1a,0xd1)
#endif
#else
typedef struct IMFMediaEngineExVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFMediaEngineEx *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFMediaEngineEx *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFMediaEngineEx *This);

    /*** IMFMediaEngine methods ***/
    HRESULT (STDMETHODCALLTYPE *GetError)(
        IMFMediaEngineEx *This,
        IMFMediaError **error);

    HRESULT (STDMETHODCALLTYPE *SetErrorCode)(
        IMFMediaEngineEx *This,
        MF_MEDIA_ENGINE_ERR error);

    HRESULT (STDMETHODCALLTYPE *SetSourceElements)(
        IMFMediaEngineEx *This,
        IMFMediaEngineSrcElements *elements);

    HRESULT (STDMETHODCALLTYPE *SetSource)(
        IMFMediaEngineEx *This,
        BSTR url);

    HRESULT (STDMETHODCALLTYPE *GetCurrentSource)(
        IMFMediaEngineEx *This,
        BSTR *url);

    USHORT (STDMETHODCALLTYPE *GetNetworkState)(
        IMFMediaEngineEx *This);

    MF_MEDIA_ENGINE_PRELOAD (STDMETHODCALLTYPE *GetPreload)(
        IMFMediaEngineEx *This);

    HRESULT (STDMETHODCALLTYPE *SetPreload)(
        IMFMediaEngineEx *This,
        MF_MEDIA_ENGINE_PRELOAD preload);

    HRESULT (STDMETHODCALLTYPE *GetBuffered)(
        IMFMediaEngineEx *This,
        IMFMediaTimeRange **buffered);

    HRESULT (STDMETHODCALLTYPE *Load)(
        IMFMediaEngineEx *This);

    HRESULT (STDMETHODCALLTYPE *CanPlayType)(
        IMFMediaEngineEx *This,
        BSTR type,
        MF_MEDIA_ENGINE_CANPLAY *answer);

    USHORT (STDMETHODCALLTYPE *GetReadyState)(
        IMFMediaEngineEx *This);

    WINBOOL (STDMETHODCALLTYPE *IsSeeking)(
        IMFMediaEngineEx *This);

    double (STDMETHODCALLTYPE *GetCurrentTime)(
        IMFMediaEngineEx *This);

    HRESULT (STDMETHODCALLTYPE *SetCurrentTime)(
        IMFMediaEngineEx *This,
        double time);

    double (STDMETHODCALLTYPE *GetStartTime)(
        IMFMediaEngineEx *This);

    double (STDMETHODCALLTYPE *GetDuration)(
        IMFMediaEngineEx *This);

    WINBOOL (STDMETHODCALLTYPE *IsPaused)(
        IMFMediaEngineEx *This);

    double (STDMETHODCALLTYPE *GetDefaultPlaybackRate)(
        IMFMediaEngineEx *This);

    HRESULT (STDMETHODCALLTYPE *SetDefaultPlaybackRate)(
        IMFMediaEngineEx *This,
        double rate);

    double (STDMETHODCALLTYPE *GetPlaybackRate)(
        IMFMediaEngineEx *This);

    HRESULT (STDMETHODCALLTYPE *SetPlaybackRate)(
        IMFMediaEngineEx *This,
        double rate);

    HRESULT (STDMETHODCALLTYPE *GetPlayed)(
        IMFMediaEngineEx *This,
        IMFMediaTimeRange **played);

    HRESULT (STDMETHODCALLTYPE *GetSeekable)(
        IMFMediaEngineEx *This,
        IMFMediaTimeRange **seekable);

    WINBOOL (STDMETHODCALLTYPE *IsEnded)(
        IMFMediaEngineEx *This);

    WINBOOL (STDMETHODCALLTYPE *GetAutoPlay)(
        IMFMediaEngineEx *This);

    HRESULT (STDMETHODCALLTYPE *SetAutoPlay)(
        IMFMediaEngineEx *This,
        WINBOOL autoplay);

    WINBOOL (STDMETHODCALLTYPE *GetLoop)(
        IMFMediaEngineEx *This);

    HRESULT (STDMETHODCALLTYPE *SetLoop)(
        IMFMediaEngineEx *This,
        WINBOOL loop);

    HRESULT (STDMETHODCALLTYPE *Play)(
        IMFMediaEngineEx *This);

    HRESULT (STDMETHODCALLTYPE *Pause)(
        IMFMediaEngineEx *This);

    WINBOOL (STDMETHODCALLTYPE *GetMuted)(
        IMFMediaEngineEx *This);

    HRESULT (STDMETHODCALLTYPE *SetMuted)(
        IMFMediaEngineEx *This,
        WINBOOL muted);

    double (STDMETHODCALLTYPE *GetVolume)(
        IMFMediaEngineEx *This);

    HRESULT (STDMETHODCALLTYPE *SetVolume)(
        IMFMediaEngineEx *This,
        double volume);

    WINBOOL (STDMETHODCALLTYPE *HasVideo)(
        IMFMediaEngineEx *This);

    WINBOOL (STDMETHODCALLTYPE *HasAudio)(
        IMFMediaEngineEx *This);

    HRESULT (STDMETHODCALLTYPE *GetNativeVideoSize)(
        IMFMediaEngineEx *This,
        DWORD *cx,
        DWORD *cy);

    HRESULT (STDMETHODCALLTYPE *GetVideoAspectRatio)(
        IMFMediaEngineEx *This,
        DWORD *cx,
        DWORD *cy);

    HRESULT (STDMETHODCALLTYPE *Shutdown)(
        IMFMediaEngineEx *This);

    HRESULT (STDMETHODCALLTYPE *TransferVideoFrame)(
        IMFMediaEngineEx *This,
        IUnknown *surface,
        const MFVideoNormalizedRect *src,
        const RECT *dst,
        const MFARGB *color);

    HRESULT (STDMETHODCALLTYPE *OnVideoStreamTick)(
        IMFMediaEngineEx *This,
        LONGLONG *time);

    /*** IMFMediaEngineEx methods ***/
    HRESULT (STDMETHODCALLTYPE *SetSourceFromByteStream)(
        IMFMediaEngineEx *This,
        IMFByteStream *bytestream,
        BSTR url);

    HRESULT (STDMETHODCALLTYPE *GetStatistics)(
        IMFMediaEngineEx *This,
        MF_MEDIA_ENGINE_STATISTIC stat_id,
        PROPVARIANT *stat);

    HRESULT (STDMETHODCALLTYPE *UpdateVideoStream)(
        IMFMediaEngineEx *This,
        const MFVideoNormalizedRect *src,
        const RECT *dst,
        const MFARGB *border_color);

    double (STDMETHODCALLTYPE *GetBalance)(
        IMFMediaEngineEx *This);

    HRESULT (STDMETHODCALLTYPE *SetBalance)(
        IMFMediaEngineEx *This,
        double balance);

    WINBOOL (STDMETHODCALLTYPE *IsPlaybackRateSupported)(
        IMFMediaEngineEx *This,
        double rate);

    HRESULT (STDMETHODCALLTYPE *FrameStep)(
        IMFMediaEngineEx *This,
        WINBOOL forward);

    HRESULT (STDMETHODCALLTYPE *GetResourceCharacteristics)(
        IMFMediaEngineEx *This,
        DWORD *flags);

    HRESULT (STDMETHODCALLTYPE *GetPresentationAttribute)(
        IMFMediaEngineEx *This,
        REFGUID attribute,
        PROPVARIANT *value);

    HRESULT (STDMETHODCALLTYPE *GetNumberOfStreams)(
        IMFMediaEngineEx *This,
        DWORD *stream_count);

    HRESULT (STDMETHODCALLTYPE *GetStreamAttribute)(
        IMFMediaEngineEx *This,
        DWORD stream_index,
        REFGUID attribute,
        PROPVARIANT *value);

    HRESULT (STDMETHODCALLTYPE *GetStreamSelection)(
        IMFMediaEngineEx *This,
        DWORD stream_index,
        WINBOOL *enabled);

    HRESULT (STDMETHODCALLTYPE *SetStreamSelection)(
        IMFMediaEngineEx *This,
        DWORD stream_index,
        WINBOOL enabled);

    HRESULT (STDMETHODCALLTYPE *ApplyStreamSelections)(
        IMFMediaEngineEx *This);

    HRESULT (STDMETHODCALLTYPE *IsProtected)(
        IMFMediaEngineEx *This,
        WINBOOL *is_protected);

    HRESULT (STDMETHODCALLTYPE *InsertVideoEffect)(
        IMFMediaEngineEx *This,
        IUnknown *effect,
        WINBOOL is_optional);

    HRESULT (STDMETHODCALLTYPE *InsertAudioEffect)(
        IMFMediaEngineEx *This,
        IUnknown *effect,
        WINBOOL is_optional);

    HRESULT (STDMETHODCALLTYPE *RemoveAllEffects)(
        IMFMediaEngineEx *This);

    HRESULT (STDMETHODCALLTYPE *SetTimelineMarkerTimer)(
        IMFMediaEngineEx *This,
        double timeout);

    HRESULT (STDMETHODCALLTYPE *GetTimelineMarkerTimer)(
        IMFMediaEngineEx *This,
        double *timeout);

    HRESULT (STDMETHODCALLTYPE *CancelTimelineMarkerTimer)(
        IMFMediaEngineEx *This);

    WINBOOL (STDMETHODCALLTYPE *IsStereo3D)(
        IMFMediaEngineEx *This);

    HRESULT (STDMETHODCALLTYPE *GetStereo3DFramePackingMode)(
        IMFMediaEngineEx *This,
        MF_MEDIA_ENGINE_S3D_PACKING_MODE *mode);

    HRESULT (STDMETHODCALLTYPE *SetStereo3DFramePackingMode)(
        IMFMediaEngineEx *This,
        MF_MEDIA_ENGINE_S3D_PACKING_MODE mode);

    HRESULT (STDMETHODCALLTYPE *GetStereo3DRenderMode)(
        IMFMediaEngineEx *This,
        MF3DVideoOutputType *output_type);

    HRESULT (STDMETHODCALLTYPE *SetStereo3DRenderMode)(
        IMFMediaEngineEx *This,
        MF3DVideoOutputType output_type);

    HRESULT (STDMETHODCALLTYPE *EnableWindowlessSwapchainMode)(
        IMFMediaEngineEx *This,
        WINBOOL enable);

    HRESULT (STDMETHODCALLTYPE *GetVideoSwapchainHandle)(
        IMFMediaEngineEx *This,
        HANDLE *swapchain);

    HRESULT (STDMETHODCALLTYPE *EnableHorizontalMirrorMode)(
        IMFMediaEngineEx *This,
        WINBOOL enable);

    HRESULT (STDMETHODCALLTYPE *GetAudioStreamCategory)(
        IMFMediaEngineEx *This,
        UINT32 *category);

    HRESULT (STDMETHODCALLTYPE *SetAudioStreamCategory)(
        IMFMediaEngineEx *This,
        UINT32 category);

    HRESULT (STDMETHODCALLTYPE *GetAudioEndpointRole)(
        IMFMediaEngineEx *This,
        UINT32 *role);

    HRESULT (STDMETHODCALLTYPE *SetAudioEndpointRole)(
        IMFMediaEngineEx *This,
        UINT32 role);

    HRESULT (STDMETHODCALLTYPE *GetRealTimeMode)(
        IMFMediaEngineEx *This,
        WINBOOL *enabled);

    HRESULT (STDMETHODCALLTYPE *SetRealTimeMode)(
        IMFMediaEngineEx *This,
        WINBOOL enable);

    HRESULT (STDMETHODCALLTYPE *SetCurrentTimeEx)(
        IMFMediaEngineEx *This,
        double seektime,
        MF_MEDIA_ENGINE_SEEK_MODE mode);

    HRESULT (STDMETHODCALLTYPE *EnableTimeUpdateTimer)(
        IMFMediaEngineEx *This,
        WINBOOL enable);

    END_INTERFACE
} IMFMediaEngineExVtbl;

interface IMFMediaEngineEx {
    CONST_VTBL IMFMediaEngineExVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFMediaEngineEx_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFMediaEngineEx_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFMediaEngineEx_Release(This) (This)->lpVtbl->Release(This)
/*** IMFMediaEngine methods ***/
#define IMFMediaEngineEx_GetError(This,error) (This)->lpVtbl->GetError(This,error)
#define IMFMediaEngineEx_SetErrorCode(This,error) (This)->lpVtbl->SetErrorCode(This,error)
#define IMFMediaEngineEx_SetSourceElements(This,elements) (This)->lpVtbl->SetSourceElements(This,elements)
#define IMFMediaEngineEx_SetSource(This,url) (This)->lpVtbl->SetSource(This,url)
#define IMFMediaEngineEx_GetCurrentSource(This,url) (This)->lpVtbl->GetCurrentSource(This,url)
#define IMFMediaEngineEx_GetNetworkState(This) (This)->lpVtbl->GetNetworkState(This)
#define IMFMediaEngineEx_GetPreload(This) (This)->lpVtbl->GetPreload(This)
#define IMFMediaEngineEx_SetPreload(This,preload) (This)->lpVtbl->SetPreload(This,preload)
#define IMFMediaEngineEx_GetBuffered(This,buffered) (This)->lpVtbl->GetBuffered(This,buffered)
#define IMFMediaEngineEx_Load(This) (This)->lpVtbl->Load(This)
#define IMFMediaEngineEx_CanPlayType(This,type,answer) (This)->lpVtbl->CanPlayType(This,type,answer)
#define IMFMediaEngineEx_GetReadyState(This) (This)->lpVtbl->GetReadyState(This)
#define IMFMediaEngineEx_IsSeeking(This) (This)->lpVtbl->IsSeeking(This)
#define IMFMediaEngineEx_GetCurrentTime(This) (This)->lpVtbl->GetCurrentTime(This)
#define IMFMediaEngineEx_SetCurrentTime(This,time) (This)->lpVtbl->SetCurrentTime(This,time)
#define IMFMediaEngineEx_GetStartTime(This) (This)->lpVtbl->GetStartTime(This)
#define IMFMediaEngineEx_GetDuration(This) (This)->lpVtbl->GetDuration(This)
#define IMFMediaEngineEx_IsPaused(This) (This)->lpVtbl->IsPaused(This)
#define IMFMediaEngineEx_GetDefaultPlaybackRate(This) (This)->lpVtbl->GetDefaultPlaybackRate(This)
#define IMFMediaEngineEx_SetDefaultPlaybackRate(This,rate) (This)->lpVtbl->SetDefaultPlaybackRate(This,rate)
#define IMFMediaEngineEx_GetPlaybackRate(This) (This)->lpVtbl->GetPlaybackRate(This)
#define IMFMediaEngineEx_SetPlaybackRate(This,rate) (This)->lpVtbl->SetPlaybackRate(This,rate)
#define IMFMediaEngineEx_GetPlayed(This,played) (This)->lpVtbl->GetPlayed(This,played)
#define IMFMediaEngineEx_GetSeekable(This,seekable) (This)->lpVtbl->GetSeekable(This,seekable)
#define IMFMediaEngineEx_IsEnded(This) (This)->lpVtbl->IsEnded(This)
#define IMFMediaEngineEx_GetAutoPlay(This) (This)->lpVtbl->GetAutoPlay(This)
#define IMFMediaEngineEx_SetAutoPlay(This,autoplay) (This)->lpVtbl->SetAutoPlay(This,autoplay)
#define IMFMediaEngineEx_GetLoop(This) (This)->lpVtbl->GetLoop(This)
#define IMFMediaEngineEx_SetLoop(This,loop) (This)->lpVtbl->SetLoop(This,loop)
#define IMFMediaEngineEx_Play(This) (This)->lpVtbl->Play(This)
#define IMFMediaEngineEx_Pause(This) (This)->lpVtbl->Pause(This)
#define IMFMediaEngineEx_GetMuted(This) (This)->lpVtbl->GetMuted(This)
#define IMFMediaEngineEx_SetMuted(This,muted) (This)->lpVtbl->SetMuted(This,muted)
#define IMFMediaEngineEx_GetVolume(This) (This)->lpVtbl->GetVolume(This)
#define IMFMediaEngineEx_SetVolume(This,volume) (This)->lpVtbl->SetVolume(This,volume)
#define IMFMediaEngineEx_HasVideo(This) (This)->lpVtbl->HasVideo(This)
#define IMFMediaEngineEx_HasAudio(This) (This)->lpVtbl->HasAudio(This)
#define IMFMediaEngineEx_GetNativeVideoSize(This,cx,cy) (This)->lpVtbl->GetNativeVideoSize(This,cx,cy)
#define IMFMediaEngineEx_GetVideoAspectRatio(This,cx,cy) (This)->lpVtbl->GetVideoAspectRatio(This,cx,cy)
#define IMFMediaEngineEx_Shutdown(This) (This)->lpVtbl->Shutdown(This)
#define IMFMediaEngineEx_TransferVideoFrame(This,surface,src,dst,color) (This)->lpVtbl->TransferVideoFrame(This,surface,src,dst,color)
#define IMFMediaEngineEx_OnVideoStreamTick(This,time) (This)->lpVtbl->OnVideoStreamTick(This,time)
/*** IMFMediaEngineEx methods ***/
#define IMFMediaEngineEx_SetSourceFromByteStream(This,bytestream,url) (This)->lpVtbl->SetSourceFromByteStream(This,bytestream,url)
#define IMFMediaEngineEx_GetStatistics(This,stat_id,stat) (This)->lpVtbl->GetStatistics(This,stat_id,stat)
#define IMFMediaEngineEx_UpdateVideoStream(This,src,dst,border_color) (This)->lpVtbl->UpdateVideoStream(This,src,dst,border_color)
#define IMFMediaEngineEx_GetBalance(This) (This)->lpVtbl->GetBalance(This)
#define IMFMediaEngineEx_SetBalance(This,balance) (This)->lpVtbl->SetBalance(This,balance)
#define IMFMediaEngineEx_IsPlaybackRateSupported(This,rate) (This)->lpVtbl->IsPlaybackRateSupported(This,rate)
#define IMFMediaEngineEx_FrameStep(This,forward) (This)->lpVtbl->FrameStep(This,forward)
#define IMFMediaEngineEx_GetResourceCharacteristics(This,flags) (This)->lpVtbl->GetResourceCharacteristics(This,flags)
#define IMFMediaEngineEx_GetPresentationAttribute(This,attribute,value) (This)->lpVtbl->GetPresentationAttribute(This,attribute,value)
#define IMFMediaEngineEx_GetNumberOfStreams(This,stream_count) (This)->lpVtbl->GetNumberOfStreams(This,stream_count)
#define IMFMediaEngineEx_GetStreamAttribute(This,stream_index,attribute,value) (This)->lpVtbl->GetStreamAttribute(This,stream_index,attribute,value)
#define IMFMediaEngineEx_GetStreamSelection(This,stream_index,enabled) (This)->lpVtbl->GetStreamSelection(This,stream_index,enabled)
#define IMFMediaEngineEx_SetStreamSelection(This,stream_index,enabled) (This)->lpVtbl->SetStreamSelection(This,stream_index,enabled)
#define IMFMediaEngineEx_ApplyStreamSelections(This) (This)->lpVtbl->ApplyStreamSelections(This)
#define IMFMediaEngineEx_IsProtected(This,is_protected) (This)->lpVtbl->IsProtected(This,is_protected)
#define IMFMediaEngineEx_InsertVideoEffect(This,effect,is_optional) (This)->lpVtbl->InsertVideoEffect(This,effect,is_optional)
#define IMFMediaEngineEx_InsertAudioEffect(This,effect,is_optional) (This)->lpVtbl->InsertAudioEffect(This,effect,is_optional)
#define IMFMediaEngineEx_RemoveAllEffects(This) (This)->lpVtbl->RemoveAllEffects(This)
#define IMFMediaEngineEx_SetTimelineMarkerTimer(This,timeout) (This)->lpVtbl->SetTimelineMarkerTimer(This,timeout)
#define IMFMediaEngineEx_GetTimelineMarkerTimer(This,timeout) (This)->lpVtbl->GetTimelineMarkerTimer(This,timeout)
#define IMFMediaEngineEx_CancelTimelineMarkerTimer(This) (This)->lpVtbl->CancelTimelineMarkerTimer(This)
#define IMFMediaEngineEx_IsStereo3D(This) (This)->lpVtbl->IsStereo3D(This)
#define IMFMediaEngineEx_GetStereo3DFramePackingMode(This,mode) (This)->lpVtbl->GetStereo3DFramePackingMode(This,mode)
#define IMFMediaEngineEx_SetStereo3DFramePackingMode(This,mode) (This)->lpVtbl->SetStereo3DFramePackingMode(This,mode)
#define IMFMediaEngineEx_GetStereo3DRenderMode(This,output_type) (This)->lpVtbl->GetStereo3DRenderMode(This,output_type)
#define IMFMediaEngineEx_SetStereo3DRenderMode(This,output_type) (This)->lpVtbl->SetStereo3DRenderMode(This,output_type)
#define IMFMediaEngineEx_EnableWindowlessSwapchainMode(This,enable) (This)->lpVtbl->EnableWindowlessSwapchainMode(This,enable)
#define IMFMediaEngineEx_GetVideoSwapchainHandle(This,swapchain) (This)->lpVtbl->GetVideoSwapchainHandle(This,swapchain)
#define IMFMediaEngineEx_EnableHorizontalMirrorMode(This,enable) (This)->lpVtbl->EnableHorizontalMirrorMode(This,enable)
#define IMFMediaEngineEx_GetAudioStreamCategory(This,category) (This)->lpVtbl->GetAudioStreamCategory(This,category)
#define IMFMediaEngineEx_SetAudioStreamCategory(This,category) (This)->lpVtbl->SetAudioStreamCategory(This,category)
#define IMFMediaEngineEx_GetAudioEndpointRole(This,role) (This)->lpVtbl->GetAudioEndpointRole(This,role)
#define IMFMediaEngineEx_SetAudioEndpointRole(This,role) (This)->lpVtbl->SetAudioEndpointRole(This,role)
#define IMFMediaEngineEx_GetRealTimeMode(This,enabled) (This)->lpVtbl->GetRealTimeMode(This,enabled)
#define IMFMediaEngineEx_SetRealTimeMode(This,enable) (This)->lpVtbl->SetRealTimeMode(This,enable)
#define IMFMediaEngineEx_SetCurrentTimeEx(This,seektime,mode) (This)->lpVtbl->SetCurrentTimeEx(This,seektime,mode)
#define IMFMediaEngineEx_EnableTimeUpdateTimer(This,enable) (This)->lpVtbl->EnableTimeUpdateTimer(This,enable)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFMediaEngineEx_QueryInterface(IMFMediaEngineEx* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFMediaEngineEx_AddRef(IMFMediaEngineEx* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFMediaEngineEx_Release(IMFMediaEngineEx* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFMediaEngine methods ***/
static inline HRESULT IMFMediaEngineEx_GetError(IMFMediaEngineEx* This,IMFMediaError **error) {
    return This->lpVtbl->GetError(This,error);
}
static inline HRESULT IMFMediaEngineEx_SetErrorCode(IMFMediaEngineEx* This,MF_MEDIA_ENGINE_ERR error) {
    return This->lpVtbl->SetErrorCode(This,error);
}
static inline HRESULT IMFMediaEngineEx_SetSourceElements(IMFMediaEngineEx* This,IMFMediaEngineSrcElements *elements) {
    return This->lpVtbl->SetSourceElements(This,elements);
}
static inline HRESULT IMFMediaEngineEx_SetSource(IMFMediaEngineEx* This,BSTR url) {
    return This->lpVtbl->SetSource(This,url);
}
static inline HRESULT IMFMediaEngineEx_GetCurrentSource(IMFMediaEngineEx* This,BSTR *url) {
    return This->lpVtbl->GetCurrentSource(This,url);
}
static inline USHORT IMFMediaEngineEx_GetNetworkState(IMFMediaEngineEx* This) {
    return This->lpVtbl->GetNetworkState(This);
}
static inline MF_MEDIA_ENGINE_PRELOAD IMFMediaEngineEx_GetPreload(IMFMediaEngineEx* This) {
    return This->lpVtbl->GetPreload(This);
}
static inline HRESULT IMFMediaEngineEx_SetPreload(IMFMediaEngineEx* This,MF_MEDIA_ENGINE_PRELOAD preload) {
    return This->lpVtbl->SetPreload(This,preload);
}
static inline HRESULT IMFMediaEngineEx_GetBuffered(IMFMediaEngineEx* This,IMFMediaTimeRange **buffered) {
    return This->lpVtbl->GetBuffered(This,buffered);
}
static inline HRESULT IMFMediaEngineEx_Load(IMFMediaEngineEx* This) {
    return This->lpVtbl->Load(This);
}
static inline HRESULT IMFMediaEngineEx_CanPlayType(IMFMediaEngineEx* This,BSTR type,MF_MEDIA_ENGINE_CANPLAY *answer) {
    return This->lpVtbl->CanPlayType(This,type,answer);
}
static inline USHORT IMFMediaEngineEx_GetReadyState(IMFMediaEngineEx* This) {
    return This->lpVtbl->GetReadyState(This);
}
static inline WINBOOL IMFMediaEngineEx_IsSeeking(IMFMediaEngineEx* This) {
    return This->lpVtbl->IsSeeking(This);
}
static inline double IMFMediaEngineEx_GetCurrentTime(IMFMediaEngineEx* This) {
    return This->lpVtbl->GetCurrentTime(This);
}
static inline HRESULT IMFMediaEngineEx_SetCurrentTime(IMFMediaEngineEx* This,double time) {
    return This->lpVtbl->SetCurrentTime(This,time);
}
static inline double IMFMediaEngineEx_GetStartTime(IMFMediaEngineEx* This) {
    return This->lpVtbl->GetStartTime(This);
}
static inline double IMFMediaEngineEx_GetDuration(IMFMediaEngineEx* This) {
    return This->lpVtbl->GetDuration(This);
}
static inline WINBOOL IMFMediaEngineEx_IsPaused(IMFMediaEngineEx* This) {
    return This->lpVtbl->IsPaused(This);
}
static inline double IMFMediaEngineEx_GetDefaultPlaybackRate(IMFMediaEngineEx* This) {
    return This->lpVtbl->GetDefaultPlaybackRate(This);
}
static inline HRESULT IMFMediaEngineEx_SetDefaultPlaybackRate(IMFMediaEngineEx* This,double rate) {
    return This->lpVtbl->SetDefaultPlaybackRate(This,rate);
}
static inline double IMFMediaEngineEx_GetPlaybackRate(IMFMediaEngineEx* This) {
    return This->lpVtbl->GetPlaybackRate(This);
}
static inline HRESULT IMFMediaEngineEx_SetPlaybackRate(IMFMediaEngineEx* This,double rate) {
    return This->lpVtbl->SetPlaybackRate(This,rate);
}
static inline HRESULT IMFMediaEngineEx_GetPlayed(IMFMediaEngineEx* This,IMFMediaTimeRange **played) {
    return This->lpVtbl->GetPlayed(This,played);
}
static inline HRESULT IMFMediaEngineEx_GetSeekable(IMFMediaEngineEx* This,IMFMediaTimeRange **seekable) {
    return This->lpVtbl->GetSeekable(This,seekable);
}
static inline WINBOOL IMFMediaEngineEx_IsEnded(IMFMediaEngineEx* This) {
    return This->lpVtbl->IsEnded(This);
}
static inline WINBOOL IMFMediaEngineEx_GetAutoPlay(IMFMediaEngineEx* This) {
    return This->lpVtbl->GetAutoPlay(This);
}
static inline HRESULT IMFMediaEngineEx_SetAutoPlay(IMFMediaEngineEx* This,WINBOOL autoplay) {
    return This->lpVtbl->SetAutoPlay(This,autoplay);
}
static inline WINBOOL IMFMediaEngineEx_GetLoop(IMFMediaEngineEx* This) {
    return This->lpVtbl->GetLoop(This);
}
static inline HRESULT IMFMediaEngineEx_SetLoop(IMFMediaEngineEx* This,WINBOOL loop) {
    return This->lpVtbl->SetLoop(This,loop);
}
static inline HRESULT IMFMediaEngineEx_Play(IMFMediaEngineEx* This) {
    return This->lpVtbl->Play(This);
}
static inline HRESULT IMFMediaEngineEx_Pause(IMFMediaEngineEx* This) {
    return This->lpVtbl->Pause(This);
}
static inline WINBOOL IMFMediaEngineEx_GetMuted(IMFMediaEngineEx* This) {
    return This->lpVtbl->GetMuted(This);
}
static inline HRESULT IMFMediaEngineEx_SetMuted(IMFMediaEngineEx* This,WINBOOL muted) {
    return This->lpVtbl->SetMuted(This,muted);
}
static inline double IMFMediaEngineEx_GetVolume(IMFMediaEngineEx* This) {
    return This->lpVtbl->GetVolume(This);
}
static inline HRESULT IMFMediaEngineEx_SetVolume(IMFMediaEngineEx* This,double volume) {
    return This->lpVtbl->SetVolume(This,volume);
}
static inline WINBOOL IMFMediaEngineEx_HasVideo(IMFMediaEngineEx* This) {
    return This->lpVtbl->HasVideo(This);
}
static inline WINBOOL IMFMediaEngineEx_HasAudio(IMFMediaEngineEx* This) {
    return This->lpVtbl->HasAudio(This);
}
static inline HRESULT IMFMediaEngineEx_GetNativeVideoSize(IMFMediaEngineEx* This,DWORD *cx,DWORD *cy) {
    return This->lpVtbl->GetNativeVideoSize(This,cx,cy);
}
static inline HRESULT IMFMediaEngineEx_GetVideoAspectRatio(IMFMediaEngineEx* This,DWORD *cx,DWORD *cy) {
    return This->lpVtbl->GetVideoAspectRatio(This,cx,cy);
}
static inline HRESULT IMFMediaEngineEx_Shutdown(IMFMediaEngineEx* This) {
    return This->lpVtbl->Shutdown(This);
}
static inline HRESULT IMFMediaEngineEx_TransferVideoFrame(IMFMediaEngineEx* This,IUnknown *surface,const MFVideoNormalizedRect *src,const RECT *dst,const MFARGB *color) {
    return This->lpVtbl->TransferVideoFrame(This,surface,src,dst,color);
}
static inline HRESULT IMFMediaEngineEx_OnVideoStreamTick(IMFMediaEngineEx* This,LONGLONG *time) {
    return This->lpVtbl->OnVideoStreamTick(This,time);
}
/*** IMFMediaEngineEx methods ***/
static inline HRESULT IMFMediaEngineEx_SetSourceFromByteStream(IMFMediaEngineEx* This,IMFByteStream *bytestream,BSTR url) {
    return This->lpVtbl->SetSourceFromByteStream(This,bytestream,url);
}
static inline HRESULT IMFMediaEngineEx_GetStatistics(IMFMediaEngineEx* This,MF_MEDIA_ENGINE_STATISTIC stat_id,PROPVARIANT *stat) {
    return This->lpVtbl->GetStatistics(This,stat_id,stat);
}
static inline HRESULT IMFMediaEngineEx_UpdateVideoStream(IMFMediaEngineEx* This,const MFVideoNormalizedRect *src,const RECT *dst,const MFARGB *border_color) {
    return This->lpVtbl->UpdateVideoStream(This,src,dst,border_color);
}
static inline double IMFMediaEngineEx_GetBalance(IMFMediaEngineEx* This) {
    return This->lpVtbl->GetBalance(This);
}
static inline HRESULT IMFMediaEngineEx_SetBalance(IMFMediaEngineEx* This,double balance) {
    return This->lpVtbl->SetBalance(This,balance);
}
static inline WINBOOL IMFMediaEngineEx_IsPlaybackRateSupported(IMFMediaEngineEx* This,double rate) {
    return This->lpVtbl->IsPlaybackRateSupported(This,rate);
}
static inline HRESULT IMFMediaEngineEx_FrameStep(IMFMediaEngineEx* This,WINBOOL forward) {
    return This->lpVtbl->FrameStep(This,forward);
}
static inline HRESULT IMFMediaEngineEx_GetResourceCharacteristics(IMFMediaEngineEx* This,DWORD *flags) {
    return This->lpVtbl->GetResourceCharacteristics(This,flags);
}
static inline HRESULT IMFMediaEngineEx_GetPresentationAttribute(IMFMediaEngineEx* This,REFGUID attribute,PROPVARIANT *value) {
    return This->lpVtbl->GetPresentationAttribute(This,attribute,value);
}
static inline HRESULT IMFMediaEngineEx_GetNumberOfStreams(IMFMediaEngineEx* This,DWORD *stream_count) {
    return This->lpVtbl->GetNumberOfStreams(This,stream_count);
}
static inline HRESULT IMFMediaEngineEx_GetStreamAttribute(IMFMediaEngineEx* This,DWORD stream_index,REFGUID attribute,PROPVARIANT *value) {
    return This->lpVtbl->GetStreamAttribute(This,stream_index,attribute,value);
}
static inline HRESULT IMFMediaEngineEx_GetStreamSelection(IMFMediaEngineEx* This,DWORD stream_index,WINBOOL *enabled) {
    return This->lpVtbl->GetStreamSelection(This,stream_index,enabled);
}
static inline HRESULT IMFMediaEngineEx_SetStreamSelection(IMFMediaEngineEx* This,DWORD stream_index,WINBOOL enabled) {
    return This->lpVtbl->SetStreamSelection(This,stream_index,enabled);
}
static inline HRESULT IMFMediaEngineEx_ApplyStreamSelections(IMFMediaEngineEx* This) {
    return This->lpVtbl->ApplyStreamSelections(This);
}
static inline HRESULT IMFMediaEngineEx_IsProtected(IMFMediaEngineEx* This,WINBOOL *is_protected) {
    return This->lpVtbl->IsProtected(This,is_protected);
}
static inline HRESULT IMFMediaEngineEx_InsertVideoEffect(IMFMediaEngineEx* This,IUnknown *effect,WINBOOL is_optional) {
    return This->lpVtbl->InsertVideoEffect(This,effect,is_optional);
}
static inline HRESULT IMFMediaEngineEx_InsertAudioEffect(IMFMediaEngineEx* This,IUnknown *effect,WINBOOL is_optional) {
    return This->lpVtbl->InsertAudioEffect(This,effect,is_optional);
}
static inline HRESULT IMFMediaEngineEx_RemoveAllEffects(IMFMediaEngineEx* This) {
    return This->lpVtbl->RemoveAllEffects(This);
}
static inline HRESULT IMFMediaEngineEx_SetTimelineMarkerTimer(IMFMediaEngineEx* This,double timeout) {
    return This->lpVtbl->SetTimelineMarkerTimer(This,timeout);
}
static inline HRESULT IMFMediaEngineEx_GetTimelineMarkerTimer(IMFMediaEngineEx* This,double *timeout) {
    return This->lpVtbl->GetTimelineMarkerTimer(This,timeout);
}
static inline HRESULT IMFMediaEngineEx_CancelTimelineMarkerTimer(IMFMediaEngineEx* This) {
    return This->lpVtbl->CancelTimelineMarkerTimer(This);
}
static inline WINBOOL IMFMediaEngineEx_IsStereo3D(IMFMediaEngineEx* This) {
    return This->lpVtbl->IsStereo3D(This);
}
static inline HRESULT IMFMediaEngineEx_GetStereo3DFramePackingMode(IMFMediaEngineEx* This,MF_MEDIA_ENGINE_S3D_PACKING_MODE *mode) {
    return This->lpVtbl->GetStereo3DFramePackingMode(This,mode);
}
static inline HRESULT IMFMediaEngineEx_SetStereo3DFramePackingMode(IMFMediaEngineEx* This,MF_MEDIA_ENGINE_S3D_PACKING_MODE mode) {
    return This->lpVtbl->SetStereo3DFramePackingMode(This,mode);
}
static inline HRESULT IMFMediaEngineEx_GetStereo3DRenderMode(IMFMediaEngineEx* This,MF3DVideoOutputType *output_type) {
    return This->lpVtbl->GetStereo3DRenderMode(This,output_type);
}
static inline HRESULT IMFMediaEngineEx_SetStereo3DRenderMode(IMFMediaEngineEx* This,MF3DVideoOutputType output_type) {
    return This->lpVtbl->SetStereo3DRenderMode(This,output_type);
}
static inline HRESULT IMFMediaEngineEx_EnableWindowlessSwapchainMode(IMFMediaEngineEx* This,WINBOOL enable) {
    return This->lpVtbl->EnableWindowlessSwapchainMode(This,enable);
}
static inline HRESULT IMFMediaEngineEx_GetVideoSwapchainHandle(IMFMediaEngineEx* This,HANDLE *swapchain) {
    return This->lpVtbl->GetVideoSwapchainHandle(This,swapchain);
}
static inline HRESULT IMFMediaEngineEx_EnableHorizontalMirrorMode(IMFMediaEngineEx* This,WINBOOL enable) {
    return This->lpVtbl->EnableHorizontalMirrorMode(This,enable);
}
static inline HRESULT IMFMediaEngineEx_GetAudioStreamCategory(IMFMediaEngineEx* This,UINT32 *category) {
    return This->lpVtbl->GetAudioStreamCategory(This,category);
}
static inline HRESULT IMFMediaEngineEx_SetAudioStreamCategory(IMFMediaEngineEx* This,UINT32 category) {
    return This->lpVtbl->SetAudioStreamCategory(This,category);
}
static inline HRESULT IMFMediaEngineEx_GetAudioEndpointRole(IMFMediaEngineEx* This,UINT32 *role) {
    return This->lpVtbl->GetAudioEndpointRole(This,role);
}
static inline HRESULT IMFMediaEngineEx_SetAudioEndpointRole(IMFMediaEngineEx* This,UINT32 role) {
    return This->lpVtbl->SetAudioEndpointRole(This,role);
}
static inline HRESULT IMFMediaEngineEx_GetRealTimeMode(IMFMediaEngineEx* This,WINBOOL *enabled) {
    return This->lpVtbl->GetRealTimeMode(This,enabled);
}
static inline HRESULT IMFMediaEngineEx_SetRealTimeMode(IMFMediaEngineEx* This,WINBOOL enable) {
    return This->lpVtbl->SetRealTimeMode(This,enable);
}
static inline HRESULT IMFMediaEngineEx_SetCurrentTimeEx(IMFMediaEngineEx* This,double seektime,MF_MEDIA_ENGINE_SEEK_MODE mode) {
    return This->lpVtbl->SetCurrentTimeEx(This,seektime,mode);
}
static inline HRESULT IMFMediaEngineEx_EnableTimeUpdateTimer(IMFMediaEngineEx* This,WINBOOL enable) {
    return This->lpVtbl->EnableTimeUpdateTimer(This,enable);
}
#endif
#endif

#endif


#endif  /* __IMFMediaEngineEx_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMFMediaEngineClassFactory interface
 */
#ifndef __IMFMediaEngineClassFactory_INTERFACE_DEFINED__
#define __IMFMediaEngineClassFactory_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFMediaEngineClassFactory, 0x4d645ace, 0x26aa, 0x4688, 0x9b,0xe1, 0xdf,0x35,0x16,0x99,0x0b,0x93);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("4d645ace-26aa-4688-9be1-df3516990b93")
IMFMediaEngineClassFactory : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE CreateInstance(
        DWORD flags,
        IMFAttributes *attributes,
        IMFMediaEngine **engine) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateTimeRange(
        IMFMediaTimeRange **range) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateError(
        IMFMediaError **error) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFMediaEngineClassFactory, 0x4d645ace, 0x26aa, 0x4688, 0x9b,0xe1, 0xdf,0x35,0x16,0x99,0x0b,0x93)
#endif
#else
typedef struct IMFMediaEngineClassFactoryVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFMediaEngineClassFactory *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFMediaEngineClassFactory *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFMediaEngineClassFactory *This);

    /*** IMFMediaEngineClassFactory methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateInstance)(
        IMFMediaEngineClassFactory *This,
        DWORD flags,
        IMFAttributes *attributes,
        IMFMediaEngine **engine);

    HRESULT (STDMETHODCALLTYPE *CreateTimeRange)(
        IMFMediaEngineClassFactory *This,
        IMFMediaTimeRange **range);

    HRESULT (STDMETHODCALLTYPE *CreateError)(
        IMFMediaEngineClassFactory *This,
        IMFMediaError **error);

    END_INTERFACE
} IMFMediaEngineClassFactoryVtbl;

interface IMFMediaEngineClassFactory {
    CONST_VTBL IMFMediaEngineClassFactoryVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFMediaEngineClassFactory_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFMediaEngineClassFactory_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFMediaEngineClassFactory_Release(This) (This)->lpVtbl->Release(This)
/*** IMFMediaEngineClassFactory methods ***/
#define IMFMediaEngineClassFactory_CreateInstance(This,flags,attributes,engine) (This)->lpVtbl->CreateInstance(This,flags,attributes,engine)
#define IMFMediaEngineClassFactory_CreateTimeRange(This,range) (This)->lpVtbl->CreateTimeRange(This,range)
#define IMFMediaEngineClassFactory_CreateError(This,error) (This)->lpVtbl->CreateError(This,error)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFMediaEngineClassFactory_QueryInterface(IMFMediaEngineClassFactory* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFMediaEngineClassFactory_AddRef(IMFMediaEngineClassFactory* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFMediaEngineClassFactory_Release(IMFMediaEngineClassFactory* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFMediaEngineClassFactory methods ***/
static inline HRESULT IMFMediaEngineClassFactory_CreateInstance(IMFMediaEngineClassFactory* This,DWORD flags,IMFAttributes *attributes,IMFMediaEngine **engine) {
    return This->lpVtbl->CreateInstance(This,flags,attributes,engine);
}
static inline HRESULT IMFMediaEngineClassFactory_CreateTimeRange(IMFMediaEngineClassFactory* This,IMFMediaTimeRange **range) {
    return This->lpVtbl->CreateTimeRange(This,range);
}
static inline HRESULT IMFMediaEngineClassFactory_CreateError(IMFMediaEngineClassFactory* This,IMFMediaError **error) {
    return This->lpVtbl->CreateError(This,error);
}
#endif
#endif

#endif


#endif  /* __IMFMediaEngineClassFactory_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMFMediaEngineNotify interface
 */
#ifndef __IMFMediaEngineNotify_INTERFACE_DEFINED__
#define __IMFMediaEngineNotify_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFMediaEngineNotify, 0xfee7c112, 0xe776, 0x42b5, 0x9b,0xbf, 0x00,0x48,0x52,0x4e,0x2b,0xd5);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("fee7c112-e776-42b5-9bbf-0048524e2bd5")
IMFMediaEngineNotify : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE EventNotify(
        DWORD event,
        DWORD_PTR param1,
        DWORD param2) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFMediaEngineNotify, 0xfee7c112, 0xe776, 0x42b5, 0x9b,0xbf, 0x00,0x48,0x52,0x4e,0x2b,0xd5)
#endif
#else
typedef struct IMFMediaEngineNotifyVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFMediaEngineNotify *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFMediaEngineNotify *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFMediaEngineNotify *This);

    /*** IMFMediaEngineNotify methods ***/
    HRESULT (STDMETHODCALLTYPE *EventNotify)(
        IMFMediaEngineNotify *This,
        DWORD event,
        DWORD_PTR param1,
        DWORD param2);

    END_INTERFACE
} IMFMediaEngineNotifyVtbl;

interface IMFMediaEngineNotify {
    CONST_VTBL IMFMediaEngineNotifyVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFMediaEngineNotify_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFMediaEngineNotify_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFMediaEngineNotify_Release(This) (This)->lpVtbl->Release(This)
/*** IMFMediaEngineNotify methods ***/
#define IMFMediaEngineNotify_EventNotify(This,event,param1,param2) (This)->lpVtbl->EventNotify(This,event,param1,param2)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFMediaEngineNotify_QueryInterface(IMFMediaEngineNotify* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFMediaEngineNotify_AddRef(IMFMediaEngineNotify* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFMediaEngineNotify_Release(IMFMediaEngineNotify* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFMediaEngineNotify methods ***/
static inline HRESULT IMFMediaEngineNotify_EventNotify(IMFMediaEngineNotify* This,DWORD event,DWORD_PTR param1,DWORD param2) {
    return This->lpVtbl->EventNotify(This,event,param1,param2);
}
#endif
#endif

#endif


#endif  /* __IMFMediaEngineNotify_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMFMediaEngineAudioEndpointId interface
 */
#ifndef __IMFMediaEngineAudioEndpointId_INTERFACE_DEFINED__
#define __IMFMediaEngineAudioEndpointId_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFMediaEngineAudioEndpointId, 0x7a3bac98, 0x0e76, 0x49fb, 0x8c,0x20, 0x8a,0x86,0xfd,0x98,0xea,0xf2);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("7a3bac98-0e76-49fb-8c20-8a86fd98eaf2")
IMFMediaEngineAudioEndpointId : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetAudioEndpointId(
        LPCWSTR id) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAudioEndpointId(
        LPWSTR *id) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFMediaEngineAudioEndpointId, 0x7a3bac98, 0x0e76, 0x49fb, 0x8c,0x20, 0x8a,0x86,0xfd,0x98,0xea,0xf2)
#endif
#else
typedef struct IMFMediaEngineAudioEndpointIdVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFMediaEngineAudioEndpointId *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFMediaEngineAudioEndpointId *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFMediaEngineAudioEndpointId *This);

    /*** IMFMediaEngineAudioEndpointId methods ***/
    HRESULT (STDMETHODCALLTYPE *SetAudioEndpointId)(
        IMFMediaEngineAudioEndpointId *This,
        LPCWSTR id);

    HRESULT (STDMETHODCALLTYPE *GetAudioEndpointId)(
        IMFMediaEngineAudioEndpointId *This,
        LPWSTR *id);

    END_INTERFACE
} IMFMediaEngineAudioEndpointIdVtbl;

interface IMFMediaEngineAudioEndpointId {
    CONST_VTBL IMFMediaEngineAudioEndpointIdVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFMediaEngineAudioEndpointId_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFMediaEngineAudioEndpointId_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFMediaEngineAudioEndpointId_Release(This) (This)->lpVtbl->Release(This)
/*** IMFMediaEngineAudioEndpointId methods ***/
#define IMFMediaEngineAudioEndpointId_SetAudioEndpointId(This,id) (This)->lpVtbl->SetAudioEndpointId(This,id)
#define IMFMediaEngineAudioEndpointId_GetAudioEndpointId(This,id) (This)->lpVtbl->GetAudioEndpointId(This,id)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFMediaEngineAudioEndpointId_QueryInterface(IMFMediaEngineAudioEndpointId* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFMediaEngineAudioEndpointId_AddRef(IMFMediaEngineAudioEndpointId* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFMediaEngineAudioEndpointId_Release(IMFMediaEngineAudioEndpointId* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFMediaEngineAudioEndpointId methods ***/
static inline HRESULT IMFMediaEngineAudioEndpointId_SetAudioEndpointId(IMFMediaEngineAudioEndpointId* This,LPCWSTR id) {
    return This->lpVtbl->SetAudioEndpointId(This,id);
}
static inline HRESULT IMFMediaEngineAudioEndpointId_GetAudioEndpointId(IMFMediaEngineAudioEndpointId* This,LPWSTR *id) {
    return This->lpVtbl->GetAudioEndpointId(This,id);
}
#endif
#endif

#endif


#endif  /* __IMFMediaEngineAudioEndpointId_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMFMediaEngineExtension interface
 */
#ifndef __IMFMediaEngineExtension_INTERFACE_DEFINED__
#define __IMFMediaEngineExtension_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFMediaEngineExtension, 0x2f69d622, 0x20b5, 0x41e9, 0xaf,0xdf, 0x89,0xce,0xd1,0xdd,0xa0,0x4e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("2f69d622-20b5-41e9-afdf-89ced1dda04e")
IMFMediaEngineExtension : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE CanPlayType(
        WINBOOL audio_only,
        BSTR mime_type,
        MF_MEDIA_ENGINE_CANPLAY *answer) = 0;

    virtual HRESULT STDMETHODCALLTYPE BeginCreateObject(
        BSTR url,
        IMFByteStream *bytestream,
        MF_OBJECT_TYPE type,
        IUnknown **cancel_cookie,
        IMFAsyncCallback *callback,
        IUnknown *state) = 0;

    virtual HRESULT STDMETHODCALLTYPE CancelObjectCreation(
        IUnknown *cancel_cookie) = 0;

    virtual HRESULT STDMETHODCALLTYPE EndCreateObject(
        IMFAsyncResult *result,
        IUnknown **object) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFMediaEngineExtension, 0x2f69d622, 0x20b5, 0x41e9, 0xaf,0xdf, 0x89,0xce,0xd1,0xdd,0xa0,0x4e)
#endif
#else
typedef struct IMFMediaEngineExtensionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFMediaEngineExtension *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFMediaEngineExtension *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFMediaEngineExtension *This);

    /*** IMFMediaEngineExtension methods ***/
    HRESULT (STDMETHODCALLTYPE *CanPlayType)(
        IMFMediaEngineExtension *This,
        WINBOOL audio_only,
        BSTR mime_type,
        MF_MEDIA_ENGINE_CANPLAY *answer);

    HRESULT (STDMETHODCALLTYPE *BeginCreateObject)(
        IMFMediaEngineExtension *This,
        BSTR url,
        IMFByteStream *bytestream,
        MF_OBJECT_TYPE type,
        IUnknown **cancel_cookie,
        IMFAsyncCallback *callback,
        IUnknown *state);

    HRESULT (STDMETHODCALLTYPE *CancelObjectCreation)(
        IMFMediaEngineExtension *This,
        IUnknown *cancel_cookie);

    HRESULT (STDMETHODCALLTYPE *EndCreateObject)(
        IMFMediaEngineExtension *This,
        IMFAsyncResult *result,
        IUnknown **object);

    END_INTERFACE
} IMFMediaEngineExtensionVtbl;

interface IMFMediaEngineExtension {
    CONST_VTBL IMFMediaEngineExtensionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFMediaEngineExtension_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFMediaEngineExtension_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFMediaEngineExtension_Release(This) (This)->lpVtbl->Release(This)
/*** IMFMediaEngineExtension methods ***/
#define IMFMediaEngineExtension_CanPlayType(This,audio_only,mime_type,answer) (This)->lpVtbl->CanPlayType(This,audio_only,mime_type,answer)
#define IMFMediaEngineExtension_BeginCreateObject(This,url,bytestream,type,cancel_cookie,callback,state) (This)->lpVtbl->BeginCreateObject(This,url,bytestream,type,cancel_cookie,callback,state)
#define IMFMediaEngineExtension_CancelObjectCreation(This,cancel_cookie) (This)->lpVtbl->CancelObjectCreation(This,cancel_cookie)
#define IMFMediaEngineExtension_EndCreateObject(This,result,object) (This)->lpVtbl->EndCreateObject(This,result,object)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFMediaEngineExtension_QueryInterface(IMFMediaEngineExtension* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFMediaEngineExtension_AddRef(IMFMediaEngineExtension* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFMediaEngineExtension_Release(IMFMediaEngineExtension* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFMediaEngineExtension methods ***/
static inline HRESULT IMFMediaEngineExtension_CanPlayType(IMFMediaEngineExtension* This,WINBOOL audio_only,BSTR mime_type,MF_MEDIA_ENGINE_CANPLAY *answer) {
    return This->lpVtbl->CanPlayType(This,audio_only,mime_type,answer);
}
static inline HRESULT IMFMediaEngineExtension_BeginCreateObject(IMFMediaEngineExtension* This,BSTR url,IMFByteStream *bytestream,MF_OBJECT_TYPE type,IUnknown **cancel_cookie,IMFAsyncCallback *callback,IUnknown *state) {
    return This->lpVtbl->BeginCreateObject(This,url,bytestream,type,cancel_cookie,callback,state);
}
static inline HRESULT IMFMediaEngineExtension_CancelObjectCreation(IMFMediaEngineExtension* This,IUnknown *cancel_cookie) {
    return This->lpVtbl->CancelObjectCreation(This,cancel_cookie);
}
static inline HRESULT IMFMediaEngineExtension_EndCreateObject(IMFMediaEngineExtension* This,IMFAsyncResult *result,IUnknown **object) {
    return This->lpVtbl->EndCreateObject(This,result,object);
}
#endif
#endif

#endif


#endif  /* __IMFMediaEngineExtension_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __mfmediaengine_h__ */
