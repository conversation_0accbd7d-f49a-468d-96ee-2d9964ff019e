# Copyright 2023 The TensorFlow Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# ==============================================================================

class FileNotFoundException(Exception): ...

class FingerprintException(Exception): ...

def CreateFingerprintDef(export_dir: str) -> bytes: ...
def ReadSavedModelFingerprint(export_dir: str) -> bytes: ...
def Singleprint(graph_def_program_hash: int, signature_def_hash: int, saved_object_graph_hash: int, checkpoint_hash: int) -> str: ...
def SingleprintFromFP(export_dir: str) -> str: ...
def SingleprintFromSM(export_dir: str) -> str: ...
