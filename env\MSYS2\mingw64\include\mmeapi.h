/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */

#ifndef _MMEAPI_H_
#define _MMEAPI_H_

#include <apiset.h>
#include <apisetcconv.h>

#ifdef _CONTRACT_GEN
#include <nt.h>
#include <ntrtl.h>
#include <nturtl.h>
#include <windows.h>
#endif

#include <mmsyscom.h>

#ifdef __cplusplus
extern "C" {
#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)

#ifndef MMNOWAVE

#define WAVERR_BADFORMAT (WAVERR_BASE + 0)
#define WAVERR_STILLPLAYING (WAVERR_BASE + 1)
#define WAVERR_UNPREPARED (WAVERR_BASE + 2)
#define WAVERR_SYNC (WAVERR_BASE + 3)
#define WAVERR_LASTERROR (WAVERR_BASE + 3)

DECLARE_HANDLE(HWAVE);
DECLARE_HANDLE(HWAVEIN);
DECLARE_HANDLE(HWAVEOUT);
typedef HWAVEIN *LPHWAVEIN;
typedef HWAVEOUT *LPHWAVEOUT;
typedef DRVCALLBACK WAVECALLBACK;
typedef WAVECALLBACK *LPWAVECALLBACK;

#define WOM_OPEN MM_WOM_OPEN
#define WOM_CLOSE MM_WOM_CLOSE
#define WOM_DONE MM_WOM_DONE
#define WIM_OPEN MM_WIM_OPEN
#define WIM_CLOSE MM_WIM_CLOSE
#define WIM_DATA MM_WIM_DATA

#define WAVE_MAPPER ((UINT)-1)

#define WAVE_FORMAT_QUERY 0x0001
#define WAVE_ALLOWSYNC 0x0002
#define WAVE_MAPPED 0x0004
#define WAVE_FORMAT_DIRECT 0x0008
#define WAVE_FORMAT_DIRECT_QUERY (WAVE_FORMAT_QUERY | WAVE_FORMAT_DIRECT)
#define WAVE_MAPPED_DEFAULT_COMMUNICATION_DEVICE 0x0010

typedef struct wavehdr_tag {
  LPSTR lpData;
  DWORD dwBufferLength;
  DWORD dwBytesRecorded;
  DWORD_PTR dwUser;
  DWORD dwFlags;
  DWORD dwLoops;
  struct wavehdr_tag *lpNext;
  DWORD_PTR reserved;
} WAVEHDR, *PWAVEHDR, *NPWAVEHDR, *LPWAVEHDR;

#define WHDR_DONE 0x00000001
#define WHDR_PREPARED 0x00000002
#define WHDR_BEGINLOOP 0x00000004
#define WHDR_ENDLOOP 0x00000008
#define WHDR_INQUEUE 0x00000010

typedef struct tagWAVEOUTCAPSA {
  WORD wMid;
  WORD wPid;
  MMVERSION vDriverVersion;
  CHAR szPname[MAXPNAMELEN];
  DWORD dwFormats;
  WORD wChannels;
  WORD wReserved1;
  DWORD dwSupport;
} WAVEOUTCAPSA, *PWAVEOUTCAPSA, *NPWAVEOUTCAPSA, *LPWAVEOUTCAPSA;

typedef struct tagWAVEOUTCAPSW {
  WORD wMid;
  WORD wPid;
  MMVERSION vDriverVersion;
  WCHAR szPname[MAXPNAMELEN];
  DWORD dwFormats;
  WORD wChannels;
  WORD wReserved1;
  DWORD dwSupport;
} WAVEOUTCAPSW, *PWAVEOUTCAPSW, *NPWAVEOUTCAPSW, *LPWAVEOUTCAPSW;

__MINGW_TYPEDEF_AW(WAVEOUTCAPS)
__MINGW_TYPEDEF_AW(PWAVEOUTCAPS)
__MINGW_TYPEDEF_AW(NPWAVEOUTCAPS)
__MINGW_TYPEDEF_AW(LPWAVEOUTCAPS)

typedef struct tagWAVEOUTCAPS2A {
  WORD wMid;
  WORD wPid;
  MMVERSION vDriverVersion;
  CHAR szPname[MAXPNAMELEN];
  DWORD dwFormats;
  WORD wChannels;
  WORD wReserved1;
  DWORD dwSupport;
  GUID ManufacturerGuid;
  GUID ProductGuid;
  GUID NameGuid;
} WAVEOUTCAPS2A, *PWAVEOUTCAPS2A, *NPWAVEOUTCAPS2A, *LPWAVEOUTCAPS2A;

typedef struct tagWAVEOUTCAPS2W {
  WORD wMid;
  WORD wPid;
  MMVERSION vDriverVersion;
  WCHAR szPname[MAXPNAMELEN];
  DWORD dwFormats;
  WORD wChannels;
  WORD wReserved1;
  DWORD dwSupport;
  GUID ManufacturerGuid;
  GUID ProductGuid;
  GUID NameGuid;
} WAVEOUTCAPS2W, *PWAVEOUTCAPS2W, *NPWAVEOUTCAPS2W, *LPWAVEOUTCAPS2W;

__MINGW_TYPEDEF_AW(WAVEOUTCAPS2)
__MINGW_TYPEDEF_AW(PWAVEOUTCAPS2)
__MINGW_TYPEDEF_AW(NPWAVEOUTCAPS2)
__MINGW_TYPEDEF_AW(LPWAVEOUTCAPS2)

#define WAVECAPS_PITCH 0x0001
#define WAVECAPS_PLAYBACKRATE 0x0002
#define WAVECAPS_VOLUME 0x0004
#define WAVECAPS_LRVOLUME 0x0008
#define WAVECAPS_SYNC 0x0010
#define WAVECAPS_SAMPLEACCURATE 0x0020

typedef struct tagWAVEINCAPSA {
  WORD wMid;
  WORD wPid;
  MMVERSION vDriverVersion;
  CHAR szPname[MAXPNAMELEN];
  DWORD dwFormats;
  WORD wChannels;
  WORD wReserved1;
} WAVEINCAPSA, *PWAVEINCAPSA, *NPWAVEINCAPSA, *LPWAVEINCAPSA;

typedef struct tagWAVEINCAPSW {
  WORD wMid;
  WORD wPid;
  MMVERSION vDriverVersion;
  WCHAR szPname[MAXPNAMELEN];
  DWORD dwFormats;
  WORD wChannels;
  WORD wReserved1;
} WAVEINCAPSW, *PWAVEINCAPSW, *NPWAVEINCAPSW, *LPWAVEINCAPSW;

__MINGW_TYPEDEF_AW(WAVEINCAPS)
__MINGW_TYPEDEF_AW(PWAVEINCAPS)
__MINGW_TYPEDEF_AW(NPWAVEINCAPS)
__MINGW_TYPEDEF_AW(LPWAVEINCAPS)

typedef struct tagWAVEINCAPS2A {
  WORD wMid;
  WORD wPid;
  MMVERSION vDriverVersion;
  CHAR szPname[MAXPNAMELEN];
  DWORD dwFormats;
  WORD wChannels;
  WORD wReserved1;
  GUID ManufacturerGuid;
  GUID ProductGuid;
  GUID NameGuid;
} WAVEINCAPS2A, *PWAVEINCAPS2A, *NPWAVEINCAPS2A, *LPWAVEINCAPS2A;

typedef struct tagWAVEINCAPS2W {
  WORD wMid;
  WORD wPid;
  MMVERSION vDriverVersion;
  WCHAR szPname[MAXPNAMELEN];
  DWORD dwFormats;
  WORD wChannels;
  WORD wReserved1;
  GUID ManufacturerGuid;
  GUID ProductGuid;
  GUID NameGuid;
} WAVEINCAPS2W, *PWAVEINCAPS2W, *NPWAVEINCAPS2W, *LPWAVEINCAPS2W;

__MINGW_TYPEDEF_AW(WAVEINCAPS2)
__MINGW_TYPEDEF_AW(PWAVEINCAPS2)
__MINGW_TYPEDEF_AW(NPWAVEINCAPS2)
__MINGW_TYPEDEF_AW(LPWAVEINCAPS2)

#define WAVE_INVALIDFORMAT 0x00000000
#define WAVE_FORMAT_1M08 0x00000001
#define WAVE_FORMAT_1S08 0x00000002
#define WAVE_FORMAT_1M16 0x00000004
#define WAVE_FORMAT_1S16 0x00000008
#define WAVE_FORMAT_2M08 0x00000010
#define WAVE_FORMAT_2S08 0x00000020
#define WAVE_FORMAT_2M16 0x00000040
#define WAVE_FORMAT_2S16 0x00000080
#define WAVE_FORMAT_4M08 0x00000100
#define WAVE_FORMAT_4S08 0x00000200
#define WAVE_FORMAT_4M16 0x00000400
#define WAVE_FORMAT_4S16 0x00000800

#define WAVE_FORMAT_44M08 0x00000100
#define WAVE_FORMAT_44S08 0x00000200
#define WAVE_FORMAT_44M16 0x00000400
#define WAVE_FORMAT_44S16 0x00000800
#define WAVE_FORMAT_48M08 0x00001000
#define WAVE_FORMAT_48S08 0x00002000
#define WAVE_FORMAT_48M16 0x00004000
#define WAVE_FORMAT_48S16 0x00008000
#define WAVE_FORMAT_96M08 0x00010000
#define WAVE_FORMAT_96S08 0x00020000
#define WAVE_FORMAT_96M16 0x00040000
#define WAVE_FORMAT_96S16 0x00080000

#ifndef WAVE_FORMAT_PCM

typedef struct waveformat_tag {
  WORD wFormatTag;
  WORD nChannels;
  DWORD nSamplesPerSec;
  DWORD nAvgBytesPerSec;
  WORD nBlockAlign;
} WAVEFORMAT, *PWAVEFORMAT, *NPWAVEFORMAT, *LPWAVEFORMAT;

#define WAVE_FORMAT_PCM 1

typedef struct pcmwaveformat_tag {
  WAVEFORMAT wf;
  WORD wBitsPerSample;
} PCMWAVEFORMAT, *PPCMWAVEFORMAT, *NPPCMWAVEFORMAT, *LPPCMWAVEFORMAT;
#endif /* WAVE_FORMAT_PCM */

#ifndef _WAVEFORMATEX_
#define _WAVEFORMATEX_

typedef struct tWAVEFORMATEX {
  WORD wFormatTag;
  WORD nChannels;
  DWORD nSamplesPerSec;
  DWORD nAvgBytesPerSec;
  WORD nBlockAlign;
  WORD wBitsPerSample;
  WORD cbSize;
} WAVEFORMATEX, *PWAVEFORMATEX, *NPWAVEFORMATEX, *LPWAVEFORMATEX;

#endif /* _WAVEFORMATEX_ */
typedef const WAVEFORMATEX *LPCWAVEFORMATEX;

WINMMAPI UINT WINAPI waveOutGetNumDevs(void);

WINMMAPI MMRESULT WINAPI waveOutGetDevCapsA(UINT_PTR uDeviceID, LPWAVEOUTCAPSA pwoc, UINT cbwoc);
WINMMAPI MMRESULT WINAPI waveOutGetDevCapsW(UINT_PTR uDeviceID, LPWAVEOUTCAPSW pwoc, UINT cbwoc);
#define waveOutGetDevCaps __MINGW_NAME_AW(waveOutGetDevCaps)

WINMMAPI MMRESULT WINAPI waveOutGetVolume(HWAVEOUT hwo, LPDWORD pdwVolume);
WINMMAPI MMRESULT WINAPI waveOutSetVolume(HWAVEOUT hwo, DWORD dwVolume);
WINMMAPI MMRESULT WINAPI waveOutGetErrorTextA(MMRESULT mmrError, LPSTR pszText, UINT cchText);
WINMMAPI MMRESULT WINAPI waveOutGetErrorTextW(MMRESULT mmrError, LPWSTR pszText, UINT cchText);
#define waveOutGetErrorText __MINGW_NAME_AW(waveOutGetErrorText)

WINMMAPI MMRESULT WINAPI waveOutOpen(LPHWAVEOUT phwo, UINT uDeviceID, LPCWAVEFORMATEX pwfx, DWORD_PTR dwCallback, DWORD_PTR dwInstance, DWORD fdwOpen);
WINMMAPI MMRESULT WINAPI waveOutClose(HWAVEOUT hwo);
WINMMAPI MMRESULT WINAPI waveOutPrepareHeader(HWAVEOUT hwo, LPWAVEHDR pwh, UINT cbwh);
WINMMAPI MMRESULT WINAPI waveOutUnprepareHeader(HWAVEOUT hwo, LPWAVEHDR pwh, UINT cbwh);
WINMMAPI MMRESULT WINAPI waveOutWrite(HWAVEOUT hwo, LPWAVEHDR pwh, UINT cbwh);
WINMMAPI MMRESULT WINAPI waveOutPause(HWAVEOUT hwo);
WINMMAPI MMRESULT WINAPI waveOutRestart(HWAVEOUT hwo);
WINMMAPI MMRESULT WINAPI waveOutReset(HWAVEOUT hwo);
WINMMAPI MMRESULT WINAPI waveOutBreakLoop(HWAVEOUT hwo);
WINMMAPI MMRESULT WINAPI waveOutGetPosition(HWAVEOUT hwo, LPMMTIME pmmt, UINT cbmmt);
WINMMAPI MMRESULT WINAPI waveOutGetPitch(HWAVEOUT hwo, LPDWORD pdwPitch);
WINMMAPI MMRESULT WINAPI waveOutSetPitch(HWAVEOUT hwo, DWORD dwPitch);
WINMMAPI MMRESULT WINAPI waveOutGetPlaybackRate(HWAVEOUT hwo, LPDWORD pdwRate);
WINMMAPI MMRESULT WINAPI waveOutSetPlaybackRate(HWAVEOUT hwo, DWORD dwRate);
WINMMAPI MMRESULT WINAPI waveOutGetID(HWAVEOUT hwo, LPUINT puDeviceID);

WINMMAPI MMRESULT WINAPI waveOutMessage(HWAVEOUT hwo, UINT uMsg, DWORD_PTR dw1, DWORD_PTR dw2);

WINMMAPI UINT WINAPI waveInGetNumDevs(void);

WINMMAPI MMRESULT WINAPI waveInGetDevCapsA(UINT_PTR uDeviceID, LPWAVEINCAPSA pwic, UINT cbwic);
WINMMAPI MMRESULT WINAPI waveInGetDevCapsW(UINT_PTR uDeviceID, LPWAVEINCAPSW pwic, UINT cbwic);
#define waveInGetDevCaps __MINGW_NAME_AW(waveInGetDevCaps)

WINMMAPI MMRESULT WINAPI waveInGetErrorTextA(MMRESULT mmrError, LPSTR pszText, UINT cchText);
WINMMAPI MMRESULT WINAPI waveInGetErrorTextW(MMRESULT mmrError, LPWSTR pszText, UINT cchText);
#define waveInGetErrorText __MINGW_NAME_AW(waveInGetErrorText)

WINMMAPI MMRESULT WINAPI waveInOpen(LPHWAVEIN phwi, UINT uDeviceID, LPCWAVEFORMATEX pwfx, DWORD_PTR dwCallback, DWORD_PTR dwInstance, DWORD fdwOpen);
WINMMAPI MMRESULT WINAPI waveInClose(HWAVEIN hwi);
WINMMAPI MMRESULT WINAPI waveInPrepareHeader(HWAVEIN hwi, LPWAVEHDR pwh, UINT cbwh);
WINMMAPI MMRESULT WINAPI waveInUnprepareHeader(HWAVEIN hwi, LPWAVEHDR pwh, UINT cbwh);
WINMMAPI MMRESULT WINAPI waveInAddBuffer(HWAVEIN hwi, LPWAVEHDR pwh, UINT cbwh);
WINMMAPI MMRESULT WINAPI waveInStart(HWAVEIN hwi);
WINMMAPI MMRESULT WINAPI waveInStop(HWAVEIN hwi);
WINMMAPI MMRESULT WINAPI waveInReset(HWAVEIN hwi);
WINMMAPI MMRESULT WINAPI waveInGetPosition(HWAVEIN hwi, LPMMTIME pmmt, UINT cbmmt);
WINMMAPI MMRESULT WINAPI waveInGetID(HWAVEIN hwi, LPUINT puDeviceID);

WINMMAPI MMRESULT WINAPI waveInMessage(HWAVEIN hwi, UINT uMsg, DWORD_PTR dw1, DWORD_PTR dw2);

#endif  /* ifndef MMNOWAVE */

#ifndef MMNOMIDI

#define MIDIERR_UNPREPARED (MIDIERR_BASE + 0)
#define MIDIERR_STILLPLAYING (MIDIERR_BASE + 1)
#define MIDIERR_NOMAP (MIDIERR_BASE + 2)
#define MIDIERR_NOTREADY (MIDIERR_BASE + 3)
#define MIDIERR_NODEVICE (MIDIERR_BASE + 4)
#define MIDIERR_INVALIDSETUP (MIDIERR_BASE + 5)
#define MIDIERR_BADOPENMODE (MIDIERR_BASE + 6)
#define MIDIERR_DONT_CONTINUE (MIDIERR_BASE + 7)
#define MIDIERR_LASTERROR (MIDIERR_BASE + 7)

DECLARE_HANDLE(HMIDI);
DECLARE_HANDLE(HMIDIIN);
DECLARE_HANDLE(HMIDIOUT);
DECLARE_HANDLE(HMIDISTRM);
typedef HMIDI *LPHMIDI;
typedef HMIDIIN *LPHMIDIIN;
typedef HMIDIOUT *LPHMIDIOUT;
typedef HMIDISTRM *LPHMIDISTRM;
typedef DRVCALLBACK MIDICALLBACK;
typedef MIDICALLBACK *LPMIDICALLBACK;
#define MIDIPATCHSIZE 128
typedef WORD PATCHARRAY[MIDIPATCHSIZE];
typedef WORD *LPPATCHARRAY;
typedef WORD KEYARRAY[MIDIPATCHSIZE];
typedef WORD *LPKEYARRAY;

#define MIM_OPEN MM_MIM_OPEN
#define MIM_CLOSE MM_MIM_CLOSE
#define MIM_DATA MM_MIM_DATA
#define MIM_LONGDATA MM_MIM_LONGDATA
#define MIM_ERROR MM_MIM_ERROR
#define MIM_LONGERROR MM_MIM_LONGERROR
#define MOM_OPEN MM_MOM_OPEN
#define MOM_CLOSE MM_MOM_CLOSE
#define MOM_DONE MM_MOM_DONE

#define MIM_MOREDATA MM_MIM_MOREDATA
#define MOM_POSITIONCB MM_MOM_POSITIONCB

#define MIDIMAPPER ((UINT)-1)
#define MIDI_MAPPER ((UINT)-1)

#define MIDI_IO_STATUS __MSABI_LONG(0x00000020)

#define MIDI_CACHE_ALL 1
#define MIDI_CACHE_BESTFIT 2
#define MIDI_CACHE_QUERY 3
#define MIDI_UNCACHE 4

typedef struct tagMIDIOUTCAPSA {
  WORD wMid;
  WORD wPid;
  MMVERSION vDriverVersion;
  CHAR szPname[MAXPNAMELEN];
  WORD wTechnology;
  WORD wVoices;
  WORD wNotes;
  WORD wChannelMask;
  DWORD dwSupport;
} MIDIOUTCAPSA, *PMIDIOUTCAPSA, *NPMIDIOUTCAPSA, *LPMIDIOUTCAPSA;

typedef struct tagMIDIOUTCAPSW {
  WORD wMid;
  WORD wPid;
  MMVERSION vDriverVersion;
  WCHAR szPname[MAXPNAMELEN];
  WORD wTechnology;
  WORD wVoices;
  WORD wNotes;
  WORD wChannelMask;
  DWORD dwSupport;
} MIDIOUTCAPSW, *PMIDIOUTCAPSW, *NPMIDIOUTCAPSW, *LPMIDIOUTCAPSW;

__MINGW_TYPEDEF_AW(MIDIOUTCAPS)
__MINGW_TYPEDEF_AW(PMIDIOUTCAPS)
__MINGW_TYPEDEF_AW(NPMIDIOUTCAPS)
__MINGW_TYPEDEF_AW(LPMIDIOUTCAPS)

typedef struct tagMIDIOUTCAPS2A {
  WORD wMid;
  WORD wPid;
  MMVERSION vDriverVersion;
  CHAR szPname[MAXPNAMELEN];
  WORD wTechnology;
  WORD wVoices;
  WORD wNotes;
  WORD wChannelMask;
  DWORD dwSupport;
  GUID ManufacturerGuid;
  GUID ProductGuid;
  GUID NameGuid;
} MIDIOUTCAPS2A, *PMIDIOUTCAPS2A, *NPMIDIOUTCAPS2A, *LPMIDIOUTCAPS2A;

typedef struct tagMIDIOUTCAPS2W {
  WORD wMid;
  WORD wPid;
  MMVERSION vDriverVersion;
  WCHAR szPname[MAXPNAMELEN];
  WORD wTechnology;
  WORD wVoices;
  WORD wNotes;
  WORD wChannelMask;
  DWORD dwSupport;
  GUID ManufacturerGuid;
  GUID ProductGuid;
  GUID NameGuid;
} MIDIOUTCAPS2W, *PMIDIOUTCAPS2W, *NPMIDIOUTCAPS2W, *LPMIDIOUTCAPS2W;

__MINGW_TYPEDEF_AW(MIDIOUTCAPS2)
__MINGW_TYPEDEF_AW(PMIDIOUTCAPS2)
__MINGW_TYPEDEF_AW(NPMIDIOUTCAPS2)
__MINGW_TYPEDEF_AW(LPMIDIOUTCAPS2)

#define MOD_MIDIPORT 1
#define MOD_SYNTH 2
#define MOD_SQSYNTH 3
#define MOD_FMSYNTH 4
#define MOD_MAPPER 5
#define MOD_WAVETABLE 6
#define MOD_SWSYNTH 7

#define MIDICAPS_VOLUME 0x0001
#define MIDICAPS_LRVOLUME 0x0002
#define MIDICAPS_CACHE 0x0004
#define MIDICAPS_STREAM 0x0008

typedef struct tagMIDIINCAPSA {
  WORD wMid;
  WORD wPid;
  MMVERSION vDriverVersion;
  CHAR szPname[MAXPNAMELEN];
  DWORD dwSupport;
} MIDIINCAPSA, *PMIDIINCAPSA, *NPMIDIINCAPSA, *LPMIDIINCAPSA;

typedef struct tagMIDIINCAPSW {
  WORD wMid;
  WORD wPid;
  MMVERSION vDriverVersion;
  WCHAR szPname[MAXPNAMELEN];
  DWORD dwSupport;
} MIDIINCAPSW, *PMIDIINCAPSW, *NPMIDIINCAPSW, *LPMIDIINCAPSW;

__MINGW_TYPEDEF_AW(MIDIINCAPS)
__MINGW_TYPEDEF_AW(PMIDIINCAPS)
__MINGW_TYPEDEF_AW(NPMIDIINCAPS)
__MINGW_TYPEDEF_AW(LPMIDIINCAPS)

typedef struct tagMIDIINCAPS2A {
  WORD wMid;
  WORD wPid;
  MMVERSION vDriverVersion;
  CHAR szPname[MAXPNAMELEN];
  DWORD dwSupport;
  GUID ManufacturerGuid;
  GUID ProductGuid;
  GUID NameGuid;
} MIDIINCAPS2A, *PMIDIINCAPS2A, *NPMIDIINCAPS2A, *LPMIDIINCAPS2A;

typedef struct tagMIDIINCAPS2W {
  WORD wMid;
  WORD wPid;
  MMVERSION vDriverVersion;
  WCHAR szPname[MAXPNAMELEN];
  DWORD dwSupport;
  GUID ManufacturerGuid;
  GUID ProductGuid;
  GUID NameGuid;
} MIDIINCAPS2W, *PMIDIINCAPS2W, *NPMIDIINCAPS2W, *LPMIDIINCAPS2W;

__MINGW_TYPEDEF_AW(MIDIINCAPS2)
__MINGW_TYPEDEF_AW(PMIDIINCAPS2)
__MINGW_TYPEDEF_AW(NPMIDIINCAPS2)
__MINGW_TYPEDEF_AW(LPMIDIINCAPS2)

typedef struct midihdr_tag {
  LPSTR lpData;
  DWORD dwBufferLength;
  DWORD dwBytesRecorded;
  DWORD_PTR dwUser;
  DWORD dwFlags;
  struct midihdr_tag far *lpNext;
  DWORD_PTR reserved;
  DWORD dwOffset;
  DWORD_PTR dwReserved[8];
} MIDIHDR, *PMIDIHDR, *NPMIDIHDR, *LPMIDIHDR;

typedef struct midievent_tag {
  DWORD dwDeltaTime;
  DWORD dwStreamID;
  DWORD dwEvent;
  DWORD dwParms[1];
} MIDIEVENT;

typedef struct midistrmbuffver_tag {
  DWORD dwVersion;
  DWORD dwMid;
  DWORD dwOEMVersion;
} MIDISTRMBUFFVER;

#define MHDR_DONE 0x00000001
#define MHDR_PREPARED 0x00000002
#define MHDR_INQUEUE 0x00000004
#define MHDR_ISSTRM 0x00000008

#define MEVT_F_SHORT __MSABI_LONG(0x00000000)
#define MEVT_F_LONG __MSABI_LONG(0x80000000)
#define MEVT_F_CALLBACK __MSABI_LONG(0x40000000)

#define MEVT_EVENTTYPE(x) ((BYTE)(((x)>>24)&0xFF))
#define MEVT_EVENTPARM(x) ((DWORD)((x)&0x00FFFFFFL))

#define MEVT_SHORTMSG ((BYTE)0x00)
#define MEVT_TEMPO ((BYTE)0x01)
#define MEVT_NOP ((BYTE)0x02)

#define MEVT_LONGMSG ((BYTE)0x80)
#define MEVT_COMMENT ((BYTE)0x82)
#define MEVT_VERSION ((BYTE)0x84)

#define MIDISTRM_ERROR (-2)

#define MIDIPROP_SET __MSABI_LONG(0x80000000)
#define MIDIPROP_GET __MSABI_LONG(0x40000000)

#define MIDIPROP_TIMEDIV __MSABI_LONG(0x00000001)
#define MIDIPROP_TEMPO __MSABI_LONG(0x00000002)

typedef struct midiproptimediv_tag {
  DWORD cbStruct;
  DWORD dwTimeDiv;
} MIDIPROPTIMEDIV, *LPMIDIPROPTIMEDIV;

typedef struct midiproptempo_tag {
  DWORD cbStruct;
  DWORD dwTempo;
} MIDIPROPTEMPO, *LPMIDIPROPTEMPO;

WINMMAPI UINT WINAPI midiOutGetNumDevs(void);
WINMMAPI MMRESULT WINAPI midiStreamOpen(LPHMIDISTRM phms, LPUINT puDeviceID, DWORD cMidi, DWORD_PTR dwCallback, DWORD_PTR dwInstance, DWORD fdwOpen);
WINMMAPI MMRESULT WINAPI midiStreamClose(HMIDISTRM hms);
WINMMAPI MMRESULT WINAPI midiStreamProperty(HMIDISTRM hms, LPBYTE lppropdata, DWORD dwProperty);
WINMMAPI MMRESULT WINAPI midiStreamPosition(HMIDISTRM hms, LPMMTIME lpmmt, UINT cbmmt);
WINMMAPI MMRESULT WINAPI midiStreamOut(HMIDISTRM hms, LPMIDIHDR pmh, UINT cbmh);
WINMMAPI MMRESULT WINAPI midiStreamPause(HMIDISTRM hms);
WINMMAPI MMRESULT WINAPI midiStreamRestart(HMIDISTRM hms);
WINMMAPI MMRESULT WINAPI midiStreamStop(HMIDISTRM hms);

WINMMAPI MMRESULT WINAPI midiConnect(HMIDI hmi, HMIDIOUT hmo, LPVOID pReserved);
WINMMAPI MMRESULT WINAPI midiDisconnect(HMIDI hmi, HMIDIOUT hmo, LPVOID pReserved);

WINMMAPI MMRESULT WINAPI midiOutGetDevCapsA(UINT_PTR uDeviceID, LPMIDIOUTCAPSA pmoc, UINT cbmoc);
WINMMAPI MMRESULT WINAPI midiOutGetDevCapsW(UINT_PTR uDeviceID, LPMIDIOUTCAPSW pmoc, UINT cbmoc);
#define midiOutGetDevCaps __MINGW_NAME_AW(midiOutGetDevCaps)

WINMMAPI MMRESULT WINAPI midiOutGetVolume(HMIDIOUT hmo, LPDWORD pdwVolume);
WINMMAPI MMRESULT WINAPI midiOutSetVolume(HMIDIOUT hmo, DWORD dwVolume);
WINMMAPI MMRESULT WINAPI midiOutGetErrorTextA(MMRESULT mmrError, LPSTR pszText, UINT cchText);
WINMMAPI MMRESULT WINAPI midiOutGetErrorTextW(MMRESULT mmrError, LPWSTR pszText, UINT cchText);
#define midiOutGetErrorText __MINGW_NAME_AW(midiOutGetErrorText)

WINMMAPI MMRESULT WINAPI midiOutOpen(LPHMIDIOUT phmo, UINT uDeviceID, DWORD_PTR dwCallback, DWORD_PTR dwInstance, DWORD fdwOpen);
WINMMAPI MMRESULT WINAPI midiOutClose(HMIDIOUT hmo);
WINMMAPI MMRESULT WINAPI midiOutPrepareHeader(HMIDIOUT hmo, LPMIDIHDR pmh, UINT cbmh);
WINMMAPI MMRESULT WINAPI midiOutUnprepareHeader(HMIDIOUT hmo, LPMIDIHDR pmh, UINT cbmh);
WINMMAPI MMRESULT WINAPI midiOutShortMsg(HMIDIOUT hmo, DWORD dwMsg);
WINMMAPI MMRESULT WINAPI midiOutLongMsg(HMIDIOUT hmo, LPMIDIHDR pmh, UINT cbmh);
WINMMAPI MMRESULT WINAPI midiOutReset(HMIDIOUT hmo);
WINMMAPI MMRESULT WINAPI midiOutCachePatches(HMIDIOUT hmo, UINT uBank, LPWORD pwpa, UINT fuCache);
WINMMAPI MMRESULT WINAPI midiOutCacheDrumPatches(HMIDIOUT hmo, UINT uPatch, LPWORD pwkya, UINT fuCache);
WINMMAPI MMRESULT WINAPI midiOutGetID(HMIDIOUT hmo, LPUINT puDeviceID);

WINMMAPI MMRESULT WINAPI midiOutMessage(HMIDIOUT hmo, UINT uMsg, DWORD_PTR dw1, DWORD_PTR dw2);

WINMMAPI UINT WINAPI midiInGetNumDevs(void);

WINMMAPI MMRESULT WINAPI midiInGetDevCapsA(UINT_PTR uDeviceID, LPMIDIINCAPSA pmic, UINT cbmic);
WINMMAPI MMRESULT WINAPI midiInGetDevCapsW(UINT_PTR uDeviceID, LPMIDIINCAPSW pmic, UINT cbmic);
#define midiInGetDevCaps __MINGW_NAME_AW(midiInGetDevCaps)

WINMMAPI MMRESULT WINAPI midiInGetErrorTextA(MMRESULT mmrError, LPSTR pszText, UINT cchText);
WINMMAPI MMRESULT WINAPI midiInGetErrorTextW(MMRESULT mmrError, LPWSTR pszText, UINT cchText);
#define midiInGetErrorText __MINGW_NAME_AW(midiInGetErrorText)

WINMMAPI MMRESULT WINAPI midiInOpen(LPHMIDIIN phmi, UINT uDeviceID, DWORD_PTR dwCallback, DWORD_PTR dwInstance, DWORD fdwOpen);
WINMMAPI MMRESULT WINAPI midiInClose(HMIDIIN hmi);
WINMMAPI MMRESULT WINAPI midiInPrepareHeader(HMIDIIN hmi, LPMIDIHDR pmh, UINT cbmh);
WINMMAPI MMRESULT WINAPI midiInUnprepareHeader(HMIDIIN hmi, LPMIDIHDR pmh, UINT cbmh);
WINMMAPI MMRESULT WINAPI midiInAddBuffer(HMIDIIN hmi, LPMIDIHDR pmh, UINT cbmh);
WINMMAPI MMRESULT WINAPI midiInStart(HMIDIIN hmi);
WINMMAPI MMRESULT WINAPI midiInStop(HMIDIIN hmi);
WINMMAPI MMRESULT WINAPI midiInReset(HMIDIIN hmi);
WINMMAPI MMRESULT WINAPI midiInGetID(HMIDIIN hmi, LPUINT puDeviceID);

WINMMAPI MMRESULT WINAPI midiInMessage(HMIDIIN hmi, UINT uMsg, DWORD_PTR dw1, DWORD_PTR dw2);
#endif  /* ifndef MMNOMIDI */

#ifndef MMNOAUX

#define AUX_MAPPER ((UINT)-1)

typedef struct tagAUXCAPSA {
  WORD wMid;
  WORD wPid;
  MMVERSION vDriverVersion;
  CHAR szPname[MAXPNAMELEN];
  WORD wTechnology;
  WORD wReserved1;
  DWORD dwSupport;
} AUXCAPSA, *PAUXCAPSA, *NPAUXCAPSA, *LPAUXCAPSA;

typedef struct tagAUXCAPSW {
  WORD wMid;
  WORD wPid;
  MMVERSION vDriverVersion;
  WCHAR szPname[MAXPNAMELEN];
  WORD wTechnology;
  WORD wReserved1;
  DWORD dwSupport;
} AUXCAPSW, *PAUXCAPSW, *NPAUXCAPSW, *LPAUXCAPSW;

__MINGW_TYPEDEF_AW(AUXCAPS)
__MINGW_TYPEDEF_AW(PAUXCAPS)
__MINGW_TYPEDEF_AW(NPAUXCAPS)
__MINGW_TYPEDEF_AW(LPAUXCAPS)

typedef struct tagAUXCAPS2A {
  WORD wMid;
  WORD wPid;
  MMVERSION vDriverVersion;
  CHAR szPname[MAXPNAMELEN];
  WORD wTechnology;
  WORD wReserved1;
  DWORD dwSupport;
  GUID ManufacturerGuid;
  GUID ProductGuid;
  GUID NameGuid;
} AUXCAPS2A, *PAUXCAPS2A, *NPAUXCAPS2A, *LPAUXCAPS2A;

typedef struct tagAUXCAPS2W {
  WORD wMid;
  WORD wPid;
  MMVERSION vDriverVersion;
  WCHAR szPname[MAXPNAMELEN];
  WORD wTechnology;
  WORD wReserved1;
  DWORD dwSupport;
  GUID ManufacturerGuid;
  GUID ProductGuid;
  GUID NameGuid;
} AUXCAPS2W, *PAUXCAPS2W, *NPAUXCAPS2W, *LPAUXCAPS2W;

__MINGW_TYPEDEF_AW(AUXCAPS2)
__MINGW_TYPEDEF_AW(PAUXCAPS2)
__MINGW_TYPEDEF_AW(NPAUXCAPS2)
__MINGW_TYPEDEF_AW(LPAUXCAPS2)

#define AUXCAPS_CDAUDIO 1
#define AUXCAPS_AUXIN 2

#define AUXCAPS_VOLUME 0x0001
#define AUXCAPS_LRVOLUME 0x0002

WINMMAPI UINT WINAPI auxGetNumDevs(void);

WINMMAPI MMRESULT WINAPI auxGetDevCapsA(UINT_PTR uDeviceID, LPAUXCAPSA pac, UINT cbac);
WINMMAPI MMRESULT WINAPI auxGetDevCapsW(UINT_PTR uDeviceID, LPAUXCAPSW pac, UINT cbac);
#define auxGetDevCaps __MINGW_NAME_AW(auxGetDevCaps)

WINMMAPI MMRESULT WINAPI auxSetVolume(UINT uDeviceID, DWORD dwVolume);
WINMMAPI MMRESULT WINAPI auxGetVolume(UINT uDeviceID, LPDWORD pdwVolume);

WINMMAPI MMRESULT WINAPI auxOutMessage(UINT uDeviceID, UINT uMsg, DWORD_PTR dw1, DWORD_PTR dw2);

#endif  /* ifndef MMNOAUX */

#ifndef MMNOMIXER

DECLARE_HANDLE(HMIXEROBJ);
typedef HMIXEROBJ *LPHMIXEROBJ;

DECLARE_HANDLE(HMIXER);
typedef HMIXER *LPHMIXER;

#define MIXER_SHORT_NAME_CHARS 16
#define MIXER_LONG_NAME_CHARS 64

#define MIXERR_INVALLINE (MIXERR_BASE + 0)
#define MIXERR_INVALCONTROL (MIXERR_BASE + 1)
#define MIXERR_INVALVALUE (MIXERR_BASE + 2)
#define MIXERR_LASTERROR (MIXERR_BASE + 2)

#define MIXER_OBJECTF_HANDLE __MSABI_LONG(0x80000000)
#define MIXER_OBJECTF_MIXER __MSABI_LONG(0x00000000)
#define MIXER_OBJECTF_HMIXER (MIXER_OBJECTF_HANDLE|MIXER_OBJECTF_MIXER)
#define MIXER_OBJECTF_WAVEOUT __MSABI_LONG(0x10000000)
#define MIXER_OBJECTF_HWAVEOUT (MIXER_OBJECTF_HANDLE|MIXER_OBJECTF_WAVEOUT)
#define MIXER_OBJECTF_WAVEIN __MSABI_LONG(0x20000000)
#define MIXER_OBJECTF_HWAVEIN (MIXER_OBJECTF_HANDLE|MIXER_OBJECTF_WAVEIN)
#define MIXER_OBJECTF_MIDIOUT __MSABI_LONG(0x30000000)
#define MIXER_OBJECTF_HMIDIOUT (MIXER_OBJECTF_HANDLE|MIXER_OBJECTF_MIDIOUT)
#define MIXER_OBJECTF_MIDIIN __MSABI_LONG(0x40000000)
#define MIXER_OBJECTF_HMIDIIN (MIXER_OBJECTF_HANDLE|MIXER_OBJECTF_MIDIIN)
#define MIXER_OBJECTF_AUX __MSABI_LONG(0x50000000)

WINMMAPI UINT WINAPI mixerGetNumDevs(void);

typedef struct tagMIXERCAPSA {
  WORD wMid;
  WORD wPid;
  MMVERSION vDriverVersion;
  CHAR szPname[MAXPNAMELEN];
  DWORD fdwSupport;
  DWORD cDestinations;
} MIXERCAPSA, *PMIXERCAPSA, *LPMIXERCAPSA;

typedef struct tagMIXERCAPSW {
  WORD wMid;
  WORD wPid;
  MMVERSION vDriverVersion;
  WCHAR szPname[MAXPNAMELEN];
  DWORD fdwSupport;
  DWORD cDestinations;
} MIXERCAPSW, *PMIXERCAPSW, *LPMIXERCAPSW;

__MINGW_TYPEDEF_AW(MIXERCAPS)
__MINGW_TYPEDEF_AW(PMIXERCAPS)
__MINGW_TYPEDEF_AW(LPMIXERCAPS)

typedef struct tagMIXERCAPS2A {
  WORD wMid;
  WORD wPid;
  MMVERSION vDriverVersion;
  CHAR szPname[MAXPNAMELEN];
  DWORD fdwSupport;
  DWORD cDestinations;
  GUID ManufacturerGuid;
  GUID ProductGuid;
  GUID NameGuid;
} MIXERCAPS2A, *PMIXERCAPS2A, *LPMIXERCAPS2A;

typedef struct tagMIXERCAPS2W {
  WORD wMid;
  WORD wPid;
  MMVERSION vDriverVersion;
  WCHAR szPname[MAXPNAMELEN];
  DWORD fdwSupport;
  DWORD cDestinations;
  GUID ManufacturerGuid;
  GUID ProductGuid;
  GUID NameGuid;
} MIXERCAPS2W, *PMIXERCAPS2W, *LPMIXERCAPS2W;

__MINGW_TYPEDEF_AW(MIXERCAPS2)
__MINGW_TYPEDEF_AW(PMIXERCAPS2)
__MINGW_TYPEDEF_AW(LPMIXERCAPS2)

WINMMAPI MMRESULT WINAPI mixerGetDevCapsA(UINT_PTR uMxId, LPMIXERCAPSA pmxcaps, UINT cbmxcaps);
WINMMAPI MMRESULT WINAPI mixerGetDevCapsW(UINT_PTR uMxId, LPMIXERCAPSW pmxcaps, UINT cbmxcaps);
#define mixerGetDevCaps __MINGW_NAME_AW(mixerGetDevCaps)

WINMMAPI MMRESULT WINAPI mixerOpen(LPHMIXER phmx, UINT uMxId, DWORD_PTR dwCallback, DWORD_PTR dwInstance, DWORD fdwOpen);
WINMMAPI MMRESULT WINAPI mixerClose(HMIXER hmx);
WINMMAPI DWORD WINAPI mixerMessage(HMIXER hmx, UINT uMsg, DWORD_PTR dwParam1, DWORD_PTR dwParam2);

typedef struct tagMIXERLINEA {
  DWORD cbStruct;
  DWORD dwDestination;
  DWORD dwSource;
  DWORD dwLineID;
  DWORD fdwLine;
  DWORD_PTR dwUser;
  DWORD dwComponentType;
  DWORD cChannels;
  DWORD cConnections;
  DWORD cControls;
  CHAR szShortName[MIXER_SHORT_NAME_CHARS];
  CHAR szName[MIXER_LONG_NAME_CHARS];
  struct {
    DWORD dwType;
    DWORD dwDeviceID;
    WORD wMid;
    WORD wPid;
    MMVERSION vDriverVersion;
    CHAR szPname[MAXPNAMELEN];
  } Target;
} MIXERLINEA, *PMIXERLINEA, *LPMIXERLINEA;

typedef struct tagMIXERLINEW {
  DWORD cbStruct;
  DWORD dwDestination;
  DWORD dwSource;
  DWORD dwLineID;
  DWORD fdwLine;
  DWORD_PTR dwUser;
  DWORD dwComponentType;
  DWORD cChannels;
  DWORD cConnections;
  DWORD cControls;
  WCHAR szShortName[MIXER_SHORT_NAME_CHARS];
  WCHAR szName[MIXER_LONG_NAME_CHARS];
  struct {
    DWORD dwType;
    DWORD dwDeviceID;
    WORD wMid;
    WORD wPid;
    MMVERSION vDriverVersion;
    WCHAR szPname[MAXPNAMELEN];
  } Target;
} MIXERLINEW, *PMIXERLINEW, *LPMIXERLINEW;

__MINGW_TYPEDEF_AW(MIXERLINE)
__MINGW_TYPEDEF_AW(PMIXERLINE)
__MINGW_TYPEDEF_AW(LPMIXERLINE)

#define MIXERLINE_LINEF_ACTIVE __MSABI_LONG(0x00000001)
#define MIXERLINE_LINEF_DISCONNECTED __MSABI_LONG(0x00008000)
#define MIXERLINE_LINEF_SOURCE __MSABI_LONG(0x80000000)

#define MIXERLINE_COMPONENTTYPE_DST_FIRST __MSABI_LONG(0x00000000)
#define MIXERLINE_COMPONENTTYPE_DST_UNDEFINED (MIXERLINE_COMPONENTTYPE_DST_FIRST + 0)
#define MIXERLINE_COMPONENTTYPE_DST_DIGITAL (MIXERLINE_COMPONENTTYPE_DST_FIRST + 1)
#define MIXERLINE_COMPONENTTYPE_DST_LINE (MIXERLINE_COMPONENTTYPE_DST_FIRST + 2)
#define MIXERLINE_COMPONENTTYPE_DST_MONITOR (MIXERLINE_COMPONENTTYPE_DST_FIRST + 3)
#define MIXERLINE_COMPONENTTYPE_DST_SPEAKERS (MIXERLINE_COMPONENTTYPE_DST_FIRST + 4)
#define MIXERLINE_COMPONENTTYPE_DST_HEADPHONES (MIXERLINE_COMPONENTTYPE_DST_FIRST + 5)
#define MIXERLINE_COMPONENTTYPE_DST_TELEPHONE (MIXERLINE_COMPONENTTYPE_DST_FIRST + 6)
#define MIXERLINE_COMPONENTTYPE_DST_WAVEIN (MIXERLINE_COMPONENTTYPE_DST_FIRST + 7)
#define MIXERLINE_COMPONENTTYPE_DST_VOICEIN (MIXERLINE_COMPONENTTYPE_DST_FIRST + 8)
#define MIXERLINE_COMPONENTTYPE_DST_LAST (MIXERLINE_COMPONENTTYPE_DST_FIRST + 8)

#define MIXERLINE_COMPONENTTYPE_SRC_FIRST __MSABI_LONG(0x00001000)
#define MIXERLINE_COMPONENTTYPE_SRC_UNDEFINED (MIXERLINE_COMPONENTTYPE_SRC_FIRST + 0)
#define MIXERLINE_COMPONENTTYPE_SRC_DIGITAL (MIXERLINE_COMPONENTTYPE_SRC_FIRST + 1)
#define MIXERLINE_COMPONENTTYPE_SRC_LINE (MIXERLINE_COMPONENTTYPE_SRC_FIRST + 2)
#define MIXERLINE_COMPONENTTYPE_SRC_MICROPHONE (MIXERLINE_COMPONENTTYPE_SRC_FIRST + 3)
#define MIXERLINE_COMPONENTTYPE_SRC_SYNTHESIZER (MIXERLINE_COMPONENTTYPE_SRC_FIRST + 4)
#define MIXERLINE_COMPONENTTYPE_SRC_COMPACTDISC (MIXERLINE_COMPONENTTYPE_SRC_FIRST + 5)
#define MIXERLINE_COMPONENTTYPE_SRC_TELEPHONE (MIXERLINE_COMPONENTTYPE_SRC_FIRST + 6)
#define MIXERLINE_COMPONENTTYPE_SRC_PCSPEAKER (MIXERLINE_COMPONENTTYPE_SRC_FIRST + 7)
#define MIXERLINE_COMPONENTTYPE_SRC_WAVEOUT (MIXERLINE_COMPONENTTYPE_SRC_FIRST + 8)
#define MIXERLINE_COMPONENTTYPE_SRC_AUXILIARY (MIXERLINE_COMPONENTTYPE_SRC_FIRST + 9)
#define MIXERLINE_COMPONENTTYPE_SRC_ANALOG (MIXERLINE_COMPONENTTYPE_SRC_FIRST + 10)
#define MIXERLINE_COMPONENTTYPE_SRC_LAST (MIXERLINE_COMPONENTTYPE_SRC_FIRST + 10)

#define MIXERLINE_TARGETTYPE_UNDEFINED 0
#define MIXERLINE_TARGETTYPE_WAVEOUT 1
#define MIXERLINE_TARGETTYPE_WAVEIN 2
#define MIXERLINE_TARGETTYPE_MIDIOUT 3
#define MIXERLINE_TARGETTYPE_MIDIIN 4
#define MIXERLINE_TARGETTYPE_AUX 5

WINMMAPI MMRESULT WINAPI mixerGetLineInfoA(HMIXEROBJ hmxobj, LPMIXERLINEA pmxl, DWORD fdwInfo);
WINMMAPI MMRESULT WINAPI mixerGetLineInfoW(HMIXEROBJ hmxobj, LPMIXERLINEW pmxl, DWORD fdwInfo);
#define mixerGetLineInfo __MINGW_NAME_AW(mixerGetLineInfo)

#define MIXER_GETLINEINFOF_DESTINATION __MSABI_LONG(0x00000000)
#define MIXER_GETLINEINFOF_SOURCE __MSABI_LONG(0x00000001)
#define MIXER_GETLINEINFOF_LINEID __MSABI_LONG(0x00000002)
#define MIXER_GETLINEINFOF_COMPONENTTYPE __MSABI_LONG(0x00000003)
#define MIXER_GETLINEINFOF_TARGETTYPE __MSABI_LONG(0x00000004)

#define MIXER_GETLINEINFOF_QUERYMASK __MSABI_LONG(0x0000000F)

WINMMAPI MMRESULT WINAPI mixerGetID(HMIXEROBJ hmxobj, UINT *puMxId, DWORD fdwId);

typedef struct tagMIXERCONTROLA {
  DWORD cbStruct;
  DWORD dwControlID;
  DWORD dwControlType;
  DWORD fdwControl;
  DWORD cMultipleItems;
  CHAR szShortName[MIXER_SHORT_NAME_CHARS];
  CHAR szName[MIXER_LONG_NAME_CHARS];
  union {
    __C89_NAMELESS struct {
      LONG lMinimum;
      LONG lMaximum;
    };
    __C89_NAMELESS struct {
      DWORD dwMinimum;
      DWORD dwMaximum;
    };
    DWORD dwReserved[6];
  } Bounds;
  union {
    DWORD cSteps;
    DWORD cbCustomData;
    DWORD dwReserved[6];
  } Metrics;
} MIXERCONTROLA, *PMIXERCONTROLA, *LPMIXERCONTROLA;

typedef struct tagMIXERCONTROLW {
  DWORD cbStruct;
  DWORD dwControlID;
  DWORD dwControlType;
  DWORD fdwControl;
  DWORD cMultipleItems;
  WCHAR szShortName[MIXER_SHORT_NAME_CHARS];
  WCHAR szName[MIXER_LONG_NAME_CHARS];
  union {
    __C89_NAMELESS struct {
      LONG lMinimum;
      LONG lMaximum;
    };
    __C89_NAMELESS struct {
      DWORD dwMinimum;
      DWORD dwMaximum;
    };
    DWORD dwReserved[6];
  } Bounds;
  union {
    DWORD cSteps;
    DWORD cbCustomData;
    DWORD dwReserved[6];
  } Metrics;
} MIXERCONTROLW, *PMIXERCONTROLW, *LPMIXERCONTROLW;

__MINGW_TYPEDEF_AW(MIXERCONTROL)
__MINGW_TYPEDEF_AW(PMIXERCONTROL)
__MINGW_TYPEDEF_AW(LPMIXERCONTROL)

#define MIXERCONTROL_CONTROLF_UNIFORM __MSABI_LONG(0x00000001)
#define MIXERCONTROL_CONTROLF_MULTIPLE __MSABI_LONG(0x00000002)
#define MIXERCONTROL_CONTROLF_DISABLED __MSABI_LONG(0x80000000)

#define MIXERCONTROL_CT_CLASS_MASK __MSABI_LONG(0xF0000000)
#define MIXERCONTROL_CT_CLASS_CUSTOM __MSABI_LONG(0x00000000)
#define MIXERCONTROL_CT_CLASS_METER __MSABI_LONG(0x10000000)
#define MIXERCONTROL_CT_CLASS_SWITCH __MSABI_LONG(0x20000000)
#define MIXERCONTROL_CT_CLASS_NUMBER __MSABI_LONG(0x30000000)
#define MIXERCONTROL_CT_CLASS_SLIDER __MSABI_LONG(0x40000000)
#define MIXERCONTROL_CT_CLASS_FADER __MSABI_LONG(0x50000000)
#define MIXERCONTROL_CT_CLASS_TIME __MSABI_LONG(0x60000000)
#define MIXERCONTROL_CT_CLASS_LIST __MSABI_LONG(0x70000000)

#define MIXERCONTROL_CT_SUBCLASS_MASK __MSABI_LONG(0x0F000000)

#define MIXERCONTROL_CT_SC_SWITCH_BOOLEAN __MSABI_LONG(0x00000000)
#define MIXERCONTROL_CT_SC_SWITCH_BUTTON __MSABI_LONG(0x01000000)

#define MIXERCONTROL_CT_SC_METER_POLLED __MSABI_LONG(0x00000000)

#define MIXERCONTROL_CT_SC_TIME_MICROSECS __MSABI_LONG(0x00000000)
#define MIXERCONTROL_CT_SC_TIME_MILLISECS __MSABI_LONG(0x01000000)

#define MIXERCONTROL_CT_SC_LIST_SINGLE __MSABI_LONG(0x00000000)
#define MIXERCONTROL_CT_SC_LIST_MULTIPLE __MSABI_LONG(0x01000000)

#define MIXERCONTROL_CT_UNITS_MASK __MSABI_LONG(0x00FF0000)
#define MIXERCONTROL_CT_UNITS_CUSTOM __MSABI_LONG(0x00000000)
#define MIXERCONTROL_CT_UNITS_BOOLEAN __MSABI_LONG(0x00010000)
#define MIXERCONTROL_CT_UNITS_SIGNED __MSABI_LONG(0x00020000)
#define MIXERCONTROL_CT_UNITS_UNSIGNED __MSABI_LONG(0x00030000)
#define MIXERCONTROL_CT_UNITS_DECIBELS __MSABI_LONG(0x00040000)
#define MIXERCONTROL_CT_UNITS_PERCENT __MSABI_LONG(0x00050000)

#define MIXERCONTROL_CONTROLTYPE_CUSTOM (MIXERCONTROL_CT_CLASS_CUSTOM | MIXERCONTROL_CT_UNITS_CUSTOM)
#define MIXERCONTROL_CONTROLTYPE_BOOLEANMETER (MIXERCONTROL_CT_CLASS_METER | MIXERCONTROL_CT_SC_METER_POLLED | MIXERCONTROL_CT_UNITS_BOOLEAN)
#define MIXERCONTROL_CONTROLTYPE_SIGNEDMETER (MIXERCONTROL_CT_CLASS_METER | MIXERCONTROL_CT_SC_METER_POLLED | MIXERCONTROL_CT_UNITS_SIGNED)
#define MIXERCONTROL_CONTROLTYPE_PEAKMETER (MIXERCONTROL_CONTROLTYPE_SIGNEDMETER + 1)
#define MIXERCONTROL_CONTROLTYPE_UNSIGNEDMETER (MIXERCONTROL_CT_CLASS_METER | MIXERCONTROL_CT_SC_METER_POLLED | MIXERCONTROL_CT_UNITS_UNSIGNED)
#define MIXERCONTROL_CONTROLTYPE_BOOLEAN (MIXERCONTROL_CT_CLASS_SWITCH | MIXERCONTROL_CT_SC_SWITCH_BOOLEAN | MIXERCONTROL_CT_UNITS_BOOLEAN)
#define MIXERCONTROL_CONTROLTYPE_ONOFF (MIXERCONTROL_CONTROLTYPE_BOOLEAN + 1)
#define MIXERCONTROL_CONTROLTYPE_MUTE (MIXERCONTROL_CONTROLTYPE_BOOLEAN + 2)
#define MIXERCONTROL_CONTROLTYPE_MONO (MIXERCONTROL_CONTROLTYPE_BOOLEAN + 3)
#define MIXERCONTROL_CONTROLTYPE_LOUDNESS (MIXERCONTROL_CONTROLTYPE_BOOLEAN + 4)
#define MIXERCONTROL_CONTROLTYPE_STEREOENH (MIXERCONTROL_CONTROLTYPE_BOOLEAN + 5)
#define MIXERCONTROL_CONTROLTYPE_BASS_BOOST (MIXERCONTROL_CONTROLTYPE_BOOLEAN + 0x00002277)
#define MIXERCONTROL_CONTROLTYPE_BUTTON (MIXERCONTROL_CT_CLASS_SWITCH | MIXERCONTROL_CT_SC_SWITCH_BUTTON | MIXERCONTROL_CT_UNITS_BOOLEAN)
#define MIXERCONTROL_CONTROLTYPE_DECIBELS (MIXERCONTROL_CT_CLASS_NUMBER | MIXERCONTROL_CT_UNITS_DECIBELS)
#define MIXERCONTROL_CONTROLTYPE_SIGNED (MIXERCONTROL_CT_CLASS_NUMBER | MIXERCONTROL_CT_UNITS_SIGNED)
#define MIXERCONTROL_CONTROLTYPE_UNSIGNED (MIXERCONTROL_CT_CLASS_NUMBER | MIXERCONTROL_CT_UNITS_UNSIGNED)
#define MIXERCONTROL_CONTROLTYPE_PERCENT (MIXERCONTROL_CT_CLASS_NUMBER | MIXERCONTROL_CT_UNITS_PERCENT)
#define MIXERCONTROL_CONTROLTYPE_SLIDER (MIXERCONTROL_CT_CLASS_SLIDER | MIXERCONTROL_CT_UNITS_SIGNED)
#define MIXERCONTROL_CONTROLTYPE_PAN (MIXERCONTROL_CONTROLTYPE_SLIDER + 1)
#define MIXERCONTROL_CONTROLTYPE_QSOUNDPAN (MIXERCONTROL_CONTROLTYPE_SLIDER + 2)
#define MIXERCONTROL_CONTROLTYPE_FADER (MIXERCONTROL_CT_CLASS_FADER | MIXERCONTROL_CT_UNITS_UNSIGNED)
#define MIXERCONTROL_CONTROLTYPE_VOLUME (MIXERCONTROL_CONTROLTYPE_FADER + 1)
#define MIXERCONTROL_CONTROLTYPE_BASS (MIXERCONTROL_CONTROLTYPE_FADER + 2)
#define MIXERCONTROL_CONTROLTYPE_TREBLE (MIXERCONTROL_CONTROLTYPE_FADER + 3)
#define MIXERCONTROL_CONTROLTYPE_EQUALIZER (MIXERCONTROL_CONTROLTYPE_FADER + 4)
#define MIXERCONTROL_CONTROLTYPE_SINGLESELECT (MIXERCONTROL_CT_CLASS_LIST | MIXERCONTROL_CT_SC_LIST_SINGLE | MIXERCONTROL_CT_UNITS_BOOLEAN)
#define MIXERCONTROL_CONTROLTYPE_MUX (MIXERCONTROL_CONTROLTYPE_SINGLESELECT + 1)
#define MIXERCONTROL_CONTROLTYPE_MULTIPLESELECT (MIXERCONTROL_CT_CLASS_LIST | MIXERCONTROL_CT_SC_LIST_MULTIPLE | MIXERCONTROL_CT_UNITS_BOOLEAN)
#define MIXERCONTROL_CONTROLTYPE_MIXER (MIXERCONTROL_CONTROLTYPE_MULTIPLESELECT + 1)
#define MIXERCONTROL_CONTROLTYPE_MICROTIME (MIXERCONTROL_CT_CLASS_TIME | MIXERCONTROL_CT_SC_TIME_MICROSECS | MIXERCONTROL_CT_UNITS_UNSIGNED)
#define MIXERCONTROL_CONTROLTYPE_MILLITIME (MIXERCONTROL_CT_CLASS_TIME | MIXERCONTROL_CT_SC_TIME_MILLISECS | MIXERCONTROL_CT_UNITS_UNSIGNED)

typedef struct tagMIXERLINECONTROLSA {
  DWORD cbStruct;
  DWORD dwLineID;
  __C89_NAMELESS union {
    DWORD dwControlID;
    DWORD dwControlType;
  };
  DWORD cControls;
  DWORD cbmxctrl;
  LPMIXERCONTROLA pamxctrl;
} MIXERLINECONTROLSA, *PMIXERLINECONTROLSA, *LPMIXERLINECONTROLSA;

typedef struct tagMIXERLINECONTROLSW {
  DWORD cbStruct;
  DWORD dwLineID;
  __C89_NAMELESS union {
    DWORD dwControlID;
    DWORD dwControlType;
  };
  DWORD cControls;
  DWORD cbmxctrl;
  LPMIXERCONTROLW pamxctrl;
} MIXERLINECONTROLSW, *PMIXERLINECONTROLSW, *LPMIXERLINECONTROLSW;

__MINGW_TYPEDEF_AW(MIXERLINECONTROLS)
__MINGW_TYPEDEF_AW(PMIXERLINECONTROLS)
__MINGW_TYPEDEF_AW(LPMIXERLINECONTROLS)

WINMMAPI MMRESULT WINAPI mixerGetLineControlsA(HMIXEROBJ hmxobj, LPMIXERLINECONTROLSA pmxlc, DWORD fdwControls);
WINMMAPI MMRESULT WINAPI mixerGetLineControlsW(HMIXEROBJ hmxobj, LPMIXERLINECONTROLSW pmxlc, DWORD fdwControls);
#define mixerGetLineControls __MINGW_NAME_AW(mixerGetLineControls)

#define MIXER_GETLINECONTROLSF_ALL __MSABI_LONG(0x00000000)
#define MIXER_GETLINECONTROLSF_ONEBYID __MSABI_LONG(0x00000001)
#define MIXER_GETLINECONTROLSF_ONEBYTYPE __MSABI_LONG(0x00000002)

#define MIXER_GETLINECONTROLSF_QUERYMASK __MSABI_LONG(0x0000000F)

typedef struct tMIXERCONTROLDETAILS {
  DWORD cbStruct;
  DWORD dwControlID;
  DWORD cChannels;
  __C89_NAMELESS union {
    HWND hwndOwner;
    DWORD cMultipleItems;
  };
  DWORD cbDetails;
  LPVOID paDetails;
} MIXERCONTROLDETAILS, *PMIXERCONTROLDETAILS, *LPMIXERCONTROLDETAILS;

typedef struct tagMIXERCONTROLDETAILS_LISTTEXTA {
  DWORD dwParam1;
  DWORD dwParam2;
  CHAR szName[MIXER_LONG_NAME_CHARS];
} MIXERCONTROLDETAILS_LISTTEXTA, *PMIXERCONTROLDETAILS_LISTTEXTA, *LPMIXERCONTROLDETAILS_LISTTEXTA;

typedef struct tagMIXERCONTROLDETAILS_LISTTEXTW {
  DWORD dwParam1;
  DWORD dwParam2;
  WCHAR szName[MIXER_LONG_NAME_CHARS];
} MIXERCONTROLDETAILS_LISTTEXTW, *PMIXERCONTROLDETAILS_LISTTEXTW, *LPMIXERCONTROLDETAILS_LISTTEXTW;

__MINGW_TYPEDEF_AW(MIXERCONTROLDETAILS_LISTTEXT)
__MINGW_TYPEDEF_AW(PMIXERCONTROLDETAILS_LISTTEXT)
__MINGW_TYPEDEF_AW(LPMIXERCONTROLDETAILS_LISTTEXT)

typedef struct tMIXERCONTROLDETAILS_BOOLEAN {
  LONG fValue;
} MIXERCONTROLDETAILS_BOOLEAN, *PMIXERCONTROLDETAILS_BOOLEAN, *LPMIXERCONTROLDETAILS_BOOLEAN;

typedef struct tMIXERCONTROLDETAILS_SIGNED {
  LONG lValue;
} MIXERCONTROLDETAILS_SIGNED, *PMIXERCONTROLDETAILS_SIGNED, *LPMIXERCONTROLDETAILS_SIGNED;

typedef struct tMIXERCONTROLDETAILS_UNSIGNED {
  DWORD dwValue;
} MIXERCONTROLDETAILS_UNSIGNED, *PMIXERCONTROLDETAILS_UNSIGNED, *LPMIXERCONTROLDETAILS_UNSIGNED;

WINMMAPI MMRESULT WINAPI mixerGetControlDetailsA(HMIXEROBJ hmxobj, LPMIXERCONTROLDETAILS pmxcd, DWORD fdwDetails);
WINMMAPI MMRESULT WINAPI mixerGetControlDetailsW(HMIXEROBJ hmxobj, LPMIXERCONTROLDETAILS pmxcd, DWORD fdwDetails);
#define mixerGetControlDetails __MINGW_NAME_AW(mixerGetControlDetails)

#define MIXER_GETCONTROLDETAILSF_VALUE __MSABI_LONG(0x00000000)
#define MIXER_GETCONTROLDETAILSF_LISTTEXT __MSABI_LONG(0x00000001)

#define MIXER_GETCONTROLDETAILSF_QUERYMASK __MSABI_LONG(0x0000000F)

WINMMAPI MMRESULT WINAPI mixerSetControlDetails(HMIXEROBJ hmxobj, LPMIXERCONTROLDETAILS pmxcd, DWORD fdwDetails);

#define MIXER_SETCONTROLDETAILSF_VALUE __MSABI_LONG(0x00000000)
#define MIXER_SETCONTROLDETAILSF_CUSTOM __MSABI_LONG(0x00000001)

#define MIXER_SETCONTROLDETAILSF_QUERYMASK __MSABI_LONG(0x0000000F)

#endif /* ifndef MMNOMIXER */

#endif /* WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP) */

#ifdef __cplusplus
}
#endif

#endif /* _MMEAPI_H_ */
