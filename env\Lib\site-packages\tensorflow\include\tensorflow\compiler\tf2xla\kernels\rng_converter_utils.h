/* Copyright 2023 The TensorFlow Authors. All Rights Reserved.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
==============================================================================*/

#ifndef TENSORFLOW_COMPILER_TF2XLA_KERNELS_RNG_CONVERTER_UTILS_H_
#define TENSORFLOW_COMPILER_TF2XLA_KERNELS_RNG_CONVERTER_UTILS_H_

#include "absl/strings/string_view.h"
#include "xla/xla_data.pb.h"
#include "tensorflow/core/framework/rng_alg.h"

namespace tensorflow {

// Given the XLA::RandomAlgorithm, return the Tensorflow equivalent.
Algorithm ToTensorflowAlgorithm(xla::RandomAlgorithm alg);

// Given the device type, return the default XLA::RandomAlgorithm
xla::RandomAlgorithm DefaultRngAlgForDeviceType(
    absl::string_view device_type_string);

}  // namespace tensorflow

#endif  // TENSORFLOW_COMPILER_TF2XLA_KERNELS_RNG_CONVERTER_UTILS_H_
