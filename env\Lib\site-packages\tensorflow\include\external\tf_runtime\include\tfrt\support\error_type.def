// Copyright 2020 The TensorFlow Runtime Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

//===- error_type.def -------------------------------------------*- C++ -*-===//
//
// This file is a macro metaprogramming file for dealing with error types.
// To use this file, #define the ERROR_TYPE macro and include the file.
//
//===----------------------------------------------------------------------===//

#ifdef ERROR_TYPE

// Not an error.
ERROR_TYPE(OK)
ERROR_TYPE(Cancelled)
ERROR_TYPE(Unknown)
ERROR_TYPE(InvalidArgument)
ERROR_TYPE(DeadlineExceeded)
ERROR_TYPE(NotFound)
ERROR_TYPE(AlreadyExists)
ERROR_TYPE(PermissionDenied)
ERROR_TYPE(ResourceExhausted)
ERROR_TYPE(FailedPrecondition)
ERROR_TYPE(Aborted)
ERROR_TYPE(OutOfRange)
ERROR_TYPE(Unimplemented)
ERROR_TYPE(Internal)
ERROR_TYPE(Unavailable)
ERROR_TYPE(DataLoss)
ERROR_TYPE(Unauthenticated)

// Errors that are generated by RPC layer
ERROR_TYPE(RpcCancelled)
ERROR_TYPE(RpcDeadlineExceeded)
ERROR_TYPE(RpcUnavailable)
ERROR_TYPE(RpcUnknown)

// Custom error types
ERROR_TYPE(DeviceNotFound)
ERROR_TYPE(TaskNotFound)
ERROR_TYPE(RemoteFunctionAlreadyExists)
ERROR_TYPE(MalformattedMlirFile)
ERROR_TYPE(CompilationFailed)
ERROR_TYPE(InvalidDistributedContextId)
ERROR_TYPE(DistributedContextAlreadyExists)

// TODO(tfrt-devs): add more error codes if needed.

#undef ERROR_TYPE
#endif
