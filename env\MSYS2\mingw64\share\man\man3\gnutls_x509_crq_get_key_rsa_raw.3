.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crq_get_key_rsa_raw" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crq_get_key_rsa_raw \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crq_get_key_rsa_raw(gnutls_x509_crq_t " crq ", gnutls_datum_t * " m ", gnutls_datum_t * " e ");"
.SH ARGUMENTS
.IP "gnutls_x509_crq_t crq" 12
Holds the certificate
.IP "gnutls_datum_t * m" 12
will hold the modulus
.IP "gnutls_datum_t * e" 12
will hold the public exponent
.SH "DESCRIPTION"
This function will export the RSA public key's parameters found in
the given structure.  The new parameters will be allocated using
\fBgnutls_malloc()\fP and will be stored in the appropriate datum.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "SINCE"
2.8.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
