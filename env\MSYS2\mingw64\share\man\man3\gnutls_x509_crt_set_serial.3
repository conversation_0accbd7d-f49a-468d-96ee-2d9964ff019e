.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crt_set_serial" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crt_set_serial \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crt_set_serial(gnutls_x509_crt_t " cert ", const void * " serial ", size_t " serial_size ");"
.SH ARGUMENTS
.IP "gnutls_x509_crt_t cert" 12
a certificate of type \fBgnutls_x509_crt_t\fP
.IP "const void * serial" 12
The serial number
.IP "size_t serial_size" 12
Holds the size of the serial field.
.SH "DESCRIPTION"
This function will set the X.509 certificate's serial number.
While the serial number is an integer, it is often handled
as an opaque field by several CAs. For this reason this function
accepts any kind of data as a serial number. To be consistent
with the X.509/PKIX specifications the provided  \fIserial\fP should be 
a big\-endian positive number (i.e. its leftmost bit should be zero).

The size of the serial is restricted to 20 bytes maximum by RFC5280.
This function allows writing more than 20 bytes but the generated
certificates in that case may be rejected by other implementations.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
