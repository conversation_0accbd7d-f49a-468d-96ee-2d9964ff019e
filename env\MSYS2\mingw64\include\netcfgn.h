/*** Autogenerated by WIDL 10.8 from include/netcfgn.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __netcfgn_h__
#define __netcfgn_h__

/* Forward declarations */

#ifndef __INetCfgPnpReconfigCallback_FWD_DEFINED__
#define __INetCfgPnpReconfigCallback_FWD_DEFINED__
typedef interface INetCfgPnpReconfigCallback INetCfgPnpReconfigCallback;
#ifdef __cplusplus
interface INetCfgPnpReconfigCallback;
#endif /* __cplusplus */
#endif

#ifndef __INetCfgComponentControl_FWD_DEFINED__
#define __INetCfgComponentControl_FWD_DEFINED__
typedef interface INetCfgComponentControl INetCfgComponentControl;
#ifdef __cplusplus
interface INetCfgComponentControl;
#endif /* __cplusplus */
#endif

#ifndef __INetCfgComponentSetup_FWD_DEFINED__
#define __INetCfgComponentSetup_FWD_DEFINED__
typedef interface INetCfgComponentSetup INetCfgComponentSetup;
#ifdef __cplusplus
interface INetCfgComponentSetup;
#endif /* __cplusplus */
#endif

#ifndef __INetCfgComponentPropertyUi_FWD_DEFINED__
#define __INetCfgComponentPropertyUi_FWD_DEFINED__
typedef interface INetCfgComponentPropertyUi INetCfgComponentPropertyUi;
#ifdef __cplusplus
interface INetCfgComponentPropertyUi;
#endif /* __cplusplus */
#endif

#ifndef __INetCfgComponentNotifyBinding_FWD_DEFINED__
#define __INetCfgComponentNotifyBinding_FWD_DEFINED__
typedef interface INetCfgComponentNotifyBinding INetCfgComponentNotifyBinding;
#ifdef __cplusplus
interface INetCfgComponentNotifyBinding;
#endif /* __cplusplus */
#endif

#ifndef __INetCfgComponentNotifyGlobal_FWD_DEFINED__
#define __INetCfgComponentNotifyGlobal_FWD_DEFINED__
typedef interface INetCfgComponentNotifyGlobal INetCfgComponentNotifyGlobal;
#ifdef __cplusplus
interface INetCfgComponentNotifyGlobal;
#endif /* __cplusplus */
#endif

#ifndef __INetCfgComponentUpperEdge_FWD_DEFINED__
#define __INetCfgComponentUpperEdge_FWD_DEFINED__
typedef interface INetCfgComponentUpperEdge INetCfgComponentUpperEdge;
#ifdef __cplusplus
interface INetCfgComponentUpperEdge;
#endif /* __cplusplus */
#endif

#ifndef __INetLanConnectionUiInfo_FWD_DEFINED__
#define __INetLanConnectionUiInfo_FWD_DEFINED__
typedef interface INetLanConnectionUiInfo INetLanConnectionUiInfo;
#ifdef __cplusplus
interface INetLanConnectionUiInfo;
#endif /* __cplusplus */
#endif

#ifndef __INetRasConnectionIpUiInfo_FWD_DEFINED__
#define __INetRasConnectionIpUiInfo_FWD_DEFINED__
typedef interface INetRasConnectionIpUiInfo INetRasConnectionIpUiInfo;
#ifdef __cplusplus
interface INetRasConnectionIpUiInfo;
#endif /* __cplusplus */
#endif

#ifndef __INetCfgComponentSysPrep_FWD_DEFINED__
#define __INetCfgComponentSysPrep_FWD_DEFINED__
typedef interface INetCfgComponentSysPrep INetCfgComponentSysPrep;
#ifdef __cplusplus
interface INetCfgComponentSysPrep;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <unknwn.h>
#include <wtypes.h>
#include <netcfgx.h>

#ifdef __cplusplus
extern "C" {
#endif

#include <winapifamily.h>
#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)

/*****************************************************************************
 * INetCfgPnpReconfigCallback interface
 */
#ifndef __INetCfgPnpReconfigCallback_INTERFACE_DEFINED__
#define __INetCfgPnpReconfigCallback_INTERFACE_DEFINED__

typedef enum tagNCPNP_RECONFIG_LAYER {
    NCRL_NDIS = 1,
    NCRL_TDI = 2
} NCPNP_RECONFIG_LAYER;
DEFINE_GUID(IID_INetCfgPnpReconfigCallback, 0x8d84bd35, 0xe227, 0x11d2, 0xb7,0x00, 0x00,0xa0,0xc9,0x8a,0x6a,0x85);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("8d84bd35-e227-11d2-b700-00a0c98a6a85")
INetCfgPnpReconfigCallback : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SendPnpReconfig(
        NCPNP_RECONFIG_LAYER Layer,
        LPCWSTR pszwUpper,
        LPCWSTR pszwLower,
        PVOID pvData,
        DWORD dwSizeOfData) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(INetCfgPnpReconfigCallback, 0x8d84bd35, 0xe227, 0x11d2, 0xb7,0x00, 0x00,0xa0,0xc9,0x8a,0x6a,0x85)
#endif
#else
typedef struct INetCfgPnpReconfigCallbackVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        INetCfgPnpReconfigCallback *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        INetCfgPnpReconfigCallback *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        INetCfgPnpReconfigCallback *This);

    /*** INetCfgPnpReconfigCallback methods ***/
    HRESULT (STDMETHODCALLTYPE *SendPnpReconfig)(
        INetCfgPnpReconfigCallback *This,
        NCPNP_RECONFIG_LAYER Layer,
        LPCWSTR pszwUpper,
        LPCWSTR pszwLower,
        PVOID pvData,
        DWORD dwSizeOfData);

    END_INTERFACE
} INetCfgPnpReconfigCallbackVtbl;

interface INetCfgPnpReconfigCallback {
    CONST_VTBL INetCfgPnpReconfigCallbackVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define INetCfgPnpReconfigCallback_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define INetCfgPnpReconfigCallback_AddRef(This) (This)->lpVtbl->AddRef(This)
#define INetCfgPnpReconfigCallback_Release(This) (This)->lpVtbl->Release(This)
/*** INetCfgPnpReconfigCallback methods ***/
#define INetCfgPnpReconfigCallback_SendPnpReconfig(This,Layer,pszwUpper,pszwLower,pvData,dwSizeOfData) (This)->lpVtbl->SendPnpReconfig(This,Layer,pszwUpper,pszwLower,pvData,dwSizeOfData)
#else
/*** IUnknown methods ***/
static inline HRESULT INetCfgPnpReconfigCallback_QueryInterface(INetCfgPnpReconfigCallback* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG INetCfgPnpReconfigCallback_AddRef(INetCfgPnpReconfigCallback* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG INetCfgPnpReconfigCallback_Release(INetCfgPnpReconfigCallback* This) {
    return This->lpVtbl->Release(This);
}
/*** INetCfgPnpReconfigCallback methods ***/
static inline HRESULT INetCfgPnpReconfigCallback_SendPnpReconfig(INetCfgPnpReconfigCallback* This,NCPNP_RECONFIG_LAYER Layer,LPCWSTR pszwUpper,LPCWSTR pszwLower,PVOID pvData,DWORD dwSizeOfData) {
    return This->lpVtbl->SendPnpReconfig(This,Layer,pszwUpper,pszwLower,pvData,dwSizeOfData);
}
#endif
#endif

#endif


#endif  /* __INetCfgPnpReconfigCallback_INTERFACE_DEFINED__ */


/*****************************************************************************
 * INetCfgComponentControl interface
 */
#ifndef __INetCfgComponentControl_INTERFACE_DEFINED__
#define __INetCfgComponentControl_INTERFACE_DEFINED__

DEFINE_GUID(IID_INetCfgComponentControl, 0x932238df, 0xbea1, 0x11d0, 0x92,0x98, 0x00,0xc0,0x4f,0xc9,0x9d,0xcf);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("932238df-bea1-11d0-9298-00c04fc99dcf")
INetCfgComponentControl : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Initialize(
        INetCfgComponent *pIComp,
        INetCfg *pINetCfg,
        WINBOOL fInstalling) = 0;

    virtual HRESULT STDMETHODCALLTYPE ApplyRegistryChanges(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE ApplyPnpChanges(
        INetCfgPnpReconfigCallback *pICallback) = 0;

    virtual HRESULT STDMETHODCALLTYPE CancelChanges(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(INetCfgComponentControl, 0x932238df, 0xbea1, 0x11d0, 0x92,0x98, 0x00,0xc0,0x4f,0xc9,0x9d,0xcf)
#endif
#else
typedef struct INetCfgComponentControlVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        INetCfgComponentControl *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        INetCfgComponentControl *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        INetCfgComponentControl *This);

    /*** INetCfgComponentControl methods ***/
    HRESULT (STDMETHODCALLTYPE *Initialize)(
        INetCfgComponentControl *This,
        INetCfgComponent *pIComp,
        INetCfg *pINetCfg,
        WINBOOL fInstalling);

    HRESULT (STDMETHODCALLTYPE *ApplyRegistryChanges)(
        INetCfgComponentControl *This);

    HRESULT (STDMETHODCALLTYPE *ApplyPnpChanges)(
        INetCfgComponentControl *This,
        INetCfgPnpReconfigCallback *pICallback);

    HRESULT (STDMETHODCALLTYPE *CancelChanges)(
        INetCfgComponentControl *This);

    END_INTERFACE
} INetCfgComponentControlVtbl;

interface INetCfgComponentControl {
    CONST_VTBL INetCfgComponentControlVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define INetCfgComponentControl_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define INetCfgComponentControl_AddRef(This) (This)->lpVtbl->AddRef(This)
#define INetCfgComponentControl_Release(This) (This)->lpVtbl->Release(This)
/*** INetCfgComponentControl methods ***/
#define INetCfgComponentControl_Initialize(This,pIComp,pINetCfg,fInstalling) (This)->lpVtbl->Initialize(This,pIComp,pINetCfg,fInstalling)
#define INetCfgComponentControl_ApplyRegistryChanges(This) (This)->lpVtbl->ApplyRegistryChanges(This)
#define INetCfgComponentControl_ApplyPnpChanges(This,pICallback) (This)->lpVtbl->ApplyPnpChanges(This,pICallback)
#define INetCfgComponentControl_CancelChanges(This) (This)->lpVtbl->CancelChanges(This)
#else
/*** IUnknown methods ***/
static inline HRESULT INetCfgComponentControl_QueryInterface(INetCfgComponentControl* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG INetCfgComponentControl_AddRef(INetCfgComponentControl* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG INetCfgComponentControl_Release(INetCfgComponentControl* This) {
    return This->lpVtbl->Release(This);
}
/*** INetCfgComponentControl methods ***/
static inline HRESULT INetCfgComponentControl_Initialize(INetCfgComponentControl* This,INetCfgComponent *pIComp,INetCfg *pINetCfg,WINBOOL fInstalling) {
    return This->lpVtbl->Initialize(This,pIComp,pINetCfg,fInstalling);
}
static inline HRESULT INetCfgComponentControl_ApplyRegistryChanges(INetCfgComponentControl* This) {
    return This->lpVtbl->ApplyRegistryChanges(This);
}
static inline HRESULT INetCfgComponentControl_ApplyPnpChanges(INetCfgComponentControl* This,INetCfgPnpReconfigCallback *pICallback) {
    return This->lpVtbl->ApplyPnpChanges(This,pICallback);
}
static inline HRESULT INetCfgComponentControl_CancelChanges(INetCfgComponentControl* This) {
    return This->lpVtbl->CancelChanges(This);
}
#endif
#endif

#endif


#endif  /* __INetCfgComponentControl_INTERFACE_DEFINED__ */


/*****************************************************************************
 * INetCfgComponentSetup interface
 */
#ifndef __INetCfgComponentSetup_INTERFACE_DEFINED__
#define __INetCfgComponentSetup_INTERFACE_DEFINED__

typedef enum tagNETWORK_INSTALL_TIME {
    NSF_PRIMARYINSTALL = 0x1,
    NSF_POSTSYSINSTALL = 0x2
} NETWORK_INSTALL_TIME;
typedef enum tagNETWORK_UPGRADE_TYPE {
    NSF_WIN16_UPGRADE = 0x10,
    NSF_WIN95_UPGRADE = 0x20,
    NSF_WINNT_WKS_UPGRADE = 0x40,
    NSF_WINNT_SVR_UPGRADE = 0x80,
    NSF_WINNT_SBS_UPGRADE = 0x100,
    NSF_COMPONENT_UPDATE = 0x200
} NETWORK_UPGRADE_TYPE;
DEFINE_GUID(IID_INetCfgComponentSetup, 0x932238e3, 0xbea1, 0x11d0, 0x92,0x98, 0x00,0xc0,0x4f,0xc9,0x9d,0xcf);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("932238e3-bea1-11d0-9298-00c04fc99dcf")
INetCfgComponentSetup : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Install(
        DWORD dwSetupFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE Upgrade(
        DWORD dwSetupFlags,
        DWORD dwUpgradeFomBuildNo) = 0;

    virtual HRESULT STDMETHODCALLTYPE ReadAnswerFile(
        LPCWSTR pszwAnswerFile,
        LPCWSTR pszwAnswerSections) = 0;

    virtual HRESULT STDMETHODCALLTYPE Removing(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(INetCfgComponentSetup, 0x932238e3, 0xbea1, 0x11d0, 0x92,0x98, 0x00,0xc0,0x4f,0xc9,0x9d,0xcf)
#endif
#else
typedef struct INetCfgComponentSetupVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        INetCfgComponentSetup *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        INetCfgComponentSetup *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        INetCfgComponentSetup *This);

    /*** INetCfgComponentSetup methods ***/
    HRESULT (STDMETHODCALLTYPE *Install)(
        INetCfgComponentSetup *This,
        DWORD dwSetupFlags);

    HRESULT (STDMETHODCALLTYPE *Upgrade)(
        INetCfgComponentSetup *This,
        DWORD dwSetupFlags,
        DWORD dwUpgradeFomBuildNo);

    HRESULT (STDMETHODCALLTYPE *ReadAnswerFile)(
        INetCfgComponentSetup *This,
        LPCWSTR pszwAnswerFile,
        LPCWSTR pszwAnswerSections);

    HRESULT (STDMETHODCALLTYPE *Removing)(
        INetCfgComponentSetup *This);

    END_INTERFACE
} INetCfgComponentSetupVtbl;

interface INetCfgComponentSetup {
    CONST_VTBL INetCfgComponentSetupVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define INetCfgComponentSetup_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define INetCfgComponentSetup_AddRef(This) (This)->lpVtbl->AddRef(This)
#define INetCfgComponentSetup_Release(This) (This)->lpVtbl->Release(This)
/*** INetCfgComponentSetup methods ***/
#define INetCfgComponentSetup_Install(This,dwSetupFlags) (This)->lpVtbl->Install(This,dwSetupFlags)
#define INetCfgComponentSetup_Upgrade(This,dwSetupFlags,dwUpgradeFomBuildNo) (This)->lpVtbl->Upgrade(This,dwSetupFlags,dwUpgradeFomBuildNo)
#define INetCfgComponentSetup_ReadAnswerFile(This,pszwAnswerFile,pszwAnswerSections) (This)->lpVtbl->ReadAnswerFile(This,pszwAnswerFile,pszwAnswerSections)
#define INetCfgComponentSetup_Removing(This) (This)->lpVtbl->Removing(This)
#else
/*** IUnknown methods ***/
static inline HRESULT INetCfgComponentSetup_QueryInterface(INetCfgComponentSetup* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG INetCfgComponentSetup_AddRef(INetCfgComponentSetup* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG INetCfgComponentSetup_Release(INetCfgComponentSetup* This) {
    return This->lpVtbl->Release(This);
}
/*** INetCfgComponentSetup methods ***/
static inline HRESULT INetCfgComponentSetup_Install(INetCfgComponentSetup* This,DWORD dwSetupFlags) {
    return This->lpVtbl->Install(This,dwSetupFlags);
}
static inline HRESULT INetCfgComponentSetup_Upgrade(INetCfgComponentSetup* This,DWORD dwSetupFlags,DWORD dwUpgradeFomBuildNo) {
    return This->lpVtbl->Upgrade(This,dwSetupFlags,dwUpgradeFomBuildNo);
}
static inline HRESULT INetCfgComponentSetup_ReadAnswerFile(INetCfgComponentSetup* This,LPCWSTR pszwAnswerFile,LPCWSTR pszwAnswerSections) {
    return This->lpVtbl->ReadAnswerFile(This,pszwAnswerFile,pszwAnswerSections);
}
static inline HRESULT INetCfgComponentSetup_Removing(INetCfgComponentSetup* This) {
    return This->lpVtbl->Removing(This);
}
#endif
#endif

#endif


#endif  /* __INetCfgComponentSetup_INTERFACE_DEFINED__ */


/*****************************************************************************
 * INetCfgComponentPropertyUi interface
 */
#ifndef __INetCfgComponentPropertyUi_INTERFACE_DEFINED__
#define __INetCfgComponentPropertyUi_INTERFACE_DEFINED__

typedef enum tagDEFAULT_PAGES {
    DPP_ADVANCED = 1
} DEFAULT_PAGES;
DEFINE_GUID(IID_INetCfgComponentPropertyUi, 0x932238e0, 0xbea1, 0x11d0, 0x92,0x98, 0x00,0xc0,0x4f,0xc9,0x9d,0xcf);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("932238e0-bea1-11d0-9298-00c04fc99dcf")
INetCfgComponentPropertyUi : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE QueryPropertyUi(
        IUnknown *pUnkReserved) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetContext(
        IUnknown *pUnkReserved) = 0;

    virtual HRESULT STDMETHODCALLTYPE MergePropPages(
        DWORD *pdwDefPages,
        BYTE **pahpspPrivate,
        UINT *pcPages,
        HWND hwndParent,
        LPCWSTR *pszStartPage) = 0;

    virtual HRESULT STDMETHODCALLTYPE ValidateProperties(
        HWND hwndSheet) = 0;

    virtual HRESULT STDMETHODCALLTYPE ApplyProperties(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE CancelProperties(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(INetCfgComponentPropertyUi, 0x932238e0, 0xbea1, 0x11d0, 0x92,0x98, 0x00,0xc0,0x4f,0xc9,0x9d,0xcf)
#endif
#else
typedef struct INetCfgComponentPropertyUiVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        INetCfgComponentPropertyUi *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        INetCfgComponentPropertyUi *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        INetCfgComponentPropertyUi *This);

    /*** INetCfgComponentPropertyUi methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryPropertyUi)(
        INetCfgComponentPropertyUi *This,
        IUnknown *pUnkReserved);

    HRESULT (STDMETHODCALLTYPE *SetContext)(
        INetCfgComponentPropertyUi *This,
        IUnknown *pUnkReserved);

    HRESULT (STDMETHODCALLTYPE *MergePropPages)(
        INetCfgComponentPropertyUi *This,
        DWORD *pdwDefPages,
        BYTE **pahpspPrivate,
        UINT *pcPages,
        HWND hwndParent,
        LPCWSTR *pszStartPage);

    HRESULT (STDMETHODCALLTYPE *ValidateProperties)(
        INetCfgComponentPropertyUi *This,
        HWND hwndSheet);

    HRESULT (STDMETHODCALLTYPE *ApplyProperties)(
        INetCfgComponentPropertyUi *This);

    HRESULT (STDMETHODCALLTYPE *CancelProperties)(
        INetCfgComponentPropertyUi *This);

    END_INTERFACE
} INetCfgComponentPropertyUiVtbl;

interface INetCfgComponentPropertyUi {
    CONST_VTBL INetCfgComponentPropertyUiVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define INetCfgComponentPropertyUi_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define INetCfgComponentPropertyUi_AddRef(This) (This)->lpVtbl->AddRef(This)
#define INetCfgComponentPropertyUi_Release(This) (This)->lpVtbl->Release(This)
/*** INetCfgComponentPropertyUi methods ***/
#define INetCfgComponentPropertyUi_QueryPropertyUi(This,pUnkReserved) (This)->lpVtbl->QueryPropertyUi(This,pUnkReserved)
#define INetCfgComponentPropertyUi_SetContext(This,pUnkReserved) (This)->lpVtbl->SetContext(This,pUnkReserved)
#define INetCfgComponentPropertyUi_MergePropPages(This,pdwDefPages,pahpspPrivate,pcPages,hwndParent,pszStartPage) (This)->lpVtbl->MergePropPages(This,pdwDefPages,pahpspPrivate,pcPages,hwndParent,pszStartPage)
#define INetCfgComponentPropertyUi_ValidateProperties(This,hwndSheet) (This)->lpVtbl->ValidateProperties(This,hwndSheet)
#define INetCfgComponentPropertyUi_ApplyProperties(This) (This)->lpVtbl->ApplyProperties(This)
#define INetCfgComponentPropertyUi_CancelProperties(This) (This)->lpVtbl->CancelProperties(This)
#else
/*** IUnknown methods ***/
static inline HRESULT INetCfgComponentPropertyUi_QueryInterface(INetCfgComponentPropertyUi* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG INetCfgComponentPropertyUi_AddRef(INetCfgComponentPropertyUi* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG INetCfgComponentPropertyUi_Release(INetCfgComponentPropertyUi* This) {
    return This->lpVtbl->Release(This);
}
/*** INetCfgComponentPropertyUi methods ***/
static inline HRESULT INetCfgComponentPropertyUi_QueryPropertyUi(INetCfgComponentPropertyUi* This,IUnknown *pUnkReserved) {
    return This->lpVtbl->QueryPropertyUi(This,pUnkReserved);
}
static inline HRESULT INetCfgComponentPropertyUi_SetContext(INetCfgComponentPropertyUi* This,IUnknown *pUnkReserved) {
    return This->lpVtbl->SetContext(This,pUnkReserved);
}
static inline HRESULT INetCfgComponentPropertyUi_MergePropPages(INetCfgComponentPropertyUi* This,DWORD *pdwDefPages,BYTE **pahpspPrivate,UINT *pcPages,HWND hwndParent,LPCWSTR *pszStartPage) {
    return This->lpVtbl->MergePropPages(This,pdwDefPages,pahpspPrivate,pcPages,hwndParent,pszStartPage);
}
static inline HRESULT INetCfgComponentPropertyUi_ValidateProperties(INetCfgComponentPropertyUi* This,HWND hwndSheet) {
    return This->lpVtbl->ValidateProperties(This,hwndSheet);
}
static inline HRESULT INetCfgComponentPropertyUi_ApplyProperties(INetCfgComponentPropertyUi* This) {
    return This->lpVtbl->ApplyProperties(This);
}
static inline HRESULT INetCfgComponentPropertyUi_CancelProperties(INetCfgComponentPropertyUi* This) {
    return This->lpVtbl->CancelProperties(This);
}
#endif
#endif

#endif


#endif  /* __INetCfgComponentPropertyUi_INTERFACE_DEFINED__ */


/*****************************************************************************
 * INetCfgComponentNotifyBinding interface
 */
#ifndef __INetCfgComponentNotifyBinding_INTERFACE_DEFINED__
#define __INetCfgComponentNotifyBinding_INTERFACE_DEFINED__

typedef enum tagBIND_FLAGS1 {
    NCN_ADD = 0x1,
    NCN_REMOVE = 0x2,
    NCN_UPDATE = 0x4,
    NCN_ENABLE = 0x10,
    NCN_DISABLE = 0x20,
    NCN_BINDING_PATH = 0x100,
    NCN_PROPERTYCHANGE = 0x200,
    NCN_NET = 0x10000,
    NCN_NETTRANS = 0x20000,
    NCN_NETCLIENT = 0x40000,
    NCN_NETSERVICE = 0x80000
} BIND_FLAGS1;
DEFINE_GUID(IID_INetCfgComponentNotifyBinding, 0x932238e1, 0xbea1, 0x11d0, 0x92,0x98, 0x00,0xc0,0x4f,0xc9,0x9d,0xcf);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("932238e1-bea1-11d0-9298-00c04fc99dcf")
INetCfgComponentNotifyBinding : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE QueryBindingPath(
        DWORD dwChangeFlag,
        INetCfgBindingPath *pIPath) = 0;

    virtual HRESULT STDMETHODCALLTYPE NotifyBindingPath(
        DWORD dwChangeFlag,
        INetCfgBindingPath *pIPath) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(INetCfgComponentNotifyBinding, 0x932238e1, 0xbea1, 0x11d0, 0x92,0x98, 0x00,0xc0,0x4f,0xc9,0x9d,0xcf)
#endif
#else
typedef struct INetCfgComponentNotifyBindingVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        INetCfgComponentNotifyBinding *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        INetCfgComponentNotifyBinding *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        INetCfgComponentNotifyBinding *This);

    /*** INetCfgComponentNotifyBinding methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryBindingPath)(
        INetCfgComponentNotifyBinding *This,
        DWORD dwChangeFlag,
        INetCfgBindingPath *pIPath);

    HRESULT (STDMETHODCALLTYPE *NotifyBindingPath)(
        INetCfgComponentNotifyBinding *This,
        DWORD dwChangeFlag,
        INetCfgBindingPath *pIPath);

    END_INTERFACE
} INetCfgComponentNotifyBindingVtbl;

interface INetCfgComponentNotifyBinding {
    CONST_VTBL INetCfgComponentNotifyBindingVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define INetCfgComponentNotifyBinding_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define INetCfgComponentNotifyBinding_AddRef(This) (This)->lpVtbl->AddRef(This)
#define INetCfgComponentNotifyBinding_Release(This) (This)->lpVtbl->Release(This)
/*** INetCfgComponentNotifyBinding methods ***/
#define INetCfgComponentNotifyBinding_QueryBindingPath(This,dwChangeFlag,pIPath) (This)->lpVtbl->QueryBindingPath(This,dwChangeFlag,pIPath)
#define INetCfgComponentNotifyBinding_NotifyBindingPath(This,dwChangeFlag,pIPath) (This)->lpVtbl->NotifyBindingPath(This,dwChangeFlag,pIPath)
#else
/*** IUnknown methods ***/
static inline HRESULT INetCfgComponentNotifyBinding_QueryInterface(INetCfgComponentNotifyBinding* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG INetCfgComponentNotifyBinding_AddRef(INetCfgComponentNotifyBinding* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG INetCfgComponentNotifyBinding_Release(INetCfgComponentNotifyBinding* This) {
    return This->lpVtbl->Release(This);
}
/*** INetCfgComponentNotifyBinding methods ***/
static inline HRESULT INetCfgComponentNotifyBinding_QueryBindingPath(INetCfgComponentNotifyBinding* This,DWORD dwChangeFlag,INetCfgBindingPath *pIPath) {
    return This->lpVtbl->QueryBindingPath(This,dwChangeFlag,pIPath);
}
static inline HRESULT INetCfgComponentNotifyBinding_NotifyBindingPath(INetCfgComponentNotifyBinding* This,DWORD dwChangeFlag,INetCfgBindingPath *pIPath) {
    return This->lpVtbl->NotifyBindingPath(This,dwChangeFlag,pIPath);
}
#endif
#endif

#endif


#endif  /* __INetCfgComponentNotifyBinding_INTERFACE_DEFINED__ */


/*****************************************************************************
 * INetCfgComponentNotifyGlobal interface
 */
#ifndef __INetCfgComponentNotifyGlobal_INTERFACE_DEFINED__
#define __INetCfgComponentNotifyGlobal_INTERFACE_DEFINED__

DEFINE_GUID(IID_INetCfgComponentNotifyGlobal, 0x932238e2, 0xbea1, 0x11d0, 0x92,0x98, 0x00,0xc0,0x4f,0xc9,0x9d,0xcf);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("932238e2-bea1-11d0-9298-00c04fc99dcf")
INetCfgComponentNotifyGlobal : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetSupportedNotifications(
        DWORD *dwNotifications) = 0;

    virtual HRESULT STDMETHODCALLTYPE SysQueryBindingPath(
        DWORD dwChangeFlag,
        INetCfgBindingPath *pIPath) = 0;

    virtual HRESULT STDMETHODCALLTYPE SysNotifyBindingPath(
        DWORD dwChangeFlag,
        INetCfgBindingPath *pIPath) = 0;

    virtual HRESULT STDMETHODCALLTYPE SysNotifyComponent(
        DWORD dwChangeFlag,
        INetCfgComponent *pIComp) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(INetCfgComponentNotifyGlobal, 0x932238e2, 0xbea1, 0x11d0, 0x92,0x98, 0x00,0xc0,0x4f,0xc9,0x9d,0xcf)
#endif
#else
typedef struct INetCfgComponentNotifyGlobalVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        INetCfgComponentNotifyGlobal *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        INetCfgComponentNotifyGlobal *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        INetCfgComponentNotifyGlobal *This);

    /*** INetCfgComponentNotifyGlobal methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSupportedNotifications)(
        INetCfgComponentNotifyGlobal *This,
        DWORD *dwNotifications);

    HRESULT (STDMETHODCALLTYPE *SysQueryBindingPath)(
        INetCfgComponentNotifyGlobal *This,
        DWORD dwChangeFlag,
        INetCfgBindingPath *pIPath);

    HRESULT (STDMETHODCALLTYPE *SysNotifyBindingPath)(
        INetCfgComponentNotifyGlobal *This,
        DWORD dwChangeFlag,
        INetCfgBindingPath *pIPath);

    HRESULT (STDMETHODCALLTYPE *SysNotifyComponent)(
        INetCfgComponentNotifyGlobal *This,
        DWORD dwChangeFlag,
        INetCfgComponent *pIComp);

    END_INTERFACE
} INetCfgComponentNotifyGlobalVtbl;

interface INetCfgComponentNotifyGlobal {
    CONST_VTBL INetCfgComponentNotifyGlobalVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define INetCfgComponentNotifyGlobal_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define INetCfgComponentNotifyGlobal_AddRef(This) (This)->lpVtbl->AddRef(This)
#define INetCfgComponentNotifyGlobal_Release(This) (This)->lpVtbl->Release(This)
/*** INetCfgComponentNotifyGlobal methods ***/
#define INetCfgComponentNotifyGlobal_GetSupportedNotifications(This,dwNotifications) (This)->lpVtbl->GetSupportedNotifications(This,dwNotifications)
#define INetCfgComponentNotifyGlobal_SysQueryBindingPath(This,dwChangeFlag,pIPath) (This)->lpVtbl->SysQueryBindingPath(This,dwChangeFlag,pIPath)
#define INetCfgComponentNotifyGlobal_SysNotifyBindingPath(This,dwChangeFlag,pIPath) (This)->lpVtbl->SysNotifyBindingPath(This,dwChangeFlag,pIPath)
#define INetCfgComponentNotifyGlobal_SysNotifyComponent(This,dwChangeFlag,pIComp) (This)->lpVtbl->SysNotifyComponent(This,dwChangeFlag,pIComp)
#else
/*** IUnknown methods ***/
static inline HRESULT INetCfgComponentNotifyGlobal_QueryInterface(INetCfgComponentNotifyGlobal* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG INetCfgComponentNotifyGlobal_AddRef(INetCfgComponentNotifyGlobal* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG INetCfgComponentNotifyGlobal_Release(INetCfgComponentNotifyGlobal* This) {
    return This->lpVtbl->Release(This);
}
/*** INetCfgComponentNotifyGlobal methods ***/
static inline HRESULT INetCfgComponentNotifyGlobal_GetSupportedNotifications(INetCfgComponentNotifyGlobal* This,DWORD *dwNotifications) {
    return This->lpVtbl->GetSupportedNotifications(This,dwNotifications);
}
static inline HRESULT INetCfgComponentNotifyGlobal_SysQueryBindingPath(INetCfgComponentNotifyGlobal* This,DWORD dwChangeFlag,INetCfgBindingPath *pIPath) {
    return This->lpVtbl->SysQueryBindingPath(This,dwChangeFlag,pIPath);
}
static inline HRESULT INetCfgComponentNotifyGlobal_SysNotifyBindingPath(INetCfgComponentNotifyGlobal* This,DWORD dwChangeFlag,INetCfgBindingPath *pIPath) {
    return This->lpVtbl->SysNotifyBindingPath(This,dwChangeFlag,pIPath);
}
static inline HRESULT INetCfgComponentNotifyGlobal_SysNotifyComponent(INetCfgComponentNotifyGlobal* This,DWORD dwChangeFlag,INetCfgComponent *pIComp) {
    return This->lpVtbl->SysNotifyComponent(This,dwChangeFlag,pIComp);
}
#endif
#endif

#endif


#endif  /* __INetCfgComponentNotifyGlobal_INTERFACE_DEFINED__ */


/*****************************************************************************
 * INetCfgComponentUpperEdge interface
 */
#ifndef __INetCfgComponentUpperEdge_INTERFACE_DEFINED__
#define __INetCfgComponentUpperEdge_INTERFACE_DEFINED__

DEFINE_GUID(IID_INetCfgComponentUpperEdge, 0x932238e4, 0xbea1, 0x11d0, 0x92,0x98, 0x00,0xc0,0x4f,0xc9,0x9d,0xcf);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("932238e4-bea1-11d0-9298-00c04fc99dcf")
INetCfgComponentUpperEdge : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetInterfaceIdsForAdapter(
        INetCfgComponent *pAdapter,
        DWORD *pdwNumInterfaces,
        GUID **ppguidInterfaceIds) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddInterfacesToAdapter(
        INetCfgComponent *pAdapter,
        DWORD dwNumInterfaces) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveInterfacesFromAdapter(
        INetCfgComponent *pAdapter,
        DWORD dwNumInterfaces,
        const GUID *pguidInterfaceIds) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(INetCfgComponentUpperEdge, 0x932238e4, 0xbea1, 0x11d0, 0x92,0x98, 0x00,0xc0,0x4f,0xc9,0x9d,0xcf)
#endif
#else
typedef struct INetCfgComponentUpperEdgeVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        INetCfgComponentUpperEdge *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        INetCfgComponentUpperEdge *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        INetCfgComponentUpperEdge *This);

    /*** INetCfgComponentUpperEdge methods ***/
    HRESULT (STDMETHODCALLTYPE *GetInterfaceIdsForAdapter)(
        INetCfgComponentUpperEdge *This,
        INetCfgComponent *pAdapter,
        DWORD *pdwNumInterfaces,
        GUID **ppguidInterfaceIds);

    HRESULT (STDMETHODCALLTYPE *AddInterfacesToAdapter)(
        INetCfgComponentUpperEdge *This,
        INetCfgComponent *pAdapter,
        DWORD dwNumInterfaces);

    HRESULT (STDMETHODCALLTYPE *RemoveInterfacesFromAdapter)(
        INetCfgComponentUpperEdge *This,
        INetCfgComponent *pAdapter,
        DWORD dwNumInterfaces,
        const GUID *pguidInterfaceIds);

    END_INTERFACE
} INetCfgComponentUpperEdgeVtbl;

interface INetCfgComponentUpperEdge {
    CONST_VTBL INetCfgComponentUpperEdgeVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define INetCfgComponentUpperEdge_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define INetCfgComponentUpperEdge_AddRef(This) (This)->lpVtbl->AddRef(This)
#define INetCfgComponentUpperEdge_Release(This) (This)->lpVtbl->Release(This)
/*** INetCfgComponentUpperEdge methods ***/
#define INetCfgComponentUpperEdge_GetInterfaceIdsForAdapter(This,pAdapter,pdwNumInterfaces,ppguidInterfaceIds) (This)->lpVtbl->GetInterfaceIdsForAdapter(This,pAdapter,pdwNumInterfaces,ppguidInterfaceIds)
#define INetCfgComponentUpperEdge_AddInterfacesToAdapter(This,pAdapter,dwNumInterfaces) (This)->lpVtbl->AddInterfacesToAdapter(This,pAdapter,dwNumInterfaces)
#define INetCfgComponentUpperEdge_RemoveInterfacesFromAdapter(This,pAdapter,dwNumInterfaces,pguidInterfaceIds) (This)->lpVtbl->RemoveInterfacesFromAdapter(This,pAdapter,dwNumInterfaces,pguidInterfaceIds)
#else
/*** IUnknown methods ***/
static inline HRESULT INetCfgComponentUpperEdge_QueryInterface(INetCfgComponentUpperEdge* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG INetCfgComponentUpperEdge_AddRef(INetCfgComponentUpperEdge* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG INetCfgComponentUpperEdge_Release(INetCfgComponentUpperEdge* This) {
    return This->lpVtbl->Release(This);
}
/*** INetCfgComponentUpperEdge methods ***/
static inline HRESULT INetCfgComponentUpperEdge_GetInterfaceIdsForAdapter(INetCfgComponentUpperEdge* This,INetCfgComponent *pAdapter,DWORD *pdwNumInterfaces,GUID **ppguidInterfaceIds) {
    return This->lpVtbl->GetInterfaceIdsForAdapter(This,pAdapter,pdwNumInterfaces,ppguidInterfaceIds);
}
static inline HRESULT INetCfgComponentUpperEdge_AddInterfacesToAdapter(INetCfgComponentUpperEdge* This,INetCfgComponent *pAdapter,DWORD dwNumInterfaces) {
    return This->lpVtbl->AddInterfacesToAdapter(This,pAdapter,dwNumInterfaces);
}
static inline HRESULT INetCfgComponentUpperEdge_RemoveInterfacesFromAdapter(INetCfgComponentUpperEdge* This,INetCfgComponent *pAdapter,DWORD dwNumInterfaces,const GUID *pguidInterfaceIds) {
    return This->lpVtbl->RemoveInterfacesFromAdapter(This,pAdapter,dwNumInterfaces,pguidInterfaceIds);
}
#endif
#endif

#endif


#endif  /* __INetCfgComponentUpperEdge_INTERFACE_DEFINED__ */


/*****************************************************************************
 * INetLanConnectionUiInfo interface
 */
#ifndef __INetLanConnectionUiInfo_INTERFACE_DEFINED__
#define __INetLanConnectionUiInfo_INTERFACE_DEFINED__

DEFINE_GUID(IID_INetLanConnectionUiInfo, 0xc08956a6, 0x1cd3, 0x11d1, 0xb1,0xc5, 0x00,0x80,0x5f,0xc1,0x27,0x0e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("c08956a6-1cd3-11d1-b1c5-00805fc1270e")
INetLanConnectionUiInfo : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetDeviceGuid(
        GUID *pguid) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(INetLanConnectionUiInfo, 0xc08956a6, 0x1cd3, 0x11d1, 0xb1,0xc5, 0x00,0x80,0x5f,0xc1,0x27,0x0e)
#endif
#else
typedef struct INetLanConnectionUiInfoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        INetLanConnectionUiInfo *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        INetLanConnectionUiInfo *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        INetLanConnectionUiInfo *This);

    /*** INetLanConnectionUiInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDeviceGuid)(
        INetLanConnectionUiInfo *This,
        GUID *pguid);

    END_INTERFACE
} INetLanConnectionUiInfoVtbl;

interface INetLanConnectionUiInfo {
    CONST_VTBL INetLanConnectionUiInfoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define INetLanConnectionUiInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define INetLanConnectionUiInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define INetLanConnectionUiInfo_Release(This) (This)->lpVtbl->Release(This)
/*** INetLanConnectionUiInfo methods ***/
#define INetLanConnectionUiInfo_GetDeviceGuid(This,pguid) (This)->lpVtbl->GetDeviceGuid(This,pguid)
#else
/*** IUnknown methods ***/
static inline HRESULT INetLanConnectionUiInfo_QueryInterface(INetLanConnectionUiInfo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG INetLanConnectionUiInfo_AddRef(INetLanConnectionUiInfo* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG INetLanConnectionUiInfo_Release(INetLanConnectionUiInfo* This) {
    return This->lpVtbl->Release(This);
}
/*** INetLanConnectionUiInfo methods ***/
static inline HRESULT INetLanConnectionUiInfo_GetDeviceGuid(INetLanConnectionUiInfo* This,GUID *pguid) {
    return This->lpVtbl->GetDeviceGuid(This,pguid);
}
#endif
#endif

#endif


#endif  /* __INetLanConnectionUiInfo_INTERFACE_DEFINED__ */


/*****************************************************************************
 * INetRasConnectionIpUiInfo interface
 */
#ifndef __INetRasConnectionIpUiInfo_INTERFACE_DEFINED__
#define __INetRasConnectionIpUiInfo_INTERFACE_DEFINED__

typedef enum tagRASCON_IPUI_FLAGS {
    RCUIF_VPN = 0x1,
    RCUIF_DEMAND_DIAL = 0x2,
    RCUIF_NOT_ADMIN = 0x4,
    RCUIF_USE_IPv4_STATICADDRESS = 0x8,
    RCUIF_USE_IPv4_NAME_SERVERS = 0x10,
    RCUIF_USE_IPv4_REMOTE_GATEWAY = 0x20,
    RCUIF_USE_IPv4_EXPLICIT_METRIC = 0x40,
    RCUIF_USE_HEADER_COMPRESSION = 0x80,
    RCUIF_USE_DISABLE_REGISTER_DNS = 0x100,
    RCUIF_USE_PRIVATE_DNS_SUFFIX = 0x200,
    RCUIF_ENABLE_NBT = 0x400,
    RCUIF_USE_IPv6_STATICADDRESS = 0x800,
    RCUIF_USE_IPv6_NAME_SERVERS = 0x1000,
    RCUIF_USE_IPv6_REMOTE_GATEWAY = 0x2000,
    RCUIF_USE_IPv6_EXPLICIT_METRIC = 0x4000,
    RCUIF_DISABLE_CLASS_BASED_ROUTE = 0x8000
} RASCON_UIINFO_FLAGS;
typedef struct tagRASCON_IPUI {
    GUID guidConnection;
    WINBOOL fIPv6Cfg;
    DWORD dwFlags;
    WCHAR pszwIpAddr[16];
    WCHAR pszwDnsAddr[16];
    WCHAR pszwDns2Addr[16];
    WCHAR pszwWinsAddr[16];
    WCHAR pszwWins2Addr[16];
    WCHAR pszwDnsSuffix[256];
    WCHAR pszwIpv6Addr[65];
    DWORD dwIpv6PrefixLength;
    WCHAR pszwIpv6DnsAddr[65];
    WCHAR pszwIpv6Dns2Addr[65];
    DWORD dwIPv4InfMetric;
    DWORD dwIPv6InfMetric;
} RASCON_IPUI;
DEFINE_GUID(IID_INetRasConnectionIpUiInfo, 0xfaedcf58, 0x31fe, 0x11d1, 0xaa,0xd2, 0x00,0x80,0x5f,0xc1,0x27,0x0e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("faedcf58-31fe-11d1-aad2-00805fc1270e")
INetRasConnectionIpUiInfo : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetUiInfo(
        RASCON_IPUI *pInfo) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(INetRasConnectionIpUiInfo, 0xfaedcf58, 0x31fe, 0x11d1, 0xaa,0xd2, 0x00,0x80,0x5f,0xc1,0x27,0x0e)
#endif
#else
typedef struct INetRasConnectionIpUiInfoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        INetRasConnectionIpUiInfo *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        INetRasConnectionIpUiInfo *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        INetRasConnectionIpUiInfo *This);

    /*** INetRasConnectionIpUiInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *GetUiInfo)(
        INetRasConnectionIpUiInfo *This,
        RASCON_IPUI *pInfo);

    END_INTERFACE
} INetRasConnectionIpUiInfoVtbl;

interface INetRasConnectionIpUiInfo {
    CONST_VTBL INetRasConnectionIpUiInfoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define INetRasConnectionIpUiInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define INetRasConnectionIpUiInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define INetRasConnectionIpUiInfo_Release(This) (This)->lpVtbl->Release(This)
/*** INetRasConnectionIpUiInfo methods ***/
#define INetRasConnectionIpUiInfo_GetUiInfo(This,pInfo) (This)->lpVtbl->GetUiInfo(This,pInfo)
#else
/*** IUnknown methods ***/
static inline HRESULT INetRasConnectionIpUiInfo_QueryInterface(INetRasConnectionIpUiInfo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG INetRasConnectionIpUiInfo_AddRef(INetRasConnectionIpUiInfo* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG INetRasConnectionIpUiInfo_Release(INetRasConnectionIpUiInfo* This) {
    return This->lpVtbl->Release(This);
}
/*** INetRasConnectionIpUiInfo methods ***/
static inline HRESULT INetRasConnectionIpUiInfo_GetUiInfo(INetRasConnectionIpUiInfo* This,RASCON_IPUI *pInfo) {
    return This->lpVtbl->GetUiInfo(This,pInfo);
}
#endif
#endif

#endif


#endif  /* __INetRasConnectionIpUiInfo_INTERFACE_DEFINED__ */


/*****************************************************************************
 * INetCfgComponentSysPrep interface
 */
#ifndef __INetCfgComponentSysPrep_INTERFACE_DEFINED__
#define __INetCfgComponentSysPrep_INTERFACE_DEFINED__

DEFINE_GUID(IID_INetCfgComponentSysPrep, 0xc0e8ae9a, 0x306e, 0x11d1, 0xaa,0xcf, 0x00,0x80,0x5f,0xc1,0x27,0x0e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("c0e8ae9a-306e-11d1-aacf-00805fc1270e")
INetCfgComponentSysPrep : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SaveAdapterParameters(
        INetCfgSysPrep *pncsp,
        LPCWSTR pszwAnswerSections,
        GUID *pAdapterInstanceGuid) = 0;

    virtual HRESULT STDMETHODCALLTYPE RestoreAdapterParameters(
        LPCWSTR pszwAnswerFile,
        LPCWSTR pszwAnswerSection,
        GUID *pAdapterInstanceGuid) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(INetCfgComponentSysPrep, 0xc0e8ae9a, 0x306e, 0x11d1, 0xaa,0xcf, 0x00,0x80,0x5f,0xc1,0x27,0x0e)
#endif
#else
typedef struct INetCfgComponentSysPrepVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        INetCfgComponentSysPrep *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        INetCfgComponentSysPrep *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        INetCfgComponentSysPrep *This);

    /*** INetCfgComponentSysPrep methods ***/
    HRESULT (STDMETHODCALLTYPE *SaveAdapterParameters)(
        INetCfgComponentSysPrep *This,
        INetCfgSysPrep *pncsp,
        LPCWSTR pszwAnswerSections,
        GUID *pAdapterInstanceGuid);

    HRESULT (STDMETHODCALLTYPE *RestoreAdapterParameters)(
        INetCfgComponentSysPrep *This,
        LPCWSTR pszwAnswerFile,
        LPCWSTR pszwAnswerSection,
        GUID *pAdapterInstanceGuid);

    END_INTERFACE
} INetCfgComponentSysPrepVtbl;

interface INetCfgComponentSysPrep {
    CONST_VTBL INetCfgComponentSysPrepVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define INetCfgComponentSysPrep_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define INetCfgComponentSysPrep_AddRef(This) (This)->lpVtbl->AddRef(This)
#define INetCfgComponentSysPrep_Release(This) (This)->lpVtbl->Release(This)
/*** INetCfgComponentSysPrep methods ***/
#define INetCfgComponentSysPrep_SaveAdapterParameters(This,pncsp,pszwAnswerSections,pAdapterInstanceGuid) (This)->lpVtbl->SaveAdapterParameters(This,pncsp,pszwAnswerSections,pAdapterInstanceGuid)
#define INetCfgComponentSysPrep_RestoreAdapterParameters(This,pszwAnswerFile,pszwAnswerSection,pAdapterInstanceGuid) (This)->lpVtbl->RestoreAdapterParameters(This,pszwAnswerFile,pszwAnswerSection,pAdapterInstanceGuid)
#else
/*** IUnknown methods ***/
static inline HRESULT INetCfgComponentSysPrep_QueryInterface(INetCfgComponentSysPrep* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG INetCfgComponentSysPrep_AddRef(INetCfgComponentSysPrep* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG INetCfgComponentSysPrep_Release(INetCfgComponentSysPrep* This) {
    return This->lpVtbl->Release(This);
}
/*** INetCfgComponentSysPrep methods ***/
static inline HRESULT INetCfgComponentSysPrep_SaveAdapterParameters(INetCfgComponentSysPrep* This,INetCfgSysPrep *pncsp,LPCWSTR pszwAnswerSections,GUID *pAdapterInstanceGuid) {
    return This->lpVtbl->SaveAdapterParameters(This,pncsp,pszwAnswerSections,pAdapterInstanceGuid);
}
static inline HRESULT INetCfgComponentSysPrep_RestoreAdapterParameters(INetCfgComponentSysPrep* This,LPCWSTR pszwAnswerFile,LPCWSTR pszwAnswerSection,GUID *pAdapterInstanceGuid) {
    return This->lpVtbl->RestoreAdapterParameters(This,pszwAnswerFile,pszwAnswerSection,pAdapterInstanceGuid);
}
#endif
#endif

#endif


#endif  /* __INetCfgComponentSysPrep_INTERFACE_DEFINED__ */

#endif /* WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP) */
/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __netcfgn_h__ */
