/*
 * Copyright (C) 2023 Mohamad Al-Jaf
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

#ifdef __WIDL__
#pragma winrt ns_prefix
#endif

import "inspectable.idl";
import "asyncinfo.idl";
import "eventtoken.idl";
import "windowscontracts.idl";
import "windows.foundation.idl";

namespace Windows.Devices.Radios {
    typedef enum RadioAccessStatus RadioAccessStatus;
    typedef enum RadioKind RadioKind;
    typedef enum RadioState RadioState;

    interface IRadio;
    interface IRadioStatics;

    runtimeclass Radio;

    declare {
        interface Windows.Foundation.Collections.IIterable<Windows.Devices.Radios.Radio *>;
        interface Windows.Foundation.Collections.IIterator<Windows.Devices.Radios.Radio *>;
        interface Windows.Foundation.Collections.IVectorView<Windows.Devices.Radios.Radio *>;
        interface Windows.Foundation.AsyncOperationCompletedHandler<Windows.Devices.Radios.Radio *>;
        interface Windows.Foundation.AsyncOperationCompletedHandler<Windows.Devices.Radios.RadioAccessStatus>;
        interface Windows.Foundation.AsyncOperationCompletedHandler<Windows.Foundation.Collections.IVectorView<Windows.Devices.Radios.Radio *> *>;
        interface Windows.Foundation.IAsyncOperation<Windows.Devices.Radios.Radio *>;
        interface Windows.Foundation.IAsyncOperation<Windows.Devices.Radios.RadioAccessStatus>;
        interface Windows.Foundation.IAsyncOperation<Windows.Foundation.Collections.IVectorView<Windows.Devices.Radios.Radio *> *>;
        interface Windows.Foundation.TypedEventHandler<Windows.Devices.Radios.Radio *, IInspectable *>;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0)
    ]
    enum RadioAccessStatus
    {
        Unspecified    = 0,
        Allowed        = 1,
        DeniedByUser   = 2,
        DeniedBySystem = 3,
    };

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0)
    ]
    enum RadioKind
    {
        Other           = 0,
        WiFi            = 1,
        MobileBroadband = 2,
        Bluetooth       = 3,
        FM              = 4,
    };

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0)
    ]
    enum RadioState
    {
        Unknown  = 0,
        On       = 1,
        Off      = 2,
        Disabled = 3,
    };

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.Devices.Radios.Radio),
        uuid(252118df-b33e-416a-875f-1cf38ae2d83e)
    ]
    interface IRadio : IInspectable
    {
        HRESULT SetStateAsync([in] Windows.Devices.Radios.RadioState value,
                              [out, retval] Windows.Foundation.IAsyncOperation<Windows.Devices.Radios.RadioAccessStatus> **retval);
        [eventadd] HRESULT StateChanged([in] Windows.Foundation.TypedEventHandler<Windows.Devices.Radios.Radio *, IInspectable *> *handler,
                                        [out, retval] EventRegistrationToken *cookie);
        [eventremove] HRESULT StateChanged([in] EventRegistrationToken cookie);
        [propget] HRESULT State([out, retval] Windows.Devices.Radios.RadioState **value);
        [propget] HRESULT Name([out, retval] HSTRING *value);
        [propget] HRESULT Kind([out, retval] Windows.Devices.Radios.RadioKind *value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.Devices.Radios.Radio),
        uuid(5fb6a12e-67cb-46ae-aae9-65919f86eff4)
    ]
    interface IRadioStatics : IInspectable
    {
        HRESULT GetRadiosAsync([out, retval] Windows.Foundation.IAsyncOperation<Windows.Foundation.Collections.IVectorView<Windows.Devices.Radios.Radio *> *> **value);
        HRESULT GetDeviceSelector([out, retval] HSTRING *selector);
        [overload("FromIdAsync")] HRESULT FromIdAsync([in] HSTRING id, [out, retval] Windows.Foundation.IAsyncOperation<Windows.Devices.Radios.Radio *> **value);
        HRESULT RequestAccessAsync([out, retval] Windows.Foundation.IAsyncOperation<Windows.Devices.Radios.RadioAccessStatus> **value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(agile),
        static(Windows.Devices.Radios.IRadioStatics, Windows.Foundation.UniversalApiContract, 1.0)
    ]
    runtimeclass Radio
    {
        [default] interface Windows.Devices.Radios.IRadio;
    }
}
