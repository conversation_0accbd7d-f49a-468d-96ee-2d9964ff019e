/*** Autogenerated by WIDL 10.8 from include/windows.applicationmodel.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __windows_applicationmodel_h__
#define __windows_applicationmodel_h__

/* Forward declarations */

#ifndef ____x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo __x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo ABI::Windows::ApplicationModel::IAppDisplayInfo
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            interface IAppDisplayInfo;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CIAppInfo_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CIAppInfo_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CIAppInfo __x_ABI_CWindows_CApplicationModel_CIAppInfo;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CIAppInfo ABI::Windows::ApplicationModel::IAppInfo
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            interface IAppInfo;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CIAppInfo2_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CIAppInfo2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CIAppInfo2 __x_ABI_CWindows_CApplicationModel_CIAppInfo2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CIAppInfo2 ABI::Windows::ApplicationModel::IAppInfo2
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            interface IAppInfo2;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CIAppInfo3_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CIAppInfo3_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CIAppInfo3 __x_ABI_CWindows_CApplicationModel_CIAppInfo3;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CIAppInfo3 ABI::Windows::ApplicationModel::IAppInfo3
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            interface IAppInfo3;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CIAppInfo4_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CIAppInfo4_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CIAppInfo4 __x_ABI_CWindows_CApplicationModel_CIAppInfo4;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CIAppInfo4 ABI::Windows::ApplicationModel::IAppInfo4
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            interface IAppInfo4;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CIAppInfoStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CIAppInfoStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CIAppInfoStatics __x_ABI_CWindows_CApplicationModel_CIAppInfoStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CIAppInfoStatics ABI::Windows::ApplicationModel::IAppInfoStatics
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            interface IAppInfoStatics;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CIDesignModeStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CIDesignModeStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics ABI::Windows::ApplicationModel::IDesignModeStatics
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            interface IDesignModeStatics;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CIDesignModeStatics2_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CIDesignModeStatics2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics2 __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics2 ABI::Windows::ApplicationModel::IDesignModeStatics2
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            interface IDesignModeStatics2;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CIEnteredBackgroundEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CIEnteredBackgroundEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CIEnteredBackgroundEventArgs __x_ABI_CWindows_CApplicationModel_CIEnteredBackgroundEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CIEnteredBackgroundEventArgs ABI::Windows::ApplicationModel::IEnteredBackgroundEventArgs
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            interface IEnteredBackgroundEventArgs;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CILeavingBackgroundEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CILeavingBackgroundEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CILeavingBackgroundEventArgs __x_ABI_CWindows_CApplicationModel_CILeavingBackgroundEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CILeavingBackgroundEventArgs ABI::Windows::ApplicationModel::ILeavingBackgroundEventArgs
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            interface ILeavingBackgroundEventArgs;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CIPackage_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CIPackage_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CIPackage __x_ABI_CWindows_CApplicationModel_CIPackage;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CIPackage ABI::Windows::ApplicationModel::IPackage
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            interface IPackage;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CIPackage2_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CIPackage2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CIPackage2 __x_ABI_CWindows_CApplicationModel_CIPackage2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CIPackage2 ABI::Windows::ApplicationModel::IPackage2
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            interface IPackage2;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CIPackage3_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CIPackage3_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CIPackage3 __x_ABI_CWindows_CApplicationModel_CIPackage3;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CIPackage3 ABI::Windows::ApplicationModel::IPackage3
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            interface IPackage3;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CIPackageStatus_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CIPackageStatus_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CIPackageStatus __x_ABI_CWindows_CApplicationModel_CIPackageStatus;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CIPackageStatus ABI::Windows::ApplicationModel::IPackageStatus
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            interface IPackageStatus;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CIPackageId_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CIPackageId_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CIPackageId __x_ABI_CWindows_CApplicationModel_CIPackageId;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CIPackageId ABI::Windows::ApplicationModel::IPackageId
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            interface IPackageId;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CIPackageIdWithMetadata_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CIPackageIdWithMetadata_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CIPackageIdWithMetadata __x_ABI_CWindows_CApplicationModel_CIPackageIdWithMetadata;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CIPackageIdWithMetadata ABI::Windows::ApplicationModel::IPackageIdWithMetadata
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            interface IPackageIdWithMetadata;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CIPackageStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CIPackageStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CIPackageStatics __x_ABI_CWindows_CApplicationModel_CIPackageStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CIPackageStatics ABI::Windows::ApplicationModel::IPackageStatics
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            interface IPackageStatics;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CISuspendingDeferral_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CISuspendingDeferral_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CISuspendingDeferral __x_ABI_CWindows_CApplicationModel_CISuspendingDeferral;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CISuspendingDeferral ABI::Windows::ApplicationModel::ISuspendingDeferral
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            interface ISuspendingDeferral;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CISuspendingEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CISuspendingEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CISuspendingEventArgs __x_ABI_CWindows_CApplicationModel_CISuspendingEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CISuspendingEventArgs ABI::Windows::ApplicationModel::ISuspendingEventArgs
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            interface ISuspendingEventArgs;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CISuspendingOperation_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CISuspendingOperation_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CISuspendingOperation __x_ABI_CWindows_CApplicationModel_CISuspendingOperation;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CISuspendingOperation ABI::Windows::ApplicationModel::ISuspendingOperation
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            interface ISuspendingOperation;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CAppDisplayInfo_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CAppDisplayInfo_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            class AppDisplayInfo;
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CAppDisplayInfo __x_ABI_CWindows_CApplicationModel_CAppDisplayInfo;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CApplicationModel_CAppDisplayInfo_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CApplicationModel_CAppInfo_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CAppInfo_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            class AppInfo;
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CAppInfo __x_ABI_CWindows_CApplicationModel_CAppInfo;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CApplicationModel_CAppInfo_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CApplicationModel_CDesignMode_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CDesignMode_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            class DesignMode;
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CDesignMode __x_ABI_CWindows_CApplicationModel_CDesignMode;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CApplicationModel_CDesignMode_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CApplicationModel_CEnteredBackgroundEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CEnteredBackgroundEventArgs_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            class EnteredBackgroundEventArgs;
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CEnteredBackgroundEventArgs __x_ABI_CWindows_CApplicationModel_CEnteredBackgroundEventArgs;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CApplicationModel_CEnteredBackgroundEventArgs_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CApplicationModel_CLeavingBackgroundEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CLeavingBackgroundEventArgs_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            class LeavingBackgroundEventArgs;
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CLeavingBackgroundEventArgs __x_ABI_CWindows_CApplicationModel_CLeavingBackgroundEventArgs;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CApplicationModel_CLeavingBackgroundEventArgs_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CApplicationModel_CPackage_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CPackage_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            class Package;
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CPackage __x_ABI_CWindows_CApplicationModel_CPackage;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CApplicationModel_CPackage_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CApplicationModel_CPackageStatus_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CPackageStatus_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            class PackageStatus;
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CPackageStatus __x_ABI_CWindows_CApplicationModel_CPackageStatus;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CApplicationModel_CPackageStatus_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CApplicationModel_CPackageId_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CPackageId_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            class PackageId;
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CPackageId __x_ABI_CWindows_CApplicationModel_CPackageId;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CApplicationModel_CPackageId_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CApplicationModel_CSuspendingDeferral_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CSuspendingDeferral_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            class SuspendingDeferral;
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CSuspendingDeferral __x_ABI_CWindows_CApplicationModel_CSuspendingDeferral;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CApplicationModel_CSuspendingDeferral_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CApplicationModel_CSuspendingEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CSuspendingEventArgs_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            class SuspendingEventArgs;
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CSuspendingEventArgs __x_ABI_CWindows_CApplicationModel_CSuspendingEventArgs;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CApplicationModel_CSuspendingEventArgs_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CApplicationModel_CSuspendingOperation_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CSuspendingOperation_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            class SuspendingOperation;
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CSuspendingOperation __x_ABI_CWindows_CApplicationModel_CSuspendingOperation;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CApplicationModel_CSuspendingOperation_FWD_DEFINED__ */

#ifndef ____FIIterable_1_Windows__CApplicationModel__CAppInfo_FWD_DEFINED__
#define ____FIIterable_1_Windows__CApplicationModel__CAppInfo_FWD_DEFINED__
typedef interface __FIIterable_1_Windows__CApplicationModel__CAppInfo __FIIterable_1_Windows__CApplicationModel__CAppInfo;
#ifdef __cplusplus
#define __FIIterable_1_Windows__CApplicationModel__CAppInfo ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::ApplicationModel::AppInfo* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterable_1_Windows__CApplicationModel__CPackage_FWD_DEFINED__
#define ____FIIterable_1_Windows__CApplicationModel__CPackage_FWD_DEFINED__
typedef interface __FIIterable_1_Windows__CApplicationModel__CPackage __FIIterable_1_Windows__CApplicationModel__CPackage;
#ifdef __cplusplus
#define __FIIterable_1_Windows__CApplicationModel__CPackage ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::ApplicationModel::Package* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_Windows__CApplicationModel__CAppInfo_FWD_DEFINED__
#define ____FIIterator_1_Windows__CApplicationModel__CAppInfo_FWD_DEFINED__
typedef interface __FIIterator_1_Windows__CApplicationModel__CAppInfo __FIIterator_1_Windows__CApplicationModel__CAppInfo;
#ifdef __cplusplus
#define __FIIterator_1_Windows__CApplicationModel__CAppInfo ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::ApplicationModel::AppInfo* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_Windows__CApplicationModel__CPackage_FWD_DEFINED__
#define ____FIIterator_1_Windows__CApplicationModel__CPackage_FWD_DEFINED__
typedef interface __FIIterator_1_Windows__CApplicationModel__CPackage __FIIterator_1_Windows__CApplicationModel__CPackage;
#ifdef __cplusplus
#define __FIIterator_1_Windows__CApplicationModel__CPackage ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::ApplicationModel::Package* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CApplicationModel__CAppInfo_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CApplicationModel__CAppInfo_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CApplicationModel__CAppInfo __FIVectorView_1_Windows__CApplicationModel__CAppInfo;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CApplicationModel__CAppInfo ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::ApplicationModel::AppInfo* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CApplicationModel__CPackage_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CApplicationModel__CPackage_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CApplicationModel__CPackage __FIVectorView_1_Windows__CApplicationModel__CPackage;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CApplicationModel__CPackage ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::ApplicationModel::Package* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry __FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::ApplicationModel::Core::AppListEntry* >
#endif /* __cplusplus */
#endif

#ifndef ____FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs_FWD_DEFINED__
#define ____FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs_FWD_DEFINED__
typedef interface __FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs __FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs;
#ifdef __cplusplus
#define __FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs ABI::Windows::Foundation::IEventHandler<ABI::Windows::ApplicationModel::EnteredBackgroundEventArgs* >
#endif /* __cplusplus */
#endif

#ifndef ____FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs_FWD_DEFINED__
#define ____FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs_FWD_DEFINED__
typedef interface __FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs __FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs;
#ifdef __cplusplus
#define __FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs ABI::Windows::Foundation::IEventHandler<ABI::Windows::ApplicationModel::LeavingBackgroundEventArgs* >
#endif /* __cplusplus */
#endif

#ifndef ____FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs_FWD_DEFINED__
#define ____FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs_FWD_DEFINED__
typedef interface __FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs __FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs;
#ifdef __cplusplus
#define __FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs ABI::Windows::Foundation::IEventHandler<ABI::Windows::ApplicationModel::SuspendingEventArgs* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo_FWD_DEFINED__
#define ____FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo_FWD_DEFINED__
typedef interface __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo;
#ifdef __cplusplus
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::ApplicationModel::AppInfo* >* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_FWD_DEFINED__
#define ____FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_FWD_DEFINED__
typedef interface __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry;
#ifdef __cplusplus
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::ApplicationModel::Core::AppListEntry* >* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo_FWD_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo_FWD_DEFINED__
typedef interface __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo;
#ifdef __cplusplus
#define __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo ABI::Windows::Foundation::IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::ApplicationModel::AppInfo* >* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_FWD_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_FWD_DEFINED__
typedef interface __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry;
#ifdef __cplusplus
#define __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry ABI::Windows::Foundation::IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::ApplicationModel::Core::AppListEntry* >* >
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <windows.foundation.h>
#include <windows.storage.h>
#include <windows.system.h>
#include <windows.applicationmodel.activation.h>
#include <windows.applicationmodel.core.h>

#ifdef __cplusplus
extern "C" {
#endif

#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CApplicationModel_CAppExecutionContext __x_ABI_CWindows_CApplicationModel_CAppExecutionContext;
#endif /* __cplusplus */

#ifndef __cplusplus
typedef struct __x_ABI_CWindows_CApplicationModel_CPackageVersion __x_ABI_CWindows_CApplicationModel_CPackageVersion;
#else /* __cplusplus */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            typedef struct PackageVersion PackageVersion;
        }
    }
}
#endif /* __cplusplus */

#ifndef ____x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo __x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo ABI::Windows::ApplicationModel::IAppDisplayInfo
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            interface IAppDisplayInfo;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CIAppInfo_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CIAppInfo_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CIAppInfo __x_ABI_CWindows_CApplicationModel_CIAppInfo;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CIAppInfo ABI::Windows::ApplicationModel::IAppInfo
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            interface IAppInfo;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CIAppInfo2_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CIAppInfo2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CIAppInfo2 __x_ABI_CWindows_CApplicationModel_CIAppInfo2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CIAppInfo2 ABI::Windows::ApplicationModel::IAppInfo2
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            interface IAppInfo2;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CIAppInfo3_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CIAppInfo3_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CIAppInfo3 __x_ABI_CWindows_CApplicationModel_CIAppInfo3;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CIAppInfo3 ABI::Windows::ApplicationModel::IAppInfo3
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            interface IAppInfo3;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CIAppInfo4_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CIAppInfo4_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CIAppInfo4 __x_ABI_CWindows_CApplicationModel_CIAppInfo4;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CIAppInfo4 ABI::Windows::ApplicationModel::IAppInfo4
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            interface IAppInfo4;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CIAppInfoStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CIAppInfoStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CIAppInfoStatics __x_ABI_CWindows_CApplicationModel_CIAppInfoStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CIAppInfoStatics ABI::Windows::ApplicationModel::IAppInfoStatics
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            interface IAppInfoStatics;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CIDesignModeStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CIDesignModeStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics ABI::Windows::ApplicationModel::IDesignModeStatics
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            interface IDesignModeStatics;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CIDesignModeStatics2_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CIDesignModeStatics2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics2 __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics2 ABI::Windows::ApplicationModel::IDesignModeStatics2
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            interface IDesignModeStatics2;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CIEnteredBackgroundEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CIEnteredBackgroundEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CIEnteredBackgroundEventArgs __x_ABI_CWindows_CApplicationModel_CIEnteredBackgroundEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CIEnteredBackgroundEventArgs ABI::Windows::ApplicationModel::IEnteredBackgroundEventArgs
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            interface IEnteredBackgroundEventArgs;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CILeavingBackgroundEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CILeavingBackgroundEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CILeavingBackgroundEventArgs __x_ABI_CWindows_CApplicationModel_CILeavingBackgroundEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CILeavingBackgroundEventArgs ABI::Windows::ApplicationModel::ILeavingBackgroundEventArgs
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            interface ILeavingBackgroundEventArgs;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CIPackage_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CIPackage_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CIPackage __x_ABI_CWindows_CApplicationModel_CIPackage;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CIPackage ABI::Windows::ApplicationModel::IPackage
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            interface IPackage;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CIPackage2_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CIPackage2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CIPackage2 __x_ABI_CWindows_CApplicationModel_CIPackage2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CIPackage2 ABI::Windows::ApplicationModel::IPackage2
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            interface IPackage2;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CIPackage3_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CIPackage3_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CIPackage3 __x_ABI_CWindows_CApplicationModel_CIPackage3;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CIPackage3 ABI::Windows::ApplicationModel::IPackage3
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            interface IPackage3;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CIPackageStatus_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CIPackageStatus_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CIPackageStatus __x_ABI_CWindows_CApplicationModel_CIPackageStatus;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CIPackageStatus ABI::Windows::ApplicationModel::IPackageStatus
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            interface IPackageStatus;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CIPackageId_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CIPackageId_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CIPackageId __x_ABI_CWindows_CApplicationModel_CIPackageId;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CIPackageId ABI::Windows::ApplicationModel::IPackageId
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            interface IPackageId;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CIPackageIdWithMetadata_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CIPackageIdWithMetadata_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CIPackageIdWithMetadata __x_ABI_CWindows_CApplicationModel_CIPackageIdWithMetadata;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CIPackageIdWithMetadata ABI::Windows::ApplicationModel::IPackageIdWithMetadata
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            interface IPackageIdWithMetadata;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CIPackageStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CIPackageStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CIPackageStatics __x_ABI_CWindows_CApplicationModel_CIPackageStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CIPackageStatics ABI::Windows::ApplicationModel::IPackageStatics
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            interface IPackageStatics;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CISuspendingDeferral_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CISuspendingDeferral_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CISuspendingDeferral __x_ABI_CWindows_CApplicationModel_CISuspendingDeferral;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CISuspendingDeferral ABI::Windows::ApplicationModel::ISuspendingDeferral
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            interface ISuspendingDeferral;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CISuspendingEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CISuspendingEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CISuspendingEventArgs __x_ABI_CWindows_CApplicationModel_CISuspendingEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CISuspendingEventArgs ABI::Windows::ApplicationModel::ISuspendingEventArgs
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            interface ISuspendingEventArgs;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CISuspendingOperation_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CISuspendingOperation_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CISuspendingOperation __x_ABI_CWindows_CApplicationModel_CISuspendingOperation;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CISuspendingOperation ABI::Windows::ApplicationModel::ISuspendingOperation
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            interface ISuspendingOperation;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____FIIterable_1_Windows__CApplicationModel__CAppInfo_FWD_DEFINED__
#define ____FIIterable_1_Windows__CApplicationModel__CAppInfo_FWD_DEFINED__
typedef interface __FIIterable_1_Windows__CApplicationModel__CAppInfo __FIIterable_1_Windows__CApplicationModel__CAppInfo;
#ifdef __cplusplus
#define __FIIterable_1_Windows__CApplicationModel__CAppInfo ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::ApplicationModel::AppInfo* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterable_1_Windows__CApplicationModel__CPackage_FWD_DEFINED__
#define ____FIIterable_1_Windows__CApplicationModel__CPackage_FWD_DEFINED__
typedef interface __FIIterable_1_Windows__CApplicationModel__CPackage __FIIterable_1_Windows__CApplicationModel__CPackage;
#ifdef __cplusplus
#define __FIIterable_1_Windows__CApplicationModel__CPackage ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::ApplicationModel::Package* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_Windows__CApplicationModel__CAppInfo_FWD_DEFINED__
#define ____FIIterator_1_Windows__CApplicationModel__CAppInfo_FWD_DEFINED__
typedef interface __FIIterator_1_Windows__CApplicationModel__CAppInfo __FIIterator_1_Windows__CApplicationModel__CAppInfo;
#ifdef __cplusplus
#define __FIIterator_1_Windows__CApplicationModel__CAppInfo ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::ApplicationModel::AppInfo* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_Windows__CApplicationModel__CPackage_FWD_DEFINED__
#define ____FIIterator_1_Windows__CApplicationModel__CPackage_FWD_DEFINED__
typedef interface __FIIterator_1_Windows__CApplicationModel__CPackage __FIIterator_1_Windows__CApplicationModel__CPackage;
#ifdef __cplusplus
#define __FIIterator_1_Windows__CApplicationModel__CPackage ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::ApplicationModel::Package* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CApplicationModel__CAppInfo_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CApplicationModel__CAppInfo_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CApplicationModel__CAppInfo __FIVectorView_1_Windows__CApplicationModel__CAppInfo;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CApplicationModel__CAppInfo ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::ApplicationModel::AppInfo* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CApplicationModel__CPackage_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CApplicationModel__CPackage_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CApplicationModel__CPackage __FIVectorView_1_Windows__CApplicationModel__CPackage;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CApplicationModel__CPackage ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::ApplicationModel::Package* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry __FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::ApplicationModel::Core::AppListEntry* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo_FWD_DEFINED__
#define ____FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo_FWD_DEFINED__
typedef interface __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo;
#ifdef __cplusplus
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::ApplicationModel::AppInfo* >* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_FWD_DEFINED__
#define ____FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_FWD_DEFINED__
typedef interface __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry;
#ifdef __cplusplus
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::ApplicationModel::Core::AppListEntry* >* >
#endif /* __cplusplus */
#endif

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xb0000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            enum AppExecutionContext {
                AppExecutionContext_Unknown = 0,
                AppExecutionContext_Host = 1,
                AppExecutionContext_Guest = 2
            };
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CApplicationModel_CAppExecutionContext {
    AppExecutionContext_Unknown = 0,
    AppExecutionContext_Host = 1,
    AppExecutionContext_Guest = 2
};
#ifdef WIDL_using_Windows_ApplicationModel
#define AppExecutionContext __x_ABI_CWindows_CApplicationModel_CAppExecutionContext
#endif /* WIDL_using_Windows_ApplicationModel */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xb0000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            struct PackageVersion {
                UINT16 Major;
                UINT16 Minor;
                UINT16 Build;
                UINT16 Revision;
            };
        }
    }
}
extern "C" {
#else
struct __x_ABI_CWindows_CApplicationModel_CPackageVersion {
    UINT16 Major;
    UINT16 Minor;
    UINT16 Build;
    UINT16 Revision;
};
#ifdef WIDL_using_Windows_ApplicationModel
#define PackageVersion __x_ABI_CWindows_CApplicationModel_CPackageVersion
#endif /* WIDL_using_Windows_ApplicationModel */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
/*****************************************************************************
 * IAppDisplayInfo interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo, 0x1aeb1103, 0xe4d4, 0x41aa, 0xa4,0xf6, 0xc4,0xa2,0x76,0xe7,0x9e,0xac);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            MIDL_INTERFACE("1aeb1103-e4d4-41aa-a4f6-c4a276e79eac")
            IAppDisplayInfo : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE get_DisplayName(
                    HSTRING *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Description(
                    HSTRING *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE GetLogo(
                    ABI::Windows::Foundation::Size size,
                    ABI::Windows::Storage::Streams::IRandomAccessStreamReference **value) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo, 0x1aeb1103, 0xe4d4, 0x41aa, 0xa4,0xf6, 0xc4,0xa2,0x76,0xe7,0x9e,0xac)
#endif
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CIAppDisplayInfoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo *This,
        TrustLevel *trustLevel);

    /*** IAppDisplayInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *get_DisplayName)(
        __x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_Description)(
        __x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *GetLogo)(
        __x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo *This,
        __x_ABI_CWindows_CFoundation_CSize size,
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference **value);

    END_INTERFACE
} __x_ABI_CWindows_CApplicationModel_CIAppDisplayInfoVtbl;

interface __x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo {
    CONST_VTBL __x_ABI_CWindows_CApplicationModel_CIAppDisplayInfoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAppDisplayInfo methods ***/
#define __x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo_get_DisplayName(This,value) (This)->lpVtbl->get_DisplayName(This,value)
#define __x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo_get_Description(This,value) (This)->lpVtbl->get_Description(This,value)
#define __x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo_GetLogo(This,size,value) (This)->lpVtbl->GetLogo(This,size,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo_QueryInterface(__x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo_AddRef(__x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo_Release(__x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo_GetIids(__x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo_GetRuntimeClassName(__x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo_GetTrustLevel(__x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAppDisplayInfo methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo_get_DisplayName(__x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo* This,HSTRING *value) {
    return This->lpVtbl->get_DisplayName(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo_get_Description(__x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo* This,HSTRING *value) {
    return This->lpVtbl->get_Description(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo_GetLogo(__x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo* This,__x_ABI_CWindows_CFoundation_CSize size,__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference **value) {
    return This->lpVtbl->GetLogo(This,size,value);
}
#endif
#ifdef WIDL_using_Windows_ApplicationModel
#define IID_IAppDisplayInfo IID___x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo
#define IAppDisplayInfoVtbl __x_ABI_CWindows_CApplicationModel_CIAppDisplayInfoVtbl
#define IAppDisplayInfo __x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo
#define IAppDisplayInfo_QueryInterface __x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo_QueryInterface
#define IAppDisplayInfo_AddRef __x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo_AddRef
#define IAppDisplayInfo_Release __x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo_Release
#define IAppDisplayInfo_GetIids __x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo_GetIids
#define IAppDisplayInfo_GetRuntimeClassName __x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo_GetRuntimeClassName
#define IAppDisplayInfo_GetTrustLevel __x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo_GetTrustLevel
#define IAppDisplayInfo_get_DisplayName __x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo_get_DisplayName
#define IAppDisplayInfo_get_Description __x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo_get_Description
#define IAppDisplayInfo_GetLogo __x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo_GetLogo
#endif /* WIDL_using_Windows_ApplicationModel */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IAppInfo interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CApplicationModel_CIAppInfo_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CIAppInfo_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CApplicationModel_CIAppInfo, 0xcf7f59b3, 0x6a09, 0x4de8, 0xa6,0xc0, 0x57,0x92,0xd5,0x68,0x80,0xd1);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            MIDL_INTERFACE("cf7f59b3-6a09-4de8-a6c0-5792d56880d1")
            IAppInfo : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE get_Id(
                    HSTRING *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_AppUserModelId(
                    HSTRING *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_DisplayInfo(
                    ABI::Windows::ApplicationModel::IAppDisplayInfo **value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_PackageFamilyName(
                    HSTRING *value) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CApplicationModel_CIAppInfo, 0xcf7f59b3, 0x6a09, 0x4de8, 0xa6,0xc0, 0x57,0x92,0xd5,0x68,0x80,0xd1)
#endif
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CIAppInfoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CApplicationModel_CIAppInfo *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CApplicationModel_CIAppInfo *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CApplicationModel_CIAppInfo *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CApplicationModel_CIAppInfo *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CApplicationModel_CIAppInfo *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CApplicationModel_CIAppInfo *This,
        TrustLevel *trustLevel);

    /*** IAppInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Id)(
        __x_ABI_CWindows_CApplicationModel_CIAppInfo *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_AppUserModelId)(
        __x_ABI_CWindows_CApplicationModel_CIAppInfo *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_DisplayInfo)(
        __x_ABI_CWindows_CApplicationModel_CIAppInfo *This,
        __x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo **value);

    HRESULT (STDMETHODCALLTYPE *get_PackageFamilyName)(
        __x_ABI_CWindows_CApplicationModel_CIAppInfo *This,
        HSTRING *value);

    END_INTERFACE
} __x_ABI_CWindows_CApplicationModel_CIAppInfoVtbl;

interface __x_ABI_CWindows_CApplicationModel_CIAppInfo {
    CONST_VTBL __x_ABI_CWindows_CApplicationModel_CIAppInfoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CApplicationModel_CIAppInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CApplicationModel_CIAppInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CApplicationModel_CIAppInfo_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CApplicationModel_CIAppInfo_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CApplicationModel_CIAppInfo_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CApplicationModel_CIAppInfo_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAppInfo methods ***/
#define __x_ABI_CWindows_CApplicationModel_CIAppInfo_get_Id(This,value) (This)->lpVtbl->get_Id(This,value)
#define __x_ABI_CWindows_CApplicationModel_CIAppInfo_get_AppUserModelId(This,value) (This)->lpVtbl->get_AppUserModelId(This,value)
#define __x_ABI_CWindows_CApplicationModel_CIAppInfo_get_DisplayInfo(This,value) (This)->lpVtbl->get_DisplayInfo(This,value)
#define __x_ABI_CWindows_CApplicationModel_CIAppInfo_get_PackageFamilyName(This,value) (This)->lpVtbl->get_PackageFamilyName(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIAppInfo_QueryInterface(__x_ABI_CWindows_CApplicationModel_CIAppInfo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CIAppInfo_AddRef(__x_ABI_CWindows_CApplicationModel_CIAppInfo* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CIAppInfo_Release(__x_ABI_CWindows_CApplicationModel_CIAppInfo* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIAppInfo_GetIids(__x_ABI_CWindows_CApplicationModel_CIAppInfo* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIAppInfo_GetRuntimeClassName(__x_ABI_CWindows_CApplicationModel_CIAppInfo* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIAppInfo_GetTrustLevel(__x_ABI_CWindows_CApplicationModel_CIAppInfo* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAppInfo methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIAppInfo_get_Id(__x_ABI_CWindows_CApplicationModel_CIAppInfo* This,HSTRING *value) {
    return This->lpVtbl->get_Id(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIAppInfo_get_AppUserModelId(__x_ABI_CWindows_CApplicationModel_CIAppInfo* This,HSTRING *value) {
    return This->lpVtbl->get_AppUserModelId(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIAppInfo_get_DisplayInfo(__x_ABI_CWindows_CApplicationModel_CIAppInfo* This,__x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo **value) {
    return This->lpVtbl->get_DisplayInfo(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIAppInfo_get_PackageFamilyName(__x_ABI_CWindows_CApplicationModel_CIAppInfo* This,HSTRING *value) {
    return This->lpVtbl->get_PackageFamilyName(This,value);
}
#endif
#ifdef WIDL_using_Windows_ApplicationModel
#define IID_IAppInfo IID___x_ABI_CWindows_CApplicationModel_CIAppInfo
#define IAppInfoVtbl __x_ABI_CWindows_CApplicationModel_CIAppInfoVtbl
#define IAppInfo __x_ABI_CWindows_CApplicationModel_CIAppInfo
#define IAppInfo_QueryInterface __x_ABI_CWindows_CApplicationModel_CIAppInfo_QueryInterface
#define IAppInfo_AddRef __x_ABI_CWindows_CApplicationModel_CIAppInfo_AddRef
#define IAppInfo_Release __x_ABI_CWindows_CApplicationModel_CIAppInfo_Release
#define IAppInfo_GetIids __x_ABI_CWindows_CApplicationModel_CIAppInfo_GetIids
#define IAppInfo_GetRuntimeClassName __x_ABI_CWindows_CApplicationModel_CIAppInfo_GetRuntimeClassName
#define IAppInfo_GetTrustLevel __x_ABI_CWindows_CApplicationModel_CIAppInfo_GetTrustLevel
#define IAppInfo_get_Id __x_ABI_CWindows_CApplicationModel_CIAppInfo_get_Id
#define IAppInfo_get_AppUserModelId __x_ABI_CWindows_CApplicationModel_CIAppInfo_get_AppUserModelId
#define IAppInfo_get_DisplayInfo __x_ABI_CWindows_CApplicationModel_CIAppInfo_get_DisplayInfo
#define IAppInfo_get_PackageFamilyName __x_ABI_CWindows_CApplicationModel_CIAppInfo_get_PackageFamilyName
#endif /* WIDL_using_Windows_ApplicationModel */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CApplicationModel_CIAppInfo_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IAppInfo2 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xa0000
#ifndef ____x_ABI_CWindows_CApplicationModel_CIAppInfo2_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CIAppInfo2_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CApplicationModel_CIAppInfo2, 0xbe4b1f5a, 0x2098, 0x431b, 0xbd,0x25, 0xb3,0x08,0x78,0x74,0x8d,0x47);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            MIDL_INTERFACE("be4b1f5a-2098-431b-bd25-b30878748d47")
            IAppInfo2 : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE get_Package(
                    ABI::Windows::ApplicationModel::IPackage **value) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CApplicationModel_CIAppInfo2, 0xbe4b1f5a, 0x2098, 0x431b, 0xbd,0x25, 0xb3,0x08,0x78,0x74,0x8d,0x47)
#endif
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CIAppInfo2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CApplicationModel_CIAppInfo2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CApplicationModel_CIAppInfo2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CApplicationModel_CIAppInfo2 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CApplicationModel_CIAppInfo2 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CApplicationModel_CIAppInfo2 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CApplicationModel_CIAppInfo2 *This,
        TrustLevel *trustLevel);

    /*** IAppInfo2 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Package)(
        __x_ABI_CWindows_CApplicationModel_CIAppInfo2 *This,
        __x_ABI_CWindows_CApplicationModel_CIPackage **value);

    END_INTERFACE
} __x_ABI_CWindows_CApplicationModel_CIAppInfo2Vtbl;

interface __x_ABI_CWindows_CApplicationModel_CIAppInfo2 {
    CONST_VTBL __x_ABI_CWindows_CApplicationModel_CIAppInfo2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CApplicationModel_CIAppInfo2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CApplicationModel_CIAppInfo2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CApplicationModel_CIAppInfo2_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CApplicationModel_CIAppInfo2_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CApplicationModel_CIAppInfo2_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CApplicationModel_CIAppInfo2_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAppInfo2 methods ***/
#define __x_ABI_CWindows_CApplicationModel_CIAppInfo2_get_Package(This,value) (This)->lpVtbl->get_Package(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIAppInfo2_QueryInterface(__x_ABI_CWindows_CApplicationModel_CIAppInfo2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CIAppInfo2_AddRef(__x_ABI_CWindows_CApplicationModel_CIAppInfo2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CIAppInfo2_Release(__x_ABI_CWindows_CApplicationModel_CIAppInfo2* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIAppInfo2_GetIids(__x_ABI_CWindows_CApplicationModel_CIAppInfo2* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIAppInfo2_GetRuntimeClassName(__x_ABI_CWindows_CApplicationModel_CIAppInfo2* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIAppInfo2_GetTrustLevel(__x_ABI_CWindows_CApplicationModel_CIAppInfo2* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAppInfo2 methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIAppInfo2_get_Package(__x_ABI_CWindows_CApplicationModel_CIAppInfo2* This,__x_ABI_CWindows_CApplicationModel_CIPackage **value) {
    return This->lpVtbl->get_Package(This,value);
}
#endif
#ifdef WIDL_using_Windows_ApplicationModel
#define IID_IAppInfo2 IID___x_ABI_CWindows_CApplicationModel_CIAppInfo2
#define IAppInfo2Vtbl __x_ABI_CWindows_CApplicationModel_CIAppInfo2Vtbl
#define IAppInfo2 __x_ABI_CWindows_CApplicationModel_CIAppInfo2
#define IAppInfo2_QueryInterface __x_ABI_CWindows_CApplicationModel_CIAppInfo2_QueryInterface
#define IAppInfo2_AddRef __x_ABI_CWindows_CApplicationModel_CIAppInfo2_AddRef
#define IAppInfo2_Release __x_ABI_CWindows_CApplicationModel_CIAppInfo2_Release
#define IAppInfo2_GetIids __x_ABI_CWindows_CApplicationModel_CIAppInfo2_GetIids
#define IAppInfo2_GetRuntimeClassName __x_ABI_CWindows_CApplicationModel_CIAppInfo2_GetRuntimeClassName
#define IAppInfo2_GetTrustLevel __x_ABI_CWindows_CApplicationModel_CIAppInfo2_GetTrustLevel
#define IAppInfo2_get_Package __x_ABI_CWindows_CApplicationModel_CIAppInfo2_get_Package
#endif /* WIDL_using_Windows_ApplicationModel */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CApplicationModel_CIAppInfo2_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xa0000 */

/*****************************************************************************
 * IAppInfo3 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xb0000
#ifndef ____x_ABI_CWindows_CApplicationModel_CIAppInfo3_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CIAppInfo3_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CApplicationModel_CIAppInfo3, 0x09a78e46, 0x93a4, 0x46de, 0x93,0x97, 0x08,0x43,0xb5,0x71,0x15,0xea);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            MIDL_INTERFACE("09a78e46-93a4-46de-9397-0843b57115ea")
            IAppInfo3 : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE get_ExecutionContext(
                    ABI::Windows::ApplicationModel::AppExecutionContext *value) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CApplicationModel_CIAppInfo3, 0x09a78e46, 0x93a4, 0x46de, 0x93,0x97, 0x08,0x43,0xb5,0x71,0x15,0xea)
#endif
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CIAppInfo3Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CApplicationModel_CIAppInfo3 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CApplicationModel_CIAppInfo3 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CApplicationModel_CIAppInfo3 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CApplicationModel_CIAppInfo3 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CApplicationModel_CIAppInfo3 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CApplicationModel_CIAppInfo3 *This,
        TrustLevel *trustLevel);

    /*** IAppInfo3 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_ExecutionContext)(
        __x_ABI_CWindows_CApplicationModel_CIAppInfo3 *This,
        __x_ABI_CWindows_CApplicationModel_CAppExecutionContext *value);

    END_INTERFACE
} __x_ABI_CWindows_CApplicationModel_CIAppInfo3Vtbl;

interface __x_ABI_CWindows_CApplicationModel_CIAppInfo3 {
    CONST_VTBL __x_ABI_CWindows_CApplicationModel_CIAppInfo3Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CApplicationModel_CIAppInfo3_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CApplicationModel_CIAppInfo3_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CApplicationModel_CIAppInfo3_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CApplicationModel_CIAppInfo3_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CApplicationModel_CIAppInfo3_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CApplicationModel_CIAppInfo3_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAppInfo3 methods ***/
#define __x_ABI_CWindows_CApplicationModel_CIAppInfo3_get_ExecutionContext(This,value) (This)->lpVtbl->get_ExecutionContext(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIAppInfo3_QueryInterface(__x_ABI_CWindows_CApplicationModel_CIAppInfo3* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CIAppInfo3_AddRef(__x_ABI_CWindows_CApplicationModel_CIAppInfo3* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CIAppInfo3_Release(__x_ABI_CWindows_CApplicationModel_CIAppInfo3* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIAppInfo3_GetIids(__x_ABI_CWindows_CApplicationModel_CIAppInfo3* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIAppInfo3_GetRuntimeClassName(__x_ABI_CWindows_CApplicationModel_CIAppInfo3* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIAppInfo3_GetTrustLevel(__x_ABI_CWindows_CApplicationModel_CIAppInfo3* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAppInfo3 methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIAppInfo3_get_ExecutionContext(__x_ABI_CWindows_CApplicationModel_CIAppInfo3* This,__x_ABI_CWindows_CApplicationModel_CAppExecutionContext *value) {
    return This->lpVtbl->get_ExecutionContext(This,value);
}
#endif
#ifdef WIDL_using_Windows_ApplicationModel
#define IID_IAppInfo3 IID___x_ABI_CWindows_CApplicationModel_CIAppInfo3
#define IAppInfo3Vtbl __x_ABI_CWindows_CApplicationModel_CIAppInfo3Vtbl
#define IAppInfo3 __x_ABI_CWindows_CApplicationModel_CIAppInfo3
#define IAppInfo3_QueryInterface __x_ABI_CWindows_CApplicationModel_CIAppInfo3_QueryInterface
#define IAppInfo3_AddRef __x_ABI_CWindows_CApplicationModel_CIAppInfo3_AddRef
#define IAppInfo3_Release __x_ABI_CWindows_CApplicationModel_CIAppInfo3_Release
#define IAppInfo3_GetIids __x_ABI_CWindows_CApplicationModel_CIAppInfo3_GetIids
#define IAppInfo3_GetRuntimeClassName __x_ABI_CWindows_CApplicationModel_CIAppInfo3_GetRuntimeClassName
#define IAppInfo3_GetTrustLevel __x_ABI_CWindows_CApplicationModel_CIAppInfo3_GetTrustLevel
#define IAppInfo3_get_ExecutionContext __x_ABI_CWindows_CApplicationModel_CIAppInfo3_get_ExecutionContext
#endif /* WIDL_using_Windows_ApplicationModel */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CApplicationModel_CIAppInfo3_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xb0000 */

/*****************************************************************************
 * IAppInfo4 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xb0000
#ifndef ____x_ABI_CWindows_CApplicationModel_CIAppInfo4_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CIAppInfo4_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CApplicationModel_CIAppInfo4, 0x2f34bdeb, 0x1609, 0x4554, 0x9f,0x33, 0x12,0xe1,0xe8,0x03,0xe0,0xd4);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            MIDL_INTERFACE("2f34bdeb-1609-4554-9f33-12e1e803e0d4")
            IAppInfo4 : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE get_SupportedFileExtensions(
                    UINT32 *value_size,
                    HSTRING **value) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CApplicationModel_CIAppInfo4, 0x2f34bdeb, 0x1609, 0x4554, 0x9f,0x33, 0x12,0xe1,0xe8,0x03,0xe0,0xd4)
#endif
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CIAppInfo4Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CApplicationModel_CIAppInfo4 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CApplicationModel_CIAppInfo4 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CApplicationModel_CIAppInfo4 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CApplicationModel_CIAppInfo4 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CApplicationModel_CIAppInfo4 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CApplicationModel_CIAppInfo4 *This,
        TrustLevel *trustLevel);

    /*** IAppInfo4 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_SupportedFileExtensions)(
        __x_ABI_CWindows_CApplicationModel_CIAppInfo4 *This,
        UINT32 *value_size,
        HSTRING **value);

    END_INTERFACE
} __x_ABI_CWindows_CApplicationModel_CIAppInfo4Vtbl;

interface __x_ABI_CWindows_CApplicationModel_CIAppInfo4 {
    CONST_VTBL __x_ABI_CWindows_CApplicationModel_CIAppInfo4Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CApplicationModel_CIAppInfo4_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CApplicationModel_CIAppInfo4_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CApplicationModel_CIAppInfo4_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CApplicationModel_CIAppInfo4_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CApplicationModel_CIAppInfo4_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CApplicationModel_CIAppInfo4_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAppInfo4 methods ***/
#define __x_ABI_CWindows_CApplicationModel_CIAppInfo4_get_SupportedFileExtensions(This,value_size,value) (This)->lpVtbl->get_SupportedFileExtensions(This,value_size,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIAppInfo4_QueryInterface(__x_ABI_CWindows_CApplicationModel_CIAppInfo4* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CIAppInfo4_AddRef(__x_ABI_CWindows_CApplicationModel_CIAppInfo4* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CIAppInfo4_Release(__x_ABI_CWindows_CApplicationModel_CIAppInfo4* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIAppInfo4_GetIids(__x_ABI_CWindows_CApplicationModel_CIAppInfo4* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIAppInfo4_GetRuntimeClassName(__x_ABI_CWindows_CApplicationModel_CIAppInfo4* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIAppInfo4_GetTrustLevel(__x_ABI_CWindows_CApplicationModel_CIAppInfo4* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAppInfo4 methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIAppInfo4_get_SupportedFileExtensions(__x_ABI_CWindows_CApplicationModel_CIAppInfo4* This,UINT32 *value_size,HSTRING **value) {
    return This->lpVtbl->get_SupportedFileExtensions(This,value_size,value);
}
#endif
#ifdef WIDL_using_Windows_ApplicationModel
#define IID_IAppInfo4 IID___x_ABI_CWindows_CApplicationModel_CIAppInfo4
#define IAppInfo4Vtbl __x_ABI_CWindows_CApplicationModel_CIAppInfo4Vtbl
#define IAppInfo4 __x_ABI_CWindows_CApplicationModel_CIAppInfo4
#define IAppInfo4_QueryInterface __x_ABI_CWindows_CApplicationModel_CIAppInfo4_QueryInterface
#define IAppInfo4_AddRef __x_ABI_CWindows_CApplicationModel_CIAppInfo4_AddRef
#define IAppInfo4_Release __x_ABI_CWindows_CApplicationModel_CIAppInfo4_Release
#define IAppInfo4_GetIids __x_ABI_CWindows_CApplicationModel_CIAppInfo4_GetIids
#define IAppInfo4_GetRuntimeClassName __x_ABI_CWindows_CApplicationModel_CIAppInfo4_GetRuntimeClassName
#define IAppInfo4_GetTrustLevel __x_ABI_CWindows_CApplicationModel_CIAppInfo4_GetTrustLevel
#define IAppInfo4_get_SupportedFileExtensions __x_ABI_CWindows_CApplicationModel_CIAppInfo4_get_SupportedFileExtensions
#endif /* WIDL_using_Windows_ApplicationModel */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CApplicationModel_CIAppInfo4_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xb0000 */

/*****************************************************************************
 * IAppInfoStatics interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xa0000
#ifndef ____x_ABI_CWindows_CApplicationModel_CIAppInfoStatics_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CIAppInfoStatics_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CApplicationModel_CIAppInfoStatics, 0xcf1f782a, 0xe48b, 0x4f0c, 0x9b,0x0b, 0x79,0xc3,0xf8,0x95,0x7d,0xd7);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            MIDL_INTERFACE("cf1f782a-e48b-4f0c-9b0b-79c3f8957dd7")
            IAppInfoStatics : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE get_Current(
                    ABI::Windows::ApplicationModel::IAppInfo **value) = 0;

                virtual HRESULT STDMETHODCALLTYPE GetFromAppUserModelId(
                    HSTRING app_user_model_id,
                    ABI::Windows::ApplicationModel::IAppInfo **result) = 0;

                virtual HRESULT STDMETHODCALLTYPE GetFromAppUserModelIdForUser(
                    ABI::Windows::System::IUser *user,
                    HSTRING app_user_model_id,
                    ABI::Windows::ApplicationModel::IAppInfo **result) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CApplicationModel_CIAppInfoStatics, 0xcf1f782a, 0xe48b, 0x4f0c, 0x9b,0x0b, 0x79,0xc3,0xf8,0x95,0x7d,0xd7)
#endif
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CIAppInfoStaticsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CApplicationModel_CIAppInfoStatics *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CApplicationModel_CIAppInfoStatics *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CApplicationModel_CIAppInfoStatics *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CApplicationModel_CIAppInfoStatics *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CApplicationModel_CIAppInfoStatics *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CApplicationModel_CIAppInfoStatics *This,
        TrustLevel *trustLevel);

    /*** IAppInfoStatics methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Current)(
        __x_ABI_CWindows_CApplicationModel_CIAppInfoStatics *This,
        __x_ABI_CWindows_CApplicationModel_CIAppInfo **value);

    HRESULT (STDMETHODCALLTYPE *GetFromAppUserModelId)(
        __x_ABI_CWindows_CApplicationModel_CIAppInfoStatics *This,
        HSTRING app_user_model_id,
        __x_ABI_CWindows_CApplicationModel_CIAppInfo **result);

    HRESULT (STDMETHODCALLTYPE *GetFromAppUserModelIdForUser)(
        __x_ABI_CWindows_CApplicationModel_CIAppInfoStatics *This,
        __x_ABI_CWindows_CSystem_CIUser *user,
        HSTRING app_user_model_id,
        __x_ABI_CWindows_CApplicationModel_CIAppInfo **result);

    END_INTERFACE
} __x_ABI_CWindows_CApplicationModel_CIAppInfoStaticsVtbl;

interface __x_ABI_CWindows_CApplicationModel_CIAppInfoStatics {
    CONST_VTBL __x_ABI_CWindows_CApplicationModel_CIAppInfoStaticsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CApplicationModel_CIAppInfoStatics_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CApplicationModel_CIAppInfoStatics_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CApplicationModel_CIAppInfoStatics_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CApplicationModel_CIAppInfoStatics_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CApplicationModel_CIAppInfoStatics_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CApplicationModel_CIAppInfoStatics_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAppInfoStatics methods ***/
#define __x_ABI_CWindows_CApplicationModel_CIAppInfoStatics_get_Current(This,value) (This)->lpVtbl->get_Current(This,value)
#define __x_ABI_CWindows_CApplicationModel_CIAppInfoStatics_GetFromAppUserModelId(This,app_user_model_id,result) (This)->lpVtbl->GetFromAppUserModelId(This,app_user_model_id,result)
#define __x_ABI_CWindows_CApplicationModel_CIAppInfoStatics_GetFromAppUserModelIdForUser(This,user,app_user_model_id,result) (This)->lpVtbl->GetFromAppUserModelIdForUser(This,user,app_user_model_id,result)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIAppInfoStatics_QueryInterface(__x_ABI_CWindows_CApplicationModel_CIAppInfoStatics* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CIAppInfoStatics_AddRef(__x_ABI_CWindows_CApplicationModel_CIAppInfoStatics* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CIAppInfoStatics_Release(__x_ABI_CWindows_CApplicationModel_CIAppInfoStatics* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIAppInfoStatics_GetIids(__x_ABI_CWindows_CApplicationModel_CIAppInfoStatics* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIAppInfoStatics_GetRuntimeClassName(__x_ABI_CWindows_CApplicationModel_CIAppInfoStatics* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIAppInfoStatics_GetTrustLevel(__x_ABI_CWindows_CApplicationModel_CIAppInfoStatics* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAppInfoStatics methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIAppInfoStatics_get_Current(__x_ABI_CWindows_CApplicationModel_CIAppInfoStatics* This,__x_ABI_CWindows_CApplicationModel_CIAppInfo **value) {
    return This->lpVtbl->get_Current(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIAppInfoStatics_GetFromAppUserModelId(__x_ABI_CWindows_CApplicationModel_CIAppInfoStatics* This,HSTRING app_user_model_id,__x_ABI_CWindows_CApplicationModel_CIAppInfo **result) {
    return This->lpVtbl->GetFromAppUserModelId(This,app_user_model_id,result);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIAppInfoStatics_GetFromAppUserModelIdForUser(__x_ABI_CWindows_CApplicationModel_CIAppInfoStatics* This,__x_ABI_CWindows_CSystem_CIUser *user,HSTRING app_user_model_id,__x_ABI_CWindows_CApplicationModel_CIAppInfo **result) {
    return This->lpVtbl->GetFromAppUserModelIdForUser(This,user,app_user_model_id,result);
}
#endif
#ifdef WIDL_using_Windows_ApplicationModel
#define IID_IAppInfoStatics IID___x_ABI_CWindows_CApplicationModel_CIAppInfoStatics
#define IAppInfoStaticsVtbl __x_ABI_CWindows_CApplicationModel_CIAppInfoStaticsVtbl
#define IAppInfoStatics __x_ABI_CWindows_CApplicationModel_CIAppInfoStatics
#define IAppInfoStatics_QueryInterface __x_ABI_CWindows_CApplicationModel_CIAppInfoStatics_QueryInterface
#define IAppInfoStatics_AddRef __x_ABI_CWindows_CApplicationModel_CIAppInfoStatics_AddRef
#define IAppInfoStatics_Release __x_ABI_CWindows_CApplicationModel_CIAppInfoStatics_Release
#define IAppInfoStatics_GetIids __x_ABI_CWindows_CApplicationModel_CIAppInfoStatics_GetIids
#define IAppInfoStatics_GetRuntimeClassName __x_ABI_CWindows_CApplicationModel_CIAppInfoStatics_GetRuntimeClassName
#define IAppInfoStatics_GetTrustLevel __x_ABI_CWindows_CApplicationModel_CIAppInfoStatics_GetTrustLevel
#define IAppInfoStatics_get_Current __x_ABI_CWindows_CApplicationModel_CIAppInfoStatics_get_Current
#define IAppInfoStatics_GetFromAppUserModelId __x_ABI_CWindows_CApplicationModel_CIAppInfoStatics_GetFromAppUserModelId
#define IAppInfoStatics_GetFromAppUserModelIdForUser __x_ABI_CWindows_CApplicationModel_CIAppInfoStatics_GetFromAppUserModelIdForUser
#endif /* WIDL_using_Windows_ApplicationModel */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CApplicationModel_CIAppInfoStatics_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xa0000 */

/*****************************************************************************
 * IDesignModeStatics interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CApplicationModel_CIDesignModeStatics_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CIDesignModeStatics_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CApplicationModel_CIDesignModeStatics, 0x2c3893cc, 0xf81a, 0x4e7a, 0xb8,0x57, 0x76,0xa8,0x08,0x87,0xe1,0x85);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            MIDL_INTERFACE("2c3893cc-f81a-4e7a-b857-76a80887e185")
            IDesignModeStatics : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE get_DesignModeEnabled(
                    boolean *value) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CApplicationModel_CIDesignModeStatics, 0x2c3893cc, 0xf81a, 0x4e7a, 0xb8,0x57, 0x76,0xa8,0x08,0x87,0xe1,0x85)
#endif
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CIDesignModeStaticsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics *This,
        TrustLevel *trustLevel);

    /*** IDesignModeStatics methods ***/
    HRESULT (STDMETHODCALLTYPE *get_DesignModeEnabled)(
        __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics *This,
        boolean *value);

    END_INTERFACE
} __x_ABI_CWindows_CApplicationModel_CIDesignModeStaticsVtbl;

interface __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics {
    CONST_VTBL __x_ABI_CWindows_CApplicationModel_CIDesignModeStaticsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IDesignModeStatics methods ***/
#define __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics_get_DesignModeEnabled(This,value) (This)->lpVtbl->get_DesignModeEnabled(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics_QueryInterface(__x_ABI_CWindows_CApplicationModel_CIDesignModeStatics* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics_AddRef(__x_ABI_CWindows_CApplicationModel_CIDesignModeStatics* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics_Release(__x_ABI_CWindows_CApplicationModel_CIDesignModeStatics* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics_GetIids(__x_ABI_CWindows_CApplicationModel_CIDesignModeStatics* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics_GetRuntimeClassName(__x_ABI_CWindows_CApplicationModel_CIDesignModeStatics* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics_GetTrustLevel(__x_ABI_CWindows_CApplicationModel_CIDesignModeStatics* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IDesignModeStatics methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics_get_DesignModeEnabled(__x_ABI_CWindows_CApplicationModel_CIDesignModeStatics* This,boolean *value) {
    return This->lpVtbl->get_DesignModeEnabled(This,value);
}
#endif
#ifdef WIDL_using_Windows_ApplicationModel
#define IID_IDesignModeStatics IID___x_ABI_CWindows_CApplicationModel_CIDesignModeStatics
#define IDesignModeStaticsVtbl __x_ABI_CWindows_CApplicationModel_CIDesignModeStaticsVtbl
#define IDesignModeStatics __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics
#define IDesignModeStatics_QueryInterface __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics_QueryInterface
#define IDesignModeStatics_AddRef __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics_AddRef
#define IDesignModeStatics_Release __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics_Release
#define IDesignModeStatics_GetIids __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics_GetIids
#define IDesignModeStatics_GetRuntimeClassName __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics_GetRuntimeClassName
#define IDesignModeStatics_GetTrustLevel __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics_GetTrustLevel
#define IDesignModeStatics_get_DesignModeEnabled __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics_get_DesignModeEnabled
#endif /* WIDL_using_Windows_ApplicationModel */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CApplicationModel_CIDesignModeStatics_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IDesignModeStatics2 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000
#ifndef ____x_ABI_CWindows_CApplicationModel_CIDesignModeStatics2_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CIDesignModeStatics2_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CApplicationModel_CIDesignModeStatics2, 0x80cf8137, 0xb064, 0x4858, 0xbe,0xc8, 0x3e,0xba,0x22,0x35,0x75,0x35);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            MIDL_INTERFACE("80cf8137-b064-4858-bec8-3eba22357535")
            IDesignModeStatics2 : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE get_DesignMode2Enabled(
                    boolean *value) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CApplicationModel_CIDesignModeStatics2, 0x80cf8137, 0xb064, 0x4858, 0xbe,0xc8, 0x3e,0xba,0x22,0x35,0x75,0x35)
#endif
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics2 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics2 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics2 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics2 *This,
        TrustLevel *trustLevel);

    /*** IDesignModeStatics2 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_DesignMode2Enabled)(
        __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics2 *This,
        boolean *value);

    END_INTERFACE
} __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics2Vtbl;

interface __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics2 {
    CONST_VTBL __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics2_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics2_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics2_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics2_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IDesignModeStatics2 methods ***/
#define __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics2_get_DesignMode2Enabled(This,value) (This)->lpVtbl->get_DesignMode2Enabled(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics2_QueryInterface(__x_ABI_CWindows_CApplicationModel_CIDesignModeStatics2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics2_AddRef(__x_ABI_CWindows_CApplicationModel_CIDesignModeStatics2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics2_Release(__x_ABI_CWindows_CApplicationModel_CIDesignModeStatics2* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics2_GetIids(__x_ABI_CWindows_CApplicationModel_CIDesignModeStatics2* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics2_GetRuntimeClassName(__x_ABI_CWindows_CApplicationModel_CIDesignModeStatics2* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics2_GetTrustLevel(__x_ABI_CWindows_CApplicationModel_CIDesignModeStatics2* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IDesignModeStatics2 methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics2_get_DesignMode2Enabled(__x_ABI_CWindows_CApplicationModel_CIDesignModeStatics2* This,boolean *value) {
    return This->lpVtbl->get_DesignMode2Enabled(This,value);
}
#endif
#ifdef WIDL_using_Windows_ApplicationModel
#define IID_IDesignModeStatics2 IID___x_ABI_CWindows_CApplicationModel_CIDesignModeStatics2
#define IDesignModeStatics2Vtbl __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics2Vtbl
#define IDesignModeStatics2 __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics2
#define IDesignModeStatics2_QueryInterface __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics2_QueryInterface
#define IDesignModeStatics2_AddRef __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics2_AddRef
#define IDesignModeStatics2_Release __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics2_Release
#define IDesignModeStatics2_GetIids __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics2_GetIids
#define IDesignModeStatics2_GetRuntimeClassName __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics2_GetRuntimeClassName
#define IDesignModeStatics2_GetTrustLevel __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics2_GetTrustLevel
#define IDesignModeStatics2_get_DesignMode2Enabled __x_ABI_CWindows_CApplicationModel_CIDesignModeStatics2_get_DesignMode2Enabled
#endif /* WIDL_using_Windows_ApplicationModel */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CApplicationModel_CIDesignModeStatics2_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000 */

/*****************************************************************************
 * IEnteredBackgroundEventArgs interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
#ifndef ____x_ABI_CWindows_CApplicationModel_CIEnteredBackgroundEventArgs_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CIEnteredBackgroundEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CApplicationModel_CIEnteredBackgroundEventArgs, 0xf722dcc2, 0x9827, 0x403d, 0xaa,0xed, 0xec,0xca,0x9a,0xc1,0x73,0x98);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            MIDL_INTERFACE("f722dcc2-9827-403d-aaed-ecca9ac17398")
            IEnteredBackgroundEventArgs : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE GetDeferral(
                    ABI::Windows::Foundation::IDeferral **value) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CApplicationModel_CIEnteredBackgroundEventArgs, 0xf722dcc2, 0x9827, 0x403d, 0xaa,0xed, 0xec,0xca,0x9a,0xc1,0x73,0x98)
#endif
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CIEnteredBackgroundEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CApplicationModel_CIEnteredBackgroundEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CApplicationModel_CIEnteredBackgroundEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CApplicationModel_CIEnteredBackgroundEventArgs *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CApplicationModel_CIEnteredBackgroundEventArgs *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CApplicationModel_CIEnteredBackgroundEventArgs *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CApplicationModel_CIEnteredBackgroundEventArgs *This,
        TrustLevel *trustLevel);

    /*** IEnteredBackgroundEventArgs methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDeferral)(
        __x_ABI_CWindows_CApplicationModel_CIEnteredBackgroundEventArgs *This,
        __x_ABI_CWindows_CFoundation_CIDeferral **value);

    END_INTERFACE
} __x_ABI_CWindows_CApplicationModel_CIEnteredBackgroundEventArgsVtbl;

interface __x_ABI_CWindows_CApplicationModel_CIEnteredBackgroundEventArgs {
    CONST_VTBL __x_ABI_CWindows_CApplicationModel_CIEnteredBackgroundEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CApplicationModel_CIEnteredBackgroundEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CApplicationModel_CIEnteredBackgroundEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CApplicationModel_CIEnteredBackgroundEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CApplicationModel_CIEnteredBackgroundEventArgs_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CApplicationModel_CIEnteredBackgroundEventArgs_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CApplicationModel_CIEnteredBackgroundEventArgs_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IEnteredBackgroundEventArgs methods ***/
#define __x_ABI_CWindows_CApplicationModel_CIEnteredBackgroundEventArgs_GetDeferral(This,value) (This)->lpVtbl->GetDeferral(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIEnteredBackgroundEventArgs_QueryInterface(__x_ABI_CWindows_CApplicationModel_CIEnteredBackgroundEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CIEnteredBackgroundEventArgs_AddRef(__x_ABI_CWindows_CApplicationModel_CIEnteredBackgroundEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CIEnteredBackgroundEventArgs_Release(__x_ABI_CWindows_CApplicationModel_CIEnteredBackgroundEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIEnteredBackgroundEventArgs_GetIids(__x_ABI_CWindows_CApplicationModel_CIEnteredBackgroundEventArgs* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIEnteredBackgroundEventArgs_GetRuntimeClassName(__x_ABI_CWindows_CApplicationModel_CIEnteredBackgroundEventArgs* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIEnteredBackgroundEventArgs_GetTrustLevel(__x_ABI_CWindows_CApplicationModel_CIEnteredBackgroundEventArgs* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IEnteredBackgroundEventArgs methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIEnteredBackgroundEventArgs_GetDeferral(__x_ABI_CWindows_CApplicationModel_CIEnteredBackgroundEventArgs* This,__x_ABI_CWindows_CFoundation_CIDeferral **value) {
    return This->lpVtbl->GetDeferral(This,value);
}
#endif
#ifdef WIDL_using_Windows_ApplicationModel
#define IID_IEnteredBackgroundEventArgs IID___x_ABI_CWindows_CApplicationModel_CIEnteredBackgroundEventArgs
#define IEnteredBackgroundEventArgsVtbl __x_ABI_CWindows_CApplicationModel_CIEnteredBackgroundEventArgsVtbl
#define IEnteredBackgroundEventArgs __x_ABI_CWindows_CApplicationModel_CIEnteredBackgroundEventArgs
#define IEnteredBackgroundEventArgs_QueryInterface __x_ABI_CWindows_CApplicationModel_CIEnteredBackgroundEventArgs_QueryInterface
#define IEnteredBackgroundEventArgs_AddRef __x_ABI_CWindows_CApplicationModel_CIEnteredBackgroundEventArgs_AddRef
#define IEnteredBackgroundEventArgs_Release __x_ABI_CWindows_CApplicationModel_CIEnteredBackgroundEventArgs_Release
#define IEnteredBackgroundEventArgs_GetIids __x_ABI_CWindows_CApplicationModel_CIEnteredBackgroundEventArgs_GetIids
#define IEnteredBackgroundEventArgs_GetRuntimeClassName __x_ABI_CWindows_CApplicationModel_CIEnteredBackgroundEventArgs_GetRuntimeClassName
#define IEnteredBackgroundEventArgs_GetTrustLevel __x_ABI_CWindows_CApplicationModel_CIEnteredBackgroundEventArgs_GetTrustLevel
#define IEnteredBackgroundEventArgs_GetDeferral __x_ABI_CWindows_CApplicationModel_CIEnteredBackgroundEventArgs_GetDeferral
#endif /* WIDL_using_Windows_ApplicationModel */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CApplicationModel_CIEnteredBackgroundEventArgs_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */

/*****************************************************************************
 * ILeavingBackgroundEventArgs interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
#ifndef ____x_ABI_CWindows_CApplicationModel_CILeavingBackgroundEventArgs_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CILeavingBackgroundEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CApplicationModel_CILeavingBackgroundEventArgs, 0x39c6ec9a, 0xae6e, 0x46f9, 0xa0,0x7a, 0xcf,0xc2,0x3f,0x88,0x73,0x3e);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            MIDL_INTERFACE("39c6ec9a-ae6e-46f9-a07a-cfc23f88733e")
            ILeavingBackgroundEventArgs : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE GetDeferral(
                    ABI::Windows::Foundation::IDeferral **value) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CApplicationModel_CILeavingBackgroundEventArgs, 0x39c6ec9a, 0xae6e, 0x46f9, 0xa0,0x7a, 0xcf,0xc2,0x3f,0x88,0x73,0x3e)
#endif
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CILeavingBackgroundEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CApplicationModel_CILeavingBackgroundEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CApplicationModel_CILeavingBackgroundEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CApplicationModel_CILeavingBackgroundEventArgs *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CApplicationModel_CILeavingBackgroundEventArgs *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CApplicationModel_CILeavingBackgroundEventArgs *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CApplicationModel_CILeavingBackgroundEventArgs *This,
        TrustLevel *trustLevel);

    /*** ILeavingBackgroundEventArgs methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDeferral)(
        __x_ABI_CWindows_CApplicationModel_CILeavingBackgroundEventArgs *This,
        __x_ABI_CWindows_CFoundation_CIDeferral **value);

    END_INTERFACE
} __x_ABI_CWindows_CApplicationModel_CILeavingBackgroundEventArgsVtbl;

interface __x_ABI_CWindows_CApplicationModel_CILeavingBackgroundEventArgs {
    CONST_VTBL __x_ABI_CWindows_CApplicationModel_CILeavingBackgroundEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CApplicationModel_CILeavingBackgroundEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CApplicationModel_CILeavingBackgroundEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CApplicationModel_CILeavingBackgroundEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CApplicationModel_CILeavingBackgroundEventArgs_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CApplicationModel_CILeavingBackgroundEventArgs_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CApplicationModel_CILeavingBackgroundEventArgs_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ILeavingBackgroundEventArgs methods ***/
#define __x_ABI_CWindows_CApplicationModel_CILeavingBackgroundEventArgs_GetDeferral(This,value) (This)->lpVtbl->GetDeferral(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CILeavingBackgroundEventArgs_QueryInterface(__x_ABI_CWindows_CApplicationModel_CILeavingBackgroundEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CILeavingBackgroundEventArgs_AddRef(__x_ABI_CWindows_CApplicationModel_CILeavingBackgroundEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CILeavingBackgroundEventArgs_Release(__x_ABI_CWindows_CApplicationModel_CILeavingBackgroundEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CILeavingBackgroundEventArgs_GetIids(__x_ABI_CWindows_CApplicationModel_CILeavingBackgroundEventArgs* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CILeavingBackgroundEventArgs_GetRuntimeClassName(__x_ABI_CWindows_CApplicationModel_CILeavingBackgroundEventArgs* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CILeavingBackgroundEventArgs_GetTrustLevel(__x_ABI_CWindows_CApplicationModel_CILeavingBackgroundEventArgs* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ILeavingBackgroundEventArgs methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CILeavingBackgroundEventArgs_GetDeferral(__x_ABI_CWindows_CApplicationModel_CILeavingBackgroundEventArgs* This,__x_ABI_CWindows_CFoundation_CIDeferral **value) {
    return This->lpVtbl->GetDeferral(This,value);
}
#endif
#ifdef WIDL_using_Windows_ApplicationModel
#define IID_ILeavingBackgroundEventArgs IID___x_ABI_CWindows_CApplicationModel_CILeavingBackgroundEventArgs
#define ILeavingBackgroundEventArgsVtbl __x_ABI_CWindows_CApplicationModel_CILeavingBackgroundEventArgsVtbl
#define ILeavingBackgroundEventArgs __x_ABI_CWindows_CApplicationModel_CILeavingBackgroundEventArgs
#define ILeavingBackgroundEventArgs_QueryInterface __x_ABI_CWindows_CApplicationModel_CILeavingBackgroundEventArgs_QueryInterface
#define ILeavingBackgroundEventArgs_AddRef __x_ABI_CWindows_CApplicationModel_CILeavingBackgroundEventArgs_AddRef
#define ILeavingBackgroundEventArgs_Release __x_ABI_CWindows_CApplicationModel_CILeavingBackgroundEventArgs_Release
#define ILeavingBackgroundEventArgs_GetIids __x_ABI_CWindows_CApplicationModel_CILeavingBackgroundEventArgs_GetIids
#define ILeavingBackgroundEventArgs_GetRuntimeClassName __x_ABI_CWindows_CApplicationModel_CILeavingBackgroundEventArgs_GetRuntimeClassName
#define ILeavingBackgroundEventArgs_GetTrustLevel __x_ABI_CWindows_CApplicationModel_CILeavingBackgroundEventArgs_GetTrustLevel
#define ILeavingBackgroundEventArgs_GetDeferral __x_ABI_CWindows_CApplicationModel_CILeavingBackgroundEventArgs_GetDeferral
#endif /* WIDL_using_Windows_ApplicationModel */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CApplicationModel_CILeavingBackgroundEventArgs_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */

/*****************************************************************************
 * IPackage interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CApplicationModel_CIPackage_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CIPackage_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CApplicationModel_CIPackage, 0x163c792f, 0xbd75, 0x413c, 0xbf,0x23, 0xb1,0xfe,0x7b,0x95,0xd8,0x25);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            MIDL_INTERFACE("163c792f-bd75-413c-bf23-b1fe7b95d825")
            IPackage : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE get_Id(
                    ABI::Windows::ApplicationModel::IPackageId **value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_InstalledLocation(
                    ABI::Windows::Storage::IStorageFolder **value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_IsFramework(
                    boolean *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Dependencies(
                    ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::ApplicationModel::Package* > **value) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CApplicationModel_CIPackage, 0x163c792f, 0xbd75, 0x413c, 0xbf,0x23, 0xb1,0xfe,0x7b,0x95,0xd8,0x25)
#endif
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CIPackageVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CApplicationModel_CIPackage *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CApplicationModel_CIPackage *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CApplicationModel_CIPackage *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CApplicationModel_CIPackage *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CApplicationModel_CIPackage *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CApplicationModel_CIPackage *This,
        TrustLevel *trustLevel);

    /*** IPackage methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Id)(
        __x_ABI_CWindows_CApplicationModel_CIPackage *This,
        __x_ABI_CWindows_CApplicationModel_CIPackageId **value);

    HRESULT (STDMETHODCALLTYPE *get_InstalledLocation)(
        __x_ABI_CWindows_CApplicationModel_CIPackage *This,
        __x_ABI_CWindows_CStorage_CIStorageFolder **value);

    HRESULT (STDMETHODCALLTYPE *get_IsFramework)(
        __x_ABI_CWindows_CApplicationModel_CIPackage *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *get_Dependencies)(
        __x_ABI_CWindows_CApplicationModel_CIPackage *This,
        __FIVectorView_1_Windows__CApplicationModel__CPackage **value);

    END_INTERFACE
} __x_ABI_CWindows_CApplicationModel_CIPackageVtbl;

interface __x_ABI_CWindows_CApplicationModel_CIPackage {
    CONST_VTBL __x_ABI_CWindows_CApplicationModel_CIPackageVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CApplicationModel_CIPackage_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CApplicationModel_CIPackage_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CApplicationModel_CIPackage_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CApplicationModel_CIPackage_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CApplicationModel_CIPackage_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CApplicationModel_CIPackage_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IPackage methods ***/
#define __x_ABI_CWindows_CApplicationModel_CIPackage_get_Id(This,value) (This)->lpVtbl->get_Id(This,value)
#define __x_ABI_CWindows_CApplicationModel_CIPackage_get_InstalledLocation(This,value) (This)->lpVtbl->get_InstalledLocation(This,value)
#define __x_ABI_CWindows_CApplicationModel_CIPackage_get_IsFramework(This,value) (This)->lpVtbl->get_IsFramework(This,value)
#define __x_ABI_CWindows_CApplicationModel_CIPackage_get_Dependencies(This,value) (This)->lpVtbl->get_Dependencies(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIPackage_QueryInterface(__x_ABI_CWindows_CApplicationModel_CIPackage* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CIPackage_AddRef(__x_ABI_CWindows_CApplicationModel_CIPackage* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CIPackage_Release(__x_ABI_CWindows_CApplicationModel_CIPackage* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIPackage_GetIids(__x_ABI_CWindows_CApplicationModel_CIPackage* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIPackage_GetRuntimeClassName(__x_ABI_CWindows_CApplicationModel_CIPackage* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIPackage_GetTrustLevel(__x_ABI_CWindows_CApplicationModel_CIPackage* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IPackage methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIPackage_get_Id(__x_ABI_CWindows_CApplicationModel_CIPackage* This,__x_ABI_CWindows_CApplicationModel_CIPackageId **value) {
    return This->lpVtbl->get_Id(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIPackage_get_InstalledLocation(__x_ABI_CWindows_CApplicationModel_CIPackage* This,__x_ABI_CWindows_CStorage_CIStorageFolder **value) {
    return This->lpVtbl->get_InstalledLocation(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIPackage_get_IsFramework(__x_ABI_CWindows_CApplicationModel_CIPackage* This,boolean *value) {
    return This->lpVtbl->get_IsFramework(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIPackage_get_Dependencies(__x_ABI_CWindows_CApplicationModel_CIPackage* This,__FIVectorView_1_Windows__CApplicationModel__CPackage **value) {
    return This->lpVtbl->get_Dependencies(This,value);
}
#endif
#ifdef WIDL_using_Windows_ApplicationModel
#define IID_IPackage IID___x_ABI_CWindows_CApplicationModel_CIPackage
#define IPackageVtbl __x_ABI_CWindows_CApplicationModel_CIPackageVtbl
#define IPackage __x_ABI_CWindows_CApplicationModel_CIPackage
#define IPackage_QueryInterface __x_ABI_CWindows_CApplicationModel_CIPackage_QueryInterface
#define IPackage_AddRef __x_ABI_CWindows_CApplicationModel_CIPackage_AddRef
#define IPackage_Release __x_ABI_CWindows_CApplicationModel_CIPackage_Release
#define IPackage_GetIids __x_ABI_CWindows_CApplicationModel_CIPackage_GetIids
#define IPackage_GetRuntimeClassName __x_ABI_CWindows_CApplicationModel_CIPackage_GetRuntimeClassName
#define IPackage_GetTrustLevel __x_ABI_CWindows_CApplicationModel_CIPackage_GetTrustLevel
#define IPackage_get_Id __x_ABI_CWindows_CApplicationModel_CIPackage_get_Id
#define IPackage_get_InstalledLocation __x_ABI_CWindows_CApplicationModel_CIPackage_get_InstalledLocation
#define IPackage_get_IsFramework __x_ABI_CWindows_CApplicationModel_CIPackage_get_IsFramework
#define IPackage_get_Dependencies __x_ABI_CWindows_CApplicationModel_CIPackage_get_Dependencies
#endif /* WIDL_using_Windows_ApplicationModel */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CApplicationModel_CIPackage_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IPackage2 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CApplicationModel_CIPackage2_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CIPackage2_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CApplicationModel_CIPackage2, 0xa6612fb6, 0x7688, 0x4ace, 0x95,0xfb, 0x35,0x95,0x38,0xe7,0xaa,0x01);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            MIDL_INTERFACE("a6612fb6-7688-4ace-95fb-359538e7aa01")
            IPackage2 : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE get_DisplayName(
                    HSTRING *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_PublisherDisplayName(
                    HSTRING *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Description(
                    HSTRING *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Logo(
                    ABI::Windows::Foundation::IUriRuntimeClass **value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_IsResourcePackage(
                    boolean *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_IsBundle(
                    boolean *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_IsDevelopmentMode(
                    boolean *value) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CApplicationModel_CIPackage2, 0xa6612fb6, 0x7688, 0x4ace, 0x95,0xfb, 0x35,0x95,0x38,0xe7,0xaa,0x01)
#endif
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CIPackage2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CApplicationModel_CIPackage2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CApplicationModel_CIPackage2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CApplicationModel_CIPackage2 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CApplicationModel_CIPackage2 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CApplicationModel_CIPackage2 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CApplicationModel_CIPackage2 *This,
        TrustLevel *trustLevel);

    /*** IPackage2 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_DisplayName)(
        __x_ABI_CWindows_CApplicationModel_CIPackage2 *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_PublisherDisplayName)(
        __x_ABI_CWindows_CApplicationModel_CIPackage2 *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_Description)(
        __x_ABI_CWindows_CApplicationModel_CIPackage2 *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_Logo)(
        __x_ABI_CWindows_CApplicationModel_CIPackage2 *This,
        __x_ABI_CWindows_CFoundation_CIUriRuntimeClass **value);

    HRESULT (STDMETHODCALLTYPE *get_IsResourcePackage)(
        __x_ABI_CWindows_CApplicationModel_CIPackage2 *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *get_IsBundle)(
        __x_ABI_CWindows_CApplicationModel_CIPackage2 *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *get_IsDevelopmentMode)(
        __x_ABI_CWindows_CApplicationModel_CIPackage2 *This,
        boolean *value);

    END_INTERFACE
} __x_ABI_CWindows_CApplicationModel_CIPackage2Vtbl;

interface __x_ABI_CWindows_CApplicationModel_CIPackage2 {
    CONST_VTBL __x_ABI_CWindows_CApplicationModel_CIPackage2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CApplicationModel_CIPackage2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CApplicationModel_CIPackage2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CApplicationModel_CIPackage2_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CApplicationModel_CIPackage2_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CApplicationModel_CIPackage2_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CApplicationModel_CIPackage2_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IPackage2 methods ***/
#define __x_ABI_CWindows_CApplicationModel_CIPackage2_get_DisplayName(This,value) (This)->lpVtbl->get_DisplayName(This,value)
#define __x_ABI_CWindows_CApplicationModel_CIPackage2_get_PublisherDisplayName(This,value) (This)->lpVtbl->get_PublisherDisplayName(This,value)
#define __x_ABI_CWindows_CApplicationModel_CIPackage2_get_Description(This,value) (This)->lpVtbl->get_Description(This,value)
#define __x_ABI_CWindows_CApplicationModel_CIPackage2_get_Logo(This,value) (This)->lpVtbl->get_Logo(This,value)
#define __x_ABI_CWindows_CApplicationModel_CIPackage2_get_IsResourcePackage(This,value) (This)->lpVtbl->get_IsResourcePackage(This,value)
#define __x_ABI_CWindows_CApplicationModel_CIPackage2_get_IsBundle(This,value) (This)->lpVtbl->get_IsBundle(This,value)
#define __x_ABI_CWindows_CApplicationModel_CIPackage2_get_IsDevelopmentMode(This,value) (This)->lpVtbl->get_IsDevelopmentMode(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIPackage2_QueryInterface(__x_ABI_CWindows_CApplicationModel_CIPackage2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CIPackage2_AddRef(__x_ABI_CWindows_CApplicationModel_CIPackage2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CIPackage2_Release(__x_ABI_CWindows_CApplicationModel_CIPackage2* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIPackage2_GetIids(__x_ABI_CWindows_CApplicationModel_CIPackage2* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIPackage2_GetRuntimeClassName(__x_ABI_CWindows_CApplicationModel_CIPackage2* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIPackage2_GetTrustLevel(__x_ABI_CWindows_CApplicationModel_CIPackage2* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IPackage2 methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIPackage2_get_DisplayName(__x_ABI_CWindows_CApplicationModel_CIPackage2* This,HSTRING *value) {
    return This->lpVtbl->get_DisplayName(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIPackage2_get_PublisherDisplayName(__x_ABI_CWindows_CApplicationModel_CIPackage2* This,HSTRING *value) {
    return This->lpVtbl->get_PublisherDisplayName(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIPackage2_get_Description(__x_ABI_CWindows_CApplicationModel_CIPackage2* This,HSTRING *value) {
    return This->lpVtbl->get_Description(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIPackage2_get_Logo(__x_ABI_CWindows_CApplicationModel_CIPackage2* This,__x_ABI_CWindows_CFoundation_CIUriRuntimeClass **value) {
    return This->lpVtbl->get_Logo(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIPackage2_get_IsResourcePackage(__x_ABI_CWindows_CApplicationModel_CIPackage2* This,boolean *value) {
    return This->lpVtbl->get_IsResourcePackage(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIPackage2_get_IsBundle(__x_ABI_CWindows_CApplicationModel_CIPackage2* This,boolean *value) {
    return This->lpVtbl->get_IsBundle(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIPackage2_get_IsDevelopmentMode(__x_ABI_CWindows_CApplicationModel_CIPackage2* This,boolean *value) {
    return This->lpVtbl->get_IsDevelopmentMode(This,value);
}
#endif
#ifdef WIDL_using_Windows_ApplicationModel
#define IID_IPackage2 IID___x_ABI_CWindows_CApplicationModel_CIPackage2
#define IPackage2Vtbl __x_ABI_CWindows_CApplicationModel_CIPackage2Vtbl
#define IPackage2 __x_ABI_CWindows_CApplicationModel_CIPackage2
#define IPackage2_QueryInterface __x_ABI_CWindows_CApplicationModel_CIPackage2_QueryInterface
#define IPackage2_AddRef __x_ABI_CWindows_CApplicationModel_CIPackage2_AddRef
#define IPackage2_Release __x_ABI_CWindows_CApplicationModel_CIPackage2_Release
#define IPackage2_GetIids __x_ABI_CWindows_CApplicationModel_CIPackage2_GetIids
#define IPackage2_GetRuntimeClassName __x_ABI_CWindows_CApplicationModel_CIPackage2_GetRuntimeClassName
#define IPackage2_GetTrustLevel __x_ABI_CWindows_CApplicationModel_CIPackage2_GetTrustLevel
#define IPackage2_get_DisplayName __x_ABI_CWindows_CApplicationModel_CIPackage2_get_DisplayName
#define IPackage2_get_PublisherDisplayName __x_ABI_CWindows_CApplicationModel_CIPackage2_get_PublisherDisplayName
#define IPackage2_get_Description __x_ABI_CWindows_CApplicationModel_CIPackage2_get_Description
#define IPackage2_get_Logo __x_ABI_CWindows_CApplicationModel_CIPackage2_get_Logo
#define IPackage2_get_IsResourcePackage __x_ABI_CWindows_CApplicationModel_CIPackage2_get_IsResourcePackage
#define IPackage2_get_IsBundle __x_ABI_CWindows_CApplicationModel_CIPackage2_get_IsBundle
#define IPackage2_get_IsDevelopmentMode __x_ABI_CWindows_CApplicationModel_CIPackage2_get_IsDevelopmentMode
#endif /* WIDL_using_Windows_ApplicationModel */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CApplicationModel_CIPackage2_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IPackage3 interface
 */
#ifndef ____x_ABI_CWindows_CApplicationModel_CIPackage3_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CIPackage3_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CApplicationModel_CIPackage3, 0x5f738b61, 0xf86a, 0x4917, 0x93,0xd1, 0xf1,0xee,0x9d,0x3b,0x35,0xd9);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            MIDL_INTERFACE("5f738b61-f86a-4917-93d1-f1ee9d3b35d9")
            IPackage3 : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE get_Status(
                    ABI::Windows::ApplicationModel::IPackageStatus **value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_InstalledDate(
                    ABI::Windows::Foundation::DateTime *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE GetAppListEntriesAsync(
                    ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::ApplicationModel::Core::AppListEntry* >* > **operation) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CApplicationModel_CIPackage3, 0x5f738b61, 0xf86a, 0x4917, 0x93,0xd1, 0xf1,0xee,0x9d,0x3b,0x35,0xd9)
#endif
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CIPackage3Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CApplicationModel_CIPackage3 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CApplicationModel_CIPackage3 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CApplicationModel_CIPackage3 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CApplicationModel_CIPackage3 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CApplicationModel_CIPackage3 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CApplicationModel_CIPackage3 *This,
        TrustLevel *trustLevel);

    /*** IPackage3 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Status)(
        __x_ABI_CWindows_CApplicationModel_CIPackage3 *This,
        __x_ABI_CWindows_CApplicationModel_CIPackageStatus **value);

    HRESULT (STDMETHODCALLTYPE *get_InstalledDate)(
        __x_ABI_CWindows_CApplicationModel_CIPackage3 *This,
        __x_ABI_CWindows_CFoundation_CDateTime *value);

    HRESULT (STDMETHODCALLTYPE *GetAppListEntriesAsync)(
        __x_ABI_CWindows_CApplicationModel_CIPackage3 *This,
        __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry **operation);

    END_INTERFACE
} __x_ABI_CWindows_CApplicationModel_CIPackage3Vtbl;

interface __x_ABI_CWindows_CApplicationModel_CIPackage3 {
    CONST_VTBL __x_ABI_CWindows_CApplicationModel_CIPackage3Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CApplicationModel_CIPackage3_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CApplicationModel_CIPackage3_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CApplicationModel_CIPackage3_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CApplicationModel_CIPackage3_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CApplicationModel_CIPackage3_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CApplicationModel_CIPackage3_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IPackage3 methods ***/
#define __x_ABI_CWindows_CApplicationModel_CIPackage3_get_Status(This,value) (This)->lpVtbl->get_Status(This,value)
#define __x_ABI_CWindows_CApplicationModel_CIPackage3_get_InstalledDate(This,value) (This)->lpVtbl->get_InstalledDate(This,value)
#define __x_ABI_CWindows_CApplicationModel_CIPackage3_GetAppListEntriesAsync(This,operation) (This)->lpVtbl->GetAppListEntriesAsync(This,operation)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIPackage3_QueryInterface(__x_ABI_CWindows_CApplicationModel_CIPackage3* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CIPackage3_AddRef(__x_ABI_CWindows_CApplicationModel_CIPackage3* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CIPackage3_Release(__x_ABI_CWindows_CApplicationModel_CIPackage3* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIPackage3_GetIids(__x_ABI_CWindows_CApplicationModel_CIPackage3* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIPackage3_GetRuntimeClassName(__x_ABI_CWindows_CApplicationModel_CIPackage3* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIPackage3_GetTrustLevel(__x_ABI_CWindows_CApplicationModel_CIPackage3* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IPackage3 methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIPackage3_get_Status(__x_ABI_CWindows_CApplicationModel_CIPackage3* This,__x_ABI_CWindows_CApplicationModel_CIPackageStatus **value) {
    return This->lpVtbl->get_Status(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIPackage3_get_InstalledDate(__x_ABI_CWindows_CApplicationModel_CIPackage3* This,__x_ABI_CWindows_CFoundation_CDateTime *value) {
    return This->lpVtbl->get_InstalledDate(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIPackage3_GetAppListEntriesAsync(__x_ABI_CWindows_CApplicationModel_CIPackage3* This,__FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry **operation) {
    return This->lpVtbl->GetAppListEntriesAsync(This,operation);
}
#endif
#ifdef WIDL_using_Windows_ApplicationModel
#define IID_IPackage3 IID___x_ABI_CWindows_CApplicationModel_CIPackage3
#define IPackage3Vtbl __x_ABI_CWindows_CApplicationModel_CIPackage3Vtbl
#define IPackage3 __x_ABI_CWindows_CApplicationModel_CIPackage3
#define IPackage3_QueryInterface __x_ABI_CWindows_CApplicationModel_CIPackage3_QueryInterface
#define IPackage3_AddRef __x_ABI_CWindows_CApplicationModel_CIPackage3_AddRef
#define IPackage3_Release __x_ABI_CWindows_CApplicationModel_CIPackage3_Release
#define IPackage3_GetIids __x_ABI_CWindows_CApplicationModel_CIPackage3_GetIids
#define IPackage3_GetRuntimeClassName __x_ABI_CWindows_CApplicationModel_CIPackage3_GetRuntimeClassName
#define IPackage3_GetTrustLevel __x_ABI_CWindows_CApplicationModel_CIPackage3_GetTrustLevel
#define IPackage3_get_Status __x_ABI_CWindows_CApplicationModel_CIPackage3_get_Status
#define IPackage3_get_InstalledDate __x_ABI_CWindows_CApplicationModel_CIPackage3_get_InstalledDate
#define IPackage3_GetAppListEntriesAsync __x_ABI_CWindows_CApplicationModel_CIPackage3_GetAppListEntriesAsync
#endif /* WIDL_using_Windows_ApplicationModel */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CApplicationModel_CIPackage3_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IPackageStatus interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CApplicationModel_CIPackageStatus_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CIPackageStatus_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CApplicationModel_CIPackageStatus, 0x5fe74f71, 0xa365, 0x4c09, 0xa0,0x2d, 0x04,0x6d,0x52,0x5e,0xa1,0xda);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            MIDL_INTERFACE("5fe74f71-a365-4c09-a02d-046d525ea1da")
            IPackageStatus : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE VerifyIsOK(
                    boolean *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_NotAvailable(
                    boolean *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_PackageOffline(
                    boolean *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_DataOffline(
                    boolean *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Disabled(
                    boolean *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_NeedsRemediation(
                    boolean *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_LicenseIssue(
                    boolean *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Modified(
                    boolean *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Tampered(
                    boolean *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_DependencyIssue(
                    boolean *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Servicing(
                    boolean *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_DeploymentInProgress(
                    boolean *value) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CApplicationModel_CIPackageStatus, 0x5fe74f71, 0xa365, 0x4c09, 0xa0,0x2d, 0x04,0x6d,0x52,0x5e,0xa1,0xda)
#endif
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CIPackageStatusVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CApplicationModel_CIPackageStatus *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CApplicationModel_CIPackageStatus *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CApplicationModel_CIPackageStatus *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CApplicationModel_CIPackageStatus *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CApplicationModel_CIPackageStatus *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CApplicationModel_CIPackageStatus *This,
        TrustLevel *trustLevel);

    /*** IPackageStatus methods ***/
    HRESULT (STDMETHODCALLTYPE *VerifyIsOK)(
        __x_ABI_CWindows_CApplicationModel_CIPackageStatus *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *get_NotAvailable)(
        __x_ABI_CWindows_CApplicationModel_CIPackageStatus *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *get_PackageOffline)(
        __x_ABI_CWindows_CApplicationModel_CIPackageStatus *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *get_DataOffline)(
        __x_ABI_CWindows_CApplicationModel_CIPackageStatus *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *get_Disabled)(
        __x_ABI_CWindows_CApplicationModel_CIPackageStatus *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *get_NeedsRemediation)(
        __x_ABI_CWindows_CApplicationModel_CIPackageStatus *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *get_LicenseIssue)(
        __x_ABI_CWindows_CApplicationModel_CIPackageStatus *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *get_Modified)(
        __x_ABI_CWindows_CApplicationModel_CIPackageStatus *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *get_Tampered)(
        __x_ABI_CWindows_CApplicationModel_CIPackageStatus *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *get_DependencyIssue)(
        __x_ABI_CWindows_CApplicationModel_CIPackageStatus *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *get_Servicing)(
        __x_ABI_CWindows_CApplicationModel_CIPackageStatus *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *get_DeploymentInProgress)(
        __x_ABI_CWindows_CApplicationModel_CIPackageStatus *This,
        boolean *value);

    END_INTERFACE
} __x_ABI_CWindows_CApplicationModel_CIPackageStatusVtbl;

interface __x_ABI_CWindows_CApplicationModel_CIPackageStatus {
    CONST_VTBL __x_ABI_CWindows_CApplicationModel_CIPackageStatusVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CApplicationModel_CIPackageStatus_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CApplicationModel_CIPackageStatus_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CApplicationModel_CIPackageStatus_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CApplicationModel_CIPackageStatus_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CApplicationModel_CIPackageStatus_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CApplicationModel_CIPackageStatus_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IPackageStatus methods ***/
#define __x_ABI_CWindows_CApplicationModel_CIPackageStatus_VerifyIsOK(This,value) (This)->lpVtbl->VerifyIsOK(This,value)
#define __x_ABI_CWindows_CApplicationModel_CIPackageStatus_get_NotAvailable(This,value) (This)->lpVtbl->get_NotAvailable(This,value)
#define __x_ABI_CWindows_CApplicationModel_CIPackageStatus_get_PackageOffline(This,value) (This)->lpVtbl->get_PackageOffline(This,value)
#define __x_ABI_CWindows_CApplicationModel_CIPackageStatus_get_DataOffline(This,value) (This)->lpVtbl->get_DataOffline(This,value)
#define __x_ABI_CWindows_CApplicationModel_CIPackageStatus_get_Disabled(This,value) (This)->lpVtbl->get_Disabled(This,value)
#define __x_ABI_CWindows_CApplicationModel_CIPackageStatus_get_NeedsRemediation(This,value) (This)->lpVtbl->get_NeedsRemediation(This,value)
#define __x_ABI_CWindows_CApplicationModel_CIPackageStatus_get_LicenseIssue(This,value) (This)->lpVtbl->get_LicenseIssue(This,value)
#define __x_ABI_CWindows_CApplicationModel_CIPackageStatus_get_Modified(This,value) (This)->lpVtbl->get_Modified(This,value)
#define __x_ABI_CWindows_CApplicationModel_CIPackageStatus_get_Tampered(This,value) (This)->lpVtbl->get_Tampered(This,value)
#define __x_ABI_CWindows_CApplicationModel_CIPackageStatus_get_DependencyIssue(This,value) (This)->lpVtbl->get_DependencyIssue(This,value)
#define __x_ABI_CWindows_CApplicationModel_CIPackageStatus_get_Servicing(This,value) (This)->lpVtbl->get_Servicing(This,value)
#define __x_ABI_CWindows_CApplicationModel_CIPackageStatus_get_DeploymentInProgress(This,value) (This)->lpVtbl->get_DeploymentInProgress(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIPackageStatus_QueryInterface(__x_ABI_CWindows_CApplicationModel_CIPackageStatus* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CIPackageStatus_AddRef(__x_ABI_CWindows_CApplicationModel_CIPackageStatus* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CIPackageStatus_Release(__x_ABI_CWindows_CApplicationModel_CIPackageStatus* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIPackageStatus_GetIids(__x_ABI_CWindows_CApplicationModel_CIPackageStatus* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIPackageStatus_GetRuntimeClassName(__x_ABI_CWindows_CApplicationModel_CIPackageStatus* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIPackageStatus_GetTrustLevel(__x_ABI_CWindows_CApplicationModel_CIPackageStatus* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IPackageStatus methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIPackageStatus_VerifyIsOK(__x_ABI_CWindows_CApplicationModel_CIPackageStatus* This,boolean *value) {
    return This->lpVtbl->VerifyIsOK(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIPackageStatus_get_NotAvailable(__x_ABI_CWindows_CApplicationModel_CIPackageStatus* This,boolean *value) {
    return This->lpVtbl->get_NotAvailable(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIPackageStatus_get_PackageOffline(__x_ABI_CWindows_CApplicationModel_CIPackageStatus* This,boolean *value) {
    return This->lpVtbl->get_PackageOffline(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIPackageStatus_get_DataOffline(__x_ABI_CWindows_CApplicationModel_CIPackageStatus* This,boolean *value) {
    return This->lpVtbl->get_DataOffline(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIPackageStatus_get_Disabled(__x_ABI_CWindows_CApplicationModel_CIPackageStatus* This,boolean *value) {
    return This->lpVtbl->get_Disabled(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIPackageStatus_get_NeedsRemediation(__x_ABI_CWindows_CApplicationModel_CIPackageStatus* This,boolean *value) {
    return This->lpVtbl->get_NeedsRemediation(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIPackageStatus_get_LicenseIssue(__x_ABI_CWindows_CApplicationModel_CIPackageStatus* This,boolean *value) {
    return This->lpVtbl->get_LicenseIssue(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIPackageStatus_get_Modified(__x_ABI_CWindows_CApplicationModel_CIPackageStatus* This,boolean *value) {
    return This->lpVtbl->get_Modified(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIPackageStatus_get_Tampered(__x_ABI_CWindows_CApplicationModel_CIPackageStatus* This,boolean *value) {
    return This->lpVtbl->get_Tampered(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIPackageStatus_get_DependencyIssue(__x_ABI_CWindows_CApplicationModel_CIPackageStatus* This,boolean *value) {
    return This->lpVtbl->get_DependencyIssue(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIPackageStatus_get_Servicing(__x_ABI_CWindows_CApplicationModel_CIPackageStatus* This,boolean *value) {
    return This->lpVtbl->get_Servicing(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIPackageStatus_get_DeploymentInProgress(__x_ABI_CWindows_CApplicationModel_CIPackageStatus* This,boolean *value) {
    return This->lpVtbl->get_DeploymentInProgress(This,value);
}
#endif
#ifdef WIDL_using_Windows_ApplicationModel
#define IID_IPackageStatus IID___x_ABI_CWindows_CApplicationModel_CIPackageStatus
#define IPackageStatusVtbl __x_ABI_CWindows_CApplicationModel_CIPackageStatusVtbl
#define IPackageStatus __x_ABI_CWindows_CApplicationModel_CIPackageStatus
#define IPackageStatus_QueryInterface __x_ABI_CWindows_CApplicationModel_CIPackageStatus_QueryInterface
#define IPackageStatus_AddRef __x_ABI_CWindows_CApplicationModel_CIPackageStatus_AddRef
#define IPackageStatus_Release __x_ABI_CWindows_CApplicationModel_CIPackageStatus_Release
#define IPackageStatus_GetIids __x_ABI_CWindows_CApplicationModel_CIPackageStatus_GetIids
#define IPackageStatus_GetRuntimeClassName __x_ABI_CWindows_CApplicationModel_CIPackageStatus_GetRuntimeClassName
#define IPackageStatus_GetTrustLevel __x_ABI_CWindows_CApplicationModel_CIPackageStatus_GetTrustLevel
#define IPackageStatus_VerifyIsOK __x_ABI_CWindows_CApplicationModel_CIPackageStatus_VerifyIsOK
#define IPackageStatus_get_NotAvailable __x_ABI_CWindows_CApplicationModel_CIPackageStatus_get_NotAvailable
#define IPackageStatus_get_PackageOffline __x_ABI_CWindows_CApplicationModel_CIPackageStatus_get_PackageOffline
#define IPackageStatus_get_DataOffline __x_ABI_CWindows_CApplicationModel_CIPackageStatus_get_DataOffline
#define IPackageStatus_get_Disabled __x_ABI_CWindows_CApplicationModel_CIPackageStatus_get_Disabled
#define IPackageStatus_get_NeedsRemediation __x_ABI_CWindows_CApplicationModel_CIPackageStatus_get_NeedsRemediation
#define IPackageStatus_get_LicenseIssue __x_ABI_CWindows_CApplicationModel_CIPackageStatus_get_LicenseIssue
#define IPackageStatus_get_Modified __x_ABI_CWindows_CApplicationModel_CIPackageStatus_get_Modified
#define IPackageStatus_get_Tampered __x_ABI_CWindows_CApplicationModel_CIPackageStatus_get_Tampered
#define IPackageStatus_get_DependencyIssue __x_ABI_CWindows_CApplicationModel_CIPackageStatus_get_DependencyIssue
#define IPackageStatus_get_Servicing __x_ABI_CWindows_CApplicationModel_CIPackageStatus_get_Servicing
#define IPackageStatus_get_DeploymentInProgress __x_ABI_CWindows_CApplicationModel_CIPackageStatus_get_DeploymentInProgress
#endif /* WIDL_using_Windows_ApplicationModel */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CApplicationModel_CIPackageStatus_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IPackageId interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CApplicationModel_CIPackageId_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CIPackageId_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CApplicationModel_CIPackageId, 0x1adb665e, 0x37c7, 0x4790, 0x99,0x80, 0xdd,0x7a,0xe7,0x4e,0x8b,0xb2);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            MIDL_INTERFACE("1adb665e-37c7-4790-9980-dd7ae74e8bb2")
            IPackageId : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE get_Name(
                    HSTRING *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Version(
                    ABI::Windows::ApplicationModel::PackageVersion *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Architecture(
                    ABI::Windows::System::ProcessorArchitecture *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_ResourceId(
                    HSTRING *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Publisher(
                    HSTRING *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_PublisherId(
                    HSTRING *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_FullName(
                    HSTRING *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_FamilyName(
                    HSTRING *value) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CApplicationModel_CIPackageId, 0x1adb665e, 0x37c7, 0x4790, 0x99,0x80, 0xdd,0x7a,0xe7,0x4e,0x8b,0xb2)
#endif
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CIPackageIdVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CApplicationModel_CIPackageId *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CApplicationModel_CIPackageId *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CApplicationModel_CIPackageId *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CApplicationModel_CIPackageId *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CApplicationModel_CIPackageId *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CApplicationModel_CIPackageId *This,
        TrustLevel *trustLevel);

    /*** IPackageId methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Name)(
        __x_ABI_CWindows_CApplicationModel_CIPackageId *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_Version)(
        __x_ABI_CWindows_CApplicationModel_CIPackageId *This,
        __x_ABI_CWindows_CApplicationModel_CPackageVersion *value);

    HRESULT (STDMETHODCALLTYPE *get_Architecture)(
        __x_ABI_CWindows_CApplicationModel_CIPackageId *This,
        __x_ABI_CWindows_CSystem_CProcessorArchitecture *value);

    HRESULT (STDMETHODCALLTYPE *get_ResourceId)(
        __x_ABI_CWindows_CApplicationModel_CIPackageId *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_Publisher)(
        __x_ABI_CWindows_CApplicationModel_CIPackageId *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_PublisherId)(
        __x_ABI_CWindows_CApplicationModel_CIPackageId *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_FullName)(
        __x_ABI_CWindows_CApplicationModel_CIPackageId *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_FamilyName)(
        __x_ABI_CWindows_CApplicationModel_CIPackageId *This,
        HSTRING *value);

    END_INTERFACE
} __x_ABI_CWindows_CApplicationModel_CIPackageIdVtbl;

interface __x_ABI_CWindows_CApplicationModel_CIPackageId {
    CONST_VTBL __x_ABI_CWindows_CApplicationModel_CIPackageIdVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CApplicationModel_CIPackageId_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CApplicationModel_CIPackageId_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CApplicationModel_CIPackageId_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CApplicationModel_CIPackageId_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CApplicationModel_CIPackageId_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CApplicationModel_CIPackageId_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IPackageId methods ***/
#define __x_ABI_CWindows_CApplicationModel_CIPackageId_get_Name(This,value) (This)->lpVtbl->get_Name(This,value)
#define __x_ABI_CWindows_CApplicationModel_CIPackageId_get_Version(This,value) (This)->lpVtbl->get_Version(This,value)
#define __x_ABI_CWindows_CApplicationModel_CIPackageId_get_Architecture(This,value) (This)->lpVtbl->get_Architecture(This,value)
#define __x_ABI_CWindows_CApplicationModel_CIPackageId_get_ResourceId(This,value) (This)->lpVtbl->get_ResourceId(This,value)
#define __x_ABI_CWindows_CApplicationModel_CIPackageId_get_Publisher(This,value) (This)->lpVtbl->get_Publisher(This,value)
#define __x_ABI_CWindows_CApplicationModel_CIPackageId_get_PublisherId(This,value) (This)->lpVtbl->get_PublisherId(This,value)
#define __x_ABI_CWindows_CApplicationModel_CIPackageId_get_FullName(This,value) (This)->lpVtbl->get_FullName(This,value)
#define __x_ABI_CWindows_CApplicationModel_CIPackageId_get_FamilyName(This,value) (This)->lpVtbl->get_FamilyName(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIPackageId_QueryInterface(__x_ABI_CWindows_CApplicationModel_CIPackageId* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CIPackageId_AddRef(__x_ABI_CWindows_CApplicationModel_CIPackageId* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CIPackageId_Release(__x_ABI_CWindows_CApplicationModel_CIPackageId* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIPackageId_GetIids(__x_ABI_CWindows_CApplicationModel_CIPackageId* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIPackageId_GetRuntimeClassName(__x_ABI_CWindows_CApplicationModel_CIPackageId* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIPackageId_GetTrustLevel(__x_ABI_CWindows_CApplicationModel_CIPackageId* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IPackageId methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIPackageId_get_Name(__x_ABI_CWindows_CApplicationModel_CIPackageId* This,HSTRING *value) {
    return This->lpVtbl->get_Name(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIPackageId_get_Version(__x_ABI_CWindows_CApplicationModel_CIPackageId* This,__x_ABI_CWindows_CApplicationModel_CPackageVersion *value) {
    return This->lpVtbl->get_Version(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIPackageId_get_Architecture(__x_ABI_CWindows_CApplicationModel_CIPackageId* This,__x_ABI_CWindows_CSystem_CProcessorArchitecture *value) {
    return This->lpVtbl->get_Architecture(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIPackageId_get_ResourceId(__x_ABI_CWindows_CApplicationModel_CIPackageId* This,HSTRING *value) {
    return This->lpVtbl->get_ResourceId(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIPackageId_get_Publisher(__x_ABI_CWindows_CApplicationModel_CIPackageId* This,HSTRING *value) {
    return This->lpVtbl->get_Publisher(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIPackageId_get_PublisherId(__x_ABI_CWindows_CApplicationModel_CIPackageId* This,HSTRING *value) {
    return This->lpVtbl->get_PublisherId(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIPackageId_get_FullName(__x_ABI_CWindows_CApplicationModel_CIPackageId* This,HSTRING *value) {
    return This->lpVtbl->get_FullName(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIPackageId_get_FamilyName(__x_ABI_CWindows_CApplicationModel_CIPackageId* This,HSTRING *value) {
    return This->lpVtbl->get_FamilyName(This,value);
}
#endif
#ifdef WIDL_using_Windows_ApplicationModel
#define IID_IPackageId IID___x_ABI_CWindows_CApplicationModel_CIPackageId
#define IPackageIdVtbl __x_ABI_CWindows_CApplicationModel_CIPackageIdVtbl
#define IPackageId __x_ABI_CWindows_CApplicationModel_CIPackageId
#define IPackageId_QueryInterface __x_ABI_CWindows_CApplicationModel_CIPackageId_QueryInterface
#define IPackageId_AddRef __x_ABI_CWindows_CApplicationModel_CIPackageId_AddRef
#define IPackageId_Release __x_ABI_CWindows_CApplicationModel_CIPackageId_Release
#define IPackageId_GetIids __x_ABI_CWindows_CApplicationModel_CIPackageId_GetIids
#define IPackageId_GetRuntimeClassName __x_ABI_CWindows_CApplicationModel_CIPackageId_GetRuntimeClassName
#define IPackageId_GetTrustLevel __x_ABI_CWindows_CApplicationModel_CIPackageId_GetTrustLevel
#define IPackageId_get_Name __x_ABI_CWindows_CApplicationModel_CIPackageId_get_Name
#define IPackageId_get_Version __x_ABI_CWindows_CApplicationModel_CIPackageId_get_Version
#define IPackageId_get_Architecture __x_ABI_CWindows_CApplicationModel_CIPackageId_get_Architecture
#define IPackageId_get_ResourceId __x_ABI_CWindows_CApplicationModel_CIPackageId_get_ResourceId
#define IPackageId_get_Publisher __x_ABI_CWindows_CApplicationModel_CIPackageId_get_Publisher
#define IPackageId_get_PublisherId __x_ABI_CWindows_CApplicationModel_CIPackageId_get_PublisherId
#define IPackageId_get_FullName __x_ABI_CWindows_CApplicationModel_CIPackageId_get_FullName
#define IPackageId_get_FamilyName __x_ABI_CWindows_CApplicationModel_CIPackageId_get_FamilyName
#endif /* WIDL_using_Windows_ApplicationModel */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CApplicationModel_CIPackageId_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IPackageIdWithMetadata interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CApplicationModel_CIPackageIdWithMetadata_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CIPackageIdWithMetadata_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CApplicationModel_CIPackageIdWithMetadata, 0x40577a7c, 0x0c9e, 0x443d, 0x90,0x74, 0x85,0x5f,0x5c,0xe0,0xa0,0x8d);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            MIDL_INTERFACE("40577a7c-0c9e-443d-9074-855f5ce0a08d")
            IPackageIdWithMetadata : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE get_ProductId(
                    HSTRING *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Author(
                    HSTRING *value) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CApplicationModel_CIPackageIdWithMetadata, 0x40577a7c, 0x0c9e, 0x443d, 0x90,0x74, 0x85,0x5f,0x5c,0xe0,0xa0,0x8d)
#endif
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CIPackageIdWithMetadataVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CApplicationModel_CIPackageIdWithMetadata *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CApplicationModel_CIPackageIdWithMetadata *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CApplicationModel_CIPackageIdWithMetadata *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CApplicationModel_CIPackageIdWithMetadata *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CApplicationModel_CIPackageIdWithMetadata *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CApplicationModel_CIPackageIdWithMetadata *This,
        TrustLevel *trustLevel);

    /*** IPackageIdWithMetadata methods ***/
    HRESULT (STDMETHODCALLTYPE *get_ProductId)(
        __x_ABI_CWindows_CApplicationModel_CIPackageIdWithMetadata *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_Author)(
        __x_ABI_CWindows_CApplicationModel_CIPackageIdWithMetadata *This,
        HSTRING *value);

    END_INTERFACE
} __x_ABI_CWindows_CApplicationModel_CIPackageIdWithMetadataVtbl;

interface __x_ABI_CWindows_CApplicationModel_CIPackageIdWithMetadata {
    CONST_VTBL __x_ABI_CWindows_CApplicationModel_CIPackageIdWithMetadataVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CApplicationModel_CIPackageIdWithMetadata_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CApplicationModel_CIPackageIdWithMetadata_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CApplicationModel_CIPackageIdWithMetadata_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CApplicationModel_CIPackageIdWithMetadata_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CApplicationModel_CIPackageIdWithMetadata_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CApplicationModel_CIPackageIdWithMetadata_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IPackageIdWithMetadata methods ***/
#define __x_ABI_CWindows_CApplicationModel_CIPackageIdWithMetadata_get_ProductId(This,value) (This)->lpVtbl->get_ProductId(This,value)
#define __x_ABI_CWindows_CApplicationModel_CIPackageIdWithMetadata_get_Author(This,value) (This)->lpVtbl->get_Author(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIPackageIdWithMetadata_QueryInterface(__x_ABI_CWindows_CApplicationModel_CIPackageIdWithMetadata* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CIPackageIdWithMetadata_AddRef(__x_ABI_CWindows_CApplicationModel_CIPackageIdWithMetadata* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CIPackageIdWithMetadata_Release(__x_ABI_CWindows_CApplicationModel_CIPackageIdWithMetadata* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIPackageIdWithMetadata_GetIids(__x_ABI_CWindows_CApplicationModel_CIPackageIdWithMetadata* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIPackageIdWithMetadata_GetRuntimeClassName(__x_ABI_CWindows_CApplicationModel_CIPackageIdWithMetadata* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIPackageIdWithMetadata_GetTrustLevel(__x_ABI_CWindows_CApplicationModel_CIPackageIdWithMetadata* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IPackageIdWithMetadata methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIPackageIdWithMetadata_get_ProductId(__x_ABI_CWindows_CApplicationModel_CIPackageIdWithMetadata* This,HSTRING *value) {
    return This->lpVtbl->get_ProductId(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIPackageIdWithMetadata_get_Author(__x_ABI_CWindows_CApplicationModel_CIPackageIdWithMetadata* This,HSTRING *value) {
    return This->lpVtbl->get_Author(This,value);
}
#endif
#ifdef WIDL_using_Windows_ApplicationModel
#define IID_IPackageIdWithMetadata IID___x_ABI_CWindows_CApplicationModel_CIPackageIdWithMetadata
#define IPackageIdWithMetadataVtbl __x_ABI_CWindows_CApplicationModel_CIPackageIdWithMetadataVtbl
#define IPackageIdWithMetadata __x_ABI_CWindows_CApplicationModel_CIPackageIdWithMetadata
#define IPackageIdWithMetadata_QueryInterface __x_ABI_CWindows_CApplicationModel_CIPackageIdWithMetadata_QueryInterface
#define IPackageIdWithMetadata_AddRef __x_ABI_CWindows_CApplicationModel_CIPackageIdWithMetadata_AddRef
#define IPackageIdWithMetadata_Release __x_ABI_CWindows_CApplicationModel_CIPackageIdWithMetadata_Release
#define IPackageIdWithMetadata_GetIids __x_ABI_CWindows_CApplicationModel_CIPackageIdWithMetadata_GetIids
#define IPackageIdWithMetadata_GetRuntimeClassName __x_ABI_CWindows_CApplicationModel_CIPackageIdWithMetadata_GetRuntimeClassName
#define IPackageIdWithMetadata_GetTrustLevel __x_ABI_CWindows_CApplicationModel_CIPackageIdWithMetadata_GetTrustLevel
#define IPackageIdWithMetadata_get_ProductId __x_ABI_CWindows_CApplicationModel_CIPackageIdWithMetadata_get_ProductId
#define IPackageIdWithMetadata_get_Author __x_ABI_CWindows_CApplicationModel_CIPackageIdWithMetadata_get_Author
#endif /* WIDL_using_Windows_ApplicationModel */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CApplicationModel_CIPackageIdWithMetadata_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IPackageStatics interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CApplicationModel_CIPackageStatics_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CIPackageStatics_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CApplicationModel_CIPackageStatics, 0x4e534bdf, 0x2960, 0x4878, 0x97,0xa4, 0x96,0x24,0xde,0xb7,0x2f,0x2d);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            MIDL_INTERFACE("4e534bdf-2960-4878-97a4-9624deb72f2d")
            IPackageStatics : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE get_Current(
                    ABI::Windows::ApplicationModel::IPackage **value) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CApplicationModel_CIPackageStatics, 0x4e534bdf, 0x2960, 0x4878, 0x97,0xa4, 0x96,0x24,0xde,0xb7,0x2f,0x2d)
#endif
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CIPackageStaticsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CApplicationModel_CIPackageStatics *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CApplicationModel_CIPackageStatics *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CApplicationModel_CIPackageStatics *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CApplicationModel_CIPackageStatics *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CApplicationModel_CIPackageStatics *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CApplicationModel_CIPackageStatics *This,
        TrustLevel *trustLevel);

    /*** IPackageStatics methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Current)(
        __x_ABI_CWindows_CApplicationModel_CIPackageStatics *This,
        __x_ABI_CWindows_CApplicationModel_CIPackage **value);

    END_INTERFACE
} __x_ABI_CWindows_CApplicationModel_CIPackageStaticsVtbl;

interface __x_ABI_CWindows_CApplicationModel_CIPackageStatics {
    CONST_VTBL __x_ABI_CWindows_CApplicationModel_CIPackageStaticsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CApplicationModel_CIPackageStatics_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CApplicationModel_CIPackageStatics_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CApplicationModel_CIPackageStatics_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CApplicationModel_CIPackageStatics_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CApplicationModel_CIPackageStatics_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CApplicationModel_CIPackageStatics_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IPackageStatics methods ***/
#define __x_ABI_CWindows_CApplicationModel_CIPackageStatics_get_Current(This,value) (This)->lpVtbl->get_Current(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIPackageStatics_QueryInterface(__x_ABI_CWindows_CApplicationModel_CIPackageStatics* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CIPackageStatics_AddRef(__x_ABI_CWindows_CApplicationModel_CIPackageStatics* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CIPackageStatics_Release(__x_ABI_CWindows_CApplicationModel_CIPackageStatics* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIPackageStatics_GetIids(__x_ABI_CWindows_CApplicationModel_CIPackageStatics* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIPackageStatics_GetRuntimeClassName(__x_ABI_CWindows_CApplicationModel_CIPackageStatics* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIPackageStatics_GetTrustLevel(__x_ABI_CWindows_CApplicationModel_CIPackageStatics* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IPackageStatics methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CIPackageStatics_get_Current(__x_ABI_CWindows_CApplicationModel_CIPackageStatics* This,__x_ABI_CWindows_CApplicationModel_CIPackage **value) {
    return This->lpVtbl->get_Current(This,value);
}
#endif
#ifdef WIDL_using_Windows_ApplicationModel
#define IID_IPackageStatics IID___x_ABI_CWindows_CApplicationModel_CIPackageStatics
#define IPackageStaticsVtbl __x_ABI_CWindows_CApplicationModel_CIPackageStaticsVtbl
#define IPackageStatics __x_ABI_CWindows_CApplicationModel_CIPackageStatics
#define IPackageStatics_QueryInterface __x_ABI_CWindows_CApplicationModel_CIPackageStatics_QueryInterface
#define IPackageStatics_AddRef __x_ABI_CWindows_CApplicationModel_CIPackageStatics_AddRef
#define IPackageStatics_Release __x_ABI_CWindows_CApplicationModel_CIPackageStatics_Release
#define IPackageStatics_GetIids __x_ABI_CWindows_CApplicationModel_CIPackageStatics_GetIids
#define IPackageStatics_GetRuntimeClassName __x_ABI_CWindows_CApplicationModel_CIPackageStatics_GetRuntimeClassName
#define IPackageStatics_GetTrustLevel __x_ABI_CWindows_CApplicationModel_CIPackageStatics_GetTrustLevel
#define IPackageStatics_get_Current __x_ABI_CWindows_CApplicationModel_CIPackageStatics_get_Current
#endif /* WIDL_using_Windows_ApplicationModel */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CApplicationModel_CIPackageStatics_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ISuspendingDeferral interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CApplicationModel_CISuspendingDeferral_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CISuspendingDeferral_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CApplicationModel_CISuspendingDeferral, 0x59140509, 0x8bc9, 0x4eb4, 0xb6,0x36, 0xda,0xbd,0xc4,0xf4,0x6f,0x66);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            MIDL_INTERFACE("59140509-8bc9-4eb4-b636-dabdc4f46f66")
            ISuspendingDeferral : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE Complete(
                    ) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CApplicationModel_CISuspendingDeferral, 0x59140509, 0x8bc9, 0x4eb4, 0xb6,0x36, 0xda,0xbd,0xc4,0xf4,0x6f,0x66)
#endif
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CISuspendingDeferralVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CApplicationModel_CISuspendingDeferral *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CApplicationModel_CISuspendingDeferral *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CApplicationModel_CISuspendingDeferral *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CApplicationModel_CISuspendingDeferral *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CApplicationModel_CISuspendingDeferral *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CApplicationModel_CISuspendingDeferral *This,
        TrustLevel *trustLevel);

    /*** ISuspendingDeferral methods ***/
    HRESULT (STDMETHODCALLTYPE *Complete)(
        __x_ABI_CWindows_CApplicationModel_CISuspendingDeferral *This);

    END_INTERFACE
} __x_ABI_CWindows_CApplicationModel_CISuspendingDeferralVtbl;

interface __x_ABI_CWindows_CApplicationModel_CISuspendingDeferral {
    CONST_VTBL __x_ABI_CWindows_CApplicationModel_CISuspendingDeferralVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CApplicationModel_CISuspendingDeferral_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CApplicationModel_CISuspendingDeferral_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CApplicationModel_CISuspendingDeferral_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CApplicationModel_CISuspendingDeferral_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CApplicationModel_CISuspendingDeferral_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CApplicationModel_CISuspendingDeferral_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ISuspendingDeferral methods ***/
#define __x_ABI_CWindows_CApplicationModel_CISuspendingDeferral_Complete(This) (This)->lpVtbl->Complete(This)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CISuspendingDeferral_QueryInterface(__x_ABI_CWindows_CApplicationModel_CISuspendingDeferral* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CISuspendingDeferral_AddRef(__x_ABI_CWindows_CApplicationModel_CISuspendingDeferral* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CISuspendingDeferral_Release(__x_ABI_CWindows_CApplicationModel_CISuspendingDeferral* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CISuspendingDeferral_GetIids(__x_ABI_CWindows_CApplicationModel_CISuspendingDeferral* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CISuspendingDeferral_GetRuntimeClassName(__x_ABI_CWindows_CApplicationModel_CISuspendingDeferral* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CISuspendingDeferral_GetTrustLevel(__x_ABI_CWindows_CApplicationModel_CISuspendingDeferral* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ISuspendingDeferral methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CISuspendingDeferral_Complete(__x_ABI_CWindows_CApplicationModel_CISuspendingDeferral* This) {
    return This->lpVtbl->Complete(This);
}
#endif
#ifdef WIDL_using_Windows_ApplicationModel
#define IID_ISuspendingDeferral IID___x_ABI_CWindows_CApplicationModel_CISuspendingDeferral
#define ISuspendingDeferralVtbl __x_ABI_CWindows_CApplicationModel_CISuspendingDeferralVtbl
#define ISuspendingDeferral __x_ABI_CWindows_CApplicationModel_CISuspendingDeferral
#define ISuspendingDeferral_QueryInterface __x_ABI_CWindows_CApplicationModel_CISuspendingDeferral_QueryInterface
#define ISuspendingDeferral_AddRef __x_ABI_CWindows_CApplicationModel_CISuspendingDeferral_AddRef
#define ISuspendingDeferral_Release __x_ABI_CWindows_CApplicationModel_CISuspendingDeferral_Release
#define ISuspendingDeferral_GetIids __x_ABI_CWindows_CApplicationModel_CISuspendingDeferral_GetIids
#define ISuspendingDeferral_GetRuntimeClassName __x_ABI_CWindows_CApplicationModel_CISuspendingDeferral_GetRuntimeClassName
#define ISuspendingDeferral_GetTrustLevel __x_ABI_CWindows_CApplicationModel_CISuspendingDeferral_GetTrustLevel
#define ISuspendingDeferral_Complete __x_ABI_CWindows_CApplicationModel_CISuspendingDeferral_Complete
#endif /* WIDL_using_Windows_ApplicationModel */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CApplicationModel_CISuspendingDeferral_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ISuspendingEventArgs interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CApplicationModel_CISuspendingEventArgs_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CISuspendingEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CApplicationModel_CISuspendingEventArgs, 0x96061c05, 0x2dba, 0x4d08, 0xb0,0xbd, 0x2b,0x30,0xa1,0x31,0xc6,0xaa);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            MIDL_INTERFACE("96061c05-2dba-4d08-b0bd-2b30a131c6aa")
            ISuspendingEventArgs : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE get_SuspendingOperation(
                    ABI::Windows::ApplicationModel::ISuspendingOperation **value) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CApplicationModel_CISuspendingEventArgs, 0x96061c05, 0x2dba, 0x4d08, 0xb0,0xbd, 0x2b,0x30,0xa1,0x31,0xc6,0xaa)
#endif
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CISuspendingEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CApplicationModel_CISuspendingEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CApplicationModel_CISuspendingEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CApplicationModel_CISuspendingEventArgs *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CApplicationModel_CISuspendingEventArgs *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CApplicationModel_CISuspendingEventArgs *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CApplicationModel_CISuspendingEventArgs *This,
        TrustLevel *trustLevel);

    /*** ISuspendingEventArgs methods ***/
    HRESULT (STDMETHODCALLTYPE *get_SuspendingOperation)(
        __x_ABI_CWindows_CApplicationModel_CISuspendingEventArgs *This,
        __x_ABI_CWindows_CApplicationModel_CISuspendingOperation **value);

    END_INTERFACE
} __x_ABI_CWindows_CApplicationModel_CISuspendingEventArgsVtbl;

interface __x_ABI_CWindows_CApplicationModel_CISuspendingEventArgs {
    CONST_VTBL __x_ABI_CWindows_CApplicationModel_CISuspendingEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CApplicationModel_CISuspendingEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CApplicationModel_CISuspendingEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CApplicationModel_CISuspendingEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CApplicationModel_CISuspendingEventArgs_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CApplicationModel_CISuspendingEventArgs_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CApplicationModel_CISuspendingEventArgs_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ISuspendingEventArgs methods ***/
#define __x_ABI_CWindows_CApplicationModel_CISuspendingEventArgs_get_SuspendingOperation(This,value) (This)->lpVtbl->get_SuspendingOperation(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CISuspendingEventArgs_QueryInterface(__x_ABI_CWindows_CApplicationModel_CISuspendingEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CISuspendingEventArgs_AddRef(__x_ABI_CWindows_CApplicationModel_CISuspendingEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CISuspendingEventArgs_Release(__x_ABI_CWindows_CApplicationModel_CISuspendingEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CISuspendingEventArgs_GetIids(__x_ABI_CWindows_CApplicationModel_CISuspendingEventArgs* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CISuspendingEventArgs_GetRuntimeClassName(__x_ABI_CWindows_CApplicationModel_CISuspendingEventArgs* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CISuspendingEventArgs_GetTrustLevel(__x_ABI_CWindows_CApplicationModel_CISuspendingEventArgs* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ISuspendingEventArgs methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CISuspendingEventArgs_get_SuspendingOperation(__x_ABI_CWindows_CApplicationModel_CISuspendingEventArgs* This,__x_ABI_CWindows_CApplicationModel_CISuspendingOperation **value) {
    return This->lpVtbl->get_SuspendingOperation(This,value);
}
#endif
#ifdef WIDL_using_Windows_ApplicationModel
#define IID_ISuspendingEventArgs IID___x_ABI_CWindows_CApplicationModel_CISuspendingEventArgs
#define ISuspendingEventArgsVtbl __x_ABI_CWindows_CApplicationModel_CISuspendingEventArgsVtbl
#define ISuspendingEventArgs __x_ABI_CWindows_CApplicationModel_CISuspendingEventArgs
#define ISuspendingEventArgs_QueryInterface __x_ABI_CWindows_CApplicationModel_CISuspendingEventArgs_QueryInterface
#define ISuspendingEventArgs_AddRef __x_ABI_CWindows_CApplicationModel_CISuspendingEventArgs_AddRef
#define ISuspendingEventArgs_Release __x_ABI_CWindows_CApplicationModel_CISuspendingEventArgs_Release
#define ISuspendingEventArgs_GetIids __x_ABI_CWindows_CApplicationModel_CISuspendingEventArgs_GetIids
#define ISuspendingEventArgs_GetRuntimeClassName __x_ABI_CWindows_CApplicationModel_CISuspendingEventArgs_GetRuntimeClassName
#define ISuspendingEventArgs_GetTrustLevel __x_ABI_CWindows_CApplicationModel_CISuspendingEventArgs_GetTrustLevel
#define ISuspendingEventArgs_get_SuspendingOperation __x_ABI_CWindows_CApplicationModel_CISuspendingEventArgs_get_SuspendingOperation
#endif /* WIDL_using_Windows_ApplicationModel */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CApplicationModel_CISuspendingEventArgs_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ISuspendingOperation interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CApplicationModel_CISuspendingOperation_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CISuspendingOperation_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CApplicationModel_CISuspendingOperation, 0x9da4ca41, 0x20e1, 0x4e9b, 0x9f,0x65, 0xa9,0xf4,0x35,0x34,0x0c,0x3a);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            MIDL_INTERFACE("9da4ca41-20e1-4e9b-9f65-a9f435340c3a")
            ISuspendingOperation : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE GetDeferral(
                    ABI::Windows::ApplicationModel::ISuspendingDeferral **deferral) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Deadline(
                    ABI::Windows::Foundation::DateTime *value) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CApplicationModel_CISuspendingOperation, 0x9da4ca41, 0x20e1, 0x4e9b, 0x9f,0x65, 0xa9,0xf4,0x35,0x34,0x0c,0x3a)
#endif
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CISuspendingOperationVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CApplicationModel_CISuspendingOperation *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CApplicationModel_CISuspendingOperation *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CApplicationModel_CISuspendingOperation *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CApplicationModel_CISuspendingOperation *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CApplicationModel_CISuspendingOperation *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CApplicationModel_CISuspendingOperation *This,
        TrustLevel *trustLevel);

    /*** ISuspendingOperation methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDeferral)(
        __x_ABI_CWindows_CApplicationModel_CISuspendingOperation *This,
        __x_ABI_CWindows_CApplicationModel_CISuspendingDeferral **deferral);

    HRESULT (STDMETHODCALLTYPE *get_Deadline)(
        __x_ABI_CWindows_CApplicationModel_CISuspendingOperation *This,
        __x_ABI_CWindows_CFoundation_CDateTime *value);

    END_INTERFACE
} __x_ABI_CWindows_CApplicationModel_CISuspendingOperationVtbl;

interface __x_ABI_CWindows_CApplicationModel_CISuspendingOperation {
    CONST_VTBL __x_ABI_CWindows_CApplicationModel_CISuspendingOperationVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CApplicationModel_CISuspendingOperation_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CApplicationModel_CISuspendingOperation_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CApplicationModel_CISuspendingOperation_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CApplicationModel_CISuspendingOperation_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CApplicationModel_CISuspendingOperation_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CApplicationModel_CISuspendingOperation_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ISuspendingOperation methods ***/
#define __x_ABI_CWindows_CApplicationModel_CISuspendingOperation_GetDeferral(This,deferral) (This)->lpVtbl->GetDeferral(This,deferral)
#define __x_ABI_CWindows_CApplicationModel_CISuspendingOperation_get_Deadline(This,value) (This)->lpVtbl->get_Deadline(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CISuspendingOperation_QueryInterface(__x_ABI_CWindows_CApplicationModel_CISuspendingOperation* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CISuspendingOperation_AddRef(__x_ABI_CWindows_CApplicationModel_CISuspendingOperation* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CISuspendingOperation_Release(__x_ABI_CWindows_CApplicationModel_CISuspendingOperation* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CISuspendingOperation_GetIids(__x_ABI_CWindows_CApplicationModel_CISuspendingOperation* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CISuspendingOperation_GetRuntimeClassName(__x_ABI_CWindows_CApplicationModel_CISuspendingOperation* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CISuspendingOperation_GetTrustLevel(__x_ABI_CWindows_CApplicationModel_CISuspendingOperation* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ISuspendingOperation methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CISuspendingOperation_GetDeferral(__x_ABI_CWindows_CApplicationModel_CISuspendingOperation* This,__x_ABI_CWindows_CApplicationModel_CISuspendingDeferral **deferral) {
    return This->lpVtbl->GetDeferral(This,deferral);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CISuspendingOperation_get_Deadline(__x_ABI_CWindows_CApplicationModel_CISuspendingOperation* This,__x_ABI_CWindows_CFoundation_CDateTime *value) {
    return This->lpVtbl->get_Deadline(This,value);
}
#endif
#ifdef WIDL_using_Windows_ApplicationModel
#define IID_ISuspendingOperation IID___x_ABI_CWindows_CApplicationModel_CISuspendingOperation
#define ISuspendingOperationVtbl __x_ABI_CWindows_CApplicationModel_CISuspendingOperationVtbl
#define ISuspendingOperation __x_ABI_CWindows_CApplicationModel_CISuspendingOperation
#define ISuspendingOperation_QueryInterface __x_ABI_CWindows_CApplicationModel_CISuspendingOperation_QueryInterface
#define ISuspendingOperation_AddRef __x_ABI_CWindows_CApplicationModel_CISuspendingOperation_AddRef
#define ISuspendingOperation_Release __x_ABI_CWindows_CApplicationModel_CISuspendingOperation_Release
#define ISuspendingOperation_GetIids __x_ABI_CWindows_CApplicationModel_CISuspendingOperation_GetIids
#define ISuspendingOperation_GetRuntimeClassName __x_ABI_CWindows_CApplicationModel_CISuspendingOperation_GetRuntimeClassName
#define ISuspendingOperation_GetTrustLevel __x_ABI_CWindows_CApplicationModel_CISuspendingOperation_GetTrustLevel
#define ISuspendingOperation_GetDeferral __x_ABI_CWindows_CApplicationModel_CISuspendingOperation_GetDeferral
#define ISuspendingOperation_get_Deadline __x_ABI_CWindows_CApplicationModel_CISuspendingOperation_get_Deadline
#endif /* WIDL_using_Windows_ApplicationModel */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CApplicationModel_CISuspendingOperation_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.ApplicationModel.AppDisplayInfo
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_ApplicationModel_AppDisplayInfo_DEFINED
#define RUNTIMECLASS_Windows_ApplicationModel_AppDisplayInfo_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_ApplicationModel_AppDisplayInfo[] = {'W','i','n','d','o','w','s','.','A','p','p','l','i','c','a','t','i','o','n','M','o','d','e','l','.','A','p','p','D','i','s','p','l','a','y','I','n','f','o',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_ApplicationModel_AppDisplayInfo[] = L"Windows.ApplicationModel.AppDisplayInfo";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_ApplicationModel_AppDisplayInfo[] = {'W','i','n','d','o','w','s','.','A','p','p','l','i','c','a','t','i','o','n','M','o','d','e','l','.','A','p','p','D','i','s','p','l','a','y','I','n','f','o',0};
#endif
#endif /* RUNTIMECLASS_Windows_ApplicationModel_AppDisplayInfo_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.ApplicationModel.AppInfo
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_ApplicationModel_AppInfo_DEFINED
#define RUNTIMECLASS_Windows_ApplicationModel_AppInfo_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_ApplicationModel_AppInfo[] = {'W','i','n','d','o','w','s','.','A','p','p','l','i','c','a','t','i','o','n','M','o','d','e','l','.','A','p','p','I','n','f','o',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_ApplicationModel_AppInfo[] = L"Windows.ApplicationModel.AppInfo";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_ApplicationModel_AppInfo[] = {'W','i','n','d','o','w','s','.','A','p','p','l','i','c','a','t','i','o','n','M','o','d','e','l','.','A','p','p','I','n','f','o',0};
#endif
#endif /* RUNTIMECLASS_Windows_ApplicationModel_AppInfo_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.ApplicationModel.DesignMode
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_ApplicationModel_DesignMode_DEFINED
#define RUNTIMECLASS_Windows_ApplicationModel_DesignMode_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_ApplicationModel_DesignMode[] = {'W','i','n','d','o','w','s','.','A','p','p','l','i','c','a','t','i','o','n','M','o','d','e','l','.','D','e','s','i','g','n','M','o','d','e',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_ApplicationModel_DesignMode[] = L"Windows.ApplicationModel.DesignMode";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_ApplicationModel_DesignMode[] = {'W','i','n','d','o','w','s','.','A','p','p','l','i','c','a','t','i','o','n','M','o','d','e','l','.','D','e','s','i','g','n','M','o','d','e',0};
#endif
#endif /* RUNTIMECLASS_Windows_ApplicationModel_DesignMode_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.ApplicationModel.EnteredBackgroundEventArgs
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
#ifndef RUNTIMECLASS_Windows_ApplicationModel_EnteredBackgroundEventArgs_DEFINED
#define RUNTIMECLASS_Windows_ApplicationModel_EnteredBackgroundEventArgs_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_ApplicationModel_EnteredBackgroundEventArgs[] = {'W','i','n','d','o','w','s','.','A','p','p','l','i','c','a','t','i','o','n','M','o','d','e','l','.','E','n','t','e','r','e','d','B','a','c','k','g','r','o','u','n','d','E','v','e','n','t','A','r','g','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_ApplicationModel_EnteredBackgroundEventArgs[] = L"Windows.ApplicationModel.EnteredBackgroundEventArgs";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_ApplicationModel_EnteredBackgroundEventArgs[] = {'W','i','n','d','o','w','s','.','A','p','p','l','i','c','a','t','i','o','n','M','o','d','e','l','.','E','n','t','e','r','e','d','B','a','c','k','g','r','o','u','n','d','E','v','e','n','t','A','r','g','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_ApplicationModel_EnteredBackgroundEventArgs_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */

/*
 * Class Windows.ApplicationModel.LeavingBackgroundEventArgs
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
#ifndef RUNTIMECLASS_Windows_ApplicationModel_LeavingBackgroundEventArgs_DEFINED
#define RUNTIMECLASS_Windows_ApplicationModel_LeavingBackgroundEventArgs_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_ApplicationModel_LeavingBackgroundEventArgs[] = {'W','i','n','d','o','w','s','.','A','p','p','l','i','c','a','t','i','o','n','M','o','d','e','l','.','L','e','a','v','i','n','g','B','a','c','k','g','r','o','u','n','d','E','v','e','n','t','A','r','g','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_ApplicationModel_LeavingBackgroundEventArgs[] = L"Windows.ApplicationModel.LeavingBackgroundEventArgs";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_ApplicationModel_LeavingBackgroundEventArgs[] = {'W','i','n','d','o','w','s','.','A','p','p','l','i','c','a','t','i','o','n','M','o','d','e','l','.','L','e','a','v','i','n','g','B','a','c','k','g','r','o','u','n','d','E','v','e','n','t','A','r','g','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_ApplicationModel_LeavingBackgroundEventArgs_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */

/*
 * Class Windows.ApplicationModel.Package
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_ApplicationModel_Package_DEFINED
#define RUNTIMECLASS_Windows_ApplicationModel_Package_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_ApplicationModel_Package[] = {'W','i','n','d','o','w','s','.','A','p','p','l','i','c','a','t','i','o','n','M','o','d','e','l','.','P','a','c','k','a','g','e',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_ApplicationModel_Package[] = L"Windows.ApplicationModel.Package";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_ApplicationModel_Package[] = {'W','i','n','d','o','w','s','.','A','p','p','l','i','c','a','t','i','o','n','M','o','d','e','l','.','P','a','c','k','a','g','e',0};
#endif
#endif /* RUNTIMECLASS_Windows_ApplicationModel_Package_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.ApplicationModel.PackageStatus
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_ApplicationModel_PackageStatus_DEFINED
#define RUNTIMECLASS_Windows_ApplicationModel_PackageStatus_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_ApplicationModel_PackageStatus[] = {'W','i','n','d','o','w','s','.','A','p','p','l','i','c','a','t','i','o','n','M','o','d','e','l','.','P','a','c','k','a','g','e','S','t','a','t','u','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_ApplicationModel_PackageStatus[] = L"Windows.ApplicationModel.PackageStatus";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_ApplicationModel_PackageStatus[] = {'W','i','n','d','o','w','s','.','A','p','p','l','i','c','a','t','i','o','n','M','o','d','e','l','.','P','a','c','k','a','g','e','S','t','a','t','u','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_ApplicationModel_PackageStatus_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.ApplicationModel.PackageId
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_ApplicationModel_PackageId_DEFINED
#define RUNTIMECLASS_Windows_ApplicationModel_PackageId_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_ApplicationModel_PackageId[] = {'W','i','n','d','o','w','s','.','A','p','p','l','i','c','a','t','i','o','n','M','o','d','e','l','.','P','a','c','k','a','g','e','I','d',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_ApplicationModel_PackageId[] = L"Windows.ApplicationModel.PackageId";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_ApplicationModel_PackageId[] = {'W','i','n','d','o','w','s','.','A','p','p','l','i','c','a','t','i','o','n','M','o','d','e','l','.','P','a','c','k','a','g','e','I','d',0};
#endif
#endif /* RUNTIMECLASS_Windows_ApplicationModel_PackageId_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.ApplicationModel.SuspendingDeferral
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_ApplicationModel_SuspendingDeferral_DEFINED
#define RUNTIMECLASS_Windows_ApplicationModel_SuspendingDeferral_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_ApplicationModel_SuspendingDeferral[] = {'W','i','n','d','o','w','s','.','A','p','p','l','i','c','a','t','i','o','n','M','o','d','e','l','.','S','u','s','p','e','n','d','i','n','g','D','e','f','e','r','r','a','l',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_ApplicationModel_SuspendingDeferral[] = L"Windows.ApplicationModel.SuspendingDeferral";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_ApplicationModel_SuspendingDeferral[] = {'W','i','n','d','o','w','s','.','A','p','p','l','i','c','a','t','i','o','n','M','o','d','e','l','.','S','u','s','p','e','n','d','i','n','g','D','e','f','e','r','r','a','l',0};
#endif
#endif /* RUNTIMECLASS_Windows_ApplicationModel_SuspendingDeferral_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.ApplicationModel.SuspendingEventArgs
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_ApplicationModel_SuspendingEventArgs_DEFINED
#define RUNTIMECLASS_Windows_ApplicationModel_SuspendingEventArgs_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_ApplicationModel_SuspendingEventArgs[] = {'W','i','n','d','o','w','s','.','A','p','p','l','i','c','a','t','i','o','n','M','o','d','e','l','.','S','u','s','p','e','n','d','i','n','g','E','v','e','n','t','A','r','g','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_ApplicationModel_SuspendingEventArgs[] = L"Windows.ApplicationModel.SuspendingEventArgs";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_ApplicationModel_SuspendingEventArgs[] = {'W','i','n','d','o','w','s','.','A','p','p','l','i','c','a','t','i','o','n','M','o','d','e','l','.','S','u','s','p','e','n','d','i','n','g','E','v','e','n','t','A','r','g','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_ApplicationModel_SuspendingEventArgs_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.ApplicationModel.SuspendingOperation
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_ApplicationModel_SuspendingOperation_DEFINED
#define RUNTIMECLASS_Windows_ApplicationModel_SuspendingOperation_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_ApplicationModel_SuspendingOperation[] = {'W','i','n','d','o','w','s','.','A','p','p','l','i','c','a','t','i','o','n','M','o','d','e','l','.','S','u','s','p','e','n','d','i','n','g','O','p','e','r','a','t','i','o','n',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_ApplicationModel_SuspendingOperation[] = L"Windows.ApplicationModel.SuspendingOperation";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_ApplicationModel_SuspendingOperation[] = {'W','i','n','d','o','w','s','.','A','p','p','l','i','c','a','t','i','o','n','M','o','d','e','l','.','S','u','s','p','e','n','d','i','n','g','O','p','e','r','a','t','i','o','n',0};
#endif
#endif /* RUNTIMECLASS_Windows_ApplicationModel_SuspendingOperation_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IIterable<ABI::Windows::ApplicationModel::AppInfo* > interface
 */
#ifndef ____FIIterable_1_Windows__CApplicationModel__CAppInfo_INTERFACE_DEFINED__
#define ____FIIterable_1_Windows__CApplicationModel__CAppInfo_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterable_1_Windows__CApplicationModel__CAppInfo, 0x63d0bffe, 0x0e34, 0x55b3, 0x83,0xd5, 0x31,0x4c,0xaf,0xf2,0xb1,0x37);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("63d0bffe-0e34-55b3-83d5-314caff2b137")
                IIterable<ABI::Windows::ApplicationModel::AppInfo* > : IIterable_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::ApplicationModel::AppInfo*, ABI::Windows::ApplicationModel::IAppInfo* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterable_1_Windows__CApplicationModel__CAppInfo, 0x63d0bffe, 0x0e34, 0x55b3, 0x83,0xd5, 0x31,0x4c,0xaf,0xf2,0xb1,0x37)
#endif
#else
typedef struct __FIIterable_1_Windows__CApplicationModel__CAppInfoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterable_1_Windows__CApplicationModel__CAppInfo *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterable_1_Windows__CApplicationModel__CAppInfo *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterable_1_Windows__CApplicationModel__CAppInfo *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterable_1_Windows__CApplicationModel__CAppInfo *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterable_1_Windows__CApplicationModel__CAppInfo *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterable_1_Windows__CApplicationModel__CAppInfo *This,
        TrustLevel *trustLevel);

    /*** IIterable<ABI::Windows::ApplicationModel::AppInfo* > methods ***/
    HRESULT (STDMETHODCALLTYPE *First)(
        __FIIterable_1_Windows__CApplicationModel__CAppInfo *This,
        __FIIterator_1_Windows__CApplicationModel__CAppInfo **value);

    END_INTERFACE
} __FIIterable_1_Windows__CApplicationModel__CAppInfoVtbl;

interface __FIIterable_1_Windows__CApplicationModel__CAppInfo {
    CONST_VTBL __FIIterable_1_Windows__CApplicationModel__CAppInfoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterable_1_Windows__CApplicationModel__CAppInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterable_1_Windows__CApplicationModel__CAppInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterable_1_Windows__CApplicationModel__CAppInfo_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterable_1_Windows__CApplicationModel__CAppInfo_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterable_1_Windows__CApplicationModel__CAppInfo_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterable_1_Windows__CApplicationModel__CAppInfo_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterable<ABI::Windows::ApplicationModel::AppInfo* > methods ***/
#define __FIIterable_1_Windows__CApplicationModel__CAppInfo_First(This,value) (This)->lpVtbl->First(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterable_1_Windows__CApplicationModel__CAppInfo_QueryInterface(__FIIterable_1_Windows__CApplicationModel__CAppInfo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterable_1_Windows__CApplicationModel__CAppInfo_AddRef(__FIIterable_1_Windows__CApplicationModel__CAppInfo* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterable_1_Windows__CApplicationModel__CAppInfo_Release(__FIIterable_1_Windows__CApplicationModel__CAppInfo* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterable_1_Windows__CApplicationModel__CAppInfo_GetIids(__FIIterable_1_Windows__CApplicationModel__CAppInfo* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterable_1_Windows__CApplicationModel__CAppInfo_GetRuntimeClassName(__FIIterable_1_Windows__CApplicationModel__CAppInfo* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterable_1_Windows__CApplicationModel__CAppInfo_GetTrustLevel(__FIIterable_1_Windows__CApplicationModel__CAppInfo* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterable<ABI::Windows::ApplicationModel::AppInfo* > methods ***/
static inline HRESULT __FIIterable_1_Windows__CApplicationModel__CAppInfo_First(__FIIterable_1_Windows__CApplicationModel__CAppInfo* This,__FIIterator_1_Windows__CApplicationModel__CAppInfo **value) {
    return This->lpVtbl->First(This,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterable_AppInfo IID___FIIterable_1_Windows__CApplicationModel__CAppInfo
#define IIterable_AppInfoVtbl __FIIterable_1_Windows__CApplicationModel__CAppInfoVtbl
#define IIterable_AppInfo __FIIterable_1_Windows__CApplicationModel__CAppInfo
#define IIterable_AppInfo_QueryInterface __FIIterable_1_Windows__CApplicationModel__CAppInfo_QueryInterface
#define IIterable_AppInfo_AddRef __FIIterable_1_Windows__CApplicationModel__CAppInfo_AddRef
#define IIterable_AppInfo_Release __FIIterable_1_Windows__CApplicationModel__CAppInfo_Release
#define IIterable_AppInfo_GetIids __FIIterable_1_Windows__CApplicationModel__CAppInfo_GetIids
#define IIterable_AppInfo_GetRuntimeClassName __FIIterable_1_Windows__CApplicationModel__CAppInfo_GetRuntimeClassName
#define IIterable_AppInfo_GetTrustLevel __FIIterable_1_Windows__CApplicationModel__CAppInfo_GetTrustLevel
#define IIterable_AppInfo_First __FIIterable_1_Windows__CApplicationModel__CAppInfo_First
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterable_1_Windows__CApplicationModel__CAppInfo_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IIterable<ABI::Windows::ApplicationModel::Package* > interface
 */
#ifndef ____FIIterable_1_Windows__CApplicationModel__CPackage_INTERFACE_DEFINED__
#define ____FIIterable_1_Windows__CApplicationModel__CPackage_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterable_1_Windows__CApplicationModel__CPackage, 0x69ad6aa7, 0x0c49, 0x5f27, 0xa5,0xeb, 0xef,0x4d,0x59,0x46,0x7b,0x6d);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("69ad6aa7-0c49-5f27-a5eb-ef4d59467b6d")
                IIterable<ABI::Windows::ApplicationModel::Package* > : IIterable_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::ApplicationModel::Package*, ABI::Windows::ApplicationModel::IPackage* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterable_1_Windows__CApplicationModel__CPackage, 0x69ad6aa7, 0x0c49, 0x5f27, 0xa5,0xeb, 0xef,0x4d,0x59,0x46,0x7b,0x6d)
#endif
#else
typedef struct __FIIterable_1_Windows__CApplicationModel__CPackageVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterable_1_Windows__CApplicationModel__CPackage *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterable_1_Windows__CApplicationModel__CPackage *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterable_1_Windows__CApplicationModel__CPackage *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterable_1_Windows__CApplicationModel__CPackage *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterable_1_Windows__CApplicationModel__CPackage *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterable_1_Windows__CApplicationModel__CPackage *This,
        TrustLevel *trustLevel);

    /*** IIterable<ABI::Windows::ApplicationModel::Package* > methods ***/
    HRESULT (STDMETHODCALLTYPE *First)(
        __FIIterable_1_Windows__CApplicationModel__CPackage *This,
        __FIIterator_1_Windows__CApplicationModel__CPackage **value);

    END_INTERFACE
} __FIIterable_1_Windows__CApplicationModel__CPackageVtbl;

interface __FIIterable_1_Windows__CApplicationModel__CPackage {
    CONST_VTBL __FIIterable_1_Windows__CApplicationModel__CPackageVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterable_1_Windows__CApplicationModel__CPackage_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterable_1_Windows__CApplicationModel__CPackage_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterable_1_Windows__CApplicationModel__CPackage_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterable_1_Windows__CApplicationModel__CPackage_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterable_1_Windows__CApplicationModel__CPackage_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterable_1_Windows__CApplicationModel__CPackage_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterable<ABI::Windows::ApplicationModel::Package* > methods ***/
#define __FIIterable_1_Windows__CApplicationModel__CPackage_First(This,value) (This)->lpVtbl->First(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterable_1_Windows__CApplicationModel__CPackage_QueryInterface(__FIIterable_1_Windows__CApplicationModel__CPackage* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterable_1_Windows__CApplicationModel__CPackage_AddRef(__FIIterable_1_Windows__CApplicationModel__CPackage* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterable_1_Windows__CApplicationModel__CPackage_Release(__FIIterable_1_Windows__CApplicationModel__CPackage* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterable_1_Windows__CApplicationModel__CPackage_GetIids(__FIIterable_1_Windows__CApplicationModel__CPackage* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterable_1_Windows__CApplicationModel__CPackage_GetRuntimeClassName(__FIIterable_1_Windows__CApplicationModel__CPackage* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterable_1_Windows__CApplicationModel__CPackage_GetTrustLevel(__FIIterable_1_Windows__CApplicationModel__CPackage* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterable<ABI::Windows::ApplicationModel::Package* > methods ***/
static inline HRESULT __FIIterable_1_Windows__CApplicationModel__CPackage_First(__FIIterable_1_Windows__CApplicationModel__CPackage* This,__FIIterator_1_Windows__CApplicationModel__CPackage **value) {
    return This->lpVtbl->First(This,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterable_Package IID___FIIterable_1_Windows__CApplicationModel__CPackage
#define IIterable_PackageVtbl __FIIterable_1_Windows__CApplicationModel__CPackageVtbl
#define IIterable_Package __FIIterable_1_Windows__CApplicationModel__CPackage
#define IIterable_Package_QueryInterface __FIIterable_1_Windows__CApplicationModel__CPackage_QueryInterface
#define IIterable_Package_AddRef __FIIterable_1_Windows__CApplicationModel__CPackage_AddRef
#define IIterable_Package_Release __FIIterable_1_Windows__CApplicationModel__CPackage_Release
#define IIterable_Package_GetIids __FIIterable_1_Windows__CApplicationModel__CPackage_GetIids
#define IIterable_Package_GetRuntimeClassName __FIIterable_1_Windows__CApplicationModel__CPackage_GetRuntimeClassName
#define IIterable_Package_GetTrustLevel __FIIterable_1_Windows__CApplicationModel__CPackage_GetTrustLevel
#define IIterable_Package_First __FIIterable_1_Windows__CApplicationModel__CPackage_First
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterable_1_Windows__CApplicationModel__CPackage_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IIterator<ABI::Windows::ApplicationModel::AppInfo* > interface
 */
#ifndef ____FIIterator_1_Windows__CApplicationModel__CAppInfo_INTERFACE_DEFINED__
#define ____FIIterator_1_Windows__CApplicationModel__CAppInfo_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterator_1_Windows__CApplicationModel__CAppInfo, 0x69cec62c, 0x41eb, 0x5d69, 0xa4,0x75, 0x29,0xee,0x22,0x32,0x3d,0xd8);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("69cec62c-41eb-5d69-a475-29ee22323dd8")
                IIterator<ABI::Windows::ApplicationModel::AppInfo* > : IIterator_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::ApplicationModel::AppInfo*, ABI::Windows::ApplicationModel::IAppInfo* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterator_1_Windows__CApplicationModel__CAppInfo, 0x69cec62c, 0x41eb, 0x5d69, 0xa4,0x75, 0x29,0xee,0x22,0x32,0x3d,0xd8)
#endif
#else
typedef struct __FIIterator_1_Windows__CApplicationModel__CAppInfoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterator_1_Windows__CApplicationModel__CAppInfo *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterator_1_Windows__CApplicationModel__CAppInfo *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterator_1_Windows__CApplicationModel__CAppInfo *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterator_1_Windows__CApplicationModel__CAppInfo *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterator_1_Windows__CApplicationModel__CAppInfo *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterator_1_Windows__CApplicationModel__CAppInfo *This,
        TrustLevel *trustLevel);

    /*** IIterator<ABI::Windows::ApplicationModel::AppInfo* > methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Current)(
        __FIIterator_1_Windows__CApplicationModel__CAppInfo *This,
        __x_ABI_CWindows_CApplicationModel_CIAppInfo **value);

    HRESULT (STDMETHODCALLTYPE *get_HasCurrent)(
        __FIIterator_1_Windows__CApplicationModel__CAppInfo *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *MoveNext)(
        __FIIterator_1_Windows__CApplicationModel__CAppInfo *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIIterator_1_Windows__CApplicationModel__CAppInfo *This,
        UINT32 items_size,
        __x_ABI_CWindows_CApplicationModel_CIAppInfo **items,
        UINT32 *value);

    END_INTERFACE
} __FIIterator_1_Windows__CApplicationModel__CAppInfoVtbl;

interface __FIIterator_1_Windows__CApplicationModel__CAppInfo {
    CONST_VTBL __FIIterator_1_Windows__CApplicationModel__CAppInfoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterator_1_Windows__CApplicationModel__CAppInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterator_1_Windows__CApplicationModel__CAppInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterator_1_Windows__CApplicationModel__CAppInfo_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterator_1_Windows__CApplicationModel__CAppInfo_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterator_1_Windows__CApplicationModel__CAppInfo_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterator_1_Windows__CApplicationModel__CAppInfo_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterator<ABI::Windows::ApplicationModel::AppInfo* > methods ***/
#define __FIIterator_1_Windows__CApplicationModel__CAppInfo_get_Current(This,value) (This)->lpVtbl->get_Current(This,value)
#define __FIIterator_1_Windows__CApplicationModel__CAppInfo_get_HasCurrent(This,value) (This)->lpVtbl->get_HasCurrent(This,value)
#define __FIIterator_1_Windows__CApplicationModel__CAppInfo_MoveNext(This,value) (This)->lpVtbl->MoveNext(This,value)
#define __FIIterator_1_Windows__CApplicationModel__CAppInfo_GetMany(This,items_size,items,value) (This)->lpVtbl->GetMany(This,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterator_1_Windows__CApplicationModel__CAppInfo_QueryInterface(__FIIterator_1_Windows__CApplicationModel__CAppInfo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterator_1_Windows__CApplicationModel__CAppInfo_AddRef(__FIIterator_1_Windows__CApplicationModel__CAppInfo* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterator_1_Windows__CApplicationModel__CAppInfo_Release(__FIIterator_1_Windows__CApplicationModel__CAppInfo* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterator_1_Windows__CApplicationModel__CAppInfo_GetIids(__FIIterator_1_Windows__CApplicationModel__CAppInfo* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterator_1_Windows__CApplicationModel__CAppInfo_GetRuntimeClassName(__FIIterator_1_Windows__CApplicationModel__CAppInfo* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterator_1_Windows__CApplicationModel__CAppInfo_GetTrustLevel(__FIIterator_1_Windows__CApplicationModel__CAppInfo* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterator<ABI::Windows::ApplicationModel::AppInfo* > methods ***/
static inline HRESULT __FIIterator_1_Windows__CApplicationModel__CAppInfo_get_Current(__FIIterator_1_Windows__CApplicationModel__CAppInfo* This,__x_ABI_CWindows_CApplicationModel_CIAppInfo **value) {
    return This->lpVtbl->get_Current(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CApplicationModel__CAppInfo_get_HasCurrent(__FIIterator_1_Windows__CApplicationModel__CAppInfo* This,boolean *value) {
    return This->lpVtbl->get_HasCurrent(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CApplicationModel__CAppInfo_MoveNext(__FIIterator_1_Windows__CApplicationModel__CAppInfo* This,boolean *value) {
    return This->lpVtbl->MoveNext(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CApplicationModel__CAppInfo_GetMany(__FIIterator_1_Windows__CApplicationModel__CAppInfo* This,UINT32 items_size,__x_ABI_CWindows_CApplicationModel_CIAppInfo **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterator_AppInfo IID___FIIterator_1_Windows__CApplicationModel__CAppInfo
#define IIterator_AppInfoVtbl __FIIterator_1_Windows__CApplicationModel__CAppInfoVtbl
#define IIterator_AppInfo __FIIterator_1_Windows__CApplicationModel__CAppInfo
#define IIterator_AppInfo_QueryInterface __FIIterator_1_Windows__CApplicationModel__CAppInfo_QueryInterface
#define IIterator_AppInfo_AddRef __FIIterator_1_Windows__CApplicationModel__CAppInfo_AddRef
#define IIterator_AppInfo_Release __FIIterator_1_Windows__CApplicationModel__CAppInfo_Release
#define IIterator_AppInfo_GetIids __FIIterator_1_Windows__CApplicationModel__CAppInfo_GetIids
#define IIterator_AppInfo_GetRuntimeClassName __FIIterator_1_Windows__CApplicationModel__CAppInfo_GetRuntimeClassName
#define IIterator_AppInfo_GetTrustLevel __FIIterator_1_Windows__CApplicationModel__CAppInfo_GetTrustLevel
#define IIterator_AppInfo_get_Current __FIIterator_1_Windows__CApplicationModel__CAppInfo_get_Current
#define IIterator_AppInfo_get_HasCurrent __FIIterator_1_Windows__CApplicationModel__CAppInfo_get_HasCurrent
#define IIterator_AppInfo_MoveNext __FIIterator_1_Windows__CApplicationModel__CAppInfo_MoveNext
#define IIterator_AppInfo_GetMany __FIIterator_1_Windows__CApplicationModel__CAppInfo_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterator_1_Windows__CApplicationModel__CAppInfo_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IIterator<ABI::Windows::ApplicationModel::Package* > interface
 */
#ifndef ____FIIterator_1_Windows__CApplicationModel__CPackage_INTERFACE_DEFINED__
#define ____FIIterator_1_Windows__CApplicationModel__CPackage_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterator_1_Windows__CApplicationModel__CPackage, 0x0217f069, 0x025c, 0x5ee6, 0xa8,0x7f, 0xe7,0x82,0xe3,0xb6,0x23,0xae);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("0217f069-025c-5ee6-a87f-e782e3b623ae")
                IIterator<ABI::Windows::ApplicationModel::Package* > : IIterator_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::ApplicationModel::Package*, ABI::Windows::ApplicationModel::IPackage* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterator_1_Windows__CApplicationModel__CPackage, 0x0217f069, 0x025c, 0x5ee6, 0xa8,0x7f, 0xe7,0x82,0xe3,0xb6,0x23,0xae)
#endif
#else
typedef struct __FIIterator_1_Windows__CApplicationModel__CPackageVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterator_1_Windows__CApplicationModel__CPackage *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterator_1_Windows__CApplicationModel__CPackage *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterator_1_Windows__CApplicationModel__CPackage *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterator_1_Windows__CApplicationModel__CPackage *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterator_1_Windows__CApplicationModel__CPackage *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterator_1_Windows__CApplicationModel__CPackage *This,
        TrustLevel *trustLevel);

    /*** IIterator<ABI::Windows::ApplicationModel::Package* > methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Current)(
        __FIIterator_1_Windows__CApplicationModel__CPackage *This,
        __x_ABI_CWindows_CApplicationModel_CIPackage **value);

    HRESULT (STDMETHODCALLTYPE *get_HasCurrent)(
        __FIIterator_1_Windows__CApplicationModel__CPackage *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *MoveNext)(
        __FIIterator_1_Windows__CApplicationModel__CPackage *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIIterator_1_Windows__CApplicationModel__CPackage *This,
        UINT32 items_size,
        __x_ABI_CWindows_CApplicationModel_CIPackage **items,
        UINT32 *value);

    END_INTERFACE
} __FIIterator_1_Windows__CApplicationModel__CPackageVtbl;

interface __FIIterator_1_Windows__CApplicationModel__CPackage {
    CONST_VTBL __FIIterator_1_Windows__CApplicationModel__CPackageVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterator_1_Windows__CApplicationModel__CPackage_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterator_1_Windows__CApplicationModel__CPackage_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterator_1_Windows__CApplicationModel__CPackage_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterator_1_Windows__CApplicationModel__CPackage_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterator_1_Windows__CApplicationModel__CPackage_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterator_1_Windows__CApplicationModel__CPackage_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterator<ABI::Windows::ApplicationModel::Package* > methods ***/
#define __FIIterator_1_Windows__CApplicationModel__CPackage_get_Current(This,value) (This)->lpVtbl->get_Current(This,value)
#define __FIIterator_1_Windows__CApplicationModel__CPackage_get_HasCurrent(This,value) (This)->lpVtbl->get_HasCurrent(This,value)
#define __FIIterator_1_Windows__CApplicationModel__CPackage_MoveNext(This,value) (This)->lpVtbl->MoveNext(This,value)
#define __FIIterator_1_Windows__CApplicationModel__CPackage_GetMany(This,items_size,items,value) (This)->lpVtbl->GetMany(This,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterator_1_Windows__CApplicationModel__CPackage_QueryInterface(__FIIterator_1_Windows__CApplicationModel__CPackage* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterator_1_Windows__CApplicationModel__CPackage_AddRef(__FIIterator_1_Windows__CApplicationModel__CPackage* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterator_1_Windows__CApplicationModel__CPackage_Release(__FIIterator_1_Windows__CApplicationModel__CPackage* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterator_1_Windows__CApplicationModel__CPackage_GetIids(__FIIterator_1_Windows__CApplicationModel__CPackage* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterator_1_Windows__CApplicationModel__CPackage_GetRuntimeClassName(__FIIterator_1_Windows__CApplicationModel__CPackage* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterator_1_Windows__CApplicationModel__CPackage_GetTrustLevel(__FIIterator_1_Windows__CApplicationModel__CPackage* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterator<ABI::Windows::ApplicationModel::Package* > methods ***/
static inline HRESULT __FIIterator_1_Windows__CApplicationModel__CPackage_get_Current(__FIIterator_1_Windows__CApplicationModel__CPackage* This,__x_ABI_CWindows_CApplicationModel_CIPackage **value) {
    return This->lpVtbl->get_Current(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CApplicationModel__CPackage_get_HasCurrent(__FIIterator_1_Windows__CApplicationModel__CPackage* This,boolean *value) {
    return This->lpVtbl->get_HasCurrent(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CApplicationModel__CPackage_MoveNext(__FIIterator_1_Windows__CApplicationModel__CPackage* This,boolean *value) {
    return This->lpVtbl->MoveNext(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CApplicationModel__CPackage_GetMany(__FIIterator_1_Windows__CApplicationModel__CPackage* This,UINT32 items_size,__x_ABI_CWindows_CApplicationModel_CIPackage **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterator_Package IID___FIIterator_1_Windows__CApplicationModel__CPackage
#define IIterator_PackageVtbl __FIIterator_1_Windows__CApplicationModel__CPackageVtbl
#define IIterator_Package __FIIterator_1_Windows__CApplicationModel__CPackage
#define IIterator_Package_QueryInterface __FIIterator_1_Windows__CApplicationModel__CPackage_QueryInterface
#define IIterator_Package_AddRef __FIIterator_1_Windows__CApplicationModel__CPackage_AddRef
#define IIterator_Package_Release __FIIterator_1_Windows__CApplicationModel__CPackage_Release
#define IIterator_Package_GetIids __FIIterator_1_Windows__CApplicationModel__CPackage_GetIids
#define IIterator_Package_GetRuntimeClassName __FIIterator_1_Windows__CApplicationModel__CPackage_GetRuntimeClassName
#define IIterator_Package_GetTrustLevel __FIIterator_1_Windows__CApplicationModel__CPackage_GetTrustLevel
#define IIterator_Package_get_Current __FIIterator_1_Windows__CApplicationModel__CPackage_get_Current
#define IIterator_Package_get_HasCurrent __FIIterator_1_Windows__CApplicationModel__CPackage_get_HasCurrent
#define IIterator_Package_MoveNext __FIIterator_1_Windows__CApplicationModel__CPackage_MoveNext
#define IIterator_Package_GetMany __FIIterator_1_Windows__CApplicationModel__CPackage_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterator_1_Windows__CApplicationModel__CPackage_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVectorView<ABI::Windows::ApplicationModel::AppInfo* > interface
 */
#ifndef ____FIVectorView_1_Windows__CApplicationModel__CAppInfo_INTERFACE_DEFINED__
#define ____FIVectorView_1_Windows__CApplicationModel__CAppInfo_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIVectorView_1_Windows__CApplicationModel__CAppInfo, 0x8246ed12, 0x33e8, 0x52b3, 0xa5,0xc5, 0x19,0x77,0x9d,0xe9,0x99,0x9e);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("8246ed12-33e8-52b3-a5c5-19779de9999e")
                IVectorView<ABI::Windows::ApplicationModel::AppInfo* > : IVectorView_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::ApplicationModel::AppInfo*, ABI::Windows::ApplicationModel::IAppInfo* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIVectorView_1_Windows__CApplicationModel__CAppInfo, 0x8246ed12, 0x33e8, 0x52b3, 0xa5,0xc5, 0x19,0x77,0x9d,0xe9,0x99,0x9e)
#endif
#else
typedef struct __FIVectorView_1_Windows__CApplicationModel__CAppInfoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIVectorView_1_Windows__CApplicationModel__CAppInfo *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIVectorView_1_Windows__CApplicationModel__CAppInfo *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIVectorView_1_Windows__CApplicationModel__CAppInfo *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIVectorView_1_Windows__CApplicationModel__CAppInfo *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIVectorView_1_Windows__CApplicationModel__CAppInfo *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIVectorView_1_Windows__CApplicationModel__CAppInfo *This,
        TrustLevel *trustLevel);

    /*** IVectorView<ABI::Windows::ApplicationModel::AppInfo* > methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAt)(
        __FIVectorView_1_Windows__CApplicationModel__CAppInfo *This,
        UINT32 index,
        __x_ABI_CWindows_CApplicationModel_CIAppInfo **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIVectorView_1_Windows__CApplicationModel__CAppInfo *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *IndexOf)(
        __FIVectorView_1_Windows__CApplicationModel__CAppInfo *This,
        __x_ABI_CWindows_CApplicationModel_CIAppInfo *element,
        UINT32 *index,
        BOOLEAN *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIVectorView_1_Windows__CApplicationModel__CAppInfo *This,
        UINT32 start_index,
        UINT32 items_size,
        __x_ABI_CWindows_CApplicationModel_CIAppInfo **items,
        UINT32 *value);

    END_INTERFACE
} __FIVectorView_1_Windows__CApplicationModel__CAppInfoVtbl;

interface __FIVectorView_1_Windows__CApplicationModel__CAppInfo {
    CONST_VTBL __FIVectorView_1_Windows__CApplicationModel__CAppInfoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIVectorView_1_Windows__CApplicationModel__CAppInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIVectorView_1_Windows__CApplicationModel__CAppInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIVectorView_1_Windows__CApplicationModel__CAppInfo_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIVectorView_1_Windows__CApplicationModel__CAppInfo_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIVectorView_1_Windows__CApplicationModel__CAppInfo_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIVectorView_1_Windows__CApplicationModel__CAppInfo_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IVectorView<ABI::Windows::ApplicationModel::AppInfo* > methods ***/
#define __FIVectorView_1_Windows__CApplicationModel__CAppInfo_GetAt(This,index,value) (This)->lpVtbl->GetAt(This,index,value)
#define __FIVectorView_1_Windows__CApplicationModel__CAppInfo_get_Size(This,value) (This)->lpVtbl->get_Size(This,value)
#define __FIVectorView_1_Windows__CApplicationModel__CAppInfo_IndexOf(This,element,index,value) (This)->lpVtbl->IndexOf(This,element,index,value)
#define __FIVectorView_1_Windows__CApplicationModel__CAppInfo_GetMany(This,start_index,items_size,items,value) (This)->lpVtbl->GetMany(This,start_index,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIVectorView_1_Windows__CApplicationModel__CAppInfo_QueryInterface(__FIVectorView_1_Windows__CApplicationModel__CAppInfo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIVectorView_1_Windows__CApplicationModel__CAppInfo_AddRef(__FIVectorView_1_Windows__CApplicationModel__CAppInfo* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIVectorView_1_Windows__CApplicationModel__CAppInfo_Release(__FIVectorView_1_Windows__CApplicationModel__CAppInfo* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIVectorView_1_Windows__CApplicationModel__CAppInfo_GetIids(__FIVectorView_1_Windows__CApplicationModel__CAppInfo* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIVectorView_1_Windows__CApplicationModel__CAppInfo_GetRuntimeClassName(__FIVectorView_1_Windows__CApplicationModel__CAppInfo* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIVectorView_1_Windows__CApplicationModel__CAppInfo_GetTrustLevel(__FIVectorView_1_Windows__CApplicationModel__CAppInfo* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IVectorView<ABI::Windows::ApplicationModel::AppInfo* > methods ***/
static inline HRESULT __FIVectorView_1_Windows__CApplicationModel__CAppInfo_GetAt(__FIVectorView_1_Windows__CApplicationModel__CAppInfo* This,UINT32 index,__x_ABI_CWindows_CApplicationModel_CIAppInfo **value) {
    return This->lpVtbl->GetAt(This,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CApplicationModel__CAppInfo_get_Size(__FIVectorView_1_Windows__CApplicationModel__CAppInfo* This,UINT32 *value) {
    return This->lpVtbl->get_Size(This,value);
}
static inline HRESULT __FIVectorView_1_Windows__CApplicationModel__CAppInfo_IndexOf(__FIVectorView_1_Windows__CApplicationModel__CAppInfo* This,__x_ABI_CWindows_CApplicationModel_CIAppInfo *element,UINT32 *index,BOOLEAN *value) {
    return This->lpVtbl->IndexOf(This,element,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CApplicationModel__CAppInfo_GetMany(__FIVectorView_1_Windows__CApplicationModel__CAppInfo* This,UINT32 start_index,UINT32 items_size,__x_ABI_CWindows_CApplicationModel_CIAppInfo **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,start_index,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IVectorView_AppInfo IID___FIVectorView_1_Windows__CApplicationModel__CAppInfo
#define IVectorView_AppInfoVtbl __FIVectorView_1_Windows__CApplicationModel__CAppInfoVtbl
#define IVectorView_AppInfo __FIVectorView_1_Windows__CApplicationModel__CAppInfo
#define IVectorView_AppInfo_QueryInterface __FIVectorView_1_Windows__CApplicationModel__CAppInfo_QueryInterface
#define IVectorView_AppInfo_AddRef __FIVectorView_1_Windows__CApplicationModel__CAppInfo_AddRef
#define IVectorView_AppInfo_Release __FIVectorView_1_Windows__CApplicationModel__CAppInfo_Release
#define IVectorView_AppInfo_GetIids __FIVectorView_1_Windows__CApplicationModel__CAppInfo_GetIids
#define IVectorView_AppInfo_GetRuntimeClassName __FIVectorView_1_Windows__CApplicationModel__CAppInfo_GetRuntimeClassName
#define IVectorView_AppInfo_GetTrustLevel __FIVectorView_1_Windows__CApplicationModel__CAppInfo_GetTrustLevel
#define IVectorView_AppInfo_GetAt __FIVectorView_1_Windows__CApplicationModel__CAppInfo_GetAt
#define IVectorView_AppInfo_get_Size __FIVectorView_1_Windows__CApplicationModel__CAppInfo_get_Size
#define IVectorView_AppInfo_IndexOf __FIVectorView_1_Windows__CApplicationModel__CAppInfo_IndexOf
#define IVectorView_AppInfo_GetMany __FIVectorView_1_Windows__CApplicationModel__CAppInfo_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIVectorView_1_Windows__CApplicationModel__CAppInfo_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVectorView<ABI::Windows::ApplicationModel::Package* > interface
 */
#ifndef ____FIVectorView_1_Windows__CApplicationModel__CPackage_INTERFACE_DEFINED__
#define ____FIVectorView_1_Windows__CApplicationModel__CPackage_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIVectorView_1_Windows__CApplicationModel__CPackage, 0x0263c4d4, 0x195c, 0x5dc5, 0xa7,0xca, 0x68,0x06,0xce,0xca,0x42,0x0b);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("0263c4d4-195c-5dc5-a7ca-6806ceca420b")
                IVectorView<ABI::Windows::ApplicationModel::Package* > : IVectorView_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::ApplicationModel::Package*, ABI::Windows::ApplicationModel::IPackage* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIVectorView_1_Windows__CApplicationModel__CPackage, 0x0263c4d4, 0x195c, 0x5dc5, 0xa7,0xca, 0x68,0x06,0xce,0xca,0x42,0x0b)
#endif
#else
typedef struct __FIVectorView_1_Windows__CApplicationModel__CPackageVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIVectorView_1_Windows__CApplicationModel__CPackage *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIVectorView_1_Windows__CApplicationModel__CPackage *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIVectorView_1_Windows__CApplicationModel__CPackage *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIVectorView_1_Windows__CApplicationModel__CPackage *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIVectorView_1_Windows__CApplicationModel__CPackage *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIVectorView_1_Windows__CApplicationModel__CPackage *This,
        TrustLevel *trustLevel);

    /*** IVectorView<ABI::Windows::ApplicationModel::Package* > methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAt)(
        __FIVectorView_1_Windows__CApplicationModel__CPackage *This,
        UINT32 index,
        __x_ABI_CWindows_CApplicationModel_CIPackage **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIVectorView_1_Windows__CApplicationModel__CPackage *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *IndexOf)(
        __FIVectorView_1_Windows__CApplicationModel__CPackage *This,
        __x_ABI_CWindows_CApplicationModel_CIPackage *element,
        UINT32 *index,
        BOOLEAN *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIVectorView_1_Windows__CApplicationModel__CPackage *This,
        UINT32 start_index,
        UINT32 items_size,
        __x_ABI_CWindows_CApplicationModel_CIPackage **items,
        UINT32 *value);

    END_INTERFACE
} __FIVectorView_1_Windows__CApplicationModel__CPackageVtbl;

interface __FIVectorView_1_Windows__CApplicationModel__CPackage {
    CONST_VTBL __FIVectorView_1_Windows__CApplicationModel__CPackageVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIVectorView_1_Windows__CApplicationModel__CPackage_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIVectorView_1_Windows__CApplicationModel__CPackage_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIVectorView_1_Windows__CApplicationModel__CPackage_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIVectorView_1_Windows__CApplicationModel__CPackage_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIVectorView_1_Windows__CApplicationModel__CPackage_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIVectorView_1_Windows__CApplicationModel__CPackage_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IVectorView<ABI::Windows::ApplicationModel::Package* > methods ***/
#define __FIVectorView_1_Windows__CApplicationModel__CPackage_GetAt(This,index,value) (This)->lpVtbl->GetAt(This,index,value)
#define __FIVectorView_1_Windows__CApplicationModel__CPackage_get_Size(This,value) (This)->lpVtbl->get_Size(This,value)
#define __FIVectorView_1_Windows__CApplicationModel__CPackage_IndexOf(This,element,index,value) (This)->lpVtbl->IndexOf(This,element,index,value)
#define __FIVectorView_1_Windows__CApplicationModel__CPackage_GetMany(This,start_index,items_size,items,value) (This)->lpVtbl->GetMany(This,start_index,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIVectorView_1_Windows__CApplicationModel__CPackage_QueryInterface(__FIVectorView_1_Windows__CApplicationModel__CPackage* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIVectorView_1_Windows__CApplicationModel__CPackage_AddRef(__FIVectorView_1_Windows__CApplicationModel__CPackage* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIVectorView_1_Windows__CApplicationModel__CPackage_Release(__FIVectorView_1_Windows__CApplicationModel__CPackage* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIVectorView_1_Windows__CApplicationModel__CPackage_GetIids(__FIVectorView_1_Windows__CApplicationModel__CPackage* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIVectorView_1_Windows__CApplicationModel__CPackage_GetRuntimeClassName(__FIVectorView_1_Windows__CApplicationModel__CPackage* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIVectorView_1_Windows__CApplicationModel__CPackage_GetTrustLevel(__FIVectorView_1_Windows__CApplicationModel__CPackage* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IVectorView<ABI::Windows::ApplicationModel::Package* > methods ***/
static inline HRESULT __FIVectorView_1_Windows__CApplicationModel__CPackage_GetAt(__FIVectorView_1_Windows__CApplicationModel__CPackage* This,UINT32 index,__x_ABI_CWindows_CApplicationModel_CIPackage **value) {
    return This->lpVtbl->GetAt(This,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CApplicationModel__CPackage_get_Size(__FIVectorView_1_Windows__CApplicationModel__CPackage* This,UINT32 *value) {
    return This->lpVtbl->get_Size(This,value);
}
static inline HRESULT __FIVectorView_1_Windows__CApplicationModel__CPackage_IndexOf(__FIVectorView_1_Windows__CApplicationModel__CPackage* This,__x_ABI_CWindows_CApplicationModel_CIPackage *element,UINT32 *index,BOOLEAN *value) {
    return This->lpVtbl->IndexOf(This,element,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CApplicationModel__CPackage_GetMany(__FIVectorView_1_Windows__CApplicationModel__CPackage* This,UINT32 start_index,UINT32 items_size,__x_ABI_CWindows_CApplicationModel_CIPackage **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,start_index,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IVectorView_Package IID___FIVectorView_1_Windows__CApplicationModel__CPackage
#define IVectorView_PackageVtbl __FIVectorView_1_Windows__CApplicationModel__CPackageVtbl
#define IVectorView_Package __FIVectorView_1_Windows__CApplicationModel__CPackage
#define IVectorView_Package_QueryInterface __FIVectorView_1_Windows__CApplicationModel__CPackage_QueryInterface
#define IVectorView_Package_AddRef __FIVectorView_1_Windows__CApplicationModel__CPackage_AddRef
#define IVectorView_Package_Release __FIVectorView_1_Windows__CApplicationModel__CPackage_Release
#define IVectorView_Package_GetIids __FIVectorView_1_Windows__CApplicationModel__CPackage_GetIids
#define IVectorView_Package_GetRuntimeClassName __FIVectorView_1_Windows__CApplicationModel__CPackage_GetRuntimeClassName
#define IVectorView_Package_GetTrustLevel __FIVectorView_1_Windows__CApplicationModel__CPackage_GetTrustLevel
#define IVectorView_Package_GetAt __FIVectorView_1_Windows__CApplicationModel__CPackage_GetAt
#define IVectorView_Package_get_Size __FIVectorView_1_Windows__CApplicationModel__CPackage_get_Size
#define IVectorView_Package_IndexOf __FIVectorView_1_Windows__CApplicationModel__CPackage_IndexOf
#define IVectorView_Package_GetMany __FIVectorView_1_Windows__CApplicationModel__CPackage_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIVectorView_1_Windows__CApplicationModel__CPackage_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVectorView<ABI::Windows::ApplicationModel::Core::AppListEntry* > interface
 */
#ifndef ____FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_INTERFACE_DEFINED__
#define ____FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry, 0x920c8b92, 0xd5ef, 0x5899, 0x87,0x76, 0x2a,0xd9,0x7a,0xca,0x6e,0x1d);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("920c8b92-d5ef-5899-8776-2ad97aca6e1d")
                IVectorView<ABI::Windows::ApplicationModel::Core::AppListEntry* > : IVectorView_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::ApplicationModel::Core::AppListEntry*, ABI::Windows::ApplicationModel::Core::IAppListEntry* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry, 0x920c8b92, 0xd5ef, 0x5899, 0x87,0x76, 0x2a,0xd9,0x7a,0xca,0x6e,0x1d)
#endif
#else
typedef struct __FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntryVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry *This,
        TrustLevel *trustLevel);

    /*** IVectorView<ABI::Windows::ApplicationModel::Core::AppListEntry* > methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAt)(
        __FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry *This,
        UINT32 index,
        __x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntry **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *IndexOf)(
        __FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry *This,
        __x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntry *element,
        UINT32 *index,
        BOOLEAN *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry *This,
        UINT32 start_index,
        UINT32 items_size,
        __x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntry **items,
        UINT32 *value);

    END_INTERFACE
} __FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntryVtbl;

interface __FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry {
    CONST_VTBL __FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntryVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IVectorView<ABI::Windows::ApplicationModel::Core::AppListEntry* > methods ***/
#define __FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_GetAt(This,index,value) (This)->lpVtbl->GetAt(This,index,value)
#define __FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_get_Size(This,value) (This)->lpVtbl->get_Size(This,value)
#define __FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_IndexOf(This,element,index,value) (This)->lpVtbl->IndexOf(This,element,index,value)
#define __FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_GetMany(This,start_index,items_size,items,value) (This)->lpVtbl->GetMany(This,start_index,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_QueryInterface(__FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_AddRef(__FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_Release(__FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_GetIids(__FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_GetRuntimeClassName(__FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_GetTrustLevel(__FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IVectorView<ABI::Windows::ApplicationModel::Core::AppListEntry* > methods ***/
static inline HRESULT __FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_GetAt(__FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry* This,UINT32 index,__x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntry **value) {
    return This->lpVtbl->GetAt(This,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_get_Size(__FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry* This,UINT32 *value) {
    return This->lpVtbl->get_Size(This,value);
}
static inline HRESULT __FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_IndexOf(__FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry* This,__x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntry *element,UINT32 *index,BOOLEAN *value) {
    return This->lpVtbl->IndexOf(This,element,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_GetMany(__FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry* This,UINT32 start_index,UINT32 items_size,__x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntry **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,start_index,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IVectorView_AppListEntry IID___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry
#define IVectorView_AppListEntryVtbl __FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntryVtbl
#define IVectorView_AppListEntry __FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry
#define IVectorView_AppListEntry_QueryInterface __FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_QueryInterface
#define IVectorView_AppListEntry_AddRef __FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_AddRef
#define IVectorView_AppListEntry_Release __FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_Release
#define IVectorView_AppListEntry_GetIids __FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_GetIids
#define IVectorView_AppListEntry_GetRuntimeClassName __FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_GetRuntimeClassName
#define IVectorView_AppListEntry_GetTrustLevel __FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_GetTrustLevel
#define IVectorView_AppListEntry_GetAt __FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_GetAt
#define IVectorView_AppListEntry_get_Size __FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_get_Size
#define IVectorView_AppListEntry_IndexOf __FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_IndexOf
#define IVectorView_AppListEntry_GetMany __FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IEventHandler<ABI::Windows::ApplicationModel::EnteredBackgroundEventArgs* > interface
 */
#ifndef ____FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs_INTERFACE_DEFINED__
#define ____FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs, 0xe0739c32, 0xfc14, 0x5361, 0xa8,0xb3, 0x08,0x09,0x69,0x9f,0xbc,0xbd);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("e0739c32-fc14-5361-a8b3-0809699fbcbd")
            IEventHandler<ABI::Windows::ApplicationModel::EnteredBackgroundEventArgs* > : IEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::ApplicationModel::EnteredBackgroundEventArgs*, ABI::Windows::ApplicationModel::IEnteredBackgroundEventArgs* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs, 0xe0739c32, 0xfc14, 0x5361, 0xa8,0xb3, 0x08,0x09,0x69,0x9f,0xbc,0xbd)
#endif
#else
typedef struct __FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs *This);

    /*** IEventHandler<ABI::Windows::ApplicationModel::EnteredBackgroundEventArgs* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs *This,
        IInspectable *sender,
        __x_ABI_CWindows_CApplicationModel_CIEnteredBackgroundEventArgs *args);

    END_INTERFACE
} __FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgsVtbl;

interface __FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs {
    CONST_VTBL __FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** IEventHandler<ABI::Windows::ApplicationModel::EnteredBackgroundEventArgs* > methods ***/
#define __FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs_QueryInterface(__FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs_AddRef(__FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs_Release(__FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** IEventHandler<ABI::Windows::ApplicationModel::EnteredBackgroundEventArgs* > methods ***/
static inline HRESULT __FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs_Invoke(__FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs* This,IInspectable *sender,__x_ABI_CWindows_CApplicationModel_CIEnteredBackgroundEventArgs *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IEventHandler_EnteredBackgroundEventArgs IID___FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs
#define IEventHandler_EnteredBackgroundEventArgsVtbl __FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgsVtbl
#define IEventHandler_EnteredBackgroundEventArgs __FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs
#define IEventHandler_EnteredBackgroundEventArgs_QueryInterface __FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs_QueryInterface
#define IEventHandler_EnteredBackgroundEventArgs_AddRef __FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs_AddRef
#define IEventHandler_EnteredBackgroundEventArgs_Release __FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs_Release
#define IEventHandler_EnteredBackgroundEventArgs_Invoke __FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IEventHandler<ABI::Windows::ApplicationModel::LeavingBackgroundEventArgs* > interface
 */
#ifndef ____FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs_INTERFACE_DEFINED__
#define ____FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs, 0x9b6171c2, 0xabb2, 0x5194, 0xaf,0xc0, 0xce,0xf1,0x67,0xc4,0x24,0xeb);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("9b6171c2-abb2-5194-afc0-cef167c424eb")
            IEventHandler<ABI::Windows::ApplicationModel::LeavingBackgroundEventArgs* > : IEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::ApplicationModel::LeavingBackgroundEventArgs*, ABI::Windows::ApplicationModel::ILeavingBackgroundEventArgs* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs, 0x9b6171c2, 0xabb2, 0x5194, 0xaf,0xc0, 0xce,0xf1,0x67,0xc4,0x24,0xeb)
#endif
#else
typedef struct __FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs *This);

    /*** IEventHandler<ABI::Windows::ApplicationModel::LeavingBackgroundEventArgs* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs *This,
        IInspectable *sender,
        __x_ABI_CWindows_CApplicationModel_CILeavingBackgroundEventArgs *args);

    END_INTERFACE
} __FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgsVtbl;

interface __FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs {
    CONST_VTBL __FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** IEventHandler<ABI::Windows::ApplicationModel::LeavingBackgroundEventArgs* > methods ***/
#define __FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs_QueryInterface(__FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs_AddRef(__FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs_Release(__FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** IEventHandler<ABI::Windows::ApplicationModel::LeavingBackgroundEventArgs* > methods ***/
static inline HRESULT __FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs_Invoke(__FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs* This,IInspectable *sender,__x_ABI_CWindows_CApplicationModel_CILeavingBackgroundEventArgs *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IEventHandler_LeavingBackgroundEventArgs IID___FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs
#define IEventHandler_LeavingBackgroundEventArgsVtbl __FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgsVtbl
#define IEventHandler_LeavingBackgroundEventArgs __FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs
#define IEventHandler_LeavingBackgroundEventArgs_QueryInterface __FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs_QueryInterface
#define IEventHandler_LeavingBackgroundEventArgs_AddRef __FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs_AddRef
#define IEventHandler_LeavingBackgroundEventArgs_Release __FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs_Release
#define IEventHandler_LeavingBackgroundEventArgs_Invoke __FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IEventHandler<ABI::Windows::ApplicationModel::SuspendingEventArgs* > interface
 */
#ifndef ____FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs_INTERFACE_DEFINED__
#define ____FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs, 0x338579bf, 0x1a35, 0x5cc4, 0xa6,0x22, 0xa6,0xf3,0x84,0xfd,0x89,0x2c);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("338579bf-1a35-5cc4-a622-a6f384fd892c")
            IEventHandler<ABI::Windows::ApplicationModel::SuspendingEventArgs* > : IEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::ApplicationModel::SuspendingEventArgs*, ABI::Windows::ApplicationModel::ISuspendingEventArgs* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs, 0x338579bf, 0x1a35, 0x5cc4, 0xa6,0x22, 0xa6,0xf3,0x84,0xfd,0x89,0x2c)
#endif
#else
typedef struct __FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs *This);

    /*** IEventHandler<ABI::Windows::ApplicationModel::SuspendingEventArgs* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs *This,
        IInspectable *sender,
        __x_ABI_CWindows_CApplicationModel_CISuspendingEventArgs *args);

    END_INTERFACE
} __FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgsVtbl;

interface __FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs {
    CONST_VTBL __FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** IEventHandler<ABI::Windows::ApplicationModel::SuspendingEventArgs* > methods ***/
#define __FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs_QueryInterface(__FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs_AddRef(__FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs_Release(__FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** IEventHandler<ABI::Windows::ApplicationModel::SuspendingEventArgs* > methods ***/
static inline HRESULT __FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs_Invoke(__FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs* This,IInspectable *sender,__x_ABI_CWindows_CApplicationModel_CISuspendingEventArgs *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IEventHandler_SuspendingEventArgs IID___FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs
#define IEventHandler_SuspendingEventArgsVtbl __FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgsVtbl
#define IEventHandler_SuspendingEventArgs __FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs
#define IEventHandler_SuspendingEventArgs_QueryInterface __FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs_QueryInterface
#define IEventHandler_SuspendingEventArgs_AddRef __FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs_AddRef
#define IEventHandler_SuspendingEventArgs_Release __FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs_Release
#define IEventHandler_SuspendingEventArgs_Invoke __FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::ApplicationModel::AppInfo* >* > interface
 */
#ifndef ____FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo_INTERFACE_DEFINED__
#define ____FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo, 0x07543d91, 0x8610, 0x5152, 0xb0,0xe4, 0x43,0xd6,0xe4,0xcd,0xd0,0xcb);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("07543d91-8610-5152-b0e4-43d6e4cdd0cb")
            IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::ApplicationModel::AppInfo* >* > : IAsyncOperation_impl<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::ApplicationModel::AppInfo* >* >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo, 0x07543d91, 0x8610, 0x5152, 0xb0,0xe4, 0x43,0xd6,0xe4,0xcd,0xd0,0xcb)
#endif
#else
typedef struct __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo *This,
        TrustLevel *trustLevel);

    /*** IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::ApplicationModel::AppInfo* >* > methods ***/
    HRESULT (STDMETHODCALLTYPE *put_Completed)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo *This,
        __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo *handler);

    HRESULT (STDMETHODCALLTYPE *get_Completed)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo *This,
        __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo **handler);

    HRESULT (STDMETHODCALLTYPE *GetResults)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo *This,
        __FIVectorView_1_Windows__CApplicationModel__CAppInfo **results);

    END_INTERFACE
} __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfoVtbl;

interface __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo {
    CONST_VTBL __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::ApplicationModel::AppInfo* >* > methods ***/
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo_put_Completed(This,handler) (This)->lpVtbl->put_Completed(This,handler)
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo_get_Completed(This,handler) (This)->lpVtbl->get_Completed(This,handler)
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo_GetResults(This,results) (This)->lpVtbl->GetResults(This,results)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo_QueryInterface(__FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo_AddRef(__FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo_Release(__FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo_GetIids(__FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo_GetRuntimeClassName(__FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo_GetTrustLevel(__FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::ApplicationModel::AppInfo* >* > methods ***/
static inline HRESULT __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo_put_Completed(__FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo* This,__FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo *handler) {
    return This->lpVtbl->put_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo_get_Completed(__FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo* This,__FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo **handler) {
    return This->lpVtbl->get_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo_GetResults(__FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo* This,__FIVectorView_1_Windows__CApplicationModel__CAppInfo **results) {
    return This->lpVtbl->GetResults(This,results);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperation_IVectorView_AppInfo IID___FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo
#define IAsyncOperation_IVectorView_AppInfoVtbl __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfoVtbl
#define IAsyncOperation_IVectorView_AppInfo __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo
#define IAsyncOperation_IVectorView_AppInfo_QueryInterface __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo_QueryInterface
#define IAsyncOperation_IVectorView_AppInfo_AddRef __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo_AddRef
#define IAsyncOperation_IVectorView_AppInfo_Release __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo_Release
#define IAsyncOperation_IVectorView_AppInfo_GetIids __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo_GetIids
#define IAsyncOperation_IVectorView_AppInfo_GetRuntimeClassName __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo_GetRuntimeClassName
#define IAsyncOperation_IVectorView_AppInfo_GetTrustLevel __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo_GetTrustLevel
#define IAsyncOperation_IVectorView_AppInfo_put_Completed __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo_put_Completed
#define IAsyncOperation_IVectorView_AppInfo_get_Completed __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo_get_Completed
#define IAsyncOperation_IVectorView_AppInfo_GetResults __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo_GetResults
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::ApplicationModel::Core::AppListEntry* >* > interface
 */
#ifndef ____FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_INTERFACE_DEFINED__
#define ____FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry, 0xd3bcf8a0, 0x3538, 0x5dae, 0x98,0xd7, 0x1f,0x2a,0xb8,0x8c,0x3f,0x01);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("d3bcf8a0-3538-5dae-98d7-1f2ab88c3f01")
            IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::ApplicationModel::Core::AppListEntry* >* > : IAsyncOperation_impl<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::ApplicationModel::Core::AppListEntry* >* >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry, 0xd3bcf8a0, 0x3538, 0x5dae, 0x98,0xd7, 0x1f,0x2a,0xb8,0x8c,0x3f,0x01)
#endif
#else
typedef struct __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntryVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry *This,
        TrustLevel *trustLevel);

    /*** IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::ApplicationModel::Core::AppListEntry* >* > methods ***/
    HRESULT (STDMETHODCALLTYPE *put_Completed)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry *This,
        __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry *handler);

    HRESULT (STDMETHODCALLTYPE *get_Completed)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry *This,
        __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry **handler);

    HRESULT (STDMETHODCALLTYPE *GetResults)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry *This,
        __FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry **results);

    END_INTERFACE
} __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntryVtbl;

interface __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry {
    CONST_VTBL __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntryVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::ApplicationModel::Core::AppListEntry* >* > methods ***/
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_put_Completed(This,handler) (This)->lpVtbl->put_Completed(This,handler)
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_get_Completed(This,handler) (This)->lpVtbl->get_Completed(This,handler)
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_GetResults(This,results) (This)->lpVtbl->GetResults(This,results)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_QueryInterface(__FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_AddRef(__FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_Release(__FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_GetIids(__FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_GetRuntimeClassName(__FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_GetTrustLevel(__FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::ApplicationModel::Core::AppListEntry* >* > methods ***/
static inline HRESULT __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_put_Completed(__FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry* This,__FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry *handler) {
    return This->lpVtbl->put_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_get_Completed(__FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry* This,__FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry **handler) {
    return This->lpVtbl->get_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_GetResults(__FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry* This,__FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry **results) {
    return This->lpVtbl->GetResults(This,results);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperation_IVectorView_AppListEntry IID___FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry
#define IAsyncOperation_IVectorView_AppListEntryVtbl __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntryVtbl
#define IAsyncOperation_IVectorView_AppListEntry __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry
#define IAsyncOperation_IVectorView_AppListEntry_QueryInterface __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_QueryInterface
#define IAsyncOperation_IVectorView_AppListEntry_AddRef __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_AddRef
#define IAsyncOperation_IVectorView_AppListEntry_Release __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_Release
#define IAsyncOperation_IVectorView_AppListEntry_GetIids __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_GetIids
#define IAsyncOperation_IVectorView_AppListEntry_GetRuntimeClassName __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_GetRuntimeClassName
#define IAsyncOperation_IVectorView_AppListEntry_GetTrustLevel __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_GetTrustLevel
#define IAsyncOperation_IVectorView_AppListEntry_put_Completed __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_put_Completed
#define IAsyncOperation_IVectorView_AppListEntry_get_Completed __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_get_Completed
#define IAsyncOperation_IVectorView_AppListEntry_GetResults __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_GetResults
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::ApplicationModel::AppInfo* >* > interface
 */
#ifndef ____FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo_INTERFACE_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo, 0x07f25b6f, 0xf054, 0x5649, 0xa5,0xce, 0xb3,0x48,0xdd,0xc6,0x18,0xb6);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("07f25b6f-f054-5649-a5ce-b348ddc618b6")
            IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::ApplicationModel::AppInfo* >* > : IAsyncOperationCompletedHandler_impl<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::ApplicationModel::AppInfo* >* >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo, 0x07f25b6f, 0xf054, 0x5649, 0xa5,0xce, 0xb3,0x48,0xdd,0xc6,0x18,0xb6)
#endif
#else
typedef struct __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CAppInfoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo *This);

    /*** IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::ApplicationModel::AppInfo* >* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo *This,
        __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo *info,
        AsyncStatus status);

    END_INTERFACE
} __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CAppInfoVtbl;

interface __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo {
    CONST_VTBL __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CAppInfoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo_Release(This) (This)->lpVtbl->Release(This)
/*** IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::ApplicationModel::AppInfo* >* > methods ***/
#define __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo_Invoke(This,info,status) (This)->lpVtbl->Invoke(This,info,status)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo_QueryInterface(__FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo_AddRef(__FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo_Release(__FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo* This) {
    return This->lpVtbl->Release(This);
}
/*** IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::ApplicationModel::AppInfo* >* > methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo_Invoke(__FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo* This,__FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo *info,AsyncStatus status) {
    return This->lpVtbl->Invoke(This,info,status);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperationCompletedHandler_IVectorView_AppInfo IID___FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo
#define IAsyncOperationCompletedHandler_IVectorView_AppInfoVtbl __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CAppInfoVtbl
#define IAsyncOperationCompletedHandler_IVectorView_AppInfo __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo
#define IAsyncOperationCompletedHandler_IVectorView_AppInfo_QueryInterface __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo_QueryInterface
#define IAsyncOperationCompletedHandler_IVectorView_AppInfo_AddRef __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo_AddRef
#define IAsyncOperationCompletedHandler_IVectorView_AppInfo_Release __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo_Release
#define IAsyncOperationCompletedHandler_IVectorView_AppInfo_Invoke __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CAppInfo_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::ApplicationModel::Core::AppListEntry* >* > interface
 */
#ifndef ____FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_INTERFACE_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry, 0x51c74372, 0x9452, 0x57ce, 0x92,0x70, 0x76,0x20,0x09,0xfb,0xfe,0x4d);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("51c74372-9452-57ce-9270-762009fbfe4d")
            IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::ApplicationModel::Core::AppListEntry* >* > : IAsyncOperationCompletedHandler_impl<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::ApplicationModel::Core::AppListEntry* >* >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry, 0x51c74372, 0x9452, 0x57ce, 0x92,0x70, 0x76,0x20,0x09,0xfb,0xfe,0x4d)
#endif
#else
typedef struct __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntryVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry *This);

    /*** IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::ApplicationModel::Core::AppListEntry* >* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry *This,
        __FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry *info,
        AsyncStatus status);

    END_INTERFACE
} __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntryVtbl;

interface __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry {
    CONST_VTBL __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntryVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_Release(This) (This)->lpVtbl->Release(This)
/*** IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::ApplicationModel::Core::AppListEntry* >* > methods ***/
#define __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_Invoke(This,info,status) (This)->lpVtbl->Invoke(This,info,status)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_QueryInterface(__FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_AddRef(__FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_Release(__FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry* This) {
    return This->lpVtbl->Release(This);
}
/*** IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::ApplicationModel::Core::AppListEntry* >* > methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_Invoke(__FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry* This,__FIAsyncOperation_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry *info,AsyncStatus status) {
    return This->lpVtbl->Invoke(This,info,status);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperationCompletedHandler_IVectorView_AppListEntry IID___FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry
#define IAsyncOperationCompletedHandler_IVectorView_AppListEntryVtbl __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntryVtbl
#define IAsyncOperationCompletedHandler_IVectorView_AppListEntry __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry
#define IAsyncOperationCompletedHandler_IVectorView_AppListEntry_QueryInterface __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_QueryInterface
#define IAsyncOperationCompletedHandler_IVectorView_AppListEntry_AddRef __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_AddRef
#define IAsyncOperationCompletedHandler_IVectorView_AppListEntry_Release __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_Release
#define IAsyncOperationCompletedHandler_IVectorView_AppListEntry_Invoke __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CApplicationModel__CCore__CAppListEntry_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER HSTRING_UserSize     (ULONG *, ULONG, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserMarshal  (ULONG *, unsigned char *, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserUnmarshal(ULONG *, unsigned char *, HSTRING *);
void            __RPC_USER HSTRING_UserFree     (ULONG *, HSTRING *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __windows_applicationmodel_h__ */
