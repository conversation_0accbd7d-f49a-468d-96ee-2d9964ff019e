/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */

import "oaidl.idl";
import "ocidl.idl";
import "propsys.idl";

typedef enum tagWPD_STREAM_UNITS {
    WPD_STREAM_UNITS_BYTES = 0x0,
    WPD_STREAM_UNITS_FRAMES = 0x1,
    WPD_STREAM_UNITS_ROWS = 0x2,
    WPD_STREAM_UNITS_MILLISECONDS = 0x4,
    WPD_STREAM_UNITS_MICROSECONDS = 0x8,
} WPD_STREAM_UNITS;

interface IWpdSerializer;
interface IPortableDeviceValues;
interface IPortableDeviceKeyCollection;
interface IPortableDevicePropVariantCollection;
interface IPortableDeviceValuesCollection;

[
   object,
   uuid(b32f4002-bb27-45ff-af4f-06631c1e8dad),
   pointer_default(unique)
]
interface IWpdSerializer : IUnknown
{
   HRESULT GetIPortableDeviceValuesFromBuffer(
      [in, size_is(input_buffer_length)] BYTE *buffer,
      [in] DWORD input_buffer_length,
      [out] IPortableDeviceValues **params);

   HRESULT WriteIPortableDeviceValuesToBuffer(
      [in] DWORD output_buffer_length,
      [in] IPortableDeviceValues *results,
      [out, size_is(output_buffer_length), length_is(*bytes_written)] BYTE *buffer,
      [out] DWORD *bytes_written);

   HRESULT GetBufferFromIPortableDeviceValues(
      [in] IPortableDeviceValues *source,
      [out, size_is(, *buffer_size)] BYTE **buffer,
      [out] DWORD *buffer_size);

   HRESULT GetSerializedSize(
      [in] IPortableDeviceValues *source,
      [out] DWORD *size);
}

[
    uuid(6848f6f2-3155-4f86-b6f5-263eeeab3143),
    object
]
interface IPortableDeviceValues : IUnknown {
    HRESULT GetCount([in] DWORD *pcelt);

    HRESULT GetAt(
            [in] const DWORD index,
            [unique, out, in]  PROPERTYKEY *pKey,
            [unique, out, in]  PROPVARIANT *pValue);

    HRESULT SetValue(
            [in] REFPROPERTYKEY key,
            [in] const PROPVARIANT *pValue);

    HRESULT GetValue(
            [in] REFPROPERTYKEY key,
            [out] PROPVARIANT *pValue);

    HRESULT SetStringValue(
            [in]  REFPROPERTYKEY key,
            [in]  LPCWSTR Value);

    HRESULT GetStringValue(
            [in] REFPROPERTYKEY key,
            [out] LPWSTR *pValue);

    HRESULT SetUnsignedIntegerValue(
            [in] REFPROPERTYKEY key,
            [in] const ULONG Value);

    HRESULT GetUnsignedIntegerValue(
            [in]  REFPROPERTYKEY key,
            [out] ULONG *pValue);

    HRESULT SetSignedIntegerValue(
            [in] REFPROPERTYKEY key,
            [in] const LONG Value);

    HRESULT GetSignedIntegerValue(
            [in] REFPROPERTYKEY key,
            [out] LONG *pValue);

    HRESULT SetUnsignedLargeIntegerValue(
            [in] REFPROPERTYKEY key,
            [in] const ULONGLONG Value);

    HRESULT GetUnsignedLargeIntegerValue(
            [in] REFPROPERTYKEY key,
            [out] ULONGLONG *pValue);

    HRESULT SetSignedLargeIntegerValue(
            [in] REFPROPERTYKEY key,
            [in] const LONGLONG Value) = 0;

    HRESULT GetSignedLargeIntegerValue(
            [in] REFPROPERTYKEY key,
            [out] LONGLONG *pValue);

    HRESULT SetFloatValue(
            [in] REFPROPERTYKEY key,
            [in] const FLOAT Value);

    HRESULT GetFloatValue(
            [in] REFPROPERTYKEY key,
            [out] FLOAT *pValue);

    HRESULT SetErrorValue(
            [in] REFPROPERTYKEY key,
            [in] const HRESULT Value);

    HRESULT GetErrorValue(
            [in] REFPROPERTYKEY key,
            [out] HRESULT *pValue);

    HRESULT SetKeyValue(
            [in] REFPROPERTYKEY key,
            [in] REFPROPERTYKEY Value);

    HRESULT GetKeyValue(
            [in] REFPROPERTYKEY key,
            [out] PROPERTYKEY *pValue);

    HRESULT SetBoolValue(
            [in] REFPROPERTYKEY key,
            [in] const BOOL Value);

    HRESULT GetBoolValue(
            [in] REFPROPERTYKEY key,
            [out] BOOL *pValue);

    HRESULT SetIUnknownValue(
            [in] REFPROPERTYKEY key,
            [in] IUnknown *pValue);

    HRESULT GetIUnknownValue(
            [in] REFPROPERTYKEY key,
            [out] IUnknown **ppValue);

    HRESULT SetGuidValue(
            [in] REFPROPERTYKEY key,
            [in] REFGUID Value);

    HRESULT GetGuidValue(
            [in] REFPROPERTYKEY key,
            [out] GUID *pValue);

    HRESULT SetBufferValue(
            [in] REFPROPERTYKEY key,
            [size_is(cbValue), in] BYTE *pValue,
            [in] DWORD cbValue);

    HRESULT GetBufferValue(
            [in] REFPROPERTYKEY key,
            [size_is(*pcbValue), out] BYTE **ppValue,
            [out] DWORD *pcbValue);

    HRESULT SetIPortableDeviceValuesValue(
            [in] REFPROPERTYKEY key,
            [in] IPortableDeviceValues *pValue);

    HRESULT GetIPortableDeviceValuesValue(
            [in] REFPROPERTYKEY key,
            [out] IPortableDeviceValues **ppValue);

    HRESULT SetIPortableDevicePropVariantCollectionValue(
            [in] REFPROPERTYKEY key,
            [in] IPortableDevicePropVariantCollection *pValue);

    HRESULT GetIPortableDevicePropVariantCollectionValue(
            [in] REFPROPERTYKEY key,
            [out] IPortableDevicePropVariantCollection **ppValue);

    HRESULT SetIPortableDeviceKeyCollectionValue(
            [in] REFPROPERTYKEY key,
            [in] IPortableDeviceKeyCollection *pValue);

    HRESULT GetIPortableDeviceKeyCollectionValue(
            [in] REFPROPERTYKEY key,
            [out] IPortableDeviceKeyCollection **ppValue);

    HRESULT SetIPortableDeviceValuesCollectionValue(
            [in] REFPROPERTYKEY key,
            [in] IPortableDeviceValuesCollection *pValue);

    HRESULT GetIPortableDeviceValuesCollectionValue(
            [in] REFPROPERTYKEY key,
            [out] IPortableDeviceValuesCollection **ppValue);

    HRESULT RemoveValue([in] REFPROPERTYKEY key);
    HRESULT CopyValuesFromPropertyStore([in] IPropertyStore *pStore);
    HRESULT CopyValuesToPropertyStore([in] IPropertyStore *pStore);
    HRESULT Clear();
}

[
    uuid(dada2357-e0ad-492e-98db-dd61c53ba353),
    object
]
interface IPortableDeviceKeyCollection : IUnknown
{
    HRESULT GetCount([in] DWORD *pcElems);

    HRESULT GetAt(
            [in] const DWORD dwIndex,
            [in] PROPERTYKEY *pKey);

    HRESULT Add([in] REFPROPERTYKEY Key);
    HRESULT Clear();
    HRESULT RemoveAt([in] const DWORD dwIndex);
}

[
    uuid(89b2e422-4f1b-4316-bcef-a44afea83eb3),
    object
]
interface IPortableDevicePropVariantCollection : IUnknown
{
    HRESULT GetCount([out] DWORD *pcElems);
    
    HRESULT GetAt(
            [in] const DWORD dwIndex,
            [in] PROPVARIANT *pValue);

    HRESULT Add([in] const PROPVARIANT *pValue);
    HRESULT GetType([out] VARTYPE *pvt);
    HRESULT ChangeType([in] const VARTYPE vt);
    HRESULT Clear();
    HRESULT RemoveAt([in] const DWORD dwIndex);
}

[
    uuid(6e3f2d79-4e07-48c4-8208-d8c2e5af4a99),
    object
]
interface IPortableDeviceValuesCollection : IUnknown
{
    HRESULT GetCount([out] DWORD *pcElems);

    HRESULT GetAt(
            [in] DWORD dwIndex,
            [out] IPortableDeviceValues **ppValues);

    HRESULT Add([in] IPortableDeviceValues *pValues);
    HRESULT Clear();
    HRESULT RemoveAt([in] const DWORD dwIndex);
}
[
    uuid(2b00ba2f-e750-4beb-9235-97142ede1d3e),
    version(1.0)
]
library PortableDeviceTypesLib
{
    [
        uuid(0b91a74b-ad7c-4a9d-b563-29eef9167172)
    ]
    coclass WpdSerializer
    {
        [default] interface IWpdSerializer;
    }

    [
        uuid(0c15d503-d017-47ce-9016-7b3f978721cc)
    ]
    coclass PortableDeviceValues
    {
        [default] interface IPortableDeviceValues;
    }

    [
        uuid(de2d022d-2480-43be-97f0-d1fa2cf98f4f)
    ]
    coclass PortableDeviceKeyCollection
    {
        [default] interface IPortableDeviceKeyCollection;
    }

    [
        uuid(08a99e2f-6d6d-4b80-af5a-baf2bcbe4cb9)
    ]
    coclass PortableDevicePropVariantCollection
    {
        [default] interface IPortableDevicePropVariantCollection;
    }

    [
        uuid(3882134d-14cf-4220-9cb4-435f86d83f60)
    ]
    coclass PortableDeviceValuesCollection
    {
        [default] interface IPortableDeviceValuesCollection;
    }
}
