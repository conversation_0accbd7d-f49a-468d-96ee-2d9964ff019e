// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/debug_event.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fdebug_5fevent_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fdebug_5fevent_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/graph_debug_info.pb.h"
#include "tensorflow/core/framework/tensor.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fprotobuf_2fdebug_5fevent_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fprotobuf_2fdebug_5fevent_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fprotobuf_2fdebug_5fevent_2eproto;
namespace tensorflow {
class CodeLocation;
struct CodeLocationDefaultTypeInternal;
extern CodeLocationDefaultTypeInternal _CodeLocation_default_instance_;
class DebugEvent;
struct DebugEventDefaultTypeInternal;
extern DebugEventDefaultTypeInternal _DebugEvent_default_instance_;
class DebugMetadata;
struct DebugMetadataDefaultTypeInternal;
extern DebugMetadataDefaultTypeInternal _DebugMetadata_default_instance_;
class DebuggedDevice;
struct DebuggedDeviceDefaultTypeInternal;
extern DebuggedDeviceDefaultTypeInternal _DebuggedDevice_default_instance_;
class DebuggedGraph;
struct DebuggedGraphDefaultTypeInternal;
extern DebuggedGraphDefaultTypeInternal _DebuggedGraph_default_instance_;
class Execution;
struct ExecutionDefaultTypeInternal;
extern ExecutionDefaultTypeInternal _Execution_default_instance_;
class GraphExecutionTrace;
struct GraphExecutionTraceDefaultTypeInternal;
extern GraphExecutionTraceDefaultTypeInternal _GraphExecutionTrace_default_instance_;
class GraphOpCreation;
struct GraphOpCreationDefaultTypeInternal;
extern GraphOpCreationDefaultTypeInternal _GraphOpCreation_default_instance_;
class SourceFile;
struct SourceFileDefaultTypeInternal;
extern SourceFileDefaultTypeInternal _SourceFile_default_instance_;
class StackFrameWithId;
struct StackFrameWithIdDefaultTypeInternal;
extern StackFrameWithIdDefaultTypeInternal _StackFrameWithId_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::CodeLocation* Arena::CreateMaybeMessage<::tensorflow::CodeLocation>(Arena*);
template<> ::tensorflow::DebugEvent* Arena::CreateMaybeMessage<::tensorflow::DebugEvent>(Arena*);
template<> ::tensorflow::DebugMetadata* Arena::CreateMaybeMessage<::tensorflow::DebugMetadata>(Arena*);
template<> ::tensorflow::DebuggedDevice* Arena::CreateMaybeMessage<::tensorflow::DebuggedDevice>(Arena*);
template<> ::tensorflow::DebuggedGraph* Arena::CreateMaybeMessage<::tensorflow::DebuggedGraph>(Arena*);
template<> ::tensorflow::Execution* Arena::CreateMaybeMessage<::tensorflow::Execution>(Arena*);
template<> ::tensorflow::GraphExecutionTrace* Arena::CreateMaybeMessage<::tensorflow::GraphExecutionTrace>(Arena*);
template<> ::tensorflow::GraphOpCreation* Arena::CreateMaybeMessage<::tensorflow::GraphOpCreation>(Arena*);
template<> ::tensorflow::SourceFile* Arena::CreateMaybeMessage<::tensorflow::SourceFile>(Arena*);
template<> ::tensorflow::StackFrameWithId* Arena::CreateMaybeMessage<::tensorflow::StackFrameWithId>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

enum TensorDebugMode : int {
  UNSPECIFIED = 0,
  NO_TENSOR = 1,
  CURT_HEALTH = 2,
  CONCISE_HEALTH = 3,
  FULL_HEALTH = 4,
  SHAPE = 5,
  FULL_NUMERICS = 6,
  FULL_TENSOR = 7,
  REDUCE_INF_NAN_THREE_SLOTS = 8,
  TensorDebugMode_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  TensorDebugMode_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool TensorDebugMode_IsValid(int value);
constexpr TensorDebugMode TensorDebugMode_MIN = UNSPECIFIED;
constexpr TensorDebugMode TensorDebugMode_MAX = REDUCE_INF_NAN_THREE_SLOTS;
constexpr int TensorDebugMode_ARRAYSIZE = TensorDebugMode_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* TensorDebugMode_descriptor();
template<typename T>
inline const std::string& TensorDebugMode_Name(T enum_t_value) {
  static_assert(::std::is_same<T, TensorDebugMode>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function TensorDebugMode_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    TensorDebugMode_descriptor(), enum_t_value);
}
inline bool TensorDebugMode_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, TensorDebugMode* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<TensorDebugMode>(
    TensorDebugMode_descriptor(), name, value);
}
// ===================================================================

class DebugEvent final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.DebugEvent) */ {
 public:
  inline DebugEvent() : DebugEvent(nullptr) {}
  ~DebugEvent() override;
  explicit PROTOBUF_CONSTEXPR DebugEvent(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DebugEvent(const DebugEvent& from);
  DebugEvent(DebugEvent&& from) noexcept
    : DebugEvent() {
    *this = ::std::move(from);
  }

  inline DebugEvent& operator=(const DebugEvent& from) {
    CopyFrom(from);
    return *this;
  }
  inline DebugEvent& operator=(DebugEvent&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DebugEvent& default_instance() {
    return *internal_default_instance();
  }
  enum WhatCase {
    kDebugMetadata = 3,
    kSourceFile = 4,
    kStackFrameWithId = 6,
    kGraphOpCreation = 7,
    kDebuggedGraph = 8,
    kExecution = 9,
    kGraphExecutionTrace = 10,
    kGraphId = 11,
    kDebuggedDevice = 12,
    WHAT_NOT_SET = 0,
  };

  static inline const DebugEvent* internal_default_instance() {
    return reinterpret_cast<const DebugEvent*>(
               &_DebugEvent_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(DebugEvent& a, DebugEvent& b) {
    a.Swap(&b);
  }
  inline void Swap(DebugEvent* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DebugEvent* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DebugEvent* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DebugEvent>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DebugEvent& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const DebugEvent& from) {
    DebugEvent::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DebugEvent* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.DebugEvent";
  }
  protected:
  explicit DebugEvent(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kWallTimeFieldNumber = 1,
    kStepFieldNumber = 2,
    kDebugMetadataFieldNumber = 3,
    kSourceFileFieldNumber = 4,
    kStackFrameWithIdFieldNumber = 6,
    kGraphOpCreationFieldNumber = 7,
    kDebuggedGraphFieldNumber = 8,
    kExecutionFieldNumber = 9,
    kGraphExecutionTraceFieldNumber = 10,
    kGraphIdFieldNumber = 11,
    kDebuggedDeviceFieldNumber = 12,
  };
  // double wall_time = 1;
  void clear_wall_time();
  double wall_time() const;
  void set_wall_time(double value);
  private:
  double _internal_wall_time() const;
  void _internal_set_wall_time(double value);
  public:

  // int64 step = 2;
  void clear_step();
  int64_t step() const;
  void set_step(int64_t value);
  private:
  int64_t _internal_step() const;
  void _internal_set_step(int64_t value);
  public:

  // .tensorflow.DebugMetadata debug_metadata = 3;
  bool has_debug_metadata() const;
  private:
  bool _internal_has_debug_metadata() const;
  public:
  void clear_debug_metadata();
  const ::tensorflow::DebugMetadata& debug_metadata() const;
  PROTOBUF_NODISCARD ::tensorflow::DebugMetadata* release_debug_metadata();
  ::tensorflow::DebugMetadata* mutable_debug_metadata();
  void set_allocated_debug_metadata(::tensorflow::DebugMetadata* debug_metadata);
  private:
  const ::tensorflow::DebugMetadata& _internal_debug_metadata() const;
  ::tensorflow::DebugMetadata* _internal_mutable_debug_metadata();
  public:
  void unsafe_arena_set_allocated_debug_metadata(
      ::tensorflow::DebugMetadata* debug_metadata);
  ::tensorflow::DebugMetadata* unsafe_arena_release_debug_metadata();

  // .tensorflow.SourceFile source_file = 4;
  bool has_source_file() const;
  private:
  bool _internal_has_source_file() const;
  public:
  void clear_source_file();
  const ::tensorflow::SourceFile& source_file() const;
  PROTOBUF_NODISCARD ::tensorflow::SourceFile* release_source_file();
  ::tensorflow::SourceFile* mutable_source_file();
  void set_allocated_source_file(::tensorflow::SourceFile* source_file);
  private:
  const ::tensorflow::SourceFile& _internal_source_file() const;
  ::tensorflow::SourceFile* _internal_mutable_source_file();
  public:
  void unsafe_arena_set_allocated_source_file(
      ::tensorflow::SourceFile* source_file);
  ::tensorflow::SourceFile* unsafe_arena_release_source_file();

  // .tensorflow.StackFrameWithId stack_frame_with_id = 6;
  bool has_stack_frame_with_id() const;
  private:
  bool _internal_has_stack_frame_with_id() const;
  public:
  void clear_stack_frame_with_id();
  const ::tensorflow::StackFrameWithId& stack_frame_with_id() const;
  PROTOBUF_NODISCARD ::tensorflow::StackFrameWithId* release_stack_frame_with_id();
  ::tensorflow::StackFrameWithId* mutable_stack_frame_with_id();
  void set_allocated_stack_frame_with_id(::tensorflow::StackFrameWithId* stack_frame_with_id);
  private:
  const ::tensorflow::StackFrameWithId& _internal_stack_frame_with_id() const;
  ::tensorflow::StackFrameWithId* _internal_mutable_stack_frame_with_id();
  public:
  void unsafe_arena_set_allocated_stack_frame_with_id(
      ::tensorflow::StackFrameWithId* stack_frame_with_id);
  ::tensorflow::StackFrameWithId* unsafe_arena_release_stack_frame_with_id();

  // .tensorflow.GraphOpCreation graph_op_creation = 7;
  bool has_graph_op_creation() const;
  private:
  bool _internal_has_graph_op_creation() const;
  public:
  void clear_graph_op_creation();
  const ::tensorflow::GraphOpCreation& graph_op_creation() const;
  PROTOBUF_NODISCARD ::tensorflow::GraphOpCreation* release_graph_op_creation();
  ::tensorflow::GraphOpCreation* mutable_graph_op_creation();
  void set_allocated_graph_op_creation(::tensorflow::GraphOpCreation* graph_op_creation);
  private:
  const ::tensorflow::GraphOpCreation& _internal_graph_op_creation() const;
  ::tensorflow::GraphOpCreation* _internal_mutable_graph_op_creation();
  public:
  void unsafe_arena_set_allocated_graph_op_creation(
      ::tensorflow::GraphOpCreation* graph_op_creation);
  ::tensorflow::GraphOpCreation* unsafe_arena_release_graph_op_creation();

  // .tensorflow.DebuggedGraph debugged_graph = 8;
  bool has_debugged_graph() const;
  private:
  bool _internal_has_debugged_graph() const;
  public:
  void clear_debugged_graph();
  const ::tensorflow::DebuggedGraph& debugged_graph() const;
  PROTOBUF_NODISCARD ::tensorflow::DebuggedGraph* release_debugged_graph();
  ::tensorflow::DebuggedGraph* mutable_debugged_graph();
  void set_allocated_debugged_graph(::tensorflow::DebuggedGraph* debugged_graph);
  private:
  const ::tensorflow::DebuggedGraph& _internal_debugged_graph() const;
  ::tensorflow::DebuggedGraph* _internal_mutable_debugged_graph();
  public:
  void unsafe_arena_set_allocated_debugged_graph(
      ::tensorflow::DebuggedGraph* debugged_graph);
  ::tensorflow::DebuggedGraph* unsafe_arena_release_debugged_graph();

  // .tensorflow.Execution execution = 9;
  bool has_execution() const;
  private:
  bool _internal_has_execution() const;
  public:
  void clear_execution();
  const ::tensorflow::Execution& execution() const;
  PROTOBUF_NODISCARD ::tensorflow::Execution* release_execution();
  ::tensorflow::Execution* mutable_execution();
  void set_allocated_execution(::tensorflow::Execution* execution);
  private:
  const ::tensorflow::Execution& _internal_execution() const;
  ::tensorflow::Execution* _internal_mutable_execution();
  public:
  void unsafe_arena_set_allocated_execution(
      ::tensorflow::Execution* execution);
  ::tensorflow::Execution* unsafe_arena_release_execution();

  // .tensorflow.GraphExecutionTrace graph_execution_trace = 10;
  bool has_graph_execution_trace() const;
  private:
  bool _internal_has_graph_execution_trace() const;
  public:
  void clear_graph_execution_trace();
  const ::tensorflow::GraphExecutionTrace& graph_execution_trace() const;
  PROTOBUF_NODISCARD ::tensorflow::GraphExecutionTrace* release_graph_execution_trace();
  ::tensorflow::GraphExecutionTrace* mutable_graph_execution_trace();
  void set_allocated_graph_execution_trace(::tensorflow::GraphExecutionTrace* graph_execution_trace);
  private:
  const ::tensorflow::GraphExecutionTrace& _internal_graph_execution_trace() const;
  ::tensorflow::GraphExecutionTrace* _internal_mutable_graph_execution_trace();
  public:
  void unsafe_arena_set_allocated_graph_execution_trace(
      ::tensorflow::GraphExecutionTrace* graph_execution_trace);
  ::tensorflow::GraphExecutionTrace* unsafe_arena_release_graph_execution_trace();

  // string graph_id = 11;
  bool has_graph_id() const;
  private:
  bool _internal_has_graph_id() const;
  public:
  void clear_graph_id();
  const std::string& graph_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_graph_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_graph_id();
  PROTOBUF_NODISCARD std::string* release_graph_id();
  void set_allocated_graph_id(std::string* graph_id);
  private:
  const std::string& _internal_graph_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_graph_id(const std::string& value);
  std::string* _internal_mutable_graph_id();
  public:

  // .tensorflow.DebuggedDevice debugged_device = 12;
  bool has_debugged_device() const;
  private:
  bool _internal_has_debugged_device() const;
  public:
  void clear_debugged_device();
  const ::tensorflow::DebuggedDevice& debugged_device() const;
  PROTOBUF_NODISCARD ::tensorflow::DebuggedDevice* release_debugged_device();
  ::tensorflow::DebuggedDevice* mutable_debugged_device();
  void set_allocated_debugged_device(::tensorflow::DebuggedDevice* debugged_device);
  private:
  const ::tensorflow::DebuggedDevice& _internal_debugged_device() const;
  ::tensorflow::DebuggedDevice* _internal_mutable_debugged_device();
  public:
  void unsafe_arena_set_allocated_debugged_device(
      ::tensorflow::DebuggedDevice* debugged_device);
  ::tensorflow::DebuggedDevice* unsafe_arena_release_debugged_device();

  void clear_what();
  WhatCase what_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.DebugEvent)
 private:
  class _Internal;
  void set_has_debug_metadata();
  void set_has_source_file();
  void set_has_stack_frame_with_id();
  void set_has_graph_op_creation();
  void set_has_debugged_graph();
  void set_has_execution();
  void set_has_graph_execution_trace();
  void set_has_graph_id();
  void set_has_debugged_device();

  inline bool has_what() const;
  inline void clear_has_what();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    double wall_time_;
    int64_t step_;
    union WhatUnion {
      constexpr WhatUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      ::tensorflow::DebugMetadata* debug_metadata_;
      ::tensorflow::SourceFile* source_file_;
      ::tensorflow::StackFrameWithId* stack_frame_with_id_;
      ::tensorflow::GraphOpCreation* graph_op_creation_;
      ::tensorflow::DebuggedGraph* debugged_graph_;
      ::tensorflow::Execution* execution_;
      ::tensorflow::GraphExecutionTrace* graph_execution_trace_;
      ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr graph_id_;
      ::tensorflow::DebuggedDevice* debugged_device_;
    } what_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    uint32_t _oneof_case_[1];

  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fdebug_5fevent_2eproto;
};
// -------------------------------------------------------------------

class DebugMetadata final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.DebugMetadata) */ {
 public:
  inline DebugMetadata() : DebugMetadata(nullptr) {}
  ~DebugMetadata() override;
  explicit PROTOBUF_CONSTEXPR DebugMetadata(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DebugMetadata(const DebugMetadata& from);
  DebugMetadata(DebugMetadata&& from) noexcept
    : DebugMetadata() {
    *this = ::std::move(from);
  }

  inline DebugMetadata& operator=(const DebugMetadata& from) {
    CopyFrom(from);
    return *this;
  }
  inline DebugMetadata& operator=(DebugMetadata&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DebugMetadata& default_instance() {
    return *internal_default_instance();
  }
  static inline const DebugMetadata* internal_default_instance() {
    return reinterpret_cast<const DebugMetadata*>(
               &_DebugMetadata_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(DebugMetadata& a, DebugMetadata& b) {
    a.Swap(&b);
  }
  inline void Swap(DebugMetadata* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DebugMetadata* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DebugMetadata* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DebugMetadata>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DebugMetadata& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const DebugMetadata& from) {
    DebugMetadata::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DebugMetadata* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.DebugMetadata";
  }
  protected:
  explicit DebugMetadata(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTensorflowVersionFieldNumber = 1,
    kFileVersionFieldNumber = 2,
    kTfdbgRunIdFieldNumber = 3,
  };
  // string tensorflow_version = 1;
  void clear_tensorflow_version();
  const std::string& tensorflow_version() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_tensorflow_version(ArgT0&& arg0, ArgT... args);
  std::string* mutable_tensorflow_version();
  PROTOBUF_NODISCARD std::string* release_tensorflow_version();
  void set_allocated_tensorflow_version(std::string* tensorflow_version);
  private:
  const std::string& _internal_tensorflow_version() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_tensorflow_version(const std::string& value);
  std::string* _internal_mutable_tensorflow_version();
  public:

  // string file_version = 2;
  void clear_file_version();
  const std::string& file_version() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_file_version(ArgT0&& arg0, ArgT... args);
  std::string* mutable_file_version();
  PROTOBUF_NODISCARD std::string* release_file_version();
  void set_allocated_file_version(std::string* file_version);
  private:
  const std::string& _internal_file_version() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_file_version(const std::string& value);
  std::string* _internal_mutable_file_version();
  public:

  // string tfdbg_run_id = 3;
  void clear_tfdbg_run_id();
  const std::string& tfdbg_run_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_tfdbg_run_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_tfdbg_run_id();
  PROTOBUF_NODISCARD std::string* release_tfdbg_run_id();
  void set_allocated_tfdbg_run_id(std::string* tfdbg_run_id);
  private:
  const std::string& _internal_tfdbg_run_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_tfdbg_run_id(const std::string& value);
  std::string* _internal_mutable_tfdbg_run_id();
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.DebugMetadata)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr tensorflow_version_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr file_version_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr tfdbg_run_id_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fdebug_5fevent_2eproto;
};
// -------------------------------------------------------------------

class SourceFile final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.SourceFile) */ {
 public:
  inline SourceFile() : SourceFile(nullptr) {}
  ~SourceFile() override;
  explicit PROTOBUF_CONSTEXPR SourceFile(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SourceFile(const SourceFile& from);
  SourceFile(SourceFile&& from) noexcept
    : SourceFile() {
    *this = ::std::move(from);
  }

  inline SourceFile& operator=(const SourceFile& from) {
    CopyFrom(from);
    return *this;
  }
  inline SourceFile& operator=(SourceFile&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SourceFile& default_instance() {
    return *internal_default_instance();
  }
  static inline const SourceFile* internal_default_instance() {
    return reinterpret_cast<const SourceFile*>(
               &_SourceFile_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(SourceFile& a, SourceFile& b) {
    a.Swap(&b);
  }
  inline void Swap(SourceFile* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SourceFile* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SourceFile* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SourceFile>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SourceFile& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const SourceFile& from) {
    SourceFile::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SourceFile* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.SourceFile";
  }
  protected:
  explicit SourceFile(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLinesFieldNumber = 3,
    kFilePathFieldNumber = 1,
    kHostNameFieldNumber = 2,
  };
  // repeated string lines = 3;
  int lines_size() const;
  private:
  int _internal_lines_size() const;
  public:
  void clear_lines();
  const std::string& lines(int index) const;
  std::string* mutable_lines(int index);
  void set_lines(int index, const std::string& value);
  void set_lines(int index, std::string&& value);
  void set_lines(int index, const char* value);
  void set_lines(int index, const char* value, size_t size);
  std::string* add_lines();
  void add_lines(const std::string& value);
  void add_lines(std::string&& value);
  void add_lines(const char* value);
  void add_lines(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& lines() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_lines();
  private:
  const std::string& _internal_lines(int index) const;
  std::string* _internal_add_lines();
  public:

  // string file_path = 1;
  void clear_file_path();
  const std::string& file_path() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_file_path(ArgT0&& arg0, ArgT... args);
  std::string* mutable_file_path();
  PROTOBUF_NODISCARD std::string* release_file_path();
  void set_allocated_file_path(std::string* file_path);
  private:
  const std::string& _internal_file_path() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_file_path(const std::string& value);
  std::string* _internal_mutable_file_path();
  public:

  // string host_name = 2;
  void clear_host_name();
  const std::string& host_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_host_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_host_name();
  PROTOBUF_NODISCARD std::string* release_host_name();
  void set_allocated_host_name(std::string* host_name);
  private:
  const std::string& _internal_host_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_host_name(const std::string& value);
  std::string* _internal_mutable_host_name();
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.SourceFile)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> lines_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr file_path_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr host_name_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fdebug_5fevent_2eproto;
};
// -------------------------------------------------------------------

class StackFrameWithId final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.StackFrameWithId) */ {
 public:
  inline StackFrameWithId() : StackFrameWithId(nullptr) {}
  ~StackFrameWithId() override;
  explicit PROTOBUF_CONSTEXPR StackFrameWithId(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  StackFrameWithId(const StackFrameWithId& from);
  StackFrameWithId(StackFrameWithId&& from) noexcept
    : StackFrameWithId() {
    *this = ::std::move(from);
  }

  inline StackFrameWithId& operator=(const StackFrameWithId& from) {
    CopyFrom(from);
    return *this;
  }
  inline StackFrameWithId& operator=(StackFrameWithId&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const StackFrameWithId& default_instance() {
    return *internal_default_instance();
  }
  static inline const StackFrameWithId* internal_default_instance() {
    return reinterpret_cast<const StackFrameWithId*>(
               &_StackFrameWithId_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(StackFrameWithId& a, StackFrameWithId& b) {
    a.Swap(&b);
  }
  inline void Swap(StackFrameWithId* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(StackFrameWithId* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  StackFrameWithId* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<StackFrameWithId>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const StackFrameWithId& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const StackFrameWithId& from) {
    StackFrameWithId::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(StackFrameWithId* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.StackFrameWithId";
  }
  protected:
  explicit StackFrameWithId(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kIdFieldNumber = 1,
    kFileLineColFieldNumber = 2,
  };
  // string id = 1;
  void clear_id();
  const std::string& id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_id();
  PROTOBUF_NODISCARD std::string* release_id();
  void set_allocated_id(std::string* id);
  private:
  const std::string& _internal_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_id(const std::string& value);
  std::string* _internal_mutable_id();
  public:

  // .tensorflow.GraphDebugInfo.FileLineCol file_line_col = 2;
  bool has_file_line_col() const;
  private:
  bool _internal_has_file_line_col() const;
  public:
  void clear_file_line_col();
  const ::tensorflow::GraphDebugInfo_FileLineCol& file_line_col() const;
  PROTOBUF_NODISCARD ::tensorflow::GraphDebugInfo_FileLineCol* release_file_line_col();
  ::tensorflow::GraphDebugInfo_FileLineCol* mutable_file_line_col();
  void set_allocated_file_line_col(::tensorflow::GraphDebugInfo_FileLineCol* file_line_col);
  private:
  const ::tensorflow::GraphDebugInfo_FileLineCol& _internal_file_line_col() const;
  ::tensorflow::GraphDebugInfo_FileLineCol* _internal_mutable_file_line_col();
  public:
  void unsafe_arena_set_allocated_file_line_col(
      ::tensorflow::GraphDebugInfo_FileLineCol* file_line_col);
  ::tensorflow::GraphDebugInfo_FileLineCol* unsafe_arena_release_file_line_col();

  // @@protoc_insertion_point(class_scope:tensorflow.StackFrameWithId)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr id_;
    ::tensorflow::GraphDebugInfo_FileLineCol* file_line_col_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fdebug_5fevent_2eproto;
};
// -------------------------------------------------------------------

class CodeLocation final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.CodeLocation) */ {
 public:
  inline CodeLocation() : CodeLocation(nullptr) {}
  ~CodeLocation() override;
  explicit PROTOBUF_CONSTEXPR CodeLocation(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CodeLocation(const CodeLocation& from);
  CodeLocation(CodeLocation&& from) noexcept
    : CodeLocation() {
    *this = ::std::move(from);
  }

  inline CodeLocation& operator=(const CodeLocation& from) {
    CopyFrom(from);
    return *this;
  }
  inline CodeLocation& operator=(CodeLocation&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CodeLocation& default_instance() {
    return *internal_default_instance();
  }
  static inline const CodeLocation* internal_default_instance() {
    return reinterpret_cast<const CodeLocation*>(
               &_CodeLocation_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(CodeLocation& a, CodeLocation& b) {
    a.Swap(&b);
  }
  inline void Swap(CodeLocation* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CodeLocation* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CodeLocation* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CodeLocation>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CodeLocation& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CodeLocation& from) {
    CodeLocation::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CodeLocation* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CodeLocation";
  }
  protected:
  explicit CodeLocation(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kStackFrameIdsFieldNumber = 2,
    kHostNameFieldNumber = 1,
  };
  // repeated string stack_frame_ids = 2;
  int stack_frame_ids_size() const;
  private:
  int _internal_stack_frame_ids_size() const;
  public:
  void clear_stack_frame_ids();
  const std::string& stack_frame_ids(int index) const;
  std::string* mutable_stack_frame_ids(int index);
  void set_stack_frame_ids(int index, const std::string& value);
  void set_stack_frame_ids(int index, std::string&& value);
  void set_stack_frame_ids(int index, const char* value);
  void set_stack_frame_ids(int index, const char* value, size_t size);
  std::string* add_stack_frame_ids();
  void add_stack_frame_ids(const std::string& value);
  void add_stack_frame_ids(std::string&& value);
  void add_stack_frame_ids(const char* value);
  void add_stack_frame_ids(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& stack_frame_ids() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_stack_frame_ids();
  private:
  const std::string& _internal_stack_frame_ids(int index) const;
  std::string* _internal_add_stack_frame_ids();
  public:

  // string host_name = 1;
  void clear_host_name();
  const std::string& host_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_host_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_host_name();
  PROTOBUF_NODISCARD std::string* release_host_name();
  void set_allocated_host_name(std::string* host_name);
  private:
  const std::string& _internal_host_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_host_name(const std::string& value);
  std::string* _internal_mutable_host_name();
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.CodeLocation)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> stack_frame_ids_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr host_name_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fdebug_5fevent_2eproto;
};
// -------------------------------------------------------------------

class GraphOpCreation final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.GraphOpCreation) */ {
 public:
  inline GraphOpCreation() : GraphOpCreation(nullptr) {}
  ~GraphOpCreation() override;
  explicit PROTOBUF_CONSTEXPR GraphOpCreation(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GraphOpCreation(const GraphOpCreation& from);
  GraphOpCreation(GraphOpCreation&& from) noexcept
    : GraphOpCreation() {
    *this = ::std::move(from);
  }

  inline GraphOpCreation& operator=(const GraphOpCreation& from) {
    CopyFrom(from);
    return *this;
  }
  inline GraphOpCreation& operator=(GraphOpCreation&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GraphOpCreation& default_instance() {
    return *internal_default_instance();
  }
  static inline const GraphOpCreation* internal_default_instance() {
    return reinterpret_cast<const GraphOpCreation*>(
               &_GraphOpCreation_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(GraphOpCreation& a, GraphOpCreation& b) {
    a.Swap(&b);
  }
  inline void Swap(GraphOpCreation* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GraphOpCreation* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GraphOpCreation* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GraphOpCreation>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GraphOpCreation& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const GraphOpCreation& from) {
    GraphOpCreation::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GraphOpCreation* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.GraphOpCreation";
  }
  protected:
  explicit GraphOpCreation(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kInputNamesFieldNumber = 6,
    kOutputTensorIdsFieldNumber = 9,
    kOpTypeFieldNumber = 1,
    kOpNameFieldNumber = 2,
    kGraphNameFieldNumber = 3,
    kGraphIdFieldNumber = 4,
    kDeviceNameFieldNumber = 5,
    kCodeLocationFieldNumber = 8,
    kNumOutputsFieldNumber = 7,
  };
  // repeated string input_names = 6;
  int input_names_size() const;
  private:
  int _internal_input_names_size() const;
  public:
  void clear_input_names();
  const std::string& input_names(int index) const;
  std::string* mutable_input_names(int index);
  void set_input_names(int index, const std::string& value);
  void set_input_names(int index, std::string&& value);
  void set_input_names(int index, const char* value);
  void set_input_names(int index, const char* value, size_t size);
  std::string* add_input_names();
  void add_input_names(const std::string& value);
  void add_input_names(std::string&& value);
  void add_input_names(const char* value);
  void add_input_names(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& input_names() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_input_names();
  private:
  const std::string& _internal_input_names(int index) const;
  std::string* _internal_add_input_names();
  public:

  // repeated int32 output_tensor_ids = 9;
  int output_tensor_ids_size() const;
  private:
  int _internal_output_tensor_ids_size() const;
  public:
  void clear_output_tensor_ids();
  private:
  int32_t _internal_output_tensor_ids(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
      _internal_output_tensor_ids() const;
  void _internal_add_output_tensor_ids(int32_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
      _internal_mutable_output_tensor_ids();
  public:
  int32_t output_tensor_ids(int index) const;
  void set_output_tensor_ids(int index, int32_t value);
  void add_output_tensor_ids(int32_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
      output_tensor_ids() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
      mutable_output_tensor_ids();

  // string op_type = 1;
  void clear_op_type();
  const std::string& op_type() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_op_type(ArgT0&& arg0, ArgT... args);
  std::string* mutable_op_type();
  PROTOBUF_NODISCARD std::string* release_op_type();
  void set_allocated_op_type(std::string* op_type);
  private:
  const std::string& _internal_op_type() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_op_type(const std::string& value);
  std::string* _internal_mutable_op_type();
  public:

  // string op_name = 2;
  void clear_op_name();
  const std::string& op_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_op_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_op_name();
  PROTOBUF_NODISCARD std::string* release_op_name();
  void set_allocated_op_name(std::string* op_name);
  private:
  const std::string& _internal_op_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_op_name(const std::string& value);
  std::string* _internal_mutable_op_name();
  public:

  // string graph_name = 3;
  void clear_graph_name();
  const std::string& graph_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_graph_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_graph_name();
  PROTOBUF_NODISCARD std::string* release_graph_name();
  void set_allocated_graph_name(std::string* graph_name);
  private:
  const std::string& _internal_graph_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_graph_name(const std::string& value);
  std::string* _internal_mutable_graph_name();
  public:

  // string graph_id = 4;
  void clear_graph_id();
  const std::string& graph_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_graph_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_graph_id();
  PROTOBUF_NODISCARD std::string* release_graph_id();
  void set_allocated_graph_id(std::string* graph_id);
  private:
  const std::string& _internal_graph_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_graph_id(const std::string& value);
  std::string* _internal_mutable_graph_id();
  public:

  // string device_name = 5;
  void clear_device_name();
  const std::string& device_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_device_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_device_name();
  PROTOBUF_NODISCARD std::string* release_device_name();
  void set_allocated_device_name(std::string* device_name);
  private:
  const std::string& _internal_device_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_device_name(const std::string& value);
  std::string* _internal_mutable_device_name();
  public:

  // .tensorflow.CodeLocation code_location = 8;
  bool has_code_location() const;
  private:
  bool _internal_has_code_location() const;
  public:
  void clear_code_location();
  const ::tensorflow::CodeLocation& code_location() const;
  PROTOBUF_NODISCARD ::tensorflow::CodeLocation* release_code_location();
  ::tensorflow::CodeLocation* mutable_code_location();
  void set_allocated_code_location(::tensorflow::CodeLocation* code_location);
  private:
  const ::tensorflow::CodeLocation& _internal_code_location() const;
  ::tensorflow::CodeLocation* _internal_mutable_code_location();
  public:
  void unsafe_arena_set_allocated_code_location(
      ::tensorflow::CodeLocation* code_location);
  ::tensorflow::CodeLocation* unsafe_arena_release_code_location();

  // int32 num_outputs = 7;
  void clear_num_outputs();
  int32_t num_outputs() const;
  void set_num_outputs(int32_t value);
  private:
  int32_t _internal_num_outputs() const;
  void _internal_set_num_outputs(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.GraphOpCreation)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> input_names_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t > output_tensor_ids_;
    mutable std::atomic<int> _output_tensor_ids_cached_byte_size_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr op_type_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr op_name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr graph_name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr graph_id_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr device_name_;
    ::tensorflow::CodeLocation* code_location_;
    int32_t num_outputs_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fdebug_5fevent_2eproto;
};
// -------------------------------------------------------------------

class DebuggedGraph final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.DebuggedGraph) */ {
 public:
  inline DebuggedGraph() : DebuggedGraph(nullptr) {}
  ~DebuggedGraph() override;
  explicit PROTOBUF_CONSTEXPR DebuggedGraph(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DebuggedGraph(const DebuggedGraph& from);
  DebuggedGraph(DebuggedGraph&& from) noexcept
    : DebuggedGraph() {
    *this = ::std::move(from);
  }

  inline DebuggedGraph& operator=(const DebuggedGraph& from) {
    CopyFrom(from);
    return *this;
  }
  inline DebuggedGraph& operator=(DebuggedGraph&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DebuggedGraph& default_instance() {
    return *internal_default_instance();
  }
  static inline const DebuggedGraph* internal_default_instance() {
    return reinterpret_cast<const DebuggedGraph*>(
               &_DebuggedGraph_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(DebuggedGraph& a, DebuggedGraph& b) {
    a.Swap(&b);
  }
  inline void Swap(DebuggedGraph* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DebuggedGraph* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DebuggedGraph* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DebuggedGraph>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DebuggedGraph& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const DebuggedGraph& from) {
    DebuggedGraph::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DebuggedGraph* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.DebuggedGraph";
  }
  protected:
  explicit DebuggedGraph(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kInstrumentedOpsFieldNumber = 3,
    kGraphIdFieldNumber = 1,
    kGraphNameFieldNumber = 2,
    kOriginalGraphDefFieldNumber = 4,
    kInstrumentedGraphDefFieldNumber = 5,
    kOuterContextIdFieldNumber = 6,
  };
  // repeated string instrumented_ops = 3;
  int instrumented_ops_size() const;
  private:
  int _internal_instrumented_ops_size() const;
  public:
  void clear_instrumented_ops();
  const std::string& instrumented_ops(int index) const;
  std::string* mutable_instrumented_ops(int index);
  void set_instrumented_ops(int index, const std::string& value);
  void set_instrumented_ops(int index, std::string&& value);
  void set_instrumented_ops(int index, const char* value);
  void set_instrumented_ops(int index, const char* value, size_t size);
  std::string* add_instrumented_ops();
  void add_instrumented_ops(const std::string& value);
  void add_instrumented_ops(std::string&& value);
  void add_instrumented_ops(const char* value);
  void add_instrumented_ops(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& instrumented_ops() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_instrumented_ops();
  private:
  const std::string& _internal_instrumented_ops(int index) const;
  std::string* _internal_add_instrumented_ops();
  public:

  // string graph_id = 1;
  void clear_graph_id();
  const std::string& graph_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_graph_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_graph_id();
  PROTOBUF_NODISCARD std::string* release_graph_id();
  void set_allocated_graph_id(std::string* graph_id);
  private:
  const std::string& _internal_graph_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_graph_id(const std::string& value);
  std::string* _internal_mutable_graph_id();
  public:

  // string graph_name = 2;
  void clear_graph_name();
  const std::string& graph_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_graph_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_graph_name();
  PROTOBUF_NODISCARD std::string* release_graph_name();
  void set_allocated_graph_name(std::string* graph_name);
  private:
  const std::string& _internal_graph_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_graph_name(const std::string& value);
  std::string* _internal_mutable_graph_name();
  public:

  // bytes original_graph_def = 4;
  void clear_original_graph_def();
  const std::string& original_graph_def() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_original_graph_def(ArgT0&& arg0, ArgT... args);
  std::string* mutable_original_graph_def();
  PROTOBUF_NODISCARD std::string* release_original_graph_def();
  void set_allocated_original_graph_def(std::string* original_graph_def);
  private:
  const std::string& _internal_original_graph_def() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_original_graph_def(const std::string& value);
  std::string* _internal_mutable_original_graph_def();
  public:

  // bytes instrumented_graph_def = 5;
  void clear_instrumented_graph_def();
  const std::string& instrumented_graph_def() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_instrumented_graph_def(ArgT0&& arg0, ArgT... args);
  std::string* mutable_instrumented_graph_def();
  PROTOBUF_NODISCARD std::string* release_instrumented_graph_def();
  void set_allocated_instrumented_graph_def(std::string* instrumented_graph_def);
  private:
  const std::string& _internal_instrumented_graph_def() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_instrumented_graph_def(const std::string& value);
  std::string* _internal_mutable_instrumented_graph_def();
  public:

  // string outer_context_id = 6;
  void clear_outer_context_id();
  const std::string& outer_context_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_outer_context_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_outer_context_id();
  PROTOBUF_NODISCARD std::string* release_outer_context_id();
  void set_allocated_outer_context_id(std::string* outer_context_id);
  private:
  const std::string& _internal_outer_context_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_outer_context_id(const std::string& value);
  std::string* _internal_mutable_outer_context_id();
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.DebuggedGraph)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> instrumented_ops_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr graph_id_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr graph_name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr original_graph_def_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr instrumented_graph_def_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr outer_context_id_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fdebug_5fevent_2eproto;
};
// -------------------------------------------------------------------

class DebuggedDevice final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.DebuggedDevice) */ {
 public:
  inline DebuggedDevice() : DebuggedDevice(nullptr) {}
  ~DebuggedDevice() override;
  explicit PROTOBUF_CONSTEXPR DebuggedDevice(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DebuggedDevice(const DebuggedDevice& from);
  DebuggedDevice(DebuggedDevice&& from) noexcept
    : DebuggedDevice() {
    *this = ::std::move(from);
  }

  inline DebuggedDevice& operator=(const DebuggedDevice& from) {
    CopyFrom(from);
    return *this;
  }
  inline DebuggedDevice& operator=(DebuggedDevice&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DebuggedDevice& default_instance() {
    return *internal_default_instance();
  }
  static inline const DebuggedDevice* internal_default_instance() {
    return reinterpret_cast<const DebuggedDevice*>(
               &_DebuggedDevice_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(DebuggedDevice& a, DebuggedDevice& b) {
    a.Swap(&b);
  }
  inline void Swap(DebuggedDevice* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DebuggedDevice* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DebuggedDevice* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DebuggedDevice>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DebuggedDevice& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const DebuggedDevice& from) {
    DebuggedDevice::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DebuggedDevice* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.DebuggedDevice";
  }
  protected:
  explicit DebuggedDevice(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDeviceNameFieldNumber = 1,
    kDeviceIdFieldNumber = 2,
  };
  // string device_name = 1;
  void clear_device_name();
  const std::string& device_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_device_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_device_name();
  PROTOBUF_NODISCARD std::string* release_device_name();
  void set_allocated_device_name(std::string* device_name);
  private:
  const std::string& _internal_device_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_device_name(const std::string& value);
  std::string* _internal_mutable_device_name();
  public:

  // int32 device_id = 2;
  void clear_device_id();
  int32_t device_id() const;
  void set_device_id(int32_t value);
  private:
  int32_t _internal_device_id() const;
  void _internal_set_device_id(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.DebuggedDevice)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr device_name_;
    int32_t device_id_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fdebug_5fevent_2eproto;
};
// -------------------------------------------------------------------

class Execution final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.Execution) */ {
 public:
  inline Execution() : Execution(nullptr) {}
  ~Execution() override;
  explicit PROTOBUF_CONSTEXPR Execution(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Execution(const Execution& from);
  Execution(Execution&& from) noexcept
    : Execution() {
    *this = ::std::move(from);
  }

  inline Execution& operator=(const Execution& from) {
    CopyFrom(from);
    return *this;
  }
  inline Execution& operator=(Execution&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Execution& default_instance() {
    return *internal_default_instance();
  }
  static inline const Execution* internal_default_instance() {
    return reinterpret_cast<const Execution*>(
               &_Execution_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(Execution& a, Execution& b) {
    a.Swap(&b);
  }
  inline void Swap(Execution* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Execution* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Execution* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Execution>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Execution& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Execution& from) {
    Execution::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Execution* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.Execution";
  }
  protected:
  explicit Execution(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kInputTensorIdsFieldNumber = 4,
    kOutputTensorIdsFieldNumber = 5,
    kTensorProtosFieldNumber = 7,
    kOutputTensorDeviceIdsFieldNumber = 9,
    kOpTypeFieldNumber = 1,
    kGraphIdFieldNumber = 3,
    kCodeLocationFieldNumber = 8,
    kNumOutputsFieldNumber = 2,
    kTensorDebugModeFieldNumber = 6,
  };
  // repeated int64 input_tensor_ids = 4;
  int input_tensor_ids_size() const;
  private:
  int _internal_input_tensor_ids_size() const;
  public:
  void clear_input_tensor_ids();
  private:
  int64_t _internal_input_tensor_ids(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      _internal_input_tensor_ids() const;
  void _internal_add_input_tensor_ids(int64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      _internal_mutable_input_tensor_ids();
  public:
  int64_t input_tensor_ids(int index) const;
  void set_input_tensor_ids(int index, int64_t value);
  void add_input_tensor_ids(int64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      input_tensor_ids() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      mutable_input_tensor_ids();

  // repeated int64 output_tensor_ids = 5;
  int output_tensor_ids_size() const;
  private:
  int _internal_output_tensor_ids_size() const;
  public:
  void clear_output_tensor_ids();
  private:
  int64_t _internal_output_tensor_ids(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      _internal_output_tensor_ids() const;
  void _internal_add_output_tensor_ids(int64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      _internal_mutable_output_tensor_ids();
  public:
  int64_t output_tensor_ids(int index) const;
  void set_output_tensor_ids(int index, int64_t value);
  void add_output_tensor_ids(int64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      output_tensor_ids() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      mutable_output_tensor_ids();

  // repeated .tensorflow.TensorProto tensor_protos = 7;
  int tensor_protos_size() const;
  private:
  int _internal_tensor_protos_size() const;
  public:
  void clear_tensor_protos();
  ::tensorflow::TensorProto* mutable_tensor_protos(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >*
      mutable_tensor_protos();
  private:
  const ::tensorflow::TensorProto& _internal_tensor_protos(int index) const;
  ::tensorflow::TensorProto* _internal_add_tensor_protos();
  public:
  const ::tensorflow::TensorProto& tensor_protos(int index) const;
  ::tensorflow::TensorProto* add_tensor_protos();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >&
      tensor_protos() const;

  // repeated int32 output_tensor_device_ids = 9;
  int output_tensor_device_ids_size() const;
  private:
  int _internal_output_tensor_device_ids_size() const;
  public:
  void clear_output_tensor_device_ids();
  private:
  int32_t _internal_output_tensor_device_ids(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
      _internal_output_tensor_device_ids() const;
  void _internal_add_output_tensor_device_ids(int32_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
      _internal_mutable_output_tensor_device_ids();
  public:
  int32_t output_tensor_device_ids(int index) const;
  void set_output_tensor_device_ids(int index, int32_t value);
  void add_output_tensor_device_ids(int32_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
      output_tensor_device_ids() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
      mutable_output_tensor_device_ids();

  // string op_type = 1;
  void clear_op_type();
  const std::string& op_type() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_op_type(ArgT0&& arg0, ArgT... args);
  std::string* mutable_op_type();
  PROTOBUF_NODISCARD std::string* release_op_type();
  void set_allocated_op_type(std::string* op_type);
  private:
  const std::string& _internal_op_type() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_op_type(const std::string& value);
  std::string* _internal_mutable_op_type();
  public:

  // string graph_id = 3;
  void clear_graph_id();
  const std::string& graph_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_graph_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_graph_id();
  PROTOBUF_NODISCARD std::string* release_graph_id();
  void set_allocated_graph_id(std::string* graph_id);
  private:
  const std::string& _internal_graph_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_graph_id(const std::string& value);
  std::string* _internal_mutable_graph_id();
  public:

  // .tensorflow.CodeLocation code_location = 8;
  bool has_code_location() const;
  private:
  bool _internal_has_code_location() const;
  public:
  void clear_code_location();
  const ::tensorflow::CodeLocation& code_location() const;
  PROTOBUF_NODISCARD ::tensorflow::CodeLocation* release_code_location();
  ::tensorflow::CodeLocation* mutable_code_location();
  void set_allocated_code_location(::tensorflow::CodeLocation* code_location);
  private:
  const ::tensorflow::CodeLocation& _internal_code_location() const;
  ::tensorflow::CodeLocation* _internal_mutable_code_location();
  public:
  void unsafe_arena_set_allocated_code_location(
      ::tensorflow::CodeLocation* code_location);
  ::tensorflow::CodeLocation* unsafe_arena_release_code_location();

  // int32 num_outputs = 2;
  void clear_num_outputs();
  int32_t num_outputs() const;
  void set_num_outputs(int32_t value);
  private:
  int32_t _internal_num_outputs() const;
  void _internal_set_num_outputs(int32_t value);
  public:

  // .tensorflow.TensorDebugMode tensor_debug_mode = 6;
  void clear_tensor_debug_mode();
  ::tensorflow::TensorDebugMode tensor_debug_mode() const;
  void set_tensor_debug_mode(::tensorflow::TensorDebugMode value);
  private:
  ::tensorflow::TensorDebugMode _internal_tensor_debug_mode() const;
  void _internal_set_tensor_debug_mode(::tensorflow::TensorDebugMode value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.Execution)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t > input_tensor_ids_;
    mutable std::atomic<int> _input_tensor_ids_cached_byte_size_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t > output_tensor_ids_;
    mutable std::atomic<int> _output_tensor_ids_cached_byte_size_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto > tensor_protos_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t > output_tensor_device_ids_;
    mutable std::atomic<int> _output_tensor_device_ids_cached_byte_size_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr op_type_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr graph_id_;
    ::tensorflow::CodeLocation* code_location_;
    int32_t num_outputs_;
    int tensor_debug_mode_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fdebug_5fevent_2eproto;
};
// -------------------------------------------------------------------

class GraphExecutionTrace final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.GraphExecutionTrace) */ {
 public:
  inline GraphExecutionTrace() : GraphExecutionTrace(nullptr) {}
  ~GraphExecutionTrace() override;
  explicit PROTOBUF_CONSTEXPR GraphExecutionTrace(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GraphExecutionTrace(const GraphExecutionTrace& from);
  GraphExecutionTrace(GraphExecutionTrace&& from) noexcept
    : GraphExecutionTrace() {
    *this = ::std::move(from);
  }

  inline GraphExecutionTrace& operator=(const GraphExecutionTrace& from) {
    CopyFrom(from);
    return *this;
  }
  inline GraphExecutionTrace& operator=(GraphExecutionTrace&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GraphExecutionTrace& default_instance() {
    return *internal_default_instance();
  }
  static inline const GraphExecutionTrace* internal_default_instance() {
    return reinterpret_cast<const GraphExecutionTrace*>(
               &_GraphExecutionTrace_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(GraphExecutionTrace& a, GraphExecutionTrace& b) {
    a.Swap(&b);
  }
  inline void Swap(GraphExecutionTrace* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GraphExecutionTrace* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GraphExecutionTrace* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GraphExecutionTrace>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GraphExecutionTrace& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const GraphExecutionTrace& from) {
    GraphExecutionTrace::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GraphExecutionTrace* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.GraphExecutionTrace";
  }
  protected:
  explicit GraphExecutionTrace(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTfdbgContextIdFieldNumber = 1,
    kOpNameFieldNumber = 2,
    kDeviceNameFieldNumber = 6,
    kTensorProtoFieldNumber = 5,
    kOutputSlotFieldNumber = 3,
    kTensorDebugModeFieldNumber = 4,
  };
  // string tfdbg_context_id = 1;
  void clear_tfdbg_context_id();
  const std::string& tfdbg_context_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_tfdbg_context_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_tfdbg_context_id();
  PROTOBUF_NODISCARD std::string* release_tfdbg_context_id();
  void set_allocated_tfdbg_context_id(std::string* tfdbg_context_id);
  private:
  const std::string& _internal_tfdbg_context_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_tfdbg_context_id(const std::string& value);
  std::string* _internal_mutable_tfdbg_context_id();
  public:

  // string op_name = 2;
  void clear_op_name();
  const std::string& op_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_op_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_op_name();
  PROTOBUF_NODISCARD std::string* release_op_name();
  void set_allocated_op_name(std::string* op_name);
  private:
  const std::string& _internal_op_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_op_name(const std::string& value);
  std::string* _internal_mutable_op_name();
  public:

  // string device_name = 6;
  void clear_device_name();
  const std::string& device_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_device_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_device_name();
  PROTOBUF_NODISCARD std::string* release_device_name();
  void set_allocated_device_name(std::string* device_name);
  private:
  const std::string& _internal_device_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_device_name(const std::string& value);
  std::string* _internal_mutable_device_name();
  public:

  // .tensorflow.TensorProto tensor_proto = 5;
  bool has_tensor_proto() const;
  private:
  bool _internal_has_tensor_proto() const;
  public:
  void clear_tensor_proto();
  const ::tensorflow::TensorProto& tensor_proto() const;
  PROTOBUF_NODISCARD ::tensorflow::TensorProto* release_tensor_proto();
  ::tensorflow::TensorProto* mutable_tensor_proto();
  void set_allocated_tensor_proto(::tensorflow::TensorProto* tensor_proto);
  private:
  const ::tensorflow::TensorProto& _internal_tensor_proto() const;
  ::tensorflow::TensorProto* _internal_mutable_tensor_proto();
  public:
  void unsafe_arena_set_allocated_tensor_proto(
      ::tensorflow::TensorProto* tensor_proto);
  ::tensorflow::TensorProto* unsafe_arena_release_tensor_proto();

  // int32 output_slot = 3;
  void clear_output_slot();
  int32_t output_slot() const;
  void set_output_slot(int32_t value);
  private:
  int32_t _internal_output_slot() const;
  void _internal_set_output_slot(int32_t value);
  public:

  // .tensorflow.TensorDebugMode tensor_debug_mode = 4;
  void clear_tensor_debug_mode();
  ::tensorflow::TensorDebugMode tensor_debug_mode() const;
  void set_tensor_debug_mode(::tensorflow::TensorDebugMode value);
  private:
  ::tensorflow::TensorDebugMode _internal_tensor_debug_mode() const;
  void _internal_set_tensor_debug_mode(::tensorflow::TensorDebugMode value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.GraphExecutionTrace)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr tfdbg_context_id_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr op_name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr device_name_;
    ::tensorflow::TensorProto* tensor_proto_;
    int32_t output_slot_;
    int tensor_debug_mode_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fdebug_5fevent_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// DebugEvent

// double wall_time = 1;
inline void DebugEvent::clear_wall_time() {
  _impl_.wall_time_ = 0;
}
inline double DebugEvent::_internal_wall_time() const {
  return _impl_.wall_time_;
}
inline double DebugEvent::wall_time() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebugEvent.wall_time)
  return _internal_wall_time();
}
inline void DebugEvent::_internal_set_wall_time(double value) {
  
  _impl_.wall_time_ = value;
}
inline void DebugEvent::set_wall_time(double value) {
  _internal_set_wall_time(value);
  // @@protoc_insertion_point(field_set:tensorflow.DebugEvent.wall_time)
}

// int64 step = 2;
inline void DebugEvent::clear_step() {
  _impl_.step_ = int64_t{0};
}
inline int64_t DebugEvent::_internal_step() const {
  return _impl_.step_;
}
inline int64_t DebugEvent::step() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebugEvent.step)
  return _internal_step();
}
inline void DebugEvent::_internal_set_step(int64_t value) {
  
  _impl_.step_ = value;
}
inline void DebugEvent::set_step(int64_t value) {
  _internal_set_step(value);
  // @@protoc_insertion_point(field_set:tensorflow.DebugEvent.step)
}

// .tensorflow.DebugMetadata debug_metadata = 3;
inline bool DebugEvent::_internal_has_debug_metadata() const {
  return what_case() == kDebugMetadata;
}
inline bool DebugEvent::has_debug_metadata() const {
  return _internal_has_debug_metadata();
}
inline void DebugEvent::set_has_debug_metadata() {
  _impl_._oneof_case_[0] = kDebugMetadata;
}
inline void DebugEvent::clear_debug_metadata() {
  if (_internal_has_debug_metadata()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.what_.debug_metadata_;
    }
    clear_has_what();
  }
}
inline ::tensorflow::DebugMetadata* DebugEvent::release_debug_metadata() {
  // @@protoc_insertion_point(field_release:tensorflow.DebugEvent.debug_metadata)
  if (_internal_has_debug_metadata()) {
    clear_has_what();
    ::tensorflow::DebugMetadata* temp = _impl_.what_.debug_metadata_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.what_.debug_metadata_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::DebugMetadata& DebugEvent::_internal_debug_metadata() const {
  return _internal_has_debug_metadata()
      ? *_impl_.what_.debug_metadata_
      : reinterpret_cast< ::tensorflow::DebugMetadata&>(::tensorflow::_DebugMetadata_default_instance_);
}
inline const ::tensorflow::DebugMetadata& DebugEvent::debug_metadata() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebugEvent.debug_metadata)
  return _internal_debug_metadata();
}
inline ::tensorflow::DebugMetadata* DebugEvent::unsafe_arena_release_debug_metadata() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.DebugEvent.debug_metadata)
  if (_internal_has_debug_metadata()) {
    clear_has_what();
    ::tensorflow::DebugMetadata* temp = _impl_.what_.debug_metadata_;
    _impl_.what_.debug_metadata_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void DebugEvent::unsafe_arena_set_allocated_debug_metadata(::tensorflow::DebugMetadata* debug_metadata) {
  clear_what();
  if (debug_metadata) {
    set_has_debug_metadata();
    _impl_.what_.debug_metadata_ = debug_metadata;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.DebugEvent.debug_metadata)
}
inline ::tensorflow::DebugMetadata* DebugEvent::_internal_mutable_debug_metadata() {
  if (!_internal_has_debug_metadata()) {
    clear_what();
    set_has_debug_metadata();
    _impl_.what_.debug_metadata_ = CreateMaybeMessage< ::tensorflow::DebugMetadata >(GetArenaForAllocation());
  }
  return _impl_.what_.debug_metadata_;
}
inline ::tensorflow::DebugMetadata* DebugEvent::mutable_debug_metadata() {
  ::tensorflow::DebugMetadata* _msg = _internal_mutable_debug_metadata();
  // @@protoc_insertion_point(field_mutable:tensorflow.DebugEvent.debug_metadata)
  return _msg;
}

// .tensorflow.SourceFile source_file = 4;
inline bool DebugEvent::_internal_has_source_file() const {
  return what_case() == kSourceFile;
}
inline bool DebugEvent::has_source_file() const {
  return _internal_has_source_file();
}
inline void DebugEvent::set_has_source_file() {
  _impl_._oneof_case_[0] = kSourceFile;
}
inline void DebugEvent::clear_source_file() {
  if (_internal_has_source_file()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.what_.source_file_;
    }
    clear_has_what();
  }
}
inline ::tensorflow::SourceFile* DebugEvent::release_source_file() {
  // @@protoc_insertion_point(field_release:tensorflow.DebugEvent.source_file)
  if (_internal_has_source_file()) {
    clear_has_what();
    ::tensorflow::SourceFile* temp = _impl_.what_.source_file_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.what_.source_file_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::SourceFile& DebugEvent::_internal_source_file() const {
  return _internal_has_source_file()
      ? *_impl_.what_.source_file_
      : reinterpret_cast< ::tensorflow::SourceFile&>(::tensorflow::_SourceFile_default_instance_);
}
inline const ::tensorflow::SourceFile& DebugEvent::source_file() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebugEvent.source_file)
  return _internal_source_file();
}
inline ::tensorflow::SourceFile* DebugEvent::unsafe_arena_release_source_file() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.DebugEvent.source_file)
  if (_internal_has_source_file()) {
    clear_has_what();
    ::tensorflow::SourceFile* temp = _impl_.what_.source_file_;
    _impl_.what_.source_file_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void DebugEvent::unsafe_arena_set_allocated_source_file(::tensorflow::SourceFile* source_file) {
  clear_what();
  if (source_file) {
    set_has_source_file();
    _impl_.what_.source_file_ = source_file;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.DebugEvent.source_file)
}
inline ::tensorflow::SourceFile* DebugEvent::_internal_mutable_source_file() {
  if (!_internal_has_source_file()) {
    clear_what();
    set_has_source_file();
    _impl_.what_.source_file_ = CreateMaybeMessage< ::tensorflow::SourceFile >(GetArenaForAllocation());
  }
  return _impl_.what_.source_file_;
}
inline ::tensorflow::SourceFile* DebugEvent::mutable_source_file() {
  ::tensorflow::SourceFile* _msg = _internal_mutable_source_file();
  // @@protoc_insertion_point(field_mutable:tensorflow.DebugEvent.source_file)
  return _msg;
}

// .tensorflow.StackFrameWithId stack_frame_with_id = 6;
inline bool DebugEvent::_internal_has_stack_frame_with_id() const {
  return what_case() == kStackFrameWithId;
}
inline bool DebugEvent::has_stack_frame_with_id() const {
  return _internal_has_stack_frame_with_id();
}
inline void DebugEvent::set_has_stack_frame_with_id() {
  _impl_._oneof_case_[0] = kStackFrameWithId;
}
inline void DebugEvent::clear_stack_frame_with_id() {
  if (_internal_has_stack_frame_with_id()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.what_.stack_frame_with_id_;
    }
    clear_has_what();
  }
}
inline ::tensorflow::StackFrameWithId* DebugEvent::release_stack_frame_with_id() {
  // @@protoc_insertion_point(field_release:tensorflow.DebugEvent.stack_frame_with_id)
  if (_internal_has_stack_frame_with_id()) {
    clear_has_what();
    ::tensorflow::StackFrameWithId* temp = _impl_.what_.stack_frame_with_id_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.what_.stack_frame_with_id_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::StackFrameWithId& DebugEvent::_internal_stack_frame_with_id() const {
  return _internal_has_stack_frame_with_id()
      ? *_impl_.what_.stack_frame_with_id_
      : reinterpret_cast< ::tensorflow::StackFrameWithId&>(::tensorflow::_StackFrameWithId_default_instance_);
}
inline const ::tensorflow::StackFrameWithId& DebugEvent::stack_frame_with_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebugEvent.stack_frame_with_id)
  return _internal_stack_frame_with_id();
}
inline ::tensorflow::StackFrameWithId* DebugEvent::unsafe_arena_release_stack_frame_with_id() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.DebugEvent.stack_frame_with_id)
  if (_internal_has_stack_frame_with_id()) {
    clear_has_what();
    ::tensorflow::StackFrameWithId* temp = _impl_.what_.stack_frame_with_id_;
    _impl_.what_.stack_frame_with_id_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void DebugEvent::unsafe_arena_set_allocated_stack_frame_with_id(::tensorflow::StackFrameWithId* stack_frame_with_id) {
  clear_what();
  if (stack_frame_with_id) {
    set_has_stack_frame_with_id();
    _impl_.what_.stack_frame_with_id_ = stack_frame_with_id;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.DebugEvent.stack_frame_with_id)
}
inline ::tensorflow::StackFrameWithId* DebugEvent::_internal_mutable_stack_frame_with_id() {
  if (!_internal_has_stack_frame_with_id()) {
    clear_what();
    set_has_stack_frame_with_id();
    _impl_.what_.stack_frame_with_id_ = CreateMaybeMessage< ::tensorflow::StackFrameWithId >(GetArenaForAllocation());
  }
  return _impl_.what_.stack_frame_with_id_;
}
inline ::tensorflow::StackFrameWithId* DebugEvent::mutable_stack_frame_with_id() {
  ::tensorflow::StackFrameWithId* _msg = _internal_mutable_stack_frame_with_id();
  // @@protoc_insertion_point(field_mutable:tensorflow.DebugEvent.stack_frame_with_id)
  return _msg;
}

// .tensorflow.GraphOpCreation graph_op_creation = 7;
inline bool DebugEvent::_internal_has_graph_op_creation() const {
  return what_case() == kGraphOpCreation;
}
inline bool DebugEvent::has_graph_op_creation() const {
  return _internal_has_graph_op_creation();
}
inline void DebugEvent::set_has_graph_op_creation() {
  _impl_._oneof_case_[0] = kGraphOpCreation;
}
inline void DebugEvent::clear_graph_op_creation() {
  if (_internal_has_graph_op_creation()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.what_.graph_op_creation_;
    }
    clear_has_what();
  }
}
inline ::tensorflow::GraphOpCreation* DebugEvent::release_graph_op_creation() {
  // @@protoc_insertion_point(field_release:tensorflow.DebugEvent.graph_op_creation)
  if (_internal_has_graph_op_creation()) {
    clear_has_what();
    ::tensorflow::GraphOpCreation* temp = _impl_.what_.graph_op_creation_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.what_.graph_op_creation_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::GraphOpCreation& DebugEvent::_internal_graph_op_creation() const {
  return _internal_has_graph_op_creation()
      ? *_impl_.what_.graph_op_creation_
      : reinterpret_cast< ::tensorflow::GraphOpCreation&>(::tensorflow::_GraphOpCreation_default_instance_);
}
inline const ::tensorflow::GraphOpCreation& DebugEvent::graph_op_creation() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebugEvent.graph_op_creation)
  return _internal_graph_op_creation();
}
inline ::tensorflow::GraphOpCreation* DebugEvent::unsafe_arena_release_graph_op_creation() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.DebugEvent.graph_op_creation)
  if (_internal_has_graph_op_creation()) {
    clear_has_what();
    ::tensorflow::GraphOpCreation* temp = _impl_.what_.graph_op_creation_;
    _impl_.what_.graph_op_creation_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void DebugEvent::unsafe_arena_set_allocated_graph_op_creation(::tensorflow::GraphOpCreation* graph_op_creation) {
  clear_what();
  if (graph_op_creation) {
    set_has_graph_op_creation();
    _impl_.what_.graph_op_creation_ = graph_op_creation;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.DebugEvent.graph_op_creation)
}
inline ::tensorflow::GraphOpCreation* DebugEvent::_internal_mutable_graph_op_creation() {
  if (!_internal_has_graph_op_creation()) {
    clear_what();
    set_has_graph_op_creation();
    _impl_.what_.graph_op_creation_ = CreateMaybeMessage< ::tensorflow::GraphOpCreation >(GetArenaForAllocation());
  }
  return _impl_.what_.graph_op_creation_;
}
inline ::tensorflow::GraphOpCreation* DebugEvent::mutable_graph_op_creation() {
  ::tensorflow::GraphOpCreation* _msg = _internal_mutable_graph_op_creation();
  // @@protoc_insertion_point(field_mutable:tensorflow.DebugEvent.graph_op_creation)
  return _msg;
}

// .tensorflow.DebuggedGraph debugged_graph = 8;
inline bool DebugEvent::_internal_has_debugged_graph() const {
  return what_case() == kDebuggedGraph;
}
inline bool DebugEvent::has_debugged_graph() const {
  return _internal_has_debugged_graph();
}
inline void DebugEvent::set_has_debugged_graph() {
  _impl_._oneof_case_[0] = kDebuggedGraph;
}
inline void DebugEvent::clear_debugged_graph() {
  if (_internal_has_debugged_graph()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.what_.debugged_graph_;
    }
    clear_has_what();
  }
}
inline ::tensorflow::DebuggedGraph* DebugEvent::release_debugged_graph() {
  // @@protoc_insertion_point(field_release:tensorflow.DebugEvent.debugged_graph)
  if (_internal_has_debugged_graph()) {
    clear_has_what();
    ::tensorflow::DebuggedGraph* temp = _impl_.what_.debugged_graph_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.what_.debugged_graph_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::DebuggedGraph& DebugEvent::_internal_debugged_graph() const {
  return _internal_has_debugged_graph()
      ? *_impl_.what_.debugged_graph_
      : reinterpret_cast< ::tensorflow::DebuggedGraph&>(::tensorflow::_DebuggedGraph_default_instance_);
}
inline const ::tensorflow::DebuggedGraph& DebugEvent::debugged_graph() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebugEvent.debugged_graph)
  return _internal_debugged_graph();
}
inline ::tensorflow::DebuggedGraph* DebugEvent::unsafe_arena_release_debugged_graph() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.DebugEvent.debugged_graph)
  if (_internal_has_debugged_graph()) {
    clear_has_what();
    ::tensorflow::DebuggedGraph* temp = _impl_.what_.debugged_graph_;
    _impl_.what_.debugged_graph_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void DebugEvent::unsafe_arena_set_allocated_debugged_graph(::tensorflow::DebuggedGraph* debugged_graph) {
  clear_what();
  if (debugged_graph) {
    set_has_debugged_graph();
    _impl_.what_.debugged_graph_ = debugged_graph;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.DebugEvent.debugged_graph)
}
inline ::tensorflow::DebuggedGraph* DebugEvent::_internal_mutable_debugged_graph() {
  if (!_internal_has_debugged_graph()) {
    clear_what();
    set_has_debugged_graph();
    _impl_.what_.debugged_graph_ = CreateMaybeMessage< ::tensorflow::DebuggedGraph >(GetArenaForAllocation());
  }
  return _impl_.what_.debugged_graph_;
}
inline ::tensorflow::DebuggedGraph* DebugEvent::mutable_debugged_graph() {
  ::tensorflow::DebuggedGraph* _msg = _internal_mutable_debugged_graph();
  // @@protoc_insertion_point(field_mutable:tensorflow.DebugEvent.debugged_graph)
  return _msg;
}

// .tensorflow.Execution execution = 9;
inline bool DebugEvent::_internal_has_execution() const {
  return what_case() == kExecution;
}
inline bool DebugEvent::has_execution() const {
  return _internal_has_execution();
}
inline void DebugEvent::set_has_execution() {
  _impl_._oneof_case_[0] = kExecution;
}
inline void DebugEvent::clear_execution() {
  if (_internal_has_execution()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.what_.execution_;
    }
    clear_has_what();
  }
}
inline ::tensorflow::Execution* DebugEvent::release_execution() {
  // @@protoc_insertion_point(field_release:tensorflow.DebugEvent.execution)
  if (_internal_has_execution()) {
    clear_has_what();
    ::tensorflow::Execution* temp = _impl_.what_.execution_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.what_.execution_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::Execution& DebugEvent::_internal_execution() const {
  return _internal_has_execution()
      ? *_impl_.what_.execution_
      : reinterpret_cast< ::tensorflow::Execution&>(::tensorflow::_Execution_default_instance_);
}
inline const ::tensorflow::Execution& DebugEvent::execution() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebugEvent.execution)
  return _internal_execution();
}
inline ::tensorflow::Execution* DebugEvent::unsafe_arena_release_execution() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.DebugEvent.execution)
  if (_internal_has_execution()) {
    clear_has_what();
    ::tensorflow::Execution* temp = _impl_.what_.execution_;
    _impl_.what_.execution_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void DebugEvent::unsafe_arena_set_allocated_execution(::tensorflow::Execution* execution) {
  clear_what();
  if (execution) {
    set_has_execution();
    _impl_.what_.execution_ = execution;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.DebugEvent.execution)
}
inline ::tensorflow::Execution* DebugEvent::_internal_mutable_execution() {
  if (!_internal_has_execution()) {
    clear_what();
    set_has_execution();
    _impl_.what_.execution_ = CreateMaybeMessage< ::tensorflow::Execution >(GetArenaForAllocation());
  }
  return _impl_.what_.execution_;
}
inline ::tensorflow::Execution* DebugEvent::mutable_execution() {
  ::tensorflow::Execution* _msg = _internal_mutable_execution();
  // @@protoc_insertion_point(field_mutable:tensorflow.DebugEvent.execution)
  return _msg;
}

// .tensorflow.GraphExecutionTrace graph_execution_trace = 10;
inline bool DebugEvent::_internal_has_graph_execution_trace() const {
  return what_case() == kGraphExecutionTrace;
}
inline bool DebugEvent::has_graph_execution_trace() const {
  return _internal_has_graph_execution_trace();
}
inline void DebugEvent::set_has_graph_execution_trace() {
  _impl_._oneof_case_[0] = kGraphExecutionTrace;
}
inline void DebugEvent::clear_graph_execution_trace() {
  if (_internal_has_graph_execution_trace()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.what_.graph_execution_trace_;
    }
    clear_has_what();
  }
}
inline ::tensorflow::GraphExecutionTrace* DebugEvent::release_graph_execution_trace() {
  // @@protoc_insertion_point(field_release:tensorflow.DebugEvent.graph_execution_trace)
  if (_internal_has_graph_execution_trace()) {
    clear_has_what();
    ::tensorflow::GraphExecutionTrace* temp = _impl_.what_.graph_execution_trace_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.what_.graph_execution_trace_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::GraphExecutionTrace& DebugEvent::_internal_graph_execution_trace() const {
  return _internal_has_graph_execution_trace()
      ? *_impl_.what_.graph_execution_trace_
      : reinterpret_cast< ::tensorflow::GraphExecutionTrace&>(::tensorflow::_GraphExecutionTrace_default_instance_);
}
inline const ::tensorflow::GraphExecutionTrace& DebugEvent::graph_execution_trace() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebugEvent.graph_execution_trace)
  return _internal_graph_execution_trace();
}
inline ::tensorflow::GraphExecutionTrace* DebugEvent::unsafe_arena_release_graph_execution_trace() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.DebugEvent.graph_execution_trace)
  if (_internal_has_graph_execution_trace()) {
    clear_has_what();
    ::tensorflow::GraphExecutionTrace* temp = _impl_.what_.graph_execution_trace_;
    _impl_.what_.graph_execution_trace_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void DebugEvent::unsafe_arena_set_allocated_graph_execution_trace(::tensorflow::GraphExecutionTrace* graph_execution_trace) {
  clear_what();
  if (graph_execution_trace) {
    set_has_graph_execution_trace();
    _impl_.what_.graph_execution_trace_ = graph_execution_trace;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.DebugEvent.graph_execution_trace)
}
inline ::tensorflow::GraphExecutionTrace* DebugEvent::_internal_mutable_graph_execution_trace() {
  if (!_internal_has_graph_execution_trace()) {
    clear_what();
    set_has_graph_execution_trace();
    _impl_.what_.graph_execution_trace_ = CreateMaybeMessage< ::tensorflow::GraphExecutionTrace >(GetArenaForAllocation());
  }
  return _impl_.what_.graph_execution_trace_;
}
inline ::tensorflow::GraphExecutionTrace* DebugEvent::mutable_graph_execution_trace() {
  ::tensorflow::GraphExecutionTrace* _msg = _internal_mutable_graph_execution_trace();
  // @@protoc_insertion_point(field_mutable:tensorflow.DebugEvent.graph_execution_trace)
  return _msg;
}

// string graph_id = 11;
inline bool DebugEvent::_internal_has_graph_id() const {
  return what_case() == kGraphId;
}
inline bool DebugEvent::has_graph_id() const {
  return _internal_has_graph_id();
}
inline void DebugEvent::set_has_graph_id() {
  _impl_._oneof_case_[0] = kGraphId;
}
inline void DebugEvent::clear_graph_id() {
  if (_internal_has_graph_id()) {
    _impl_.what_.graph_id_.Destroy();
    clear_has_what();
  }
}
inline const std::string& DebugEvent::graph_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebugEvent.graph_id)
  return _internal_graph_id();
}
template <typename ArgT0, typename... ArgT>
inline void DebugEvent::set_graph_id(ArgT0&& arg0, ArgT... args) {
  if (!_internal_has_graph_id()) {
    clear_what();
    set_has_graph_id();
    _impl_.what_.graph_id_.InitDefault();
  }
  _impl_.what_.graph_id_.Set( static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.DebugEvent.graph_id)
}
inline std::string* DebugEvent::mutable_graph_id() {
  std::string* _s = _internal_mutable_graph_id();
  // @@protoc_insertion_point(field_mutable:tensorflow.DebugEvent.graph_id)
  return _s;
}
inline const std::string& DebugEvent::_internal_graph_id() const {
  if (_internal_has_graph_id()) {
    return _impl_.what_.graph_id_.Get();
  }
  return ::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited();
}
inline void DebugEvent::_internal_set_graph_id(const std::string& value) {
  if (!_internal_has_graph_id()) {
    clear_what();
    set_has_graph_id();
    _impl_.what_.graph_id_.InitDefault();
  }
  _impl_.what_.graph_id_.Set(value, GetArenaForAllocation());
}
inline std::string* DebugEvent::_internal_mutable_graph_id() {
  if (!_internal_has_graph_id()) {
    clear_what();
    set_has_graph_id();
    _impl_.what_.graph_id_.InitDefault();
  }
  return _impl_.what_.graph_id_.Mutable(      GetArenaForAllocation());
}
inline std::string* DebugEvent::release_graph_id() {
  // @@protoc_insertion_point(field_release:tensorflow.DebugEvent.graph_id)
  if (_internal_has_graph_id()) {
    clear_has_what();
    return _impl_.what_.graph_id_.Release();
  } else {
    return nullptr;
  }
}
inline void DebugEvent::set_allocated_graph_id(std::string* graph_id) {
  if (has_what()) {
    clear_what();
  }
  if (graph_id != nullptr) {
    set_has_graph_id();
    _impl_.what_.graph_id_.InitAllocated(graph_id, GetArenaForAllocation());
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DebugEvent.graph_id)
}

// .tensorflow.DebuggedDevice debugged_device = 12;
inline bool DebugEvent::_internal_has_debugged_device() const {
  return what_case() == kDebuggedDevice;
}
inline bool DebugEvent::has_debugged_device() const {
  return _internal_has_debugged_device();
}
inline void DebugEvent::set_has_debugged_device() {
  _impl_._oneof_case_[0] = kDebuggedDevice;
}
inline void DebugEvent::clear_debugged_device() {
  if (_internal_has_debugged_device()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.what_.debugged_device_;
    }
    clear_has_what();
  }
}
inline ::tensorflow::DebuggedDevice* DebugEvent::release_debugged_device() {
  // @@protoc_insertion_point(field_release:tensorflow.DebugEvent.debugged_device)
  if (_internal_has_debugged_device()) {
    clear_has_what();
    ::tensorflow::DebuggedDevice* temp = _impl_.what_.debugged_device_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.what_.debugged_device_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::DebuggedDevice& DebugEvent::_internal_debugged_device() const {
  return _internal_has_debugged_device()
      ? *_impl_.what_.debugged_device_
      : reinterpret_cast< ::tensorflow::DebuggedDevice&>(::tensorflow::_DebuggedDevice_default_instance_);
}
inline const ::tensorflow::DebuggedDevice& DebugEvent::debugged_device() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebugEvent.debugged_device)
  return _internal_debugged_device();
}
inline ::tensorflow::DebuggedDevice* DebugEvent::unsafe_arena_release_debugged_device() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.DebugEvent.debugged_device)
  if (_internal_has_debugged_device()) {
    clear_has_what();
    ::tensorflow::DebuggedDevice* temp = _impl_.what_.debugged_device_;
    _impl_.what_.debugged_device_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void DebugEvent::unsafe_arena_set_allocated_debugged_device(::tensorflow::DebuggedDevice* debugged_device) {
  clear_what();
  if (debugged_device) {
    set_has_debugged_device();
    _impl_.what_.debugged_device_ = debugged_device;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.DebugEvent.debugged_device)
}
inline ::tensorflow::DebuggedDevice* DebugEvent::_internal_mutable_debugged_device() {
  if (!_internal_has_debugged_device()) {
    clear_what();
    set_has_debugged_device();
    _impl_.what_.debugged_device_ = CreateMaybeMessage< ::tensorflow::DebuggedDevice >(GetArenaForAllocation());
  }
  return _impl_.what_.debugged_device_;
}
inline ::tensorflow::DebuggedDevice* DebugEvent::mutable_debugged_device() {
  ::tensorflow::DebuggedDevice* _msg = _internal_mutable_debugged_device();
  // @@protoc_insertion_point(field_mutable:tensorflow.DebugEvent.debugged_device)
  return _msg;
}

inline bool DebugEvent::has_what() const {
  return what_case() != WHAT_NOT_SET;
}
inline void DebugEvent::clear_has_what() {
  _impl_._oneof_case_[0] = WHAT_NOT_SET;
}
inline DebugEvent::WhatCase DebugEvent::what_case() const {
  return DebugEvent::WhatCase(_impl_._oneof_case_[0]);
}
// -------------------------------------------------------------------

// DebugMetadata

// string tensorflow_version = 1;
inline void DebugMetadata::clear_tensorflow_version() {
  _impl_.tensorflow_version_.ClearToEmpty();
}
inline const std::string& DebugMetadata::tensorflow_version() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebugMetadata.tensorflow_version)
  return _internal_tensorflow_version();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DebugMetadata::set_tensorflow_version(ArgT0&& arg0, ArgT... args) {
 
 _impl_.tensorflow_version_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.DebugMetadata.tensorflow_version)
}
inline std::string* DebugMetadata::mutable_tensorflow_version() {
  std::string* _s = _internal_mutable_tensorflow_version();
  // @@protoc_insertion_point(field_mutable:tensorflow.DebugMetadata.tensorflow_version)
  return _s;
}
inline const std::string& DebugMetadata::_internal_tensorflow_version() const {
  return _impl_.tensorflow_version_.Get();
}
inline void DebugMetadata::_internal_set_tensorflow_version(const std::string& value) {
  
  _impl_.tensorflow_version_.Set(value, GetArenaForAllocation());
}
inline std::string* DebugMetadata::_internal_mutable_tensorflow_version() {
  
  return _impl_.tensorflow_version_.Mutable(GetArenaForAllocation());
}
inline std::string* DebugMetadata::release_tensorflow_version() {
  // @@protoc_insertion_point(field_release:tensorflow.DebugMetadata.tensorflow_version)
  return _impl_.tensorflow_version_.Release();
}
inline void DebugMetadata::set_allocated_tensorflow_version(std::string* tensorflow_version) {
  if (tensorflow_version != nullptr) {
    
  } else {
    
  }
  _impl_.tensorflow_version_.SetAllocated(tensorflow_version, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.tensorflow_version_.IsDefault()) {
    _impl_.tensorflow_version_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DebugMetadata.tensorflow_version)
}

// string file_version = 2;
inline void DebugMetadata::clear_file_version() {
  _impl_.file_version_.ClearToEmpty();
}
inline const std::string& DebugMetadata::file_version() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebugMetadata.file_version)
  return _internal_file_version();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DebugMetadata::set_file_version(ArgT0&& arg0, ArgT... args) {
 
 _impl_.file_version_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.DebugMetadata.file_version)
}
inline std::string* DebugMetadata::mutable_file_version() {
  std::string* _s = _internal_mutable_file_version();
  // @@protoc_insertion_point(field_mutable:tensorflow.DebugMetadata.file_version)
  return _s;
}
inline const std::string& DebugMetadata::_internal_file_version() const {
  return _impl_.file_version_.Get();
}
inline void DebugMetadata::_internal_set_file_version(const std::string& value) {
  
  _impl_.file_version_.Set(value, GetArenaForAllocation());
}
inline std::string* DebugMetadata::_internal_mutable_file_version() {
  
  return _impl_.file_version_.Mutable(GetArenaForAllocation());
}
inline std::string* DebugMetadata::release_file_version() {
  // @@protoc_insertion_point(field_release:tensorflow.DebugMetadata.file_version)
  return _impl_.file_version_.Release();
}
inline void DebugMetadata::set_allocated_file_version(std::string* file_version) {
  if (file_version != nullptr) {
    
  } else {
    
  }
  _impl_.file_version_.SetAllocated(file_version, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.file_version_.IsDefault()) {
    _impl_.file_version_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DebugMetadata.file_version)
}

// string tfdbg_run_id = 3;
inline void DebugMetadata::clear_tfdbg_run_id() {
  _impl_.tfdbg_run_id_.ClearToEmpty();
}
inline const std::string& DebugMetadata::tfdbg_run_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebugMetadata.tfdbg_run_id)
  return _internal_tfdbg_run_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DebugMetadata::set_tfdbg_run_id(ArgT0&& arg0, ArgT... args) {
 
 _impl_.tfdbg_run_id_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.DebugMetadata.tfdbg_run_id)
}
inline std::string* DebugMetadata::mutable_tfdbg_run_id() {
  std::string* _s = _internal_mutable_tfdbg_run_id();
  // @@protoc_insertion_point(field_mutable:tensorflow.DebugMetadata.tfdbg_run_id)
  return _s;
}
inline const std::string& DebugMetadata::_internal_tfdbg_run_id() const {
  return _impl_.tfdbg_run_id_.Get();
}
inline void DebugMetadata::_internal_set_tfdbg_run_id(const std::string& value) {
  
  _impl_.tfdbg_run_id_.Set(value, GetArenaForAllocation());
}
inline std::string* DebugMetadata::_internal_mutable_tfdbg_run_id() {
  
  return _impl_.tfdbg_run_id_.Mutable(GetArenaForAllocation());
}
inline std::string* DebugMetadata::release_tfdbg_run_id() {
  // @@protoc_insertion_point(field_release:tensorflow.DebugMetadata.tfdbg_run_id)
  return _impl_.tfdbg_run_id_.Release();
}
inline void DebugMetadata::set_allocated_tfdbg_run_id(std::string* tfdbg_run_id) {
  if (tfdbg_run_id != nullptr) {
    
  } else {
    
  }
  _impl_.tfdbg_run_id_.SetAllocated(tfdbg_run_id, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.tfdbg_run_id_.IsDefault()) {
    _impl_.tfdbg_run_id_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DebugMetadata.tfdbg_run_id)
}

// -------------------------------------------------------------------

// SourceFile

// string file_path = 1;
inline void SourceFile::clear_file_path() {
  _impl_.file_path_.ClearToEmpty();
}
inline const std::string& SourceFile::file_path() const {
  // @@protoc_insertion_point(field_get:tensorflow.SourceFile.file_path)
  return _internal_file_path();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SourceFile::set_file_path(ArgT0&& arg0, ArgT... args) {
 
 _impl_.file_path_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.SourceFile.file_path)
}
inline std::string* SourceFile::mutable_file_path() {
  std::string* _s = _internal_mutable_file_path();
  // @@protoc_insertion_point(field_mutable:tensorflow.SourceFile.file_path)
  return _s;
}
inline const std::string& SourceFile::_internal_file_path() const {
  return _impl_.file_path_.Get();
}
inline void SourceFile::_internal_set_file_path(const std::string& value) {
  
  _impl_.file_path_.Set(value, GetArenaForAllocation());
}
inline std::string* SourceFile::_internal_mutable_file_path() {
  
  return _impl_.file_path_.Mutable(GetArenaForAllocation());
}
inline std::string* SourceFile::release_file_path() {
  // @@protoc_insertion_point(field_release:tensorflow.SourceFile.file_path)
  return _impl_.file_path_.Release();
}
inline void SourceFile::set_allocated_file_path(std::string* file_path) {
  if (file_path != nullptr) {
    
  } else {
    
  }
  _impl_.file_path_.SetAllocated(file_path, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.file_path_.IsDefault()) {
    _impl_.file_path_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SourceFile.file_path)
}

// string host_name = 2;
inline void SourceFile::clear_host_name() {
  _impl_.host_name_.ClearToEmpty();
}
inline const std::string& SourceFile::host_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.SourceFile.host_name)
  return _internal_host_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SourceFile::set_host_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.host_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.SourceFile.host_name)
}
inline std::string* SourceFile::mutable_host_name() {
  std::string* _s = _internal_mutable_host_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.SourceFile.host_name)
  return _s;
}
inline const std::string& SourceFile::_internal_host_name() const {
  return _impl_.host_name_.Get();
}
inline void SourceFile::_internal_set_host_name(const std::string& value) {
  
  _impl_.host_name_.Set(value, GetArenaForAllocation());
}
inline std::string* SourceFile::_internal_mutable_host_name() {
  
  return _impl_.host_name_.Mutable(GetArenaForAllocation());
}
inline std::string* SourceFile::release_host_name() {
  // @@protoc_insertion_point(field_release:tensorflow.SourceFile.host_name)
  return _impl_.host_name_.Release();
}
inline void SourceFile::set_allocated_host_name(std::string* host_name) {
  if (host_name != nullptr) {
    
  } else {
    
  }
  _impl_.host_name_.SetAllocated(host_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.host_name_.IsDefault()) {
    _impl_.host_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SourceFile.host_name)
}

// repeated string lines = 3;
inline int SourceFile::_internal_lines_size() const {
  return _impl_.lines_.size();
}
inline int SourceFile::lines_size() const {
  return _internal_lines_size();
}
inline void SourceFile::clear_lines() {
  _impl_.lines_.Clear();
}
inline std::string* SourceFile::add_lines() {
  std::string* _s = _internal_add_lines();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.SourceFile.lines)
  return _s;
}
inline const std::string& SourceFile::_internal_lines(int index) const {
  return _impl_.lines_.Get(index);
}
inline const std::string& SourceFile::lines(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.SourceFile.lines)
  return _internal_lines(index);
}
inline std::string* SourceFile::mutable_lines(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.SourceFile.lines)
  return _impl_.lines_.Mutable(index);
}
inline void SourceFile::set_lines(int index, const std::string& value) {
  _impl_.lines_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.SourceFile.lines)
}
inline void SourceFile::set_lines(int index, std::string&& value) {
  _impl_.lines_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.SourceFile.lines)
}
inline void SourceFile::set_lines(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.lines_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.SourceFile.lines)
}
inline void SourceFile::set_lines(int index, const char* value, size_t size) {
  _impl_.lines_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.SourceFile.lines)
}
inline std::string* SourceFile::_internal_add_lines() {
  return _impl_.lines_.Add();
}
inline void SourceFile::add_lines(const std::string& value) {
  _impl_.lines_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.SourceFile.lines)
}
inline void SourceFile::add_lines(std::string&& value) {
  _impl_.lines_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.SourceFile.lines)
}
inline void SourceFile::add_lines(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.lines_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.SourceFile.lines)
}
inline void SourceFile::add_lines(const char* value, size_t size) {
  _impl_.lines_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.SourceFile.lines)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
SourceFile::lines() const {
  // @@protoc_insertion_point(field_list:tensorflow.SourceFile.lines)
  return _impl_.lines_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
SourceFile::mutable_lines() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.SourceFile.lines)
  return &_impl_.lines_;
}

// -------------------------------------------------------------------

// StackFrameWithId

// string id = 1;
inline void StackFrameWithId::clear_id() {
  _impl_.id_.ClearToEmpty();
}
inline const std::string& StackFrameWithId::id() const {
  // @@protoc_insertion_point(field_get:tensorflow.StackFrameWithId.id)
  return _internal_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void StackFrameWithId::set_id(ArgT0&& arg0, ArgT... args) {
 
 _impl_.id_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.StackFrameWithId.id)
}
inline std::string* StackFrameWithId::mutable_id() {
  std::string* _s = _internal_mutable_id();
  // @@protoc_insertion_point(field_mutable:tensorflow.StackFrameWithId.id)
  return _s;
}
inline const std::string& StackFrameWithId::_internal_id() const {
  return _impl_.id_.Get();
}
inline void StackFrameWithId::_internal_set_id(const std::string& value) {
  
  _impl_.id_.Set(value, GetArenaForAllocation());
}
inline std::string* StackFrameWithId::_internal_mutable_id() {
  
  return _impl_.id_.Mutable(GetArenaForAllocation());
}
inline std::string* StackFrameWithId::release_id() {
  // @@protoc_insertion_point(field_release:tensorflow.StackFrameWithId.id)
  return _impl_.id_.Release();
}
inline void StackFrameWithId::set_allocated_id(std::string* id) {
  if (id != nullptr) {
    
  } else {
    
  }
  _impl_.id_.SetAllocated(id, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.id_.IsDefault()) {
    _impl_.id_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.StackFrameWithId.id)
}

// .tensorflow.GraphDebugInfo.FileLineCol file_line_col = 2;
inline bool StackFrameWithId::_internal_has_file_line_col() const {
  return this != internal_default_instance() && _impl_.file_line_col_ != nullptr;
}
inline bool StackFrameWithId::has_file_line_col() const {
  return _internal_has_file_line_col();
}
inline const ::tensorflow::GraphDebugInfo_FileLineCol& StackFrameWithId::_internal_file_line_col() const {
  const ::tensorflow::GraphDebugInfo_FileLineCol* p = _impl_.file_line_col_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::GraphDebugInfo_FileLineCol&>(
      ::tensorflow::_GraphDebugInfo_FileLineCol_default_instance_);
}
inline const ::tensorflow::GraphDebugInfo_FileLineCol& StackFrameWithId::file_line_col() const {
  // @@protoc_insertion_point(field_get:tensorflow.StackFrameWithId.file_line_col)
  return _internal_file_line_col();
}
inline void StackFrameWithId::unsafe_arena_set_allocated_file_line_col(
    ::tensorflow::GraphDebugInfo_FileLineCol* file_line_col) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.file_line_col_);
  }
  _impl_.file_line_col_ = file_line_col;
  if (file_line_col) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.StackFrameWithId.file_line_col)
}
inline ::tensorflow::GraphDebugInfo_FileLineCol* StackFrameWithId::release_file_line_col() {
  
  ::tensorflow::GraphDebugInfo_FileLineCol* temp = _impl_.file_line_col_;
  _impl_.file_line_col_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::GraphDebugInfo_FileLineCol* StackFrameWithId::unsafe_arena_release_file_line_col() {
  // @@protoc_insertion_point(field_release:tensorflow.StackFrameWithId.file_line_col)
  
  ::tensorflow::GraphDebugInfo_FileLineCol* temp = _impl_.file_line_col_;
  _impl_.file_line_col_ = nullptr;
  return temp;
}
inline ::tensorflow::GraphDebugInfo_FileLineCol* StackFrameWithId::_internal_mutable_file_line_col() {
  
  if (_impl_.file_line_col_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::GraphDebugInfo_FileLineCol>(GetArenaForAllocation());
    _impl_.file_line_col_ = p;
  }
  return _impl_.file_line_col_;
}
inline ::tensorflow::GraphDebugInfo_FileLineCol* StackFrameWithId::mutable_file_line_col() {
  ::tensorflow::GraphDebugInfo_FileLineCol* _msg = _internal_mutable_file_line_col();
  // @@protoc_insertion_point(field_mutable:tensorflow.StackFrameWithId.file_line_col)
  return _msg;
}
inline void StackFrameWithId::set_allocated_file_line_col(::tensorflow::GraphDebugInfo_FileLineCol* file_line_col) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.file_line_col_);
  }
  if (file_line_col) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(file_line_col));
    if (message_arena != submessage_arena) {
      file_line_col = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, file_line_col, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.file_line_col_ = file_line_col;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.StackFrameWithId.file_line_col)
}

// -------------------------------------------------------------------

// CodeLocation

// string host_name = 1;
inline void CodeLocation::clear_host_name() {
  _impl_.host_name_.ClearToEmpty();
}
inline const std::string& CodeLocation::host_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.CodeLocation.host_name)
  return _internal_host_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CodeLocation::set_host_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.host_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.CodeLocation.host_name)
}
inline std::string* CodeLocation::mutable_host_name() {
  std::string* _s = _internal_mutable_host_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.CodeLocation.host_name)
  return _s;
}
inline const std::string& CodeLocation::_internal_host_name() const {
  return _impl_.host_name_.Get();
}
inline void CodeLocation::_internal_set_host_name(const std::string& value) {
  
  _impl_.host_name_.Set(value, GetArenaForAllocation());
}
inline std::string* CodeLocation::_internal_mutable_host_name() {
  
  return _impl_.host_name_.Mutable(GetArenaForAllocation());
}
inline std::string* CodeLocation::release_host_name() {
  // @@protoc_insertion_point(field_release:tensorflow.CodeLocation.host_name)
  return _impl_.host_name_.Release();
}
inline void CodeLocation::set_allocated_host_name(std::string* host_name) {
  if (host_name != nullptr) {
    
  } else {
    
  }
  _impl_.host_name_.SetAllocated(host_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.host_name_.IsDefault()) {
    _impl_.host_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CodeLocation.host_name)
}

// repeated string stack_frame_ids = 2;
inline int CodeLocation::_internal_stack_frame_ids_size() const {
  return _impl_.stack_frame_ids_.size();
}
inline int CodeLocation::stack_frame_ids_size() const {
  return _internal_stack_frame_ids_size();
}
inline void CodeLocation::clear_stack_frame_ids() {
  _impl_.stack_frame_ids_.Clear();
}
inline std::string* CodeLocation::add_stack_frame_ids() {
  std::string* _s = _internal_add_stack_frame_ids();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.CodeLocation.stack_frame_ids)
  return _s;
}
inline const std::string& CodeLocation::_internal_stack_frame_ids(int index) const {
  return _impl_.stack_frame_ids_.Get(index);
}
inline const std::string& CodeLocation::stack_frame_ids(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.CodeLocation.stack_frame_ids)
  return _internal_stack_frame_ids(index);
}
inline std::string* CodeLocation::mutable_stack_frame_ids(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.CodeLocation.stack_frame_ids)
  return _impl_.stack_frame_ids_.Mutable(index);
}
inline void CodeLocation::set_stack_frame_ids(int index, const std::string& value) {
  _impl_.stack_frame_ids_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.CodeLocation.stack_frame_ids)
}
inline void CodeLocation::set_stack_frame_ids(int index, std::string&& value) {
  _impl_.stack_frame_ids_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.CodeLocation.stack_frame_ids)
}
inline void CodeLocation::set_stack_frame_ids(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.stack_frame_ids_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.CodeLocation.stack_frame_ids)
}
inline void CodeLocation::set_stack_frame_ids(int index, const char* value, size_t size) {
  _impl_.stack_frame_ids_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.CodeLocation.stack_frame_ids)
}
inline std::string* CodeLocation::_internal_add_stack_frame_ids() {
  return _impl_.stack_frame_ids_.Add();
}
inline void CodeLocation::add_stack_frame_ids(const std::string& value) {
  _impl_.stack_frame_ids_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.CodeLocation.stack_frame_ids)
}
inline void CodeLocation::add_stack_frame_ids(std::string&& value) {
  _impl_.stack_frame_ids_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.CodeLocation.stack_frame_ids)
}
inline void CodeLocation::add_stack_frame_ids(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.stack_frame_ids_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.CodeLocation.stack_frame_ids)
}
inline void CodeLocation::add_stack_frame_ids(const char* value, size_t size) {
  _impl_.stack_frame_ids_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.CodeLocation.stack_frame_ids)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
CodeLocation::stack_frame_ids() const {
  // @@protoc_insertion_point(field_list:tensorflow.CodeLocation.stack_frame_ids)
  return _impl_.stack_frame_ids_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
CodeLocation::mutable_stack_frame_ids() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.CodeLocation.stack_frame_ids)
  return &_impl_.stack_frame_ids_;
}

// -------------------------------------------------------------------

// GraphOpCreation

// string op_type = 1;
inline void GraphOpCreation::clear_op_type() {
  _impl_.op_type_.ClearToEmpty();
}
inline const std::string& GraphOpCreation::op_type() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphOpCreation.op_type)
  return _internal_op_type();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GraphOpCreation::set_op_type(ArgT0&& arg0, ArgT... args) {
 
 _impl_.op_type_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.GraphOpCreation.op_type)
}
inline std::string* GraphOpCreation::mutable_op_type() {
  std::string* _s = _internal_mutable_op_type();
  // @@protoc_insertion_point(field_mutable:tensorflow.GraphOpCreation.op_type)
  return _s;
}
inline const std::string& GraphOpCreation::_internal_op_type() const {
  return _impl_.op_type_.Get();
}
inline void GraphOpCreation::_internal_set_op_type(const std::string& value) {
  
  _impl_.op_type_.Set(value, GetArenaForAllocation());
}
inline std::string* GraphOpCreation::_internal_mutable_op_type() {
  
  return _impl_.op_type_.Mutable(GetArenaForAllocation());
}
inline std::string* GraphOpCreation::release_op_type() {
  // @@protoc_insertion_point(field_release:tensorflow.GraphOpCreation.op_type)
  return _impl_.op_type_.Release();
}
inline void GraphOpCreation::set_allocated_op_type(std::string* op_type) {
  if (op_type != nullptr) {
    
  } else {
    
  }
  _impl_.op_type_.SetAllocated(op_type, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.op_type_.IsDefault()) {
    _impl_.op_type_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GraphOpCreation.op_type)
}

// string op_name = 2;
inline void GraphOpCreation::clear_op_name() {
  _impl_.op_name_.ClearToEmpty();
}
inline const std::string& GraphOpCreation::op_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphOpCreation.op_name)
  return _internal_op_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GraphOpCreation::set_op_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.op_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.GraphOpCreation.op_name)
}
inline std::string* GraphOpCreation::mutable_op_name() {
  std::string* _s = _internal_mutable_op_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.GraphOpCreation.op_name)
  return _s;
}
inline const std::string& GraphOpCreation::_internal_op_name() const {
  return _impl_.op_name_.Get();
}
inline void GraphOpCreation::_internal_set_op_name(const std::string& value) {
  
  _impl_.op_name_.Set(value, GetArenaForAllocation());
}
inline std::string* GraphOpCreation::_internal_mutable_op_name() {
  
  return _impl_.op_name_.Mutable(GetArenaForAllocation());
}
inline std::string* GraphOpCreation::release_op_name() {
  // @@protoc_insertion_point(field_release:tensorflow.GraphOpCreation.op_name)
  return _impl_.op_name_.Release();
}
inline void GraphOpCreation::set_allocated_op_name(std::string* op_name) {
  if (op_name != nullptr) {
    
  } else {
    
  }
  _impl_.op_name_.SetAllocated(op_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.op_name_.IsDefault()) {
    _impl_.op_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GraphOpCreation.op_name)
}

// string graph_name = 3;
inline void GraphOpCreation::clear_graph_name() {
  _impl_.graph_name_.ClearToEmpty();
}
inline const std::string& GraphOpCreation::graph_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphOpCreation.graph_name)
  return _internal_graph_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GraphOpCreation::set_graph_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.graph_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.GraphOpCreation.graph_name)
}
inline std::string* GraphOpCreation::mutable_graph_name() {
  std::string* _s = _internal_mutable_graph_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.GraphOpCreation.graph_name)
  return _s;
}
inline const std::string& GraphOpCreation::_internal_graph_name() const {
  return _impl_.graph_name_.Get();
}
inline void GraphOpCreation::_internal_set_graph_name(const std::string& value) {
  
  _impl_.graph_name_.Set(value, GetArenaForAllocation());
}
inline std::string* GraphOpCreation::_internal_mutable_graph_name() {
  
  return _impl_.graph_name_.Mutable(GetArenaForAllocation());
}
inline std::string* GraphOpCreation::release_graph_name() {
  // @@protoc_insertion_point(field_release:tensorflow.GraphOpCreation.graph_name)
  return _impl_.graph_name_.Release();
}
inline void GraphOpCreation::set_allocated_graph_name(std::string* graph_name) {
  if (graph_name != nullptr) {
    
  } else {
    
  }
  _impl_.graph_name_.SetAllocated(graph_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.graph_name_.IsDefault()) {
    _impl_.graph_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GraphOpCreation.graph_name)
}

// string graph_id = 4;
inline void GraphOpCreation::clear_graph_id() {
  _impl_.graph_id_.ClearToEmpty();
}
inline const std::string& GraphOpCreation::graph_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphOpCreation.graph_id)
  return _internal_graph_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GraphOpCreation::set_graph_id(ArgT0&& arg0, ArgT... args) {
 
 _impl_.graph_id_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.GraphOpCreation.graph_id)
}
inline std::string* GraphOpCreation::mutable_graph_id() {
  std::string* _s = _internal_mutable_graph_id();
  // @@protoc_insertion_point(field_mutable:tensorflow.GraphOpCreation.graph_id)
  return _s;
}
inline const std::string& GraphOpCreation::_internal_graph_id() const {
  return _impl_.graph_id_.Get();
}
inline void GraphOpCreation::_internal_set_graph_id(const std::string& value) {
  
  _impl_.graph_id_.Set(value, GetArenaForAllocation());
}
inline std::string* GraphOpCreation::_internal_mutable_graph_id() {
  
  return _impl_.graph_id_.Mutable(GetArenaForAllocation());
}
inline std::string* GraphOpCreation::release_graph_id() {
  // @@protoc_insertion_point(field_release:tensorflow.GraphOpCreation.graph_id)
  return _impl_.graph_id_.Release();
}
inline void GraphOpCreation::set_allocated_graph_id(std::string* graph_id) {
  if (graph_id != nullptr) {
    
  } else {
    
  }
  _impl_.graph_id_.SetAllocated(graph_id, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.graph_id_.IsDefault()) {
    _impl_.graph_id_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GraphOpCreation.graph_id)
}

// string device_name = 5;
inline void GraphOpCreation::clear_device_name() {
  _impl_.device_name_.ClearToEmpty();
}
inline const std::string& GraphOpCreation::device_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphOpCreation.device_name)
  return _internal_device_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GraphOpCreation::set_device_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.device_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.GraphOpCreation.device_name)
}
inline std::string* GraphOpCreation::mutable_device_name() {
  std::string* _s = _internal_mutable_device_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.GraphOpCreation.device_name)
  return _s;
}
inline const std::string& GraphOpCreation::_internal_device_name() const {
  return _impl_.device_name_.Get();
}
inline void GraphOpCreation::_internal_set_device_name(const std::string& value) {
  
  _impl_.device_name_.Set(value, GetArenaForAllocation());
}
inline std::string* GraphOpCreation::_internal_mutable_device_name() {
  
  return _impl_.device_name_.Mutable(GetArenaForAllocation());
}
inline std::string* GraphOpCreation::release_device_name() {
  // @@protoc_insertion_point(field_release:tensorflow.GraphOpCreation.device_name)
  return _impl_.device_name_.Release();
}
inline void GraphOpCreation::set_allocated_device_name(std::string* device_name) {
  if (device_name != nullptr) {
    
  } else {
    
  }
  _impl_.device_name_.SetAllocated(device_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.device_name_.IsDefault()) {
    _impl_.device_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GraphOpCreation.device_name)
}

// repeated string input_names = 6;
inline int GraphOpCreation::_internal_input_names_size() const {
  return _impl_.input_names_.size();
}
inline int GraphOpCreation::input_names_size() const {
  return _internal_input_names_size();
}
inline void GraphOpCreation::clear_input_names() {
  _impl_.input_names_.Clear();
}
inline std::string* GraphOpCreation::add_input_names() {
  std::string* _s = _internal_add_input_names();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.GraphOpCreation.input_names)
  return _s;
}
inline const std::string& GraphOpCreation::_internal_input_names(int index) const {
  return _impl_.input_names_.Get(index);
}
inline const std::string& GraphOpCreation::input_names(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphOpCreation.input_names)
  return _internal_input_names(index);
}
inline std::string* GraphOpCreation::mutable_input_names(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.GraphOpCreation.input_names)
  return _impl_.input_names_.Mutable(index);
}
inline void GraphOpCreation::set_input_names(int index, const std::string& value) {
  _impl_.input_names_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.GraphOpCreation.input_names)
}
inline void GraphOpCreation::set_input_names(int index, std::string&& value) {
  _impl_.input_names_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.GraphOpCreation.input_names)
}
inline void GraphOpCreation::set_input_names(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.input_names_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.GraphOpCreation.input_names)
}
inline void GraphOpCreation::set_input_names(int index, const char* value, size_t size) {
  _impl_.input_names_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.GraphOpCreation.input_names)
}
inline std::string* GraphOpCreation::_internal_add_input_names() {
  return _impl_.input_names_.Add();
}
inline void GraphOpCreation::add_input_names(const std::string& value) {
  _impl_.input_names_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.GraphOpCreation.input_names)
}
inline void GraphOpCreation::add_input_names(std::string&& value) {
  _impl_.input_names_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.GraphOpCreation.input_names)
}
inline void GraphOpCreation::add_input_names(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.input_names_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.GraphOpCreation.input_names)
}
inline void GraphOpCreation::add_input_names(const char* value, size_t size) {
  _impl_.input_names_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.GraphOpCreation.input_names)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
GraphOpCreation::input_names() const {
  // @@protoc_insertion_point(field_list:tensorflow.GraphOpCreation.input_names)
  return _impl_.input_names_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
GraphOpCreation::mutable_input_names() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.GraphOpCreation.input_names)
  return &_impl_.input_names_;
}

// int32 num_outputs = 7;
inline void GraphOpCreation::clear_num_outputs() {
  _impl_.num_outputs_ = 0;
}
inline int32_t GraphOpCreation::_internal_num_outputs() const {
  return _impl_.num_outputs_;
}
inline int32_t GraphOpCreation::num_outputs() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphOpCreation.num_outputs)
  return _internal_num_outputs();
}
inline void GraphOpCreation::_internal_set_num_outputs(int32_t value) {
  
  _impl_.num_outputs_ = value;
}
inline void GraphOpCreation::set_num_outputs(int32_t value) {
  _internal_set_num_outputs(value);
  // @@protoc_insertion_point(field_set:tensorflow.GraphOpCreation.num_outputs)
}

// .tensorflow.CodeLocation code_location = 8;
inline bool GraphOpCreation::_internal_has_code_location() const {
  return this != internal_default_instance() && _impl_.code_location_ != nullptr;
}
inline bool GraphOpCreation::has_code_location() const {
  return _internal_has_code_location();
}
inline void GraphOpCreation::clear_code_location() {
  if (GetArenaForAllocation() == nullptr && _impl_.code_location_ != nullptr) {
    delete _impl_.code_location_;
  }
  _impl_.code_location_ = nullptr;
}
inline const ::tensorflow::CodeLocation& GraphOpCreation::_internal_code_location() const {
  const ::tensorflow::CodeLocation* p = _impl_.code_location_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::CodeLocation&>(
      ::tensorflow::_CodeLocation_default_instance_);
}
inline const ::tensorflow::CodeLocation& GraphOpCreation::code_location() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphOpCreation.code_location)
  return _internal_code_location();
}
inline void GraphOpCreation::unsafe_arena_set_allocated_code_location(
    ::tensorflow::CodeLocation* code_location) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.code_location_);
  }
  _impl_.code_location_ = code_location;
  if (code_location) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.GraphOpCreation.code_location)
}
inline ::tensorflow::CodeLocation* GraphOpCreation::release_code_location() {
  
  ::tensorflow::CodeLocation* temp = _impl_.code_location_;
  _impl_.code_location_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::CodeLocation* GraphOpCreation::unsafe_arena_release_code_location() {
  // @@protoc_insertion_point(field_release:tensorflow.GraphOpCreation.code_location)
  
  ::tensorflow::CodeLocation* temp = _impl_.code_location_;
  _impl_.code_location_ = nullptr;
  return temp;
}
inline ::tensorflow::CodeLocation* GraphOpCreation::_internal_mutable_code_location() {
  
  if (_impl_.code_location_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::CodeLocation>(GetArenaForAllocation());
    _impl_.code_location_ = p;
  }
  return _impl_.code_location_;
}
inline ::tensorflow::CodeLocation* GraphOpCreation::mutable_code_location() {
  ::tensorflow::CodeLocation* _msg = _internal_mutable_code_location();
  // @@protoc_insertion_point(field_mutable:tensorflow.GraphOpCreation.code_location)
  return _msg;
}
inline void GraphOpCreation::set_allocated_code_location(::tensorflow::CodeLocation* code_location) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.code_location_;
  }
  if (code_location) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(code_location);
    if (message_arena != submessage_arena) {
      code_location = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, code_location, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.code_location_ = code_location;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GraphOpCreation.code_location)
}

// repeated int32 output_tensor_ids = 9;
inline int GraphOpCreation::_internal_output_tensor_ids_size() const {
  return _impl_.output_tensor_ids_.size();
}
inline int GraphOpCreation::output_tensor_ids_size() const {
  return _internal_output_tensor_ids_size();
}
inline void GraphOpCreation::clear_output_tensor_ids() {
  _impl_.output_tensor_ids_.Clear();
}
inline int32_t GraphOpCreation::_internal_output_tensor_ids(int index) const {
  return _impl_.output_tensor_ids_.Get(index);
}
inline int32_t GraphOpCreation::output_tensor_ids(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphOpCreation.output_tensor_ids)
  return _internal_output_tensor_ids(index);
}
inline void GraphOpCreation::set_output_tensor_ids(int index, int32_t value) {
  _impl_.output_tensor_ids_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.GraphOpCreation.output_tensor_ids)
}
inline void GraphOpCreation::_internal_add_output_tensor_ids(int32_t value) {
  _impl_.output_tensor_ids_.Add(value);
}
inline void GraphOpCreation::add_output_tensor_ids(int32_t value) {
  _internal_add_output_tensor_ids(value);
  // @@protoc_insertion_point(field_add:tensorflow.GraphOpCreation.output_tensor_ids)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
GraphOpCreation::_internal_output_tensor_ids() const {
  return _impl_.output_tensor_ids_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
GraphOpCreation::output_tensor_ids() const {
  // @@protoc_insertion_point(field_list:tensorflow.GraphOpCreation.output_tensor_ids)
  return _internal_output_tensor_ids();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
GraphOpCreation::_internal_mutable_output_tensor_ids() {
  return &_impl_.output_tensor_ids_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
GraphOpCreation::mutable_output_tensor_ids() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.GraphOpCreation.output_tensor_ids)
  return _internal_mutable_output_tensor_ids();
}

// -------------------------------------------------------------------

// DebuggedGraph

// string graph_id = 1;
inline void DebuggedGraph::clear_graph_id() {
  _impl_.graph_id_.ClearToEmpty();
}
inline const std::string& DebuggedGraph::graph_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebuggedGraph.graph_id)
  return _internal_graph_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DebuggedGraph::set_graph_id(ArgT0&& arg0, ArgT... args) {
 
 _impl_.graph_id_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.DebuggedGraph.graph_id)
}
inline std::string* DebuggedGraph::mutable_graph_id() {
  std::string* _s = _internal_mutable_graph_id();
  // @@protoc_insertion_point(field_mutable:tensorflow.DebuggedGraph.graph_id)
  return _s;
}
inline const std::string& DebuggedGraph::_internal_graph_id() const {
  return _impl_.graph_id_.Get();
}
inline void DebuggedGraph::_internal_set_graph_id(const std::string& value) {
  
  _impl_.graph_id_.Set(value, GetArenaForAllocation());
}
inline std::string* DebuggedGraph::_internal_mutable_graph_id() {
  
  return _impl_.graph_id_.Mutable(GetArenaForAllocation());
}
inline std::string* DebuggedGraph::release_graph_id() {
  // @@protoc_insertion_point(field_release:tensorflow.DebuggedGraph.graph_id)
  return _impl_.graph_id_.Release();
}
inline void DebuggedGraph::set_allocated_graph_id(std::string* graph_id) {
  if (graph_id != nullptr) {
    
  } else {
    
  }
  _impl_.graph_id_.SetAllocated(graph_id, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.graph_id_.IsDefault()) {
    _impl_.graph_id_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DebuggedGraph.graph_id)
}

// string graph_name = 2;
inline void DebuggedGraph::clear_graph_name() {
  _impl_.graph_name_.ClearToEmpty();
}
inline const std::string& DebuggedGraph::graph_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebuggedGraph.graph_name)
  return _internal_graph_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DebuggedGraph::set_graph_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.graph_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.DebuggedGraph.graph_name)
}
inline std::string* DebuggedGraph::mutable_graph_name() {
  std::string* _s = _internal_mutable_graph_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.DebuggedGraph.graph_name)
  return _s;
}
inline const std::string& DebuggedGraph::_internal_graph_name() const {
  return _impl_.graph_name_.Get();
}
inline void DebuggedGraph::_internal_set_graph_name(const std::string& value) {
  
  _impl_.graph_name_.Set(value, GetArenaForAllocation());
}
inline std::string* DebuggedGraph::_internal_mutable_graph_name() {
  
  return _impl_.graph_name_.Mutable(GetArenaForAllocation());
}
inline std::string* DebuggedGraph::release_graph_name() {
  // @@protoc_insertion_point(field_release:tensorflow.DebuggedGraph.graph_name)
  return _impl_.graph_name_.Release();
}
inline void DebuggedGraph::set_allocated_graph_name(std::string* graph_name) {
  if (graph_name != nullptr) {
    
  } else {
    
  }
  _impl_.graph_name_.SetAllocated(graph_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.graph_name_.IsDefault()) {
    _impl_.graph_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DebuggedGraph.graph_name)
}

// repeated string instrumented_ops = 3;
inline int DebuggedGraph::_internal_instrumented_ops_size() const {
  return _impl_.instrumented_ops_.size();
}
inline int DebuggedGraph::instrumented_ops_size() const {
  return _internal_instrumented_ops_size();
}
inline void DebuggedGraph::clear_instrumented_ops() {
  _impl_.instrumented_ops_.Clear();
}
inline std::string* DebuggedGraph::add_instrumented_ops() {
  std::string* _s = _internal_add_instrumented_ops();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.DebuggedGraph.instrumented_ops)
  return _s;
}
inline const std::string& DebuggedGraph::_internal_instrumented_ops(int index) const {
  return _impl_.instrumented_ops_.Get(index);
}
inline const std::string& DebuggedGraph::instrumented_ops(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.DebuggedGraph.instrumented_ops)
  return _internal_instrumented_ops(index);
}
inline std::string* DebuggedGraph::mutable_instrumented_ops(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.DebuggedGraph.instrumented_ops)
  return _impl_.instrumented_ops_.Mutable(index);
}
inline void DebuggedGraph::set_instrumented_ops(int index, const std::string& value) {
  _impl_.instrumented_ops_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.DebuggedGraph.instrumented_ops)
}
inline void DebuggedGraph::set_instrumented_ops(int index, std::string&& value) {
  _impl_.instrumented_ops_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.DebuggedGraph.instrumented_ops)
}
inline void DebuggedGraph::set_instrumented_ops(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.instrumented_ops_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.DebuggedGraph.instrumented_ops)
}
inline void DebuggedGraph::set_instrumented_ops(int index, const char* value, size_t size) {
  _impl_.instrumented_ops_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.DebuggedGraph.instrumented_ops)
}
inline std::string* DebuggedGraph::_internal_add_instrumented_ops() {
  return _impl_.instrumented_ops_.Add();
}
inline void DebuggedGraph::add_instrumented_ops(const std::string& value) {
  _impl_.instrumented_ops_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.DebuggedGraph.instrumented_ops)
}
inline void DebuggedGraph::add_instrumented_ops(std::string&& value) {
  _impl_.instrumented_ops_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.DebuggedGraph.instrumented_ops)
}
inline void DebuggedGraph::add_instrumented_ops(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.instrumented_ops_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.DebuggedGraph.instrumented_ops)
}
inline void DebuggedGraph::add_instrumented_ops(const char* value, size_t size) {
  _impl_.instrumented_ops_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.DebuggedGraph.instrumented_ops)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
DebuggedGraph::instrumented_ops() const {
  // @@protoc_insertion_point(field_list:tensorflow.DebuggedGraph.instrumented_ops)
  return _impl_.instrumented_ops_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
DebuggedGraph::mutable_instrumented_ops() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.DebuggedGraph.instrumented_ops)
  return &_impl_.instrumented_ops_;
}

// bytes original_graph_def = 4;
inline void DebuggedGraph::clear_original_graph_def() {
  _impl_.original_graph_def_.ClearToEmpty();
}
inline const std::string& DebuggedGraph::original_graph_def() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebuggedGraph.original_graph_def)
  return _internal_original_graph_def();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DebuggedGraph::set_original_graph_def(ArgT0&& arg0, ArgT... args) {
 
 _impl_.original_graph_def_.SetBytes(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.DebuggedGraph.original_graph_def)
}
inline std::string* DebuggedGraph::mutable_original_graph_def() {
  std::string* _s = _internal_mutable_original_graph_def();
  // @@protoc_insertion_point(field_mutable:tensorflow.DebuggedGraph.original_graph_def)
  return _s;
}
inline const std::string& DebuggedGraph::_internal_original_graph_def() const {
  return _impl_.original_graph_def_.Get();
}
inline void DebuggedGraph::_internal_set_original_graph_def(const std::string& value) {
  
  _impl_.original_graph_def_.Set(value, GetArenaForAllocation());
}
inline std::string* DebuggedGraph::_internal_mutable_original_graph_def() {
  
  return _impl_.original_graph_def_.Mutable(GetArenaForAllocation());
}
inline std::string* DebuggedGraph::release_original_graph_def() {
  // @@protoc_insertion_point(field_release:tensorflow.DebuggedGraph.original_graph_def)
  return _impl_.original_graph_def_.Release();
}
inline void DebuggedGraph::set_allocated_original_graph_def(std::string* original_graph_def) {
  if (original_graph_def != nullptr) {
    
  } else {
    
  }
  _impl_.original_graph_def_.SetAllocated(original_graph_def, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.original_graph_def_.IsDefault()) {
    _impl_.original_graph_def_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DebuggedGraph.original_graph_def)
}

// bytes instrumented_graph_def = 5;
inline void DebuggedGraph::clear_instrumented_graph_def() {
  _impl_.instrumented_graph_def_.ClearToEmpty();
}
inline const std::string& DebuggedGraph::instrumented_graph_def() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebuggedGraph.instrumented_graph_def)
  return _internal_instrumented_graph_def();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DebuggedGraph::set_instrumented_graph_def(ArgT0&& arg0, ArgT... args) {
 
 _impl_.instrumented_graph_def_.SetBytes(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.DebuggedGraph.instrumented_graph_def)
}
inline std::string* DebuggedGraph::mutable_instrumented_graph_def() {
  std::string* _s = _internal_mutable_instrumented_graph_def();
  // @@protoc_insertion_point(field_mutable:tensorflow.DebuggedGraph.instrumented_graph_def)
  return _s;
}
inline const std::string& DebuggedGraph::_internal_instrumented_graph_def() const {
  return _impl_.instrumented_graph_def_.Get();
}
inline void DebuggedGraph::_internal_set_instrumented_graph_def(const std::string& value) {
  
  _impl_.instrumented_graph_def_.Set(value, GetArenaForAllocation());
}
inline std::string* DebuggedGraph::_internal_mutable_instrumented_graph_def() {
  
  return _impl_.instrumented_graph_def_.Mutable(GetArenaForAllocation());
}
inline std::string* DebuggedGraph::release_instrumented_graph_def() {
  // @@protoc_insertion_point(field_release:tensorflow.DebuggedGraph.instrumented_graph_def)
  return _impl_.instrumented_graph_def_.Release();
}
inline void DebuggedGraph::set_allocated_instrumented_graph_def(std::string* instrumented_graph_def) {
  if (instrumented_graph_def != nullptr) {
    
  } else {
    
  }
  _impl_.instrumented_graph_def_.SetAllocated(instrumented_graph_def, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.instrumented_graph_def_.IsDefault()) {
    _impl_.instrumented_graph_def_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DebuggedGraph.instrumented_graph_def)
}

// string outer_context_id = 6;
inline void DebuggedGraph::clear_outer_context_id() {
  _impl_.outer_context_id_.ClearToEmpty();
}
inline const std::string& DebuggedGraph::outer_context_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebuggedGraph.outer_context_id)
  return _internal_outer_context_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DebuggedGraph::set_outer_context_id(ArgT0&& arg0, ArgT... args) {
 
 _impl_.outer_context_id_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.DebuggedGraph.outer_context_id)
}
inline std::string* DebuggedGraph::mutable_outer_context_id() {
  std::string* _s = _internal_mutable_outer_context_id();
  // @@protoc_insertion_point(field_mutable:tensorflow.DebuggedGraph.outer_context_id)
  return _s;
}
inline const std::string& DebuggedGraph::_internal_outer_context_id() const {
  return _impl_.outer_context_id_.Get();
}
inline void DebuggedGraph::_internal_set_outer_context_id(const std::string& value) {
  
  _impl_.outer_context_id_.Set(value, GetArenaForAllocation());
}
inline std::string* DebuggedGraph::_internal_mutable_outer_context_id() {
  
  return _impl_.outer_context_id_.Mutable(GetArenaForAllocation());
}
inline std::string* DebuggedGraph::release_outer_context_id() {
  // @@protoc_insertion_point(field_release:tensorflow.DebuggedGraph.outer_context_id)
  return _impl_.outer_context_id_.Release();
}
inline void DebuggedGraph::set_allocated_outer_context_id(std::string* outer_context_id) {
  if (outer_context_id != nullptr) {
    
  } else {
    
  }
  _impl_.outer_context_id_.SetAllocated(outer_context_id, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.outer_context_id_.IsDefault()) {
    _impl_.outer_context_id_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DebuggedGraph.outer_context_id)
}

// -------------------------------------------------------------------

// DebuggedDevice

// string device_name = 1;
inline void DebuggedDevice::clear_device_name() {
  _impl_.device_name_.ClearToEmpty();
}
inline const std::string& DebuggedDevice::device_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebuggedDevice.device_name)
  return _internal_device_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DebuggedDevice::set_device_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.device_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.DebuggedDevice.device_name)
}
inline std::string* DebuggedDevice::mutable_device_name() {
  std::string* _s = _internal_mutable_device_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.DebuggedDevice.device_name)
  return _s;
}
inline const std::string& DebuggedDevice::_internal_device_name() const {
  return _impl_.device_name_.Get();
}
inline void DebuggedDevice::_internal_set_device_name(const std::string& value) {
  
  _impl_.device_name_.Set(value, GetArenaForAllocation());
}
inline std::string* DebuggedDevice::_internal_mutable_device_name() {
  
  return _impl_.device_name_.Mutable(GetArenaForAllocation());
}
inline std::string* DebuggedDevice::release_device_name() {
  // @@protoc_insertion_point(field_release:tensorflow.DebuggedDevice.device_name)
  return _impl_.device_name_.Release();
}
inline void DebuggedDevice::set_allocated_device_name(std::string* device_name) {
  if (device_name != nullptr) {
    
  } else {
    
  }
  _impl_.device_name_.SetAllocated(device_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.device_name_.IsDefault()) {
    _impl_.device_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DebuggedDevice.device_name)
}

// int32 device_id = 2;
inline void DebuggedDevice::clear_device_id() {
  _impl_.device_id_ = 0;
}
inline int32_t DebuggedDevice::_internal_device_id() const {
  return _impl_.device_id_;
}
inline int32_t DebuggedDevice::device_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebuggedDevice.device_id)
  return _internal_device_id();
}
inline void DebuggedDevice::_internal_set_device_id(int32_t value) {
  
  _impl_.device_id_ = value;
}
inline void DebuggedDevice::set_device_id(int32_t value) {
  _internal_set_device_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.DebuggedDevice.device_id)
}

// -------------------------------------------------------------------

// Execution

// string op_type = 1;
inline void Execution::clear_op_type() {
  _impl_.op_type_.ClearToEmpty();
}
inline const std::string& Execution::op_type() const {
  // @@protoc_insertion_point(field_get:tensorflow.Execution.op_type)
  return _internal_op_type();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Execution::set_op_type(ArgT0&& arg0, ArgT... args) {
 
 _impl_.op_type_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.Execution.op_type)
}
inline std::string* Execution::mutable_op_type() {
  std::string* _s = _internal_mutable_op_type();
  // @@protoc_insertion_point(field_mutable:tensorflow.Execution.op_type)
  return _s;
}
inline const std::string& Execution::_internal_op_type() const {
  return _impl_.op_type_.Get();
}
inline void Execution::_internal_set_op_type(const std::string& value) {
  
  _impl_.op_type_.Set(value, GetArenaForAllocation());
}
inline std::string* Execution::_internal_mutable_op_type() {
  
  return _impl_.op_type_.Mutable(GetArenaForAllocation());
}
inline std::string* Execution::release_op_type() {
  // @@protoc_insertion_point(field_release:tensorflow.Execution.op_type)
  return _impl_.op_type_.Release();
}
inline void Execution::set_allocated_op_type(std::string* op_type) {
  if (op_type != nullptr) {
    
  } else {
    
  }
  _impl_.op_type_.SetAllocated(op_type, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.op_type_.IsDefault()) {
    _impl_.op_type_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.Execution.op_type)
}

// int32 num_outputs = 2;
inline void Execution::clear_num_outputs() {
  _impl_.num_outputs_ = 0;
}
inline int32_t Execution::_internal_num_outputs() const {
  return _impl_.num_outputs_;
}
inline int32_t Execution::num_outputs() const {
  // @@protoc_insertion_point(field_get:tensorflow.Execution.num_outputs)
  return _internal_num_outputs();
}
inline void Execution::_internal_set_num_outputs(int32_t value) {
  
  _impl_.num_outputs_ = value;
}
inline void Execution::set_num_outputs(int32_t value) {
  _internal_set_num_outputs(value);
  // @@protoc_insertion_point(field_set:tensorflow.Execution.num_outputs)
}

// string graph_id = 3;
inline void Execution::clear_graph_id() {
  _impl_.graph_id_.ClearToEmpty();
}
inline const std::string& Execution::graph_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.Execution.graph_id)
  return _internal_graph_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Execution::set_graph_id(ArgT0&& arg0, ArgT... args) {
 
 _impl_.graph_id_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.Execution.graph_id)
}
inline std::string* Execution::mutable_graph_id() {
  std::string* _s = _internal_mutable_graph_id();
  // @@protoc_insertion_point(field_mutable:tensorflow.Execution.graph_id)
  return _s;
}
inline const std::string& Execution::_internal_graph_id() const {
  return _impl_.graph_id_.Get();
}
inline void Execution::_internal_set_graph_id(const std::string& value) {
  
  _impl_.graph_id_.Set(value, GetArenaForAllocation());
}
inline std::string* Execution::_internal_mutable_graph_id() {
  
  return _impl_.graph_id_.Mutable(GetArenaForAllocation());
}
inline std::string* Execution::release_graph_id() {
  // @@protoc_insertion_point(field_release:tensorflow.Execution.graph_id)
  return _impl_.graph_id_.Release();
}
inline void Execution::set_allocated_graph_id(std::string* graph_id) {
  if (graph_id != nullptr) {
    
  } else {
    
  }
  _impl_.graph_id_.SetAllocated(graph_id, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.graph_id_.IsDefault()) {
    _impl_.graph_id_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.Execution.graph_id)
}

// repeated int64 input_tensor_ids = 4;
inline int Execution::_internal_input_tensor_ids_size() const {
  return _impl_.input_tensor_ids_.size();
}
inline int Execution::input_tensor_ids_size() const {
  return _internal_input_tensor_ids_size();
}
inline void Execution::clear_input_tensor_ids() {
  _impl_.input_tensor_ids_.Clear();
}
inline int64_t Execution::_internal_input_tensor_ids(int index) const {
  return _impl_.input_tensor_ids_.Get(index);
}
inline int64_t Execution::input_tensor_ids(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.Execution.input_tensor_ids)
  return _internal_input_tensor_ids(index);
}
inline void Execution::set_input_tensor_ids(int index, int64_t value) {
  _impl_.input_tensor_ids_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.Execution.input_tensor_ids)
}
inline void Execution::_internal_add_input_tensor_ids(int64_t value) {
  _impl_.input_tensor_ids_.Add(value);
}
inline void Execution::add_input_tensor_ids(int64_t value) {
  _internal_add_input_tensor_ids(value);
  // @@protoc_insertion_point(field_add:tensorflow.Execution.input_tensor_ids)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
Execution::_internal_input_tensor_ids() const {
  return _impl_.input_tensor_ids_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
Execution::input_tensor_ids() const {
  // @@protoc_insertion_point(field_list:tensorflow.Execution.input_tensor_ids)
  return _internal_input_tensor_ids();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
Execution::_internal_mutable_input_tensor_ids() {
  return &_impl_.input_tensor_ids_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
Execution::mutable_input_tensor_ids() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.Execution.input_tensor_ids)
  return _internal_mutable_input_tensor_ids();
}

// repeated int64 output_tensor_ids = 5;
inline int Execution::_internal_output_tensor_ids_size() const {
  return _impl_.output_tensor_ids_.size();
}
inline int Execution::output_tensor_ids_size() const {
  return _internal_output_tensor_ids_size();
}
inline void Execution::clear_output_tensor_ids() {
  _impl_.output_tensor_ids_.Clear();
}
inline int64_t Execution::_internal_output_tensor_ids(int index) const {
  return _impl_.output_tensor_ids_.Get(index);
}
inline int64_t Execution::output_tensor_ids(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.Execution.output_tensor_ids)
  return _internal_output_tensor_ids(index);
}
inline void Execution::set_output_tensor_ids(int index, int64_t value) {
  _impl_.output_tensor_ids_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.Execution.output_tensor_ids)
}
inline void Execution::_internal_add_output_tensor_ids(int64_t value) {
  _impl_.output_tensor_ids_.Add(value);
}
inline void Execution::add_output_tensor_ids(int64_t value) {
  _internal_add_output_tensor_ids(value);
  // @@protoc_insertion_point(field_add:tensorflow.Execution.output_tensor_ids)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
Execution::_internal_output_tensor_ids() const {
  return _impl_.output_tensor_ids_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
Execution::output_tensor_ids() const {
  // @@protoc_insertion_point(field_list:tensorflow.Execution.output_tensor_ids)
  return _internal_output_tensor_ids();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
Execution::_internal_mutable_output_tensor_ids() {
  return &_impl_.output_tensor_ids_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
Execution::mutable_output_tensor_ids() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.Execution.output_tensor_ids)
  return _internal_mutable_output_tensor_ids();
}

// .tensorflow.TensorDebugMode tensor_debug_mode = 6;
inline void Execution::clear_tensor_debug_mode() {
  _impl_.tensor_debug_mode_ = 0;
}
inline ::tensorflow::TensorDebugMode Execution::_internal_tensor_debug_mode() const {
  return static_cast< ::tensorflow::TensorDebugMode >(_impl_.tensor_debug_mode_);
}
inline ::tensorflow::TensorDebugMode Execution::tensor_debug_mode() const {
  // @@protoc_insertion_point(field_get:tensorflow.Execution.tensor_debug_mode)
  return _internal_tensor_debug_mode();
}
inline void Execution::_internal_set_tensor_debug_mode(::tensorflow::TensorDebugMode value) {
  
  _impl_.tensor_debug_mode_ = value;
}
inline void Execution::set_tensor_debug_mode(::tensorflow::TensorDebugMode value) {
  _internal_set_tensor_debug_mode(value);
  // @@protoc_insertion_point(field_set:tensorflow.Execution.tensor_debug_mode)
}

// repeated .tensorflow.TensorProto tensor_protos = 7;
inline int Execution::_internal_tensor_protos_size() const {
  return _impl_.tensor_protos_.size();
}
inline int Execution::tensor_protos_size() const {
  return _internal_tensor_protos_size();
}
inline ::tensorflow::TensorProto* Execution::mutable_tensor_protos(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.Execution.tensor_protos)
  return _impl_.tensor_protos_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >*
Execution::mutable_tensor_protos() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.Execution.tensor_protos)
  return &_impl_.tensor_protos_;
}
inline const ::tensorflow::TensorProto& Execution::_internal_tensor_protos(int index) const {
  return _impl_.tensor_protos_.Get(index);
}
inline const ::tensorflow::TensorProto& Execution::tensor_protos(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.Execution.tensor_protos)
  return _internal_tensor_protos(index);
}
inline ::tensorflow::TensorProto* Execution::_internal_add_tensor_protos() {
  return _impl_.tensor_protos_.Add();
}
inline ::tensorflow::TensorProto* Execution::add_tensor_protos() {
  ::tensorflow::TensorProto* _add = _internal_add_tensor_protos();
  // @@protoc_insertion_point(field_add:tensorflow.Execution.tensor_protos)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >&
Execution::tensor_protos() const {
  // @@protoc_insertion_point(field_list:tensorflow.Execution.tensor_protos)
  return _impl_.tensor_protos_;
}

// .tensorflow.CodeLocation code_location = 8;
inline bool Execution::_internal_has_code_location() const {
  return this != internal_default_instance() && _impl_.code_location_ != nullptr;
}
inline bool Execution::has_code_location() const {
  return _internal_has_code_location();
}
inline void Execution::clear_code_location() {
  if (GetArenaForAllocation() == nullptr && _impl_.code_location_ != nullptr) {
    delete _impl_.code_location_;
  }
  _impl_.code_location_ = nullptr;
}
inline const ::tensorflow::CodeLocation& Execution::_internal_code_location() const {
  const ::tensorflow::CodeLocation* p = _impl_.code_location_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::CodeLocation&>(
      ::tensorflow::_CodeLocation_default_instance_);
}
inline const ::tensorflow::CodeLocation& Execution::code_location() const {
  // @@protoc_insertion_point(field_get:tensorflow.Execution.code_location)
  return _internal_code_location();
}
inline void Execution::unsafe_arena_set_allocated_code_location(
    ::tensorflow::CodeLocation* code_location) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.code_location_);
  }
  _impl_.code_location_ = code_location;
  if (code_location) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.Execution.code_location)
}
inline ::tensorflow::CodeLocation* Execution::release_code_location() {
  
  ::tensorflow::CodeLocation* temp = _impl_.code_location_;
  _impl_.code_location_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::CodeLocation* Execution::unsafe_arena_release_code_location() {
  // @@protoc_insertion_point(field_release:tensorflow.Execution.code_location)
  
  ::tensorflow::CodeLocation* temp = _impl_.code_location_;
  _impl_.code_location_ = nullptr;
  return temp;
}
inline ::tensorflow::CodeLocation* Execution::_internal_mutable_code_location() {
  
  if (_impl_.code_location_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::CodeLocation>(GetArenaForAllocation());
    _impl_.code_location_ = p;
  }
  return _impl_.code_location_;
}
inline ::tensorflow::CodeLocation* Execution::mutable_code_location() {
  ::tensorflow::CodeLocation* _msg = _internal_mutable_code_location();
  // @@protoc_insertion_point(field_mutable:tensorflow.Execution.code_location)
  return _msg;
}
inline void Execution::set_allocated_code_location(::tensorflow::CodeLocation* code_location) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.code_location_;
  }
  if (code_location) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(code_location);
    if (message_arena != submessage_arena) {
      code_location = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, code_location, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.code_location_ = code_location;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.Execution.code_location)
}

// repeated int32 output_tensor_device_ids = 9;
inline int Execution::_internal_output_tensor_device_ids_size() const {
  return _impl_.output_tensor_device_ids_.size();
}
inline int Execution::output_tensor_device_ids_size() const {
  return _internal_output_tensor_device_ids_size();
}
inline void Execution::clear_output_tensor_device_ids() {
  _impl_.output_tensor_device_ids_.Clear();
}
inline int32_t Execution::_internal_output_tensor_device_ids(int index) const {
  return _impl_.output_tensor_device_ids_.Get(index);
}
inline int32_t Execution::output_tensor_device_ids(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.Execution.output_tensor_device_ids)
  return _internal_output_tensor_device_ids(index);
}
inline void Execution::set_output_tensor_device_ids(int index, int32_t value) {
  _impl_.output_tensor_device_ids_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.Execution.output_tensor_device_ids)
}
inline void Execution::_internal_add_output_tensor_device_ids(int32_t value) {
  _impl_.output_tensor_device_ids_.Add(value);
}
inline void Execution::add_output_tensor_device_ids(int32_t value) {
  _internal_add_output_tensor_device_ids(value);
  // @@protoc_insertion_point(field_add:tensorflow.Execution.output_tensor_device_ids)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
Execution::_internal_output_tensor_device_ids() const {
  return _impl_.output_tensor_device_ids_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
Execution::output_tensor_device_ids() const {
  // @@protoc_insertion_point(field_list:tensorflow.Execution.output_tensor_device_ids)
  return _internal_output_tensor_device_ids();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
Execution::_internal_mutable_output_tensor_device_ids() {
  return &_impl_.output_tensor_device_ids_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
Execution::mutable_output_tensor_device_ids() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.Execution.output_tensor_device_ids)
  return _internal_mutable_output_tensor_device_ids();
}

// -------------------------------------------------------------------

// GraphExecutionTrace

// string tfdbg_context_id = 1;
inline void GraphExecutionTrace::clear_tfdbg_context_id() {
  _impl_.tfdbg_context_id_.ClearToEmpty();
}
inline const std::string& GraphExecutionTrace::tfdbg_context_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphExecutionTrace.tfdbg_context_id)
  return _internal_tfdbg_context_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GraphExecutionTrace::set_tfdbg_context_id(ArgT0&& arg0, ArgT... args) {
 
 _impl_.tfdbg_context_id_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.GraphExecutionTrace.tfdbg_context_id)
}
inline std::string* GraphExecutionTrace::mutable_tfdbg_context_id() {
  std::string* _s = _internal_mutable_tfdbg_context_id();
  // @@protoc_insertion_point(field_mutable:tensorflow.GraphExecutionTrace.tfdbg_context_id)
  return _s;
}
inline const std::string& GraphExecutionTrace::_internal_tfdbg_context_id() const {
  return _impl_.tfdbg_context_id_.Get();
}
inline void GraphExecutionTrace::_internal_set_tfdbg_context_id(const std::string& value) {
  
  _impl_.tfdbg_context_id_.Set(value, GetArenaForAllocation());
}
inline std::string* GraphExecutionTrace::_internal_mutable_tfdbg_context_id() {
  
  return _impl_.tfdbg_context_id_.Mutable(GetArenaForAllocation());
}
inline std::string* GraphExecutionTrace::release_tfdbg_context_id() {
  // @@protoc_insertion_point(field_release:tensorflow.GraphExecutionTrace.tfdbg_context_id)
  return _impl_.tfdbg_context_id_.Release();
}
inline void GraphExecutionTrace::set_allocated_tfdbg_context_id(std::string* tfdbg_context_id) {
  if (tfdbg_context_id != nullptr) {
    
  } else {
    
  }
  _impl_.tfdbg_context_id_.SetAllocated(tfdbg_context_id, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.tfdbg_context_id_.IsDefault()) {
    _impl_.tfdbg_context_id_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GraphExecutionTrace.tfdbg_context_id)
}

// string op_name = 2;
inline void GraphExecutionTrace::clear_op_name() {
  _impl_.op_name_.ClearToEmpty();
}
inline const std::string& GraphExecutionTrace::op_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphExecutionTrace.op_name)
  return _internal_op_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GraphExecutionTrace::set_op_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.op_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.GraphExecutionTrace.op_name)
}
inline std::string* GraphExecutionTrace::mutable_op_name() {
  std::string* _s = _internal_mutable_op_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.GraphExecutionTrace.op_name)
  return _s;
}
inline const std::string& GraphExecutionTrace::_internal_op_name() const {
  return _impl_.op_name_.Get();
}
inline void GraphExecutionTrace::_internal_set_op_name(const std::string& value) {
  
  _impl_.op_name_.Set(value, GetArenaForAllocation());
}
inline std::string* GraphExecutionTrace::_internal_mutable_op_name() {
  
  return _impl_.op_name_.Mutable(GetArenaForAllocation());
}
inline std::string* GraphExecutionTrace::release_op_name() {
  // @@protoc_insertion_point(field_release:tensorflow.GraphExecutionTrace.op_name)
  return _impl_.op_name_.Release();
}
inline void GraphExecutionTrace::set_allocated_op_name(std::string* op_name) {
  if (op_name != nullptr) {
    
  } else {
    
  }
  _impl_.op_name_.SetAllocated(op_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.op_name_.IsDefault()) {
    _impl_.op_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GraphExecutionTrace.op_name)
}

// int32 output_slot = 3;
inline void GraphExecutionTrace::clear_output_slot() {
  _impl_.output_slot_ = 0;
}
inline int32_t GraphExecutionTrace::_internal_output_slot() const {
  return _impl_.output_slot_;
}
inline int32_t GraphExecutionTrace::output_slot() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphExecutionTrace.output_slot)
  return _internal_output_slot();
}
inline void GraphExecutionTrace::_internal_set_output_slot(int32_t value) {
  
  _impl_.output_slot_ = value;
}
inline void GraphExecutionTrace::set_output_slot(int32_t value) {
  _internal_set_output_slot(value);
  // @@protoc_insertion_point(field_set:tensorflow.GraphExecutionTrace.output_slot)
}

// .tensorflow.TensorDebugMode tensor_debug_mode = 4;
inline void GraphExecutionTrace::clear_tensor_debug_mode() {
  _impl_.tensor_debug_mode_ = 0;
}
inline ::tensorflow::TensorDebugMode GraphExecutionTrace::_internal_tensor_debug_mode() const {
  return static_cast< ::tensorflow::TensorDebugMode >(_impl_.tensor_debug_mode_);
}
inline ::tensorflow::TensorDebugMode GraphExecutionTrace::tensor_debug_mode() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphExecutionTrace.tensor_debug_mode)
  return _internal_tensor_debug_mode();
}
inline void GraphExecutionTrace::_internal_set_tensor_debug_mode(::tensorflow::TensorDebugMode value) {
  
  _impl_.tensor_debug_mode_ = value;
}
inline void GraphExecutionTrace::set_tensor_debug_mode(::tensorflow::TensorDebugMode value) {
  _internal_set_tensor_debug_mode(value);
  // @@protoc_insertion_point(field_set:tensorflow.GraphExecutionTrace.tensor_debug_mode)
}

// .tensorflow.TensorProto tensor_proto = 5;
inline bool GraphExecutionTrace::_internal_has_tensor_proto() const {
  return this != internal_default_instance() && _impl_.tensor_proto_ != nullptr;
}
inline bool GraphExecutionTrace::has_tensor_proto() const {
  return _internal_has_tensor_proto();
}
inline const ::tensorflow::TensorProto& GraphExecutionTrace::_internal_tensor_proto() const {
  const ::tensorflow::TensorProto* p = _impl_.tensor_proto_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::TensorProto&>(
      ::tensorflow::_TensorProto_default_instance_);
}
inline const ::tensorflow::TensorProto& GraphExecutionTrace::tensor_proto() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphExecutionTrace.tensor_proto)
  return _internal_tensor_proto();
}
inline void GraphExecutionTrace::unsafe_arena_set_allocated_tensor_proto(
    ::tensorflow::TensorProto* tensor_proto) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.tensor_proto_);
  }
  _impl_.tensor_proto_ = tensor_proto;
  if (tensor_proto) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.GraphExecutionTrace.tensor_proto)
}
inline ::tensorflow::TensorProto* GraphExecutionTrace::release_tensor_proto() {
  
  ::tensorflow::TensorProto* temp = _impl_.tensor_proto_;
  _impl_.tensor_proto_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::TensorProto* GraphExecutionTrace::unsafe_arena_release_tensor_proto() {
  // @@protoc_insertion_point(field_release:tensorflow.GraphExecutionTrace.tensor_proto)
  
  ::tensorflow::TensorProto* temp = _impl_.tensor_proto_;
  _impl_.tensor_proto_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorProto* GraphExecutionTrace::_internal_mutable_tensor_proto() {
  
  if (_impl_.tensor_proto_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorProto>(GetArenaForAllocation());
    _impl_.tensor_proto_ = p;
  }
  return _impl_.tensor_proto_;
}
inline ::tensorflow::TensorProto* GraphExecutionTrace::mutable_tensor_proto() {
  ::tensorflow::TensorProto* _msg = _internal_mutable_tensor_proto();
  // @@protoc_insertion_point(field_mutable:tensorflow.GraphExecutionTrace.tensor_proto)
  return _msg;
}
inline void GraphExecutionTrace::set_allocated_tensor_proto(::tensorflow::TensorProto* tensor_proto) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.tensor_proto_);
  }
  if (tensor_proto) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(tensor_proto));
    if (message_arena != submessage_arena) {
      tensor_proto = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, tensor_proto, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.tensor_proto_ = tensor_proto;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GraphExecutionTrace.tensor_proto)
}

// string device_name = 6;
inline void GraphExecutionTrace::clear_device_name() {
  _impl_.device_name_.ClearToEmpty();
}
inline const std::string& GraphExecutionTrace::device_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphExecutionTrace.device_name)
  return _internal_device_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GraphExecutionTrace::set_device_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.device_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.GraphExecutionTrace.device_name)
}
inline std::string* GraphExecutionTrace::mutable_device_name() {
  std::string* _s = _internal_mutable_device_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.GraphExecutionTrace.device_name)
  return _s;
}
inline const std::string& GraphExecutionTrace::_internal_device_name() const {
  return _impl_.device_name_.Get();
}
inline void GraphExecutionTrace::_internal_set_device_name(const std::string& value) {
  
  _impl_.device_name_.Set(value, GetArenaForAllocation());
}
inline std::string* GraphExecutionTrace::_internal_mutable_device_name() {
  
  return _impl_.device_name_.Mutable(GetArenaForAllocation());
}
inline std::string* GraphExecutionTrace::release_device_name() {
  // @@protoc_insertion_point(field_release:tensorflow.GraphExecutionTrace.device_name)
  return _impl_.device_name_.Release();
}
inline void GraphExecutionTrace::set_allocated_device_name(std::string* device_name) {
  if (device_name != nullptr) {
    
  } else {
    
  }
  _impl_.device_name_.SetAllocated(device_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.device_name_.IsDefault()) {
    _impl_.device_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GraphExecutionTrace.device_name)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::tensorflow::TensorDebugMode> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::TensorDebugMode>() {
  return ::tensorflow::TensorDebugMode_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fdebug_5fevent_2eproto
