/* Autogenerated by mlir-tblgen; don't manually edit */

#ifdef GEN_PASS_DECL
// Generate declarations for all passes.
#define GEN_PASS_DECL_ADDDATAFLOWEDGESPASS
#define GEN_PASS_DECL_APPLYSHARDINGCONSTRAINTSPASS
#define GEN_PASS_DECL_CONSTANTSPLITTERPASS
#define GEN_PASS_DECL_LIFTINLINEDMESHESPASS
#define GEN_PASS_DECL_MANUALAXESCLEANUPPASS
#define GEN_PASS_DECL_SHARDINGGROUPIMPORTPASS
#undef GEN_PASS_DECL
#endif // GEN_PASS_DECL

//===----------------------------------------------------------------------===//
// AddDataFlowEdgesPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_ADDDATAFLOWEDGESPASS
std::unique_ptr<::mlir::Pass> createAddDataFlowEdgesPass();
#undef GEN_PASS_DECL_ADDDATAFLOWEDGESPASS
#endif // GEN_PASS_DECL_ADDDATAFLOWEDGESPASS
#ifdef GEN_PASS_DEF_ADDDATAFLOWEDGESPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createAddDataFlowEdgesPass();
} // namespace impl
namespace impl {

template <typename DerivedT>
class AddDataFlowEdgesPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = AddDataFlowEdgesPassBase;

  AddDataFlowEdgesPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  AddDataFlowEdgesPassBase(const AddDataFlowEdgesPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  AddDataFlowEdgesPassBase& operator=(const AddDataFlowEdgesPassBase &) = delete;
  AddDataFlowEdgesPassBase(AddDataFlowEdgesPassBase &&) = delete;
  AddDataFlowEdgesPassBase& operator=(AddDataFlowEdgesPassBase &&) = delete;
  ~AddDataFlowEdgesPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("sdy-add-data-flow-edges");
  }
  ::llvm::StringRef getArgument() const override { return "sdy-add-data-flow-edges"; }

  ::llvm::StringRef getDescription() const override { return "Inserts `DataFlowEdgeOp` for every data-flow edge."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AddDataFlowEdgesPass");
  }
  ::llvm::StringRef getName() const override { return "AddDataFlowEdgesPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::sdy::SdyDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(AddDataFlowEdgesPassBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createAddDataFlowEdgesPass() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createAddDataFlowEdgesPass() {
  return impl::createAddDataFlowEdgesPass();
}
#undef GEN_PASS_DEF_ADDDATAFLOWEDGESPASS
#endif // GEN_PASS_DEF_ADDDATAFLOWEDGESPASS

//===----------------------------------------------------------------------===//
// ApplyShardingConstraintsPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_APPLYSHARDINGCONSTRAINTSPASS
std::unique_ptr<::mlir::Pass> createApplyShardingConstraintsPass();
#undef GEN_PASS_DECL_APPLYSHARDINGCONSTRAINTSPASS
#endif // GEN_PASS_DECL_APPLYSHARDINGCONSTRAINTSPASS
#ifdef GEN_PASS_DEF_APPLYSHARDINGCONSTRAINTSPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createApplyShardingConstraintsPass();
} // namespace impl
namespace impl {

template <typename DerivedT>
class ApplyShardingConstraintsPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = ApplyShardingConstraintsPassBase;

  ApplyShardingConstraintsPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  ApplyShardingConstraintsPassBase(const ApplyShardingConstraintsPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  ApplyShardingConstraintsPassBase& operator=(const ApplyShardingConstraintsPassBase &) = delete;
  ApplyShardingConstraintsPassBase(ApplyShardingConstraintsPassBase &&) = delete;
  ApplyShardingConstraintsPassBase& operator=(ApplyShardingConstraintsPassBase &&) = delete;
  ~ApplyShardingConstraintsPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("sdy-apply-sharding-constraints");
  }
  ::llvm::StringRef getArgument() const override { return "sdy-apply-sharding-constraints"; }

  ::llvm::StringRef getDescription() const override { return "Applies constraints that dictate the sharding of their input."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ApplyShardingConstraintsPass");
  }
  ::llvm::StringRef getName() const override { return "ApplyShardingConstraintsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::sdy::SdyDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ApplyShardingConstraintsPassBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createApplyShardingConstraintsPass() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createApplyShardingConstraintsPass() {
  return impl::createApplyShardingConstraintsPass();
}
#undef GEN_PASS_DEF_APPLYSHARDINGCONSTRAINTSPASS
#endif // GEN_PASS_DEF_APPLYSHARDINGCONSTRAINTSPASS

//===----------------------------------------------------------------------===//
// ConstantSplitterPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CONSTANTSPLITTERPASS
std::unique_ptr<::mlir::Pass> createConstantSplitterPass();
#undef GEN_PASS_DECL_CONSTANTSPLITTERPASS
#endif // GEN_PASS_DECL_CONSTANTSPLITTERPASS
#ifdef GEN_PASS_DEF_CONSTANTSPLITTERPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createConstantSplitterPass();
} // namespace impl
namespace impl {

template <typename DerivedT>
class ConstantSplitterPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = ConstantSplitterPassBase;

  ConstantSplitterPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConstantSplitterPassBase(const ConstantSplitterPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  ConstantSplitterPassBase& operator=(const ConstantSplitterPassBase &) = delete;
  ConstantSplitterPassBase(ConstantSplitterPassBase &&) = delete;
  ConstantSplitterPassBase& operator=(ConstantSplitterPassBase &&) = delete;
  ~ConstantSplitterPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("sdy-constant-splitter");
  }
  ::llvm::StringRef getArgument() const override { return "sdy-constant-splitter"; }

  ::llvm::StringRef getDescription() const override { return "Splits constant sub-computations so each has a single use."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConstantSplitterPass");
  }
  ::llvm::StringRef getName() const override { return "ConstantSplitterPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::sdy::SdyDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConstantSplitterPassBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createConstantSplitterPass() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createConstantSplitterPass() {
  return impl::createConstantSplitterPass();
}
#undef GEN_PASS_DEF_CONSTANTSPLITTERPASS
#endif // GEN_PASS_DEF_CONSTANTSPLITTERPASS

//===----------------------------------------------------------------------===//
// LiftInlinedMeshesPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_LIFTINLINEDMESHESPASS
std::unique_ptr<::mlir::Pass> createLiftInlinedMeshesPass();
#undef GEN_PASS_DECL_LIFTINLINEDMESHESPASS
#endif // GEN_PASS_DECL_LIFTINLINEDMESHESPASS
#ifdef GEN_PASS_DEF_LIFTINLINEDMESHESPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createLiftInlinedMeshesPass();
} // namespace impl
namespace impl {

template <typename DerivedT>
class LiftInlinedMeshesPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = LiftInlinedMeshesPassBase;

  LiftInlinedMeshesPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  LiftInlinedMeshesPassBase(const LiftInlinedMeshesPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  LiftInlinedMeshesPassBase& operator=(const LiftInlinedMeshesPassBase &) = delete;
  LiftInlinedMeshesPassBase(LiftInlinedMeshesPassBase &&) = delete;
  LiftInlinedMeshesPassBase& operator=(LiftInlinedMeshesPassBase &&) = delete;
  ~LiftInlinedMeshesPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("sdy-lift-inlined-meshes");
  }
  ::llvm::StringRef getArgument() const override { return "sdy-lift-inlined-meshes"; }

  ::llvm::StringRef getDescription() const override { return "Lifts inlined `MeshAttr`s in shardings as symbol `MeshOp`s."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LiftInlinedMeshesPass");
  }
  ::llvm::StringRef getName() const override { return "LiftInlinedMeshesPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::sdy::SdyDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LiftInlinedMeshesPassBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createLiftInlinedMeshesPass() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createLiftInlinedMeshesPass() {
  return impl::createLiftInlinedMeshesPass();
}
#undef GEN_PASS_DEF_LIFTINLINEDMESHESPASS
#endif // GEN_PASS_DEF_LIFTINLINEDMESHESPASS

//===----------------------------------------------------------------------===//
// ManualAxesCleanupPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_MANUALAXESCLEANUPPASS
std::unique_ptr<::mlir::Pass> createManualAxesCleanupPass();
#undef GEN_PASS_DECL_MANUALAXESCLEANUPPASS
#endif // GEN_PASS_DECL_MANUALAXESCLEANUPPASS
#ifdef GEN_PASS_DEF_MANUALAXESCLEANUPPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createManualAxesCleanupPass();
} // namespace impl
namespace impl {

template <typename DerivedT>
class ManualAxesCleanupPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ManualAxesCleanupPassBase;

  ManualAxesCleanupPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ManualAxesCleanupPassBase(const ManualAxesCleanupPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  ManualAxesCleanupPassBase& operator=(const ManualAxesCleanupPassBase &) = delete;
  ManualAxesCleanupPassBase(ManualAxesCleanupPassBase &&) = delete;
  ManualAxesCleanupPassBase& operator=(ManualAxesCleanupPassBase &&) = delete;
  ~ManualAxesCleanupPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("sdy-manual-axes-cleanup");
  }
  ::llvm::StringRef getArgument() const override { return "sdy-manual-axes-cleanup"; }

  ::llvm::StringRef getDescription() const override { return "Cleans up the use of manual axes in `ManualComputationOp`s"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ManualAxesCleanupPass");
  }
  ::llvm::StringRef getName() const override { return "ManualAxesCleanupPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::sdy::SdyDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ManualAxesCleanupPassBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createManualAxesCleanupPass() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createManualAxesCleanupPass() {
  return impl::createManualAxesCleanupPass();
}
#undef GEN_PASS_DEF_MANUALAXESCLEANUPPASS
#endif // GEN_PASS_DEF_MANUALAXESCLEANUPPASS

//===----------------------------------------------------------------------===//
// ShardingGroupImportPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_SHARDINGGROUPIMPORTPASS
std::unique_ptr<::mlir::Pass> createShardingGroupImportPass();
#undef GEN_PASS_DECL_SHARDINGGROUPIMPORTPASS
#endif // GEN_PASS_DECL_SHARDINGGROUPIMPORTPASS
#ifdef GEN_PASS_DEF_SHARDINGGROUPIMPORTPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createShardingGroupImportPass();
} // namespace impl
namespace impl {

template <typename DerivedT>
class ShardingGroupImportPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ShardingGroupImportPassBase;

  ShardingGroupImportPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ShardingGroupImportPassBase(const ShardingGroupImportPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  ShardingGroupImportPassBase& operator=(const ShardingGroupImportPassBase &) = delete;
  ShardingGroupImportPassBase(ShardingGroupImportPassBase &&) = delete;
  ShardingGroupImportPassBase& operator=(ShardingGroupImportPassBase &&) = delete;
  ~ShardingGroupImportPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("sdy-sharding-group-import");
  }
  ::llvm::StringRef getArgument() const override { return "sdy-sharding-group-import"; }

  ::llvm::StringRef getDescription() const override { return "Canonicalization and validation pass for sharding groups."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ShardingGroupImportPass");
  }
  ::llvm::StringRef getName() const override { return "ShardingGroupImportPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::sdy::SdyDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ShardingGroupImportPassBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createShardingGroupImportPass() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createShardingGroupImportPass() {
  return impl::createShardingGroupImportPass();
}
#undef GEN_PASS_DEF_SHARDINGGROUPIMPORTPASS
#endif // GEN_PASS_DEF_SHARDINGGROUPIMPORTPASS
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// AddDataFlowEdgesPass Registration
//===----------------------------------------------------------------------===//

inline void registerAddDataFlowEdgesPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createAddDataFlowEdgesPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerAddDataFlowEdgesPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createAddDataFlowEdgesPass();
  });
}

//===----------------------------------------------------------------------===//
// ApplyShardingConstraintsPass Registration
//===----------------------------------------------------------------------===//

inline void registerApplyShardingConstraintsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createApplyShardingConstraintsPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerApplyShardingConstraintsPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createApplyShardingConstraintsPass();
  });
}

//===----------------------------------------------------------------------===//
// ConstantSplitterPass Registration
//===----------------------------------------------------------------------===//

inline void registerConstantSplitterPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConstantSplitterPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerConstantSplitterPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createConstantSplitterPass();
  });
}

//===----------------------------------------------------------------------===//
// LiftInlinedMeshesPass Registration
//===----------------------------------------------------------------------===//

inline void registerLiftInlinedMeshesPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createLiftInlinedMeshesPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerLiftInlinedMeshesPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createLiftInlinedMeshesPass();
  });
}

//===----------------------------------------------------------------------===//
// ManualAxesCleanupPass Registration
//===----------------------------------------------------------------------===//

inline void registerManualAxesCleanupPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createManualAxesCleanupPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerManualAxesCleanupPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createManualAxesCleanupPass();
  });
}

//===----------------------------------------------------------------------===//
// ShardingGroupImportPass Registration
//===----------------------------------------------------------------------===//

inline void registerShardingGroupImportPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createShardingGroupImportPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerShardingGroupImportPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createShardingGroupImportPass();
  });
}

//===----------------------------------------------------------------------===//
// SdyImport Registration
//===----------------------------------------------------------------------===//

inline void registerSdyImportPasses() {
  registerAddDataFlowEdgesPass();
  registerApplyShardingConstraintsPass();
  registerConstantSplitterPass();
  registerLiftInlinedMeshesPass();
  registerManualAxesCleanupPass();
  registerShardingGroupImportPass();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
// Deprecated. Please use the new per-pass macros.
#ifdef GEN_PASS_CLASSES

template <typename DerivedT>
class AddDataFlowEdgesPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = AddDataFlowEdgesPassBase;

  AddDataFlowEdgesPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  AddDataFlowEdgesPassBase(const AddDataFlowEdgesPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  AddDataFlowEdgesPassBase& operator=(const AddDataFlowEdgesPassBase &) = delete;
  AddDataFlowEdgesPassBase(AddDataFlowEdgesPassBase &&) = delete;
  AddDataFlowEdgesPassBase& operator=(AddDataFlowEdgesPassBase &&) = delete;
  ~AddDataFlowEdgesPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("sdy-add-data-flow-edges");
  }
  ::llvm::StringRef getArgument() const override { return "sdy-add-data-flow-edges"; }

  ::llvm::StringRef getDescription() const override { return "Inserts `DataFlowEdgeOp` for every data-flow edge."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AddDataFlowEdgesPass");
  }
  ::llvm::StringRef getName() const override { return "AddDataFlowEdgesPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::sdy::SdyDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(AddDataFlowEdgesPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ApplyShardingConstraintsPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = ApplyShardingConstraintsPassBase;

  ApplyShardingConstraintsPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  ApplyShardingConstraintsPassBase(const ApplyShardingConstraintsPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  ApplyShardingConstraintsPassBase& operator=(const ApplyShardingConstraintsPassBase &) = delete;
  ApplyShardingConstraintsPassBase(ApplyShardingConstraintsPassBase &&) = delete;
  ApplyShardingConstraintsPassBase& operator=(ApplyShardingConstraintsPassBase &&) = delete;
  ~ApplyShardingConstraintsPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("sdy-apply-sharding-constraints");
  }
  ::llvm::StringRef getArgument() const override { return "sdy-apply-sharding-constraints"; }

  ::llvm::StringRef getDescription() const override { return "Applies constraints that dictate the sharding of their input."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ApplyShardingConstraintsPass");
  }
  ::llvm::StringRef getName() const override { return "ApplyShardingConstraintsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::sdy::SdyDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ApplyShardingConstraintsPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ConstantSplitterPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = ConstantSplitterPassBase;

  ConstantSplitterPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  ConstantSplitterPassBase(const ConstantSplitterPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  ConstantSplitterPassBase& operator=(const ConstantSplitterPassBase &) = delete;
  ConstantSplitterPassBase(ConstantSplitterPassBase &&) = delete;
  ConstantSplitterPassBase& operator=(ConstantSplitterPassBase &&) = delete;
  ~ConstantSplitterPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("sdy-constant-splitter");
  }
  ::llvm::StringRef getArgument() const override { return "sdy-constant-splitter"; }

  ::llvm::StringRef getDescription() const override { return "Splits constant sub-computations so each has a single use."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ConstantSplitterPass");
  }
  ::llvm::StringRef getName() const override { return "ConstantSplitterPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::sdy::SdyDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ConstantSplitterPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class LiftInlinedMeshesPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = LiftInlinedMeshesPassBase;

  LiftInlinedMeshesPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  LiftInlinedMeshesPassBase(const LiftInlinedMeshesPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  LiftInlinedMeshesPassBase& operator=(const LiftInlinedMeshesPassBase &) = delete;
  LiftInlinedMeshesPassBase(LiftInlinedMeshesPassBase &&) = delete;
  LiftInlinedMeshesPassBase& operator=(LiftInlinedMeshesPassBase &&) = delete;
  ~LiftInlinedMeshesPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("sdy-lift-inlined-meshes");
  }
  ::llvm::StringRef getArgument() const override { return "sdy-lift-inlined-meshes"; }

  ::llvm::StringRef getDescription() const override { return "Lifts inlined `MeshAttr`s in shardings as symbol `MeshOp`s."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LiftInlinedMeshesPass");
  }
  ::llvm::StringRef getName() const override { return "LiftInlinedMeshesPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::sdy::SdyDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LiftInlinedMeshesPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ManualAxesCleanupPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ManualAxesCleanupPassBase;

  ManualAxesCleanupPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ManualAxesCleanupPassBase(const ManualAxesCleanupPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  ManualAxesCleanupPassBase& operator=(const ManualAxesCleanupPassBase &) = delete;
  ManualAxesCleanupPassBase(ManualAxesCleanupPassBase &&) = delete;
  ManualAxesCleanupPassBase& operator=(ManualAxesCleanupPassBase &&) = delete;
  ~ManualAxesCleanupPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("sdy-manual-axes-cleanup");
  }
  ::llvm::StringRef getArgument() const override { return "sdy-manual-axes-cleanup"; }

  ::llvm::StringRef getDescription() const override { return "Cleans up the use of manual axes in `ManualComputationOp`s"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ManualAxesCleanupPass");
  }
  ::llvm::StringRef getName() const override { return "ManualAxesCleanupPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::sdy::SdyDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ManualAxesCleanupPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ShardingGroupImportPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ShardingGroupImportPassBase;

  ShardingGroupImportPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ShardingGroupImportPassBase(const ShardingGroupImportPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  ShardingGroupImportPassBase& operator=(const ShardingGroupImportPassBase &) = delete;
  ShardingGroupImportPassBase(ShardingGroupImportPassBase &&) = delete;
  ShardingGroupImportPassBase& operator=(ShardingGroupImportPassBase &&) = delete;
  ~ShardingGroupImportPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("sdy-sharding-group-import");
  }
  ::llvm::StringRef getArgument() const override { return "sdy-sharding-group-import"; }

  ::llvm::StringRef getDescription() const override { return "Canonicalization and validation pass for sharding groups."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ShardingGroupImportPass");
  }
  ::llvm::StringRef getName() const override { return "ShardingGroupImportPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::sdy::SdyDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ShardingGroupImportPassBase<DerivedT>)

protected:
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
