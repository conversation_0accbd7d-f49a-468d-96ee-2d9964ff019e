/*
 * Copyright (C) 2023 Mo<PERSON>ad <PERSON>af
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

#ifdef __WIDL__
#pragma winrt ns_prefix
#endif

import "inspectable.idl";
import "asyncinfo.idl";
import "eventtoken.idl";
import "windowscontracts.idl";
import "windows.foundation.idl";
import "windows.foundation.numerics.idl";
import "windows.graphics.directx.idl";
import "windows.graphics.directx.direct3d11.idl";
/* import "windows.perception.idl"; */
import "windows.perception.spatial.idl";
import "windows.ui.core.idl";

namespace Windows.Graphics.Holographic {
    typedef struct HolographicAdapterId HolographicAdapterId;

    interface IHolographicCamera;
    interface IHolographicFrame;
    interface IHolographicSpaceStatics;
    interface IHolographicSpaceStatics2;
    interface IHolographicSpaceStatics3;
    interface IHolographicSpaceCameraAddedEventArgs;
    interface IHolographicSpaceCameraRemovedEventArgs;

    runtimeclass HolographicCamera;
    runtimeclass HolographicFrame;
    runtimeclass HolographicSpace;
    runtimeclass HolographicSpaceCameraAddedEventArgs;
    runtimeclass HolographicSpaceCameraRemovedEventArgs;

    declare {
        interface Windows.Foundation.TypedEventHandler<Windows.Graphics.Holographic.HolographicSpace *, IInspectable *>;
        interface Windows.Foundation.TypedEventHandler<Windows.Graphics.Holographic.HolographicSpace *, Windows.Graphics.Holographic.HolographicSpaceCameraAddedEventArgs *>;
        interface Windows.Foundation.TypedEventHandler<Windows.Graphics.Holographic.HolographicSpace *, Windows.Graphics.Holographic.HolographicSpaceCameraRemovedEventArgs *>;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0)
    ]
    struct HolographicAdapterId
    {
        UINT32 LowPart;
        INT32 HighPart;
    };

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        exclusiveto(Windows.Graphics.Holographic.HolographicSpace),
        uuid(4380dba6-5e78-434f-807c-3433d1efe8b7)
    ]
    interface IHolographicSpace : IInspectable
    {
        [propget] HRESULT PrimaryAdapterId([out, retval] Windows.Graphics.Holographic.HolographicAdapterId *value);
        HRESULT SetDirect3D11Device([in] Windows.Graphics.DirectX.Direct3D11.IDirect3DDevice *value);
        [eventadd] HRESULT CameraAdded(
            [in] Windows.Foundation.TypedEventHandler<Windows.Graphics.Holographic.HolographicSpace *, Windows.Graphics.Holographic.HolographicSpaceCameraAddedEventArgs *> *handler,
            [out, retval] EventRegistrationToken *cookie
        );
        [eventremove] HRESULT CameraAdded([in] EventRegistrationToken cookie);
        [eventadd] HRESULT CameraRemoved(
            [in] Windows.Foundation.TypedEventHandler<Windows.Graphics.Holographic.HolographicSpace *, Windows.Graphics.Holographic.HolographicSpaceCameraRemovedEventArgs *> *handler,
            [out, retval] EventRegistrationToken *cookie
        );
        [eventremove] HRESULT CameraRemoved([in] EventRegistrationToken cookie);
        HRESULT CreateNextFrame([out, retval] Windows.Graphics.Holographic.HolographicFrame **value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 4.0),
        exclusiveto(Windows.Graphics.Holographic.HolographicSpace),
        uuid(0e777088-75fc-48af-8758-0652f6f07c59)
    ]
    interface IHolographicSpaceStatics2 : IInspectable
    {
        [propget] HRESULT IsSupported([out, retval] boolean *value);
        [propget] HRESULT IsAvailable([out, retval] boolean *value);
        [eventadd] HRESULT IsAvailableChanged([in] Windows.Foundation.EventHandler<IInspectable *> *handler, [out, retval] EventRegistrationToken *token);
        [eventremove] HRESULT IsAvailableChanged([in] EventRegistrationToken token);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 5.0),
        exclusiveto(Windows.Graphics.Holographic.HolographicSpace),
        uuid(3b00de3d-b1a3-4dfe-8e79-fec5909e6df8)
    ]
    interface IHolographicSpaceStatics3 : IInspectable
    {
        [propget] HRESULT IsConfigured([out, retval] boolean *value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        exclusiveto(Windows.Graphics.Holographic.HolographicSpaceCameraAddedEventArgs),
        uuid(58f1da35-bbb3-3c8f-993d-6c80e7feb99f)
    ]
    interface IHolographicSpaceCameraAddedEventArgs : IInspectable
    {
        [propget] HRESULT Camera([out, retval] Windows.Graphics.Holographic.HolographicCamera **value);
        HRESULT GetDeferral([out, retval] Windows.Foundation.Deferral **value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        exclusiveto(Windows.Graphics.Holographic.HolographicSpaceCameraRemovedEventArgs),
        uuid(805444a8-f2ae-322e-8da9-836a0a95a4c1)
    ]
    interface IHolographicSpaceCameraRemovedEventArgs : IInspectable
    {
        [propget] HRESULT Camera([out, retval] Windows.Graphics.Holographic.HolographicCamera **value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        marshaling_behavior(agile),
        threading(both)
    ]
    runtimeclass HolographicCamera
    {
        [default] interface Windows.Graphics.Holographic.IHolographicCamera;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        marshaling_behavior(agile),
        threading(both)
    ]
    runtimeclass HolographicFrame
    {
        [default] interface Windows.Graphics.Holographic.IHolographicFrame;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        marshaling_behavior(agile),
        static(Windows.Graphics.Holographic.IHolographicSpaceStatics, Windows.Foundation.UniversalApiContract, 2.0),
        static(Windows.Graphics.Holographic.IHolographicSpaceStatics2, Windows.Foundation.UniversalApiContract, 4.0),
        static(Windows.Graphics.Holographic.IHolographicSpaceStatics3, Windows.Foundation.UniversalApiContract, 5.0),
        threading(both)
    ]
    runtimeclass HolographicSpace
    {
        [default] interface Windows.Graphics.Holographic.IHolographicSpace;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        marshaling_behavior(agile),
        threading(both)
    ]
    runtimeclass HolographicSpaceCameraAddedEventArgs
    {
        [default] interface Windows.Graphics.Holographic.IHolographicSpaceCameraAddedEventArgs;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        marshaling_behavior(agile),
        threading(both)
    ]
    runtimeclass HolographicSpaceCameraRemovedEventArgs
    {
        [default] interface Windows.Graphics.Holographic.IHolographicSpaceCameraRemovedEventArgs;
    }
}
