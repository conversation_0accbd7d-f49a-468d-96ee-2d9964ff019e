/*** Autogenerated by WIDL 10.8 from include/windows.devices.input.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __windows_devices_input_h__
#define __windows_devices_input_h__

/* Forward declarations */

#ifndef ____x_ABI_CWindows_CDevices_CInput_CIPointerDevice_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CInput_CIPointerDevice_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CInput_CIPointerDevice __x_ABI_CWindows_CDevices_CInput_CIPointerDevice;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CInput_CIPointerDevice ABI::Windows::Devices::Input::IPointerDevice
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Input {
                interface IPointerDevice;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CInput_CIPointerDevice2_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CInput_CIPointerDevice2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CInput_CIPointerDevice2 __x_ABI_CWindows_CDevices_CInput_CIPointerDevice2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CInput_CIPointerDevice2 ABI::Windows::Devices::Input::IPointerDevice2
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Input {
                interface IPointerDevice2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CInput_CIPointerDeviceStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CInput_CIPointerDeviceStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CInput_CIPointerDeviceStatics __x_ABI_CWindows_CDevices_CInput_CIPointerDeviceStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CInput_CIPointerDeviceStatics ABI::Windows::Devices::Input::IPointerDeviceStatics
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Input {
                interface IPointerDeviceStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CInput_CPointerDevice_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CInput_CPointerDevice_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Input {
                class PointerDevice;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CDevices_CInput_CPointerDevice __x_ABI_CWindows_CDevices_CInput_CPointerDevice;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CDevices_CInput_CPointerDevice_FWD_DEFINED__ */

#ifndef ____FIVectorView_1_Windows__CDevices__CInput__CPointerDevice_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CDevices__CInput__CPointerDevice_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CDevices__CInput__CPointerDevice __FIVectorView_1_Windows__CDevices__CInput__CPointerDevice;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CDevices__CInput__CPointerDevice ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Devices::Input::PointerDevice* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_PointerDeviceUsage_FWD_DEFINED__
#define ____FIVectorView_1_PointerDeviceUsage_FWD_DEFINED__
typedef interface __FIVectorView_1_PointerDeviceUsage __FIVectorView_1_PointerDeviceUsage;
#ifdef __cplusplus
#define __FIVectorView_1_PointerDeviceUsage ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Devices::Input::PointerDeviceUsage >
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <inspectable.h>
#include <asyncinfo.h>
#include <eventtoken.h>
#include <windowscontracts.h>
#include <windows.foundation.h>

#ifdef __cplusplus
extern "C" {
#endif

#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CDevices_CInput_CPointerDeviceType __x_ABI_CWindows_CDevices_CInput_CPointerDeviceType;
#endif /* __cplusplus */

#ifndef __cplusplus
typedef struct __x_ABI_CWindows_CDevices_CInput_CPointerDeviceUsage __x_ABI_CWindows_CDevices_CInput_CPointerDeviceUsage;
#else /* __cplusplus */
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Input {
                typedef struct PointerDeviceUsage PointerDeviceUsage;
            }
        }
    }
}
#endif /* __cplusplus */

#ifndef ____x_ABI_CWindows_CDevices_CInput_CIPointerDevice_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CInput_CIPointerDevice_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CInput_CIPointerDevice __x_ABI_CWindows_CDevices_CInput_CIPointerDevice;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CInput_CIPointerDevice ABI::Windows::Devices::Input::IPointerDevice
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Input {
                interface IPointerDevice;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CInput_CIPointerDevice2_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CInput_CIPointerDevice2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CInput_CIPointerDevice2 __x_ABI_CWindows_CDevices_CInput_CIPointerDevice2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CInput_CIPointerDevice2 ABI::Windows::Devices::Input::IPointerDevice2
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Input {
                interface IPointerDevice2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CInput_CIPointerDeviceStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CInput_CIPointerDeviceStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CInput_CIPointerDeviceStatics __x_ABI_CWindows_CDevices_CInput_CIPointerDeviceStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CInput_CIPointerDeviceStatics ABI::Windows::Devices::Input::IPointerDeviceStatics
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Input {
                interface IPointerDeviceStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CDevices__CInput__CPointerDevice_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CDevices__CInput__CPointerDevice_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CDevices__CInput__CPointerDevice __FIVectorView_1_Windows__CDevices__CInput__CPointerDevice;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CDevices__CInput__CPointerDevice ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Devices::Input::PointerDevice* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_PointerDeviceUsage_FWD_DEFINED__
#define ____FIVectorView_1_PointerDeviceUsage_FWD_DEFINED__
typedef interface __FIVectorView_1_PointerDeviceUsage __FIVectorView_1_PointerDeviceUsage;
#ifdef __cplusplus
#define __FIVectorView_1_PointerDeviceUsage ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Devices::Input::PointerDeviceUsage >
#endif /* __cplusplus */
#endif

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Input {
                enum PointerDeviceType {
                    PointerDeviceType_Touch = 0,
                    PointerDeviceType_Pen = 1,
                    PointerDeviceType_Mouse = 2
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CDevices_CInput_CPointerDeviceType {
    PointerDeviceType_Touch = 0,
    PointerDeviceType_Pen = 1,
    PointerDeviceType_Mouse = 2
};
#ifdef WIDL_using_Windows_Devices_Input
#define PointerDeviceType __x_ABI_CWindows_CDevices_CInput_CPointerDeviceType
#endif /* WIDL_using_Windows_Devices_Input */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Input {
                struct PointerDeviceUsage {
                    UINT32 UsagePage;
                    UINT32 Usage;
                    INT32 MinLogical;
                    INT32 MaxLogical;
                    INT32 MinPhysical;
                    INT32 MaxPhysical;
                    UINT32 Unit;
                    FLOAT PhysicalMultiplier;
                };
            }
        }
    }
}
extern "C" {
#else
struct __x_ABI_CWindows_CDevices_CInput_CPointerDeviceUsage {
    UINT32 UsagePage;
    UINT32 Usage;
    INT32 MinLogical;
    INT32 MaxLogical;
    INT32 MinPhysical;
    INT32 MaxPhysical;
    UINT32 Unit;
    FLOAT PhysicalMultiplier;
};
#ifdef WIDL_using_Windows_Devices_Input
#define PointerDeviceUsage __x_ABI_CWindows_CDevices_CInput_CPointerDeviceUsage
#endif /* WIDL_using_Windows_Devices_Input */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
/*****************************************************************************
 * IPointerDevice interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CDevices_CInput_CIPointerDevice_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CDevices_CInput_CIPointerDevice_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CDevices_CInput_CIPointerDevice, 0x93c9bafc, 0xebcb, 0x467e, 0x82,0xc6, 0x27,0x6f,0xea,0xe3,0x6b,0x5a);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Input {
                MIDL_INTERFACE("93c9bafc-ebcb-467e-82c6-276feae36b5a")
                IPointerDevice : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_PointerDeviceType(
                        ABI::Windows::Devices::Input::PointerDeviceType *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_IsIntegrated(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_MaxContacts(
                        UINT32 *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_PhysicalDeviceRect(
                        ABI::Windows::Foundation::Rect *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_ScreenRect(
                        ABI::Windows::Foundation::Rect *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_SupportedUsages(
                        ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Devices::Input::PointerDeviceUsage > **value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CDevices_CInput_CIPointerDevice, 0x93c9bafc, 0xebcb, 0x467e, 0x82,0xc6, 0x27,0x6f,0xea,0xe3,0x6b,0x5a)
#endif
#else
typedef struct __x_ABI_CWindows_CDevices_CInput_CIPointerDeviceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CDevices_CInput_CIPointerDevice *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CDevices_CInput_CIPointerDevice *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CDevices_CInput_CIPointerDevice *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CDevices_CInput_CIPointerDevice *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CDevices_CInput_CIPointerDevice *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CDevices_CInput_CIPointerDevice *This,
        TrustLevel *trustLevel);

    /*** IPointerDevice methods ***/
    HRESULT (STDMETHODCALLTYPE *get_PointerDeviceType)(
        __x_ABI_CWindows_CDevices_CInput_CIPointerDevice *This,
        __x_ABI_CWindows_CDevices_CInput_CPointerDeviceType *value);

    HRESULT (STDMETHODCALLTYPE *get_IsIntegrated)(
        __x_ABI_CWindows_CDevices_CInput_CIPointerDevice *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *get_MaxContacts)(
        __x_ABI_CWindows_CDevices_CInput_CIPointerDevice *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *get_PhysicalDeviceRect)(
        __x_ABI_CWindows_CDevices_CInput_CIPointerDevice *This,
        __x_ABI_CWindows_CFoundation_CRect *value);

    HRESULT (STDMETHODCALLTYPE *get_ScreenRect)(
        __x_ABI_CWindows_CDevices_CInput_CIPointerDevice *This,
        __x_ABI_CWindows_CFoundation_CRect *value);

    HRESULT (STDMETHODCALLTYPE *get_SupportedUsages)(
        __x_ABI_CWindows_CDevices_CInput_CIPointerDevice *This,
        __FIVectorView_1_PointerDeviceUsage **value);

    END_INTERFACE
} __x_ABI_CWindows_CDevices_CInput_CIPointerDeviceVtbl;

interface __x_ABI_CWindows_CDevices_CInput_CIPointerDevice {
    CONST_VTBL __x_ABI_CWindows_CDevices_CInput_CIPointerDeviceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CDevices_CInput_CIPointerDevice_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CDevices_CInput_CIPointerDevice_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CDevices_CInput_CIPointerDevice_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CDevices_CInput_CIPointerDevice_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CDevices_CInput_CIPointerDevice_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CDevices_CInput_CIPointerDevice_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IPointerDevice methods ***/
#define __x_ABI_CWindows_CDevices_CInput_CIPointerDevice_get_PointerDeviceType(This,value) (This)->lpVtbl->get_PointerDeviceType(This,value)
#define __x_ABI_CWindows_CDevices_CInput_CIPointerDevice_get_IsIntegrated(This,value) (This)->lpVtbl->get_IsIntegrated(This,value)
#define __x_ABI_CWindows_CDevices_CInput_CIPointerDevice_get_MaxContacts(This,value) (This)->lpVtbl->get_MaxContacts(This,value)
#define __x_ABI_CWindows_CDevices_CInput_CIPointerDevice_get_PhysicalDeviceRect(This,value) (This)->lpVtbl->get_PhysicalDeviceRect(This,value)
#define __x_ABI_CWindows_CDevices_CInput_CIPointerDevice_get_ScreenRect(This,value) (This)->lpVtbl->get_ScreenRect(This,value)
#define __x_ABI_CWindows_CDevices_CInput_CIPointerDevice_get_SupportedUsages(This,value) (This)->lpVtbl->get_SupportedUsages(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CInput_CIPointerDevice_QueryInterface(__x_ABI_CWindows_CDevices_CInput_CIPointerDevice* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CDevices_CInput_CIPointerDevice_AddRef(__x_ABI_CWindows_CDevices_CInput_CIPointerDevice* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CDevices_CInput_CIPointerDevice_Release(__x_ABI_CWindows_CDevices_CInput_CIPointerDevice* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CInput_CIPointerDevice_GetIids(__x_ABI_CWindows_CDevices_CInput_CIPointerDevice* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CInput_CIPointerDevice_GetRuntimeClassName(__x_ABI_CWindows_CDevices_CInput_CIPointerDevice* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CInput_CIPointerDevice_GetTrustLevel(__x_ABI_CWindows_CDevices_CInput_CIPointerDevice* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IPointerDevice methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CInput_CIPointerDevice_get_PointerDeviceType(__x_ABI_CWindows_CDevices_CInput_CIPointerDevice* This,__x_ABI_CWindows_CDevices_CInput_CPointerDeviceType *value) {
    return This->lpVtbl->get_PointerDeviceType(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CInput_CIPointerDevice_get_IsIntegrated(__x_ABI_CWindows_CDevices_CInput_CIPointerDevice* This,boolean *value) {
    return This->lpVtbl->get_IsIntegrated(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CInput_CIPointerDevice_get_MaxContacts(__x_ABI_CWindows_CDevices_CInput_CIPointerDevice* This,UINT32 *value) {
    return This->lpVtbl->get_MaxContacts(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CInput_CIPointerDevice_get_PhysicalDeviceRect(__x_ABI_CWindows_CDevices_CInput_CIPointerDevice* This,__x_ABI_CWindows_CFoundation_CRect *value) {
    return This->lpVtbl->get_PhysicalDeviceRect(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CInput_CIPointerDevice_get_ScreenRect(__x_ABI_CWindows_CDevices_CInput_CIPointerDevice* This,__x_ABI_CWindows_CFoundation_CRect *value) {
    return This->lpVtbl->get_ScreenRect(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CInput_CIPointerDevice_get_SupportedUsages(__x_ABI_CWindows_CDevices_CInput_CIPointerDevice* This,__FIVectorView_1_PointerDeviceUsage **value) {
    return This->lpVtbl->get_SupportedUsages(This,value);
}
#endif
#ifdef WIDL_using_Windows_Devices_Input
#define IID_IPointerDevice IID___x_ABI_CWindows_CDevices_CInput_CIPointerDevice
#define IPointerDeviceVtbl __x_ABI_CWindows_CDevices_CInput_CIPointerDeviceVtbl
#define IPointerDevice __x_ABI_CWindows_CDevices_CInput_CIPointerDevice
#define IPointerDevice_QueryInterface __x_ABI_CWindows_CDevices_CInput_CIPointerDevice_QueryInterface
#define IPointerDevice_AddRef __x_ABI_CWindows_CDevices_CInput_CIPointerDevice_AddRef
#define IPointerDevice_Release __x_ABI_CWindows_CDevices_CInput_CIPointerDevice_Release
#define IPointerDevice_GetIids __x_ABI_CWindows_CDevices_CInput_CIPointerDevice_GetIids
#define IPointerDevice_GetRuntimeClassName __x_ABI_CWindows_CDevices_CInput_CIPointerDevice_GetRuntimeClassName
#define IPointerDevice_GetTrustLevel __x_ABI_CWindows_CDevices_CInput_CIPointerDevice_GetTrustLevel
#define IPointerDevice_get_PointerDeviceType __x_ABI_CWindows_CDevices_CInput_CIPointerDevice_get_PointerDeviceType
#define IPointerDevice_get_IsIntegrated __x_ABI_CWindows_CDevices_CInput_CIPointerDevice_get_IsIntegrated
#define IPointerDevice_get_MaxContacts __x_ABI_CWindows_CDevices_CInput_CIPointerDevice_get_MaxContacts
#define IPointerDevice_get_PhysicalDeviceRect __x_ABI_CWindows_CDevices_CInput_CIPointerDevice_get_PhysicalDeviceRect
#define IPointerDevice_get_ScreenRect __x_ABI_CWindows_CDevices_CInput_CIPointerDevice_get_ScreenRect
#define IPointerDevice_get_SupportedUsages __x_ABI_CWindows_CDevices_CInput_CIPointerDevice_get_SupportedUsages
#endif /* WIDL_using_Windows_Devices_Input */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CDevices_CInput_CIPointerDevice_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IPointerDevice2 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CDevices_CInput_CIPointerDevice2_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CDevices_CInput_CIPointerDevice2_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CDevices_CInput_CIPointerDevice2, 0xf8a6d2a0, 0xc484, 0x489f, 0xae,0x3e, 0x30,0xd2,0xee,0x1f,0xfd,0x3e);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Input {
                MIDL_INTERFACE("f8a6d2a0-c484-489f-ae3e-30d2ee1ffd3e")
                IPointerDevice2 : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_MaxPointersWithZDistance(
                        UINT32 *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CDevices_CInput_CIPointerDevice2, 0xf8a6d2a0, 0xc484, 0x489f, 0xae,0x3e, 0x30,0xd2,0xee,0x1f,0xfd,0x3e)
#endif
#else
typedef struct __x_ABI_CWindows_CDevices_CInput_CIPointerDevice2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CDevices_CInput_CIPointerDevice2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CDevices_CInput_CIPointerDevice2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CDevices_CInput_CIPointerDevice2 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CDevices_CInput_CIPointerDevice2 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CDevices_CInput_CIPointerDevice2 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CDevices_CInput_CIPointerDevice2 *This,
        TrustLevel *trustLevel);

    /*** IPointerDevice2 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_MaxPointersWithZDistance)(
        __x_ABI_CWindows_CDevices_CInput_CIPointerDevice2 *This,
        UINT32 *value);

    END_INTERFACE
} __x_ABI_CWindows_CDevices_CInput_CIPointerDevice2Vtbl;

interface __x_ABI_CWindows_CDevices_CInput_CIPointerDevice2 {
    CONST_VTBL __x_ABI_CWindows_CDevices_CInput_CIPointerDevice2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CDevices_CInput_CIPointerDevice2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CDevices_CInput_CIPointerDevice2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CDevices_CInput_CIPointerDevice2_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CDevices_CInput_CIPointerDevice2_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CDevices_CInput_CIPointerDevice2_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CDevices_CInput_CIPointerDevice2_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IPointerDevice2 methods ***/
#define __x_ABI_CWindows_CDevices_CInput_CIPointerDevice2_get_MaxPointersWithZDistance(This,value) (This)->lpVtbl->get_MaxPointersWithZDistance(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CInput_CIPointerDevice2_QueryInterface(__x_ABI_CWindows_CDevices_CInput_CIPointerDevice2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CDevices_CInput_CIPointerDevice2_AddRef(__x_ABI_CWindows_CDevices_CInput_CIPointerDevice2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CDevices_CInput_CIPointerDevice2_Release(__x_ABI_CWindows_CDevices_CInput_CIPointerDevice2* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CInput_CIPointerDevice2_GetIids(__x_ABI_CWindows_CDevices_CInput_CIPointerDevice2* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CInput_CIPointerDevice2_GetRuntimeClassName(__x_ABI_CWindows_CDevices_CInput_CIPointerDevice2* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CInput_CIPointerDevice2_GetTrustLevel(__x_ABI_CWindows_CDevices_CInput_CIPointerDevice2* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IPointerDevice2 methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CInput_CIPointerDevice2_get_MaxPointersWithZDistance(__x_ABI_CWindows_CDevices_CInput_CIPointerDevice2* This,UINT32 *value) {
    return This->lpVtbl->get_MaxPointersWithZDistance(This,value);
}
#endif
#ifdef WIDL_using_Windows_Devices_Input
#define IID_IPointerDevice2 IID___x_ABI_CWindows_CDevices_CInput_CIPointerDevice2
#define IPointerDevice2Vtbl __x_ABI_CWindows_CDevices_CInput_CIPointerDevice2Vtbl
#define IPointerDevice2 __x_ABI_CWindows_CDevices_CInput_CIPointerDevice2
#define IPointerDevice2_QueryInterface __x_ABI_CWindows_CDevices_CInput_CIPointerDevice2_QueryInterface
#define IPointerDevice2_AddRef __x_ABI_CWindows_CDevices_CInput_CIPointerDevice2_AddRef
#define IPointerDevice2_Release __x_ABI_CWindows_CDevices_CInput_CIPointerDevice2_Release
#define IPointerDevice2_GetIids __x_ABI_CWindows_CDevices_CInput_CIPointerDevice2_GetIids
#define IPointerDevice2_GetRuntimeClassName __x_ABI_CWindows_CDevices_CInput_CIPointerDevice2_GetRuntimeClassName
#define IPointerDevice2_GetTrustLevel __x_ABI_CWindows_CDevices_CInput_CIPointerDevice2_GetTrustLevel
#define IPointerDevice2_get_MaxPointersWithZDistance __x_ABI_CWindows_CDevices_CInput_CIPointerDevice2_get_MaxPointersWithZDistance
#endif /* WIDL_using_Windows_Devices_Input */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CDevices_CInput_CIPointerDevice2_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IPointerDeviceStatics interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CDevices_CInput_CIPointerDeviceStatics_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CDevices_CInput_CIPointerDeviceStatics_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CDevices_CInput_CIPointerDeviceStatics, 0xd8b89aa1, 0xd1c6, 0x416e, 0xbd,0x8d, 0x57,0x90,0x91,0x4d,0xc5,0x63);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Input {
                MIDL_INTERFACE("d8b89aa1-d1c6-416e-bd8d-5790914dc563")
                IPointerDeviceStatics : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE GetPointerDevice(
                        UINT32 pointer_id,
                        ABI::Windows::Devices::Input::IPointerDevice **pointer_device) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetPointerDevices(
                        ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Devices::Input::PointerDevice* > **pointer_devices) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CDevices_CInput_CIPointerDeviceStatics, 0xd8b89aa1, 0xd1c6, 0x416e, 0xbd,0x8d, 0x57,0x90,0x91,0x4d,0xc5,0x63)
#endif
#else
typedef struct __x_ABI_CWindows_CDevices_CInput_CIPointerDeviceStaticsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CDevices_CInput_CIPointerDeviceStatics *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CDevices_CInput_CIPointerDeviceStatics *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CDevices_CInput_CIPointerDeviceStatics *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CDevices_CInput_CIPointerDeviceStatics *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CDevices_CInput_CIPointerDeviceStatics *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CDevices_CInput_CIPointerDeviceStatics *This,
        TrustLevel *trustLevel);

    /*** IPointerDeviceStatics methods ***/
    HRESULT (STDMETHODCALLTYPE *GetPointerDevice)(
        __x_ABI_CWindows_CDevices_CInput_CIPointerDeviceStatics *This,
        UINT32 pointer_id,
        __x_ABI_CWindows_CDevices_CInput_CIPointerDevice **pointer_device);

    HRESULT (STDMETHODCALLTYPE *GetPointerDevices)(
        __x_ABI_CWindows_CDevices_CInput_CIPointerDeviceStatics *This,
        __FIVectorView_1_Windows__CDevices__CInput__CPointerDevice **pointer_devices);

    END_INTERFACE
} __x_ABI_CWindows_CDevices_CInput_CIPointerDeviceStaticsVtbl;

interface __x_ABI_CWindows_CDevices_CInput_CIPointerDeviceStatics {
    CONST_VTBL __x_ABI_CWindows_CDevices_CInput_CIPointerDeviceStaticsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CDevices_CInput_CIPointerDeviceStatics_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CDevices_CInput_CIPointerDeviceStatics_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CDevices_CInput_CIPointerDeviceStatics_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CDevices_CInput_CIPointerDeviceStatics_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CDevices_CInput_CIPointerDeviceStatics_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CDevices_CInput_CIPointerDeviceStatics_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IPointerDeviceStatics methods ***/
#define __x_ABI_CWindows_CDevices_CInput_CIPointerDeviceStatics_GetPointerDevice(This,pointer_id,pointer_device) (This)->lpVtbl->GetPointerDevice(This,pointer_id,pointer_device)
#define __x_ABI_CWindows_CDevices_CInput_CIPointerDeviceStatics_GetPointerDevices(This,pointer_devices) (This)->lpVtbl->GetPointerDevices(This,pointer_devices)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CInput_CIPointerDeviceStatics_QueryInterface(__x_ABI_CWindows_CDevices_CInput_CIPointerDeviceStatics* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CDevices_CInput_CIPointerDeviceStatics_AddRef(__x_ABI_CWindows_CDevices_CInput_CIPointerDeviceStatics* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CDevices_CInput_CIPointerDeviceStatics_Release(__x_ABI_CWindows_CDevices_CInput_CIPointerDeviceStatics* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CInput_CIPointerDeviceStatics_GetIids(__x_ABI_CWindows_CDevices_CInput_CIPointerDeviceStatics* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CInput_CIPointerDeviceStatics_GetRuntimeClassName(__x_ABI_CWindows_CDevices_CInput_CIPointerDeviceStatics* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CInput_CIPointerDeviceStatics_GetTrustLevel(__x_ABI_CWindows_CDevices_CInput_CIPointerDeviceStatics* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IPointerDeviceStatics methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CInput_CIPointerDeviceStatics_GetPointerDevice(__x_ABI_CWindows_CDevices_CInput_CIPointerDeviceStatics* This,UINT32 pointer_id,__x_ABI_CWindows_CDevices_CInput_CIPointerDevice **pointer_device) {
    return This->lpVtbl->GetPointerDevice(This,pointer_id,pointer_device);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CInput_CIPointerDeviceStatics_GetPointerDevices(__x_ABI_CWindows_CDevices_CInput_CIPointerDeviceStatics* This,__FIVectorView_1_Windows__CDevices__CInput__CPointerDevice **pointer_devices) {
    return This->lpVtbl->GetPointerDevices(This,pointer_devices);
}
#endif
#ifdef WIDL_using_Windows_Devices_Input
#define IID_IPointerDeviceStatics IID___x_ABI_CWindows_CDevices_CInput_CIPointerDeviceStatics
#define IPointerDeviceStaticsVtbl __x_ABI_CWindows_CDevices_CInput_CIPointerDeviceStaticsVtbl
#define IPointerDeviceStatics __x_ABI_CWindows_CDevices_CInput_CIPointerDeviceStatics
#define IPointerDeviceStatics_QueryInterface __x_ABI_CWindows_CDevices_CInput_CIPointerDeviceStatics_QueryInterface
#define IPointerDeviceStatics_AddRef __x_ABI_CWindows_CDevices_CInput_CIPointerDeviceStatics_AddRef
#define IPointerDeviceStatics_Release __x_ABI_CWindows_CDevices_CInput_CIPointerDeviceStatics_Release
#define IPointerDeviceStatics_GetIids __x_ABI_CWindows_CDevices_CInput_CIPointerDeviceStatics_GetIids
#define IPointerDeviceStatics_GetRuntimeClassName __x_ABI_CWindows_CDevices_CInput_CIPointerDeviceStatics_GetRuntimeClassName
#define IPointerDeviceStatics_GetTrustLevel __x_ABI_CWindows_CDevices_CInput_CIPointerDeviceStatics_GetTrustLevel
#define IPointerDeviceStatics_GetPointerDevice __x_ABI_CWindows_CDevices_CInput_CIPointerDeviceStatics_GetPointerDevice
#define IPointerDeviceStatics_GetPointerDevices __x_ABI_CWindows_CDevices_CInput_CIPointerDeviceStatics_GetPointerDevices
#endif /* WIDL_using_Windows_Devices_Input */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CDevices_CInput_CIPointerDeviceStatics_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Devices.Input.PointerDevice
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Devices_Input_PointerDevice_DEFINED
#define RUNTIMECLASS_Windows_Devices_Input_PointerDevice_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Devices_Input_PointerDevice[] = {'W','i','n','d','o','w','s','.','D','e','v','i','c','e','s','.','I','n','p','u','t','.','P','o','i','n','t','e','r','D','e','v','i','c','e',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Devices_Input_PointerDevice[] = L"Windows.Devices.Input.PointerDevice";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Devices_Input_PointerDevice[] = {'W','i','n','d','o','w','s','.','D','e','v','i','c','e','s','.','I','n','p','u','t','.','P','o','i','n','t','e','r','D','e','v','i','c','e',0};
#endif
#endif /* RUNTIMECLASS_Windows_Devices_Input_PointerDevice_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IVectorView<ABI::Windows::Devices::Input::PointerDevice* > interface
 */
#ifndef ____FIVectorView_1_Windows__CDevices__CInput__CPointerDevice_INTERFACE_DEFINED__
#define ____FIVectorView_1_Windows__CDevices__CInput__CPointerDevice_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIVectorView_1_Windows__CDevices__CInput__CPointerDevice, 0xcf5674f1, 0x9808, 0x5a2b, 0x80,0xb8, 0x56,0x84,0xed,0x0e,0xa8,0x16);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("cf5674f1-9808-5a2b-80b8-5684ed0ea816")
                IVectorView<ABI::Windows::Devices::Input::PointerDevice* > : IVectorView_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Devices::Input::PointerDevice*, ABI::Windows::Devices::Input::IPointerDevice* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIVectorView_1_Windows__CDevices__CInput__CPointerDevice, 0xcf5674f1, 0x9808, 0x5a2b, 0x80,0xb8, 0x56,0x84,0xed,0x0e,0xa8,0x16)
#endif
#else
typedef struct __FIVectorView_1_Windows__CDevices__CInput__CPointerDeviceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIVectorView_1_Windows__CDevices__CInput__CPointerDevice *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIVectorView_1_Windows__CDevices__CInput__CPointerDevice *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIVectorView_1_Windows__CDevices__CInput__CPointerDevice *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIVectorView_1_Windows__CDevices__CInput__CPointerDevice *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIVectorView_1_Windows__CDevices__CInput__CPointerDevice *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIVectorView_1_Windows__CDevices__CInput__CPointerDevice *This,
        TrustLevel *trustLevel);

    /*** IVectorView<ABI::Windows::Devices::Input::PointerDevice* > methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAt)(
        __FIVectorView_1_Windows__CDevices__CInput__CPointerDevice *This,
        UINT32 index,
        __x_ABI_CWindows_CDevices_CInput_CIPointerDevice **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIVectorView_1_Windows__CDevices__CInput__CPointerDevice *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *IndexOf)(
        __FIVectorView_1_Windows__CDevices__CInput__CPointerDevice *This,
        __x_ABI_CWindows_CDevices_CInput_CIPointerDevice *element,
        UINT32 *index,
        BOOLEAN *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIVectorView_1_Windows__CDevices__CInput__CPointerDevice *This,
        UINT32 start_index,
        UINT32 items_size,
        __x_ABI_CWindows_CDevices_CInput_CIPointerDevice **items,
        UINT32 *value);

    END_INTERFACE
} __FIVectorView_1_Windows__CDevices__CInput__CPointerDeviceVtbl;

interface __FIVectorView_1_Windows__CDevices__CInput__CPointerDevice {
    CONST_VTBL __FIVectorView_1_Windows__CDevices__CInput__CPointerDeviceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIVectorView_1_Windows__CDevices__CInput__CPointerDevice_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIVectorView_1_Windows__CDevices__CInput__CPointerDevice_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIVectorView_1_Windows__CDevices__CInput__CPointerDevice_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIVectorView_1_Windows__CDevices__CInput__CPointerDevice_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIVectorView_1_Windows__CDevices__CInput__CPointerDevice_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIVectorView_1_Windows__CDevices__CInput__CPointerDevice_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IVectorView<ABI::Windows::Devices::Input::PointerDevice* > methods ***/
#define __FIVectorView_1_Windows__CDevices__CInput__CPointerDevice_GetAt(This,index,value) (This)->lpVtbl->GetAt(This,index,value)
#define __FIVectorView_1_Windows__CDevices__CInput__CPointerDevice_get_Size(This,value) (This)->lpVtbl->get_Size(This,value)
#define __FIVectorView_1_Windows__CDevices__CInput__CPointerDevice_IndexOf(This,element,index,value) (This)->lpVtbl->IndexOf(This,element,index,value)
#define __FIVectorView_1_Windows__CDevices__CInput__CPointerDevice_GetMany(This,start_index,items_size,items,value) (This)->lpVtbl->GetMany(This,start_index,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIVectorView_1_Windows__CDevices__CInput__CPointerDevice_QueryInterface(__FIVectorView_1_Windows__CDevices__CInput__CPointerDevice* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIVectorView_1_Windows__CDevices__CInput__CPointerDevice_AddRef(__FIVectorView_1_Windows__CDevices__CInput__CPointerDevice* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIVectorView_1_Windows__CDevices__CInput__CPointerDevice_Release(__FIVectorView_1_Windows__CDevices__CInput__CPointerDevice* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIVectorView_1_Windows__CDevices__CInput__CPointerDevice_GetIids(__FIVectorView_1_Windows__CDevices__CInput__CPointerDevice* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIVectorView_1_Windows__CDevices__CInput__CPointerDevice_GetRuntimeClassName(__FIVectorView_1_Windows__CDevices__CInput__CPointerDevice* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIVectorView_1_Windows__CDevices__CInput__CPointerDevice_GetTrustLevel(__FIVectorView_1_Windows__CDevices__CInput__CPointerDevice* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IVectorView<ABI::Windows::Devices::Input::PointerDevice* > methods ***/
static inline HRESULT __FIVectorView_1_Windows__CDevices__CInput__CPointerDevice_GetAt(__FIVectorView_1_Windows__CDevices__CInput__CPointerDevice* This,UINT32 index,__x_ABI_CWindows_CDevices_CInput_CIPointerDevice **value) {
    return This->lpVtbl->GetAt(This,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CDevices__CInput__CPointerDevice_get_Size(__FIVectorView_1_Windows__CDevices__CInput__CPointerDevice* This,UINT32 *value) {
    return This->lpVtbl->get_Size(This,value);
}
static inline HRESULT __FIVectorView_1_Windows__CDevices__CInput__CPointerDevice_IndexOf(__FIVectorView_1_Windows__CDevices__CInput__CPointerDevice* This,__x_ABI_CWindows_CDevices_CInput_CIPointerDevice *element,UINT32 *index,BOOLEAN *value) {
    return This->lpVtbl->IndexOf(This,element,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CDevices__CInput__CPointerDevice_GetMany(__FIVectorView_1_Windows__CDevices__CInput__CPointerDevice* This,UINT32 start_index,UINT32 items_size,__x_ABI_CWindows_CDevices_CInput_CIPointerDevice **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,start_index,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IVectorView_PointerDevice IID___FIVectorView_1_Windows__CDevices__CInput__CPointerDevice
#define IVectorView_PointerDeviceVtbl __FIVectorView_1_Windows__CDevices__CInput__CPointerDeviceVtbl
#define IVectorView_PointerDevice __FIVectorView_1_Windows__CDevices__CInput__CPointerDevice
#define IVectorView_PointerDevice_QueryInterface __FIVectorView_1_Windows__CDevices__CInput__CPointerDevice_QueryInterface
#define IVectorView_PointerDevice_AddRef __FIVectorView_1_Windows__CDevices__CInput__CPointerDevice_AddRef
#define IVectorView_PointerDevice_Release __FIVectorView_1_Windows__CDevices__CInput__CPointerDevice_Release
#define IVectorView_PointerDevice_GetIids __FIVectorView_1_Windows__CDevices__CInput__CPointerDevice_GetIids
#define IVectorView_PointerDevice_GetRuntimeClassName __FIVectorView_1_Windows__CDevices__CInput__CPointerDevice_GetRuntimeClassName
#define IVectorView_PointerDevice_GetTrustLevel __FIVectorView_1_Windows__CDevices__CInput__CPointerDevice_GetTrustLevel
#define IVectorView_PointerDevice_GetAt __FIVectorView_1_Windows__CDevices__CInput__CPointerDevice_GetAt
#define IVectorView_PointerDevice_get_Size __FIVectorView_1_Windows__CDevices__CInput__CPointerDevice_get_Size
#define IVectorView_PointerDevice_IndexOf __FIVectorView_1_Windows__CDevices__CInput__CPointerDevice_IndexOf
#define IVectorView_PointerDevice_GetMany __FIVectorView_1_Windows__CDevices__CInput__CPointerDevice_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIVectorView_1_Windows__CDevices__CInput__CPointerDevice_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVectorView<ABI::Windows::Devices::Input::PointerDeviceUsage > interface
 */
#ifndef ____FIVectorView_1_PointerDeviceUsage_INTERFACE_DEFINED__
#define ____FIVectorView_1_PointerDeviceUsage_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIVectorView_1_PointerDeviceUsage, 0x8e5a2c7e, 0x3830, 0x50d5, 0x92,0xba, 0x31,0x63,0xc8,0x9c,0xbb,0xd0);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("8e5a2c7e-3830-50d5-92ba-3163c89cbbd0")
                IVectorView<ABI::Windows::Devices::Input::PointerDeviceUsage > : IVectorView_impl<ABI::Windows::Devices::Input::PointerDeviceUsage >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIVectorView_1_PointerDeviceUsage, 0x8e5a2c7e, 0x3830, 0x50d5, 0x92,0xba, 0x31,0x63,0xc8,0x9c,0xbb,0xd0)
#endif
#else
typedef struct __FIVectorView_1_PointerDeviceUsageVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIVectorView_1_PointerDeviceUsage *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIVectorView_1_PointerDeviceUsage *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIVectorView_1_PointerDeviceUsage *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIVectorView_1_PointerDeviceUsage *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIVectorView_1_PointerDeviceUsage *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIVectorView_1_PointerDeviceUsage *This,
        TrustLevel *trustLevel);

    /*** IVectorView<ABI::Windows::Devices::Input::PointerDeviceUsage > methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAt)(
        __FIVectorView_1_PointerDeviceUsage *This,
        UINT32 index,
        __x_ABI_CWindows_CDevices_CInput_CPointerDeviceUsage *value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIVectorView_1_PointerDeviceUsage *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *IndexOf)(
        __FIVectorView_1_PointerDeviceUsage *This,
        __x_ABI_CWindows_CDevices_CInput_CPointerDeviceUsage element,
        UINT32 *index,
        BOOLEAN *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIVectorView_1_PointerDeviceUsage *This,
        UINT32 start_index,
        UINT32 items_size,
        __x_ABI_CWindows_CDevices_CInput_CPointerDeviceUsage *items,
        UINT32 *value);

    END_INTERFACE
} __FIVectorView_1_PointerDeviceUsageVtbl;

interface __FIVectorView_1_PointerDeviceUsage {
    CONST_VTBL __FIVectorView_1_PointerDeviceUsageVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIVectorView_1_PointerDeviceUsage_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIVectorView_1_PointerDeviceUsage_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIVectorView_1_PointerDeviceUsage_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIVectorView_1_PointerDeviceUsage_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIVectorView_1_PointerDeviceUsage_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIVectorView_1_PointerDeviceUsage_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IVectorView<ABI::Windows::Devices::Input::PointerDeviceUsage > methods ***/
#define __FIVectorView_1_PointerDeviceUsage_GetAt(This,index,value) (This)->lpVtbl->GetAt(This,index,value)
#define __FIVectorView_1_PointerDeviceUsage_get_Size(This,value) (This)->lpVtbl->get_Size(This,value)
#define __FIVectorView_1_PointerDeviceUsage_IndexOf(This,element,index,value) (This)->lpVtbl->IndexOf(This,element,index,value)
#define __FIVectorView_1_PointerDeviceUsage_GetMany(This,start_index,items_size,items,value) (This)->lpVtbl->GetMany(This,start_index,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIVectorView_1_PointerDeviceUsage_QueryInterface(__FIVectorView_1_PointerDeviceUsage* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIVectorView_1_PointerDeviceUsage_AddRef(__FIVectorView_1_PointerDeviceUsage* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIVectorView_1_PointerDeviceUsage_Release(__FIVectorView_1_PointerDeviceUsage* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIVectorView_1_PointerDeviceUsage_GetIids(__FIVectorView_1_PointerDeviceUsage* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIVectorView_1_PointerDeviceUsage_GetRuntimeClassName(__FIVectorView_1_PointerDeviceUsage* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIVectorView_1_PointerDeviceUsage_GetTrustLevel(__FIVectorView_1_PointerDeviceUsage* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IVectorView<ABI::Windows::Devices::Input::PointerDeviceUsage > methods ***/
static inline HRESULT __FIVectorView_1_PointerDeviceUsage_GetAt(__FIVectorView_1_PointerDeviceUsage* This,UINT32 index,__x_ABI_CWindows_CDevices_CInput_CPointerDeviceUsage *value) {
    return This->lpVtbl->GetAt(This,index,value);
}
static inline HRESULT __FIVectorView_1_PointerDeviceUsage_get_Size(__FIVectorView_1_PointerDeviceUsage* This,UINT32 *value) {
    return This->lpVtbl->get_Size(This,value);
}
static inline HRESULT __FIVectorView_1_PointerDeviceUsage_IndexOf(__FIVectorView_1_PointerDeviceUsage* This,__x_ABI_CWindows_CDevices_CInput_CPointerDeviceUsage element,UINT32 *index,BOOLEAN *value) {
    return This->lpVtbl->IndexOf(This,element,index,value);
}
static inline HRESULT __FIVectorView_1_PointerDeviceUsage_GetMany(__FIVectorView_1_PointerDeviceUsage* This,UINT32 start_index,UINT32 items_size,__x_ABI_CWindows_CDevices_CInput_CPointerDeviceUsage *items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,start_index,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IVectorView_PointerDeviceUsage IID___FIVectorView_1_PointerDeviceUsage
#define IVectorView_PointerDeviceUsageVtbl __FIVectorView_1_PointerDeviceUsageVtbl
#define IVectorView_PointerDeviceUsage __FIVectorView_1_PointerDeviceUsage
#define IVectorView_PointerDeviceUsage_QueryInterface __FIVectorView_1_PointerDeviceUsage_QueryInterface
#define IVectorView_PointerDeviceUsage_AddRef __FIVectorView_1_PointerDeviceUsage_AddRef
#define IVectorView_PointerDeviceUsage_Release __FIVectorView_1_PointerDeviceUsage_Release
#define IVectorView_PointerDeviceUsage_GetIids __FIVectorView_1_PointerDeviceUsage_GetIids
#define IVectorView_PointerDeviceUsage_GetRuntimeClassName __FIVectorView_1_PointerDeviceUsage_GetRuntimeClassName
#define IVectorView_PointerDeviceUsage_GetTrustLevel __FIVectorView_1_PointerDeviceUsage_GetTrustLevel
#define IVectorView_PointerDeviceUsage_GetAt __FIVectorView_1_PointerDeviceUsage_GetAt
#define IVectorView_PointerDeviceUsage_get_Size __FIVectorView_1_PointerDeviceUsage_get_Size
#define IVectorView_PointerDeviceUsage_IndexOf __FIVectorView_1_PointerDeviceUsage_IndexOf
#define IVectorView_PointerDeviceUsage_GetMany __FIVectorView_1_PointerDeviceUsage_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIVectorView_1_PointerDeviceUsage_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __windows_devices_input_h__ */
