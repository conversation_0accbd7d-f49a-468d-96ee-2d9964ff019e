// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: xla/tsl/protobuf/dnn.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_xla_2ftsl_2fprotobuf_2fdnn_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_xla_2ftsl_2fprotobuf_2fdnn_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include <google/protobuf/wrappers.pb.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_xla_2ftsl_2fprotobuf_2fdnn_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_xla_2ftsl_2fprotobuf_2fdnn_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_xla_2ftsl_2fprotobuf_2fdnn_2eproto;
namespace stream_executor {
namespace dnn {
class AlgorithmConfigProto;
struct AlgorithmConfigProtoDefaultTypeInternal;
extern AlgorithmConfigProtoDefaultTypeInternal _AlgorithmConfigProto_default_instance_;
class AlgorithmProto;
struct AlgorithmProtoDefaultTypeInternal;
extern AlgorithmProtoDefaultTypeInternal _AlgorithmProto_default_instance_;
class AlgorithmProto_TuningKnobsEntry_DoNotUse;
struct AlgorithmProto_TuningKnobsEntry_DoNotUseDefaultTypeInternal;
extern AlgorithmProto_TuningKnobsEntry_DoNotUseDefaultTypeInternal _AlgorithmProto_TuningKnobsEntry_DoNotUse_default_instance_;
class ConvolutionDescriptorProto;
struct ConvolutionDescriptorProtoDefaultTypeInternal;
extern ConvolutionDescriptorProtoDefaultTypeInternal _ConvolutionDescriptorProto_default_instance_;
class TensorDescriptorProto;
struct TensorDescriptorProtoDefaultTypeInternal;
extern TensorDescriptorProtoDefaultTypeInternal _TensorDescriptorProto_default_instance_;
}  // namespace dnn
}  // namespace stream_executor
PROTOBUF_NAMESPACE_OPEN
template<> ::stream_executor::dnn::AlgorithmConfigProto* Arena::CreateMaybeMessage<::stream_executor::dnn::AlgorithmConfigProto>(Arena*);
template<> ::stream_executor::dnn::AlgorithmProto* Arena::CreateMaybeMessage<::stream_executor::dnn::AlgorithmProto>(Arena*);
template<> ::stream_executor::dnn::AlgorithmProto_TuningKnobsEntry_DoNotUse* Arena::CreateMaybeMessage<::stream_executor::dnn::AlgorithmProto_TuningKnobsEntry_DoNotUse>(Arena*);
template<> ::stream_executor::dnn::ConvolutionDescriptorProto* Arena::CreateMaybeMessage<::stream_executor::dnn::ConvolutionDescriptorProto>(Arena*);
template<> ::stream_executor::dnn::TensorDescriptorProto* Arena::CreateMaybeMessage<::stream_executor::dnn::TensorDescriptorProto>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace stream_executor {
namespace dnn {

enum AlgorithmProto_MathType : int {
  AlgorithmProto_MathType_DEFAULT_MATH = 0,
  AlgorithmProto_MathType_TENSOR_OP_MATH = 1,
  AlgorithmProto_MathType_AlgorithmProto_MathType_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  AlgorithmProto_MathType_AlgorithmProto_MathType_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool AlgorithmProto_MathType_IsValid(int value);
constexpr AlgorithmProto_MathType AlgorithmProto_MathType_MathType_MIN = AlgorithmProto_MathType_DEFAULT_MATH;
constexpr AlgorithmProto_MathType AlgorithmProto_MathType_MathType_MAX = AlgorithmProto_MathType_TENSOR_OP_MATH;
constexpr int AlgorithmProto_MathType_MathType_ARRAYSIZE = AlgorithmProto_MathType_MathType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* AlgorithmProto_MathType_descriptor();
template<typename T>
inline const std::string& AlgorithmProto_MathType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, AlgorithmProto_MathType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function AlgorithmProto_MathType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    AlgorithmProto_MathType_descriptor(), enum_t_value);
}
inline bool AlgorithmProto_MathType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, AlgorithmProto_MathType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<AlgorithmProto_MathType>(
    AlgorithmProto_MathType_descriptor(), name, value);
}
enum DataType : int {
  kFloat = 0,
  kDouble = 1,
  kHalf = 2,
  kInt8 = 3,
  kInt32 = 4,
  kComplexFloat = 5,
  kComplexDouble = 6,
  kBF16 = 7,
  kF8E5M2 = 8,
  kF8E4M3FN = 9,
  kF8E5M2FNUZ = 10,
  kF8E4M3FNUZ = 11,
  kInt64 = 12,
  kF8E4M3 = 13,
  kF8E3M4 = 14,
  kF4E2M1FN = 15,
  kF8E8M0FNU = 16,
  DataType_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  DataType_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool DataType_IsValid(int value);
constexpr DataType DataType_MIN = kFloat;
constexpr DataType DataType_MAX = kF8E8M0FNU;
constexpr int DataType_ARRAYSIZE = DataType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* DataType_descriptor();
template<typename T>
inline const std::string& DataType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, DataType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function DataType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    DataType_descriptor(), enum_t_value);
}
inline bool DataType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, DataType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<DataType>(
    DataType_descriptor(), name, value);
}
enum DataLayout : int {
  kYXDepthBatch = 0,
  kYXBatchDepth = 1,
  kBatchYXDepth = 2,
  kBatchDepthYX = 3,
  kBatchDepthYX4 = 4,
  kBatchDepthYX32 = 5,
  DataLayout_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  DataLayout_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool DataLayout_IsValid(int value);
constexpr DataLayout DataLayout_MIN = kYXDepthBatch;
constexpr DataLayout DataLayout_MAX = kBatchDepthYX32;
constexpr int DataLayout_ARRAYSIZE = DataLayout_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* DataLayout_descriptor();
template<typename T>
inline const std::string& DataLayout_Name(T enum_t_value) {
  static_assert(::std::is_same<T, DataLayout>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function DataLayout_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    DataLayout_descriptor(), enum_t_value);
}
inline bool DataLayout_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, DataLayout* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<DataLayout>(
    DataLayout_descriptor(), name, value);
}
enum FilterLayout : int {
  kOutputInputYX = 0,
  kOutputYXInput = 1,
  kOutputInputYX4 = 2,
  kOutputInputYX32 = 5,
  kOutputInputYX32_CudnnReordered = 6,
  kInputYXOutput = 3,
  kYXInputOutput = 4,
  FilterLayout_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  FilterLayout_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool FilterLayout_IsValid(int value);
constexpr FilterLayout FilterLayout_MIN = kOutputInputYX;
constexpr FilterLayout FilterLayout_MAX = kOutputInputYX32_CudnnReordered;
constexpr int FilterLayout_ARRAYSIZE = FilterLayout_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* FilterLayout_descriptor();
template<typename T>
inline const std::string& FilterLayout_Name(T enum_t_value) {
  static_assert(::std::is_same<T, FilterLayout>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function FilterLayout_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    FilterLayout_descriptor(), enum_t_value);
}
inline bool FilterLayout_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, FilterLayout* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<FilterLayout>(
    FilterLayout_descriptor(), name, value);
}
enum ActivationMode : int {
  kNone = 0,
  kSigmoid = 1,
  kRelu = 2,
  kRelu6 = 3,
  kReluX = 4,
  kTanh = 5,
  kBandPass = 6,
  kElu = 7,
  kLeakyRelu = 8,
  kGeluExact = 9,
  ActivationMode_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  ActivationMode_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool ActivationMode_IsValid(int value);
constexpr ActivationMode ActivationMode_MIN = kNone;
constexpr ActivationMode ActivationMode_MAX = kGeluExact;
constexpr int ActivationMode_ARRAYSIZE = ActivationMode_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* ActivationMode_descriptor();
template<typename T>
inline const std::string& ActivationMode_Name(T enum_t_value) {
  static_assert(::std::is_same<T, ActivationMode>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function ActivationMode_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    ActivationMode_descriptor(), enum_t_value);
}
inline bool ActivationMode_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, ActivationMode* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<ActivationMode>(
    ActivationMode_descriptor(), name, value);
}
enum ConvolutionMode : int {
  CROSS_CORRELATION = 0,
  CONVOLUTION = 1,
  ConvolutionMode_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  ConvolutionMode_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool ConvolutionMode_IsValid(int value);
constexpr ConvolutionMode ConvolutionMode_MIN = CROSS_CORRELATION;
constexpr ConvolutionMode ConvolutionMode_MAX = CONVOLUTION;
constexpr int ConvolutionMode_ARRAYSIZE = ConvolutionMode_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* ConvolutionMode_descriptor();
template<typename T>
inline const std::string& ConvolutionMode_Name(T enum_t_value) {
  static_assert(::std::is_same<T, ConvolutionMode>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function ConvolutionMode_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    ConvolutionMode_descriptor(), enum_t_value);
}
inline bool ConvolutionMode_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, ConvolutionMode* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<ConvolutionMode>(
    ConvolutionMode_descriptor(), name, value);
}
enum ConvolutionKind : int {
  INVALID = 0,
  FORWARD = 1,
  BACKWARD_FILTER = 2,
  BACKWARD_DATA = 3,
  FORWARD_BIAS_ACTIVATION = 4,
  FORWARD_GRAPH = 5,
  ConvolutionKind_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  ConvolutionKind_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool ConvolutionKind_IsValid(int value);
constexpr ConvolutionKind ConvolutionKind_MIN = INVALID;
constexpr ConvolutionKind ConvolutionKind_MAX = FORWARD_GRAPH;
constexpr int ConvolutionKind_ARRAYSIZE = ConvolutionKind_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* ConvolutionKind_descriptor();
template<typename T>
inline const std::string& ConvolutionKind_Name(T enum_t_value) {
  static_assert(::std::is_same<T, ConvolutionKind>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function ConvolutionKind_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    ConvolutionKind_descriptor(), enum_t_value);
}
inline bool ConvolutionKind_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, ConvolutionKind* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<ConvolutionKind>(
    ConvolutionKind_descriptor(), name, value);
}
enum NormKind : int {
  LAYER_FWD_INFER = 0,
  LAYER_FWD_TRAIN = 1,
  LAYER_BWD = 2,
  NormKind_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  NormKind_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool NormKind_IsValid(int value);
constexpr NormKind NormKind_MIN = LAYER_FWD_INFER;
constexpr NormKind NormKind_MAX = LAYER_BWD;
constexpr int NormKind_ARRAYSIZE = NormKind_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* NormKind_descriptor();
template<typename T>
inline const std::string& NormKind_Name(T enum_t_value) {
  static_assert(::std::is_same<T, NormKind>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function NormKind_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    NormKind_descriptor(), enum_t_value);
}
inline bool NormKind_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, NormKind* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<NormKind>(
    NormKind_descriptor(), name, value);
}
enum FusedMHAKind : int {
  BMM1_OUTPUT_UNKNOWN = 0,
  BMM1_OUTPUT_INPUT_TYPE = 1,
  BMM1_OUTPUT_FLOAT = 2,
  FusedMHAKind_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  FusedMHAKind_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool FusedMHAKind_IsValid(int value);
constexpr FusedMHAKind FusedMHAKind_MIN = BMM1_OUTPUT_UNKNOWN;
constexpr FusedMHAKind FusedMHAKind_MAX = BMM1_OUTPUT_FLOAT;
constexpr int FusedMHAKind_ARRAYSIZE = FusedMHAKind_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* FusedMHAKind_descriptor();
template<typename T>
inline const std::string& FusedMHAKind_Name(T enum_t_value) {
  static_assert(::std::is_same<T, FusedMHAKind>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function FusedMHAKind_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    FusedMHAKind_descriptor(), enum_t_value);
}
inline bool FusedMHAKind_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, FusedMHAKind* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<FusedMHAKind>(
    FusedMHAKind_descriptor(), name, value);
}
enum FMHAMaskKind : int {
  NO_MASK = 0,
  PADDING = 1,
  CAUSAL = 2,
  PADDING_CAUSAL = 3,
  ALIBI = 4,
  FMHAMaskKind_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  FMHAMaskKind_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool FMHAMaskKind_IsValid(int value);
constexpr FMHAMaskKind FMHAMaskKind_MIN = NO_MASK;
constexpr FMHAMaskKind FMHAMaskKind_MAX = ALIBI;
constexpr int FMHAMaskKind_ARRAYSIZE = FMHAMaskKind_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* FMHAMaskKind_descriptor();
template<typename T>
inline const std::string& FMHAMaskKind_Name(T enum_t_value) {
  static_assert(::std::is_same<T, FMHAMaskKind>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function FMHAMaskKind_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    FMHAMaskKind_descriptor(), enum_t_value);
}
inline bool FMHAMaskKind_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, FMHAMaskKind* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<FMHAMaskKind>(
    FMHAMaskKind_descriptor(), name, value);
}
// ===================================================================

class TensorDescriptorProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:stream_executor.dnn.TensorDescriptorProto) */ {
 public:
  inline TensorDescriptorProto() : TensorDescriptorProto(nullptr) {}
  ~TensorDescriptorProto() override;
  explicit PROTOBUF_CONSTEXPR TensorDescriptorProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TensorDescriptorProto(const TensorDescriptorProto& from);
  TensorDescriptorProto(TensorDescriptorProto&& from) noexcept
    : TensorDescriptorProto() {
    *this = ::std::move(from);
  }

  inline TensorDescriptorProto& operator=(const TensorDescriptorProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline TensorDescriptorProto& operator=(TensorDescriptorProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TensorDescriptorProto& default_instance() {
    return *internal_default_instance();
  }
  enum LayoutOneofCase {
    kDataLayout = 3,
    kFilterLayout = 4,
    LAYOUT_ONEOF_NOT_SET = 0,
  };

  static inline const TensorDescriptorProto* internal_default_instance() {
    return reinterpret_cast<const TensorDescriptorProto*>(
               &_TensorDescriptorProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(TensorDescriptorProto& a, TensorDescriptorProto& b) {
    a.Swap(&b);
  }
  inline void Swap(TensorDescriptorProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TensorDescriptorProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TensorDescriptorProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TensorDescriptorProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TensorDescriptorProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const TensorDescriptorProto& from) {
    TensorDescriptorProto::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TensorDescriptorProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "stream_executor.dnn.TensorDescriptorProto";
  }
  protected:
  explicit TensorDescriptorProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDimensionsFieldNumber = 1,
    kDataTypeFieldNumber = 2,
    kDataLayoutFieldNumber = 3,
    kFilterLayoutFieldNumber = 4,
  };
  // repeated int64 dimensions = 1;
  int dimensions_size() const;
  private:
  int _internal_dimensions_size() const;
  public:
  void clear_dimensions();
  private:
  int64_t _internal_dimensions(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      _internal_dimensions() const;
  void _internal_add_dimensions(int64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      _internal_mutable_dimensions();
  public:
  int64_t dimensions(int index) const;
  void set_dimensions(int index, int64_t value);
  void add_dimensions(int64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      dimensions() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      mutable_dimensions();

  // .stream_executor.dnn.DataType data_type = 2;
  void clear_data_type();
  ::stream_executor::dnn::DataType data_type() const;
  void set_data_type(::stream_executor::dnn::DataType value);
  private:
  ::stream_executor::dnn::DataType _internal_data_type() const;
  void _internal_set_data_type(::stream_executor::dnn::DataType value);
  public:

  // .stream_executor.dnn.DataLayout data_layout = 3;
  bool has_data_layout() const;
  private:
  bool _internal_has_data_layout() const;
  public:
  void clear_data_layout();
  ::stream_executor::dnn::DataLayout data_layout() const;
  void set_data_layout(::stream_executor::dnn::DataLayout value);
  private:
  ::stream_executor::dnn::DataLayout _internal_data_layout() const;
  void _internal_set_data_layout(::stream_executor::dnn::DataLayout value);
  public:

  // .stream_executor.dnn.FilterLayout filter_layout = 4;
  bool has_filter_layout() const;
  private:
  bool _internal_has_filter_layout() const;
  public:
  void clear_filter_layout();
  ::stream_executor::dnn::FilterLayout filter_layout() const;
  void set_filter_layout(::stream_executor::dnn::FilterLayout value);
  private:
  ::stream_executor::dnn::FilterLayout _internal_filter_layout() const;
  void _internal_set_filter_layout(::stream_executor::dnn::FilterLayout value);
  public:

  void clear_layout_oneof();
  LayoutOneofCase layout_oneof_case() const;
  // @@protoc_insertion_point(class_scope:stream_executor.dnn.TensorDescriptorProto)
 private:
  class _Internal;
  void set_has_data_layout();
  void set_has_filter_layout();

  inline bool has_layout_oneof() const;
  inline void clear_has_layout_oneof();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t > dimensions_;
    mutable std::atomic<int> _dimensions_cached_byte_size_;
    int data_type_;
    union LayoutOneofUnion {
      constexpr LayoutOneofUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      int data_layout_;
      int filter_layout_;
    } layout_oneof_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    uint32_t _oneof_case_[1];

  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2fdnn_2eproto;
};
// -------------------------------------------------------------------

class AlgorithmProto_TuningKnobsEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<AlgorithmProto_TuningKnobsEntry_DoNotUse, 
    int64_t, int64_t,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<AlgorithmProto_TuningKnobsEntry_DoNotUse, 
    int64_t, int64_t,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64> SuperType;
  AlgorithmProto_TuningKnobsEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR AlgorithmProto_TuningKnobsEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit AlgorithmProto_TuningKnobsEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const AlgorithmProto_TuningKnobsEntry_DoNotUse& other);
  static const AlgorithmProto_TuningKnobsEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const AlgorithmProto_TuningKnobsEntry_DoNotUse*>(&_AlgorithmProto_TuningKnobsEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(void*) { return true; }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2fdnn_2eproto;
};

// -------------------------------------------------------------------

class AlgorithmProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:stream_executor.dnn.AlgorithmProto) */ {
 public:
  inline AlgorithmProto() : AlgorithmProto(nullptr) {}
  ~AlgorithmProto() override;
  explicit PROTOBUF_CONSTEXPR AlgorithmProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  AlgorithmProto(const AlgorithmProto& from);
  AlgorithmProto(AlgorithmProto&& from) noexcept
    : AlgorithmProto() {
    *this = ::std::move(from);
  }

  inline AlgorithmProto& operator=(const AlgorithmProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline AlgorithmProto& operator=(AlgorithmProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const AlgorithmProto& default_instance() {
    return *internal_default_instance();
  }
  static inline const AlgorithmProto* internal_default_instance() {
    return reinterpret_cast<const AlgorithmProto*>(
               &_AlgorithmProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(AlgorithmProto& a, AlgorithmProto& b) {
    a.Swap(&b);
  }
  inline void Swap(AlgorithmProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AlgorithmProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  AlgorithmProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<AlgorithmProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const AlgorithmProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const AlgorithmProto& from) {
    AlgorithmProto::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AlgorithmProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "stream_executor.dnn.AlgorithmProto";
  }
  protected:
  explicit AlgorithmProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  typedef AlgorithmProto_MathType MathType;
  static constexpr MathType DEFAULT_MATH =
    AlgorithmProto_MathType_DEFAULT_MATH;
  static constexpr MathType TENSOR_OP_MATH =
    AlgorithmProto_MathType_TENSOR_OP_MATH;
  static inline bool MathType_IsValid(int value) {
    return AlgorithmProto_MathType_IsValid(value);
  }
  static constexpr MathType MathType_MIN =
    AlgorithmProto_MathType_MathType_MIN;
  static constexpr MathType MathType_MAX =
    AlgorithmProto_MathType_MathType_MAX;
  static constexpr int MathType_ARRAYSIZE =
    AlgorithmProto_MathType_MathType_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  MathType_descriptor() {
    return AlgorithmProto_MathType_descriptor();
  }
  template<typename T>
  static inline const std::string& MathType_Name(T enum_t_value) {
    static_assert(::std::is_same<T, MathType>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function MathType_Name.");
    return AlgorithmProto_MathType_Name(enum_t_value);
  }
  static inline bool MathType_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      MathType* value) {
    return AlgorithmProto_MathType_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kTuningKnobsFieldNumber = 4,
    kWorkspaceSizeFieldNumber = 6,
    kAlgoIdFieldNumber = 1,
    kMathTypeFieldNumber = 2,
    kIsCudnnFrontendFieldNumber = 5,
  };
  // map<int64, int64> tuning_knobs = 4;
  int tuning_knobs_size() const;
  private:
  int _internal_tuning_knobs_size() const;
  public:
  void clear_tuning_knobs();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< int64_t, int64_t >&
      _internal_tuning_knobs() const;
  ::PROTOBUF_NAMESPACE_ID::Map< int64_t, int64_t >*
      _internal_mutable_tuning_knobs();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< int64_t, int64_t >&
      tuning_knobs() const;
  ::PROTOBUF_NAMESPACE_ID::Map< int64_t, int64_t >*
      mutable_tuning_knobs();

  // .google.protobuf.UInt64Value workspace_size = 6;
  bool has_workspace_size() const;
  private:
  bool _internal_has_workspace_size() const;
  public:
  void clear_workspace_size();
  const ::PROTOBUF_NAMESPACE_ID::UInt64Value& workspace_size() const;
  PROTOBUF_NODISCARD ::PROTOBUF_NAMESPACE_ID::UInt64Value* release_workspace_size();
  ::PROTOBUF_NAMESPACE_ID::UInt64Value* mutable_workspace_size();
  void set_allocated_workspace_size(::PROTOBUF_NAMESPACE_ID::UInt64Value* workspace_size);
  private:
  const ::PROTOBUF_NAMESPACE_ID::UInt64Value& _internal_workspace_size() const;
  ::PROTOBUF_NAMESPACE_ID::UInt64Value* _internal_mutable_workspace_size();
  public:
  void unsafe_arena_set_allocated_workspace_size(
      ::PROTOBUF_NAMESPACE_ID::UInt64Value* workspace_size);
  ::PROTOBUF_NAMESPACE_ID::UInt64Value* unsafe_arena_release_workspace_size();

  // int64 algo_id = 1;
  void clear_algo_id();
  int64_t algo_id() const;
  void set_algo_id(int64_t value);
  private:
  int64_t _internal_algo_id() const;
  void _internal_set_algo_id(int64_t value);
  public:

  // .stream_executor.dnn.AlgorithmProto.MathType math_type = 2;
  void clear_math_type();
  ::stream_executor::dnn::AlgorithmProto_MathType math_type() const;
  void set_math_type(::stream_executor::dnn::AlgorithmProto_MathType value);
  private:
  ::stream_executor::dnn::AlgorithmProto_MathType _internal_math_type() const;
  void _internal_set_math_type(::stream_executor::dnn::AlgorithmProto_MathType value);
  public:

  // bool is_cudnn_frontend = 5;
  void clear_is_cudnn_frontend();
  bool is_cudnn_frontend() const;
  void set_is_cudnn_frontend(bool value);
  private:
  bool _internal_is_cudnn_frontend() const;
  void _internal_set_is_cudnn_frontend(bool value);
  public:

  // @@protoc_insertion_point(class_scope:stream_executor.dnn.AlgorithmProto)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        AlgorithmProto_TuningKnobsEntry_DoNotUse,
        int64_t, int64_t,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64> tuning_knobs_;
    ::PROTOBUF_NAMESPACE_ID::UInt64Value* workspace_size_;
    int64_t algo_id_;
    int math_type_;
    bool is_cudnn_frontend_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2fdnn_2eproto;
};
// -------------------------------------------------------------------

class AlgorithmConfigProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:stream_executor.dnn.AlgorithmConfigProto) */ {
 public:
  inline AlgorithmConfigProto() : AlgorithmConfigProto(nullptr) {}
  ~AlgorithmConfigProto() override;
  explicit PROTOBUF_CONSTEXPR AlgorithmConfigProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  AlgorithmConfigProto(const AlgorithmConfigProto& from);
  AlgorithmConfigProto(AlgorithmConfigProto&& from) noexcept
    : AlgorithmConfigProto() {
    *this = ::std::move(from);
  }

  inline AlgorithmConfigProto& operator=(const AlgorithmConfigProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline AlgorithmConfigProto& operator=(AlgorithmConfigProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const AlgorithmConfigProto& default_instance() {
    return *internal_default_instance();
  }
  enum OptionalAlgorithmCase {
    kAlgorithm = 1,
    OPTIONAL_ALGORITHM_NOT_SET = 0,
  };

  enum OptionalAlgorithmNoScratchCase {
    kAlgorithmNoScratch = 2,
    OPTIONAL_ALGORITHM_NO_SCRATCH_NOT_SET = 0,
  };

  enum OptionalScratchSizeCase {
    kScratchSize = 3,
    OPTIONAL_SCRATCH_SIZE_NOT_SET = 0,
  };

  static inline const AlgorithmConfigProto* internal_default_instance() {
    return reinterpret_cast<const AlgorithmConfigProto*>(
               &_AlgorithmConfigProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(AlgorithmConfigProto& a, AlgorithmConfigProto& b) {
    a.Swap(&b);
  }
  inline void Swap(AlgorithmConfigProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AlgorithmConfigProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  AlgorithmConfigProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<AlgorithmConfigProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const AlgorithmConfigProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const AlgorithmConfigProto& from) {
    AlgorithmConfigProto::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AlgorithmConfigProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "stream_executor.dnn.AlgorithmConfigProto";
  }
  protected:
  explicit AlgorithmConfigProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kAlgorithmFieldNumber = 1,
    kAlgorithmNoScratchFieldNumber = 2,
    kScratchSizeFieldNumber = 3,
  };
  // .stream_executor.dnn.AlgorithmProto algorithm = 1;
  bool has_algorithm() const;
  private:
  bool _internal_has_algorithm() const;
  public:
  void clear_algorithm();
  const ::stream_executor::dnn::AlgorithmProto& algorithm() const;
  PROTOBUF_NODISCARD ::stream_executor::dnn::AlgorithmProto* release_algorithm();
  ::stream_executor::dnn::AlgorithmProto* mutable_algorithm();
  void set_allocated_algorithm(::stream_executor::dnn::AlgorithmProto* algorithm);
  private:
  const ::stream_executor::dnn::AlgorithmProto& _internal_algorithm() const;
  ::stream_executor::dnn::AlgorithmProto* _internal_mutable_algorithm();
  public:
  void unsafe_arena_set_allocated_algorithm(
      ::stream_executor::dnn::AlgorithmProto* algorithm);
  ::stream_executor::dnn::AlgorithmProto* unsafe_arena_release_algorithm();

  // .stream_executor.dnn.AlgorithmProto algorithm_no_scratch = 2;
  bool has_algorithm_no_scratch() const;
  private:
  bool _internal_has_algorithm_no_scratch() const;
  public:
  void clear_algorithm_no_scratch();
  const ::stream_executor::dnn::AlgorithmProto& algorithm_no_scratch() const;
  PROTOBUF_NODISCARD ::stream_executor::dnn::AlgorithmProto* release_algorithm_no_scratch();
  ::stream_executor::dnn::AlgorithmProto* mutable_algorithm_no_scratch();
  void set_allocated_algorithm_no_scratch(::stream_executor::dnn::AlgorithmProto* algorithm_no_scratch);
  private:
  const ::stream_executor::dnn::AlgorithmProto& _internal_algorithm_no_scratch() const;
  ::stream_executor::dnn::AlgorithmProto* _internal_mutable_algorithm_no_scratch();
  public:
  void unsafe_arena_set_allocated_algorithm_no_scratch(
      ::stream_executor::dnn::AlgorithmProto* algorithm_no_scratch);
  ::stream_executor::dnn::AlgorithmProto* unsafe_arena_release_algorithm_no_scratch();

  // int64 scratch_size = 3;
  bool has_scratch_size() const;
  private:
  bool _internal_has_scratch_size() const;
  public:
  void clear_scratch_size();
  int64_t scratch_size() const;
  void set_scratch_size(int64_t value);
  private:
  int64_t _internal_scratch_size() const;
  void _internal_set_scratch_size(int64_t value);
  public:

  void clear_optional_algorithm();
  OptionalAlgorithmCase optional_algorithm_case() const;
  void clear_optional_algorithm_no_scratch();
  OptionalAlgorithmNoScratchCase optional_algorithm_no_scratch_case() const;
  void clear_optional_scratch_size();
  OptionalScratchSizeCase optional_scratch_size_case() const;
  // @@protoc_insertion_point(class_scope:stream_executor.dnn.AlgorithmConfigProto)
 private:
  class _Internal;
  void set_has_algorithm();
  void set_has_algorithm_no_scratch();
  void set_has_scratch_size();

  inline bool has_optional_algorithm() const;
  inline void clear_has_optional_algorithm();

  inline bool has_optional_algorithm_no_scratch() const;
  inline void clear_has_optional_algorithm_no_scratch();

  inline bool has_optional_scratch_size() const;
  inline void clear_has_optional_scratch_size();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    union OptionalAlgorithmUnion {
      constexpr OptionalAlgorithmUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      ::stream_executor::dnn::AlgorithmProto* algorithm_;
    } optional_algorithm_;
    union OptionalAlgorithmNoScratchUnion {
      constexpr OptionalAlgorithmNoScratchUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      ::stream_executor::dnn::AlgorithmProto* algorithm_no_scratch_;
    } optional_algorithm_no_scratch_;
    union OptionalScratchSizeUnion {
      constexpr OptionalScratchSizeUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      int64_t scratch_size_;
    } optional_scratch_size_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    uint32_t _oneof_case_[3];

  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2fdnn_2eproto;
};
// -------------------------------------------------------------------

class ConvolutionDescriptorProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:stream_executor.dnn.ConvolutionDescriptorProto) */ {
 public:
  inline ConvolutionDescriptorProto() : ConvolutionDescriptorProto(nullptr) {}
  ~ConvolutionDescriptorProto() override;
  explicit PROTOBUF_CONSTEXPR ConvolutionDescriptorProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ConvolutionDescriptorProto(const ConvolutionDescriptorProto& from);
  ConvolutionDescriptorProto(ConvolutionDescriptorProto&& from) noexcept
    : ConvolutionDescriptorProto() {
    *this = ::std::move(from);
  }

  inline ConvolutionDescriptorProto& operator=(const ConvolutionDescriptorProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline ConvolutionDescriptorProto& operator=(ConvolutionDescriptorProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ConvolutionDescriptorProto& default_instance() {
    return *internal_default_instance();
  }
  static inline const ConvolutionDescriptorProto* internal_default_instance() {
    return reinterpret_cast<const ConvolutionDescriptorProto*>(
               &_ConvolutionDescriptorProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(ConvolutionDescriptorProto& a, ConvolutionDescriptorProto& b) {
    a.Swap(&b);
  }
  inline void Swap(ConvolutionDescriptorProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ConvolutionDescriptorProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ConvolutionDescriptorProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ConvolutionDescriptorProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ConvolutionDescriptorProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ConvolutionDescriptorProto& from) {
    ConvolutionDescriptorProto::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ConvolutionDescriptorProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "stream_executor.dnn.ConvolutionDescriptorProto";
  }
  protected:
  explicit ConvolutionDescriptorProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPaddingsFieldNumber = 1,
    kStridesFieldNumber = 2,
    kDilationsFieldNumber = 3,
    kNameFieldNumber = 7,
    kComputeModeFieldNumber = 4,
    kGroupCountFieldNumber = 5,
    kConvolutionModeFieldNumber = 6,
  };
  // repeated int64 paddings = 1;
  int paddings_size() const;
  private:
  int _internal_paddings_size() const;
  public:
  void clear_paddings();
  private:
  int64_t _internal_paddings(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      _internal_paddings() const;
  void _internal_add_paddings(int64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      _internal_mutable_paddings();
  public:
  int64_t paddings(int index) const;
  void set_paddings(int index, int64_t value);
  void add_paddings(int64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      paddings() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      mutable_paddings();

  // repeated int64 strides = 2;
  int strides_size() const;
  private:
  int _internal_strides_size() const;
  public:
  void clear_strides();
  private:
  int64_t _internal_strides(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      _internal_strides() const;
  void _internal_add_strides(int64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      _internal_mutable_strides();
  public:
  int64_t strides(int index) const;
  void set_strides(int index, int64_t value);
  void add_strides(int64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      strides() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      mutable_strides();

  // repeated int64 dilations = 3;
  int dilations_size() const;
  private:
  int _internal_dilations_size() const;
  public:
  void clear_dilations();
  private:
  int64_t _internal_dilations(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      _internal_dilations() const;
  void _internal_add_dilations(int64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      _internal_mutable_dilations();
  public:
  int64_t dilations(int index) const;
  void set_dilations(int index, int64_t value);
  void add_dilations(int64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      dilations() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      mutable_dilations();

  // string name = 7;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // .stream_executor.dnn.DataType compute_mode = 4;
  void clear_compute_mode();
  ::stream_executor::dnn::DataType compute_mode() const;
  void set_compute_mode(::stream_executor::dnn::DataType value);
  private:
  ::stream_executor::dnn::DataType _internal_compute_mode() const;
  void _internal_set_compute_mode(::stream_executor::dnn::DataType value);
  public:

  // int32 group_count = 5;
  void clear_group_count();
  int32_t group_count() const;
  void set_group_count(int32_t value);
  private:
  int32_t _internal_group_count() const;
  void _internal_set_group_count(int32_t value);
  public:

  // .stream_executor.dnn.ConvolutionMode convolution_mode = 6;
  void clear_convolution_mode();
  ::stream_executor::dnn::ConvolutionMode convolution_mode() const;
  void set_convolution_mode(::stream_executor::dnn::ConvolutionMode value);
  private:
  ::stream_executor::dnn::ConvolutionMode _internal_convolution_mode() const;
  void _internal_set_convolution_mode(::stream_executor::dnn::ConvolutionMode value);
  public:

  // @@protoc_insertion_point(class_scope:stream_executor.dnn.ConvolutionDescriptorProto)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t > paddings_;
    mutable std::atomic<int> _paddings_cached_byte_size_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t > strides_;
    mutable std::atomic<int> _strides_cached_byte_size_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t > dilations_;
    mutable std::atomic<int> _dilations_cached_byte_size_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    int compute_mode_;
    int32_t group_count_;
    int convolution_mode_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2fdnn_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// TensorDescriptorProto

// repeated int64 dimensions = 1;
inline int TensorDescriptorProto::_internal_dimensions_size() const {
  return _impl_.dimensions_.size();
}
inline int TensorDescriptorProto::dimensions_size() const {
  return _internal_dimensions_size();
}
inline void TensorDescriptorProto::clear_dimensions() {
  _impl_.dimensions_.Clear();
}
inline int64_t TensorDescriptorProto::_internal_dimensions(int index) const {
  return _impl_.dimensions_.Get(index);
}
inline int64_t TensorDescriptorProto::dimensions(int index) const {
  // @@protoc_insertion_point(field_get:stream_executor.dnn.TensorDescriptorProto.dimensions)
  return _internal_dimensions(index);
}
inline void TensorDescriptorProto::set_dimensions(int index, int64_t value) {
  _impl_.dimensions_.Set(index, value);
  // @@protoc_insertion_point(field_set:stream_executor.dnn.TensorDescriptorProto.dimensions)
}
inline void TensorDescriptorProto::_internal_add_dimensions(int64_t value) {
  _impl_.dimensions_.Add(value);
}
inline void TensorDescriptorProto::add_dimensions(int64_t value) {
  _internal_add_dimensions(value);
  // @@protoc_insertion_point(field_add:stream_executor.dnn.TensorDescriptorProto.dimensions)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
TensorDescriptorProto::_internal_dimensions() const {
  return _impl_.dimensions_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
TensorDescriptorProto::dimensions() const {
  // @@protoc_insertion_point(field_list:stream_executor.dnn.TensorDescriptorProto.dimensions)
  return _internal_dimensions();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
TensorDescriptorProto::_internal_mutable_dimensions() {
  return &_impl_.dimensions_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
TensorDescriptorProto::mutable_dimensions() {
  // @@protoc_insertion_point(field_mutable_list:stream_executor.dnn.TensorDescriptorProto.dimensions)
  return _internal_mutable_dimensions();
}

// .stream_executor.dnn.DataType data_type = 2;
inline void TensorDescriptorProto::clear_data_type() {
  _impl_.data_type_ = 0;
}
inline ::stream_executor::dnn::DataType TensorDescriptorProto::_internal_data_type() const {
  return static_cast< ::stream_executor::dnn::DataType >(_impl_.data_type_);
}
inline ::stream_executor::dnn::DataType TensorDescriptorProto::data_type() const {
  // @@protoc_insertion_point(field_get:stream_executor.dnn.TensorDescriptorProto.data_type)
  return _internal_data_type();
}
inline void TensorDescriptorProto::_internal_set_data_type(::stream_executor::dnn::DataType value) {
  
  _impl_.data_type_ = value;
}
inline void TensorDescriptorProto::set_data_type(::stream_executor::dnn::DataType value) {
  _internal_set_data_type(value);
  // @@protoc_insertion_point(field_set:stream_executor.dnn.TensorDescriptorProto.data_type)
}

// .stream_executor.dnn.DataLayout data_layout = 3;
inline bool TensorDescriptorProto::_internal_has_data_layout() const {
  return layout_oneof_case() == kDataLayout;
}
inline bool TensorDescriptorProto::has_data_layout() const {
  return _internal_has_data_layout();
}
inline void TensorDescriptorProto::set_has_data_layout() {
  _impl_._oneof_case_[0] = kDataLayout;
}
inline void TensorDescriptorProto::clear_data_layout() {
  if (_internal_has_data_layout()) {
    _impl_.layout_oneof_.data_layout_ = 0;
    clear_has_layout_oneof();
  }
}
inline ::stream_executor::dnn::DataLayout TensorDescriptorProto::_internal_data_layout() const {
  if (_internal_has_data_layout()) {
    return static_cast< ::stream_executor::dnn::DataLayout >(_impl_.layout_oneof_.data_layout_);
  }
  return static_cast< ::stream_executor::dnn::DataLayout >(0);
}
inline ::stream_executor::dnn::DataLayout TensorDescriptorProto::data_layout() const {
  // @@protoc_insertion_point(field_get:stream_executor.dnn.TensorDescriptorProto.data_layout)
  return _internal_data_layout();
}
inline void TensorDescriptorProto::_internal_set_data_layout(::stream_executor::dnn::DataLayout value) {
  if (!_internal_has_data_layout()) {
    clear_layout_oneof();
    set_has_data_layout();
  }
  _impl_.layout_oneof_.data_layout_ = value;
}
inline void TensorDescriptorProto::set_data_layout(::stream_executor::dnn::DataLayout value) {
  _internal_set_data_layout(value);
  // @@protoc_insertion_point(field_set:stream_executor.dnn.TensorDescriptorProto.data_layout)
}

// .stream_executor.dnn.FilterLayout filter_layout = 4;
inline bool TensorDescriptorProto::_internal_has_filter_layout() const {
  return layout_oneof_case() == kFilterLayout;
}
inline bool TensorDescriptorProto::has_filter_layout() const {
  return _internal_has_filter_layout();
}
inline void TensorDescriptorProto::set_has_filter_layout() {
  _impl_._oneof_case_[0] = kFilterLayout;
}
inline void TensorDescriptorProto::clear_filter_layout() {
  if (_internal_has_filter_layout()) {
    _impl_.layout_oneof_.filter_layout_ = 0;
    clear_has_layout_oneof();
  }
}
inline ::stream_executor::dnn::FilterLayout TensorDescriptorProto::_internal_filter_layout() const {
  if (_internal_has_filter_layout()) {
    return static_cast< ::stream_executor::dnn::FilterLayout >(_impl_.layout_oneof_.filter_layout_);
  }
  return static_cast< ::stream_executor::dnn::FilterLayout >(0);
}
inline ::stream_executor::dnn::FilterLayout TensorDescriptorProto::filter_layout() const {
  // @@protoc_insertion_point(field_get:stream_executor.dnn.TensorDescriptorProto.filter_layout)
  return _internal_filter_layout();
}
inline void TensorDescriptorProto::_internal_set_filter_layout(::stream_executor::dnn::FilterLayout value) {
  if (!_internal_has_filter_layout()) {
    clear_layout_oneof();
    set_has_filter_layout();
  }
  _impl_.layout_oneof_.filter_layout_ = value;
}
inline void TensorDescriptorProto::set_filter_layout(::stream_executor::dnn::FilterLayout value) {
  _internal_set_filter_layout(value);
  // @@protoc_insertion_point(field_set:stream_executor.dnn.TensorDescriptorProto.filter_layout)
}

inline bool TensorDescriptorProto::has_layout_oneof() const {
  return layout_oneof_case() != LAYOUT_ONEOF_NOT_SET;
}
inline void TensorDescriptorProto::clear_has_layout_oneof() {
  _impl_._oneof_case_[0] = LAYOUT_ONEOF_NOT_SET;
}
inline TensorDescriptorProto::LayoutOneofCase TensorDescriptorProto::layout_oneof_case() const {
  return TensorDescriptorProto::LayoutOneofCase(_impl_._oneof_case_[0]);
}
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// AlgorithmProto

// int64 algo_id = 1;
inline void AlgorithmProto::clear_algo_id() {
  _impl_.algo_id_ = int64_t{0};
}
inline int64_t AlgorithmProto::_internal_algo_id() const {
  return _impl_.algo_id_;
}
inline int64_t AlgorithmProto::algo_id() const {
  // @@protoc_insertion_point(field_get:stream_executor.dnn.AlgorithmProto.algo_id)
  return _internal_algo_id();
}
inline void AlgorithmProto::_internal_set_algo_id(int64_t value) {
  
  _impl_.algo_id_ = value;
}
inline void AlgorithmProto::set_algo_id(int64_t value) {
  _internal_set_algo_id(value);
  // @@protoc_insertion_point(field_set:stream_executor.dnn.AlgorithmProto.algo_id)
}

// .stream_executor.dnn.AlgorithmProto.MathType math_type = 2;
inline void AlgorithmProto::clear_math_type() {
  _impl_.math_type_ = 0;
}
inline ::stream_executor::dnn::AlgorithmProto_MathType AlgorithmProto::_internal_math_type() const {
  return static_cast< ::stream_executor::dnn::AlgorithmProto_MathType >(_impl_.math_type_);
}
inline ::stream_executor::dnn::AlgorithmProto_MathType AlgorithmProto::math_type() const {
  // @@protoc_insertion_point(field_get:stream_executor.dnn.AlgorithmProto.math_type)
  return _internal_math_type();
}
inline void AlgorithmProto::_internal_set_math_type(::stream_executor::dnn::AlgorithmProto_MathType value) {
  
  _impl_.math_type_ = value;
}
inline void AlgorithmProto::set_math_type(::stream_executor::dnn::AlgorithmProto_MathType value) {
  _internal_set_math_type(value);
  // @@protoc_insertion_point(field_set:stream_executor.dnn.AlgorithmProto.math_type)
}

// map<int64, int64> tuning_knobs = 4;
inline int AlgorithmProto::_internal_tuning_knobs_size() const {
  return _impl_.tuning_knobs_.size();
}
inline int AlgorithmProto::tuning_knobs_size() const {
  return _internal_tuning_knobs_size();
}
inline void AlgorithmProto::clear_tuning_knobs() {
  _impl_.tuning_knobs_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< int64_t, int64_t >&
AlgorithmProto::_internal_tuning_knobs() const {
  return _impl_.tuning_knobs_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< int64_t, int64_t >&
AlgorithmProto::tuning_knobs() const {
  // @@protoc_insertion_point(field_map:stream_executor.dnn.AlgorithmProto.tuning_knobs)
  return _internal_tuning_knobs();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< int64_t, int64_t >*
AlgorithmProto::_internal_mutable_tuning_knobs() {
  return _impl_.tuning_knobs_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< int64_t, int64_t >*
AlgorithmProto::mutable_tuning_knobs() {
  // @@protoc_insertion_point(field_mutable_map:stream_executor.dnn.AlgorithmProto.tuning_knobs)
  return _internal_mutable_tuning_knobs();
}

// bool is_cudnn_frontend = 5;
inline void AlgorithmProto::clear_is_cudnn_frontend() {
  _impl_.is_cudnn_frontend_ = false;
}
inline bool AlgorithmProto::_internal_is_cudnn_frontend() const {
  return _impl_.is_cudnn_frontend_;
}
inline bool AlgorithmProto::is_cudnn_frontend() const {
  // @@protoc_insertion_point(field_get:stream_executor.dnn.AlgorithmProto.is_cudnn_frontend)
  return _internal_is_cudnn_frontend();
}
inline void AlgorithmProto::_internal_set_is_cudnn_frontend(bool value) {
  
  _impl_.is_cudnn_frontend_ = value;
}
inline void AlgorithmProto::set_is_cudnn_frontend(bool value) {
  _internal_set_is_cudnn_frontend(value);
  // @@protoc_insertion_point(field_set:stream_executor.dnn.AlgorithmProto.is_cudnn_frontend)
}

// .google.protobuf.UInt64Value workspace_size = 6;
inline bool AlgorithmProto::_internal_has_workspace_size() const {
  return this != internal_default_instance() && _impl_.workspace_size_ != nullptr;
}
inline bool AlgorithmProto::has_workspace_size() const {
  return _internal_has_workspace_size();
}
inline const ::PROTOBUF_NAMESPACE_ID::UInt64Value& AlgorithmProto::_internal_workspace_size() const {
  const ::PROTOBUF_NAMESPACE_ID::UInt64Value* p = _impl_.workspace_size_;
  return p != nullptr ? *p : reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::UInt64Value&>(
      ::PROTOBUF_NAMESPACE_ID::_UInt64Value_default_instance_);
}
inline const ::PROTOBUF_NAMESPACE_ID::UInt64Value& AlgorithmProto::workspace_size() const {
  // @@protoc_insertion_point(field_get:stream_executor.dnn.AlgorithmProto.workspace_size)
  return _internal_workspace_size();
}
inline void AlgorithmProto::unsafe_arena_set_allocated_workspace_size(
    ::PROTOBUF_NAMESPACE_ID::UInt64Value* workspace_size) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.workspace_size_);
  }
  _impl_.workspace_size_ = workspace_size;
  if (workspace_size) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:stream_executor.dnn.AlgorithmProto.workspace_size)
}
inline ::PROTOBUF_NAMESPACE_ID::UInt64Value* AlgorithmProto::release_workspace_size() {
  
  ::PROTOBUF_NAMESPACE_ID::UInt64Value* temp = _impl_.workspace_size_;
  _impl_.workspace_size_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::UInt64Value* AlgorithmProto::unsafe_arena_release_workspace_size() {
  // @@protoc_insertion_point(field_release:stream_executor.dnn.AlgorithmProto.workspace_size)
  
  ::PROTOBUF_NAMESPACE_ID::UInt64Value* temp = _impl_.workspace_size_;
  _impl_.workspace_size_ = nullptr;
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::UInt64Value* AlgorithmProto::_internal_mutable_workspace_size() {
  
  if (_impl_.workspace_size_ == nullptr) {
    auto* p = CreateMaybeMessage<::PROTOBUF_NAMESPACE_ID::UInt64Value>(GetArenaForAllocation());
    _impl_.workspace_size_ = p;
  }
  return _impl_.workspace_size_;
}
inline ::PROTOBUF_NAMESPACE_ID::UInt64Value* AlgorithmProto::mutable_workspace_size() {
  ::PROTOBUF_NAMESPACE_ID::UInt64Value* _msg = _internal_mutable_workspace_size();
  // @@protoc_insertion_point(field_mutable:stream_executor.dnn.AlgorithmProto.workspace_size)
  return _msg;
}
inline void AlgorithmProto::set_allocated_workspace_size(::PROTOBUF_NAMESPACE_ID::UInt64Value* workspace_size) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.workspace_size_);
  }
  if (workspace_size) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(workspace_size));
    if (message_arena != submessage_arena) {
      workspace_size = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, workspace_size, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.workspace_size_ = workspace_size;
  // @@protoc_insertion_point(field_set_allocated:stream_executor.dnn.AlgorithmProto.workspace_size)
}

// -------------------------------------------------------------------

// AlgorithmConfigProto

// .stream_executor.dnn.AlgorithmProto algorithm = 1;
inline bool AlgorithmConfigProto::_internal_has_algorithm() const {
  return optional_algorithm_case() == kAlgorithm;
}
inline bool AlgorithmConfigProto::has_algorithm() const {
  return _internal_has_algorithm();
}
inline void AlgorithmConfigProto::set_has_algorithm() {
  _impl_._oneof_case_[0] = kAlgorithm;
}
inline void AlgorithmConfigProto::clear_algorithm() {
  if (_internal_has_algorithm()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.optional_algorithm_.algorithm_;
    }
    clear_has_optional_algorithm();
  }
}
inline ::stream_executor::dnn::AlgorithmProto* AlgorithmConfigProto::release_algorithm() {
  // @@protoc_insertion_point(field_release:stream_executor.dnn.AlgorithmConfigProto.algorithm)
  if (_internal_has_algorithm()) {
    clear_has_optional_algorithm();
    ::stream_executor::dnn::AlgorithmProto* temp = _impl_.optional_algorithm_.algorithm_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.optional_algorithm_.algorithm_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::stream_executor::dnn::AlgorithmProto& AlgorithmConfigProto::_internal_algorithm() const {
  return _internal_has_algorithm()
      ? *_impl_.optional_algorithm_.algorithm_
      : reinterpret_cast< ::stream_executor::dnn::AlgorithmProto&>(::stream_executor::dnn::_AlgorithmProto_default_instance_);
}
inline const ::stream_executor::dnn::AlgorithmProto& AlgorithmConfigProto::algorithm() const {
  // @@protoc_insertion_point(field_get:stream_executor.dnn.AlgorithmConfigProto.algorithm)
  return _internal_algorithm();
}
inline ::stream_executor::dnn::AlgorithmProto* AlgorithmConfigProto::unsafe_arena_release_algorithm() {
  // @@protoc_insertion_point(field_unsafe_arena_release:stream_executor.dnn.AlgorithmConfigProto.algorithm)
  if (_internal_has_algorithm()) {
    clear_has_optional_algorithm();
    ::stream_executor::dnn::AlgorithmProto* temp = _impl_.optional_algorithm_.algorithm_;
    _impl_.optional_algorithm_.algorithm_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void AlgorithmConfigProto::unsafe_arena_set_allocated_algorithm(::stream_executor::dnn::AlgorithmProto* algorithm) {
  clear_optional_algorithm();
  if (algorithm) {
    set_has_algorithm();
    _impl_.optional_algorithm_.algorithm_ = algorithm;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:stream_executor.dnn.AlgorithmConfigProto.algorithm)
}
inline ::stream_executor::dnn::AlgorithmProto* AlgorithmConfigProto::_internal_mutable_algorithm() {
  if (!_internal_has_algorithm()) {
    clear_optional_algorithm();
    set_has_algorithm();
    _impl_.optional_algorithm_.algorithm_ = CreateMaybeMessage< ::stream_executor::dnn::AlgorithmProto >(GetArenaForAllocation());
  }
  return _impl_.optional_algorithm_.algorithm_;
}
inline ::stream_executor::dnn::AlgorithmProto* AlgorithmConfigProto::mutable_algorithm() {
  ::stream_executor::dnn::AlgorithmProto* _msg = _internal_mutable_algorithm();
  // @@protoc_insertion_point(field_mutable:stream_executor.dnn.AlgorithmConfigProto.algorithm)
  return _msg;
}

// .stream_executor.dnn.AlgorithmProto algorithm_no_scratch = 2;
inline bool AlgorithmConfigProto::_internal_has_algorithm_no_scratch() const {
  return optional_algorithm_no_scratch_case() == kAlgorithmNoScratch;
}
inline bool AlgorithmConfigProto::has_algorithm_no_scratch() const {
  return _internal_has_algorithm_no_scratch();
}
inline void AlgorithmConfigProto::set_has_algorithm_no_scratch() {
  _impl_._oneof_case_[1] = kAlgorithmNoScratch;
}
inline void AlgorithmConfigProto::clear_algorithm_no_scratch() {
  if (_internal_has_algorithm_no_scratch()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.optional_algorithm_no_scratch_.algorithm_no_scratch_;
    }
    clear_has_optional_algorithm_no_scratch();
  }
}
inline ::stream_executor::dnn::AlgorithmProto* AlgorithmConfigProto::release_algorithm_no_scratch() {
  // @@protoc_insertion_point(field_release:stream_executor.dnn.AlgorithmConfigProto.algorithm_no_scratch)
  if (_internal_has_algorithm_no_scratch()) {
    clear_has_optional_algorithm_no_scratch();
    ::stream_executor::dnn::AlgorithmProto* temp = _impl_.optional_algorithm_no_scratch_.algorithm_no_scratch_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.optional_algorithm_no_scratch_.algorithm_no_scratch_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::stream_executor::dnn::AlgorithmProto& AlgorithmConfigProto::_internal_algorithm_no_scratch() const {
  return _internal_has_algorithm_no_scratch()
      ? *_impl_.optional_algorithm_no_scratch_.algorithm_no_scratch_
      : reinterpret_cast< ::stream_executor::dnn::AlgorithmProto&>(::stream_executor::dnn::_AlgorithmProto_default_instance_);
}
inline const ::stream_executor::dnn::AlgorithmProto& AlgorithmConfigProto::algorithm_no_scratch() const {
  // @@protoc_insertion_point(field_get:stream_executor.dnn.AlgorithmConfigProto.algorithm_no_scratch)
  return _internal_algorithm_no_scratch();
}
inline ::stream_executor::dnn::AlgorithmProto* AlgorithmConfigProto::unsafe_arena_release_algorithm_no_scratch() {
  // @@protoc_insertion_point(field_unsafe_arena_release:stream_executor.dnn.AlgorithmConfigProto.algorithm_no_scratch)
  if (_internal_has_algorithm_no_scratch()) {
    clear_has_optional_algorithm_no_scratch();
    ::stream_executor::dnn::AlgorithmProto* temp = _impl_.optional_algorithm_no_scratch_.algorithm_no_scratch_;
    _impl_.optional_algorithm_no_scratch_.algorithm_no_scratch_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void AlgorithmConfigProto::unsafe_arena_set_allocated_algorithm_no_scratch(::stream_executor::dnn::AlgorithmProto* algorithm_no_scratch) {
  clear_optional_algorithm_no_scratch();
  if (algorithm_no_scratch) {
    set_has_algorithm_no_scratch();
    _impl_.optional_algorithm_no_scratch_.algorithm_no_scratch_ = algorithm_no_scratch;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:stream_executor.dnn.AlgorithmConfigProto.algorithm_no_scratch)
}
inline ::stream_executor::dnn::AlgorithmProto* AlgorithmConfigProto::_internal_mutable_algorithm_no_scratch() {
  if (!_internal_has_algorithm_no_scratch()) {
    clear_optional_algorithm_no_scratch();
    set_has_algorithm_no_scratch();
    _impl_.optional_algorithm_no_scratch_.algorithm_no_scratch_ = CreateMaybeMessage< ::stream_executor::dnn::AlgorithmProto >(GetArenaForAllocation());
  }
  return _impl_.optional_algorithm_no_scratch_.algorithm_no_scratch_;
}
inline ::stream_executor::dnn::AlgorithmProto* AlgorithmConfigProto::mutable_algorithm_no_scratch() {
  ::stream_executor::dnn::AlgorithmProto* _msg = _internal_mutable_algorithm_no_scratch();
  // @@protoc_insertion_point(field_mutable:stream_executor.dnn.AlgorithmConfigProto.algorithm_no_scratch)
  return _msg;
}

// int64 scratch_size = 3;
inline bool AlgorithmConfigProto::_internal_has_scratch_size() const {
  return optional_scratch_size_case() == kScratchSize;
}
inline bool AlgorithmConfigProto::has_scratch_size() const {
  return _internal_has_scratch_size();
}
inline void AlgorithmConfigProto::set_has_scratch_size() {
  _impl_._oneof_case_[2] = kScratchSize;
}
inline void AlgorithmConfigProto::clear_scratch_size() {
  if (_internal_has_scratch_size()) {
    _impl_.optional_scratch_size_.scratch_size_ = int64_t{0};
    clear_has_optional_scratch_size();
  }
}
inline int64_t AlgorithmConfigProto::_internal_scratch_size() const {
  if (_internal_has_scratch_size()) {
    return _impl_.optional_scratch_size_.scratch_size_;
  }
  return int64_t{0};
}
inline void AlgorithmConfigProto::_internal_set_scratch_size(int64_t value) {
  if (!_internal_has_scratch_size()) {
    clear_optional_scratch_size();
    set_has_scratch_size();
  }
  _impl_.optional_scratch_size_.scratch_size_ = value;
}
inline int64_t AlgorithmConfigProto::scratch_size() const {
  // @@protoc_insertion_point(field_get:stream_executor.dnn.AlgorithmConfigProto.scratch_size)
  return _internal_scratch_size();
}
inline void AlgorithmConfigProto::set_scratch_size(int64_t value) {
  _internal_set_scratch_size(value);
  // @@protoc_insertion_point(field_set:stream_executor.dnn.AlgorithmConfigProto.scratch_size)
}

inline bool AlgorithmConfigProto::has_optional_algorithm() const {
  return optional_algorithm_case() != OPTIONAL_ALGORITHM_NOT_SET;
}
inline void AlgorithmConfigProto::clear_has_optional_algorithm() {
  _impl_._oneof_case_[0] = OPTIONAL_ALGORITHM_NOT_SET;
}
inline bool AlgorithmConfigProto::has_optional_algorithm_no_scratch() const {
  return optional_algorithm_no_scratch_case() != OPTIONAL_ALGORITHM_NO_SCRATCH_NOT_SET;
}
inline void AlgorithmConfigProto::clear_has_optional_algorithm_no_scratch() {
  _impl_._oneof_case_[1] = OPTIONAL_ALGORITHM_NO_SCRATCH_NOT_SET;
}
inline bool AlgorithmConfigProto::has_optional_scratch_size() const {
  return optional_scratch_size_case() != OPTIONAL_SCRATCH_SIZE_NOT_SET;
}
inline void AlgorithmConfigProto::clear_has_optional_scratch_size() {
  _impl_._oneof_case_[2] = OPTIONAL_SCRATCH_SIZE_NOT_SET;
}
inline AlgorithmConfigProto::OptionalAlgorithmCase AlgorithmConfigProto::optional_algorithm_case() const {
  return AlgorithmConfigProto::OptionalAlgorithmCase(_impl_._oneof_case_[0]);
}
inline AlgorithmConfigProto::OptionalAlgorithmNoScratchCase AlgorithmConfigProto::optional_algorithm_no_scratch_case() const {
  return AlgorithmConfigProto::OptionalAlgorithmNoScratchCase(_impl_._oneof_case_[1]);
}
inline AlgorithmConfigProto::OptionalScratchSizeCase AlgorithmConfigProto::optional_scratch_size_case() const {
  return AlgorithmConfigProto::OptionalScratchSizeCase(_impl_._oneof_case_[2]);
}
// -------------------------------------------------------------------

// ConvolutionDescriptorProto

// repeated int64 paddings = 1;
inline int ConvolutionDescriptorProto::_internal_paddings_size() const {
  return _impl_.paddings_.size();
}
inline int ConvolutionDescriptorProto::paddings_size() const {
  return _internal_paddings_size();
}
inline void ConvolutionDescriptorProto::clear_paddings() {
  _impl_.paddings_.Clear();
}
inline int64_t ConvolutionDescriptorProto::_internal_paddings(int index) const {
  return _impl_.paddings_.Get(index);
}
inline int64_t ConvolutionDescriptorProto::paddings(int index) const {
  // @@protoc_insertion_point(field_get:stream_executor.dnn.ConvolutionDescriptorProto.paddings)
  return _internal_paddings(index);
}
inline void ConvolutionDescriptorProto::set_paddings(int index, int64_t value) {
  _impl_.paddings_.Set(index, value);
  // @@protoc_insertion_point(field_set:stream_executor.dnn.ConvolutionDescriptorProto.paddings)
}
inline void ConvolutionDescriptorProto::_internal_add_paddings(int64_t value) {
  _impl_.paddings_.Add(value);
}
inline void ConvolutionDescriptorProto::add_paddings(int64_t value) {
  _internal_add_paddings(value);
  // @@protoc_insertion_point(field_add:stream_executor.dnn.ConvolutionDescriptorProto.paddings)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
ConvolutionDescriptorProto::_internal_paddings() const {
  return _impl_.paddings_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
ConvolutionDescriptorProto::paddings() const {
  // @@protoc_insertion_point(field_list:stream_executor.dnn.ConvolutionDescriptorProto.paddings)
  return _internal_paddings();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
ConvolutionDescriptorProto::_internal_mutable_paddings() {
  return &_impl_.paddings_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
ConvolutionDescriptorProto::mutable_paddings() {
  // @@protoc_insertion_point(field_mutable_list:stream_executor.dnn.ConvolutionDescriptorProto.paddings)
  return _internal_mutable_paddings();
}

// repeated int64 strides = 2;
inline int ConvolutionDescriptorProto::_internal_strides_size() const {
  return _impl_.strides_.size();
}
inline int ConvolutionDescriptorProto::strides_size() const {
  return _internal_strides_size();
}
inline void ConvolutionDescriptorProto::clear_strides() {
  _impl_.strides_.Clear();
}
inline int64_t ConvolutionDescriptorProto::_internal_strides(int index) const {
  return _impl_.strides_.Get(index);
}
inline int64_t ConvolutionDescriptorProto::strides(int index) const {
  // @@protoc_insertion_point(field_get:stream_executor.dnn.ConvolutionDescriptorProto.strides)
  return _internal_strides(index);
}
inline void ConvolutionDescriptorProto::set_strides(int index, int64_t value) {
  _impl_.strides_.Set(index, value);
  // @@protoc_insertion_point(field_set:stream_executor.dnn.ConvolutionDescriptorProto.strides)
}
inline void ConvolutionDescriptorProto::_internal_add_strides(int64_t value) {
  _impl_.strides_.Add(value);
}
inline void ConvolutionDescriptorProto::add_strides(int64_t value) {
  _internal_add_strides(value);
  // @@protoc_insertion_point(field_add:stream_executor.dnn.ConvolutionDescriptorProto.strides)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
ConvolutionDescriptorProto::_internal_strides() const {
  return _impl_.strides_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
ConvolutionDescriptorProto::strides() const {
  // @@protoc_insertion_point(field_list:stream_executor.dnn.ConvolutionDescriptorProto.strides)
  return _internal_strides();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
ConvolutionDescriptorProto::_internal_mutable_strides() {
  return &_impl_.strides_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
ConvolutionDescriptorProto::mutable_strides() {
  // @@protoc_insertion_point(field_mutable_list:stream_executor.dnn.ConvolutionDescriptorProto.strides)
  return _internal_mutable_strides();
}

// repeated int64 dilations = 3;
inline int ConvolutionDescriptorProto::_internal_dilations_size() const {
  return _impl_.dilations_.size();
}
inline int ConvolutionDescriptorProto::dilations_size() const {
  return _internal_dilations_size();
}
inline void ConvolutionDescriptorProto::clear_dilations() {
  _impl_.dilations_.Clear();
}
inline int64_t ConvolutionDescriptorProto::_internal_dilations(int index) const {
  return _impl_.dilations_.Get(index);
}
inline int64_t ConvolutionDescriptorProto::dilations(int index) const {
  // @@protoc_insertion_point(field_get:stream_executor.dnn.ConvolutionDescriptorProto.dilations)
  return _internal_dilations(index);
}
inline void ConvolutionDescriptorProto::set_dilations(int index, int64_t value) {
  _impl_.dilations_.Set(index, value);
  // @@protoc_insertion_point(field_set:stream_executor.dnn.ConvolutionDescriptorProto.dilations)
}
inline void ConvolutionDescriptorProto::_internal_add_dilations(int64_t value) {
  _impl_.dilations_.Add(value);
}
inline void ConvolutionDescriptorProto::add_dilations(int64_t value) {
  _internal_add_dilations(value);
  // @@protoc_insertion_point(field_add:stream_executor.dnn.ConvolutionDescriptorProto.dilations)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
ConvolutionDescriptorProto::_internal_dilations() const {
  return _impl_.dilations_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
ConvolutionDescriptorProto::dilations() const {
  // @@protoc_insertion_point(field_list:stream_executor.dnn.ConvolutionDescriptorProto.dilations)
  return _internal_dilations();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
ConvolutionDescriptorProto::_internal_mutable_dilations() {
  return &_impl_.dilations_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
ConvolutionDescriptorProto::mutable_dilations() {
  // @@protoc_insertion_point(field_mutable_list:stream_executor.dnn.ConvolutionDescriptorProto.dilations)
  return _internal_mutable_dilations();
}

// .stream_executor.dnn.DataType compute_mode = 4;
inline void ConvolutionDescriptorProto::clear_compute_mode() {
  _impl_.compute_mode_ = 0;
}
inline ::stream_executor::dnn::DataType ConvolutionDescriptorProto::_internal_compute_mode() const {
  return static_cast< ::stream_executor::dnn::DataType >(_impl_.compute_mode_);
}
inline ::stream_executor::dnn::DataType ConvolutionDescriptorProto::compute_mode() const {
  // @@protoc_insertion_point(field_get:stream_executor.dnn.ConvolutionDescriptorProto.compute_mode)
  return _internal_compute_mode();
}
inline void ConvolutionDescriptorProto::_internal_set_compute_mode(::stream_executor::dnn::DataType value) {
  
  _impl_.compute_mode_ = value;
}
inline void ConvolutionDescriptorProto::set_compute_mode(::stream_executor::dnn::DataType value) {
  _internal_set_compute_mode(value);
  // @@protoc_insertion_point(field_set:stream_executor.dnn.ConvolutionDescriptorProto.compute_mode)
}

// int32 group_count = 5;
inline void ConvolutionDescriptorProto::clear_group_count() {
  _impl_.group_count_ = 0;
}
inline int32_t ConvolutionDescriptorProto::_internal_group_count() const {
  return _impl_.group_count_;
}
inline int32_t ConvolutionDescriptorProto::group_count() const {
  // @@protoc_insertion_point(field_get:stream_executor.dnn.ConvolutionDescriptorProto.group_count)
  return _internal_group_count();
}
inline void ConvolutionDescriptorProto::_internal_set_group_count(int32_t value) {
  
  _impl_.group_count_ = value;
}
inline void ConvolutionDescriptorProto::set_group_count(int32_t value) {
  _internal_set_group_count(value);
  // @@protoc_insertion_point(field_set:stream_executor.dnn.ConvolutionDescriptorProto.group_count)
}

// .stream_executor.dnn.ConvolutionMode convolution_mode = 6;
inline void ConvolutionDescriptorProto::clear_convolution_mode() {
  _impl_.convolution_mode_ = 0;
}
inline ::stream_executor::dnn::ConvolutionMode ConvolutionDescriptorProto::_internal_convolution_mode() const {
  return static_cast< ::stream_executor::dnn::ConvolutionMode >(_impl_.convolution_mode_);
}
inline ::stream_executor::dnn::ConvolutionMode ConvolutionDescriptorProto::convolution_mode() const {
  // @@protoc_insertion_point(field_get:stream_executor.dnn.ConvolutionDescriptorProto.convolution_mode)
  return _internal_convolution_mode();
}
inline void ConvolutionDescriptorProto::_internal_set_convolution_mode(::stream_executor::dnn::ConvolutionMode value) {
  
  _impl_.convolution_mode_ = value;
}
inline void ConvolutionDescriptorProto::set_convolution_mode(::stream_executor::dnn::ConvolutionMode value) {
  _internal_set_convolution_mode(value);
  // @@protoc_insertion_point(field_set:stream_executor.dnn.ConvolutionDescriptorProto.convolution_mode)
}

// string name = 7;
inline void ConvolutionDescriptorProto::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& ConvolutionDescriptorProto::name() const {
  // @@protoc_insertion_point(field_get:stream_executor.dnn.ConvolutionDescriptorProto.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ConvolutionDescriptorProto::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:stream_executor.dnn.ConvolutionDescriptorProto.name)
}
inline std::string* ConvolutionDescriptorProto::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:stream_executor.dnn.ConvolutionDescriptorProto.name)
  return _s;
}
inline const std::string& ConvolutionDescriptorProto::_internal_name() const {
  return _impl_.name_.Get();
}
inline void ConvolutionDescriptorProto::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* ConvolutionDescriptorProto::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* ConvolutionDescriptorProto::release_name() {
  // @@protoc_insertion_point(field_release:stream_executor.dnn.ConvolutionDescriptorProto.name)
  return _impl_.name_.Release();
}
inline void ConvolutionDescriptorProto::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:stream_executor.dnn.ConvolutionDescriptorProto.name)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace dnn
}  // namespace stream_executor

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::stream_executor::dnn::AlgorithmProto_MathType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::stream_executor::dnn::AlgorithmProto_MathType>() {
  return ::stream_executor::dnn::AlgorithmProto_MathType_descriptor();
}
template <> struct is_proto_enum< ::stream_executor::dnn::DataType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::stream_executor::dnn::DataType>() {
  return ::stream_executor::dnn::DataType_descriptor();
}
template <> struct is_proto_enum< ::stream_executor::dnn::DataLayout> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::stream_executor::dnn::DataLayout>() {
  return ::stream_executor::dnn::DataLayout_descriptor();
}
template <> struct is_proto_enum< ::stream_executor::dnn::FilterLayout> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::stream_executor::dnn::FilterLayout>() {
  return ::stream_executor::dnn::FilterLayout_descriptor();
}
template <> struct is_proto_enum< ::stream_executor::dnn::ActivationMode> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::stream_executor::dnn::ActivationMode>() {
  return ::stream_executor::dnn::ActivationMode_descriptor();
}
template <> struct is_proto_enum< ::stream_executor::dnn::ConvolutionMode> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::stream_executor::dnn::ConvolutionMode>() {
  return ::stream_executor::dnn::ConvolutionMode_descriptor();
}
template <> struct is_proto_enum< ::stream_executor::dnn::ConvolutionKind> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::stream_executor::dnn::ConvolutionKind>() {
  return ::stream_executor::dnn::ConvolutionKind_descriptor();
}
template <> struct is_proto_enum< ::stream_executor::dnn::NormKind> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::stream_executor::dnn::NormKind>() {
  return ::stream_executor::dnn::NormKind_descriptor();
}
template <> struct is_proto_enum< ::stream_executor::dnn::FusedMHAKind> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::stream_executor::dnn::FusedMHAKind>() {
  return ::stream_executor::dnn::FusedMHAKind_descriptor();
}
template <> struct is_proto_enum< ::stream_executor::dnn::FMHAMaskKind> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::stream_executor::dnn::FMHAMaskKind>() {
  return ::stream_executor::dnn::FMHAMaskKind_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_xla_2ftsl_2fprotobuf_2fdnn_2eproto
