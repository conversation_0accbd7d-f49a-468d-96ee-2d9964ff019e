// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: xla/service/hlo_profile_printer_data.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_xla_2fservice_2fhlo_5fprofile_5fprinter_5fdata_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_xla_2fservice_2fhlo_5fprofile_5fprinter_5fdata_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_xla_2fservice_2fhlo_5fprofile_5fprinter_5fdata_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_xla_2fservice_2fhlo_5fprofile_5fprinter_5fdata_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_xla_2fservice_2fhlo_5fprofile_5fprinter_5fdata_2eproto;
namespace xla {
class HloProfilePrinterData;
struct HloProfilePrinterDataDefaultTypeInternal;
extern HloProfilePrinterDataDefaultTypeInternal _HloProfilePrinterData_default_instance_;
class HloProfilePrinterData_ExtraMetricsEntry_DoNotUse;
struct HloProfilePrinterData_ExtraMetricsEntry_DoNotUseDefaultTypeInternal;
extern HloProfilePrinterData_ExtraMetricsEntry_DoNotUseDefaultTypeInternal _HloProfilePrinterData_ExtraMetricsEntry_DoNotUse_default_instance_;
class HloProfilePrinterData_HloComputationInfo;
struct HloProfilePrinterData_HloComputationInfoDefaultTypeInternal;
extern HloProfilePrinterData_HloComputationInfoDefaultTypeInternal _HloProfilePrinterData_HloComputationInfo_default_instance_;
class HloProfilePrinterData_HloInstructionInfo;
struct HloProfilePrinterData_HloInstructionInfoDefaultTypeInternal;
extern HloProfilePrinterData_HloInstructionInfoDefaultTypeInternal _HloProfilePrinterData_HloInstructionInfo_default_instance_;
}  // namespace xla
PROTOBUF_NAMESPACE_OPEN
template<> ::xla::HloProfilePrinterData* Arena::CreateMaybeMessage<::xla::HloProfilePrinterData>(Arena*);
template<> ::xla::HloProfilePrinterData_ExtraMetricsEntry_DoNotUse* Arena::CreateMaybeMessage<::xla::HloProfilePrinterData_ExtraMetricsEntry_DoNotUse>(Arena*);
template<> ::xla::HloProfilePrinterData_HloComputationInfo* Arena::CreateMaybeMessage<::xla::HloProfilePrinterData_HloComputationInfo>(Arena*);
template<> ::xla::HloProfilePrinterData_HloInstructionInfo* Arena::CreateMaybeMessage<::xla::HloProfilePrinterData_HloInstructionInfo>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace xla {

// ===================================================================

class HloProfilePrinterData_HloInstructionInfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.HloProfilePrinterData.HloInstructionInfo) */ {
 public:
  inline HloProfilePrinterData_HloInstructionInfo() : HloProfilePrinterData_HloInstructionInfo(nullptr) {}
  ~HloProfilePrinterData_HloInstructionInfo() override;
  explicit PROTOBUF_CONSTEXPR HloProfilePrinterData_HloInstructionInfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  HloProfilePrinterData_HloInstructionInfo(const HloProfilePrinterData_HloInstructionInfo& from);
  HloProfilePrinterData_HloInstructionInfo(HloProfilePrinterData_HloInstructionInfo&& from) noexcept
    : HloProfilePrinterData_HloInstructionInfo() {
    *this = ::std::move(from);
  }

  inline HloProfilePrinterData_HloInstructionInfo& operator=(const HloProfilePrinterData_HloInstructionInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline HloProfilePrinterData_HloInstructionInfo& operator=(HloProfilePrinterData_HloInstructionInfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const HloProfilePrinterData_HloInstructionInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const HloProfilePrinterData_HloInstructionInfo* internal_default_instance() {
    return reinterpret_cast<const HloProfilePrinterData_HloInstructionInfo*>(
               &_HloProfilePrinterData_HloInstructionInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(HloProfilePrinterData_HloInstructionInfo& a, HloProfilePrinterData_HloInstructionInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(HloProfilePrinterData_HloInstructionInfo* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(HloProfilePrinterData_HloInstructionInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  HloProfilePrinterData_HloInstructionInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<HloProfilePrinterData_HloInstructionInfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const HloProfilePrinterData_HloInstructionInfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const HloProfilePrinterData_HloInstructionInfo& from) {
    HloProfilePrinterData_HloInstructionInfo::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(HloProfilePrinterData_HloInstructionInfo* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.HloProfilePrinterData.HloInstructionInfo";
  }
  protected:
  explicit HloProfilePrinterData_HloInstructionInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLongNameFieldNumber = 1,
    kShortNameFieldNumber = 2,
    kCategoryFieldNumber = 3,
    kFlopCountFieldNumber = 4,
    kTranscendentalCountFieldNumber = 5,
    kProfileIndexFieldNumber = 8,
    kBytesAccessedFieldNumber = 9,
    kOptimalSecondsFieldNumber = 7,
  };
  // string long_name = 1;
  void clear_long_name();
  const std::string& long_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_long_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_long_name();
  PROTOBUF_NODISCARD std::string* release_long_name();
  void set_allocated_long_name(std::string* long_name);
  private:
  const std::string& _internal_long_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_long_name(const std::string& value);
  std::string* _internal_mutable_long_name();
  public:

  // string short_name = 2;
  void clear_short_name();
  const std::string& short_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_short_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_short_name();
  PROTOBUF_NODISCARD std::string* release_short_name();
  void set_allocated_short_name(std::string* short_name);
  private:
  const std::string& _internal_short_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_short_name(const std::string& value);
  std::string* _internal_mutable_short_name();
  public:

  // string category = 3;
  void clear_category();
  const std::string& category() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_category(ArgT0&& arg0, ArgT... args);
  std::string* mutable_category();
  PROTOBUF_NODISCARD std::string* release_category();
  void set_allocated_category(std::string* category);
  private:
  const std::string& _internal_category() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_category(const std::string& value);
  std::string* _internal_mutable_category();
  public:

  // float flop_count = 4;
  void clear_flop_count();
  float flop_count() const;
  void set_flop_count(float value);
  private:
  float _internal_flop_count() const;
  void _internal_set_flop_count(float value);
  public:

  // float transcendental_count = 5;
  void clear_transcendental_count();
  float transcendental_count() const;
  void set_transcendental_count(float value);
  private:
  float _internal_transcendental_count() const;
  void _internal_set_transcendental_count(float value);
  public:

  // int64 profile_index = 8;
  void clear_profile_index();
  int64_t profile_index() const;
  void set_profile_index(int64_t value);
  private:
  int64_t _internal_profile_index() const;
  void _internal_set_profile_index(int64_t value);
  public:

  // int64 bytes_accessed = 9;
  void clear_bytes_accessed();
  int64_t bytes_accessed() const;
  void set_bytes_accessed(int64_t value);
  private:
  int64_t _internal_bytes_accessed() const;
  void _internal_set_bytes_accessed(int64_t value);
  public:

  // float optimal_seconds = 7;
  void clear_optimal_seconds();
  float optimal_seconds() const;
  void set_optimal_seconds(float value);
  private:
  float _internal_optimal_seconds() const;
  void _internal_set_optimal_seconds(float value);
  public:

  // @@protoc_insertion_point(class_scope:xla.HloProfilePrinterData.HloInstructionInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr long_name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr short_name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr category_;
    float flop_count_;
    float transcendental_count_;
    int64_t profile_index_;
    int64_t bytes_accessed_;
    float optimal_seconds_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fservice_2fhlo_5fprofile_5fprinter_5fdata_2eproto;
};
// -------------------------------------------------------------------

class HloProfilePrinterData_HloComputationInfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.HloProfilePrinterData.HloComputationInfo) */ {
 public:
  inline HloProfilePrinterData_HloComputationInfo() : HloProfilePrinterData_HloComputationInfo(nullptr) {}
  ~HloProfilePrinterData_HloComputationInfo() override;
  explicit PROTOBUF_CONSTEXPR HloProfilePrinterData_HloComputationInfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  HloProfilePrinterData_HloComputationInfo(const HloProfilePrinterData_HloComputationInfo& from);
  HloProfilePrinterData_HloComputationInfo(HloProfilePrinterData_HloComputationInfo&& from) noexcept
    : HloProfilePrinterData_HloComputationInfo() {
    *this = ::std::move(from);
  }

  inline HloProfilePrinterData_HloComputationInfo& operator=(const HloProfilePrinterData_HloComputationInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline HloProfilePrinterData_HloComputationInfo& operator=(HloProfilePrinterData_HloComputationInfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const HloProfilePrinterData_HloComputationInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const HloProfilePrinterData_HloComputationInfo* internal_default_instance() {
    return reinterpret_cast<const HloProfilePrinterData_HloComputationInfo*>(
               &_HloProfilePrinterData_HloComputationInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(HloProfilePrinterData_HloComputationInfo& a, HloProfilePrinterData_HloComputationInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(HloProfilePrinterData_HloComputationInfo* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(HloProfilePrinterData_HloComputationInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  HloProfilePrinterData_HloComputationInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<HloProfilePrinterData_HloComputationInfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const HloProfilePrinterData_HloComputationInfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const HloProfilePrinterData_HloComputationInfo& from) {
    HloProfilePrinterData_HloComputationInfo::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(HloProfilePrinterData_HloComputationInfo* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.HloProfilePrinterData.HloComputationInfo";
  }
  protected:
  explicit HloProfilePrinterData_HloComputationInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kInstructionInfosFieldNumber = 3,
    kNameFieldNumber = 1,
    kProfileIndexFieldNumber = 2,
  };
  // repeated .xla.HloProfilePrinterData.HloInstructionInfo instruction_infos = 3;
  int instruction_infos_size() const;
  private:
  int _internal_instruction_infos_size() const;
  public:
  void clear_instruction_infos();
  ::xla::HloProfilePrinterData_HloInstructionInfo* mutable_instruction_infos(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::HloProfilePrinterData_HloInstructionInfo >*
      mutable_instruction_infos();
  private:
  const ::xla::HloProfilePrinterData_HloInstructionInfo& _internal_instruction_infos(int index) const;
  ::xla::HloProfilePrinterData_HloInstructionInfo* _internal_add_instruction_infos();
  public:
  const ::xla::HloProfilePrinterData_HloInstructionInfo& instruction_infos(int index) const;
  ::xla::HloProfilePrinterData_HloInstructionInfo* add_instruction_infos();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::HloProfilePrinterData_HloInstructionInfo >&
      instruction_infos() const;

  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // int64 profile_index = 2;
  void clear_profile_index();
  int64_t profile_index() const;
  void set_profile_index(int64_t value);
  private:
  int64_t _internal_profile_index() const;
  void _internal_set_profile_index(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:xla.HloProfilePrinterData.HloComputationInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::HloProfilePrinterData_HloInstructionInfo > instruction_infos_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    int64_t profile_index_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fservice_2fhlo_5fprofile_5fprinter_5fdata_2eproto;
};
// -------------------------------------------------------------------

class HloProfilePrinterData_ExtraMetricsEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<HloProfilePrinterData_ExtraMetricsEntry_DoNotUse, 
    std::string, int64_t,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<HloProfilePrinterData_ExtraMetricsEntry_DoNotUse, 
    std::string, int64_t,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64> SuperType;
  HloProfilePrinterData_ExtraMetricsEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR HloProfilePrinterData_ExtraMetricsEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit HloProfilePrinterData_ExtraMetricsEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const HloProfilePrinterData_ExtraMetricsEntry_DoNotUse& other);
  static const HloProfilePrinterData_ExtraMetricsEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const HloProfilePrinterData_ExtraMetricsEntry_DoNotUse*>(&_HloProfilePrinterData_ExtraMetricsEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "xla.HloProfilePrinterData.ExtraMetricsEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_xla_2fservice_2fhlo_5fprofile_5fprinter_5fdata_2eproto;
};

// -------------------------------------------------------------------

class HloProfilePrinterData final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.HloProfilePrinterData) */ {
 public:
  inline HloProfilePrinterData() : HloProfilePrinterData(nullptr) {}
  ~HloProfilePrinterData() override;
  explicit PROTOBUF_CONSTEXPR HloProfilePrinterData(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  HloProfilePrinterData(const HloProfilePrinterData& from);
  HloProfilePrinterData(HloProfilePrinterData&& from) noexcept
    : HloProfilePrinterData() {
    *this = ::std::move(from);
  }

  inline HloProfilePrinterData& operator=(const HloProfilePrinterData& from) {
    CopyFrom(from);
    return *this;
  }
  inline HloProfilePrinterData& operator=(HloProfilePrinterData&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const HloProfilePrinterData& default_instance() {
    return *internal_default_instance();
  }
  static inline const HloProfilePrinterData* internal_default_instance() {
    return reinterpret_cast<const HloProfilePrinterData*>(
               &_HloProfilePrinterData_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(HloProfilePrinterData& a, HloProfilePrinterData& b) {
    a.Swap(&b);
  }
  inline void Swap(HloProfilePrinterData* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(HloProfilePrinterData* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  HloProfilePrinterData* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<HloProfilePrinterData>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const HloProfilePrinterData& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const HloProfilePrinterData& from) {
    HloProfilePrinterData::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(HloProfilePrinterData* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.HloProfilePrinterData";
  }
  protected:
  explicit HloProfilePrinterData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef HloProfilePrinterData_HloInstructionInfo HloInstructionInfo;
  typedef HloProfilePrinterData_HloComputationInfo HloComputationInfo;

  // accessors -------------------------------------------------------

  enum : int {
    kComputationInfosFieldNumber = 1,
    kExtraMetricsFieldNumber = 3,
    kEntryComputationFieldNumber = 4,
    kProfileCountersSizeFieldNumber = 2,
  };
  // repeated .xla.HloProfilePrinterData.HloComputationInfo computation_infos = 1;
  int computation_infos_size() const;
  private:
  int _internal_computation_infos_size() const;
  public:
  void clear_computation_infos();
  ::xla::HloProfilePrinterData_HloComputationInfo* mutable_computation_infos(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::HloProfilePrinterData_HloComputationInfo >*
      mutable_computation_infos();
  private:
  const ::xla::HloProfilePrinterData_HloComputationInfo& _internal_computation_infos(int index) const;
  ::xla::HloProfilePrinterData_HloComputationInfo* _internal_add_computation_infos();
  public:
  const ::xla::HloProfilePrinterData_HloComputationInfo& computation_infos(int index) const;
  ::xla::HloProfilePrinterData_HloComputationInfo* add_computation_infos();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::HloProfilePrinterData_HloComputationInfo >&
      computation_infos() const;

  // map<string, int64> extra_metrics = 3;
  int extra_metrics_size() const;
  private:
  int _internal_extra_metrics_size() const;
  public:
  void clear_extra_metrics();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, int64_t >&
      _internal_extra_metrics() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, int64_t >*
      _internal_mutable_extra_metrics();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, int64_t >&
      extra_metrics() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, int64_t >*
      mutable_extra_metrics();

  // string entry_computation = 4;
  void clear_entry_computation();
  const std::string& entry_computation() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_entry_computation(ArgT0&& arg0, ArgT... args);
  std::string* mutable_entry_computation();
  PROTOBUF_NODISCARD std::string* release_entry_computation();
  void set_allocated_entry_computation(std::string* entry_computation);
  private:
  const std::string& _internal_entry_computation() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_entry_computation(const std::string& value);
  std::string* _internal_mutable_entry_computation();
  public:

  // int64 profile_counters_size = 2;
  void clear_profile_counters_size();
  int64_t profile_counters_size() const;
  void set_profile_counters_size(int64_t value);
  private:
  int64_t _internal_profile_counters_size() const;
  void _internal_set_profile_counters_size(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:xla.HloProfilePrinterData)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::HloProfilePrinterData_HloComputationInfo > computation_infos_;
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        HloProfilePrinterData_ExtraMetricsEntry_DoNotUse,
        std::string, int64_t,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64> extra_metrics_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr entry_computation_;
    int64_t profile_counters_size_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fservice_2fhlo_5fprofile_5fprinter_5fdata_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// HloProfilePrinterData_HloInstructionInfo

// string long_name = 1;
inline void HloProfilePrinterData_HloInstructionInfo::clear_long_name() {
  _impl_.long_name_.ClearToEmpty();
}
inline const std::string& HloProfilePrinterData_HloInstructionInfo::long_name() const {
  // @@protoc_insertion_point(field_get:xla.HloProfilePrinterData.HloInstructionInfo.long_name)
  return _internal_long_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void HloProfilePrinterData_HloInstructionInfo::set_long_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.long_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:xla.HloProfilePrinterData.HloInstructionInfo.long_name)
}
inline std::string* HloProfilePrinterData_HloInstructionInfo::mutable_long_name() {
  std::string* _s = _internal_mutable_long_name();
  // @@protoc_insertion_point(field_mutable:xla.HloProfilePrinterData.HloInstructionInfo.long_name)
  return _s;
}
inline const std::string& HloProfilePrinterData_HloInstructionInfo::_internal_long_name() const {
  return _impl_.long_name_.Get();
}
inline void HloProfilePrinterData_HloInstructionInfo::_internal_set_long_name(const std::string& value) {
  
  _impl_.long_name_.Set(value, GetArenaForAllocation());
}
inline std::string* HloProfilePrinterData_HloInstructionInfo::_internal_mutable_long_name() {
  
  return _impl_.long_name_.Mutable(GetArenaForAllocation());
}
inline std::string* HloProfilePrinterData_HloInstructionInfo::release_long_name() {
  // @@protoc_insertion_point(field_release:xla.HloProfilePrinterData.HloInstructionInfo.long_name)
  return _impl_.long_name_.Release();
}
inline void HloProfilePrinterData_HloInstructionInfo::set_allocated_long_name(std::string* long_name) {
  if (long_name != nullptr) {
    
  } else {
    
  }
  _impl_.long_name_.SetAllocated(long_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.long_name_.IsDefault()) {
    _impl_.long_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:xla.HloProfilePrinterData.HloInstructionInfo.long_name)
}

// string short_name = 2;
inline void HloProfilePrinterData_HloInstructionInfo::clear_short_name() {
  _impl_.short_name_.ClearToEmpty();
}
inline const std::string& HloProfilePrinterData_HloInstructionInfo::short_name() const {
  // @@protoc_insertion_point(field_get:xla.HloProfilePrinterData.HloInstructionInfo.short_name)
  return _internal_short_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void HloProfilePrinterData_HloInstructionInfo::set_short_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.short_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:xla.HloProfilePrinterData.HloInstructionInfo.short_name)
}
inline std::string* HloProfilePrinterData_HloInstructionInfo::mutable_short_name() {
  std::string* _s = _internal_mutable_short_name();
  // @@protoc_insertion_point(field_mutable:xla.HloProfilePrinterData.HloInstructionInfo.short_name)
  return _s;
}
inline const std::string& HloProfilePrinterData_HloInstructionInfo::_internal_short_name() const {
  return _impl_.short_name_.Get();
}
inline void HloProfilePrinterData_HloInstructionInfo::_internal_set_short_name(const std::string& value) {
  
  _impl_.short_name_.Set(value, GetArenaForAllocation());
}
inline std::string* HloProfilePrinterData_HloInstructionInfo::_internal_mutable_short_name() {
  
  return _impl_.short_name_.Mutable(GetArenaForAllocation());
}
inline std::string* HloProfilePrinterData_HloInstructionInfo::release_short_name() {
  // @@protoc_insertion_point(field_release:xla.HloProfilePrinterData.HloInstructionInfo.short_name)
  return _impl_.short_name_.Release();
}
inline void HloProfilePrinterData_HloInstructionInfo::set_allocated_short_name(std::string* short_name) {
  if (short_name != nullptr) {
    
  } else {
    
  }
  _impl_.short_name_.SetAllocated(short_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.short_name_.IsDefault()) {
    _impl_.short_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:xla.HloProfilePrinterData.HloInstructionInfo.short_name)
}

// string category = 3;
inline void HloProfilePrinterData_HloInstructionInfo::clear_category() {
  _impl_.category_.ClearToEmpty();
}
inline const std::string& HloProfilePrinterData_HloInstructionInfo::category() const {
  // @@protoc_insertion_point(field_get:xla.HloProfilePrinterData.HloInstructionInfo.category)
  return _internal_category();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void HloProfilePrinterData_HloInstructionInfo::set_category(ArgT0&& arg0, ArgT... args) {
 
 _impl_.category_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:xla.HloProfilePrinterData.HloInstructionInfo.category)
}
inline std::string* HloProfilePrinterData_HloInstructionInfo::mutable_category() {
  std::string* _s = _internal_mutable_category();
  // @@protoc_insertion_point(field_mutable:xla.HloProfilePrinterData.HloInstructionInfo.category)
  return _s;
}
inline const std::string& HloProfilePrinterData_HloInstructionInfo::_internal_category() const {
  return _impl_.category_.Get();
}
inline void HloProfilePrinterData_HloInstructionInfo::_internal_set_category(const std::string& value) {
  
  _impl_.category_.Set(value, GetArenaForAllocation());
}
inline std::string* HloProfilePrinterData_HloInstructionInfo::_internal_mutable_category() {
  
  return _impl_.category_.Mutable(GetArenaForAllocation());
}
inline std::string* HloProfilePrinterData_HloInstructionInfo::release_category() {
  // @@protoc_insertion_point(field_release:xla.HloProfilePrinterData.HloInstructionInfo.category)
  return _impl_.category_.Release();
}
inline void HloProfilePrinterData_HloInstructionInfo::set_allocated_category(std::string* category) {
  if (category != nullptr) {
    
  } else {
    
  }
  _impl_.category_.SetAllocated(category, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.category_.IsDefault()) {
    _impl_.category_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:xla.HloProfilePrinterData.HloInstructionInfo.category)
}

// float flop_count = 4;
inline void HloProfilePrinterData_HloInstructionInfo::clear_flop_count() {
  _impl_.flop_count_ = 0;
}
inline float HloProfilePrinterData_HloInstructionInfo::_internal_flop_count() const {
  return _impl_.flop_count_;
}
inline float HloProfilePrinterData_HloInstructionInfo::flop_count() const {
  // @@protoc_insertion_point(field_get:xla.HloProfilePrinterData.HloInstructionInfo.flop_count)
  return _internal_flop_count();
}
inline void HloProfilePrinterData_HloInstructionInfo::_internal_set_flop_count(float value) {
  
  _impl_.flop_count_ = value;
}
inline void HloProfilePrinterData_HloInstructionInfo::set_flop_count(float value) {
  _internal_set_flop_count(value);
  // @@protoc_insertion_point(field_set:xla.HloProfilePrinterData.HloInstructionInfo.flop_count)
}

// float transcendental_count = 5;
inline void HloProfilePrinterData_HloInstructionInfo::clear_transcendental_count() {
  _impl_.transcendental_count_ = 0;
}
inline float HloProfilePrinterData_HloInstructionInfo::_internal_transcendental_count() const {
  return _impl_.transcendental_count_;
}
inline float HloProfilePrinterData_HloInstructionInfo::transcendental_count() const {
  // @@protoc_insertion_point(field_get:xla.HloProfilePrinterData.HloInstructionInfo.transcendental_count)
  return _internal_transcendental_count();
}
inline void HloProfilePrinterData_HloInstructionInfo::_internal_set_transcendental_count(float value) {
  
  _impl_.transcendental_count_ = value;
}
inline void HloProfilePrinterData_HloInstructionInfo::set_transcendental_count(float value) {
  _internal_set_transcendental_count(value);
  // @@protoc_insertion_point(field_set:xla.HloProfilePrinterData.HloInstructionInfo.transcendental_count)
}

// int64 bytes_accessed = 9;
inline void HloProfilePrinterData_HloInstructionInfo::clear_bytes_accessed() {
  _impl_.bytes_accessed_ = int64_t{0};
}
inline int64_t HloProfilePrinterData_HloInstructionInfo::_internal_bytes_accessed() const {
  return _impl_.bytes_accessed_;
}
inline int64_t HloProfilePrinterData_HloInstructionInfo::bytes_accessed() const {
  // @@protoc_insertion_point(field_get:xla.HloProfilePrinterData.HloInstructionInfo.bytes_accessed)
  return _internal_bytes_accessed();
}
inline void HloProfilePrinterData_HloInstructionInfo::_internal_set_bytes_accessed(int64_t value) {
  
  _impl_.bytes_accessed_ = value;
}
inline void HloProfilePrinterData_HloInstructionInfo::set_bytes_accessed(int64_t value) {
  _internal_set_bytes_accessed(value);
  // @@protoc_insertion_point(field_set:xla.HloProfilePrinterData.HloInstructionInfo.bytes_accessed)
}

// float optimal_seconds = 7;
inline void HloProfilePrinterData_HloInstructionInfo::clear_optimal_seconds() {
  _impl_.optimal_seconds_ = 0;
}
inline float HloProfilePrinterData_HloInstructionInfo::_internal_optimal_seconds() const {
  return _impl_.optimal_seconds_;
}
inline float HloProfilePrinterData_HloInstructionInfo::optimal_seconds() const {
  // @@protoc_insertion_point(field_get:xla.HloProfilePrinterData.HloInstructionInfo.optimal_seconds)
  return _internal_optimal_seconds();
}
inline void HloProfilePrinterData_HloInstructionInfo::_internal_set_optimal_seconds(float value) {
  
  _impl_.optimal_seconds_ = value;
}
inline void HloProfilePrinterData_HloInstructionInfo::set_optimal_seconds(float value) {
  _internal_set_optimal_seconds(value);
  // @@protoc_insertion_point(field_set:xla.HloProfilePrinterData.HloInstructionInfo.optimal_seconds)
}

// int64 profile_index = 8;
inline void HloProfilePrinterData_HloInstructionInfo::clear_profile_index() {
  _impl_.profile_index_ = int64_t{0};
}
inline int64_t HloProfilePrinterData_HloInstructionInfo::_internal_profile_index() const {
  return _impl_.profile_index_;
}
inline int64_t HloProfilePrinterData_HloInstructionInfo::profile_index() const {
  // @@protoc_insertion_point(field_get:xla.HloProfilePrinterData.HloInstructionInfo.profile_index)
  return _internal_profile_index();
}
inline void HloProfilePrinterData_HloInstructionInfo::_internal_set_profile_index(int64_t value) {
  
  _impl_.profile_index_ = value;
}
inline void HloProfilePrinterData_HloInstructionInfo::set_profile_index(int64_t value) {
  _internal_set_profile_index(value);
  // @@protoc_insertion_point(field_set:xla.HloProfilePrinterData.HloInstructionInfo.profile_index)
}

// -------------------------------------------------------------------

// HloProfilePrinterData_HloComputationInfo

// string name = 1;
inline void HloProfilePrinterData_HloComputationInfo::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& HloProfilePrinterData_HloComputationInfo::name() const {
  // @@protoc_insertion_point(field_get:xla.HloProfilePrinterData.HloComputationInfo.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void HloProfilePrinterData_HloComputationInfo::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:xla.HloProfilePrinterData.HloComputationInfo.name)
}
inline std::string* HloProfilePrinterData_HloComputationInfo::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:xla.HloProfilePrinterData.HloComputationInfo.name)
  return _s;
}
inline const std::string& HloProfilePrinterData_HloComputationInfo::_internal_name() const {
  return _impl_.name_.Get();
}
inline void HloProfilePrinterData_HloComputationInfo::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* HloProfilePrinterData_HloComputationInfo::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* HloProfilePrinterData_HloComputationInfo::release_name() {
  // @@protoc_insertion_point(field_release:xla.HloProfilePrinterData.HloComputationInfo.name)
  return _impl_.name_.Release();
}
inline void HloProfilePrinterData_HloComputationInfo::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:xla.HloProfilePrinterData.HloComputationInfo.name)
}

// int64 profile_index = 2;
inline void HloProfilePrinterData_HloComputationInfo::clear_profile_index() {
  _impl_.profile_index_ = int64_t{0};
}
inline int64_t HloProfilePrinterData_HloComputationInfo::_internal_profile_index() const {
  return _impl_.profile_index_;
}
inline int64_t HloProfilePrinterData_HloComputationInfo::profile_index() const {
  // @@protoc_insertion_point(field_get:xla.HloProfilePrinterData.HloComputationInfo.profile_index)
  return _internal_profile_index();
}
inline void HloProfilePrinterData_HloComputationInfo::_internal_set_profile_index(int64_t value) {
  
  _impl_.profile_index_ = value;
}
inline void HloProfilePrinterData_HloComputationInfo::set_profile_index(int64_t value) {
  _internal_set_profile_index(value);
  // @@protoc_insertion_point(field_set:xla.HloProfilePrinterData.HloComputationInfo.profile_index)
}

// repeated .xla.HloProfilePrinterData.HloInstructionInfo instruction_infos = 3;
inline int HloProfilePrinterData_HloComputationInfo::_internal_instruction_infos_size() const {
  return _impl_.instruction_infos_.size();
}
inline int HloProfilePrinterData_HloComputationInfo::instruction_infos_size() const {
  return _internal_instruction_infos_size();
}
inline void HloProfilePrinterData_HloComputationInfo::clear_instruction_infos() {
  _impl_.instruction_infos_.Clear();
}
inline ::xla::HloProfilePrinterData_HloInstructionInfo* HloProfilePrinterData_HloComputationInfo::mutable_instruction_infos(int index) {
  // @@protoc_insertion_point(field_mutable:xla.HloProfilePrinterData.HloComputationInfo.instruction_infos)
  return _impl_.instruction_infos_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::HloProfilePrinterData_HloInstructionInfo >*
HloProfilePrinterData_HloComputationInfo::mutable_instruction_infos() {
  // @@protoc_insertion_point(field_mutable_list:xla.HloProfilePrinterData.HloComputationInfo.instruction_infos)
  return &_impl_.instruction_infos_;
}
inline const ::xla::HloProfilePrinterData_HloInstructionInfo& HloProfilePrinterData_HloComputationInfo::_internal_instruction_infos(int index) const {
  return _impl_.instruction_infos_.Get(index);
}
inline const ::xla::HloProfilePrinterData_HloInstructionInfo& HloProfilePrinterData_HloComputationInfo::instruction_infos(int index) const {
  // @@protoc_insertion_point(field_get:xla.HloProfilePrinterData.HloComputationInfo.instruction_infos)
  return _internal_instruction_infos(index);
}
inline ::xla::HloProfilePrinterData_HloInstructionInfo* HloProfilePrinterData_HloComputationInfo::_internal_add_instruction_infos() {
  return _impl_.instruction_infos_.Add();
}
inline ::xla::HloProfilePrinterData_HloInstructionInfo* HloProfilePrinterData_HloComputationInfo::add_instruction_infos() {
  ::xla::HloProfilePrinterData_HloInstructionInfo* _add = _internal_add_instruction_infos();
  // @@protoc_insertion_point(field_add:xla.HloProfilePrinterData.HloComputationInfo.instruction_infos)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::HloProfilePrinterData_HloInstructionInfo >&
HloProfilePrinterData_HloComputationInfo::instruction_infos() const {
  // @@protoc_insertion_point(field_list:xla.HloProfilePrinterData.HloComputationInfo.instruction_infos)
  return _impl_.instruction_infos_;
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// HloProfilePrinterData

// repeated .xla.HloProfilePrinterData.HloComputationInfo computation_infos = 1;
inline int HloProfilePrinterData::_internal_computation_infos_size() const {
  return _impl_.computation_infos_.size();
}
inline int HloProfilePrinterData::computation_infos_size() const {
  return _internal_computation_infos_size();
}
inline void HloProfilePrinterData::clear_computation_infos() {
  _impl_.computation_infos_.Clear();
}
inline ::xla::HloProfilePrinterData_HloComputationInfo* HloProfilePrinterData::mutable_computation_infos(int index) {
  // @@protoc_insertion_point(field_mutable:xla.HloProfilePrinterData.computation_infos)
  return _impl_.computation_infos_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::HloProfilePrinterData_HloComputationInfo >*
HloProfilePrinterData::mutable_computation_infos() {
  // @@protoc_insertion_point(field_mutable_list:xla.HloProfilePrinterData.computation_infos)
  return &_impl_.computation_infos_;
}
inline const ::xla::HloProfilePrinterData_HloComputationInfo& HloProfilePrinterData::_internal_computation_infos(int index) const {
  return _impl_.computation_infos_.Get(index);
}
inline const ::xla::HloProfilePrinterData_HloComputationInfo& HloProfilePrinterData::computation_infos(int index) const {
  // @@protoc_insertion_point(field_get:xla.HloProfilePrinterData.computation_infos)
  return _internal_computation_infos(index);
}
inline ::xla::HloProfilePrinterData_HloComputationInfo* HloProfilePrinterData::_internal_add_computation_infos() {
  return _impl_.computation_infos_.Add();
}
inline ::xla::HloProfilePrinterData_HloComputationInfo* HloProfilePrinterData::add_computation_infos() {
  ::xla::HloProfilePrinterData_HloComputationInfo* _add = _internal_add_computation_infos();
  // @@protoc_insertion_point(field_add:xla.HloProfilePrinterData.computation_infos)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::HloProfilePrinterData_HloComputationInfo >&
HloProfilePrinterData::computation_infos() const {
  // @@protoc_insertion_point(field_list:xla.HloProfilePrinterData.computation_infos)
  return _impl_.computation_infos_;
}

// int64 profile_counters_size = 2;
inline void HloProfilePrinterData::clear_profile_counters_size() {
  _impl_.profile_counters_size_ = int64_t{0};
}
inline int64_t HloProfilePrinterData::_internal_profile_counters_size() const {
  return _impl_.profile_counters_size_;
}
inline int64_t HloProfilePrinterData::profile_counters_size() const {
  // @@protoc_insertion_point(field_get:xla.HloProfilePrinterData.profile_counters_size)
  return _internal_profile_counters_size();
}
inline void HloProfilePrinterData::_internal_set_profile_counters_size(int64_t value) {
  
  _impl_.profile_counters_size_ = value;
}
inline void HloProfilePrinterData::set_profile_counters_size(int64_t value) {
  _internal_set_profile_counters_size(value);
  // @@protoc_insertion_point(field_set:xla.HloProfilePrinterData.profile_counters_size)
}

// map<string, int64> extra_metrics = 3;
inline int HloProfilePrinterData::_internal_extra_metrics_size() const {
  return _impl_.extra_metrics_.size();
}
inline int HloProfilePrinterData::extra_metrics_size() const {
  return _internal_extra_metrics_size();
}
inline void HloProfilePrinterData::clear_extra_metrics() {
  _impl_.extra_metrics_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, int64_t >&
HloProfilePrinterData::_internal_extra_metrics() const {
  return _impl_.extra_metrics_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, int64_t >&
HloProfilePrinterData::extra_metrics() const {
  // @@protoc_insertion_point(field_map:xla.HloProfilePrinterData.extra_metrics)
  return _internal_extra_metrics();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, int64_t >*
HloProfilePrinterData::_internal_mutable_extra_metrics() {
  return _impl_.extra_metrics_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, int64_t >*
HloProfilePrinterData::mutable_extra_metrics() {
  // @@protoc_insertion_point(field_mutable_map:xla.HloProfilePrinterData.extra_metrics)
  return _internal_mutable_extra_metrics();
}

// string entry_computation = 4;
inline void HloProfilePrinterData::clear_entry_computation() {
  _impl_.entry_computation_.ClearToEmpty();
}
inline const std::string& HloProfilePrinterData::entry_computation() const {
  // @@protoc_insertion_point(field_get:xla.HloProfilePrinterData.entry_computation)
  return _internal_entry_computation();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void HloProfilePrinterData::set_entry_computation(ArgT0&& arg0, ArgT... args) {
 
 _impl_.entry_computation_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:xla.HloProfilePrinterData.entry_computation)
}
inline std::string* HloProfilePrinterData::mutable_entry_computation() {
  std::string* _s = _internal_mutable_entry_computation();
  // @@protoc_insertion_point(field_mutable:xla.HloProfilePrinterData.entry_computation)
  return _s;
}
inline const std::string& HloProfilePrinterData::_internal_entry_computation() const {
  return _impl_.entry_computation_.Get();
}
inline void HloProfilePrinterData::_internal_set_entry_computation(const std::string& value) {
  
  _impl_.entry_computation_.Set(value, GetArenaForAllocation());
}
inline std::string* HloProfilePrinterData::_internal_mutable_entry_computation() {
  
  return _impl_.entry_computation_.Mutable(GetArenaForAllocation());
}
inline std::string* HloProfilePrinterData::release_entry_computation() {
  // @@protoc_insertion_point(field_release:xla.HloProfilePrinterData.entry_computation)
  return _impl_.entry_computation_.Release();
}
inline void HloProfilePrinterData::set_allocated_entry_computation(std::string* entry_computation) {
  if (entry_computation != nullptr) {
    
  } else {
    
  }
  _impl_.entry_computation_.SetAllocated(entry_computation, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.entry_computation_.IsDefault()) {
    _impl_.entry_computation_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:xla.HloProfilePrinterData.entry_computation)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace xla

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_xla_2fservice_2fhlo_5fprofile_5fprinter_5fdata_2eproto
