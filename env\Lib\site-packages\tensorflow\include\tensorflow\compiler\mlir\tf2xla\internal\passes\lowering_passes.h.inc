/* Autogenerated by mlir-tblgen; don't manually edit */

#ifdef GEN_PASS_DECL
// Generate declarations for all passes.
#define GEN_PASS_DECL_INPUTLOWERINGMETRICSPASS
#undef GEN_PASS_DECL
#endif // GEN_PASS_DECL

//===----------------------------------------------------------------------===//
// InputLoweringMetricsPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_INPUTLOWERINGMETRICSPASS
#undef GEN_PASS_DECL_INPUTLOWERINGMETRICSPASS
#endif // GEN_PA<PERSON>_DECL_INPUTLOWERINGMETRICSPASS
#ifdef GEN_<PERSON>SS_DEF_INPUTLOWERINGMETRICSPASS
namespace impl {

template <typename DerivedT>
class InputLoweringMetricsPassBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = InputLoweringMetricsPassBase;

  InputLoweringMetricsPassBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  InputLoweringMetricsPassBase(const InputLoweringMetricsPassBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}
  InputLoweringMetricsPassBase& operator=(const InputLoweringMetricsPassBase &) = delete;
  InputLoweringMetricsPassBase(InputLoweringMetricsPassBase &&) = delete;
  InputLoweringMetricsPassBase& operator=(InputLoweringMetricsPassBase &&) = delete;
  ~InputLoweringMetricsPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("input-lowering-metrics-pass");
  }
  ::llvm::StringRef getArgument() const override { return "input-lowering-metrics-pass"; }

  ::llvm::StringRef getDescription() const override { return "Collects various metrics about the input to the lowering portion of the bridge. This is a logical no-op."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("InputLoweringMetricsPass");
  }
  ::llvm::StringRef getName() const override { return "InputLoweringMetricsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(InputLoweringMetricsPassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_INPUTLOWERINGMETRICSPASS
#endif // GEN_PASS_DEF_INPUTLOWERINGMETRICSPASS
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// InputLoweringMetricsPass Registration
//===----------------------------------------------------------------------===//

inline void registerInputLoweringMetricsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return tensorflow::tf2xla::internal::CreateInputLoweringMetricsPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerInputLoweringMetricsPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return tensorflow::tf2xla::internal::CreateInputLoweringMetricsPass();
  });
}

//===----------------------------------------------------------------------===//
// TFXLABridgeLowering Registration
//===----------------------------------------------------------------------===//

inline void registerTFXLABridgeLoweringPasses() {
  registerInputLoweringMetricsPass();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
// Deprecated. Please use the new per-pass macros.
#ifdef GEN_PASS_CLASSES

template <typename DerivedT>
class InputLoweringMetricsPassBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = InputLoweringMetricsPassBase;

  InputLoweringMetricsPassBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  InputLoweringMetricsPassBase(const InputLoweringMetricsPassBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}
  InputLoweringMetricsPassBase& operator=(const InputLoweringMetricsPassBase &) = delete;
  InputLoweringMetricsPassBase(InputLoweringMetricsPassBase &&) = delete;
  InputLoweringMetricsPassBase& operator=(InputLoweringMetricsPassBase &&) = delete;
  ~InputLoweringMetricsPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("input-lowering-metrics-pass");
  }
  ::llvm::StringRef getArgument() const override { return "input-lowering-metrics-pass"; }

  ::llvm::StringRef getDescription() const override { return "Collects various metrics about the input to the lowering portion of the bridge. This is a logical no-op."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("InputLoweringMetricsPass");
  }
  ::llvm::StringRef getName() const override { return "InputLoweringMetricsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(InputLoweringMetricsPassBase<DerivedT>)

protected:
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
