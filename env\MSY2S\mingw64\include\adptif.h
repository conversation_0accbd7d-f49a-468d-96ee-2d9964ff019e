/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */
#ifndef _IPX_ADAPTER_
#define _IPX_ADAPTER_

typedef struct _ADDRESS_RESERVED {
  UCHAR Reserved[28];
} ADDRESS_RESERVED,*PADDRESS_RESERVED;

HANDLE WINAPI CreateSocketPort(USHORT Socket);
DWORD WINAPI DeleteSocketPort(HANDLE Handle);
DWORD WINAPI IpxRecvPacket(<PERSON>AN<PERSON><PERSON> Handle,PUCHAR IpxPacket,ULONG IpxPacketLength,PADDRESS_RESERVED lpReserved,LPOVERLAPPED lpOverlapped,LPOVERLAPPED_COMPLETION_ROUTINE CompletionRoutine);
DWORD WINAPI IpxSendPacket(<PERSON><PERSON><PERSON><PERSON> Handle,ULONG AdapterIdx,<PERSON>UC<PERSON><PERSON> IpxPacket,ULONG IpxPacketLength,<PERSON>DDRESS_RESERVED lpReserved,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> lpOverlapped,LPOVERLAPPED_COMPLETION_ROUTINE CompletionRoutine);

#define GetNicIdx(pReserved) ((ULONG)*((USHORT *)(pReserved+2)))

#endif
