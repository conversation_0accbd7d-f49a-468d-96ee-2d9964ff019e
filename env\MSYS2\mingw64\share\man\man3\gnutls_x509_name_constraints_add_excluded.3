.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_name_constraints_add_excluded" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_name_constraints_add_excluded \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_name_constraints_add_excluded(gnutls_x509_name_constraints_t " nc ", gnutls_x509_subject_alt_name_t " type ", const gnutls_datum_t * " name ");"
.SH ARGUMENTS
.IP "gnutls_x509_name_constraints_t nc" 12
The nameconstraints
.IP "gnutls_x509_subject_alt_name_t type" 12
The type of the constraints
.IP "const gnutls_datum_t * name" 12
The data of the constraints
.SH "DESCRIPTION"
This function will add a name constraint to the list of excluded
constraints. The constraints  \fItype\fP can be any of the following types:
\fBGNUTLS_SAN_DNSNAME\fP, \fBGNUTLS_SAN_RFC822NAME\fP, \fBGNUTLS_SAN_DN\fP,
\fBGNUTLS_SAN_URI\fP, \fBGNUTLS_SAN_IPADDRESS\fP. For the latter, an IP address
in network byte order is expected, followed by its network mask (which is
4 bytes in IPv4 or 16\-bytes in IPv6).
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a negative error value.
.SH "SINCE"
3.3.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
