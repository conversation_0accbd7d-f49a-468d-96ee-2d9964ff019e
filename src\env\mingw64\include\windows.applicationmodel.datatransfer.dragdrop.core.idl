/*
 * Copyright (C) 2024 <PERSON><PERSON><PERSON> for CodeWeavers
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

#ifdef __WIDL__
#pragma winrt ns_prefix
#endif

#ifndef DO_NO_IMPORTS
import "inspectable.idl";
import "asyncinfo.idl";
import "eventtoken.idl";
import "windowscontracts.idl";
import "windows.foundation.idl";
import "windows.applicationmodel.datatransfer.idl";
import "windows.applicationmodel.datatransfer.dragdrop.idl";
import "windows.graphics.imaging.idl";
#endif

namespace Windows.ApplicationModel.DataTransfer.DragDrop.Core {
    interface ICoreDragDropManager;
    interface ICoreDragDropManagerStatics;
    interface ICoreDragInfo;
    interface ICoreDragInfo2;
    interface ICoreDropOperationTarget;
    interface ICoreDropOperationTargetRequestedEventArgs;
    interface ICoreDragUIOverride;

    runtimeclass CoreDragDropManager;
    runtimeclass CoreDragInfo;
    runtimeclass CoreDropOperationTargetRequestedEventArgs;
    runtimeclass CoreDragUIOverride;

    declare {
        interface Windows.Foundation.TypedEventHandler<Windows.ApplicationModel.DataTransfer.DragDrop.Core.CoreDragDropManager *, Windows.ApplicationModel.DataTransfer.DragDrop.Core.CoreDropOperationTargetRequestedEventArgs *>;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.ApplicationModel.DataTransfer.DragDrop.Core.CoreDragDropManager),
        uuid(7d56d344-8464-4faf-aa49-37ea6e2d7bd1)
    ]
    interface ICoreDragDropManager : IInspectable
    {
        [eventadd] HRESULT TargetRequested([in] Windows.Foundation.TypedEventHandler<Windows.ApplicationModel.DataTransfer.DragDrop.Core.CoreDragDropManager *, Windows.ApplicationModel.DataTransfer.DragDrop.Core.CoreDropOperationTargetRequestedEventArgs *> *value, [out, retval] EventRegistrationToken *return_value);
        [eventremove] HRESULT TargetRequested([in] EventRegistrationToken value);
        [propget] HRESULT AreConcurrentOperationsEnabled([out, retval] boolean *value);
        [propput] HRESULT AreConcurrentOperationsEnabled([in] boolean value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.ApplicationModel.DataTransfer.DragDrop.Core.CoreDragDropManager),
        uuid(9542fdca-da12-4c1c-8d06-041db29733c3)
    ]
    interface ICoreDragDropManagerStatics : IInspectable
    {
        HRESULT GetForCurrentView([out, retval] Windows.ApplicationModel.DataTransfer.DragDrop.Core.CoreDragDropManager **value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.ApplicationModel.DataTransfer.DragDrop.Core.CoreDragInfo),
        uuid(48353a8b-cb50-464e-9575-cd4e3a7ab028)
    ]
    interface ICoreDragInfo : IInspectable
    {
        [propget] HRESULT Data([out, retval] Windows.ApplicationModel.DataTransfer.DataPackageView **value);
        [propget] HRESULT Modifiers([out, retval] Windows.ApplicationModel.DataTransfer.DragDrop.DragDropModifiers *value);
        [propget] HRESULT Position([out, retval] Windows.Foundation.Point *value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        exclusiveto(Windows.ApplicationModel.DataTransfer.DragDrop.Core.CoreDragInfo),
        uuid(c54691e5-e6fb-4d74-b4b1-8a3c17f25e9e)
    ]
    interface ICoreDragInfo2 : IInspectable
        requires Windows.ApplicationModel.DataTransfer.DragDrop.Core.ICoreDragInfo
    {
        [propget] HRESULT AllowedOperations([out, retval] Windows.ApplicationModel.DataTransfer.DataPackageOperation *value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        uuid(d9126196-4c5b-417d-bb37-76381def8db4)
    ]
    interface ICoreDropOperationTarget : IInspectable
    {
        HRESULT EnterAsync([in] Windows.ApplicationModel.DataTransfer.DragDrop.Core.CoreDragInfo *info, [in] Windows.ApplicationModel.DataTransfer.DragDrop.Core.CoreDragUIOverride *override, [out, retval] Windows.Foundation.IAsyncOperation<Windows.ApplicationModel.DataTransfer.DataPackageOperation> **return_value);
        HRESULT OverAsync([in] Windows.ApplicationModel.DataTransfer.DragDrop.Core.CoreDragInfo *info, [in] Windows.ApplicationModel.DataTransfer.DragDrop.Core.CoreDragUIOverride *override, [out, retval] Windows.Foundation.IAsyncOperation<Windows.ApplicationModel.DataTransfer.DataPackageOperation> **return_value);
        HRESULT LeaveAsync([in] Windows.ApplicationModel.DataTransfer.DragDrop.Core.CoreDragInfo *info, [out, retval] Windows.Foundation.IAsyncAction **return_value);
        HRESULT DropAsync([in] Windows.ApplicationModel.DataTransfer.DragDrop.Core.CoreDragInfo *info, [out, retval] Windows.Foundation.IAsyncOperation<Windows.ApplicationModel.DataTransfer.DataPackageOperation> **return_value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.ApplicationModel.DataTransfer.DragDrop.Core.CoreDropOperationTargetRequestedEventArgs),
        uuid(2aca929a-5e28-4ea6-829e-29134e665d6d)
    ]
    interface ICoreDropOperationTargetRequestedEventArgs : IInspectable
    {
        HRESULT SetTarget([in] Windows.ApplicationModel.DataTransfer.DragDrop.Core.ICoreDropOperationTarget *target);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.ApplicationModel.DataTransfer.DragDrop.Core.CoreDragUIOverride),
        uuid(89a85064-3389-4f4f-8897-7e8a3ffb3c93)
    ]
    interface ICoreDragUIOverride : IInspectable
    {
        [overload("SetContentFromSoftwareBitmap")] HRESULT SetContentFromSoftwareBitmap([in] Windows.Graphics.Imaging.SoftwareBitmap *bitmap);
        [overload("SetContentFromSoftwareBitmap")] HRESULT SetContentFromSoftwareBitmapWithAnchorPoint([in] Windows.Graphics.Imaging.SoftwareBitmap *bitmap, [in] Windows.Foundation.Point point);
        [propget] HRESULT IsContentVisible([out, retval] boolean *value);
        [propput] HRESULT IsContentVisible([in] boolean value);
        [propget] HRESULT Caption([out, retval] HSTRING *value);
        [propput] HRESULT Caption([in] HSTRING value);
        [propget] HRESULT IsCaptionVisible([out, retval] boolean *value);
        [propput] HRESULT IsCaptionVisible([in] boolean value);
        [propget] HRESULT IsGlyphVisible([out, retval] boolean *value);
        [propput] HRESULT IsGlyphVisible([in] boolean value);
        HRESULT Clear();
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(agile),
        static(Windows.ApplicationModel.DataTransfer.DragDrop.Core.ICoreDragDropManagerStatics, Windows.Foundation.UniversalApiContract, 1.0),
        threading(both)
    ]
    runtimeclass CoreDragDropManager
    {
        [default] interface Windows.ApplicationModel.DataTransfer.DragDrop.Core.ICoreDragDropManager;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(agile),
        threading(both)
    ]
    runtimeclass CoreDragInfo
    {
        [default] interface Windows.ApplicationModel.DataTransfer.DragDrop.Core.ICoreDragInfo;
        [contract(Windows.Foundation.UniversalApiContract, 2.0)] interface Windows.ApplicationModel.DataTransfer.DragDrop.Core.ICoreDragInfo2;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(agile),
        threading(both)
    ]
    runtimeclass CoreDropOperationTargetRequestedEventArgs
    {
        [default] interface Windows.ApplicationModel.DataTransfer.DragDrop.Core.ICoreDropOperationTargetRequestedEventArgs;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(agile),
        threading(both)
    ]
    runtimeclass CoreDragUIOverride
    {
        [default] interface Windows.ApplicationModel.DataTransfer.DragDrop.Core.ICoreDragUIOverride;
    }
}
