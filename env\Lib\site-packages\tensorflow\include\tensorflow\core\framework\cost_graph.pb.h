// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/framework/cost_graph.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/tensor_shape.pb.h"
#include "tensorflow/core/framework/types.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto;
namespace tensorflow {
class CostGraphDef;
struct CostGraphDefDefaultTypeInternal;
extern CostGraphDefDefaultTypeInternal _CostGraphDef_default_instance_;
class CostGraphDef_AggregatedCost;
struct CostGraphDef_AggregatedCostDefaultTypeInternal;
extern CostGraphDef_AggregatedCostDefaultTypeInternal _CostGraphDef_AggregatedCost_default_instance_;
class CostGraphDef_Node;
struct CostGraphDef_NodeDefaultTypeInternal;
extern CostGraphDef_NodeDefaultTypeInternal _CostGraphDef_Node_default_instance_;
class CostGraphDef_Node_InputInfo;
struct CostGraphDef_Node_InputInfoDefaultTypeInternal;
extern CostGraphDef_Node_InputInfoDefaultTypeInternal _CostGraphDef_Node_InputInfo_default_instance_;
class CostGraphDef_Node_OutputInfo;
struct CostGraphDef_Node_OutputInfoDefaultTypeInternal;
extern CostGraphDef_Node_OutputInfoDefaultTypeInternal _CostGraphDef_Node_OutputInfo_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::CostGraphDef* Arena::CreateMaybeMessage<::tensorflow::CostGraphDef>(Arena*);
template<> ::tensorflow::CostGraphDef_AggregatedCost* Arena::CreateMaybeMessage<::tensorflow::CostGraphDef_AggregatedCost>(Arena*);
template<> ::tensorflow::CostGraphDef_Node* Arena::CreateMaybeMessage<::tensorflow::CostGraphDef_Node>(Arena*);
template<> ::tensorflow::CostGraphDef_Node_InputInfo* Arena::CreateMaybeMessage<::tensorflow::CostGraphDef_Node_InputInfo>(Arena*);
template<> ::tensorflow::CostGraphDef_Node_OutputInfo* Arena::CreateMaybeMessage<::tensorflow::CostGraphDef_Node_OutputInfo>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

// ===================================================================

class CostGraphDef_Node_InputInfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.CostGraphDef.Node.InputInfo) */ {
 public:
  inline CostGraphDef_Node_InputInfo() : CostGraphDef_Node_InputInfo(nullptr) {}
  ~CostGraphDef_Node_InputInfo() override;
  explicit PROTOBUF_CONSTEXPR CostGraphDef_Node_InputInfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CostGraphDef_Node_InputInfo(const CostGraphDef_Node_InputInfo& from);
  CostGraphDef_Node_InputInfo(CostGraphDef_Node_InputInfo&& from) noexcept
    : CostGraphDef_Node_InputInfo() {
    *this = ::std::move(from);
  }

  inline CostGraphDef_Node_InputInfo& operator=(const CostGraphDef_Node_InputInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline CostGraphDef_Node_InputInfo& operator=(CostGraphDef_Node_InputInfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CostGraphDef_Node_InputInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const CostGraphDef_Node_InputInfo* internal_default_instance() {
    return reinterpret_cast<const CostGraphDef_Node_InputInfo*>(
               &_CostGraphDef_Node_InputInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(CostGraphDef_Node_InputInfo& a, CostGraphDef_Node_InputInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(CostGraphDef_Node_InputInfo* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CostGraphDef_Node_InputInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CostGraphDef_Node_InputInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CostGraphDef_Node_InputInfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CostGraphDef_Node_InputInfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CostGraphDef_Node_InputInfo& from) {
    CostGraphDef_Node_InputInfo::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CostGraphDef_Node_InputInfo* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CostGraphDef.Node.InputInfo";
  }
  protected:
  explicit CostGraphDef_Node_InputInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPrecedingNodeFieldNumber = 1,
    kPrecedingPortFieldNumber = 2,
  };
  // int32 preceding_node = 1;
  void clear_preceding_node();
  int32_t preceding_node() const;
  void set_preceding_node(int32_t value);
  private:
  int32_t _internal_preceding_node() const;
  void _internal_set_preceding_node(int32_t value);
  public:

  // int32 preceding_port = 2;
  void clear_preceding_port();
  int32_t preceding_port() const;
  void set_preceding_port(int32_t value);
  private:
  int32_t _internal_preceding_port() const;
  void _internal_set_preceding_port(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.CostGraphDef.Node.InputInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int32_t preceding_node_;
    int32_t preceding_port_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto;
};
// -------------------------------------------------------------------

class CostGraphDef_Node_OutputInfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.CostGraphDef.Node.OutputInfo) */ {
 public:
  inline CostGraphDef_Node_OutputInfo() : CostGraphDef_Node_OutputInfo(nullptr) {}
  ~CostGraphDef_Node_OutputInfo() override;
  explicit PROTOBUF_CONSTEXPR CostGraphDef_Node_OutputInfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CostGraphDef_Node_OutputInfo(const CostGraphDef_Node_OutputInfo& from);
  CostGraphDef_Node_OutputInfo(CostGraphDef_Node_OutputInfo&& from) noexcept
    : CostGraphDef_Node_OutputInfo() {
    *this = ::std::move(from);
  }

  inline CostGraphDef_Node_OutputInfo& operator=(const CostGraphDef_Node_OutputInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline CostGraphDef_Node_OutputInfo& operator=(CostGraphDef_Node_OutputInfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CostGraphDef_Node_OutputInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const CostGraphDef_Node_OutputInfo* internal_default_instance() {
    return reinterpret_cast<const CostGraphDef_Node_OutputInfo*>(
               &_CostGraphDef_Node_OutputInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(CostGraphDef_Node_OutputInfo& a, CostGraphDef_Node_OutputInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(CostGraphDef_Node_OutputInfo* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CostGraphDef_Node_OutputInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CostGraphDef_Node_OutputInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CostGraphDef_Node_OutputInfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CostGraphDef_Node_OutputInfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CostGraphDef_Node_OutputInfo& from) {
    CostGraphDef_Node_OutputInfo::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CostGraphDef_Node_OutputInfo* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CostGraphDef.Node.OutputInfo";
  }
  protected:
  explicit CostGraphDef_Node_OutputInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kShapeFieldNumber = 3,
    kSizeFieldNumber = 1,
    kAliasInputPortFieldNumber = 2,
    kDtypeFieldNumber = 4,
  };
  // .tensorflow.TensorShapeProto shape = 3;
  bool has_shape() const;
  private:
  bool _internal_has_shape() const;
  public:
  void clear_shape();
  const ::tensorflow::TensorShapeProto& shape() const;
  PROTOBUF_NODISCARD ::tensorflow::TensorShapeProto* release_shape();
  ::tensorflow::TensorShapeProto* mutable_shape();
  void set_allocated_shape(::tensorflow::TensorShapeProto* shape);
  private:
  const ::tensorflow::TensorShapeProto& _internal_shape() const;
  ::tensorflow::TensorShapeProto* _internal_mutable_shape();
  public:
  void unsafe_arena_set_allocated_shape(
      ::tensorflow::TensorShapeProto* shape);
  ::tensorflow::TensorShapeProto* unsafe_arena_release_shape();

  // int64 size = 1;
  void clear_size();
  int64_t size() const;
  void set_size(int64_t value);
  private:
  int64_t _internal_size() const;
  void _internal_set_size(int64_t value);
  public:

  // int64 alias_input_port = 2;
  void clear_alias_input_port();
  int64_t alias_input_port() const;
  void set_alias_input_port(int64_t value);
  private:
  int64_t _internal_alias_input_port() const;
  void _internal_set_alias_input_port(int64_t value);
  public:

  // .tensorflow.DataType dtype = 4;
  void clear_dtype();
  ::tensorflow::DataType dtype() const;
  void set_dtype(::tensorflow::DataType value);
  private:
  ::tensorflow::DataType _internal_dtype() const;
  void _internal_set_dtype(::tensorflow::DataType value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.CostGraphDef.Node.OutputInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::tensorflow::TensorShapeProto* shape_;
    int64_t size_;
    int64_t alias_input_port_;
    int dtype_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto;
};
// -------------------------------------------------------------------

class CostGraphDef_Node final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.CostGraphDef.Node) */ {
 public:
  inline CostGraphDef_Node() : CostGraphDef_Node(nullptr) {}
  ~CostGraphDef_Node() override;
  explicit PROTOBUF_CONSTEXPR CostGraphDef_Node(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CostGraphDef_Node(const CostGraphDef_Node& from);
  CostGraphDef_Node(CostGraphDef_Node&& from) noexcept
    : CostGraphDef_Node() {
    *this = ::std::move(from);
  }

  inline CostGraphDef_Node& operator=(const CostGraphDef_Node& from) {
    CopyFrom(from);
    return *this;
  }
  inline CostGraphDef_Node& operator=(CostGraphDef_Node&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CostGraphDef_Node& default_instance() {
    return *internal_default_instance();
  }
  static inline const CostGraphDef_Node* internal_default_instance() {
    return reinterpret_cast<const CostGraphDef_Node*>(
               &_CostGraphDef_Node_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(CostGraphDef_Node& a, CostGraphDef_Node& b) {
    a.Swap(&b);
  }
  inline void Swap(CostGraphDef_Node* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CostGraphDef_Node* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CostGraphDef_Node* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CostGraphDef_Node>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CostGraphDef_Node& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CostGraphDef_Node& from) {
    CostGraphDef_Node::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CostGraphDef_Node* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CostGraphDef.Node";
  }
  protected:
  explicit CostGraphDef_Node(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef CostGraphDef_Node_InputInfo InputInfo;
  typedef CostGraphDef_Node_OutputInfo OutputInfo;

  // accessors -------------------------------------------------------

  enum : int {
    kInputInfoFieldNumber = 4,
    kOutputInfoFieldNumber = 5,
    kControlInputFieldNumber = 8,
    kNameFieldNumber = 1,
    kDeviceFieldNumber = 2,
    kTemporaryMemorySizeFieldNumber = 6,
    kComputeCostFieldNumber = 9,
    kIdFieldNumber = 3,
    kIsFinalFieldNumber = 7,
    kInaccurateFieldNumber = 17,
    kHostTempMemorySizeFieldNumber = 10,
    kDeviceTempMemorySizeFieldNumber = 11,
    kPersistentMemorySizeFieldNumber = 12,
    kComputeTimeFieldNumber = 14,
    kMemoryTimeFieldNumber = 15,
    kDevicePersistentMemorySizeFieldNumber = 16,
  };
  // repeated .tensorflow.CostGraphDef.Node.InputInfo input_info = 4;
  int input_info_size() const;
  private:
  int _internal_input_info_size() const;
  public:
  void clear_input_info();
  ::tensorflow::CostGraphDef_Node_InputInfo* mutable_input_info(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CostGraphDef_Node_InputInfo >*
      mutable_input_info();
  private:
  const ::tensorflow::CostGraphDef_Node_InputInfo& _internal_input_info(int index) const;
  ::tensorflow::CostGraphDef_Node_InputInfo* _internal_add_input_info();
  public:
  const ::tensorflow::CostGraphDef_Node_InputInfo& input_info(int index) const;
  ::tensorflow::CostGraphDef_Node_InputInfo* add_input_info();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CostGraphDef_Node_InputInfo >&
      input_info() const;

  // repeated .tensorflow.CostGraphDef.Node.OutputInfo output_info = 5;
  int output_info_size() const;
  private:
  int _internal_output_info_size() const;
  public:
  void clear_output_info();
  ::tensorflow::CostGraphDef_Node_OutputInfo* mutable_output_info(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CostGraphDef_Node_OutputInfo >*
      mutable_output_info();
  private:
  const ::tensorflow::CostGraphDef_Node_OutputInfo& _internal_output_info(int index) const;
  ::tensorflow::CostGraphDef_Node_OutputInfo* _internal_add_output_info();
  public:
  const ::tensorflow::CostGraphDef_Node_OutputInfo& output_info(int index) const;
  ::tensorflow::CostGraphDef_Node_OutputInfo* add_output_info();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CostGraphDef_Node_OutputInfo >&
      output_info() const;

  // repeated int32 control_input = 8;
  int control_input_size() const;
  private:
  int _internal_control_input_size() const;
  public:
  void clear_control_input();
  private:
  int32_t _internal_control_input(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
      _internal_control_input() const;
  void _internal_add_control_input(int32_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
      _internal_mutable_control_input();
  public:
  int32_t control_input(int index) const;
  void set_control_input(int index, int32_t value);
  void add_control_input(int32_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
      control_input() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
      mutable_control_input();

  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // string device = 2;
  void clear_device();
  const std::string& device() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_device(ArgT0&& arg0, ArgT... args);
  std::string* mutable_device();
  PROTOBUF_NODISCARD std::string* release_device();
  void set_allocated_device(std::string* device);
  private:
  const std::string& _internal_device() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_device(const std::string& value);
  std::string* _internal_mutable_device();
  public:

  // int64 temporary_memory_size = 6;
  void clear_temporary_memory_size();
  int64_t temporary_memory_size() const;
  void set_temporary_memory_size(int64_t value);
  private:
  int64_t _internal_temporary_memory_size() const;
  void _internal_set_temporary_memory_size(int64_t value);
  public:

  // int64 compute_cost = 9;
  void clear_compute_cost();
  int64_t compute_cost() const;
  void set_compute_cost(int64_t value);
  private:
  int64_t _internal_compute_cost() const;
  void _internal_set_compute_cost(int64_t value);
  public:

  // int32 id = 3;
  void clear_id();
  int32_t id() const;
  void set_id(int32_t value);
  private:
  int32_t _internal_id() const;
  void _internal_set_id(int32_t value);
  public:

  // bool is_final = 7;
  void clear_is_final();
  bool is_final() const;
  void set_is_final(bool value);
  private:
  bool _internal_is_final() const;
  void _internal_set_is_final(bool value);
  public:

  // bool inaccurate = 17;
  void clear_inaccurate();
  bool inaccurate() const;
  void set_inaccurate(bool value);
  private:
  bool _internal_inaccurate() const;
  void _internal_set_inaccurate(bool value);
  public:

  // int64 host_temp_memory_size = 10 [deprecated = true];
  PROTOBUF_DEPRECATED void clear_host_temp_memory_size();
  PROTOBUF_DEPRECATED int64_t host_temp_memory_size() const;
  PROTOBUF_DEPRECATED void set_host_temp_memory_size(int64_t value);
  private:
  int64_t _internal_host_temp_memory_size() const;
  void _internal_set_host_temp_memory_size(int64_t value);
  public:

  // int64 device_temp_memory_size = 11 [deprecated = true];
  PROTOBUF_DEPRECATED void clear_device_temp_memory_size();
  PROTOBUF_DEPRECATED int64_t device_temp_memory_size() const;
  PROTOBUF_DEPRECATED void set_device_temp_memory_size(int64_t value);
  private:
  int64_t _internal_device_temp_memory_size() const;
  void _internal_set_device_temp_memory_size(int64_t value);
  public:

  // int64 persistent_memory_size = 12;
  void clear_persistent_memory_size();
  int64_t persistent_memory_size() const;
  void set_persistent_memory_size(int64_t value);
  private:
  int64_t _internal_persistent_memory_size() const;
  void _internal_set_persistent_memory_size(int64_t value);
  public:

  // int64 compute_time = 14;
  void clear_compute_time();
  int64_t compute_time() const;
  void set_compute_time(int64_t value);
  private:
  int64_t _internal_compute_time() const;
  void _internal_set_compute_time(int64_t value);
  public:

  // int64 memory_time = 15;
  void clear_memory_time();
  int64_t memory_time() const;
  void set_memory_time(int64_t value);
  private:
  int64_t _internal_memory_time() const;
  void _internal_set_memory_time(int64_t value);
  public:

  // int64 device_persistent_memory_size = 16 [deprecated = true];
  PROTOBUF_DEPRECATED void clear_device_persistent_memory_size();
  PROTOBUF_DEPRECATED int64_t device_persistent_memory_size() const;
  PROTOBUF_DEPRECATED void set_device_persistent_memory_size(int64_t value);
  private:
  int64_t _internal_device_persistent_memory_size() const;
  void _internal_set_device_persistent_memory_size(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.CostGraphDef.Node)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CostGraphDef_Node_InputInfo > input_info_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CostGraphDef_Node_OutputInfo > output_info_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t > control_input_;
    mutable std::atomic<int> _control_input_cached_byte_size_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr device_;
    int64_t temporary_memory_size_;
    int64_t compute_cost_;
    int32_t id_;
    bool is_final_;
    bool inaccurate_;
    int64_t host_temp_memory_size_;
    int64_t device_temp_memory_size_;
    int64_t persistent_memory_size_;
    int64_t compute_time_;
    int64_t memory_time_;
    int64_t device_persistent_memory_size_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto;
};
// -------------------------------------------------------------------

class CostGraphDef_AggregatedCost final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.CostGraphDef.AggregatedCost) */ {
 public:
  inline CostGraphDef_AggregatedCost() : CostGraphDef_AggregatedCost(nullptr) {}
  ~CostGraphDef_AggregatedCost() override;
  explicit PROTOBUF_CONSTEXPR CostGraphDef_AggregatedCost(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CostGraphDef_AggregatedCost(const CostGraphDef_AggregatedCost& from);
  CostGraphDef_AggregatedCost(CostGraphDef_AggregatedCost&& from) noexcept
    : CostGraphDef_AggregatedCost() {
    *this = ::std::move(from);
  }

  inline CostGraphDef_AggregatedCost& operator=(const CostGraphDef_AggregatedCost& from) {
    CopyFrom(from);
    return *this;
  }
  inline CostGraphDef_AggregatedCost& operator=(CostGraphDef_AggregatedCost&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CostGraphDef_AggregatedCost& default_instance() {
    return *internal_default_instance();
  }
  static inline const CostGraphDef_AggregatedCost* internal_default_instance() {
    return reinterpret_cast<const CostGraphDef_AggregatedCost*>(
               &_CostGraphDef_AggregatedCost_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(CostGraphDef_AggregatedCost& a, CostGraphDef_AggregatedCost& b) {
    a.Swap(&b);
  }
  inline void Swap(CostGraphDef_AggregatedCost* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CostGraphDef_AggregatedCost* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CostGraphDef_AggregatedCost* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CostGraphDef_AggregatedCost>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CostGraphDef_AggregatedCost& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CostGraphDef_AggregatedCost& from) {
    CostGraphDef_AggregatedCost::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CostGraphDef_AggregatedCost* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CostGraphDef.AggregatedCost";
  }
  protected:
  explicit CostGraphDef_AggregatedCost(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDimensionFieldNumber = 2,
    kCostFieldNumber = 1,
  };
  // string dimension = 2;
  void clear_dimension();
  const std::string& dimension() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_dimension(ArgT0&& arg0, ArgT... args);
  std::string* mutable_dimension();
  PROTOBUF_NODISCARD std::string* release_dimension();
  void set_allocated_dimension(std::string* dimension);
  private:
  const std::string& _internal_dimension() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_dimension(const std::string& value);
  std::string* _internal_mutable_dimension();
  public:

  // float cost = 1;
  void clear_cost();
  float cost() const;
  void set_cost(float value);
  private:
  float _internal_cost() const;
  void _internal_set_cost(float value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.CostGraphDef.AggregatedCost)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr dimension_;
    float cost_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto;
};
// -------------------------------------------------------------------

class CostGraphDef final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.CostGraphDef) */ {
 public:
  inline CostGraphDef() : CostGraphDef(nullptr) {}
  ~CostGraphDef() override;
  explicit PROTOBUF_CONSTEXPR CostGraphDef(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CostGraphDef(const CostGraphDef& from);
  CostGraphDef(CostGraphDef&& from) noexcept
    : CostGraphDef() {
    *this = ::std::move(from);
  }

  inline CostGraphDef& operator=(const CostGraphDef& from) {
    CopyFrom(from);
    return *this;
  }
  inline CostGraphDef& operator=(CostGraphDef&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CostGraphDef& default_instance() {
    return *internal_default_instance();
  }
  static inline const CostGraphDef* internal_default_instance() {
    return reinterpret_cast<const CostGraphDef*>(
               &_CostGraphDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(CostGraphDef& a, CostGraphDef& b) {
    a.Swap(&b);
  }
  inline void Swap(CostGraphDef* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CostGraphDef* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CostGraphDef* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CostGraphDef>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CostGraphDef& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CostGraphDef& from) {
    CostGraphDef::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CostGraphDef* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CostGraphDef";
  }
  protected:
  explicit CostGraphDef(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef CostGraphDef_Node Node;
  typedef CostGraphDef_AggregatedCost AggregatedCost;

  // accessors -------------------------------------------------------

  enum : int {
    kNodeFieldNumber = 1,
    kCostFieldNumber = 2,
  };
  // repeated .tensorflow.CostGraphDef.Node node = 1;
  int node_size() const;
  private:
  int _internal_node_size() const;
  public:
  void clear_node();
  ::tensorflow::CostGraphDef_Node* mutable_node(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CostGraphDef_Node >*
      mutable_node();
  private:
  const ::tensorflow::CostGraphDef_Node& _internal_node(int index) const;
  ::tensorflow::CostGraphDef_Node* _internal_add_node();
  public:
  const ::tensorflow::CostGraphDef_Node& node(int index) const;
  ::tensorflow::CostGraphDef_Node* add_node();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CostGraphDef_Node >&
      node() const;

  // repeated .tensorflow.CostGraphDef.AggregatedCost cost = 2;
  int cost_size() const;
  private:
  int _internal_cost_size() const;
  public:
  void clear_cost();
  ::tensorflow::CostGraphDef_AggregatedCost* mutable_cost(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CostGraphDef_AggregatedCost >*
      mutable_cost();
  private:
  const ::tensorflow::CostGraphDef_AggregatedCost& _internal_cost(int index) const;
  ::tensorflow::CostGraphDef_AggregatedCost* _internal_add_cost();
  public:
  const ::tensorflow::CostGraphDef_AggregatedCost& cost(int index) const;
  ::tensorflow::CostGraphDef_AggregatedCost* add_cost();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CostGraphDef_AggregatedCost >&
      cost() const;

  // @@protoc_insertion_point(class_scope:tensorflow.CostGraphDef)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CostGraphDef_Node > node_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CostGraphDef_AggregatedCost > cost_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// CostGraphDef_Node_InputInfo

// int32 preceding_node = 1;
inline void CostGraphDef_Node_InputInfo::clear_preceding_node() {
  _impl_.preceding_node_ = 0;
}
inline int32_t CostGraphDef_Node_InputInfo::_internal_preceding_node() const {
  return _impl_.preceding_node_;
}
inline int32_t CostGraphDef_Node_InputInfo::preceding_node() const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.InputInfo.preceding_node)
  return _internal_preceding_node();
}
inline void CostGraphDef_Node_InputInfo::_internal_set_preceding_node(int32_t value) {
  
  _impl_.preceding_node_ = value;
}
inline void CostGraphDef_Node_InputInfo::set_preceding_node(int32_t value) {
  _internal_set_preceding_node(value);
  // @@protoc_insertion_point(field_set:tensorflow.CostGraphDef.Node.InputInfo.preceding_node)
}

// int32 preceding_port = 2;
inline void CostGraphDef_Node_InputInfo::clear_preceding_port() {
  _impl_.preceding_port_ = 0;
}
inline int32_t CostGraphDef_Node_InputInfo::_internal_preceding_port() const {
  return _impl_.preceding_port_;
}
inline int32_t CostGraphDef_Node_InputInfo::preceding_port() const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.InputInfo.preceding_port)
  return _internal_preceding_port();
}
inline void CostGraphDef_Node_InputInfo::_internal_set_preceding_port(int32_t value) {
  
  _impl_.preceding_port_ = value;
}
inline void CostGraphDef_Node_InputInfo::set_preceding_port(int32_t value) {
  _internal_set_preceding_port(value);
  // @@protoc_insertion_point(field_set:tensorflow.CostGraphDef.Node.InputInfo.preceding_port)
}

// -------------------------------------------------------------------

// CostGraphDef_Node_OutputInfo

// int64 size = 1;
inline void CostGraphDef_Node_OutputInfo::clear_size() {
  _impl_.size_ = int64_t{0};
}
inline int64_t CostGraphDef_Node_OutputInfo::_internal_size() const {
  return _impl_.size_;
}
inline int64_t CostGraphDef_Node_OutputInfo::size() const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.OutputInfo.size)
  return _internal_size();
}
inline void CostGraphDef_Node_OutputInfo::_internal_set_size(int64_t value) {
  
  _impl_.size_ = value;
}
inline void CostGraphDef_Node_OutputInfo::set_size(int64_t value) {
  _internal_set_size(value);
  // @@protoc_insertion_point(field_set:tensorflow.CostGraphDef.Node.OutputInfo.size)
}

// int64 alias_input_port = 2;
inline void CostGraphDef_Node_OutputInfo::clear_alias_input_port() {
  _impl_.alias_input_port_ = int64_t{0};
}
inline int64_t CostGraphDef_Node_OutputInfo::_internal_alias_input_port() const {
  return _impl_.alias_input_port_;
}
inline int64_t CostGraphDef_Node_OutputInfo::alias_input_port() const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.OutputInfo.alias_input_port)
  return _internal_alias_input_port();
}
inline void CostGraphDef_Node_OutputInfo::_internal_set_alias_input_port(int64_t value) {
  
  _impl_.alias_input_port_ = value;
}
inline void CostGraphDef_Node_OutputInfo::set_alias_input_port(int64_t value) {
  _internal_set_alias_input_port(value);
  // @@protoc_insertion_point(field_set:tensorflow.CostGraphDef.Node.OutputInfo.alias_input_port)
}

// .tensorflow.TensorShapeProto shape = 3;
inline bool CostGraphDef_Node_OutputInfo::_internal_has_shape() const {
  return this != internal_default_instance() && _impl_.shape_ != nullptr;
}
inline bool CostGraphDef_Node_OutputInfo::has_shape() const {
  return _internal_has_shape();
}
inline const ::tensorflow::TensorShapeProto& CostGraphDef_Node_OutputInfo::_internal_shape() const {
  const ::tensorflow::TensorShapeProto* p = _impl_.shape_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::TensorShapeProto&>(
      ::tensorflow::_TensorShapeProto_default_instance_);
}
inline const ::tensorflow::TensorShapeProto& CostGraphDef_Node_OutputInfo::shape() const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.OutputInfo.shape)
  return _internal_shape();
}
inline void CostGraphDef_Node_OutputInfo::unsafe_arena_set_allocated_shape(
    ::tensorflow::TensorShapeProto* shape) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.shape_);
  }
  _impl_.shape_ = shape;
  if (shape) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CostGraphDef.Node.OutputInfo.shape)
}
inline ::tensorflow::TensorShapeProto* CostGraphDef_Node_OutputInfo::release_shape() {
  
  ::tensorflow::TensorShapeProto* temp = _impl_.shape_;
  _impl_.shape_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::TensorShapeProto* CostGraphDef_Node_OutputInfo::unsafe_arena_release_shape() {
  // @@protoc_insertion_point(field_release:tensorflow.CostGraphDef.Node.OutputInfo.shape)
  
  ::tensorflow::TensorShapeProto* temp = _impl_.shape_;
  _impl_.shape_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorShapeProto* CostGraphDef_Node_OutputInfo::_internal_mutable_shape() {
  
  if (_impl_.shape_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorShapeProto>(GetArenaForAllocation());
    _impl_.shape_ = p;
  }
  return _impl_.shape_;
}
inline ::tensorflow::TensorShapeProto* CostGraphDef_Node_OutputInfo::mutable_shape() {
  ::tensorflow::TensorShapeProto* _msg = _internal_mutable_shape();
  // @@protoc_insertion_point(field_mutable:tensorflow.CostGraphDef.Node.OutputInfo.shape)
  return _msg;
}
inline void CostGraphDef_Node_OutputInfo::set_allocated_shape(::tensorflow::TensorShapeProto* shape) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.shape_);
  }
  if (shape) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(shape));
    if (message_arena != submessage_arena) {
      shape = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, shape, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.shape_ = shape;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CostGraphDef.Node.OutputInfo.shape)
}

// .tensorflow.DataType dtype = 4;
inline void CostGraphDef_Node_OutputInfo::clear_dtype() {
  _impl_.dtype_ = 0;
}
inline ::tensorflow::DataType CostGraphDef_Node_OutputInfo::_internal_dtype() const {
  return static_cast< ::tensorflow::DataType >(_impl_.dtype_);
}
inline ::tensorflow::DataType CostGraphDef_Node_OutputInfo::dtype() const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.OutputInfo.dtype)
  return _internal_dtype();
}
inline void CostGraphDef_Node_OutputInfo::_internal_set_dtype(::tensorflow::DataType value) {
  
  _impl_.dtype_ = value;
}
inline void CostGraphDef_Node_OutputInfo::set_dtype(::tensorflow::DataType value) {
  _internal_set_dtype(value);
  // @@protoc_insertion_point(field_set:tensorflow.CostGraphDef.Node.OutputInfo.dtype)
}

// -------------------------------------------------------------------

// CostGraphDef_Node

// string name = 1;
inline void CostGraphDef_Node::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& CostGraphDef_Node::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CostGraphDef_Node::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.CostGraphDef.Node.name)
}
inline std::string* CostGraphDef_Node::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.CostGraphDef.Node.name)
  return _s;
}
inline const std::string& CostGraphDef_Node::_internal_name() const {
  return _impl_.name_.Get();
}
inline void CostGraphDef_Node::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* CostGraphDef_Node::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* CostGraphDef_Node::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.CostGraphDef.Node.name)
  return _impl_.name_.Release();
}
inline void CostGraphDef_Node::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CostGraphDef.Node.name)
}

// string device = 2;
inline void CostGraphDef_Node::clear_device() {
  _impl_.device_.ClearToEmpty();
}
inline const std::string& CostGraphDef_Node::device() const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.device)
  return _internal_device();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CostGraphDef_Node::set_device(ArgT0&& arg0, ArgT... args) {
 
 _impl_.device_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.CostGraphDef.Node.device)
}
inline std::string* CostGraphDef_Node::mutable_device() {
  std::string* _s = _internal_mutable_device();
  // @@protoc_insertion_point(field_mutable:tensorflow.CostGraphDef.Node.device)
  return _s;
}
inline const std::string& CostGraphDef_Node::_internal_device() const {
  return _impl_.device_.Get();
}
inline void CostGraphDef_Node::_internal_set_device(const std::string& value) {
  
  _impl_.device_.Set(value, GetArenaForAllocation());
}
inline std::string* CostGraphDef_Node::_internal_mutable_device() {
  
  return _impl_.device_.Mutable(GetArenaForAllocation());
}
inline std::string* CostGraphDef_Node::release_device() {
  // @@protoc_insertion_point(field_release:tensorflow.CostGraphDef.Node.device)
  return _impl_.device_.Release();
}
inline void CostGraphDef_Node::set_allocated_device(std::string* device) {
  if (device != nullptr) {
    
  } else {
    
  }
  _impl_.device_.SetAllocated(device, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.device_.IsDefault()) {
    _impl_.device_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CostGraphDef.Node.device)
}

// int32 id = 3;
inline void CostGraphDef_Node::clear_id() {
  _impl_.id_ = 0;
}
inline int32_t CostGraphDef_Node::_internal_id() const {
  return _impl_.id_;
}
inline int32_t CostGraphDef_Node::id() const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.id)
  return _internal_id();
}
inline void CostGraphDef_Node::_internal_set_id(int32_t value) {
  
  _impl_.id_ = value;
}
inline void CostGraphDef_Node::set_id(int32_t value) {
  _internal_set_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.CostGraphDef.Node.id)
}

// repeated .tensorflow.CostGraphDef.Node.InputInfo input_info = 4;
inline int CostGraphDef_Node::_internal_input_info_size() const {
  return _impl_.input_info_.size();
}
inline int CostGraphDef_Node::input_info_size() const {
  return _internal_input_info_size();
}
inline void CostGraphDef_Node::clear_input_info() {
  _impl_.input_info_.Clear();
}
inline ::tensorflow::CostGraphDef_Node_InputInfo* CostGraphDef_Node::mutable_input_info(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.CostGraphDef.Node.input_info)
  return _impl_.input_info_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CostGraphDef_Node_InputInfo >*
CostGraphDef_Node::mutable_input_info() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.CostGraphDef.Node.input_info)
  return &_impl_.input_info_;
}
inline const ::tensorflow::CostGraphDef_Node_InputInfo& CostGraphDef_Node::_internal_input_info(int index) const {
  return _impl_.input_info_.Get(index);
}
inline const ::tensorflow::CostGraphDef_Node_InputInfo& CostGraphDef_Node::input_info(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.input_info)
  return _internal_input_info(index);
}
inline ::tensorflow::CostGraphDef_Node_InputInfo* CostGraphDef_Node::_internal_add_input_info() {
  return _impl_.input_info_.Add();
}
inline ::tensorflow::CostGraphDef_Node_InputInfo* CostGraphDef_Node::add_input_info() {
  ::tensorflow::CostGraphDef_Node_InputInfo* _add = _internal_add_input_info();
  // @@protoc_insertion_point(field_add:tensorflow.CostGraphDef.Node.input_info)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CostGraphDef_Node_InputInfo >&
CostGraphDef_Node::input_info() const {
  // @@protoc_insertion_point(field_list:tensorflow.CostGraphDef.Node.input_info)
  return _impl_.input_info_;
}

// repeated .tensorflow.CostGraphDef.Node.OutputInfo output_info = 5;
inline int CostGraphDef_Node::_internal_output_info_size() const {
  return _impl_.output_info_.size();
}
inline int CostGraphDef_Node::output_info_size() const {
  return _internal_output_info_size();
}
inline void CostGraphDef_Node::clear_output_info() {
  _impl_.output_info_.Clear();
}
inline ::tensorflow::CostGraphDef_Node_OutputInfo* CostGraphDef_Node::mutable_output_info(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.CostGraphDef.Node.output_info)
  return _impl_.output_info_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CostGraphDef_Node_OutputInfo >*
CostGraphDef_Node::mutable_output_info() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.CostGraphDef.Node.output_info)
  return &_impl_.output_info_;
}
inline const ::tensorflow::CostGraphDef_Node_OutputInfo& CostGraphDef_Node::_internal_output_info(int index) const {
  return _impl_.output_info_.Get(index);
}
inline const ::tensorflow::CostGraphDef_Node_OutputInfo& CostGraphDef_Node::output_info(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.output_info)
  return _internal_output_info(index);
}
inline ::tensorflow::CostGraphDef_Node_OutputInfo* CostGraphDef_Node::_internal_add_output_info() {
  return _impl_.output_info_.Add();
}
inline ::tensorflow::CostGraphDef_Node_OutputInfo* CostGraphDef_Node::add_output_info() {
  ::tensorflow::CostGraphDef_Node_OutputInfo* _add = _internal_add_output_info();
  // @@protoc_insertion_point(field_add:tensorflow.CostGraphDef.Node.output_info)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CostGraphDef_Node_OutputInfo >&
CostGraphDef_Node::output_info() const {
  // @@protoc_insertion_point(field_list:tensorflow.CostGraphDef.Node.output_info)
  return _impl_.output_info_;
}

// int64 temporary_memory_size = 6;
inline void CostGraphDef_Node::clear_temporary_memory_size() {
  _impl_.temporary_memory_size_ = int64_t{0};
}
inline int64_t CostGraphDef_Node::_internal_temporary_memory_size() const {
  return _impl_.temporary_memory_size_;
}
inline int64_t CostGraphDef_Node::temporary_memory_size() const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.temporary_memory_size)
  return _internal_temporary_memory_size();
}
inline void CostGraphDef_Node::_internal_set_temporary_memory_size(int64_t value) {
  
  _impl_.temporary_memory_size_ = value;
}
inline void CostGraphDef_Node::set_temporary_memory_size(int64_t value) {
  _internal_set_temporary_memory_size(value);
  // @@protoc_insertion_point(field_set:tensorflow.CostGraphDef.Node.temporary_memory_size)
}

// int64 persistent_memory_size = 12;
inline void CostGraphDef_Node::clear_persistent_memory_size() {
  _impl_.persistent_memory_size_ = int64_t{0};
}
inline int64_t CostGraphDef_Node::_internal_persistent_memory_size() const {
  return _impl_.persistent_memory_size_;
}
inline int64_t CostGraphDef_Node::persistent_memory_size() const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.persistent_memory_size)
  return _internal_persistent_memory_size();
}
inline void CostGraphDef_Node::_internal_set_persistent_memory_size(int64_t value) {
  
  _impl_.persistent_memory_size_ = value;
}
inline void CostGraphDef_Node::set_persistent_memory_size(int64_t value) {
  _internal_set_persistent_memory_size(value);
  // @@protoc_insertion_point(field_set:tensorflow.CostGraphDef.Node.persistent_memory_size)
}

// int64 host_temp_memory_size = 10 [deprecated = true];
inline void CostGraphDef_Node::clear_host_temp_memory_size() {
  _impl_.host_temp_memory_size_ = int64_t{0};
}
inline int64_t CostGraphDef_Node::_internal_host_temp_memory_size() const {
  return _impl_.host_temp_memory_size_;
}
inline int64_t CostGraphDef_Node::host_temp_memory_size() const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.host_temp_memory_size)
  return _internal_host_temp_memory_size();
}
inline void CostGraphDef_Node::_internal_set_host_temp_memory_size(int64_t value) {
  
  _impl_.host_temp_memory_size_ = value;
}
inline void CostGraphDef_Node::set_host_temp_memory_size(int64_t value) {
  _internal_set_host_temp_memory_size(value);
  // @@protoc_insertion_point(field_set:tensorflow.CostGraphDef.Node.host_temp_memory_size)
}

// int64 device_temp_memory_size = 11 [deprecated = true];
inline void CostGraphDef_Node::clear_device_temp_memory_size() {
  _impl_.device_temp_memory_size_ = int64_t{0};
}
inline int64_t CostGraphDef_Node::_internal_device_temp_memory_size() const {
  return _impl_.device_temp_memory_size_;
}
inline int64_t CostGraphDef_Node::device_temp_memory_size() const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.device_temp_memory_size)
  return _internal_device_temp_memory_size();
}
inline void CostGraphDef_Node::_internal_set_device_temp_memory_size(int64_t value) {
  
  _impl_.device_temp_memory_size_ = value;
}
inline void CostGraphDef_Node::set_device_temp_memory_size(int64_t value) {
  _internal_set_device_temp_memory_size(value);
  // @@protoc_insertion_point(field_set:tensorflow.CostGraphDef.Node.device_temp_memory_size)
}

// int64 device_persistent_memory_size = 16 [deprecated = true];
inline void CostGraphDef_Node::clear_device_persistent_memory_size() {
  _impl_.device_persistent_memory_size_ = int64_t{0};
}
inline int64_t CostGraphDef_Node::_internal_device_persistent_memory_size() const {
  return _impl_.device_persistent_memory_size_;
}
inline int64_t CostGraphDef_Node::device_persistent_memory_size() const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.device_persistent_memory_size)
  return _internal_device_persistent_memory_size();
}
inline void CostGraphDef_Node::_internal_set_device_persistent_memory_size(int64_t value) {
  
  _impl_.device_persistent_memory_size_ = value;
}
inline void CostGraphDef_Node::set_device_persistent_memory_size(int64_t value) {
  _internal_set_device_persistent_memory_size(value);
  // @@protoc_insertion_point(field_set:tensorflow.CostGraphDef.Node.device_persistent_memory_size)
}

// int64 compute_cost = 9;
inline void CostGraphDef_Node::clear_compute_cost() {
  _impl_.compute_cost_ = int64_t{0};
}
inline int64_t CostGraphDef_Node::_internal_compute_cost() const {
  return _impl_.compute_cost_;
}
inline int64_t CostGraphDef_Node::compute_cost() const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.compute_cost)
  return _internal_compute_cost();
}
inline void CostGraphDef_Node::_internal_set_compute_cost(int64_t value) {
  
  _impl_.compute_cost_ = value;
}
inline void CostGraphDef_Node::set_compute_cost(int64_t value) {
  _internal_set_compute_cost(value);
  // @@protoc_insertion_point(field_set:tensorflow.CostGraphDef.Node.compute_cost)
}

// int64 compute_time = 14;
inline void CostGraphDef_Node::clear_compute_time() {
  _impl_.compute_time_ = int64_t{0};
}
inline int64_t CostGraphDef_Node::_internal_compute_time() const {
  return _impl_.compute_time_;
}
inline int64_t CostGraphDef_Node::compute_time() const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.compute_time)
  return _internal_compute_time();
}
inline void CostGraphDef_Node::_internal_set_compute_time(int64_t value) {
  
  _impl_.compute_time_ = value;
}
inline void CostGraphDef_Node::set_compute_time(int64_t value) {
  _internal_set_compute_time(value);
  // @@protoc_insertion_point(field_set:tensorflow.CostGraphDef.Node.compute_time)
}

// int64 memory_time = 15;
inline void CostGraphDef_Node::clear_memory_time() {
  _impl_.memory_time_ = int64_t{0};
}
inline int64_t CostGraphDef_Node::_internal_memory_time() const {
  return _impl_.memory_time_;
}
inline int64_t CostGraphDef_Node::memory_time() const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.memory_time)
  return _internal_memory_time();
}
inline void CostGraphDef_Node::_internal_set_memory_time(int64_t value) {
  
  _impl_.memory_time_ = value;
}
inline void CostGraphDef_Node::set_memory_time(int64_t value) {
  _internal_set_memory_time(value);
  // @@protoc_insertion_point(field_set:tensorflow.CostGraphDef.Node.memory_time)
}

// bool is_final = 7;
inline void CostGraphDef_Node::clear_is_final() {
  _impl_.is_final_ = false;
}
inline bool CostGraphDef_Node::_internal_is_final() const {
  return _impl_.is_final_;
}
inline bool CostGraphDef_Node::is_final() const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.is_final)
  return _internal_is_final();
}
inline void CostGraphDef_Node::_internal_set_is_final(bool value) {
  
  _impl_.is_final_ = value;
}
inline void CostGraphDef_Node::set_is_final(bool value) {
  _internal_set_is_final(value);
  // @@protoc_insertion_point(field_set:tensorflow.CostGraphDef.Node.is_final)
}

// repeated int32 control_input = 8;
inline int CostGraphDef_Node::_internal_control_input_size() const {
  return _impl_.control_input_.size();
}
inline int CostGraphDef_Node::control_input_size() const {
  return _internal_control_input_size();
}
inline void CostGraphDef_Node::clear_control_input() {
  _impl_.control_input_.Clear();
}
inline int32_t CostGraphDef_Node::_internal_control_input(int index) const {
  return _impl_.control_input_.Get(index);
}
inline int32_t CostGraphDef_Node::control_input(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.control_input)
  return _internal_control_input(index);
}
inline void CostGraphDef_Node::set_control_input(int index, int32_t value) {
  _impl_.control_input_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.CostGraphDef.Node.control_input)
}
inline void CostGraphDef_Node::_internal_add_control_input(int32_t value) {
  _impl_.control_input_.Add(value);
}
inline void CostGraphDef_Node::add_control_input(int32_t value) {
  _internal_add_control_input(value);
  // @@protoc_insertion_point(field_add:tensorflow.CostGraphDef.Node.control_input)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
CostGraphDef_Node::_internal_control_input() const {
  return _impl_.control_input_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
CostGraphDef_Node::control_input() const {
  // @@protoc_insertion_point(field_list:tensorflow.CostGraphDef.Node.control_input)
  return _internal_control_input();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
CostGraphDef_Node::_internal_mutable_control_input() {
  return &_impl_.control_input_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
CostGraphDef_Node::mutable_control_input() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.CostGraphDef.Node.control_input)
  return _internal_mutable_control_input();
}

// bool inaccurate = 17;
inline void CostGraphDef_Node::clear_inaccurate() {
  _impl_.inaccurate_ = false;
}
inline bool CostGraphDef_Node::_internal_inaccurate() const {
  return _impl_.inaccurate_;
}
inline bool CostGraphDef_Node::inaccurate() const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.inaccurate)
  return _internal_inaccurate();
}
inline void CostGraphDef_Node::_internal_set_inaccurate(bool value) {
  
  _impl_.inaccurate_ = value;
}
inline void CostGraphDef_Node::set_inaccurate(bool value) {
  _internal_set_inaccurate(value);
  // @@protoc_insertion_point(field_set:tensorflow.CostGraphDef.Node.inaccurate)
}

// -------------------------------------------------------------------

// CostGraphDef_AggregatedCost

// float cost = 1;
inline void CostGraphDef_AggregatedCost::clear_cost() {
  _impl_.cost_ = 0;
}
inline float CostGraphDef_AggregatedCost::_internal_cost() const {
  return _impl_.cost_;
}
inline float CostGraphDef_AggregatedCost::cost() const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.AggregatedCost.cost)
  return _internal_cost();
}
inline void CostGraphDef_AggregatedCost::_internal_set_cost(float value) {
  
  _impl_.cost_ = value;
}
inline void CostGraphDef_AggregatedCost::set_cost(float value) {
  _internal_set_cost(value);
  // @@protoc_insertion_point(field_set:tensorflow.CostGraphDef.AggregatedCost.cost)
}

// string dimension = 2;
inline void CostGraphDef_AggregatedCost::clear_dimension() {
  _impl_.dimension_.ClearToEmpty();
}
inline const std::string& CostGraphDef_AggregatedCost::dimension() const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.AggregatedCost.dimension)
  return _internal_dimension();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CostGraphDef_AggregatedCost::set_dimension(ArgT0&& arg0, ArgT... args) {
 
 _impl_.dimension_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.CostGraphDef.AggregatedCost.dimension)
}
inline std::string* CostGraphDef_AggregatedCost::mutable_dimension() {
  std::string* _s = _internal_mutable_dimension();
  // @@protoc_insertion_point(field_mutable:tensorflow.CostGraphDef.AggregatedCost.dimension)
  return _s;
}
inline const std::string& CostGraphDef_AggregatedCost::_internal_dimension() const {
  return _impl_.dimension_.Get();
}
inline void CostGraphDef_AggregatedCost::_internal_set_dimension(const std::string& value) {
  
  _impl_.dimension_.Set(value, GetArenaForAllocation());
}
inline std::string* CostGraphDef_AggregatedCost::_internal_mutable_dimension() {
  
  return _impl_.dimension_.Mutable(GetArenaForAllocation());
}
inline std::string* CostGraphDef_AggregatedCost::release_dimension() {
  // @@protoc_insertion_point(field_release:tensorflow.CostGraphDef.AggregatedCost.dimension)
  return _impl_.dimension_.Release();
}
inline void CostGraphDef_AggregatedCost::set_allocated_dimension(std::string* dimension) {
  if (dimension != nullptr) {
    
  } else {
    
  }
  _impl_.dimension_.SetAllocated(dimension, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.dimension_.IsDefault()) {
    _impl_.dimension_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CostGraphDef.AggregatedCost.dimension)
}

// -------------------------------------------------------------------

// CostGraphDef

// repeated .tensorflow.CostGraphDef.Node node = 1;
inline int CostGraphDef::_internal_node_size() const {
  return _impl_.node_.size();
}
inline int CostGraphDef::node_size() const {
  return _internal_node_size();
}
inline void CostGraphDef::clear_node() {
  _impl_.node_.Clear();
}
inline ::tensorflow::CostGraphDef_Node* CostGraphDef::mutable_node(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.CostGraphDef.node)
  return _impl_.node_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CostGraphDef_Node >*
CostGraphDef::mutable_node() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.CostGraphDef.node)
  return &_impl_.node_;
}
inline const ::tensorflow::CostGraphDef_Node& CostGraphDef::_internal_node(int index) const {
  return _impl_.node_.Get(index);
}
inline const ::tensorflow::CostGraphDef_Node& CostGraphDef::node(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.node)
  return _internal_node(index);
}
inline ::tensorflow::CostGraphDef_Node* CostGraphDef::_internal_add_node() {
  return _impl_.node_.Add();
}
inline ::tensorflow::CostGraphDef_Node* CostGraphDef::add_node() {
  ::tensorflow::CostGraphDef_Node* _add = _internal_add_node();
  // @@protoc_insertion_point(field_add:tensorflow.CostGraphDef.node)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CostGraphDef_Node >&
CostGraphDef::node() const {
  // @@protoc_insertion_point(field_list:tensorflow.CostGraphDef.node)
  return _impl_.node_;
}

// repeated .tensorflow.CostGraphDef.AggregatedCost cost = 2;
inline int CostGraphDef::_internal_cost_size() const {
  return _impl_.cost_.size();
}
inline int CostGraphDef::cost_size() const {
  return _internal_cost_size();
}
inline void CostGraphDef::clear_cost() {
  _impl_.cost_.Clear();
}
inline ::tensorflow::CostGraphDef_AggregatedCost* CostGraphDef::mutable_cost(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.CostGraphDef.cost)
  return _impl_.cost_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CostGraphDef_AggregatedCost >*
CostGraphDef::mutable_cost() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.CostGraphDef.cost)
  return &_impl_.cost_;
}
inline const ::tensorflow::CostGraphDef_AggregatedCost& CostGraphDef::_internal_cost(int index) const {
  return _impl_.cost_.Get(index);
}
inline const ::tensorflow::CostGraphDef_AggregatedCost& CostGraphDef::cost(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.cost)
  return _internal_cost(index);
}
inline ::tensorflow::CostGraphDef_AggregatedCost* CostGraphDef::_internal_add_cost() {
  return _impl_.cost_.Add();
}
inline ::tensorflow::CostGraphDef_AggregatedCost* CostGraphDef::add_cost() {
  ::tensorflow::CostGraphDef_AggregatedCost* _add = _internal_add_cost();
  // @@protoc_insertion_point(field_add:tensorflow.CostGraphDef.cost)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CostGraphDef_AggregatedCost >&
CostGraphDef::cost() const {
  // @@protoc_insertion_point(field_list:tensorflow.CostGraphDef.cost)
  return _impl_.cost_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto
