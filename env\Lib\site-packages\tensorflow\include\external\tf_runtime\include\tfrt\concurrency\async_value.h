/* Copyright 2022 Google LLC. All Rights Reserved.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
==============================================================================*/

#ifndef TFRT_CONCURRENCY_ASYNC_VALUE_H_
#define TFRT_CONCURRENCY_ASYNC_VALUE_H_

#include "xla/tsl/concurrency/async_value.h"  // from @xla  // IWYU pragma: export

// TODO(ezhulenev): We have targets that depend on transitive includes. This
// should be removed once we finally move to TSL concurrency package.
#include "tfrt/support/logging.h"  // IWYU pragma: export

#endif  // TFRT_CONCURRENCY_ASYNC_VALUE_H_
