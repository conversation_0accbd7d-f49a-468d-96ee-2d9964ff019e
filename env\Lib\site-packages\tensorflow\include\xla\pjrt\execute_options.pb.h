// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: xla/pjrt/execute_options.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_xla_2fpjrt_2fexecute_5foptions_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_xla_2fpjrt_2fexecute_5foptions_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_xla_2fpjrt_2fexecute_5foptions_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_xla_2fpjrt_2fexecute_5foptions_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_xla_2fpjrt_2fexecute_5foptions_2eproto;
namespace xla {
class ExecuteOptionsProto;
struct ExecuteOptionsProtoDefaultTypeInternal;
extern ExecuteOptionsProtoDefaultTypeInternal _ExecuteOptionsProto_default_instance_;
}  // namespace xla
PROTOBUF_NAMESPACE_OPEN
template<> ::xla::ExecuteOptionsProto* Arena::CreateMaybeMessage<::xla::ExecuteOptionsProto>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace xla {

enum ExecutionModeProto : int {
  EXECUTION_MODE_UNSPECIFIED = 0,
  EXECUTION_MODE_DEFAULT = 1,
  EXECUTION_MODE_SYNCHRONOUS = 2,
  EXECUTION_MODE_ASYNCHRONOUS = 3,
  ExecutionModeProto_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  ExecutionModeProto_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool ExecutionModeProto_IsValid(int value);
constexpr ExecutionModeProto ExecutionModeProto_MIN = EXECUTION_MODE_UNSPECIFIED;
constexpr ExecutionModeProto ExecutionModeProto_MAX = EXECUTION_MODE_ASYNCHRONOUS;
constexpr int ExecutionModeProto_ARRAYSIZE = ExecutionModeProto_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* ExecutionModeProto_descriptor();
template<typename T>
inline const std::string& ExecutionModeProto_Name(T enum_t_value) {
  static_assert(::std::is_same<T, ExecutionModeProto>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function ExecutionModeProto_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    ExecutionModeProto_descriptor(), enum_t_value);
}
inline bool ExecutionModeProto_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, ExecutionModeProto* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<ExecutionModeProto>(
    ExecutionModeProto_descriptor(), name, value);
}
// ===================================================================

class ExecuteOptionsProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.ExecuteOptionsProto) */ {
 public:
  inline ExecuteOptionsProto() : ExecuteOptionsProto(nullptr) {}
  ~ExecuteOptionsProto() override;
  explicit PROTOBUF_CONSTEXPR ExecuteOptionsProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ExecuteOptionsProto(const ExecuteOptionsProto& from);
  ExecuteOptionsProto(ExecuteOptionsProto&& from) noexcept
    : ExecuteOptionsProto() {
    *this = ::std::move(from);
  }

  inline ExecuteOptionsProto& operator=(const ExecuteOptionsProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline ExecuteOptionsProto& operator=(ExecuteOptionsProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ExecuteOptionsProto& default_instance() {
    return *internal_default_instance();
  }
  static inline const ExecuteOptionsProto* internal_default_instance() {
    return reinterpret_cast<const ExecuteOptionsProto*>(
               &_ExecuteOptionsProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(ExecuteOptionsProto& a, ExecuteOptionsProto& b) {
    a.Swap(&b);
  }
  inline void Swap(ExecuteOptionsProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ExecuteOptionsProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ExecuteOptionsProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ExecuteOptionsProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ExecuteOptionsProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ExecuteOptionsProto& from) {
    ExecuteOptionsProto::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ExecuteOptionsProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.ExecuteOptionsProto";
  }
  protected:
  explicit ExecuteOptionsProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNonDonatableInputIndicesFieldNumber = 7,
    kLaunchIdFieldNumber = 3,
    kArgumentsAreTupledFieldNumber = 1,
    kUntupleResultFieldNumber = 2,
    kStrictShapeCheckingFieldNumber = 4,
    kUseMajorToMinorDataLayoutForCallbacksFieldNumber = 8,
    kExecutionModeFieldNumber = 6,
  };
  // repeated int32 non_donatable_input_indices = 7;
  int non_donatable_input_indices_size() const;
  private:
  int _internal_non_donatable_input_indices_size() const;
  public:
  void clear_non_donatable_input_indices();
  private:
  int32_t _internal_non_donatable_input_indices(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
      _internal_non_donatable_input_indices() const;
  void _internal_add_non_donatable_input_indices(int32_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
      _internal_mutable_non_donatable_input_indices();
  public:
  int32_t non_donatable_input_indices(int index) const;
  void set_non_donatable_input_indices(int index, int32_t value);
  void add_non_donatable_input_indices(int32_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
      non_donatable_input_indices() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
      mutable_non_donatable_input_indices();

  // int32 launch_id = 3;
  void clear_launch_id();
  int32_t launch_id() const;
  void set_launch_id(int32_t value);
  private:
  int32_t _internal_launch_id() const;
  void _internal_set_launch_id(int32_t value);
  public:

  // bool arguments_are_tupled = 1;
  void clear_arguments_are_tupled();
  bool arguments_are_tupled() const;
  void set_arguments_are_tupled(bool value);
  private:
  bool _internal_arguments_are_tupled() const;
  void _internal_set_arguments_are_tupled(bool value);
  public:

  // bool untuple_result = 2;
  void clear_untuple_result();
  bool untuple_result() const;
  void set_untuple_result(bool value);
  private:
  bool _internal_untuple_result() const;
  void _internal_set_untuple_result(bool value);
  public:

  // bool strict_shape_checking = 4;
  void clear_strict_shape_checking();
  bool strict_shape_checking() const;
  void set_strict_shape_checking(bool value);
  private:
  bool _internal_strict_shape_checking() const;
  void _internal_set_strict_shape_checking(bool value);
  public:

  // bool use_major_to_minor_data_layout_for_callbacks = 8;
  void clear_use_major_to_minor_data_layout_for_callbacks();
  bool use_major_to_minor_data_layout_for_callbacks() const;
  void set_use_major_to_minor_data_layout_for_callbacks(bool value);
  private:
  bool _internal_use_major_to_minor_data_layout_for_callbacks() const;
  void _internal_set_use_major_to_minor_data_layout_for_callbacks(bool value);
  public:

  // .xla.ExecutionModeProto execution_mode = 6;
  void clear_execution_mode();
  ::xla::ExecutionModeProto execution_mode() const;
  void set_execution_mode(::xla::ExecutionModeProto value);
  private:
  ::xla::ExecutionModeProto _internal_execution_mode() const;
  void _internal_set_execution_mode(::xla::ExecutionModeProto value);
  public:

  // @@protoc_insertion_point(class_scope:xla.ExecuteOptionsProto)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t > non_donatable_input_indices_;
    mutable std::atomic<int> _non_donatable_input_indices_cached_byte_size_;
    int32_t launch_id_;
    bool arguments_are_tupled_;
    bool untuple_result_;
    bool strict_shape_checking_;
    bool use_major_to_minor_data_layout_for_callbacks_;
    int execution_mode_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fpjrt_2fexecute_5foptions_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// ExecuteOptionsProto

// bool arguments_are_tupled = 1;
inline void ExecuteOptionsProto::clear_arguments_are_tupled() {
  _impl_.arguments_are_tupled_ = false;
}
inline bool ExecuteOptionsProto::_internal_arguments_are_tupled() const {
  return _impl_.arguments_are_tupled_;
}
inline bool ExecuteOptionsProto::arguments_are_tupled() const {
  // @@protoc_insertion_point(field_get:xla.ExecuteOptionsProto.arguments_are_tupled)
  return _internal_arguments_are_tupled();
}
inline void ExecuteOptionsProto::_internal_set_arguments_are_tupled(bool value) {
  
  _impl_.arguments_are_tupled_ = value;
}
inline void ExecuteOptionsProto::set_arguments_are_tupled(bool value) {
  _internal_set_arguments_are_tupled(value);
  // @@protoc_insertion_point(field_set:xla.ExecuteOptionsProto.arguments_are_tupled)
}

// bool untuple_result = 2;
inline void ExecuteOptionsProto::clear_untuple_result() {
  _impl_.untuple_result_ = false;
}
inline bool ExecuteOptionsProto::_internal_untuple_result() const {
  return _impl_.untuple_result_;
}
inline bool ExecuteOptionsProto::untuple_result() const {
  // @@protoc_insertion_point(field_get:xla.ExecuteOptionsProto.untuple_result)
  return _internal_untuple_result();
}
inline void ExecuteOptionsProto::_internal_set_untuple_result(bool value) {
  
  _impl_.untuple_result_ = value;
}
inline void ExecuteOptionsProto::set_untuple_result(bool value) {
  _internal_set_untuple_result(value);
  // @@protoc_insertion_point(field_set:xla.ExecuteOptionsProto.untuple_result)
}

// int32 launch_id = 3;
inline void ExecuteOptionsProto::clear_launch_id() {
  _impl_.launch_id_ = 0;
}
inline int32_t ExecuteOptionsProto::_internal_launch_id() const {
  return _impl_.launch_id_;
}
inline int32_t ExecuteOptionsProto::launch_id() const {
  // @@protoc_insertion_point(field_get:xla.ExecuteOptionsProto.launch_id)
  return _internal_launch_id();
}
inline void ExecuteOptionsProto::_internal_set_launch_id(int32_t value) {
  
  _impl_.launch_id_ = value;
}
inline void ExecuteOptionsProto::set_launch_id(int32_t value) {
  _internal_set_launch_id(value);
  // @@protoc_insertion_point(field_set:xla.ExecuteOptionsProto.launch_id)
}

// bool strict_shape_checking = 4;
inline void ExecuteOptionsProto::clear_strict_shape_checking() {
  _impl_.strict_shape_checking_ = false;
}
inline bool ExecuteOptionsProto::_internal_strict_shape_checking() const {
  return _impl_.strict_shape_checking_;
}
inline bool ExecuteOptionsProto::strict_shape_checking() const {
  // @@protoc_insertion_point(field_get:xla.ExecuteOptionsProto.strict_shape_checking)
  return _internal_strict_shape_checking();
}
inline void ExecuteOptionsProto::_internal_set_strict_shape_checking(bool value) {
  
  _impl_.strict_shape_checking_ = value;
}
inline void ExecuteOptionsProto::set_strict_shape_checking(bool value) {
  _internal_set_strict_shape_checking(value);
  // @@protoc_insertion_point(field_set:xla.ExecuteOptionsProto.strict_shape_checking)
}

// bool use_major_to_minor_data_layout_for_callbacks = 8;
inline void ExecuteOptionsProto::clear_use_major_to_minor_data_layout_for_callbacks() {
  _impl_.use_major_to_minor_data_layout_for_callbacks_ = false;
}
inline bool ExecuteOptionsProto::_internal_use_major_to_minor_data_layout_for_callbacks() const {
  return _impl_.use_major_to_minor_data_layout_for_callbacks_;
}
inline bool ExecuteOptionsProto::use_major_to_minor_data_layout_for_callbacks() const {
  // @@protoc_insertion_point(field_get:xla.ExecuteOptionsProto.use_major_to_minor_data_layout_for_callbacks)
  return _internal_use_major_to_minor_data_layout_for_callbacks();
}
inline void ExecuteOptionsProto::_internal_set_use_major_to_minor_data_layout_for_callbacks(bool value) {
  
  _impl_.use_major_to_minor_data_layout_for_callbacks_ = value;
}
inline void ExecuteOptionsProto::set_use_major_to_minor_data_layout_for_callbacks(bool value) {
  _internal_set_use_major_to_minor_data_layout_for_callbacks(value);
  // @@protoc_insertion_point(field_set:xla.ExecuteOptionsProto.use_major_to_minor_data_layout_for_callbacks)
}

// .xla.ExecutionModeProto execution_mode = 6;
inline void ExecuteOptionsProto::clear_execution_mode() {
  _impl_.execution_mode_ = 0;
}
inline ::xla::ExecutionModeProto ExecuteOptionsProto::_internal_execution_mode() const {
  return static_cast< ::xla::ExecutionModeProto >(_impl_.execution_mode_);
}
inline ::xla::ExecutionModeProto ExecuteOptionsProto::execution_mode() const {
  // @@protoc_insertion_point(field_get:xla.ExecuteOptionsProto.execution_mode)
  return _internal_execution_mode();
}
inline void ExecuteOptionsProto::_internal_set_execution_mode(::xla::ExecutionModeProto value) {
  
  _impl_.execution_mode_ = value;
}
inline void ExecuteOptionsProto::set_execution_mode(::xla::ExecutionModeProto value) {
  _internal_set_execution_mode(value);
  // @@protoc_insertion_point(field_set:xla.ExecuteOptionsProto.execution_mode)
}

// repeated int32 non_donatable_input_indices = 7;
inline int ExecuteOptionsProto::_internal_non_donatable_input_indices_size() const {
  return _impl_.non_donatable_input_indices_.size();
}
inline int ExecuteOptionsProto::non_donatable_input_indices_size() const {
  return _internal_non_donatable_input_indices_size();
}
inline void ExecuteOptionsProto::clear_non_donatable_input_indices() {
  _impl_.non_donatable_input_indices_.Clear();
}
inline int32_t ExecuteOptionsProto::_internal_non_donatable_input_indices(int index) const {
  return _impl_.non_donatable_input_indices_.Get(index);
}
inline int32_t ExecuteOptionsProto::non_donatable_input_indices(int index) const {
  // @@protoc_insertion_point(field_get:xla.ExecuteOptionsProto.non_donatable_input_indices)
  return _internal_non_donatable_input_indices(index);
}
inline void ExecuteOptionsProto::set_non_donatable_input_indices(int index, int32_t value) {
  _impl_.non_donatable_input_indices_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.ExecuteOptionsProto.non_donatable_input_indices)
}
inline void ExecuteOptionsProto::_internal_add_non_donatable_input_indices(int32_t value) {
  _impl_.non_donatable_input_indices_.Add(value);
}
inline void ExecuteOptionsProto::add_non_donatable_input_indices(int32_t value) {
  _internal_add_non_donatable_input_indices(value);
  // @@protoc_insertion_point(field_add:xla.ExecuteOptionsProto.non_donatable_input_indices)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
ExecuteOptionsProto::_internal_non_donatable_input_indices() const {
  return _impl_.non_donatable_input_indices_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
ExecuteOptionsProto::non_donatable_input_indices() const {
  // @@protoc_insertion_point(field_list:xla.ExecuteOptionsProto.non_donatable_input_indices)
  return _internal_non_donatable_input_indices();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
ExecuteOptionsProto::_internal_mutable_non_donatable_input_indices() {
  return &_impl_.non_donatable_input_indices_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
ExecuteOptionsProto::mutable_non_donatable_input_indices() {
  // @@protoc_insertion_point(field_mutable_list:xla.ExecuteOptionsProto.non_donatable_input_indices)
  return _internal_mutable_non_donatable_input_indices();
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace xla

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::xla::ExecutionModeProto> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::xla::ExecutionModeProto>() {
  return ::xla::ExecutionModeProto_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_xla_2fpjrt_2fexecute_5foptions_2eproto
