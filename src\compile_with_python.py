#!/usr/bin/env python3
"""
使用Python调用编译器编译denoise_training
"""
import os
import subprocess
import sys
from pathlib import Path

def find_compiler():
    """查找可用的编译器"""
    compilers = [
        # MSYS2 gcc
        "env/mingw64/bin/gcc.exe",
        "env/usr/bin/gcc.exe", 
        # 系统gcc
        "gcc",
        "gcc.exe",
        # Visual Studio
        "cl",
        "cl.exe",
        # Clang
        "clang",
        "clang.exe"
    ]
    
    for compiler in compilers:
        try:
            if os.path.exists(compiler):
                print(f"找到编译器: {compiler}")
                return compiler
            else:
                # 尝试在PATH中查找
                result = subprocess.run(['where', compiler], 
                                      capture_output=True, text=True, shell=True)
                if result.returncode == 0:
                    print(f"在PATH中找到编译器: {compiler}")
                    return compiler
        except:
            continue
    
    return None

def compile_with_gcc(compiler):
    """使用GCC编译"""
    cmd = [
        compiler,
        "-DTRAINING=1",
        "-Wall", "-W", "-O3", "-g",
        "-I../include",
        "denoise.c",
        "kiss_fft.c", 
        "pitch.c",
        "celt_lpc.c",
        "rnn.c",
        "rnnoise_data.c",
        "-o", "denoise_training.exe",
        "-lm"
    ]
    
    print("编译命令:", " ".join(cmd))
    return subprocess.run(cmd)

def compile_with_cl(compiler):
    """使用Visual Studio CL编译"""
    cmd = [
        compiler,
        "/DTRAINING=1",
        "/I../include",
        "denoise.c",
        "kiss_fft.c",
        "pitch.c", 
        "celt_lpc.c",
        "rnn.c",
        "rnnoise_data.c",
        "/Fe:denoise_training.exe"
    ]
    
    print("编译命令:", " ".join(cmd))
    return subprocess.run(cmd)

def main():
    print("=== RNNoise 编译脚本 ===")
    print("正在查找编译器...")
    
    # 确保在src目录中
    if not os.path.exists("denoise.c"):
        print("错误：请在src目录中运行此脚本")
        return 1
    
    compiler = find_compiler()
    if not compiler:
        print("错误：未找到任何编译器！")
        print("请安装以下编译器之一：")
        print("1. Visual Studio Build Tools")
        print("2. MinGW-w64") 
        print("3. MSYS2")
        return 1
    
    print(f"使用编译器: {compiler}")
    
    # 根据编译器类型选择编译方法
    if "cl" in compiler.lower():
        result = compile_with_cl(compiler)
    else:
        result = compile_with_gcc(compiler)
    
    if result.returncode == 0:
        print("✅ 编译成功！生成了 denoise_training.exe")
        return 0
    else:
        print("❌ 编译失败！")
        print("错误信息:", result.stderr if result.stderr else "无详细错误信息")
        return 1

if __name__ == "__main__":
    sys.exit(main())
