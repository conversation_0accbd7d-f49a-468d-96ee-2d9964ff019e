// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/framework/function.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2ffunction_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2ffunction_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/attr_value.pb.h"
#include "tensorflow/core/framework/node_def.pb.h"
#include "tensorflow/core/framework/op_def.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fframework_2ffunction_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fframework_2ffunction_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fframework_2ffunction_2eproto;
namespace tensorflow {
class FunctionDef;
struct FunctionDefDefaultTypeInternal;
extern FunctionDefDefaultTypeInternal _FunctionDef_default_instance_;
class FunctionDefLibrary;
struct FunctionDefLibraryDefaultTypeInternal;
extern FunctionDefLibraryDefaultTypeInternal _FunctionDefLibrary_default_instance_;
class FunctionDef_ArgAttrEntry_DoNotUse;
struct FunctionDef_ArgAttrEntry_DoNotUseDefaultTypeInternal;
extern FunctionDef_ArgAttrEntry_DoNotUseDefaultTypeInternal _FunctionDef_ArgAttrEntry_DoNotUse_default_instance_;
class FunctionDef_ArgAttrs;
struct FunctionDef_ArgAttrsDefaultTypeInternal;
extern FunctionDef_ArgAttrsDefaultTypeInternal _FunctionDef_ArgAttrs_default_instance_;
class FunctionDef_ArgAttrs_AttrEntry_DoNotUse;
struct FunctionDef_ArgAttrs_AttrEntry_DoNotUseDefaultTypeInternal;
extern FunctionDef_ArgAttrs_AttrEntry_DoNotUseDefaultTypeInternal _FunctionDef_ArgAttrs_AttrEntry_DoNotUse_default_instance_;
class FunctionDef_AttrEntry_DoNotUse;
struct FunctionDef_AttrEntry_DoNotUseDefaultTypeInternal;
extern FunctionDef_AttrEntry_DoNotUseDefaultTypeInternal _FunctionDef_AttrEntry_DoNotUse_default_instance_;
class FunctionDef_ControlRetEntry_DoNotUse;
struct FunctionDef_ControlRetEntry_DoNotUseDefaultTypeInternal;
extern FunctionDef_ControlRetEntry_DoNotUseDefaultTypeInternal _FunctionDef_ControlRetEntry_DoNotUse_default_instance_;
class FunctionDef_ResourceArgUniqueIdEntry_DoNotUse;
struct FunctionDef_ResourceArgUniqueIdEntry_DoNotUseDefaultTypeInternal;
extern FunctionDef_ResourceArgUniqueIdEntry_DoNotUseDefaultTypeInternal _FunctionDef_ResourceArgUniqueIdEntry_DoNotUse_default_instance_;
class FunctionDef_RetEntry_DoNotUse;
struct FunctionDef_RetEntry_DoNotUseDefaultTypeInternal;
extern FunctionDef_RetEntry_DoNotUseDefaultTypeInternal _FunctionDef_RetEntry_DoNotUse_default_instance_;
class GradientDef;
struct GradientDefDefaultTypeInternal;
extern GradientDefDefaultTypeInternal _GradientDef_default_instance_;
class RegisteredGradient;
struct RegisteredGradientDefaultTypeInternal;
extern RegisteredGradientDefaultTypeInternal _RegisteredGradient_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::FunctionDef* Arena::CreateMaybeMessage<::tensorflow::FunctionDef>(Arena*);
template<> ::tensorflow::FunctionDefLibrary* Arena::CreateMaybeMessage<::tensorflow::FunctionDefLibrary>(Arena*);
template<> ::tensorflow::FunctionDef_ArgAttrEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::FunctionDef_ArgAttrEntry_DoNotUse>(Arena*);
template<> ::tensorflow::FunctionDef_ArgAttrs* Arena::CreateMaybeMessage<::tensorflow::FunctionDef_ArgAttrs>(Arena*);
template<> ::tensorflow::FunctionDef_ArgAttrs_AttrEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::FunctionDef_ArgAttrs_AttrEntry_DoNotUse>(Arena*);
template<> ::tensorflow::FunctionDef_AttrEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::FunctionDef_AttrEntry_DoNotUse>(Arena*);
template<> ::tensorflow::FunctionDef_ControlRetEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::FunctionDef_ControlRetEntry_DoNotUse>(Arena*);
template<> ::tensorflow::FunctionDef_ResourceArgUniqueIdEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::FunctionDef_ResourceArgUniqueIdEntry_DoNotUse>(Arena*);
template<> ::tensorflow::FunctionDef_RetEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::FunctionDef_RetEntry_DoNotUse>(Arena*);
template<> ::tensorflow::GradientDef* Arena::CreateMaybeMessage<::tensorflow::GradientDef>(Arena*);
template<> ::tensorflow::RegisteredGradient* Arena::CreateMaybeMessage<::tensorflow::RegisteredGradient>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

// ===================================================================

class FunctionDefLibrary final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.FunctionDefLibrary) */ {
 public:
  inline FunctionDefLibrary() : FunctionDefLibrary(nullptr) {}
  ~FunctionDefLibrary() override;
  explicit PROTOBUF_CONSTEXPR FunctionDefLibrary(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  FunctionDefLibrary(const FunctionDefLibrary& from);
  FunctionDefLibrary(FunctionDefLibrary&& from) noexcept
    : FunctionDefLibrary() {
    *this = ::std::move(from);
  }

  inline FunctionDefLibrary& operator=(const FunctionDefLibrary& from) {
    CopyFrom(from);
    return *this;
  }
  inline FunctionDefLibrary& operator=(FunctionDefLibrary&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const FunctionDefLibrary& default_instance() {
    return *internal_default_instance();
  }
  static inline const FunctionDefLibrary* internal_default_instance() {
    return reinterpret_cast<const FunctionDefLibrary*>(
               &_FunctionDefLibrary_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(FunctionDefLibrary& a, FunctionDefLibrary& b) {
    a.Swap(&b);
  }
  inline void Swap(FunctionDefLibrary* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(FunctionDefLibrary* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  FunctionDefLibrary* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<FunctionDefLibrary>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const FunctionDefLibrary& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const FunctionDefLibrary& from) {
    FunctionDefLibrary::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(FunctionDefLibrary* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.FunctionDefLibrary";
  }
  protected:
  explicit FunctionDefLibrary(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFunctionFieldNumber = 1,
    kGradientFieldNumber = 2,
    kRegisteredGradientsFieldNumber = 3,
  };
  // repeated .tensorflow.FunctionDef function = 1;
  int function_size() const;
  private:
  int _internal_function_size() const;
  public:
  void clear_function();
  ::tensorflow::FunctionDef* mutable_function(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::FunctionDef >*
      mutable_function();
  private:
  const ::tensorflow::FunctionDef& _internal_function(int index) const;
  ::tensorflow::FunctionDef* _internal_add_function();
  public:
  const ::tensorflow::FunctionDef& function(int index) const;
  ::tensorflow::FunctionDef* add_function();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::FunctionDef >&
      function() const;

  // repeated .tensorflow.GradientDef gradient = 2;
  int gradient_size() const;
  private:
  int _internal_gradient_size() const;
  public:
  void clear_gradient();
  ::tensorflow::GradientDef* mutable_gradient(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GradientDef >*
      mutable_gradient();
  private:
  const ::tensorflow::GradientDef& _internal_gradient(int index) const;
  ::tensorflow::GradientDef* _internal_add_gradient();
  public:
  const ::tensorflow::GradientDef& gradient(int index) const;
  ::tensorflow::GradientDef* add_gradient();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GradientDef >&
      gradient() const;

  // repeated .tensorflow.RegisteredGradient registered_gradients = 3;
  int registered_gradients_size() const;
  private:
  int _internal_registered_gradients_size() const;
  public:
  void clear_registered_gradients();
  ::tensorflow::RegisteredGradient* mutable_registered_gradients(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::RegisteredGradient >*
      mutable_registered_gradients();
  private:
  const ::tensorflow::RegisteredGradient& _internal_registered_gradients(int index) const;
  ::tensorflow::RegisteredGradient* _internal_add_registered_gradients();
  public:
  const ::tensorflow::RegisteredGradient& registered_gradients(int index) const;
  ::tensorflow::RegisteredGradient* add_registered_gradients();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::RegisteredGradient >&
      registered_gradients() const;

  // @@protoc_insertion_point(class_scope:tensorflow.FunctionDefLibrary)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::FunctionDef > function_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GradientDef > gradient_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::RegisteredGradient > registered_gradients_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2ffunction_2eproto;
};
// -------------------------------------------------------------------

class FunctionDef_AttrEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<FunctionDef_AttrEntry_DoNotUse, 
    std::string, ::tensorflow::AttrValue,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<FunctionDef_AttrEntry_DoNotUse, 
    std::string, ::tensorflow::AttrValue,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> SuperType;
  FunctionDef_AttrEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR FunctionDef_AttrEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit FunctionDef_AttrEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const FunctionDef_AttrEntry_DoNotUse& other);
  static const FunctionDef_AttrEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const FunctionDef_AttrEntry_DoNotUse*>(&_FunctionDef_AttrEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.FunctionDef.AttrEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2ffunction_2eproto;
};

// -------------------------------------------------------------------

class FunctionDef_ArgAttrs_AttrEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<FunctionDef_ArgAttrs_AttrEntry_DoNotUse, 
    std::string, ::tensorflow::AttrValue,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<FunctionDef_ArgAttrs_AttrEntry_DoNotUse, 
    std::string, ::tensorflow::AttrValue,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> SuperType;
  FunctionDef_ArgAttrs_AttrEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR FunctionDef_ArgAttrs_AttrEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit FunctionDef_ArgAttrs_AttrEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const FunctionDef_ArgAttrs_AttrEntry_DoNotUse& other);
  static const FunctionDef_ArgAttrs_AttrEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const FunctionDef_ArgAttrs_AttrEntry_DoNotUse*>(&_FunctionDef_ArgAttrs_AttrEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.FunctionDef.ArgAttrs.AttrEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2ffunction_2eproto;
};

// -------------------------------------------------------------------

class FunctionDef_ArgAttrs final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.FunctionDef.ArgAttrs) */ {
 public:
  inline FunctionDef_ArgAttrs() : FunctionDef_ArgAttrs(nullptr) {}
  ~FunctionDef_ArgAttrs() override;
  explicit PROTOBUF_CONSTEXPR FunctionDef_ArgAttrs(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  FunctionDef_ArgAttrs(const FunctionDef_ArgAttrs& from);
  FunctionDef_ArgAttrs(FunctionDef_ArgAttrs&& from) noexcept
    : FunctionDef_ArgAttrs() {
    *this = ::std::move(from);
  }

  inline FunctionDef_ArgAttrs& operator=(const FunctionDef_ArgAttrs& from) {
    CopyFrom(from);
    return *this;
  }
  inline FunctionDef_ArgAttrs& operator=(FunctionDef_ArgAttrs&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const FunctionDef_ArgAttrs& default_instance() {
    return *internal_default_instance();
  }
  static inline const FunctionDef_ArgAttrs* internal_default_instance() {
    return reinterpret_cast<const FunctionDef_ArgAttrs*>(
               &_FunctionDef_ArgAttrs_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(FunctionDef_ArgAttrs& a, FunctionDef_ArgAttrs& b) {
    a.Swap(&b);
  }
  inline void Swap(FunctionDef_ArgAttrs* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(FunctionDef_ArgAttrs* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  FunctionDef_ArgAttrs* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<FunctionDef_ArgAttrs>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const FunctionDef_ArgAttrs& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const FunctionDef_ArgAttrs& from) {
    FunctionDef_ArgAttrs::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(FunctionDef_ArgAttrs* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.FunctionDef.ArgAttrs";
  }
  protected:
  explicit FunctionDef_ArgAttrs(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kAttrFieldNumber = 1,
  };
  // map<string, .tensorflow.AttrValue> attr = 1;
  int attr_size() const;
  private:
  int _internal_attr_size() const;
  public:
  void clear_attr();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >&
      _internal_attr() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >*
      _internal_mutable_attr();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >&
      attr() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >*
      mutable_attr();

  // @@protoc_insertion_point(class_scope:tensorflow.FunctionDef.ArgAttrs)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        FunctionDef_ArgAttrs_AttrEntry_DoNotUse,
        std::string, ::tensorflow::AttrValue,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> attr_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2ffunction_2eproto;
};
// -------------------------------------------------------------------

class FunctionDef_ArgAttrEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<FunctionDef_ArgAttrEntry_DoNotUse, 
    uint32_t, ::tensorflow::FunctionDef_ArgAttrs,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_UINT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<FunctionDef_ArgAttrEntry_DoNotUse, 
    uint32_t, ::tensorflow::FunctionDef_ArgAttrs,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_UINT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> SuperType;
  FunctionDef_ArgAttrEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR FunctionDef_ArgAttrEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit FunctionDef_ArgAttrEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const FunctionDef_ArgAttrEntry_DoNotUse& other);
  static const FunctionDef_ArgAttrEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const FunctionDef_ArgAttrEntry_DoNotUse*>(&_FunctionDef_ArgAttrEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(void*) { return true; }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2ffunction_2eproto;
};

// -------------------------------------------------------------------

class FunctionDef_ResourceArgUniqueIdEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<FunctionDef_ResourceArgUniqueIdEntry_DoNotUse, 
    uint32_t, uint32_t,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_UINT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_UINT32> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<FunctionDef_ResourceArgUniqueIdEntry_DoNotUse, 
    uint32_t, uint32_t,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_UINT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_UINT32> SuperType;
  FunctionDef_ResourceArgUniqueIdEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR FunctionDef_ResourceArgUniqueIdEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit FunctionDef_ResourceArgUniqueIdEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const FunctionDef_ResourceArgUniqueIdEntry_DoNotUse& other);
  static const FunctionDef_ResourceArgUniqueIdEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const FunctionDef_ResourceArgUniqueIdEntry_DoNotUse*>(&_FunctionDef_ResourceArgUniqueIdEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(void*) { return true; }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2ffunction_2eproto;
};

// -------------------------------------------------------------------

class FunctionDef_RetEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<FunctionDef_RetEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<FunctionDef_RetEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> SuperType;
  FunctionDef_RetEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR FunctionDef_RetEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit FunctionDef_RetEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const FunctionDef_RetEntry_DoNotUse& other);
  static const FunctionDef_RetEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const FunctionDef_RetEntry_DoNotUse*>(&_FunctionDef_RetEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.FunctionDef.RetEntry.key");
 }
  static bool ValidateValue(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.FunctionDef.RetEntry.value");
 }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2ffunction_2eproto;
};

// -------------------------------------------------------------------

class FunctionDef_ControlRetEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<FunctionDef_ControlRetEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<FunctionDef_ControlRetEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> SuperType;
  FunctionDef_ControlRetEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR FunctionDef_ControlRetEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit FunctionDef_ControlRetEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const FunctionDef_ControlRetEntry_DoNotUse& other);
  static const FunctionDef_ControlRetEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const FunctionDef_ControlRetEntry_DoNotUse*>(&_FunctionDef_ControlRetEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.FunctionDef.ControlRetEntry.key");
 }
  static bool ValidateValue(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.FunctionDef.ControlRetEntry.value");
 }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2ffunction_2eproto;
};

// -------------------------------------------------------------------

class FunctionDef final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.FunctionDef) */ {
 public:
  inline FunctionDef() : FunctionDef(nullptr) {}
  ~FunctionDef() override;
  explicit PROTOBUF_CONSTEXPR FunctionDef(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  FunctionDef(const FunctionDef& from);
  FunctionDef(FunctionDef&& from) noexcept
    : FunctionDef() {
    *this = ::std::move(from);
  }

  inline FunctionDef& operator=(const FunctionDef& from) {
    CopyFrom(from);
    return *this;
  }
  inline FunctionDef& operator=(FunctionDef&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const FunctionDef& default_instance() {
    return *internal_default_instance();
  }
  static inline const FunctionDef* internal_default_instance() {
    return reinterpret_cast<const FunctionDef*>(
               &_FunctionDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(FunctionDef& a, FunctionDef& b) {
    a.Swap(&b);
  }
  inline void Swap(FunctionDef* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(FunctionDef* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  FunctionDef* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<FunctionDef>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const FunctionDef& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const FunctionDef& from) {
    FunctionDef::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(FunctionDef* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.FunctionDef";
  }
  protected:
  explicit FunctionDef(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef FunctionDef_ArgAttrs ArgAttrs;

  // accessors -------------------------------------------------------

  enum : int {
    kNodeDefFieldNumber = 3,
    kRetFieldNumber = 4,
    kAttrFieldNumber = 5,
    kControlRetFieldNumber = 6,
    kArgAttrFieldNumber = 7,
    kResourceArgUniqueIdFieldNumber = 8,
    kSignatureFieldNumber = 1,
  };
  // repeated .tensorflow.NodeDef node_def = 3;
  int node_def_size() const;
  private:
  int _internal_node_def_size() const;
  public:
  void clear_node_def();
  ::tensorflow::NodeDef* mutable_node_def(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NodeDef >*
      mutable_node_def();
  private:
  const ::tensorflow::NodeDef& _internal_node_def(int index) const;
  ::tensorflow::NodeDef* _internal_add_node_def();
  public:
  const ::tensorflow::NodeDef& node_def(int index) const;
  ::tensorflow::NodeDef* add_node_def();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NodeDef >&
      node_def() const;

  // map<string, string> ret = 4;
  int ret_size() const;
  private:
  int _internal_ret_size() const;
  public:
  void clear_ret();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      _internal_ret() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      _internal_mutable_ret();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      ret() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      mutable_ret();

  // map<string, .tensorflow.AttrValue> attr = 5;
  int attr_size() const;
  private:
  int _internal_attr_size() const;
  public:
  void clear_attr();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >&
      _internal_attr() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >*
      _internal_mutable_attr();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >&
      attr() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >*
      mutable_attr();

  // map<string, string> control_ret = 6;
  int control_ret_size() const;
  private:
  int _internal_control_ret_size() const;
  public:
  void clear_control_ret();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      _internal_control_ret() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      _internal_mutable_control_ret();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      control_ret() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      mutable_control_ret();

  // map<uint32, .tensorflow.FunctionDef.ArgAttrs> arg_attr = 7;
  int arg_attr_size() const;
  private:
  int _internal_arg_attr_size() const;
  public:
  void clear_arg_attr();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< uint32_t, ::tensorflow::FunctionDef_ArgAttrs >&
      _internal_arg_attr() const;
  ::PROTOBUF_NAMESPACE_ID::Map< uint32_t, ::tensorflow::FunctionDef_ArgAttrs >*
      _internal_mutable_arg_attr();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< uint32_t, ::tensorflow::FunctionDef_ArgAttrs >&
      arg_attr() const;
  ::PROTOBUF_NAMESPACE_ID::Map< uint32_t, ::tensorflow::FunctionDef_ArgAttrs >*
      mutable_arg_attr();

  // map<uint32, uint32> resource_arg_unique_id = 8;
  int resource_arg_unique_id_size() const;
  private:
  int _internal_resource_arg_unique_id_size() const;
  public:
  void clear_resource_arg_unique_id();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< uint32_t, uint32_t >&
      _internal_resource_arg_unique_id() const;
  ::PROTOBUF_NAMESPACE_ID::Map< uint32_t, uint32_t >*
      _internal_mutable_resource_arg_unique_id();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< uint32_t, uint32_t >&
      resource_arg_unique_id() const;
  ::PROTOBUF_NAMESPACE_ID::Map< uint32_t, uint32_t >*
      mutable_resource_arg_unique_id();

  // .tensorflow.OpDef signature = 1;
  bool has_signature() const;
  private:
  bool _internal_has_signature() const;
  public:
  void clear_signature();
  const ::tensorflow::OpDef& signature() const;
  PROTOBUF_NODISCARD ::tensorflow::OpDef* release_signature();
  ::tensorflow::OpDef* mutable_signature();
  void set_allocated_signature(::tensorflow::OpDef* signature);
  private:
  const ::tensorflow::OpDef& _internal_signature() const;
  ::tensorflow::OpDef* _internal_mutable_signature();
  public:
  void unsafe_arena_set_allocated_signature(
      ::tensorflow::OpDef* signature);
  ::tensorflow::OpDef* unsafe_arena_release_signature();

  // @@protoc_insertion_point(class_scope:tensorflow.FunctionDef)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NodeDef > node_def_;
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        FunctionDef_RetEntry_DoNotUse,
        std::string, std::string,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> ret_;
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        FunctionDef_AttrEntry_DoNotUse,
        std::string, ::tensorflow::AttrValue,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> attr_;
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        FunctionDef_ControlRetEntry_DoNotUse,
        std::string, std::string,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> control_ret_;
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        FunctionDef_ArgAttrEntry_DoNotUse,
        uint32_t, ::tensorflow::FunctionDef_ArgAttrs,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_UINT32,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> arg_attr_;
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        FunctionDef_ResourceArgUniqueIdEntry_DoNotUse,
        uint32_t, uint32_t,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_UINT32,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_UINT32> resource_arg_unique_id_;
    ::tensorflow::OpDef* signature_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2ffunction_2eproto;
};
// -------------------------------------------------------------------

class GradientDef final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.GradientDef) */ {
 public:
  inline GradientDef() : GradientDef(nullptr) {}
  ~GradientDef() override;
  explicit PROTOBUF_CONSTEXPR GradientDef(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GradientDef(const GradientDef& from);
  GradientDef(GradientDef&& from) noexcept
    : GradientDef() {
    *this = ::std::move(from);
  }

  inline GradientDef& operator=(const GradientDef& from) {
    CopyFrom(from);
    return *this;
  }
  inline GradientDef& operator=(GradientDef&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GradientDef& default_instance() {
    return *internal_default_instance();
  }
  static inline const GradientDef* internal_default_instance() {
    return reinterpret_cast<const GradientDef*>(
               &_GradientDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(GradientDef& a, GradientDef& b) {
    a.Swap(&b);
  }
  inline void Swap(GradientDef* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GradientDef* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GradientDef* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GradientDef>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GradientDef& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const GradientDef& from) {
    GradientDef::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GradientDef* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.GradientDef";
  }
  protected:
  explicit GradientDef(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFunctionNameFieldNumber = 1,
    kGradientFuncFieldNumber = 2,
  };
  // string function_name = 1;
  void clear_function_name();
  const std::string& function_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_function_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_function_name();
  PROTOBUF_NODISCARD std::string* release_function_name();
  void set_allocated_function_name(std::string* function_name);
  private:
  const std::string& _internal_function_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_function_name(const std::string& value);
  std::string* _internal_mutable_function_name();
  public:

  // string gradient_func = 2;
  void clear_gradient_func();
  const std::string& gradient_func() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_gradient_func(ArgT0&& arg0, ArgT... args);
  std::string* mutable_gradient_func();
  PROTOBUF_NODISCARD std::string* release_gradient_func();
  void set_allocated_gradient_func(std::string* gradient_func);
  private:
  const std::string& _internal_gradient_func() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_gradient_func(const std::string& value);
  std::string* _internal_mutable_gradient_func();
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.GradientDef)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr function_name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr gradient_func_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2ffunction_2eproto;
};
// -------------------------------------------------------------------

class RegisteredGradient final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.RegisteredGradient) */ {
 public:
  inline RegisteredGradient() : RegisteredGradient(nullptr) {}
  ~RegisteredGradient() override;
  explicit PROTOBUF_CONSTEXPR RegisteredGradient(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RegisteredGradient(const RegisteredGradient& from);
  RegisteredGradient(RegisteredGradient&& from) noexcept
    : RegisteredGradient() {
    *this = ::std::move(from);
  }

  inline RegisteredGradient& operator=(const RegisteredGradient& from) {
    CopyFrom(from);
    return *this;
  }
  inline RegisteredGradient& operator=(RegisteredGradient&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RegisteredGradient& default_instance() {
    return *internal_default_instance();
  }
  static inline const RegisteredGradient* internal_default_instance() {
    return reinterpret_cast<const RegisteredGradient*>(
               &_RegisteredGradient_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  friend void swap(RegisteredGradient& a, RegisteredGradient& b) {
    a.Swap(&b);
  }
  inline void Swap(RegisteredGradient* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RegisteredGradient* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RegisteredGradient* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RegisteredGradient>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RegisteredGradient& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const RegisteredGradient& from) {
    RegisteredGradient::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RegisteredGradient* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.RegisteredGradient";
  }
  protected:
  explicit RegisteredGradient(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kGradientFuncFieldNumber = 1,
    kRegisteredOpTypeFieldNumber = 2,
  };
  // string gradient_func = 1;
  void clear_gradient_func();
  const std::string& gradient_func() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_gradient_func(ArgT0&& arg0, ArgT... args);
  std::string* mutable_gradient_func();
  PROTOBUF_NODISCARD std::string* release_gradient_func();
  void set_allocated_gradient_func(std::string* gradient_func);
  private:
  const std::string& _internal_gradient_func() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_gradient_func(const std::string& value);
  std::string* _internal_mutable_gradient_func();
  public:

  // string registered_op_type = 2;
  void clear_registered_op_type();
  const std::string& registered_op_type() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_registered_op_type(ArgT0&& arg0, ArgT... args);
  std::string* mutable_registered_op_type();
  PROTOBUF_NODISCARD std::string* release_registered_op_type();
  void set_allocated_registered_op_type(std::string* registered_op_type);
  private:
  const std::string& _internal_registered_op_type() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_registered_op_type(const std::string& value);
  std::string* _internal_mutable_registered_op_type();
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.RegisteredGradient)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr gradient_func_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr registered_op_type_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2ffunction_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// FunctionDefLibrary

// repeated .tensorflow.FunctionDef function = 1;
inline int FunctionDefLibrary::_internal_function_size() const {
  return _impl_.function_.size();
}
inline int FunctionDefLibrary::function_size() const {
  return _internal_function_size();
}
inline void FunctionDefLibrary::clear_function() {
  _impl_.function_.Clear();
}
inline ::tensorflow::FunctionDef* FunctionDefLibrary::mutable_function(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.FunctionDefLibrary.function)
  return _impl_.function_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::FunctionDef >*
FunctionDefLibrary::mutable_function() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.FunctionDefLibrary.function)
  return &_impl_.function_;
}
inline const ::tensorflow::FunctionDef& FunctionDefLibrary::_internal_function(int index) const {
  return _impl_.function_.Get(index);
}
inline const ::tensorflow::FunctionDef& FunctionDefLibrary::function(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.FunctionDefLibrary.function)
  return _internal_function(index);
}
inline ::tensorflow::FunctionDef* FunctionDefLibrary::_internal_add_function() {
  return _impl_.function_.Add();
}
inline ::tensorflow::FunctionDef* FunctionDefLibrary::add_function() {
  ::tensorflow::FunctionDef* _add = _internal_add_function();
  // @@protoc_insertion_point(field_add:tensorflow.FunctionDefLibrary.function)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::FunctionDef >&
FunctionDefLibrary::function() const {
  // @@protoc_insertion_point(field_list:tensorflow.FunctionDefLibrary.function)
  return _impl_.function_;
}

// repeated .tensorflow.GradientDef gradient = 2;
inline int FunctionDefLibrary::_internal_gradient_size() const {
  return _impl_.gradient_.size();
}
inline int FunctionDefLibrary::gradient_size() const {
  return _internal_gradient_size();
}
inline void FunctionDefLibrary::clear_gradient() {
  _impl_.gradient_.Clear();
}
inline ::tensorflow::GradientDef* FunctionDefLibrary::mutable_gradient(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.FunctionDefLibrary.gradient)
  return _impl_.gradient_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GradientDef >*
FunctionDefLibrary::mutable_gradient() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.FunctionDefLibrary.gradient)
  return &_impl_.gradient_;
}
inline const ::tensorflow::GradientDef& FunctionDefLibrary::_internal_gradient(int index) const {
  return _impl_.gradient_.Get(index);
}
inline const ::tensorflow::GradientDef& FunctionDefLibrary::gradient(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.FunctionDefLibrary.gradient)
  return _internal_gradient(index);
}
inline ::tensorflow::GradientDef* FunctionDefLibrary::_internal_add_gradient() {
  return _impl_.gradient_.Add();
}
inline ::tensorflow::GradientDef* FunctionDefLibrary::add_gradient() {
  ::tensorflow::GradientDef* _add = _internal_add_gradient();
  // @@protoc_insertion_point(field_add:tensorflow.FunctionDefLibrary.gradient)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GradientDef >&
FunctionDefLibrary::gradient() const {
  // @@protoc_insertion_point(field_list:tensorflow.FunctionDefLibrary.gradient)
  return _impl_.gradient_;
}

// repeated .tensorflow.RegisteredGradient registered_gradients = 3;
inline int FunctionDefLibrary::_internal_registered_gradients_size() const {
  return _impl_.registered_gradients_.size();
}
inline int FunctionDefLibrary::registered_gradients_size() const {
  return _internal_registered_gradients_size();
}
inline void FunctionDefLibrary::clear_registered_gradients() {
  _impl_.registered_gradients_.Clear();
}
inline ::tensorflow::RegisteredGradient* FunctionDefLibrary::mutable_registered_gradients(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.FunctionDefLibrary.registered_gradients)
  return _impl_.registered_gradients_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::RegisteredGradient >*
FunctionDefLibrary::mutable_registered_gradients() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.FunctionDefLibrary.registered_gradients)
  return &_impl_.registered_gradients_;
}
inline const ::tensorflow::RegisteredGradient& FunctionDefLibrary::_internal_registered_gradients(int index) const {
  return _impl_.registered_gradients_.Get(index);
}
inline const ::tensorflow::RegisteredGradient& FunctionDefLibrary::registered_gradients(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.FunctionDefLibrary.registered_gradients)
  return _internal_registered_gradients(index);
}
inline ::tensorflow::RegisteredGradient* FunctionDefLibrary::_internal_add_registered_gradients() {
  return _impl_.registered_gradients_.Add();
}
inline ::tensorflow::RegisteredGradient* FunctionDefLibrary::add_registered_gradients() {
  ::tensorflow::RegisteredGradient* _add = _internal_add_registered_gradients();
  // @@protoc_insertion_point(field_add:tensorflow.FunctionDefLibrary.registered_gradients)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::RegisteredGradient >&
FunctionDefLibrary::registered_gradients() const {
  // @@protoc_insertion_point(field_list:tensorflow.FunctionDefLibrary.registered_gradients)
  return _impl_.registered_gradients_;
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// FunctionDef_ArgAttrs

// map<string, .tensorflow.AttrValue> attr = 1;
inline int FunctionDef_ArgAttrs::_internal_attr_size() const {
  return _impl_.attr_.size();
}
inline int FunctionDef_ArgAttrs::attr_size() const {
  return _internal_attr_size();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >&
FunctionDef_ArgAttrs::_internal_attr() const {
  return _impl_.attr_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >&
FunctionDef_ArgAttrs::attr() const {
  // @@protoc_insertion_point(field_map:tensorflow.FunctionDef.ArgAttrs.attr)
  return _internal_attr();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >*
FunctionDef_ArgAttrs::_internal_mutable_attr() {
  return _impl_.attr_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >*
FunctionDef_ArgAttrs::mutable_attr() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.FunctionDef.ArgAttrs.attr)
  return _internal_mutable_attr();
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// FunctionDef

// .tensorflow.OpDef signature = 1;
inline bool FunctionDef::_internal_has_signature() const {
  return this != internal_default_instance() && _impl_.signature_ != nullptr;
}
inline bool FunctionDef::has_signature() const {
  return _internal_has_signature();
}
inline const ::tensorflow::OpDef& FunctionDef::_internal_signature() const {
  const ::tensorflow::OpDef* p = _impl_.signature_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::OpDef&>(
      ::tensorflow::_OpDef_default_instance_);
}
inline const ::tensorflow::OpDef& FunctionDef::signature() const {
  // @@protoc_insertion_point(field_get:tensorflow.FunctionDef.signature)
  return _internal_signature();
}
inline void FunctionDef::unsafe_arena_set_allocated_signature(
    ::tensorflow::OpDef* signature) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.signature_);
  }
  _impl_.signature_ = signature;
  if (signature) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.FunctionDef.signature)
}
inline ::tensorflow::OpDef* FunctionDef::release_signature() {
  
  ::tensorflow::OpDef* temp = _impl_.signature_;
  _impl_.signature_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::OpDef* FunctionDef::unsafe_arena_release_signature() {
  // @@protoc_insertion_point(field_release:tensorflow.FunctionDef.signature)
  
  ::tensorflow::OpDef* temp = _impl_.signature_;
  _impl_.signature_ = nullptr;
  return temp;
}
inline ::tensorflow::OpDef* FunctionDef::_internal_mutable_signature() {
  
  if (_impl_.signature_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::OpDef>(GetArenaForAllocation());
    _impl_.signature_ = p;
  }
  return _impl_.signature_;
}
inline ::tensorflow::OpDef* FunctionDef::mutable_signature() {
  ::tensorflow::OpDef* _msg = _internal_mutable_signature();
  // @@protoc_insertion_point(field_mutable:tensorflow.FunctionDef.signature)
  return _msg;
}
inline void FunctionDef::set_allocated_signature(::tensorflow::OpDef* signature) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.signature_);
  }
  if (signature) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(signature));
    if (message_arena != submessage_arena) {
      signature = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, signature, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.signature_ = signature;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.FunctionDef.signature)
}

// map<string, .tensorflow.AttrValue> attr = 5;
inline int FunctionDef::_internal_attr_size() const {
  return _impl_.attr_.size();
}
inline int FunctionDef::attr_size() const {
  return _internal_attr_size();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >&
FunctionDef::_internal_attr() const {
  return _impl_.attr_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >&
FunctionDef::attr() const {
  // @@protoc_insertion_point(field_map:tensorflow.FunctionDef.attr)
  return _internal_attr();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >*
FunctionDef::_internal_mutable_attr() {
  return _impl_.attr_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >*
FunctionDef::mutable_attr() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.FunctionDef.attr)
  return _internal_mutable_attr();
}

// map<uint32, .tensorflow.FunctionDef.ArgAttrs> arg_attr = 7;
inline int FunctionDef::_internal_arg_attr_size() const {
  return _impl_.arg_attr_.size();
}
inline int FunctionDef::arg_attr_size() const {
  return _internal_arg_attr_size();
}
inline void FunctionDef::clear_arg_attr() {
  _impl_.arg_attr_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< uint32_t, ::tensorflow::FunctionDef_ArgAttrs >&
FunctionDef::_internal_arg_attr() const {
  return _impl_.arg_attr_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< uint32_t, ::tensorflow::FunctionDef_ArgAttrs >&
FunctionDef::arg_attr() const {
  // @@protoc_insertion_point(field_map:tensorflow.FunctionDef.arg_attr)
  return _internal_arg_attr();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< uint32_t, ::tensorflow::FunctionDef_ArgAttrs >*
FunctionDef::_internal_mutable_arg_attr() {
  return _impl_.arg_attr_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< uint32_t, ::tensorflow::FunctionDef_ArgAttrs >*
FunctionDef::mutable_arg_attr() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.FunctionDef.arg_attr)
  return _internal_mutable_arg_attr();
}

// map<uint32, uint32> resource_arg_unique_id = 8;
inline int FunctionDef::_internal_resource_arg_unique_id_size() const {
  return _impl_.resource_arg_unique_id_.size();
}
inline int FunctionDef::resource_arg_unique_id_size() const {
  return _internal_resource_arg_unique_id_size();
}
inline void FunctionDef::clear_resource_arg_unique_id() {
  _impl_.resource_arg_unique_id_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< uint32_t, uint32_t >&
FunctionDef::_internal_resource_arg_unique_id() const {
  return _impl_.resource_arg_unique_id_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< uint32_t, uint32_t >&
FunctionDef::resource_arg_unique_id() const {
  // @@protoc_insertion_point(field_map:tensorflow.FunctionDef.resource_arg_unique_id)
  return _internal_resource_arg_unique_id();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< uint32_t, uint32_t >*
FunctionDef::_internal_mutable_resource_arg_unique_id() {
  return _impl_.resource_arg_unique_id_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< uint32_t, uint32_t >*
FunctionDef::mutable_resource_arg_unique_id() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.FunctionDef.resource_arg_unique_id)
  return _internal_mutable_resource_arg_unique_id();
}

// repeated .tensorflow.NodeDef node_def = 3;
inline int FunctionDef::_internal_node_def_size() const {
  return _impl_.node_def_.size();
}
inline int FunctionDef::node_def_size() const {
  return _internal_node_def_size();
}
inline ::tensorflow::NodeDef* FunctionDef::mutable_node_def(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.FunctionDef.node_def)
  return _impl_.node_def_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NodeDef >*
FunctionDef::mutable_node_def() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.FunctionDef.node_def)
  return &_impl_.node_def_;
}
inline const ::tensorflow::NodeDef& FunctionDef::_internal_node_def(int index) const {
  return _impl_.node_def_.Get(index);
}
inline const ::tensorflow::NodeDef& FunctionDef::node_def(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.FunctionDef.node_def)
  return _internal_node_def(index);
}
inline ::tensorflow::NodeDef* FunctionDef::_internal_add_node_def() {
  return _impl_.node_def_.Add();
}
inline ::tensorflow::NodeDef* FunctionDef::add_node_def() {
  ::tensorflow::NodeDef* _add = _internal_add_node_def();
  // @@protoc_insertion_point(field_add:tensorflow.FunctionDef.node_def)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NodeDef >&
FunctionDef::node_def() const {
  // @@protoc_insertion_point(field_list:tensorflow.FunctionDef.node_def)
  return _impl_.node_def_;
}

// map<string, string> ret = 4;
inline int FunctionDef::_internal_ret_size() const {
  return _impl_.ret_.size();
}
inline int FunctionDef::ret_size() const {
  return _internal_ret_size();
}
inline void FunctionDef::clear_ret() {
  _impl_.ret_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
FunctionDef::_internal_ret() const {
  return _impl_.ret_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
FunctionDef::ret() const {
  // @@protoc_insertion_point(field_map:tensorflow.FunctionDef.ret)
  return _internal_ret();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
FunctionDef::_internal_mutable_ret() {
  return _impl_.ret_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
FunctionDef::mutable_ret() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.FunctionDef.ret)
  return _internal_mutable_ret();
}

// map<string, string> control_ret = 6;
inline int FunctionDef::_internal_control_ret_size() const {
  return _impl_.control_ret_.size();
}
inline int FunctionDef::control_ret_size() const {
  return _internal_control_ret_size();
}
inline void FunctionDef::clear_control_ret() {
  _impl_.control_ret_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
FunctionDef::_internal_control_ret() const {
  return _impl_.control_ret_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
FunctionDef::control_ret() const {
  // @@protoc_insertion_point(field_map:tensorflow.FunctionDef.control_ret)
  return _internal_control_ret();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
FunctionDef::_internal_mutable_control_ret() {
  return _impl_.control_ret_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
FunctionDef::mutable_control_ret() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.FunctionDef.control_ret)
  return _internal_mutable_control_ret();
}

// -------------------------------------------------------------------

// GradientDef

// string function_name = 1;
inline void GradientDef::clear_function_name() {
  _impl_.function_name_.ClearToEmpty();
}
inline const std::string& GradientDef::function_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.GradientDef.function_name)
  return _internal_function_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GradientDef::set_function_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.function_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.GradientDef.function_name)
}
inline std::string* GradientDef::mutable_function_name() {
  std::string* _s = _internal_mutable_function_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.GradientDef.function_name)
  return _s;
}
inline const std::string& GradientDef::_internal_function_name() const {
  return _impl_.function_name_.Get();
}
inline void GradientDef::_internal_set_function_name(const std::string& value) {
  
  _impl_.function_name_.Set(value, GetArenaForAllocation());
}
inline std::string* GradientDef::_internal_mutable_function_name() {
  
  return _impl_.function_name_.Mutable(GetArenaForAllocation());
}
inline std::string* GradientDef::release_function_name() {
  // @@protoc_insertion_point(field_release:tensorflow.GradientDef.function_name)
  return _impl_.function_name_.Release();
}
inline void GradientDef::set_allocated_function_name(std::string* function_name) {
  if (function_name != nullptr) {
    
  } else {
    
  }
  _impl_.function_name_.SetAllocated(function_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.function_name_.IsDefault()) {
    _impl_.function_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GradientDef.function_name)
}

// string gradient_func = 2;
inline void GradientDef::clear_gradient_func() {
  _impl_.gradient_func_.ClearToEmpty();
}
inline const std::string& GradientDef::gradient_func() const {
  // @@protoc_insertion_point(field_get:tensorflow.GradientDef.gradient_func)
  return _internal_gradient_func();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GradientDef::set_gradient_func(ArgT0&& arg0, ArgT... args) {
 
 _impl_.gradient_func_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.GradientDef.gradient_func)
}
inline std::string* GradientDef::mutable_gradient_func() {
  std::string* _s = _internal_mutable_gradient_func();
  // @@protoc_insertion_point(field_mutable:tensorflow.GradientDef.gradient_func)
  return _s;
}
inline const std::string& GradientDef::_internal_gradient_func() const {
  return _impl_.gradient_func_.Get();
}
inline void GradientDef::_internal_set_gradient_func(const std::string& value) {
  
  _impl_.gradient_func_.Set(value, GetArenaForAllocation());
}
inline std::string* GradientDef::_internal_mutable_gradient_func() {
  
  return _impl_.gradient_func_.Mutable(GetArenaForAllocation());
}
inline std::string* GradientDef::release_gradient_func() {
  // @@protoc_insertion_point(field_release:tensorflow.GradientDef.gradient_func)
  return _impl_.gradient_func_.Release();
}
inline void GradientDef::set_allocated_gradient_func(std::string* gradient_func) {
  if (gradient_func != nullptr) {
    
  } else {
    
  }
  _impl_.gradient_func_.SetAllocated(gradient_func, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.gradient_func_.IsDefault()) {
    _impl_.gradient_func_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GradientDef.gradient_func)
}

// -------------------------------------------------------------------

// RegisteredGradient

// string gradient_func = 1;
inline void RegisteredGradient::clear_gradient_func() {
  _impl_.gradient_func_.ClearToEmpty();
}
inline const std::string& RegisteredGradient::gradient_func() const {
  // @@protoc_insertion_point(field_get:tensorflow.RegisteredGradient.gradient_func)
  return _internal_gradient_func();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RegisteredGradient::set_gradient_func(ArgT0&& arg0, ArgT... args) {
 
 _impl_.gradient_func_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.RegisteredGradient.gradient_func)
}
inline std::string* RegisteredGradient::mutable_gradient_func() {
  std::string* _s = _internal_mutable_gradient_func();
  // @@protoc_insertion_point(field_mutable:tensorflow.RegisteredGradient.gradient_func)
  return _s;
}
inline const std::string& RegisteredGradient::_internal_gradient_func() const {
  return _impl_.gradient_func_.Get();
}
inline void RegisteredGradient::_internal_set_gradient_func(const std::string& value) {
  
  _impl_.gradient_func_.Set(value, GetArenaForAllocation());
}
inline std::string* RegisteredGradient::_internal_mutable_gradient_func() {
  
  return _impl_.gradient_func_.Mutable(GetArenaForAllocation());
}
inline std::string* RegisteredGradient::release_gradient_func() {
  // @@protoc_insertion_point(field_release:tensorflow.RegisteredGradient.gradient_func)
  return _impl_.gradient_func_.Release();
}
inline void RegisteredGradient::set_allocated_gradient_func(std::string* gradient_func) {
  if (gradient_func != nullptr) {
    
  } else {
    
  }
  _impl_.gradient_func_.SetAllocated(gradient_func, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.gradient_func_.IsDefault()) {
    _impl_.gradient_func_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RegisteredGradient.gradient_func)
}

// string registered_op_type = 2;
inline void RegisteredGradient::clear_registered_op_type() {
  _impl_.registered_op_type_.ClearToEmpty();
}
inline const std::string& RegisteredGradient::registered_op_type() const {
  // @@protoc_insertion_point(field_get:tensorflow.RegisteredGradient.registered_op_type)
  return _internal_registered_op_type();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RegisteredGradient::set_registered_op_type(ArgT0&& arg0, ArgT... args) {
 
 _impl_.registered_op_type_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.RegisteredGradient.registered_op_type)
}
inline std::string* RegisteredGradient::mutable_registered_op_type() {
  std::string* _s = _internal_mutable_registered_op_type();
  // @@protoc_insertion_point(field_mutable:tensorflow.RegisteredGradient.registered_op_type)
  return _s;
}
inline const std::string& RegisteredGradient::_internal_registered_op_type() const {
  return _impl_.registered_op_type_.Get();
}
inline void RegisteredGradient::_internal_set_registered_op_type(const std::string& value) {
  
  _impl_.registered_op_type_.Set(value, GetArenaForAllocation());
}
inline std::string* RegisteredGradient::_internal_mutable_registered_op_type() {
  
  return _impl_.registered_op_type_.Mutable(GetArenaForAllocation());
}
inline std::string* RegisteredGradient::release_registered_op_type() {
  // @@protoc_insertion_point(field_release:tensorflow.RegisteredGradient.registered_op_type)
  return _impl_.registered_op_type_.Release();
}
inline void RegisteredGradient::set_allocated_registered_op_type(std::string* registered_op_type) {
  if (registered_op_type != nullptr) {
    
  } else {
    
  }
  _impl_.registered_op_type_.SetAllocated(registered_op_type, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.registered_op_type_.IsDefault()) {
    _impl_.registered_op_type_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RegisteredGradient.registered_op_type)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2ffunction_2eproto
