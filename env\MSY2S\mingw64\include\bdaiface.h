/*** Autogenerated by WIDL 10.8 from include/bdaiface.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __bdaiface_h__
#define __bdaiface_h__

/* Forward declarations */

#ifndef __IBDA_NetworkProvider_FWD_DEFINED__
#define __IBDA_NetworkProvider_FWD_DEFINED__
typedef interface IBDA_NetworkProvider IBDA_NetworkProvider;
#ifdef __cplusplus
interface IBDA_NetworkProvider;
#endif /* __cplusplus */
#endif

#ifndef __IBDA_EthernetFilter_FWD_DEFINED__
#define __IBDA_EthernetFilter_FWD_DEFINED__
typedef interface IBDA_EthernetFilter IBDA_EthernetFilter;
#ifdef __cplusplus
interface IBDA_EthernetFilter;
#endif /* __cplusplus */
#endif

#ifndef __IBDA_IPV4Filter_FWD_DEFINED__
#define __IBDA_IPV4Filter_FWD_DEFINED__
typedef interface IBDA_IPV4Filter IBDA_IPV4Filter;
#ifdef __cplusplus
interface IBDA_IPV4Filter;
#endif /* __cplusplus */
#endif

#ifndef __IBDA_IPV6Filter_FWD_DEFINED__
#define __IBDA_IPV6Filter_FWD_DEFINED__
typedef interface IBDA_IPV6Filter IBDA_IPV6Filter;
#ifdef __cplusplus
interface IBDA_IPV6Filter;
#endif /* __cplusplus */
#endif

#ifndef __IBDA_DeviceControl_FWD_DEFINED__
#define __IBDA_DeviceControl_FWD_DEFINED__
typedef interface IBDA_DeviceControl IBDA_DeviceControl;
#ifdef __cplusplus
interface IBDA_DeviceControl;
#endif /* __cplusplus */
#endif

#ifndef __IBDA_PinControl_FWD_DEFINED__
#define __IBDA_PinControl_FWD_DEFINED__
typedef interface IBDA_PinControl IBDA_PinControl;
#ifdef __cplusplus
interface IBDA_PinControl;
#endif /* __cplusplus */
#endif

#ifndef __IBDA_SignalProperties_FWD_DEFINED__
#define __IBDA_SignalProperties_FWD_DEFINED__
typedef interface IBDA_SignalProperties IBDA_SignalProperties;
#ifdef __cplusplus
interface IBDA_SignalProperties;
#endif /* __cplusplus */
#endif

#ifndef __IBDA_SignalStatistics_FWD_DEFINED__
#define __IBDA_SignalStatistics_FWD_DEFINED__
typedef interface IBDA_SignalStatistics IBDA_SignalStatistics;
#ifdef __cplusplus
interface IBDA_SignalStatistics;
#endif /* __cplusplus */
#endif

#ifndef __IBDA_Topology_FWD_DEFINED__
#define __IBDA_Topology_FWD_DEFINED__
typedef interface IBDA_Topology IBDA_Topology;
#ifdef __cplusplus
interface IBDA_Topology;
#endif /* __cplusplus */
#endif

#ifndef __IBDA_VoidTransform_FWD_DEFINED__
#define __IBDA_VoidTransform_FWD_DEFINED__
typedef interface IBDA_VoidTransform IBDA_VoidTransform;
#ifdef __cplusplus
interface IBDA_VoidTransform;
#endif /* __cplusplus */
#endif

#ifndef __IBDA_NullTransform_FWD_DEFINED__
#define __IBDA_NullTransform_FWD_DEFINED__
typedef interface IBDA_NullTransform IBDA_NullTransform;
#ifdef __cplusplus
interface IBDA_NullTransform;
#endif /* __cplusplus */
#endif

#ifndef __IBDA_FrequencyFilter_FWD_DEFINED__
#define __IBDA_FrequencyFilter_FWD_DEFINED__
typedef interface IBDA_FrequencyFilter IBDA_FrequencyFilter;
#ifdef __cplusplus
interface IBDA_FrequencyFilter;
#endif /* __cplusplus */
#endif

#ifndef __IBDA_LNBInfo_FWD_DEFINED__
#define __IBDA_LNBInfo_FWD_DEFINED__
typedef interface IBDA_LNBInfo IBDA_LNBInfo;
#ifdef __cplusplus
interface IBDA_LNBInfo;
#endif /* __cplusplus */
#endif

#ifndef __IBDA_DiseqCommand_FWD_DEFINED__
#define __IBDA_DiseqCommand_FWD_DEFINED__
typedef interface IBDA_DiseqCommand IBDA_DiseqCommand;
#ifdef __cplusplus
interface IBDA_DiseqCommand;
#endif /* __cplusplus */
#endif

#ifndef __IBDA_AutoDemodulate_FWD_DEFINED__
#define __IBDA_AutoDemodulate_FWD_DEFINED__
typedef interface IBDA_AutoDemodulate IBDA_AutoDemodulate;
#ifdef __cplusplus
interface IBDA_AutoDemodulate;
#endif /* __cplusplus */
#endif

#ifndef __IBDA_AutoDemodulateEx_FWD_DEFINED__
#define __IBDA_AutoDemodulateEx_FWD_DEFINED__
typedef interface IBDA_AutoDemodulateEx IBDA_AutoDemodulateEx;
#ifdef __cplusplus
interface IBDA_AutoDemodulateEx;
#endif /* __cplusplus */
#endif

#ifndef __IBDA_DigitalDemodulator_FWD_DEFINED__
#define __IBDA_DigitalDemodulator_FWD_DEFINED__
typedef interface IBDA_DigitalDemodulator IBDA_DigitalDemodulator;
#ifdef __cplusplus
interface IBDA_DigitalDemodulator;
#endif /* __cplusplus */
#endif

#ifndef __IBDA_DigitalDemodulator2_FWD_DEFINED__
#define __IBDA_DigitalDemodulator2_FWD_DEFINED__
typedef interface IBDA_DigitalDemodulator2 IBDA_DigitalDemodulator2;
#ifdef __cplusplus
interface IBDA_DigitalDemodulator2;
#endif /* __cplusplus */
#endif

#ifndef __IBDA_DigitalDemodulator3_FWD_DEFINED__
#define __IBDA_DigitalDemodulator3_FWD_DEFINED__
typedef interface IBDA_DigitalDemodulator3 IBDA_DigitalDemodulator3;
#ifdef __cplusplus
interface IBDA_DigitalDemodulator3;
#endif /* __cplusplus */
#endif

#ifndef __ICCSubStreamFiltering_FWD_DEFINED__
#define __ICCSubStreamFiltering_FWD_DEFINED__
typedef interface ICCSubStreamFiltering ICCSubStreamFiltering;
#ifdef __cplusplus
interface ICCSubStreamFiltering;
#endif /* __cplusplus */
#endif

#ifndef __IBDA_IPSinkControl_FWD_DEFINED__
#define __IBDA_IPSinkControl_FWD_DEFINED__
typedef interface IBDA_IPSinkControl IBDA_IPSinkControl;
#ifdef __cplusplus
interface IBDA_IPSinkControl;
#endif /* __cplusplus */
#endif

#ifndef __IBDA_IPSinkInfo_FWD_DEFINED__
#define __IBDA_IPSinkInfo_FWD_DEFINED__
typedef interface IBDA_IPSinkInfo IBDA_IPSinkInfo;
#ifdef __cplusplus
interface IBDA_IPSinkInfo;
#endif /* __cplusplus */
#endif

#ifndef __IEnumPIDMap_FWD_DEFINED__
#define __IEnumPIDMap_FWD_DEFINED__
typedef interface IEnumPIDMap IEnumPIDMap;
#ifdef __cplusplus
interface IEnumPIDMap;
#endif /* __cplusplus */
#endif

#ifndef __IMPEG2PIDMap_FWD_DEFINED__
#define __IMPEG2PIDMap_FWD_DEFINED__
typedef interface IMPEG2PIDMap IMPEG2PIDMap;
#ifdef __cplusplus
interface IMPEG2PIDMap;
#endif /* __cplusplus */
#endif

#ifndef __IFrequencyMap_FWD_DEFINED__
#define __IFrequencyMap_FWD_DEFINED__
typedef interface IFrequencyMap IFrequencyMap;
#ifdef __cplusplus
interface IFrequencyMap;
#endif /* __cplusplus */
#endif

#ifndef __IBDA_EasMessage_FWD_DEFINED__
#define __IBDA_EasMessage_FWD_DEFINED__
typedef interface IBDA_EasMessage IBDA_EasMessage;
#ifdef __cplusplus
interface IBDA_EasMessage;
#endif /* __cplusplus */
#endif

#ifndef __IBDA_TransportStreamInfo_FWD_DEFINED__
#define __IBDA_TransportStreamInfo_FWD_DEFINED__
typedef interface IBDA_TransportStreamInfo IBDA_TransportStreamInfo;
#ifdef __cplusplus
interface IBDA_TransportStreamInfo;
#endif /* __cplusplus */
#endif

#ifndef __IBDA_ConditionalAccess_FWD_DEFINED__
#define __IBDA_ConditionalAccess_FWD_DEFINED__
typedef interface IBDA_ConditionalAccess IBDA_ConditionalAccess;
#ifdef __cplusplus
interface IBDA_ConditionalAccess;
#endif /* __cplusplus */
#endif

#ifndef __IBDA_DiagnosticProperties_FWD_DEFINED__
#define __IBDA_DiagnosticProperties_FWD_DEFINED__
typedef interface IBDA_DiagnosticProperties IBDA_DiagnosticProperties;
#ifdef __cplusplus
interface IBDA_DiagnosticProperties;
#endif /* __cplusplus */
#endif

#ifndef __IBDA_DRM_FWD_DEFINED__
#define __IBDA_DRM_FWD_DEFINED__
typedef interface IBDA_DRM IBDA_DRM;
#ifdef __cplusplus
interface IBDA_DRM;
#endif /* __cplusplus */
#endif

#ifndef __IBDA_NameValueService_FWD_DEFINED__
#define __IBDA_NameValueService_FWD_DEFINED__
typedef interface IBDA_NameValueService IBDA_NameValueService;
#ifdef __cplusplus
interface IBDA_NameValueService;
#endif /* __cplusplus */
#endif

#ifndef __IBDA_ConditionalAccessEx_FWD_DEFINED__
#define __IBDA_ConditionalAccessEx_FWD_DEFINED__
typedef interface IBDA_ConditionalAccessEx IBDA_ConditionalAccessEx;
#ifdef __cplusplus
interface IBDA_ConditionalAccessEx;
#endif /* __cplusplus */
#endif

#ifndef __IBDA_ISDBConditionalAccess_FWD_DEFINED__
#define __IBDA_ISDBConditionalAccess_FWD_DEFINED__
typedef interface IBDA_ISDBConditionalAccess IBDA_ISDBConditionalAccess;
#ifdef __cplusplus
interface IBDA_ISDBConditionalAccess;
#endif /* __cplusplus */
#endif

#ifndef __IBDA_EventingService_FWD_DEFINED__
#define __IBDA_EventingService_FWD_DEFINED__
typedef interface IBDA_EventingService IBDA_EventingService;
#ifdef __cplusplus
interface IBDA_EventingService;
#endif /* __cplusplus */
#endif

#ifndef __IBDA_AUX_FWD_DEFINED__
#define __IBDA_AUX_FWD_DEFINED__
typedef interface IBDA_AUX IBDA_AUX;
#ifdef __cplusplus
interface IBDA_AUX;
#endif /* __cplusplus */
#endif

#ifndef __IBDA_Encoder_FWD_DEFINED__
#define __IBDA_Encoder_FWD_DEFINED__
typedef interface IBDA_Encoder IBDA_Encoder;
#ifdef __cplusplus
interface IBDA_Encoder;
#endif /* __cplusplus */
#endif

#ifndef __IBDA_FDC_FWD_DEFINED__
#define __IBDA_FDC_FWD_DEFINED__
typedef interface IBDA_FDC IBDA_FDC;
#ifdef __cplusplus
interface IBDA_FDC;
#endif /* __cplusplus */
#endif

#ifndef __IBDA_GuideDataDeliveryService_FWD_DEFINED__
#define __IBDA_GuideDataDeliveryService_FWD_DEFINED__
typedef interface IBDA_GuideDataDeliveryService IBDA_GuideDataDeliveryService;
#ifdef __cplusplus
interface IBDA_GuideDataDeliveryService;
#endif /* __cplusplus */
#endif

#ifndef __IBDA_DRMService_FWD_DEFINED__
#define __IBDA_DRMService_FWD_DEFINED__
typedef interface IBDA_DRMService IBDA_DRMService;
#ifdef __cplusplus
interface IBDA_DRMService;
#endif /* __cplusplus */
#endif

#ifndef __IBDA_WMDRMSession_FWD_DEFINED__
#define __IBDA_WMDRMSession_FWD_DEFINED__
typedef interface IBDA_WMDRMSession IBDA_WMDRMSession;
#ifdef __cplusplus
interface IBDA_WMDRMSession;
#endif /* __cplusplus */
#endif

#ifndef __IBDA_WMDRMTuner_FWD_DEFINED__
#define __IBDA_WMDRMTuner_FWD_DEFINED__
typedef interface IBDA_WMDRMTuner IBDA_WMDRMTuner;
#ifdef __cplusplus
interface IBDA_WMDRMTuner;
#endif /* __cplusplus */
#endif

#ifndef __IBDA_DRIDRMService_FWD_DEFINED__
#define __IBDA_DRIDRMService_FWD_DEFINED__
typedef interface IBDA_DRIDRMService IBDA_DRIDRMService;
#ifdef __cplusplus
interface IBDA_DRIDRMService;
#endif /* __cplusplus */
#endif

#ifndef __IBDA_DRIWMDRMSession_FWD_DEFINED__
#define __IBDA_DRIWMDRMSession_FWD_DEFINED__
typedef interface IBDA_DRIWMDRMSession IBDA_DRIWMDRMSession;
#ifdef __cplusplus
interface IBDA_DRIWMDRMSession;
#endif /* __cplusplus */
#endif

#ifndef __IBDA_MUX_FWD_DEFINED__
#define __IBDA_MUX_FWD_DEFINED__
typedef interface IBDA_MUX IBDA_MUX;
#ifdef __cplusplus
interface IBDA_MUX;
#endif /* __cplusplus */
#endif

#ifndef __IBDA_TransportStreamSelector_FWD_DEFINED__
#define __IBDA_TransportStreamSelector_FWD_DEFINED__
typedef interface IBDA_TransportStreamSelector IBDA_TransportStreamSelector;
#ifdef __cplusplus
interface IBDA_TransportStreamSelector;
#endif /* __cplusplus */
#endif

#ifndef __IBDA_UserActivityService_FWD_DEFINED__
#define __IBDA_UserActivityService_FWD_DEFINED__
typedef interface IBDA_UserActivityService IBDA_UserActivityService;
#ifdef __cplusplus
interface IBDA_UserActivityService;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <unknwn.h>
#include <strmif.h>
#include <bdatypes.h>

#ifdef __cplusplus
extern "C" {
#endif

#include <winapifamily.h>

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
typedef enum SmartCardStatusType {
    CardInserted = 0,
    CardRemoved = 1,
    CardError = 2,
    CardDataChanged = 3,
    CardFirmwareUpgrade = 4
} SmartCardStatusType;
typedef enum SmartCardAssociationType {
    NotAssociated = 0,
    Associated = 1,
    AssociationUnknown = 2
} SmartCardAssociationType;
typedef enum LocationCodeSchemeType {
    SCTE_18 = 0
} LocationCodeSchemeType;
typedef enum EntitlementType {
    Entitled = 0,
    NotEntitled = 1,
    TechnicalFailure = 2
} EntitlementType;
typedef enum UICloseReasonType {
    NotReady = 0,
    UserClosed = 1,
    SystemClosed = 2,
    DeviceClosed = 3,
    ErrorClosed = 4
} UICloseReasonType;
typedef enum BDA_DrmPairingError {
    BDA_DrmPairing_Succeeded = 0,
    BDA_DrmPairing_HardwareFailure = 1,
    BDA_DrmPairing_NeedRevocationData = 2,
    BDA_DrmPairing_NeedIndiv = 3,
    BDA_DrmPairing_Other = 4,
    BDA_DrmPairing_DrmInitFailed = 5,
    BDA_DrmPairing_DrmNotPaired = 6,
    BDA_DrmPairing_DrmRePairSoon = 7,
    BDA_DrmPairing_Aborted = 8,
    BDA_DrmPairing_NeedSDKUpdate = 9
} BDA_DrmPairingError;
typedef struct EALocationCodeType {
    LocationCodeSchemeType LocationCodeScheme;
    BYTE state_code;
    BYTE county_subdivision;
    WORD county_code;
} EALocationCodeType;
typedef struct SmartCardApplication {
    ApplicationTypeType ApplicationType;
    USHORT ApplicationVersion;
    BSTR pbstrApplicationName;
    BSTR pbstrApplicationURL;
} SmartCardApplication;

#define PBDA_Encoder_Audio_AlgorithmType_MPEG1LayerII 0x0
#define PBDA_Encoder_Audio_AlgorithmType_AC3 0x1

#define PBDA_Encoder_Video_MPEG2PartII 0x0
#define PBDA_Encoder_Video_MPEG4Part10 0x1

#define PBDA_Encoder_Video_AVC 0x1
#define PBDA_Encoder_Video_H264 0x1

#define PBDA_Encoder_BitrateMode_Constant 1
#define PBDA_Encoder_BitrateMode_Variable 2
#define PBDA_Encoder_BitrateMode_Average 3
typedef enum __WIDL_bdaiface_generated_name_00000017 {
    KSPROPERTY_IPSINK_MULTICASTLIST = 0,
    KSPROPERTY_IPSINK_ADAPTER_DESCRIPTION = 1,
    KSPROPERTY_IPSINK_ADAPTER_ADDRESS = 2
} KSPROPERTY_IPSINK;
/*****************************************************************************
 * IBDA_NetworkProvider interface
 */
#ifndef __IBDA_NetworkProvider_INTERFACE_DEFINED__
#define __IBDA_NetworkProvider_INTERFACE_DEFINED__

DEFINE_GUID(IID_IBDA_NetworkProvider, 0xfd501041, 0x8ebe, 0x11ce, 0x81,0x83, 0x00,0xaa,0x00,0x57,0x7d,0xa2);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("fd501041-8ebe-11ce-8183-00aa00577da2")
IBDA_NetworkProvider : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE PutSignalSource(
        ULONG ulSignalSource) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSignalSource(
        ULONG *pulSignalSource) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetNetworkType(
        GUID *pguidNetworkType) = 0;

    virtual HRESULT STDMETHODCALLTYPE PutTuningSpace(
        REFGUID guidTuningSpace) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTuningSpace(
        GUID *pguidTuingSpace) = 0;

    virtual HRESULT STDMETHODCALLTYPE RegisterDeviceFilter(
        IUnknown *pUnkFilterControl,
        ULONG *ppvRegisitrationContext) = 0;

    virtual HRESULT STDMETHODCALLTYPE UnRegisterDeviceFilter(
        ULONG pvRegistrationContext) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IBDA_NetworkProvider, 0xfd501041, 0x8ebe, 0x11ce, 0x81,0x83, 0x00,0xaa,0x00,0x57,0x7d,0xa2)
#endif
#else
typedef struct IBDA_NetworkProviderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IBDA_NetworkProvider *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IBDA_NetworkProvider *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IBDA_NetworkProvider *This);

    /*** IBDA_NetworkProvider methods ***/
    HRESULT (STDMETHODCALLTYPE *PutSignalSource)(
        IBDA_NetworkProvider *This,
        ULONG ulSignalSource);

    HRESULT (STDMETHODCALLTYPE *GetSignalSource)(
        IBDA_NetworkProvider *This,
        ULONG *pulSignalSource);

    HRESULT (STDMETHODCALLTYPE *GetNetworkType)(
        IBDA_NetworkProvider *This,
        GUID *pguidNetworkType);

    HRESULT (STDMETHODCALLTYPE *PutTuningSpace)(
        IBDA_NetworkProvider *This,
        REFGUID guidTuningSpace);

    HRESULT (STDMETHODCALLTYPE *GetTuningSpace)(
        IBDA_NetworkProvider *This,
        GUID *pguidTuingSpace);

    HRESULT (STDMETHODCALLTYPE *RegisterDeviceFilter)(
        IBDA_NetworkProvider *This,
        IUnknown *pUnkFilterControl,
        ULONG *ppvRegisitrationContext);

    HRESULT (STDMETHODCALLTYPE *UnRegisterDeviceFilter)(
        IBDA_NetworkProvider *This,
        ULONG pvRegistrationContext);

    END_INTERFACE
} IBDA_NetworkProviderVtbl;

interface IBDA_NetworkProvider {
    CONST_VTBL IBDA_NetworkProviderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IBDA_NetworkProvider_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IBDA_NetworkProvider_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IBDA_NetworkProvider_Release(This) (This)->lpVtbl->Release(This)
/*** IBDA_NetworkProvider methods ***/
#define IBDA_NetworkProvider_PutSignalSource(This,ulSignalSource) (This)->lpVtbl->PutSignalSource(This,ulSignalSource)
#define IBDA_NetworkProvider_GetSignalSource(This,pulSignalSource) (This)->lpVtbl->GetSignalSource(This,pulSignalSource)
#define IBDA_NetworkProvider_GetNetworkType(This,pguidNetworkType) (This)->lpVtbl->GetNetworkType(This,pguidNetworkType)
#define IBDA_NetworkProvider_PutTuningSpace(This,guidTuningSpace) (This)->lpVtbl->PutTuningSpace(This,guidTuningSpace)
#define IBDA_NetworkProvider_GetTuningSpace(This,pguidTuingSpace) (This)->lpVtbl->GetTuningSpace(This,pguidTuingSpace)
#define IBDA_NetworkProvider_RegisterDeviceFilter(This,pUnkFilterControl,ppvRegisitrationContext) (This)->lpVtbl->RegisterDeviceFilter(This,pUnkFilterControl,ppvRegisitrationContext)
#define IBDA_NetworkProvider_UnRegisterDeviceFilter(This,pvRegistrationContext) (This)->lpVtbl->UnRegisterDeviceFilter(This,pvRegistrationContext)
#else
/*** IUnknown methods ***/
static inline HRESULT IBDA_NetworkProvider_QueryInterface(IBDA_NetworkProvider* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IBDA_NetworkProvider_AddRef(IBDA_NetworkProvider* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IBDA_NetworkProvider_Release(IBDA_NetworkProvider* This) {
    return This->lpVtbl->Release(This);
}
/*** IBDA_NetworkProvider methods ***/
static inline HRESULT IBDA_NetworkProvider_PutSignalSource(IBDA_NetworkProvider* This,ULONG ulSignalSource) {
    return This->lpVtbl->PutSignalSource(This,ulSignalSource);
}
static inline HRESULT IBDA_NetworkProvider_GetSignalSource(IBDA_NetworkProvider* This,ULONG *pulSignalSource) {
    return This->lpVtbl->GetSignalSource(This,pulSignalSource);
}
static inline HRESULT IBDA_NetworkProvider_GetNetworkType(IBDA_NetworkProvider* This,GUID *pguidNetworkType) {
    return This->lpVtbl->GetNetworkType(This,pguidNetworkType);
}
static inline HRESULT IBDA_NetworkProvider_PutTuningSpace(IBDA_NetworkProvider* This,REFGUID guidTuningSpace) {
    return This->lpVtbl->PutTuningSpace(This,guidTuningSpace);
}
static inline HRESULT IBDA_NetworkProvider_GetTuningSpace(IBDA_NetworkProvider* This,GUID *pguidTuingSpace) {
    return This->lpVtbl->GetTuningSpace(This,pguidTuingSpace);
}
static inline HRESULT IBDA_NetworkProvider_RegisterDeviceFilter(IBDA_NetworkProvider* This,IUnknown *pUnkFilterControl,ULONG *ppvRegisitrationContext) {
    return This->lpVtbl->RegisterDeviceFilter(This,pUnkFilterControl,ppvRegisitrationContext);
}
static inline HRESULT IBDA_NetworkProvider_UnRegisterDeviceFilter(IBDA_NetworkProvider* This,ULONG pvRegistrationContext) {
    return This->lpVtbl->UnRegisterDeviceFilter(This,pvRegistrationContext);
}
#endif
#endif

#endif


#endif  /* __IBDA_NetworkProvider_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IBDA_EthernetFilter interface
 */
#ifndef __IBDA_EthernetFilter_INTERFACE_DEFINED__
#define __IBDA_EthernetFilter_INTERFACE_DEFINED__

DEFINE_GUID(IID_IBDA_EthernetFilter, 0x71985f43, 0x1ca1, 0x11d3, 0x9c,0xc8, 0x00,0xc0,0x4f,0x79,0x71,0xe0);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("71985f43-1ca1-11d3-9cc8-00c04f7971e0")
IBDA_EthernetFilter : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetMulticastListSize(
        ULONG *pulcbAddresses) = 0;

    virtual HRESULT STDMETHODCALLTYPE PutMulticastList(
        ULONG ulcbAddresses,
        BYTE pAddressList[]) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMulticastList(
        ULONG *pulcbAddresses,
        BYTE pAddressList[]) = 0;

    virtual HRESULT STDMETHODCALLTYPE PutMulticastMode(
        ULONG ulModeMask) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMulticastMode(
        ULONG *pulModeMask) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IBDA_EthernetFilter, 0x71985f43, 0x1ca1, 0x11d3, 0x9c,0xc8, 0x00,0xc0,0x4f,0x79,0x71,0xe0)
#endif
#else
typedef struct IBDA_EthernetFilterVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IBDA_EthernetFilter *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IBDA_EthernetFilter *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IBDA_EthernetFilter *This);

    /*** IBDA_EthernetFilter methods ***/
    HRESULT (STDMETHODCALLTYPE *GetMulticastListSize)(
        IBDA_EthernetFilter *This,
        ULONG *pulcbAddresses);

    HRESULT (STDMETHODCALLTYPE *PutMulticastList)(
        IBDA_EthernetFilter *This,
        ULONG ulcbAddresses,
        BYTE pAddressList[]);

    HRESULT (STDMETHODCALLTYPE *GetMulticastList)(
        IBDA_EthernetFilter *This,
        ULONG *pulcbAddresses,
        BYTE pAddressList[]);

    HRESULT (STDMETHODCALLTYPE *PutMulticastMode)(
        IBDA_EthernetFilter *This,
        ULONG ulModeMask);

    HRESULT (STDMETHODCALLTYPE *GetMulticastMode)(
        IBDA_EthernetFilter *This,
        ULONG *pulModeMask);

    END_INTERFACE
} IBDA_EthernetFilterVtbl;

interface IBDA_EthernetFilter {
    CONST_VTBL IBDA_EthernetFilterVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IBDA_EthernetFilter_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IBDA_EthernetFilter_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IBDA_EthernetFilter_Release(This) (This)->lpVtbl->Release(This)
/*** IBDA_EthernetFilter methods ***/
#define IBDA_EthernetFilter_GetMulticastListSize(This,pulcbAddresses) (This)->lpVtbl->GetMulticastListSize(This,pulcbAddresses)
#define IBDA_EthernetFilter_PutMulticastList(This,ulcbAddresses,pAddressList) (This)->lpVtbl->PutMulticastList(This,ulcbAddresses,pAddressList)
#define IBDA_EthernetFilter_GetMulticastList(This,pulcbAddresses,pAddressList) (This)->lpVtbl->GetMulticastList(This,pulcbAddresses,pAddressList)
#define IBDA_EthernetFilter_PutMulticastMode(This,ulModeMask) (This)->lpVtbl->PutMulticastMode(This,ulModeMask)
#define IBDA_EthernetFilter_GetMulticastMode(This,pulModeMask) (This)->lpVtbl->GetMulticastMode(This,pulModeMask)
#else
/*** IUnknown methods ***/
static inline HRESULT IBDA_EthernetFilter_QueryInterface(IBDA_EthernetFilter* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IBDA_EthernetFilter_AddRef(IBDA_EthernetFilter* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IBDA_EthernetFilter_Release(IBDA_EthernetFilter* This) {
    return This->lpVtbl->Release(This);
}
/*** IBDA_EthernetFilter methods ***/
static inline HRESULT IBDA_EthernetFilter_GetMulticastListSize(IBDA_EthernetFilter* This,ULONG *pulcbAddresses) {
    return This->lpVtbl->GetMulticastListSize(This,pulcbAddresses);
}
static inline HRESULT IBDA_EthernetFilter_PutMulticastList(IBDA_EthernetFilter* This,ULONG ulcbAddresses,BYTE pAddressList[]) {
    return This->lpVtbl->PutMulticastList(This,ulcbAddresses,pAddressList);
}
static inline HRESULT IBDA_EthernetFilter_GetMulticastList(IBDA_EthernetFilter* This,ULONG *pulcbAddresses,BYTE pAddressList[]) {
    return This->lpVtbl->GetMulticastList(This,pulcbAddresses,pAddressList);
}
static inline HRESULT IBDA_EthernetFilter_PutMulticastMode(IBDA_EthernetFilter* This,ULONG ulModeMask) {
    return This->lpVtbl->PutMulticastMode(This,ulModeMask);
}
static inline HRESULT IBDA_EthernetFilter_GetMulticastMode(IBDA_EthernetFilter* This,ULONG *pulModeMask) {
    return This->lpVtbl->GetMulticastMode(This,pulModeMask);
}
#endif
#endif

#endif


#endif  /* __IBDA_EthernetFilter_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IBDA_IPV4Filter interface
 */
#ifndef __IBDA_IPV4Filter_INTERFACE_DEFINED__
#define __IBDA_IPV4Filter_INTERFACE_DEFINED__

DEFINE_GUID(IID_IBDA_IPV4Filter, 0x71985f44, 0x1ca1, 0x11d3, 0x9c,0xc8, 0x00,0xc0,0x4f,0x79,0x71,0xe0);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("71985f44-1ca1-11d3-9cc8-00c04f7971e0")
IBDA_IPV4Filter : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetMulticastListSize(
        ULONG *pulcbAddresses) = 0;

    virtual HRESULT STDMETHODCALLTYPE PutMulticastList(
        ULONG ulcbAddresses,
        BYTE pAddressList[]) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMulticastList(
        ULONG *pulcbAddresses,
        BYTE pAddressList[]) = 0;

    virtual HRESULT STDMETHODCALLTYPE PutMulticastMode(
        ULONG ulModeMask) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMulticastMode(
        ULONG *pulModeMask) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IBDA_IPV4Filter, 0x71985f44, 0x1ca1, 0x11d3, 0x9c,0xc8, 0x00,0xc0,0x4f,0x79,0x71,0xe0)
#endif
#else
typedef struct IBDA_IPV4FilterVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IBDA_IPV4Filter *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IBDA_IPV4Filter *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IBDA_IPV4Filter *This);

    /*** IBDA_IPV4Filter methods ***/
    HRESULT (STDMETHODCALLTYPE *GetMulticastListSize)(
        IBDA_IPV4Filter *This,
        ULONG *pulcbAddresses);

    HRESULT (STDMETHODCALLTYPE *PutMulticastList)(
        IBDA_IPV4Filter *This,
        ULONG ulcbAddresses,
        BYTE pAddressList[]);

    HRESULT (STDMETHODCALLTYPE *GetMulticastList)(
        IBDA_IPV4Filter *This,
        ULONG *pulcbAddresses,
        BYTE pAddressList[]);

    HRESULT (STDMETHODCALLTYPE *PutMulticastMode)(
        IBDA_IPV4Filter *This,
        ULONG ulModeMask);

    HRESULT (STDMETHODCALLTYPE *GetMulticastMode)(
        IBDA_IPV4Filter *This,
        ULONG *pulModeMask);

    END_INTERFACE
} IBDA_IPV4FilterVtbl;

interface IBDA_IPV4Filter {
    CONST_VTBL IBDA_IPV4FilterVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IBDA_IPV4Filter_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IBDA_IPV4Filter_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IBDA_IPV4Filter_Release(This) (This)->lpVtbl->Release(This)
/*** IBDA_IPV4Filter methods ***/
#define IBDA_IPV4Filter_GetMulticastListSize(This,pulcbAddresses) (This)->lpVtbl->GetMulticastListSize(This,pulcbAddresses)
#define IBDA_IPV4Filter_PutMulticastList(This,ulcbAddresses,pAddressList) (This)->lpVtbl->PutMulticastList(This,ulcbAddresses,pAddressList)
#define IBDA_IPV4Filter_GetMulticastList(This,pulcbAddresses,pAddressList) (This)->lpVtbl->GetMulticastList(This,pulcbAddresses,pAddressList)
#define IBDA_IPV4Filter_PutMulticastMode(This,ulModeMask) (This)->lpVtbl->PutMulticastMode(This,ulModeMask)
#define IBDA_IPV4Filter_GetMulticastMode(This,pulModeMask) (This)->lpVtbl->GetMulticastMode(This,pulModeMask)
#else
/*** IUnknown methods ***/
static inline HRESULT IBDA_IPV4Filter_QueryInterface(IBDA_IPV4Filter* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IBDA_IPV4Filter_AddRef(IBDA_IPV4Filter* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IBDA_IPV4Filter_Release(IBDA_IPV4Filter* This) {
    return This->lpVtbl->Release(This);
}
/*** IBDA_IPV4Filter methods ***/
static inline HRESULT IBDA_IPV4Filter_GetMulticastListSize(IBDA_IPV4Filter* This,ULONG *pulcbAddresses) {
    return This->lpVtbl->GetMulticastListSize(This,pulcbAddresses);
}
static inline HRESULT IBDA_IPV4Filter_PutMulticastList(IBDA_IPV4Filter* This,ULONG ulcbAddresses,BYTE pAddressList[]) {
    return This->lpVtbl->PutMulticastList(This,ulcbAddresses,pAddressList);
}
static inline HRESULT IBDA_IPV4Filter_GetMulticastList(IBDA_IPV4Filter* This,ULONG *pulcbAddresses,BYTE pAddressList[]) {
    return This->lpVtbl->GetMulticastList(This,pulcbAddresses,pAddressList);
}
static inline HRESULT IBDA_IPV4Filter_PutMulticastMode(IBDA_IPV4Filter* This,ULONG ulModeMask) {
    return This->lpVtbl->PutMulticastMode(This,ulModeMask);
}
static inline HRESULT IBDA_IPV4Filter_GetMulticastMode(IBDA_IPV4Filter* This,ULONG *pulModeMask) {
    return This->lpVtbl->GetMulticastMode(This,pulModeMask);
}
#endif
#endif

#endif


#endif  /* __IBDA_IPV4Filter_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IBDA_IPV6Filter interface
 */
#ifndef __IBDA_IPV6Filter_INTERFACE_DEFINED__
#define __IBDA_IPV6Filter_INTERFACE_DEFINED__

DEFINE_GUID(IID_IBDA_IPV6Filter, 0xe1785a74, 0x2a23, 0x4fb3, 0x92,0x45, 0xa8,0xf8,0x80,0x17,0xef,0x33);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("e1785a74-2a23-4fb3-9245-a8f88017ef33")
IBDA_IPV6Filter : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetMulticastListSize(
        ULONG *pulcbAddresses) = 0;

    virtual HRESULT STDMETHODCALLTYPE PutMulticastList(
        ULONG ulcbAddresses,
        BYTE pAddressList[]) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMulticastList(
        ULONG *pulcbAddresses,
        BYTE pAddressList[]) = 0;

    virtual HRESULT STDMETHODCALLTYPE PutMulticastMode(
        ULONG ulModeMask) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMulticastMode(
        ULONG *pulModeMask) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IBDA_IPV6Filter, 0xe1785a74, 0x2a23, 0x4fb3, 0x92,0x45, 0xa8,0xf8,0x80,0x17,0xef,0x33)
#endif
#else
typedef struct IBDA_IPV6FilterVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IBDA_IPV6Filter *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IBDA_IPV6Filter *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IBDA_IPV6Filter *This);

    /*** IBDA_IPV6Filter methods ***/
    HRESULT (STDMETHODCALLTYPE *GetMulticastListSize)(
        IBDA_IPV6Filter *This,
        ULONG *pulcbAddresses);

    HRESULT (STDMETHODCALLTYPE *PutMulticastList)(
        IBDA_IPV6Filter *This,
        ULONG ulcbAddresses,
        BYTE pAddressList[]);

    HRESULT (STDMETHODCALLTYPE *GetMulticastList)(
        IBDA_IPV6Filter *This,
        ULONG *pulcbAddresses,
        BYTE pAddressList[]);

    HRESULT (STDMETHODCALLTYPE *PutMulticastMode)(
        IBDA_IPV6Filter *This,
        ULONG ulModeMask);

    HRESULT (STDMETHODCALLTYPE *GetMulticastMode)(
        IBDA_IPV6Filter *This,
        ULONG *pulModeMask);

    END_INTERFACE
} IBDA_IPV6FilterVtbl;

interface IBDA_IPV6Filter {
    CONST_VTBL IBDA_IPV6FilterVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IBDA_IPV6Filter_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IBDA_IPV6Filter_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IBDA_IPV6Filter_Release(This) (This)->lpVtbl->Release(This)
/*** IBDA_IPV6Filter methods ***/
#define IBDA_IPV6Filter_GetMulticastListSize(This,pulcbAddresses) (This)->lpVtbl->GetMulticastListSize(This,pulcbAddresses)
#define IBDA_IPV6Filter_PutMulticastList(This,ulcbAddresses,pAddressList) (This)->lpVtbl->PutMulticastList(This,ulcbAddresses,pAddressList)
#define IBDA_IPV6Filter_GetMulticastList(This,pulcbAddresses,pAddressList) (This)->lpVtbl->GetMulticastList(This,pulcbAddresses,pAddressList)
#define IBDA_IPV6Filter_PutMulticastMode(This,ulModeMask) (This)->lpVtbl->PutMulticastMode(This,ulModeMask)
#define IBDA_IPV6Filter_GetMulticastMode(This,pulModeMask) (This)->lpVtbl->GetMulticastMode(This,pulModeMask)
#else
/*** IUnknown methods ***/
static inline HRESULT IBDA_IPV6Filter_QueryInterface(IBDA_IPV6Filter* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IBDA_IPV6Filter_AddRef(IBDA_IPV6Filter* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IBDA_IPV6Filter_Release(IBDA_IPV6Filter* This) {
    return This->lpVtbl->Release(This);
}
/*** IBDA_IPV6Filter methods ***/
static inline HRESULT IBDA_IPV6Filter_GetMulticastListSize(IBDA_IPV6Filter* This,ULONG *pulcbAddresses) {
    return This->lpVtbl->GetMulticastListSize(This,pulcbAddresses);
}
static inline HRESULT IBDA_IPV6Filter_PutMulticastList(IBDA_IPV6Filter* This,ULONG ulcbAddresses,BYTE pAddressList[]) {
    return This->lpVtbl->PutMulticastList(This,ulcbAddresses,pAddressList);
}
static inline HRESULT IBDA_IPV6Filter_GetMulticastList(IBDA_IPV6Filter* This,ULONG *pulcbAddresses,BYTE pAddressList[]) {
    return This->lpVtbl->GetMulticastList(This,pulcbAddresses,pAddressList);
}
static inline HRESULT IBDA_IPV6Filter_PutMulticastMode(IBDA_IPV6Filter* This,ULONG ulModeMask) {
    return This->lpVtbl->PutMulticastMode(This,ulModeMask);
}
static inline HRESULT IBDA_IPV6Filter_GetMulticastMode(IBDA_IPV6Filter* This,ULONG *pulModeMask) {
    return This->lpVtbl->GetMulticastMode(This,pulModeMask);
}
#endif
#endif

#endif


#endif  /* __IBDA_IPV6Filter_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IBDA_DeviceControl interface
 */
#ifndef __IBDA_DeviceControl_INTERFACE_DEFINED__
#define __IBDA_DeviceControl_INTERFACE_DEFINED__

DEFINE_GUID(IID_IBDA_DeviceControl, 0xfd0a5af3, 0xb41d, 0x11d2, 0x9c,0x95, 0x00,0xc0,0x4f,0x79,0x71,0xe0);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("fd0a5af3-b41d-11d2-9c95-00c04f7971e0")
IBDA_DeviceControl : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE StartChanges(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE CheckChanges(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE CommitChanges(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetChangeState(
        ULONG *pState) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IBDA_DeviceControl, 0xfd0a5af3, 0xb41d, 0x11d2, 0x9c,0x95, 0x00,0xc0,0x4f,0x79,0x71,0xe0)
#endif
#else
typedef struct IBDA_DeviceControlVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IBDA_DeviceControl *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IBDA_DeviceControl *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IBDA_DeviceControl *This);

    /*** IBDA_DeviceControl methods ***/
    HRESULT (STDMETHODCALLTYPE *StartChanges)(
        IBDA_DeviceControl *This);

    HRESULT (STDMETHODCALLTYPE *CheckChanges)(
        IBDA_DeviceControl *This);

    HRESULT (STDMETHODCALLTYPE *CommitChanges)(
        IBDA_DeviceControl *This);

    HRESULT (STDMETHODCALLTYPE *GetChangeState)(
        IBDA_DeviceControl *This,
        ULONG *pState);

    END_INTERFACE
} IBDA_DeviceControlVtbl;

interface IBDA_DeviceControl {
    CONST_VTBL IBDA_DeviceControlVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IBDA_DeviceControl_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IBDA_DeviceControl_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IBDA_DeviceControl_Release(This) (This)->lpVtbl->Release(This)
/*** IBDA_DeviceControl methods ***/
#define IBDA_DeviceControl_StartChanges(This) (This)->lpVtbl->StartChanges(This)
#define IBDA_DeviceControl_CheckChanges(This) (This)->lpVtbl->CheckChanges(This)
#define IBDA_DeviceControl_CommitChanges(This) (This)->lpVtbl->CommitChanges(This)
#define IBDA_DeviceControl_GetChangeState(This,pState) (This)->lpVtbl->GetChangeState(This,pState)
#else
/*** IUnknown methods ***/
static inline HRESULT IBDA_DeviceControl_QueryInterface(IBDA_DeviceControl* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IBDA_DeviceControl_AddRef(IBDA_DeviceControl* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IBDA_DeviceControl_Release(IBDA_DeviceControl* This) {
    return This->lpVtbl->Release(This);
}
/*** IBDA_DeviceControl methods ***/
static inline HRESULT IBDA_DeviceControl_StartChanges(IBDA_DeviceControl* This) {
    return This->lpVtbl->StartChanges(This);
}
static inline HRESULT IBDA_DeviceControl_CheckChanges(IBDA_DeviceControl* This) {
    return This->lpVtbl->CheckChanges(This);
}
static inline HRESULT IBDA_DeviceControl_CommitChanges(IBDA_DeviceControl* This) {
    return This->lpVtbl->CommitChanges(This);
}
static inline HRESULT IBDA_DeviceControl_GetChangeState(IBDA_DeviceControl* This,ULONG *pState) {
    return This->lpVtbl->GetChangeState(This,pState);
}
#endif
#endif

#endif


#endif  /* __IBDA_DeviceControl_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IBDA_PinControl interface
 */
#ifndef __IBDA_PinControl_INTERFACE_DEFINED__
#define __IBDA_PinControl_INTERFACE_DEFINED__

DEFINE_GUID(IID_IBDA_PinControl, 0x0ded49d5, 0xa8b7, 0x4d5d, 0x97,0xa1, 0x12,0xb0,0xc1,0x95,0x87,0x4d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0ded49d5-a8b7-4d5d-97a1-12b0c195874d")
IBDA_PinControl : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetPinID(
        ULONG *pulPinID) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPinType(
        ULONG *pulPinType) = 0;

    virtual HRESULT STDMETHODCALLTYPE RegistrationContext(
        ULONG *pulRegistrationCtx) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IBDA_PinControl, 0x0ded49d5, 0xa8b7, 0x4d5d, 0x97,0xa1, 0x12,0xb0,0xc1,0x95,0x87,0x4d)
#endif
#else
typedef struct IBDA_PinControlVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IBDA_PinControl *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IBDA_PinControl *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IBDA_PinControl *This);

    /*** IBDA_PinControl methods ***/
    HRESULT (STDMETHODCALLTYPE *GetPinID)(
        IBDA_PinControl *This,
        ULONG *pulPinID);

    HRESULT (STDMETHODCALLTYPE *GetPinType)(
        IBDA_PinControl *This,
        ULONG *pulPinType);

    HRESULT (STDMETHODCALLTYPE *RegistrationContext)(
        IBDA_PinControl *This,
        ULONG *pulRegistrationCtx);

    END_INTERFACE
} IBDA_PinControlVtbl;

interface IBDA_PinControl {
    CONST_VTBL IBDA_PinControlVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IBDA_PinControl_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IBDA_PinControl_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IBDA_PinControl_Release(This) (This)->lpVtbl->Release(This)
/*** IBDA_PinControl methods ***/
#define IBDA_PinControl_GetPinID(This,pulPinID) (This)->lpVtbl->GetPinID(This,pulPinID)
#define IBDA_PinControl_GetPinType(This,pulPinType) (This)->lpVtbl->GetPinType(This,pulPinType)
#define IBDA_PinControl_RegistrationContext(This,pulRegistrationCtx) (This)->lpVtbl->RegistrationContext(This,pulRegistrationCtx)
#else
/*** IUnknown methods ***/
static inline HRESULT IBDA_PinControl_QueryInterface(IBDA_PinControl* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IBDA_PinControl_AddRef(IBDA_PinControl* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IBDA_PinControl_Release(IBDA_PinControl* This) {
    return This->lpVtbl->Release(This);
}
/*** IBDA_PinControl methods ***/
static inline HRESULT IBDA_PinControl_GetPinID(IBDA_PinControl* This,ULONG *pulPinID) {
    return This->lpVtbl->GetPinID(This,pulPinID);
}
static inline HRESULT IBDA_PinControl_GetPinType(IBDA_PinControl* This,ULONG *pulPinType) {
    return This->lpVtbl->GetPinType(This,pulPinType);
}
static inline HRESULT IBDA_PinControl_RegistrationContext(IBDA_PinControl* This,ULONG *pulRegistrationCtx) {
    return This->lpVtbl->RegistrationContext(This,pulRegistrationCtx);
}
#endif
#endif

#endif


#endif  /* __IBDA_PinControl_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IBDA_SignalProperties interface
 */
#ifndef __IBDA_SignalProperties_INTERFACE_DEFINED__
#define __IBDA_SignalProperties_INTERFACE_DEFINED__

DEFINE_GUID(IID_IBDA_SignalProperties, 0xd2f1644b, 0xb409, 0x11d2, 0xbc,0x69, 0x00,0xa0,0xc9,0xee,0x9e,0x16);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d2f1644b-b409-11d2-bc69-00a0c9ee9e16")
IBDA_SignalProperties : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE PutNetworkType(
        REFGUID guidNetworkType) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetNetworkType(
        GUID *pguidNetworkType) = 0;

    virtual HRESULT STDMETHODCALLTYPE PutSignalSource(
        ULONG ulSignalSource) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSignalSource(
        ULONG *pulSignalSource) = 0;

    virtual HRESULT STDMETHODCALLTYPE PutTuningSpace(
        REFGUID guidTuningSpace) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTuningSpace(
        GUID *pguidTuingSpace) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IBDA_SignalProperties, 0xd2f1644b, 0xb409, 0x11d2, 0xbc,0x69, 0x00,0xa0,0xc9,0xee,0x9e,0x16)
#endif
#else
typedef struct IBDA_SignalPropertiesVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IBDA_SignalProperties *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IBDA_SignalProperties *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IBDA_SignalProperties *This);

    /*** IBDA_SignalProperties methods ***/
    HRESULT (STDMETHODCALLTYPE *PutNetworkType)(
        IBDA_SignalProperties *This,
        REFGUID guidNetworkType);

    HRESULT (STDMETHODCALLTYPE *GetNetworkType)(
        IBDA_SignalProperties *This,
        GUID *pguidNetworkType);

    HRESULT (STDMETHODCALLTYPE *PutSignalSource)(
        IBDA_SignalProperties *This,
        ULONG ulSignalSource);

    HRESULT (STDMETHODCALLTYPE *GetSignalSource)(
        IBDA_SignalProperties *This,
        ULONG *pulSignalSource);

    HRESULT (STDMETHODCALLTYPE *PutTuningSpace)(
        IBDA_SignalProperties *This,
        REFGUID guidTuningSpace);

    HRESULT (STDMETHODCALLTYPE *GetTuningSpace)(
        IBDA_SignalProperties *This,
        GUID *pguidTuingSpace);

    END_INTERFACE
} IBDA_SignalPropertiesVtbl;

interface IBDA_SignalProperties {
    CONST_VTBL IBDA_SignalPropertiesVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IBDA_SignalProperties_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IBDA_SignalProperties_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IBDA_SignalProperties_Release(This) (This)->lpVtbl->Release(This)
/*** IBDA_SignalProperties methods ***/
#define IBDA_SignalProperties_PutNetworkType(This,guidNetworkType) (This)->lpVtbl->PutNetworkType(This,guidNetworkType)
#define IBDA_SignalProperties_GetNetworkType(This,pguidNetworkType) (This)->lpVtbl->GetNetworkType(This,pguidNetworkType)
#define IBDA_SignalProperties_PutSignalSource(This,ulSignalSource) (This)->lpVtbl->PutSignalSource(This,ulSignalSource)
#define IBDA_SignalProperties_GetSignalSource(This,pulSignalSource) (This)->lpVtbl->GetSignalSource(This,pulSignalSource)
#define IBDA_SignalProperties_PutTuningSpace(This,guidTuningSpace) (This)->lpVtbl->PutTuningSpace(This,guidTuningSpace)
#define IBDA_SignalProperties_GetTuningSpace(This,pguidTuingSpace) (This)->lpVtbl->GetTuningSpace(This,pguidTuingSpace)
#else
/*** IUnknown methods ***/
static inline HRESULT IBDA_SignalProperties_QueryInterface(IBDA_SignalProperties* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IBDA_SignalProperties_AddRef(IBDA_SignalProperties* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IBDA_SignalProperties_Release(IBDA_SignalProperties* This) {
    return This->lpVtbl->Release(This);
}
/*** IBDA_SignalProperties methods ***/
static inline HRESULT IBDA_SignalProperties_PutNetworkType(IBDA_SignalProperties* This,REFGUID guidNetworkType) {
    return This->lpVtbl->PutNetworkType(This,guidNetworkType);
}
static inline HRESULT IBDA_SignalProperties_GetNetworkType(IBDA_SignalProperties* This,GUID *pguidNetworkType) {
    return This->lpVtbl->GetNetworkType(This,pguidNetworkType);
}
static inline HRESULT IBDA_SignalProperties_PutSignalSource(IBDA_SignalProperties* This,ULONG ulSignalSource) {
    return This->lpVtbl->PutSignalSource(This,ulSignalSource);
}
static inline HRESULT IBDA_SignalProperties_GetSignalSource(IBDA_SignalProperties* This,ULONG *pulSignalSource) {
    return This->lpVtbl->GetSignalSource(This,pulSignalSource);
}
static inline HRESULT IBDA_SignalProperties_PutTuningSpace(IBDA_SignalProperties* This,REFGUID guidTuningSpace) {
    return This->lpVtbl->PutTuningSpace(This,guidTuningSpace);
}
static inline HRESULT IBDA_SignalProperties_GetTuningSpace(IBDA_SignalProperties* This,GUID *pguidTuingSpace) {
    return This->lpVtbl->GetTuningSpace(This,pguidTuingSpace);
}
#endif
#endif

#endif


#endif  /* __IBDA_SignalProperties_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IBDA_SignalStatistics interface
 */
#ifndef __IBDA_SignalStatistics_INTERFACE_DEFINED__
#define __IBDA_SignalStatistics_INTERFACE_DEFINED__

DEFINE_GUID(IID_IBDA_SignalStatistics, 0x1347d106, 0xcf3a, 0x428a, 0xa5,0xcb, 0xac,0x0d,0x9a,0x2a,0x43,0x38);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("1347d106-cf3a-428a-a5cb-ac0d9a2a4338")
IBDA_SignalStatistics : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE put_SignalStrength(
        LONG lDbStrength) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_SignalStrength(
        LONG *plDbStrength) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_SignalQuality(
        LONG lPercentQuality) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_SignalQuality(
        LONG *plPercentQuality) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_SignalPresent(
        BOOLEAN fPresent) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_SignalPresent(
        BOOLEAN *pfPresent) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_SignalLocked(
        BOOLEAN fLocked) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_SignalLocked(
        BOOLEAN *pfLocked) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_SampleTime(
        LONG lmsSampleTime) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_SampleTime(
        LONG *plmsSampleTime) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IBDA_SignalStatistics, 0x1347d106, 0xcf3a, 0x428a, 0xa5,0xcb, 0xac,0x0d,0x9a,0x2a,0x43,0x38)
#endif
#else
typedef struct IBDA_SignalStatisticsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IBDA_SignalStatistics *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IBDA_SignalStatistics *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IBDA_SignalStatistics *This);

    /*** IBDA_SignalStatistics methods ***/
    HRESULT (STDMETHODCALLTYPE *put_SignalStrength)(
        IBDA_SignalStatistics *This,
        LONG lDbStrength);

    HRESULT (STDMETHODCALLTYPE *get_SignalStrength)(
        IBDA_SignalStatistics *This,
        LONG *plDbStrength);

    HRESULT (STDMETHODCALLTYPE *put_SignalQuality)(
        IBDA_SignalStatistics *This,
        LONG lPercentQuality);

    HRESULT (STDMETHODCALLTYPE *get_SignalQuality)(
        IBDA_SignalStatistics *This,
        LONG *plPercentQuality);

    HRESULT (STDMETHODCALLTYPE *put_SignalPresent)(
        IBDA_SignalStatistics *This,
        BOOLEAN fPresent);

    HRESULT (STDMETHODCALLTYPE *get_SignalPresent)(
        IBDA_SignalStatistics *This,
        BOOLEAN *pfPresent);

    HRESULT (STDMETHODCALLTYPE *put_SignalLocked)(
        IBDA_SignalStatistics *This,
        BOOLEAN fLocked);

    HRESULT (STDMETHODCALLTYPE *get_SignalLocked)(
        IBDA_SignalStatistics *This,
        BOOLEAN *pfLocked);

    HRESULT (STDMETHODCALLTYPE *put_SampleTime)(
        IBDA_SignalStatistics *This,
        LONG lmsSampleTime);

    HRESULT (STDMETHODCALLTYPE *get_SampleTime)(
        IBDA_SignalStatistics *This,
        LONG *plmsSampleTime);

    END_INTERFACE
} IBDA_SignalStatisticsVtbl;

interface IBDA_SignalStatistics {
    CONST_VTBL IBDA_SignalStatisticsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IBDA_SignalStatistics_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IBDA_SignalStatistics_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IBDA_SignalStatistics_Release(This) (This)->lpVtbl->Release(This)
/*** IBDA_SignalStatistics methods ***/
#define IBDA_SignalStatistics_put_SignalStrength(This,lDbStrength) (This)->lpVtbl->put_SignalStrength(This,lDbStrength)
#define IBDA_SignalStatistics_get_SignalStrength(This,plDbStrength) (This)->lpVtbl->get_SignalStrength(This,plDbStrength)
#define IBDA_SignalStatistics_put_SignalQuality(This,lPercentQuality) (This)->lpVtbl->put_SignalQuality(This,lPercentQuality)
#define IBDA_SignalStatistics_get_SignalQuality(This,plPercentQuality) (This)->lpVtbl->get_SignalQuality(This,plPercentQuality)
#define IBDA_SignalStatistics_put_SignalPresent(This,fPresent) (This)->lpVtbl->put_SignalPresent(This,fPresent)
#define IBDA_SignalStatistics_get_SignalPresent(This,pfPresent) (This)->lpVtbl->get_SignalPresent(This,pfPresent)
#define IBDA_SignalStatistics_put_SignalLocked(This,fLocked) (This)->lpVtbl->put_SignalLocked(This,fLocked)
#define IBDA_SignalStatistics_get_SignalLocked(This,pfLocked) (This)->lpVtbl->get_SignalLocked(This,pfLocked)
#define IBDA_SignalStatistics_put_SampleTime(This,lmsSampleTime) (This)->lpVtbl->put_SampleTime(This,lmsSampleTime)
#define IBDA_SignalStatistics_get_SampleTime(This,plmsSampleTime) (This)->lpVtbl->get_SampleTime(This,plmsSampleTime)
#else
/*** IUnknown methods ***/
static inline HRESULT IBDA_SignalStatistics_QueryInterface(IBDA_SignalStatistics* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IBDA_SignalStatistics_AddRef(IBDA_SignalStatistics* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IBDA_SignalStatistics_Release(IBDA_SignalStatistics* This) {
    return This->lpVtbl->Release(This);
}
/*** IBDA_SignalStatistics methods ***/
static inline HRESULT IBDA_SignalStatistics_put_SignalStrength(IBDA_SignalStatistics* This,LONG lDbStrength) {
    return This->lpVtbl->put_SignalStrength(This,lDbStrength);
}
static inline HRESULT IBDA_SignalStatistics_get_SignalStrength(IBDA_SignalStatistics* This,LONG *plDbStrength) {
    return This->lpVtbl->get_SignalStrength(This,plDbStrength);
}
static inline HRESULT IBDA_SignalStatistics_put_SignalQuality(IBDA_SignalStatistics* This,LONG lPercentQuality) {
    return This->lpVtbl->put_SignalQuality(This,lPercentQuality);
}
static inline HRESULT IBDA_SignalStatistics_get_SignalQuality(IBDA_SignalStatistics* This,LONG *plPercentQuality) {
    return This->lpVtbl->get_SignalQuality(This,plPercentQuality);
}
static inline HRESULT IBDA_SignalStatistics_put_SignalPresent(IBDA_SignalStatistics* This,BOOLEAN fPresent) {
    return This->lpVtbl->put_SignalPresent(This,fPresent);
}
static inline HRESULT IBDA_SignalStatistics_get_SignalPresent(IBDA_SignalStatistics* This,BOOLEAN *pfPresent) {
    return This->lpVtbl->get_SignalPresent(This,pfPresent);
}
static inline HRESULT IBDA_SignalStatistics_put_SignalLocked(IBDA_SignalStatistics* This,BOOLEAN fLocked) {
    return This->lpVtbl->put_SignalLocked(This,fLocked);
}
static inline HRESULT IBDA_SignalStatistics_get_SignalLocked(IBDA_SignalStatistics* This,BOOLEAN *pfLocked) {
    return This->lpVtbl->get_SignalLocked(This,pfLocked);
}
static inline HRESULT IBDA_SignalStatistics_put_SampleTime(IBDA_SignalStatistics* This,LONG lmsSampleTime) {
    return This->lpVtbl->put_SampleTime(This,lmsSampleTime);
}
static inline HRESULT IBDA_SignalStatistics_get_SampleTime(IBDA_SignalStatistics* This,LONG *plmsSampleTime) {
    return This->lpVtbl->get_SampleTime(This,plmsSampleTime);
}
#endif
#endif

#endif


#endif  /* __IBDA_SignalStatistics_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IBDA_Topology interface
 */
#ifndef __IBDA_Topology_INTERFACE_DEFINED__
#define __IBDA_Topology_INTERFACE_DEFINED__

DEFINE_GUID(IID_IBDA_Topology, 0x79b56888, 0x7fea, 0x4690, 0xb4,0x5d, 0x38,0xfd,0x3c,0x78,0x49,0xbe);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("79b56888-7fea-4690-b45d-38fd3c7849be")
IBDA_Topology : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetNodeTypes(
        ULONG *pulcNodeTypes,
        ULONG ulcNodeTypesMax,
        ULONG rgulNodeTypes[]) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetNodeDescriptors(
        ULONG *ulcNodeDescriptors,
        ULONG ulcNodeDescriptorsMax,
        BDANODE_DESCRIPTOR rgNodeDescriptors[]) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetNodeInterfaces(
        ULONG ulNodeType,
        ULONG *pulcInterfaces,
        ULONG ulcInterfacesMax,
        GUID rgguidInterfaces[]) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPinTypes(
        ULONG *pulcPinTypes,
        ULONG ulcPinTypesMax,
        ULONG rgulPinTypes[]) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTemplateConnections(
        ULONG *pulcConnections,
        ULONG ulcConnectionsMax,
        BDA_TEMPLATE_CONNECTION rgConnections[]) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreatePin(
        ULONG ulPinType,
        ULONG *pulPinId) = 0;

    virtual HRESULT STDMETHODCALLTYPE DeletePin(
        ULONG ulPinId) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetMediaType(
        ULONG ulPinId,
        AM_MEDIA_TYPE *pMediaType) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetMedium(
        ULONG ulPinId,
        REGPINMEDIUM *pMedium) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateTopology(
        ULONG ulInputPinId,
        ULONG ulOutputPinId) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetControlNode(
        ULONG ulInputPinId,
        ULONG ulOutputPinId,
        ULONG ulNodeType,
        IUnknown **ppControlNode) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IBDA_Topology, 0x79b56888, 0x7fea, 0x4690, 0xb4,0x5d, 0x38,0xfd,0x3c,0x78,0x49,0xbe)
#endif
#else
typedef struct IBDA_TopologyVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IBDA_Topology *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IBDA_Topology *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IBDA_Topology *This);

    /*** IBDA_Topology methods ***/
    HRESULT (STDMETHODCALLTYPE *GetNodeTypes)(
        IBDA_Topology *This,
        ULONG *pulcNodeTypes,
        ULONG ulcNodeTypesMax,
        ULONG rgulNodeTypes[]);

    HRESULT (STDMETHODCALLTYPE *GetNodeDescriptors)(
        IBDA_Topology *This,
        ULONG *ulcNodeDescriptors,
        ULONG ulcNodeDescriptorsMax,
        BDANODE_DESCRIPTOR rgNodeDescriptors[]);

    HRESULT (STDMETHODCALLTYPE *GetNodeInterfaces)(
        IBDA_Topology *This,
        ULONG ulNodeType,
        ULONG *pulcInterfaces,
        ULONG ulcInterfacesMax,
        GUID rgguidInterfaces[]);

    HRESULT (STDMETHODCALLTYPE *GetPinTypes)(
        IBDA_Topology *This,
        ULONG *pulcPinTypes,
        ULONG ulcPinTypesMax,
        ULONG rgulPinTypes[]);

    HRESULT (STDMETHODCALLTYPE *GetTemplateConnections)(
        IBDA_Topology *This,
        ULONG *pulcConnections,
        ULONG ulcConnectionsMax,
        BDA_TEMPLATE_CONNECTION rgConnections[]);

    HRESULT (STDMETHODCALLTYPE *CreatePin)(
        IBDA_Topology *This,
        ULONG ulPinType,
        ULONG *pulPinId);

    HRESULT (STDMETHODCALLTYPE *DeletePin)(
        IBDA_Topology *This,
        ULONG ulPinId);

    HRESULT (STDMETHODCALLTYPE *SetMediaType)(
        IBDA_Topology *This,
        ULONG ulPinId,
        AM_MEDIA_TYPE *pMediaType);

    HRESULT (STDMETHODCALLTYPE *SetMedium)(
        IBDA_Topology *This,
        ULONG ulPinId,
        REGPINMEDIUM *pMedium);

    HRESULT (STDMETHODCALLTYPE *CreateTopology)(
        IBDA_Topology *This,
        ULONG ulInputPinId,
        ULONG ulOutputPinId);

    HRESULT (STDMETHODCALLTYPE *GetControlNode)(
        IBDA_Topology *This,
        ULONG ulInputPinId,
        ULONG ulOutputPinId,
        ULONG ulNodeType,
        IUnknown **ppControlNode);

    END_INTERFACE
} IBDA_TopologyVtbl;

interface IBDA_Topology {
    CONST_VTBL IBDA_TopologyVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IBDA_Topology_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IBDA_Topology_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IBDA_Topology_Release(This) (This)->lpVtbl->Release(This)
/*** IBDA_Topology methods ***/
#define IBDA_Topology_GetNodeTypes(This,pulcNodeTypes,ulcNodeTypesMax,rgulNodeTypes) (This)->lpVtbl->GetNodeTypes(This,pulcNodeTypes,ulcNodeTypesMax,rgulNodeTypes)
#define IBDA_Topology_GetNodeDescriptors(This,ulcNodeDescriptors,ulcNodeDescriptorsMax,rgNodeDescriptors) (This)->lpVtbl->GetNodeDescriptors(This,ulcNodeDescriptors,ulcNodeDescriptorsMax,rgNodeDescriptors)
#define IBDA_Topology_GetNodeInterfaces(This,ulNodeType,pulcInterfaces,ulcInterfacesMax,rgguidInterfaces) (This)->lpVtbl->GetNodeInterfaces(This,ulNodeType,pulcInterfaces,ulcInterfacesMax,rgguidInterfaces)
#define IBDA_Topology_GetPinTypes(This,pulcPinTypes,ulcPinTypesMax,rgulPinTypes) (This)->lpVtbl->GetPinTypes(This,pulcPinTypes,ulcPinTypesMax,rgulPinTypes)
#define IBDA_Topology_GetTemplateConnections(This,pulcConnections,ulcConnectionsMax,rgConnections) (This)->lpVtbl->GetTemplateConnections(This,pulcConnections,ulcConnectionsMax,rgConnections)
#define IBDA_Topology_CreatePin(This,ulPinType,pulPinId) (This)->lpVtbl->CreatePin(This,ulPinType,pulPinId)
#define IBDA_Topology_DeletePin(This,ulPinId) (This)->lpVtbl->DeletePin(This,ulPinId)
#define IBDA_Topology_SetMediaType(This,ulPinId,pMediaType) (This)->lpVtbl->SetMediaType(This,ulPinId,pMediaType)
#define IBDA_Topology_SetMedium(This,ulPinId,pMedium) (This)->lpVtbl->SetMedium(This,ulPinId,pMedium)
#define IBDA_Topology_CreateTopology(This,ulInputPinId,ulOutputPinId) (This)->lpVtbl->CreateTopology(This,ulInputPinId,ulOutputPinId)
#define IBDA_Topology_GetControlNode(This,ulInputPinId,ulOutputPinId,ulNodeType,ppControlNode) (This)->lpVtbl->GetControlNode(This,ulInputPinId,ulOutputPinId,ulNodeType,ppControlNode)
#else
/*** IUnknown methods ***/
static inline HRESULT IBDA_Topology_QueryInterface(IBDA_Topology* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IBDA_Topology_AddRef(IBDA_Topology* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IBDA_Topology_Release(IBDA_Topology* This) {
    return This->lpVtbl->Release(This);
}
/*** IBDA_Topology methods ***/
static inline HRESULT IBDA_Topology_GetNodeTypes(IBDA_Topology* This,ULONG *pulcNodeTypes,ULONG ulcNodeTypesMax,ULONG rgulNodeTypes[]) {
    return This->lpVtbl->GetNodeTypes(This,pulcNodeTypes,ulcNodeTypesMax,rgulNodeTypes);
}
static inline HRESULT IBDA_Topology_GetNodeDescriptors(IBDA_Topology* This,ULONG *ulcNodeDescriptors,ULONG ulcNodeDescriptorsMax,BDANODE_DESCRIPTOR rgNodeDescriptors[]) {
    return This->lpVtbl->GetNodeDescriptors(This,ulcNodeDescriptors,ulcNodeDescriptorsMax,rgNodeDescriptors);
}
static inline HRESULT IBDA_Topology_GetNodeInterfaces(IBDA_Topology* This,ULONG ulNodeType,ULONG *pulcInterfaces,ULONG ulcInterfacesMax,GUID rgguidInterfaces[]) {
    return This->lpVtbl->GetNodeInterfaces(This,ulNodeType,pulcInterfaces,ulcInterfacesMax,rgguidInterfaces);
}
static inline HRESULT IBDA_Topology_GetPinTypes(IBDA_Topology* This,ULONG *pulcPinTypes,ULONG ulcPinTypesMax,ULONG rgulPinTypes[]) {
    return This->lpVtbl->GetPinTypes(This,pulcPinTypes,ulcPinTypesMax,rgulPinTypes);
}
static inline HRESULT IBDA_Topology_GetTemplateConnections(IBDA_Topology* This,ULONG *pulcConnections,ULONG ulcConnectionsMax,BDA_TEMPLATE_CONNECTION rgConnections[]) {
    return This->lpVtbl->GetTemplateConnections(This,pulcConnections,ulcConnectionsMax,rgConnections);
}
static inline HRESULT IBDA_Topology_CreatePin(IBDA_Topology* This,ULONG ulPinType,ULONG *pulPinId) {
    return This->lpVtbl->CreatePin(This,ulPinType,pulPinId);
}
static inline HRESULT IBDA_Topology_DeletePin(IBDA_Topology* This,ULONG ulPinId) {
    return This->lpVtbl->DeletePin(This,ulPinId);
}
static inline HRESULT IBDA_Topology_SetMediaType(IBDA_Topology* This,ULONG ulPinId,AM_MEDIA_TYPE *pMediaType) {
    return This->lpVtbl->SetMediaType(This,ulPinId,pMediaType);
}
static inline HRESULT IBDA_Topology_SetMedium(IBDA_Topology* This,ULONG ulPinId,REGPINMEDIUM *pMedium) {
    return This->lpVtbl->SetMedium(This,ulPinId,pMedium);
}
static inline HRESULT IBDA_Topology_CreateTopology(IBDA_Topology* This,ULONG ulInputPinId,ULONG ulOutputPinId) {
    return This->lpVtbl->CreateTopology(This,ulInputPinId,ulOutputPinId);
}
static inline HRESULT IBDA_Topology_GetControlNode(IBDA_Topology* This,ULONG ulInputPinId,ULONG ulOutputPinId,ULONG ulNodeType,IUnknown **ppControlNode) {
    return This->lpVtbl->GetControlNode(This,ulInputPinId,ulOutputPinId,ulNodeType,ppControlNode);
}
#endif
#endif

#endif


#endif  /* __IBDA_Topology_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IBDA_VoidTransform interface
 */
#ifndef __IBDA_VoidTransform_INTERFACE_DEFINED__
#define __IBDA_VoidTransform_INTERFACE_DEFINED__

DEFINE_GUID(IID_IBDA_VoidTransform, 0x71985f46, 0x1ca1, 0x11d3, 0x9c,0xc8, 0x00,0xc0,0x4f,0x79,0x71,0xe0);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("71985f46-1ca1-11d3-9cc8-00c04f7971e0")
IBDA_VoidTransform : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Start(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Stop(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IBDA_VoidTransform, 0x71985f46, 0x1ca1, 0x11d3, 0x9c,0xc8, 0x00,0xc0,0x4f,0x79,0x71,0xe0)
#endif
#else
typedef struct IBDA_VoidTransformVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IBDA_VoidTransform *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IBDA_VoidTransform *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IBDA_VoidTransform *This);

    /*** IBDA_VoidTransform methods ***/
    HRESULT (STDMETHODCALLTYPE *Start)(
        IBDA_VoidTransform *This);

    HRESULT (STDMETHODCALLTYPE *Stop)(
        IBDA_VoidTransform *This);

    END_INTERFACE
} IBDA_VoidTransformVtbl;

interface IBDA_VoidTransform {
    CONST_VTBL IBDA_VoidTransformVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IBDA_VoidTransform_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IBDA_VoidTransform_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IBDA_VoidTransform_Release(This) (This)->lpVtbl->Release(This)
/*** IBDA_VoidTransform methods ***/
#define IBDA_VoidTransform_Start(This) (This)->lpVtbl->Start(This)
#define IBDA_VoidTransform_Stop(This) (This)->lpVtbl->Stop(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IBDA_VoidTransform_QueryInterface(IBDA_VoidTransform* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IBDA_VoidTransform_AddRef(IBDA_VoidTransform* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IBDA_VoidTransform_Release(IBDA_VoidTransform* This) {
    return This->lpVtbl->Release(This);
}
/*** IBDA_VoidTransform methods ***/
static inline HRESULT IBDA_VoidTransform_Start(IBDA_VoidTransform* This) {
    return This->lpVtbl->Start(This);
}
static inline HRESULT IBDA_VoidTransform_Stop(IBDA_VoidTransform* This) {
    return This->lpVtbl->Stop(This);
}
#endif
#endif

#endif


#endif  /* __IBDA_VoidTransform_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IBDA_NullTransform interface
 */
#ifndef __IBDA_NullTransform_INTERFACE_DEFINED__
#define __IBDA_NullTransform_INTERFACE_DEFINED__

DEFINE_GUID(IID_IBDA_NullTransform, 0xddf15b0d, 0xbd25, 0x11d2, 0x9c,0xa0, 0x00,0xc0,0x4f,0x79,0x71,0xe0);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("ddf15b0d-bd25-11d2-9ca0-00c04f7971e0")
IBDA_NullTransform : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Start(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Stop(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IBDA_NullTransform, 0xddf15b0d, 0xbd25, 0x11d2, 0x9c,0xa0, 0x00,0xc0,0x4f,0x79,0x71,0xe0)
#endif
#else
typedef struct IBDA_NullTransformVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IBDA_NullTransform *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IBDA_NullTransform *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IBDA_NullTransform *This);

    /*** IBDA_NullTransform methods ***/
    HRESULT (STDMETHODCALLTYPE *Start)(
        IBDA_NullTransform *This);

    HRESULT (STDMETHODCALLTYPE *Stop)(
        IBDA_NullTransform *This);

    END_INTERFACE
} IBDA_NullTransformVtbl;

interface IBDA_NullTransform {
    CONST_VTBL IBDA_NullTransformVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IBDA_NullTransform_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IBDA_NullTransform_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IBDA_NullTransform_Release(This) (This)->lpVtbl->Release(This)
/*** IBDA_NullTransform methods ***/
#define IBDA_NullTransform_Start(This) (This)->lpVtbl->Start(This)
#define IBDA_NullTransform_Stop(This) (This)->lpVtbl->Stop(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IBDA_NullTransform_QueryInterface(IBDA_NullTransform* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IBDA_NullTransform_AddRef(IBDA_NullTransform* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IBDA_NullTransform_Release(IBDA_NullTransform* This) {
    return This->lpVtbl->Release(This);
}
/*** IBDA_NullTransform methods ***/
static inline HRESULT IBDA_NullTransform_Start(IBDA_NullTransform* This) {
    return This->lpVtbl->Start(This);
}
static inline HRESULT IBDA_NullTransform_Stop(IBDA_NullTransform* This) {
    return This->lpVtbl->Stop(This);
}
#endif
#endif

#endif


#endif  /* __IBDA_NullTransform_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IBDA_FrequencyFilter interface
 */
#ifndef __IBDA_FrequencyFilter_INTERFACE_DEFINED__
#define __IBDA_FrequencyFilter_INTERFACE_DEFINED__

DEFINE_GUID(IID_IBDA_FrequencyFilter, 0x71985f47, 0x1ca1, 0x11d3, 0x9c,0xc8, 0x00,0xc0,0x4f,0x79,0x71,0xe0);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("71985f47-1ca1-11d3-9cc8-00c04f7971e0")
IBDA_FrequencyFilter : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE put_Autotune(
        ULONG ulTransponder) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Autotune(
        ULONG *pulTransponder) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Frequency(
        ULONG ulFrequency) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Frequency(
        ULONG *pulFrequency) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Polarity(
        Polarisation Polarity) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Polarity(
        Polarisation *pPolarity) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Range(
        ULONG ulRange) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Range(
        ULONG *pulRange) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Bandwidth(
        ULONG ulBandwidth) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Bandwidth(
        ULONG *pulBandwidth) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_FrequencyMultiplier(
        ULONG ulMultiplier) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_FrequencyMultiplier(
        ULONG *pulMultiplier) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IBDA_FrequencyFilter, 0x71985f47, 0x1ca1, 0x11d3, 0x9c,0xc8, 0x00,0xc0,0x4f,0x79,0x71,0xe0)
#endif
#else
typedef struct IBDA_FrequencyFilterVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IBDA_FrequencyFilter *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IBDA_FrequencyFilter *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IBDA_FrequencyFilter *This);

    /*** IBDA_FrequencyFilter methods ***/
    HRESULT (STDMETHODCALLTYPE *put_Autotune)(
        IBDA_FrequencyFilter *This,
        ULONG ulTransponder);

    HRESULT (STDMETHODCALLTYPE *get_Autotune)(
        IBDA_FrequencyFilter *This,
        ULONG *pulTransponder);

    HRESULT (STDMETHODCALLTYPE *put_Frequency)(
        IBDA_FrequencyFilter *This,
        ULONG ulFrequency);

    HRESULT (STDMETHODCALLTYPE *get_Frequency)(
        IBDA_FrequencyFilter *This,
        ULONG *pulFrequency);

    HRESULT (STDMETHODCALLTYPE *put_Polarity)(
        IBDA_FrequencyFilter *This,
        Polarisation Polarity);

    HRESULT (STDMETHODCALLTYPE *get_Polarity)(
        IBDA_FrequencyFilter *This,
        Polarisation *pPolarity);

    HRESULT (STDMETHODCALLTYPE *put_Range)(
        IBDA_FrequencyFilter *This,
        ULONG ulRange);

    HRESULT (STDMETHODCALLTYPE *get_Range)(
        IBDA_FrequencyFilter *This,
        ULONG *pulRange);

    HRESULT (STDMETHODCALLTYPE *put_Bandwidth)(
        IBDA_FrequencyFilter *This,
        ULONG ulBandwidth);

    HRESULT (STDMETHODCALLTYPE *get_Bandwidth)(
        IBDA_FrequencyFilter *This,
        ULONG *pulBandwidth);

    HRESULT (STDMETHODCALLTYPE *put_FrequencyMultiplier)(
        IBDA_FrequencyFilter *This,
        ULONG ulMultiplier);

    HRESULT (STDMETHODCALLTYPE *get_FrequencyMultiplier)(
        IBDA_FrequencyFilter *This,
        ULONG *pulMultiplier);

    END_INTERFACE
} IBDA_FrequencyFilterVtbl;

interface IBDA_FrequencyFilter {
    CONST_VTBL IBDA_FrequencyFilterVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IBDA_FrequencyFilter_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IBDA_FrequencyFilter_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IBDA_FrequencyFilter_Release(This) (This)->lpVtbl->Release(This)
/*** IBDA_FrequencyFilter methods ***/
#define IBDA_FrequencyFilter_put_Autotune(This,ulTransponder) (This)->lpVtbl->put_Autotune(This,ulTransponder)
#define IBDA_FrequencyFilter_get_Autotune(This,pulTransponder) (This)->lpVtbl->get_Autotune(This,pulTransponder)
#define IBDA_FrequencyFilter_put_Frequency(This,ulFrequency) (This)->lpVtbl->put_Frequency(This,ulFrequency)
#define IBDA_FrequencyFilter_get_Frequency(This,pulFrequency) (This)->lpVtbl->get_Frequency(This,pulFrequency)
#define IBDA_FrequencyFilter_put_Polarity(This,Polarity) (This)->lpVtbl->put_Polarity(This,Polarity)
#define IBDA_FrequencyFilter_get_Polarity(This,pPolarity) (This)->lpVtbl->get_Polarity(This,pPolarity)
#define IBDA_FrequencyFilter_put_Range(This,ulRange) (This)->lpVtbl->put_Range(This,ulRange)
#define IBDA_FrequencyFilter_get_Range(This,pulRange) (This)->lpVtbl->get_Range(This,pulRange)
#define IBDA_FrequencyFilter_put_Bandwidth(This,ulBandwidth) (This)->lpVtbl->put_Bandwidth(This,ulBandwidth)
#define IBDA_FrequencyFilter_get_Bandwidth(This,pulBandwidth) (This)->lpVtbl->get_Bandwidth(This,pulBandwidth)
#define IBDA_FrequencyFilter_put_FrequencyMultiplier(This,ulMultiplier) (This)->lpVtbl->put_FrequencyMultiplier(This,ulMultiplier)
#define IBDA_FrequencyFilter_get_FrequencyMultiplier(This,pulMultiplier) (This)->lpVtbl->get_FrequencyMultiplier(This,pulMultiplier)
#else
/*** IUnknown methods ***/
static inline HRESULT IBDA_FrequencyFilter_QueryInterface(IBDA_FrequencyFilter* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IBDA_FrequencyFilter_AddRef(IBDA_FrequencyFilter* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IBDA_FrequencyFilter_Release(IBDA_FrequencyFilter* This) {
    return This->lpVtbl->Release(This);
}
/*** IBDA_FrequencyFilter methods ***/
static inline HRESULT IBDA_FrequencyFilter_put_Autotune(IBDA_FrequencyFilter* This,ULONG ulTransponder) {
    return This->lpVtbl->put_Autotune(This,ulTransponder);
}
static inline HRESULT IBDA_FrequencyFilter_get_Autotune(IBDA_FrequencyFilter* This,ULONG *pulTransponder) {
    return This->lpVtbl->get_Autotune(This,pulTransponder);
}
static inline HRESULT IBDA_FrequencyFilter_put_Frequency(IBDA_FrequencyFilter* This,ULONG ulFrequency) {
    return This->lpVtbl->put_Frequency(This,ulFrequency);
}
static inline HRESULT IBDA_FrequencyFilter_get_Frequency(IBDA_FrequencyFilter* This,ULONG *pulFrequency) {
    return This->lpVtbl->get_Frequency(This,pulFrequency);
}
static inline HRESULT IBDA_FrequencyFilter_put_Polarity(IBDA_FrequencyFilter* This,Polarisation Polarity) {
    return This->lpVtbl->put_Polarity(This,Polarity);
}
static inline HRESULT IBDA_FrequencyFilter_get_Polarity(IBDA_FrequencyFilter* This,Polarisation *pPolarity) {
    return This->lpVtbl->get_Polarity(This,pPolarity);
}
static inline HRESULT IBDA_FrequencyFilter_put_Range(IBDA_FrequencyFilter* This,ULONG ulRange) {
    return This->lpVtbl->put_Range(This,ulRange);
}
static inline HRESULT IBDA_FrequencyFilter_get_Range(IBDA_FrequencyFilter* This,ULONG *pulRange) {
    return This->lpVtbl->get_Range(This,pulRange);
}
static inline HRESULT IBDA_FrequencyFilter_put_Bandwidth(IBDA_FrequencyFilter* This,ULONG ulBandwidth) {
    return This->lpVtbl->put_Bandwidth(This,ulBandwidth);
}
static inline HRESULT IBDA_FrequencyFilter_get_Bandwidth(IBDA_FrequencyFilter* This,ULONG *pulBandwidth) {
    return This->lpVtbl->get_Bandwidth(This,pulBandwidth);
}
static inline HRESULT IBDA_FrequencyFilter_put_FrequencyMultiplier(IBDA_FrequencyFilter* This,ULONG ulMultiplier) {
    return This->lpVtbl->put_FrequencyMultiplier(This,ulMultiplier);
}
static inline HRESULT IBDA_FrequencyFilter_get_FrequencyMultiplier(IBDA_FrequencyFilter* This,ULONG *pulMultiplier) {
    return This->lpVtbl->get_FrequencyMultiplier(This,pulMultiplier);
}
#endif
#endif

#endif


#endif  /* __IBDA_FrequencyFilter_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IBDA_LNBInfo interface
 */
#ifndef __IBDA_LNBInfo_INTERFACE_DEFINED__
#define __IBDA_LNBInfo_INTERFACE_DEFINED__

DEFINE_GUID(IID_IBDA_LNBInfo, 0x992cf102, 0x49f9, 0x4719, 0xa6,0x64, 0xc4,0xf2,0x3e,0x24,0x08,0xf4);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("992cf102-49f9-4719-a664-c4f23e2408f4")
IBDA_LNBInfo : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE put_LocalOscilatorFrequencyLowBand(
        ULONG ulLOFLow) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_LocalOscilatorFrequencyLowBand(
        ULONG *pulLOFLow) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_LocalOscilatorFrequencyHighBand(
        ULONG ulLOFHigh) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_LocalOscilatorFrequencyHighBand(
        ULONG *pulLOFHigh) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_HighLowSwitchFrequency(
        ULONG ulSwitchFrequency) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_HighLowSwitchFrequency(
        ULONG *pulSwitchFrequency) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IBDA_LNBInfo, 0x992cf102, 0x49f9, 0x4719, 0xa6,0x64, 0xc4,0xf2,0x3e,0x24,0x08,0xf4)
#endif
#else
typedef struct IBDA_LNBInfoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IBDA_LNBInfo *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IBDA_LNBInfo *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IBDA_LNBInfo *This);

    /*** IBDA_LNBInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *put_LocalOscilatorFrequencyLowBand)(
        IBDA_LNBInfo *This,
        ULONG ulLOFLow);

    HRESULT (STDMETHODCALLTYPE *get_LocalOscilatorFrequencyLowBand)(
        IBDA_LNBInfo *This,
        ULONG *pulLOFLow);

    HRESULT (STDMETHODCALLTYPE *put_LocalOscilatorFrequencyHighBand)(
        IBDA_LNBInfo *This,
        ULONG ulLOFHigh);

    HRESULT (STDMETHODCALLTYPE *get_LocalOscilatorFrequencyHighBand)(
        IBDA_LNBInfo *This,
        ULONG *pulLOFHigh);

    HRESULT (STDMETHODCALLTYPE *put_HighLowSwitchFrequency)(
        IBDA_LNBInfo *This,
        ULONG ulSwitchFrequency);

    HRESULT (STDMETHODCALLTYPE *get_HighLowSwitchFrequency)(
        IBDA_LNBInfo *This,
        ULONG *pulSwitchFrequency);

    END_INTERFACE
} IBDA_LNBInfoVtbl;

interface IBDA_LNBInfo {
    CONST_VTBL IBDA_LNBInfoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IBDA_LNBInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IBDA_LNBInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IBDA_LNBInfo_Release(This) (This)->lpVtbl->Release(This)
/*** IBDA_LNBInfo methods ***/
#define IBDA_LNBInfo_put_LocalOscilatorFrequencyLowBand(This,ulLOFLow) (This)->lpVtbl->put_LocalOscilatorFrequencyLowBand(This,ulLOFLow)
#define IBDA_LNBInfo_get_LocalOscilatorFrequencyLowBand(This,pulLOFLow) (This)->lpVtbl->get_LocalOscilatorFrequencyLowBand(This,pulLOFLow)
#define IBDA_LNBInfo_put_LocalOscilatorFrequencyHighBand(This,ulLOFHigh) (This)->lpVtbl->put_LocalOscilatorFrequencyHighBand(This,ulLOFHigh)
#define IBDA_LNBInfo_get_LocalOscilatorFrequencyHighBand(This,pulLOFHigh) (This)->lpVtbl->get_LocalOscilatorFrequencyHighBand(This,pulLOFHigh)
#define IBDA_LNBInfo_put_HighLowSwitchFrequency(This,ulSwitchFrequency) (This)->lpVtbl->put_HighLowSwitchFrequency(This,ulSwitchFrequency)
#define IBDA_LNBInfo_get_HighLowSwitchFrequency(This,pulSwitchFrequency) (This)->lpVtbl->get_HighLowSwitchFrequency(This,pulSwitchFrequency)
#else
/*** IUnknown methods ***/
static inline HRESULT IBDA_LNBInfo_QueryInterface(IBDA_LNBInfo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IBDA_LNBInfo_AddRef(IBDA_LNBInfo* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IBDA_LNBInfo_Release(IBDA_LNBInfo* This) {
    return This->lpVtbl->Release(This);
}
/*** IBDA_LNBInfo methods ***/
static inline HRESULT IBDA_LNBInfo_put_LocalOscilatorFrequencyLowBand(IBDA_LNBInfo* This,ULONG ulLOFLow) {
    return This->lpVtbl->put_LocalOscilatorFrequencyLowBand(This,ulLOFLow);
}
static inline HRESULT IBDA_LNBInfo_get_LocalOscilatorFrequencyLowBand(IBDA_LNBInfo* This,ULONG *pulLOFLow) {
    return This->lpVtbl->get_LocalOscilatorFrequencyLowBand(This,pulLOFLow);
}
static inline HRESULT IBDA_LNBInfo_put_LocalOscilatorFrequencyHighBand(IBDA_LNBInfo* This,ULONG ulLOFHigh) {
    return This->lpVtbl->put_LocalOscilatorFrequencyHighBand(This,ulLOFHigh);
}
static inline HRESULT IBDA_LNBInfo_get_LocalOscilatorFrequencyHighBand(IBDA_LNBInfo* This,ULONG *pulLOFHigh) {
    return This->lpVtbl->get_LocalOscilatorFrequencyHighBand(This,pulLOFHigh);
}
static inline HRESULT IBDA_LNBInfo_put_HighLowSwitchFrequency(IBDA_LNBInfo* This,ULONG ulSwitchFrequency) {
    return This->lpVtbl->put_HighLowSwitchFrequency(This,ulSwitchFrequency);
}
static inline HRESULT IBDA_LNBInfo_get_HighLowSwitchFrequency(IBDA_LNBInfo* This,ULONG *pulSwitchFrequency) {
    return This->lpVtbl->get_HighLowSwitchFrequency(This,pulSwitchFrequency);
}
#endif
#endif

#endif


#endif  /* __IBDA_LNBInfo_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IBDA_DiseqCommand interface
 */
#ifndef __IBDA_DiseqCommand_INTERFACE_DEFINED__
#define __IBDA_DiseqCommand_INTERFACE_DEFINED__

DEFINE_GUID(IID_IBDA_DiseqCommand, 0xf84e2ab0, 0x3c6b, 0x45e3, 0xa0,0xfc, 0x86,0x69,0xd4,0xb8,0x1f,0x11);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("f84e2ab0-3c6b-45e3-a0fc-8669d4b81f11")
IBDA_DiseqCommand : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE put_EnableDiseqCommands(
        BOOLEAN bEnable) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_DiseqLNBSource(
        ULONG ulLNBSource) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_DiseqUseToneBurst(
        BOOLEAN bUseToneBurst) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_DiseqRepeats(
        ULONG ulRepeats) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_DiseqSendCommand(
        ULONG ulRequestId,
        ULONG ulcbCommandLen,
        BYTE *pbCommand) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_DiseqResponse(
        ULONG ulRequestId,
        ULONG *pulcbResponseLen,
        BYTE pbResponse[]) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IBDA_DiseqCommand, 0xf84e2ab0, 0x3c6b, 0x45e3, 0xa0,0xfc, 0x86,0x69,0xd4,0xb8,0x1f,0x11)
#endif
#else
typedef struct IBDA_DiseqCommandVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IBDA_DiseqCommand *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IBDA_DiseqCommand *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IBDA_DiseqCommand *This);

    /*** IBDA_DiseqCommand methods ***/
    HRESULT (STDMETHODCALLTYPE *put_EnableDiseqCommands)(
        IBDA_DiseqCommand *This,
        BOOLEAN bEnable);

    HRESULT (STDMETHODCALLTYPE *put_DiseqLNBSource)(
        IBDA_DiseqCommand *This,
        ULONG ulLNBSource);

    HRESULT (STDMETHODCALLTYPE *put_DiseqUseToneBurst)(
        IBDA_DiseqCommand *This,
        BOOLEAN bUseToneBurst);

    HRESULT (STDMETHODCALLTYPE *put_DiseqRepeats)(
        IBDA_DiseqCommand *This,
        ULONG ulRepeats);

    HRESULT (STDMETHODCALLTYPE *put_DiseqSendCommand)(
        IBDA_DiseqCommand *This,
        ULONG ulRequestId,
        ULONG ulcbCommandLen,
        BYTE *pbCommand);

    HRESULT (STDMETHODCALLTYPE *get_DiseqResponse)(
        IBDA_DiseqCommand *This,
        ULONG ulRequestId,
        ULONG *pulcbResponseLen,
        BYTE pbResponse[]);

    END_INTERFACE
} IBDA_DiseqCommandVtbl;

interface IBDA_DiseqCommand {
    CONST_VTBL IBDA_DiseqCommandVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IBDA_DiseqCommand_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IBDA_DiseqCommand_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IBDA_DiseqCommand_Release(This) (This)->lpVtbl->Release(This)
/*** IBDA_DiseqCommand methods ***/
#define IBDA_DiseqCommand_put_EnableDiseqCommands(This,bEnable) (This)->lpVtbl->put_EnableDiseqCommands(This,bEnable)
#define IBDA_DiseqCommand_put_DiseqLNBSource(This,ulLNBSource) (This)->lpVtbl->put_DiseqLNBSource(This,ulLNBSource)
#define IBDA_DiseqCommand_put_DiseqUseToneBurst(This,bUseToneBurst) (This)->lpVtbl->put_DiseqUseToneBurst(This,bUseToneBurst)
#define IBDA_DiseqCommand_put_DiseqRepeats(This,ulRepeats) (This)->lpVtbl->put_DiseqRepeats(This,ulRepeats)
#define IBDA_DiseqCommand_put_DiseqSendCommand(This,ulRequestId,ulcbCommandLen,pbCommand) (This)->lpVtbl->put_DiseqSendCommand(This,ulRequestId,ulcbCommandLen,pbCommand)
#define IBDA_DiseqCommand_get_DiseqResponse(This,ulRequestId,pulcbResponseLen,pbResponse) (This)->lpVtbl->get_DiseqResponse(This,ulRequestId,pulcbResponseLen,pbResponse)
#else
/*** IUnknown methods ***/
static inline HRESULT IBDA_DiseqCommand_QueryInterface(IBDA_DiseqCommand* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IBDA_DiseqCommand_AddRef(IBDA_DiseqCommand* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IBDA_DiseqCommand_Release(IBDA_DiseqCommand* This) {
    return This->lpVtbl->Release(This);
}
/*** IBDA_DiseqCommand methods ***/
static inline HRESULT IBDA_DiseqCommand_put_EnableDiseqCommands(IBDA_DiseqCommand* This,BOOLEAN bEnable) {
    return This->lpVtbl->put_EnableDiseqCommands(This,bEnable);
}
static inline HRESULT IBDA_DiseqCommand_put_DiseqLNBSource(IBDA_DiseqCommand* This,ULONG ulLNBSource) {
    return This->lpVtbl->put_DiseqLNBSource(This,ulLNBSource);
}
static inline HRESULT IBDA_DiseqCommand_put_DiseqUseToneBurst(IBDA_DiseqCommand* This,BOOLEAN bUseToneBurst) {
    return This->lpVtbl->put_DiseqUseToneBurst(This,bUseToneBurst);
}
static inline HRESULT IBDA_DiseqCommand_put_DiseqRepeats(IBDA_DiseqCommand* This,ULONG ulRepeats) {
    return This->lpVtbl->put_DiseqRepeats(This,ulRepeats);
}
static inline HRESULT IBDA_DiseqCommand_put_DiseqSendCommand(IBDA_DiseqCommand* This,ULONG ulRequestId,ULONG ulcbCommandLen,BYTE *pbCommand) {
    return This->lpVtbl->put_DiseqSendCommand(This,ulRequestId,ulcbCommandLen,pbCommand);
}
static inline HRESULT IBDA_DiseqCommand_get_DiseqResponse(IBDA_DiseqCommand* This,ULONG ulRequestId,ULONG *pulcbResponseLen,BYTE pbResponse[]) {
    return This->lpVtbl->get_DiseqResponse(This,ulRequestId,pulcbResponseLen,pbResponse);
}
#endif
#endif

#endif


#endif  /* __IBDA_DiseqCommand_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IBDA_AutoDemodulate interface
 */
#ifndef __IBDA_AutoDemodulate_INTERFACE_DEFINED__
#define __IBDA_AutoDemodulate_INTERFACE_DEFINED__

DEFINE_GUID(IID_IBDA_AutoDemodulate, 0xddf15b12, 0xbd25, 0x11d2, 0x9c,0xa0, 0x00,0xc0,0x4f,0x79,0x71,0xe0);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("ddf15b12-bd25-11d2-9ca0-00c04f7971e0")
IBDA_AutoDemodulate : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE put_AutoDemodulate(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IBDA_AutoDemodulate, 0xddf15b12, 0xbd25, 0x11d2, 0x9c,0xa0, 0x00,0xc0,0x4f,0x79,0x71,0xe0)
#endif
#else
typedef struct IBDA_AutoDemodulateVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IBDA_AutoDemodulate *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IBDA_AutoDemodulate *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IBDA_AutoDemodulate *This);

    /*** IBDA_AutoDemodulate methods ***/
    HRESULT (STDMETHODCALLTYPE *put_AutoDemodulate)(
        IBDA_AutoDemodulate *This);

    END_INTERFACE
} IBDA_AutoDemodulateVtbl;

interface IBDA_AutoDemodulate {
    CONST_VTBL IBDA_AutoDemodulateVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IBDA_AutoDemodulate_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IBDA_AutoDemodulate_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IBDA_AutoDemodulate_Release(This) (This)->lpVtbl->Release(This)
/*** IBDA_AutoDemodulate methods ***/
#define IBDA_AutoDemodulate_put_AutoDemodulate(This) (This)->lpVtbl->put_AutoDemodulate(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IBDA_AutoDemodulate_QueryInterface(IBDA_AutoDemodulate* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IBDA_AutoDemodulate_AddRef(IBDA_AutoDemodulate* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IBDA_AutoDemodulate_Release(IBDA_AutoDemodulate* This) {
    return This->lpVtbl->Release(This);
}
/*** IBDA_AutoDemodulate methods ***/
static inline HRESULT IBDA_AutoDemodulate_put_AutoDemodulate(IBDA_AutoDemodulate* This) {
    return This->lpVtbl->put_AutoDemodulate(This);
}
#endif
#endif

#endif


#endif  /* __IBDA_AutoDemodulate_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IBDA_AutoDemodulateEx interface
 */
#ifndef __IBDA_AutoDemodulateEx_INTERFACE_DEFINED__
#define __IBDA_AutoDemodulateEx_INTERFACE_DEFINED__

DEFINE_GUID(IID_IBDA_AutoDemodulateEx, 0x34518d13, 0x1182, 0x48e6, 0xb2,0x8f, 0xb2,0x49,0x87,0x78,0x73,0x26);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("34518d13-1182-48e6-b28f-b24987787326")
IBDA_AutoDemodulateEx : public IBDA_AutoDemodulate
{
    virtual HRESULT STDMETHODCALLTYPE get_SupportedDeviceNodeTypes(
        ULONG ulcDeviceNodeTypesMax,
        ULONG *pulcDeviceNodeTypes,
        GUID *pguidDeviceNodeTypes) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_SupportedVideoFormats(
        ULONG *pulAMTunerModeType,
        ULONG *pulAnalogVideoStandard) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_AuxInputCount(
        ULONG *pulCompositeCount,
        ULONG *pulSvideoCount) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IBDA_AutoDemodulateEx, 0x34518d13, 0x1182, 0x48e6, 0xb2,0x8f, 0xb2,0x49,0x87,0x78,0x73,0x26)
#endif
#else
typedef struct IBDA_AutoDemodulateExVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IBDA_AutoDemodulateEx *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IBDA_AutoDemodulateEx *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IBDA_AutoDemodulateEx *This);

    /*** IBDA_AutoDemodulate methods ***/
    HRESULT (STDMETHODCALLTYPE *put_AutoDemodulate)(
        IBDA_AutoDemodulateEx *This);

    /*** IBDA_AutoDemodulateEx methods ***/
    HRESULT (STDMETHODCALLTYPE *get_SupportedDeviceNodeTypes)(
        IBDA_AutoDemodulateEx *This,
        ULONG ulcDeviceNodeTypesMax,
        ULONG *pulcDeviceNodeTypes,
        GUID *pguidDeviceNodeTypes);

    HRESULT (STDMETHODCALLTYPE *get_SupportedVideoFormats)(
        IBDA_AutoDemodulateEx *This,
        ULONG *pulAMTunerModeType,
        ULONG *pulAnalogVideoStandard);

    HRESULT (STDMETHODCALLTYPE *get_AuxInputCount)(
        IBDA_AutoDemodulateEx *This,
        ULONG *pulCompositeCount,
        ULONG *pulSvideoCount);

    END_INTERFACE
} IBDA_AutoDemodulateExVtbl;

interface IBDA_AutoDemodulateEx {
    CONST_VTBL IBDA_AutoDemodulateExVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IBDA_AutoDemodulateEx_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IBDA_AutoDemodulateEx_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IBDA_AutoDemodulateEx_Release(This) (This)->lpVtbl->Release(This)
/*** IBDA_AutoDemodulate methods ***/
#define IBDA_AutoDemodulateEx_put_AutoDemodulate(This) (This)->lpVtbl->put_AutoDemodulate(This)
/*** IBDA_AutoDemodulateEx methods ***/
#define IBDA_AutoDemodulateEx_get_SupportedDeviceNodeTypes(This,ulcDeviceNodeTypesMax,pulcDeviceNodeTypes,pguidDeviceNodeTypes) (This)->lpVtbl->get_SupportedDeviceNodeTypes(This,ulcDeviceNodeTypesMax,pulcDeviceNodeTypes,pguidDeviceNodeTypes)
#define IBDA_AutoDemodulateEx_get_SupportedVideoFormats(This,pulAMTunerModeType,pulAnalogVideoStandard) (This)->lpVtbl->get_SupportedVideoFormats(This,pulAMTunerModeType,pulAnalogVideoStandard)
#define IBDA_AutoDemodulateEx_get_AuxInputCount(This,pulCompositeCount,pulSvideoCount) (This)->lpVtbl->get_AuxInputCount(This,pulCompositeCount,pulSvideoCount)
#else
/*** IUnknown methods ***/
static inline HRESULT IBDA_AutoDemodulateEx_QueryInterface(IBDA_AutoDemodulateEx* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IBDA_AutoDemodulateEx_AddRef(IBDA_AutoDemodulateEx* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IBDA_AutoDemodulateEx_Release(IBDA_AutoDemodulateEx* This) {
    return This->lpVtbl->Release(This);
}
/*** IBDA_AutoDemodulate methods ***/
static inline HRESULT IBDA_AutoDemodulateEx_put_AutoDemodulate(IBDA_AutoDemodulateEx* This) {
    return This->lpVtbl->put_AutoDemodulate(This);
}
/*** IBDA_AutoDemodulateEx methods ***/
static inline HRESULT IBDA_AutoDemodulateEx_get_SupportedDeviceNodeTypes(IBDA_AutoDemodulateEx* This,ULONG ulcDeviceNodeTypesMax,ULONG *pulcDeviceNodeTypes,GUID *pguidDeviceNodeTypes) {
    return This->lpVtbl->get_SupportedDeviceNodeTypes(This,ulcDeviceNodeTypesMax,pulcDeviceNodeTypes,pguidDeviceNodeTypes);
}
static inline HRESULT IBDA_AutoDemodulateEx_get_SupportedVideoFormats(IBDA_AutoDemodulateEx* This,ULONG *pulAMTunerModeType,ULONG *pulAnalogVideoStandard) {
    return This->lpVtbl->get_SupportedVideoFormats(This,pulAMTunerModeType,pulAnalogVideoStandard);
}
static inline HRESULT IBDA_AutoDemodulateEx_get_AuxInputCount(IBDA_AutoDemodulateEx* This,ULONG *pulCompositeCount,ULONG *pulSvideoCount) {
    return This->lpVtbl->get_AuxInputCount(This,pulCompositeCount,pulSvideoCount);
}
#endif
#endif

#endif


#endif  /* __IBDA_AutoDemodulateEx_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IBDA_DigitalDemodulator interface
 */
#ifndef __IBDA_DigitalDemodulator_INTERFACE_DEFINED__
#define __IBDA_DigitalDemodulator_INTERFACE_DEFINED__

DEFINE_GUID(IID_IBDA_DigitalDemodulator, 0xef30f379, 0x985b, 0x4d10, 0xb6,0x40, 0xa7,0x9d,0x5e,0x04,0xe1,0xe0);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("ef30f379-985b-4d10-b640-a79d5e04e1e0")
IBDA_DigitalDemodulator : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE put_ModulationType(
        ModulationType *pModulationType) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ModulationType(
        ModulationType *pModulationType) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_InnerFECMethod(
        FECMethod *pFECMethod) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_InnerFECMethod(
        FECMethod *pFECMethod) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_InnerFECRate(
        BinaryConvolutionCodeRate *pFECRate) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_InnerFECRate(
        BinaryConvolutionCodeRate *pFECRate) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_OuterFECMethod(
        FECMethod *pFECMethod) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_OuterFECMethod(
        FECMethod *pFECMethod) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_OuterFECRate(
        BinaryConvolutionCodeRate *pFECRate) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_OuterFECRate(
        BinaryConvolutionCodeRate *pFECRate) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_SymbolRate(
        ULONG *pSymbolRate) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_SymbolRate(
        ULONG *pSymbolRate) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_SpectralInversion(
        SpectralInversion *pSpectralInversion) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_SpectralInversion(
        SpectralInversion *pSpectralInversion) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IBDA_DigitalDemodulator, 0xef30f379, 0x985b, 0x4d10, 0xb6,0x40, 0xa7,0x9d,0x5e,0x04,0xe1,0xe0)
#endif
#else
typedef struct IBDA_DigitalDemodulatorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IBDA_DigitalDemodulator *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IBDA_DigitalDemodulator *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IBDA_DigitalDemodulator *This);

    /*** IBDA_DigitalDemodulator methods ***/
    HRESULT (STDMETHODCALLTYPE *put_ModulationType)(
        IBDA_DigitalDemodulator *This,
        ModulationType *pModulationType);

    HRESULT (STDMETHODCALLTYPE *get_ModulationType)(
        IBDA_DigitalDemodulator *This,
        ModulationType *pModulationType);

    HRESULT (STDMETHODCALLTYPE *put_InnerFECMethod)(
        IBDA_DigitalDemodulator *This,
        FECMethod *pFECMethod);

    HRESULT (STDMETHODCALLTYPE *get_InnerFECMethod)(
        IBDA_DigitalDemodulator *This,
        FECMethod *pFECMethod);

    HRESULT (STDMETHODCALLTYPE *put_InnerFECRate)(
        IBDA_DigitalDemodulator *This,
        BinaryConvolutionCodeRate *pFECRate);

    HRESULT (STDMETHODCALLTYPE *get_InnerFECRate)(
        IBDA_DigitalDemodulator *This,
        BinaryConvolutionCodeRate *pFECRate);

    HRESULT (STDMETHODCALLTYPE *put_OuterFECMethod)(
        IBDA_DigitalDemodulator *This,
        FECMethod *pFECMethod);

    HRESULT (STDMETHODCALLTYPE *get_OuterFECMethod)(
        IBDA_DigitalDemodulator *This,
        FECMethod *pFECMethod);

    HRESULT (STDMETHODCALLTYPE *put_OuterFECRate)(
        IBDA_DigitalDemodulator *This,
        BinaryConvolutionCodeRate *pFECRate);

    HRESULT (STDMETHODCALLTYPE *get_OuterFECRate)(
        IBDA_DigitalDemodulator *This,
        BinaryConvolutionCodeRate *pFECRate);

    HRESULT (STDMETHODCALLTYPE *put_SymbolRate)(
        IBDA_DigitalDemodulator *This,
        ULONG *pSymbolRate);

    HRESULT (STDMETHODCALLTYPE *get_SymbolRate)(
        IBDA_DigitalDemodulator *This,
        ULONG *pSymbolRate);

    HRESULT (STDMETHODCALLTYPE *put_SpectralInversion)(
        IBDA_DigitalDemodulator *This,
        SpectralInversion *pSpectralInversion);

    HRESULT (STDMETHODCALLTYPE *get_SpectralInversion)(
        IBDA_DigitalDemodulator *This,
        SpectralInversion *pSpectralInversion);

    END_INTERFACE
} IBDA_DigitalDemodulatorVtbl;

interface IBDA_DigitalDemodulator {
    CONST_VTBL IBDA_DigitalDemodulatorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IBDA_DigitalDemodulator_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IBDA_DigitalDemodulator_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IBDA_DigitalDemodulator_Release(This) (This)->lpVtbl->Release(This)
/*** IBDA_DigitalDemodulator methods ***/
#define IBDA_DigitalDemodulator_put_ModulationType(This,pModulationType) (This)->lpVtbl->put_ModulationType(This,pModulationType)
#define IBDA_DigitalDemodulator_get_ModulationType(This,pModulationType) (This)->lpVtbl->get_ModulationType(This,pModulationType)
#define IBDA_DigitalDemodulator_put_InnerFECMethod(This,pFECMethod) (This)->lpVtbl->put_InnerFECMethod(This,pFECMethod)
#define IBDA_DigitalDemodulator_get_InnerFECMethod(This,pFECMethod) (This)->lpVtbl->get_InnerFECMethod(This,pFECMethod)
#define IBDA_DigitalDemodulator_put_InnerFECRate(This,pFECRate) (This)->lpVtbl->put_InnerFECRate(This,pFECRate)
#define IBDA_DigitalDemodulator_get_InnerFECRate(This,pFECRate) (This)->lpVtbl->get_InnerFECRate(This,pFECRate)
#define IBDA_DigitalDemodulator_put_OuterFECMethod(This,pFECMethod) (This)->lpVtbl->put_OuterFECMethod(This,pFECMethod)
#define IBDA_DigitalDemodulator_get_OuterFECMethod(This,pFECMethod) (This)->lpVtbl->get_OuterFECMethod(This,pFECMethod)
#define IBDA_DigitalDemodulator_put_OuterFECRate(This,pFECRate) (This)->lpVtbl->put_OuterFECRate(This,pFECRate)
#define IBDA_DigitalDemodulator_get_OuterFECRate(This,pFECRate) (This)->lpVtbl->get_OuterFECRate(This,pFECRate)
#define IBDA_DigitalDemodulator_put_SymbolRate(This,pSymbolRate) (This)->lpVtbl->put_SymbolRate(This,pSymbolRate)
#define IBDA_DigitalDemodulator_get_SymbolRate(This,pSymbolRate) (This)->lpVtbl->get_SymbolRate(This,pSymbolRate)
#define IBDA_DigitalDemodulator_put_SpectralInversion(This,pSpectralInversion) (This)->lpVtbl->put_SpectralInversion(This,pSpectralInversion)
#define IBDA_DigitalDemodulator_get_SpectralInversion(This,pSpectralInversion) (This)->lpVtbl->get_SpectralInversion(This,pSpectralInversion)
#else
/*** IUnknown methods ***/
static inline HRESULT IBDA_DigitalDemodulator_QueryInterface(IBDA_DigitalDemodulator* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IBDA_DigitalDemodulator_AddRef(IBDA_DigitalDemodulator* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IBDA_DigitalDemodulator_Release(IBDA_DigitalDemodulator* This) {
    return This->lpVtbl->Release(This);
}
/*** IBDA_DigitalDemodulator methods ***/
static inline HRESULT IBDA_DigitalDemodulator_put_ModulationType(IBDA_DigitalDemodulator* This,ModulationType *pModulationType) {
    return This->lpVtbl->put_ModulationType(This,pModulationType);
}
static inline HRESULT IBDA_DigitalDemodulator_get_ModulationType(IBDA_DigitalDemodulator* This,ModulationType *pModulationType) {
    return This->lpVtbl->get_ModulationType(This,pModulationType);
}
static inline HRESULT IBDA_DigitalDemodulator_put_InnerFECMethod(IBDA_DigitalDemodulator* This,FECMethod *pFECMethod) {
    return This->lpVtbl->put_InnerFECMethod(This,pFECMethod);
}
static inline HRESULT IBDA_DigitalDemodulator_get_InnerFECMethod(IBDA_DigitalDemodulator* This,FECMethod *pFECMethod) {
    return This->lpVtbl->get_InnerFECMethod(This,pFECMethod);
}
static inline HRESULT IBDA_DigitalDemodulator_put_InnerFECRate(IBDA_DigitalDemodulator* This,BinaryConvolutionCodeRate *pFECRate) {
    return This->lpVtbl->put_InnerFECRate(This,pFECRate);
}
static inline HRESULT IBDA_DigitalDemodulator_get_InnerFECRate(IBDA_DigitalDemodulator* This,BinaryConvolutionCodeRate *pFECRate) {
    return This->lpVtbl->get_InnerFECRate(This,pFECRate);
}
static inline HRESULT IBDA_DigitalDemodulator_put_OuterFECMethod(IBDA_DigitalDemodulator* This,FECMethod *pFECMethod) {
    return This->lpVtbl->put_OuterFECMethod(This,pFECMethod);
}
static inline HRESULT IBDA_DigitalDemodulator_get_OuterFECMethod(IBDA_DigitalDemodulator* This,FECMethod *pFECMethod) {
    return This->lpVtbl->get_OuterFECMethod(This,pFECMethod);
}
static inline HRESULT IBDA_DigitalDemodulator_put_OuterFECRate(IBDA_DigitalDemodulator* This,BinaryConvolutionCodeRate *pFECRate) {
    return This->lpVtbl->put_OuterFECRate(This,pFECRate);
}
static inline HRESULT IBDA_DigitalDemodulator_get_OuterFECRate(IBDA_DigitalDemodulator* This,BinaryConvolutionCodeRate *pFECRate) {
    return This->lpVtbl->get_OuterFECRate(This,pFECRate);
}
static inline HRESULT IBDA_DigitalDemodulator_put_SymbolRate(IBDA_DigitalDemodulator* This,ULONG *pSymbolRate) {
    return This->lpVtbl->put_SymbolRate(This,pSymbolRate);
}
static inline HRESULT IBDA_DigitalDemodulator_get_SymbolRate(IBDA_DigitalDemodulator* This,ULONG *pSymbolRate) {
    return This->lpVtbl->get_SymbolRate(This,pSymbolRate);
}
static inline HRESULT IBDA_DigitalDemodulator_put_SpectralInversion(IBDA_DigitalDemodulator* This,SpectralInversion *pSpectralInversion) {
    return This->lpVtbl->put_SpectralInversion(This,pSpectralInversion);
}
static inline HRESULT IBDA_DigitalDemodulator_get_SpectralInversion(IBDA_DigitalDemodulator* This,SpectralInversion *pSpectralInversion) {
    return This->lpVtbl->get_SpectralInversion(This,pSpectralInversion);
}
#endif
#endif

#endif


#endif  /* __IBDA_DigitalDemodulator_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IBDA_DigitalDemodulator2 interface
 */
#ifndef __IBDA_DigitalDemodulator2_INTERFACE_DEFINED__
#define __IBDA_DigitalDemodulator2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IBDA_DigitalDemodulator2, 0x525ed3ee, 0x5cf3, 0x4e1e, 0x9a,0x06, 0x53,0x68,0xa8,0x4f,0x9a,0x6e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("525ed3ee-5cf3-4e1e-9a06-5368a84f9a6e")
IBDA_DigitalDemodulator2 : public IBDA_DigitalDemodulator
{
    virtual HRESULT STDMETHODCALLTYPE put_GuardInterval(
        GuardInterval *pGuardInterval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_GuardInterval(
        GuardInterval *pGuardInterval) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_TransmissionMode(
        TransmissionMode *pTransmissionMode) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_TransmissionMode(
        TransmissionMode *pTransmissionMode) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_RollOff(
        RollOff *pRollOff) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_RollOff(
        RollOff *pRollOff) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Pilot(
        Pilot *pPilot) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Pilot(
        Pilot *pPilot) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IBDA_DigitalDemodulator2, 0x525ed3ee, 0x5cf3, 0x4e1e, 0x9a,0x06, 0x53,0x68,0xa8,0x4f,0x9a,0x6e)
#endif
#else
typedef struct IBDA_DigitalDemodulator2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IBDA_DigitalDemodulator2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IBDA_DigitalDemodulator2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IBDA_DigitalDemodulator2 *This);

    /*** IBDA_DigitalDemodulator methods ***/
    HRESULT (STDMETHODCALLTYPE *put_ModulationType)(
        IBDA_DigitalDemodulator2 *This,
        ModulationType *pModulationType);

    HRESULT (STDMETHODCALLTYPE *get_ModulationType)(
        IBDA_DigitalDemodulator2 *This,
        ModulationType *pModulationType);

    HRESULT (STDMETHODCALLTYPE *put_InnerFECMethod)(
        IBDA_DigitalDemodulator2 *This,
        FECMethod *pFECMethod);

    HRESULT (STDMETHODCALLTYPE *get_InnerFECMethod)(
        IBDA_DigitalDemodulator2 *This,
        FECMethod *pFECMethod);

    HRESULT (STDMETHODCALLTYPE *put_InnerFECRate)(
        IBDA_DigitalDemodulator2 *This,
        BinaryConvolutionCodeRate *pFECRate);

    HRESULT (STDMETHODCALLTYPE *get_InnerFECRate)(
        IBDA_DigitalDemodulator2 *This,
        BinaryConvolutionCodeRate *pFECRate);

    HRESULT (STDMETHODCALLTYPE *put_OuterFECMethod)(
        IBDA_DigitalDemodulator2 *This,
        FECMethod *pFECMethod);

    HRESULT (STDMETHODCALLTYPE *get_OuterFECMethod)(
        IBDA_DigitalDemodulator2 *This,
        FECMethod *pFECMethod);

    HRESULT (STDMETHODCALLTYPE *put_OuterFECRate)(
        IBDA_DigitalDemodulator2 *This,
        BinaryConvolutionCodeRate *pFECRate);

    HRESULT (STDMETHODCALLTYPE *get_OuterFECRate)(
        IBDA_DigitalDemodulator2 *This,
        BinaryConvolutionCodeRate *pFECRate);

    HRESULT (STDMETHODCALLTYPE *put_SymbolRate)(
        IBDA_DigitalDemodulator2 *This,
        ULONG *pSymbolRate);

    HRESULT (STDMETHODCALLTYPE *get_SymbolRate)(
        IBDA_DigitalDemodulator2 *This,
        ULONG *pSymbolRate);

    HRESULT (STDMETHODCALLTYPE *put_SpectralInversion)(
        IBDA_DigitalDemodulator2 *This,
        SpectralInversion *pSpectralInversion);

    HRESULT (STDMETHODCALLTYPE *get_SpectralInversion)(
        IBDA_DigitalDemodulator2 *This,
        SpectralInversion *pSpectralInversion);

    /*** IBDA_DigitalDemodulator2 methods ***/
    HRESULT (STDMETHODCALLTYPE *put_GuardInterval)(
        IBDA_DigitalDemodulator2 *This,
        GuardInterval *pGuardInterval);

    HRESULT (STDMETHODCALLTYPE *get_GuardInterval)(
        IBDA_DigitalDemodulator2 *This,
        GuardInterval *pGuardInterval);

    HRESULT (STDMETHODCALLTYPE *put_TransmissionMode)(
        IBDA_DigitalDemodulator2 *This,
        TransmissionMode *pTransmissionMode);

    HRESULT (STDMETHODCALLTYPE *get_TransmissionMode)(
        IBDA_DigitalDemodulator2 *This,
        TransmissionMode *pTransmissionMode);

    HRESULT (STDMETHODCALLTYPE *put_RollOff)(
        IBDA_DigitalDemodulator2 *This,
        RollOff *pRollOff);

    HRESULT (STDMETHODCALLTYPE *get_RollOff)(
        IBDA_DigitalDemodulator2 *This,
        RollOff *pRollOff);

    HRESULT (STDMETHODCALLTYPE *put_Pilot)(
        IBDA_DigitalDemodulator2 *This,
        Pilot *pPilot);

    HRESULT (STDMETHODCALLTYPE *get_Pilot)(
        IBDA_DigitalDemodulator2 *This,
        Pilot *pPilot);

    END_INTERFACE
} IBDA_DigitalDemodulator2Vtbl;

interface IBDA_DigitalDemodulator2 {
    CONST_VTBL IBDA_DigitalDemodulator2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IBDA_DigitalDemodulator2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IBDA_DigitalDemodulator2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IBDA_DigitalDemodulator2_Release(This) (This)->lpVtbl->Release(This)
/*** IBDA_DigitalDemodulator methods ***/
#define IBDA_DigitalDemodulator2_put_ModulationType(This,pModulationType) (This)->lpVtbl->put_ModulationType(This,pModulationType)
#define IBDA_DigitalDemodulator2_get_ModulationType(This,pModulationType) (This)->lpVtbl->get_ModulationType(This,pModulationType)
#define IBDA_DigitalDemodulator2_put_InnerFECMethod(This,pFECMethod) (This)->lpVtbl->put_InnerFECMethod(This,pFECMethod)
#define IBDA_DigitalDemodulator2_get_InnerFECMethod(This,pFECMethod) (This)->lpVtbl->get_InnerFECMethod(This,pFECMethod)
#define IBDA_DigitalDemodulator2_put_InnerFECRate(This,pFECRate) (This)->lpVtbl->put_InnerFECRate(This,pFECRate)
#define IBDA_DigitalDemodulator2_get_InnerFECRate(This,pFECRate) (This)->lpVtbl->get_InnerFECRate(This,pFECRate)
#define IBDA_DigitalDemodulator2_put_OuterFECMethod(This,pFECMethod) (This)->lpVtbl->put_OuterFECMethod(This,pFECMethod)
#define IBDA_DigitalDemodulator2_get_OuterFECMethod(This,pFECMethod) (This)->lpVtbl->get_OuterFECMethod(This,pFECMethod)
#define IBDA_DigitalDemodulator2_put_OuterFECRate(This,pFECRate) (This)->lpVtbl->put_OuterFECRate(This,pFECRate)
#define IBDA_DigitalDemodulator2_get_OuterFECRate(This,pFECRate) (This)->lpVtbl->get_OuterFECRate(This,pFECRate)
#define IBDA_DigitalDemodulator2_put_SymbolRate(This,pSymbolRate) (This)->lpVtbl->put_SymbolRate(This,pSymbolRate)
#define IBDA_DigitalDemodulator2_get_SymbolRate(This,pSymbolRate) (This)->lpVtbl->get_SymbolRate(This,pSymbolRate)
#define IBDA_DigitalDemodulator2_put_SpectralInversion(This,pSpectralInversion) (This)->lpVtbl->put_SpectralInversion(This,pSpectralInversion)
#define IBDA_DigitalDemodulator2_get_SpectralInversion(This,pSpectralInversion) (This)->lpVtbl->get_SpectralInversion(This,pSpectralInversion)
/*** IBDA_DigitalDemodulator2 methods ***/
#define IBDA_DigitalDemodulator2_put_GuardInterval(This,pGuardInterval) (This)->lpVtbl->put_GuardInterval(This,pGuardInterval)
#define IBDA_DigitalDemodulator2_get_GuardInterval(This,pGuardInterval) (This)->lpVtbl->get_GuardInterval(This,pGuardInterval)
#define IBDA_DigitalDemodulator2_put_TransmissionMode(This,pTransmissionMode) (This)->lpVtbl->put_TransmissionMode(This,pTransmissionMode)
#define IBDA_DigitalDemodulator2_get_TransmissionMode(This,pTransmissionMode) (This)->lpVtbl->get_TransmissionMode(This,pTransmissionMode)
#define IBDA_DigitalDemodulator2_put_RollOff(This,pRollOff) (This)->lpVtbl->put_RollOff(This,pRollOff)
#define IBDA_DigitalDemodulator2_get_RollOff(This,pRollOff) (This)->lpVtbl->get_RollOff(This,pRollOff)
#define IBDA_DigitalDemodulator2_put_Pilot(This,pPilot) (This)->lpVtbl->put_Pilot(This,pPilot)
#define IBDA_DigitalDemodulator2_get_Pilot(This,pPilot) (This)->lpVtbl->get_Pilot(This,pPilot)
#else
/*** IUnknown methods ***/
static inline HRESULT IBDA_DigitalDemodulator2_QueryInterface(IBDA_DigitalDemodulator2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IBDA_DigitalDemodulator2_AddRef(IBDA_DigitalDemodulator2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IBDA_DigitalDemodulator2_Release(IBDA_DigitalDemodulator2* This) {
    return This->lpVtbl->Release(This);
}
/*** IBDA_DigitalDemodulator methods ***/
static inline HRESULT IBDA_DigitalDemodulator2_put_ModulationType(IBDA_DigitalDemodulator2* This,ModulationType *pModulationType) {
    return This->lpVtbl->put_ModulationType(This,pModulationType);
}
static inline HRESULT IBDA_DigitalDemodulator2_get_ModulationType(IBDA_DigitalDemodulator2* This,ModulationType *pModulationType) {
    return This->lpVtbl->get_ModulationType(This,pModulationType);
}
static inline HRESULT IBDA_DigitalDemodulator2_put_InnerFECMethod(IBDA_DigitalDemodulator2* This,FECMethod *pFECMethod) {
    return This->lpVtbl->put_InnerFECMethod(This,pFECMethod);
}
static inline HRESULT IBDA_DigitalDemodulator2_get_InnerFECMethod(IBDA_DigitalDemodulator2* This,FECMethod *pFECMethod) {
    return This->lpVtbl->get_InnerFECMethod(This,pFECMethod);
}
static inline HRESULT IBDA_DigitalDemodulator2_put_InnerFECRate(IBDA_DigitalDemodulator2* This,BinaryConvolutionCodeRate *pFECRate) {
    return This->lpVtbl->put_InnerFECRate(This,pFECRate);
}
static inline HRESULT IBDA_DigitalDemodulator2_get_InnerFECRate(IBDA_DigitalDemodulator2* This,BinaryConvolutionCodeRate *pFECRate) {
    return This->lpVtbl->get_InnerFECRate(This,pFECRate);
}
static inline HRESULT IBDA_DigitalDemodulator2_put_OuterFECMethod(IBDA_DigitalDemodulator2* This,FECMethod *pFECMethod) {
    return This->lpVtbl->put_OuterFECMethod(This,pFECMethod);
}
static inline HRESULT IBDA_DigitalDemodulator2_get_OuterFECMethod(IBDA_DigitalDemodulator2* This,FECMethod *pFECMethod) {
    return This->lpVtbl->get_OuterFECMethod(This,pFECMethod);
}
static inline HRESULT IBDA_DigitalDemodulator2_put_OuterFECRate(IBDA_DigitalDemodulator2* This,BinaryConvolutionCodeRate *pFECRate) {
    return This->lpVtbl->put_OuterFECRate(This,pFECRate);
}
static inline HRESULT IBDA_DigitalDemodulator2_get_OuterFECRate(IBDA_DigitalDemodulator2* This,BinaryConvolutionCodeRate *pFECRate) {
    return This->lpVtbl->get_OuterFECRate(This,pFECRate);
}
static inline HRESULT IBDA_DigitalDemodulator2_put_SymbolRate(IBDA_DigitalDemodulator2* This,ULONG *pSymbolRate) {
    return This->lpVtbl->put_SymbolRate(This,pSymbolRate);
}
static inline HRESULT IBDA_DigitalDemodulator2_get_SymbolRate(IBDA_DigitalDemodulator2* This,ULONG *pSymbolRate) {
    return This->lpVtbl->get_SymbolRate(This,pSymbolRate);
}
static inline HRESULT IBDA_DigitalDemodulator2_put_SpectralInversion(IBDA_DigitalDemodulator2* This,SpectralInversion *pSpectralInversion) {
    return This->lpVtbl->put_SpectralInversion(This,pSpectralInversion);
}
static inline HRESULT IBDA_DigitalDemodulator2_get_SpectralInversion(IBDA_DigitalDemodulator2* This,SpectralInversion *pSpectralInversion) {
    return This->lpVtbl->get_SpectralInversion(This,pSpectralInversion);
}
/*** IBDA_DigitalDemodulator2 methods ***/
static inline HRESULT IBDA_DigitalDemodulator2_put_GuardInterval(IBDA_DigitalDemodulator2* This,GuardInterval *pGuardInterval) {
    return This->lpVtbl->put_GuardInterval(This,pGuardInterval);
}
static inline HRESULT IBDA_DigitalDemodulator2_get_GuardInterval(IBDA_DigitalDemodulator2* This,GuardInterval *pGuardInterval) {
    return This->lpVtbl->get_GuardInterval(This,pGuardInterval);
}
static inline HRESULT IBDA_DigitalDemodulator2_put_TransmissionMode(IBDA_DigitalDemodulator2* This,TransmissionMode *pTransmissionMode) {
    return This->lpVtbl->put_TransmissionMode(This,pTransmissionMode);
}
static inline HRESULT IBDA_DigitalDemodulator2_get_TransmissionMode(IBDA_DigitalDemodulator2* This,TransmissionMode *pTransmissionMode) {
    return This->lpVtbl->get_TransmissionMode(This,pTransmissionMode);
}
static inline HRESULT IBDA_DigitalDemodulator2_put_RollOff(IBDA_DigitalDemodulator2* This,RollOff *pRollOff) {
    return This->lpVtbl->put_RollOff(This,pRollOff);
}
static inline HRESULT IBDA_DigitalDemodulator2_get_RollOff(IBDA_DigitalDemodulator2* This,RollOff *pRollOff) {
    return This->lpVtbl->get_RollOff(This,pRollOff);
}
static inline HRESULT IBDA_DigitalDemodulator2_put_Pilot(IBDA_DigitalDemodulator2* This,Pilot *pPilot) {
    return This->lpVtbl->put_Pilot(This,pPilot);
}
static inline HRESULT IBDA_DigitalDemodulator2_get_Pilot(IBDA_DigitalDemodulator2* This,Pilot *pPilot) {
    return This->lpVtbl->get_Pilot(This,pPilot);
}
#endif
#endif

#endif


#endif  /* __IBDA_DigitalDemodulator2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IBDA_DigitalDemodulator3 interface
 */
#ifndef __IBDA_DigitalDemodulator3_INTERFACE_DEFINED__
#define __IBDA_DigitalDemodulator3_INTERFACE_DEFINED__

DEFINE_GUID(IID_IBDA_DigitalDemodulator3, 0x13f19604, 0x7d32, 0x4359, 0x93,0xa2, 0xa0,0x52,0x05,0xd9,0x0a,0xc9);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("13f19604-7d32-4359-93a2-a05205d90ac9")
IBDA_DigitalDemodulator3 : public IBDA_DigitalDemodulator2
{
    virtual HRESULT STDMETHODCALLTYPE put_SignalTimeouts(
        BDA_SIGNAL_TIMEOUTS *pSignalTimeouts) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_SignalTimeouts(
        BDA_SIGNAL_TIMEOUTS *pSignalTimeouts) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_PLPNumber(
        ULONG *pPLPNumber) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_PLPNumber(
        ULONG *pPLPNumber) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IBDA_DigitalDemodulator3, 0x13f19604, 0x7d32, 0x4359, 0x93,0xa2, 0xa0,0x52,0x05,0xd9,0x0a,0xc9)
#endif
#else
typedef struct IBDA_DigitalDemodulator3Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IBDA_DigitalDemodulator3 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IBDA_DigitalDemodulator3 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IBDA_DigitalDemodulator3 *This);

    /*** IBDA_DigitalDemodulator methods ***/
    HRESULT (STDMETHODCALLTYPE *put_ModulationType)(
        IBDA_DigitalDemodulator3 *This,
        ModulationType *pModulationType);

    HRESULT (STDMETHODCALLTYPE *get_ModulationType)(
        IBDA_DigitalDemodulator3 *This,
        ModulationType *pModulationType);

    HRESULT (STDMETHODCALLTYPE *put_InnerFECMethod)(
        IBDA_DigitalDemodulator3 *This,
        FECMethod *pFECMethod);

    HRESULT (STDMETHODCALLTYPE *get_InnerFECMethod)(
        IBDA_DigitalDemodulator3 *This,
        FECMethod *pFECMethod);

    HRESULT (STDMETHODCALLTYPE *put_InnerFECRate)(
        IBDA_DigitalDemodulator3 *This,
        BinaryConvolutionCodeRate *pFECRate);

    HRESULT (STDMETHODCALLTYPE *get_InnerFECRate)(
        IBDA_DigitalDemodulator3 *This,
        BinaryConvolutionCodeRate *pFECRate);

    HRESULT (STDMETHODCALLTYPE *put_OuterFECMethod)(
        IBDA_DigitalDemodulator3 *This,
        FECMethod *pFECMethod);

    HRESULT (STDMETHODCALLTYPE *get_OuterFECMethod)(
        IBDA_DigitalDemodulator3 *This,
        FECMethod *pFECMethod);

    HRESULT (STDMETHODCALLTYPE *put_OuterFECRate)(
        IBDA_DigitalDemodulator3 *This,
        BinaryConvolutionCodeRate *pFECRate);

    HRESULT (STDMETHODCALLTYPE *get_OuterFECRate)(
        IBDA_DigitalDemodulator3 *This,
        BinaryConvolutionCodeRate *pFECRate);

    HRESULT (STDMETHODCALLTYPE *put_SymbolRate)(
        IBDA_DigitalDemodulator3 *This,
        ULONG *pSymbolRate);

    HRESULT (STDMETHODCALLTYPE *get_SymbolRate)(
        IBDA_DigitalDemodulator3 *This,
        ULONG *pSymbolRate);

    HRESULT (STDMETHODCALLTYPE *put_SpectralInversion)(
        IBDA_DigitalDemodulator3 *This,
        SpectralInversion *pSpectralInversion);

    HRESULT (STDMETHODCALLTYPE *get_SpectralInversion)(
        IBDA_DigitalDemodulator3 *This,
        SpectralInversion *pSpectralInversion);

    /*** IBDA_DigitalDemodulator2 methods ***/
    HRESULT (STDMETHODCALLTYPE *put_GuardInterval)(
        IBDA_DigitalDemodulator3 *This,
        GuardInterval *pGuardInterval);

    HRESULT (STDMETHODCALLTYPE *get_GuardInterval)(
        IBDA_DigitalDemodulator3 *This,
        GuardInterval *pGuardInterval);

    HRESULT (STDMETHODCALLTYPE *put_TransmissionMode)(
        IBDA_DigitalDemodulator3 *This,
        TransmissionMode *pTransmissionMode);

    HRESULT (STDMETHODCALLTYPE *get_TransmissionMode)(
        IBDA_DigitalDemodulator3 *This,
        TransmissionMode *pTransmissionMode);

    HRESULT (STDMETHODCALLTYPE *put_RollOff)(
        IBDA_DigitalDemodulator3 *This,
        RollOff *pRollOff);

    HRESULT (STDMETHODCALLTYPE *get_RollOff)(
        IBDA_DigitalDemodulator3 *This,
        RollOff *pRollOff);

    HRESULT (STDMETHODCALLTYPE *put_Pilot)(
        IBDA_DigitalDemodulator3 *This,
        Pilot *pPilot);

    HRESULT (STDMETHODCALLTYPE *get_Pilot)(
        IBDA_DigitalDemodulator3 *This,
        Pilot *pPilot);

    /*** IBDA_DigitalDemodulator3 methods ***/
    HRESULT (STDMETHODCALLTYPE *put_SignalTimeouts)(
        IBDA_DigitalDemodulator3 *This,
        BDA_SIGNAL_TIMEOUTS *pSignalTimeouts);

    HRESULT (STDMETHODCALLTYPE *get_SignalTimeouts)(
        IBDA_DigitalDemodulator3 *This,
        BDA_SIGNAL_TIMEOUTS *pSignalTimeouts);

    HRESULT (STDMETHODCALLTYPE *put_PLPNumber)(
        IBDA_DigitalDemodulator3 *This,
        ULONG *pPLPNumber);

    HRESULT (STDMETHODCALLTYPE *get_PLPNumber)(
        IBDA_DigitalDemodulator3 *This,
        ULONG *pPLPNumber);

    END_INTERFACE
} IBDA_DigitalDemodulator3Vtbl;

interface IBDA_DigitalDemodulator3 {
    CONST_VTBL IBDA_DigitalDemodulator3Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IBDA_DigitalDemodulator3_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IBDA_DigitalDemodulator3_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IBDA_DigitalDemodulator3_Release(This) (This)->lpVtbl->Release(This)
/*** IBDA_DigitalDemodulator methods ***/
#define IBDA_DigitalDemodulator3_put_ModulationType(This,pModulationType) (This)->lpVtbl->put_ModulationType(This,pModulationType)
#define IBDA_DigitalDemodulator3_get_ModulationType(This,pModulationType) (This)->lpVtbl->get_ModulationType(This,pModulationType)
#define IBDA_DigitalDemodulator3_put_InnerFECMethod(This,pFECMethod) (This)->lpVtbl->put_InnerFECMethod(This,pFECMethod)
#define IBDA_DigitalDemodulator3_get_InnerFECMethod(This,pFECMethod) (This)->lpVtbl->get_InnerFECMethod(This,pFECMethod)
#define IBDA_DigitalDemodulator3_put_InnerFECRate(This,pFECRate) (This)->lpVtbl->put_InnerFECRate(This,pFECRate)
#define IBDA_DigitalDemodulator3_get_InnerFECRate(This,pFECRate) (This)->lpVtbl->get_InnerFECRate(This,pFECRate)
#define IBDA_DigitalDemodulator3_put_OuterFECMethod(This,pFECMethod) (This)->lpVtbl->put_OuterFECMethod(This,pFECMethod)
#define IBDA_DigitalDemodulator3_get_OuterFECMethod(This,pFECMethod) (This)->lpVtbl->get_OuterFECMethod(This,pFECMethod)
#define IBDA_DigitalDemodulator3_put_OuterFECRate(This,pFECRate) (This)->lpVtbl->put_OuterFECRate(This,pFECRate)
#define IBDA_DigitalDemodulator3_get_OuterFECRate(This,pFECRate) (This)->lpVtbl->get_OuterFECRate(This,pFECRate)
#define IBDA_DigitalDemodulator3_put_SymbolRate(This,pSymbolRate) (This)->lpVtbl->put_SymbolRate(This,pSymbolRate)
#define IBDA_DigitalDemodulator3_get_SymbolRate(This,pSymbolRate) (This)->lpVtbl->get_SymbolRate(This,pSymbolRate)
#define IBDA_DigitalDemodulator3_put_SpectralInversion(This,pSpectralInversion) (This)->lpVtbl->put_SpectralInversion(This,pSpectralInversion)
#define IBDA_DigitalDemodulator3_get_SpectralInversion(This,pSpectralInversion) (This)->lpVtbl->get_SpectralInversion(This,pSpectralInversion)
/*** IBDA_DigitalDemodulator2 methods ***/
#define IBDA_DigitalDemodulator3_put_GuardInterval(This,pGuardInterval) (This)->lpVtbl->put_GuardInterval(This,pGuardInterval)
#define IBDA_DigitalDemodulator3_get_GuardInterval(This,pGuardInterval) (This)->lpVtbl->get_GuardInterval(This,pGuardInterval)
#define IBDA_DigitalDemodulator3_put_TransmissionMode(This,pTransmissionMode) (This)->lpVtbl->put_TransmissionMode(This,pTransmissionMode)
#define IBDA_DigitalDemodulator3_get_TransmissionMode(This,pTransmissionMode) (This)->lpVtbl->get_TransmissionMode(This,pTransmissionMode)
#define IBDA_DigitalDemodulator3_put_RollOff(This,pRollOff) (This)->lpVtbl->put_RollOff(This,pRollOff)
#define IBDA_DigitalDemodulator3_get_RollOff(This,pRollOff) (This)->lpVtbl->get_RollOff(This,pRollOff)
#define IBDA_DigitalDemodulator3_put_Pilot(This,pPilot) (This)->lpVtbl->put_Pilot(This,pPilot)
#define IBDA_DigitalDemodulator3_get_Pilot(This,pPilot) (This)->lpVtbl->get_Pilot(This,pPilot)
/*** IBDA_DigitalDemodulator3 methods ***/
#define IBDA_DigitalDemodulator3_put_SignalTimeouts(This,pSignalTimeouts) (This)->lpVtbl->put_SignalTimeouts(This,pSignalTimeouts)
#define IBDA_DigitalDemodulator3_get_SignalTimeouts(This,pSignalTimeouts) (This)->lpVtbl->get_SignalTimeouts(This,pSignalTimeouts)
#define IBDA_DigitalDemodulator3_put_PLPNumber(This,pPLPNumber) (This)->lpVtbl->put_PLPNumber(This,pPLPNumber)
#define IBDA_DigitalDemodulator3_get_PLPNumber(This,pPLPNumber) (This)->lpVtbl->get_PLPNumber(This,pPLPNumber)
#else
/*** IUnknown methods ***/
static inline HRESULT IBDA_DigitalDemodulator3_QueryInterface(IBDA_DigitalDemodulator3* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IBDA_DigitalDemodulator3_AddRef(IBDA_DigitalDemodulator3* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IBDA_DigitalDemodulator3_Release(IBDA_DigitalDemodulator3* This) {
    return This->lpVtbl->Release(This);
}
/*** IBDA_DigitalDemodulator methods ***/
static inline HRESULT IBDA_DigitalDemodulator3_put_ModulationType(IBDA_DigitalDemodulator3* This,ModulationType *pModulationType) {
    return This->lpVtbl->put_ModulationType(This,pModulationType);
}
static inline HRESULT IBDA_DigitalDemodulator3_get_ModulationType(IBDA_DigitalDemodulator3* This,ModulationType *pModulationType) {
    return This->lpVtbl->get_ModulationType(This,pModulationType);
}
static inline HRESULT IBDA_DigitalDemodulator3_put_InnerFECMethod(IBDA_DigitalDemodulator3* This,FECMethod *pFECMethod) {
    return This->lpVtbl->put_InnerFECMethod(This,pFECMethod);
}
static inline HRESULT IBDA_DigitalDemodulator3_get_InnerFECMethod(IBDA_DigitalDemodulator3* This,FECMethod *pFECMethod) {
    return This->lpVtbl->get_InnerFECMethod(This,pFECMethod);
}
static inline HRESULT IBDA_DigitalDemodulator3_put_InnerFECRate(IBDA_DigitalDemodulator3* This,BinaryConvolutionCodeRate *pFECRate) {
    return This->lpVtbl->put_InnerFECRate(This,pFECRate);
}
static inline HRESULT IBDA_DigitalDemodulator3_get_InnerFECRate(IBDA_DigitalDemodulator3* This,BinaryConvolutionCodeRate *pFECRate) {
    return This->lpVtbl->get_InnerFECRate(This,pFECRate);
}
static inline HRESULT IBDA_DigitalDemodulator3_put_OuterFECMethod(IBDA_DigitalDemodulator3* This,FECMethod *pFECMethod) {
    return This->lpVtbl->put_OuterFECMethod(This,pFECMethod);
}
static inline HRESULT IBDA_DigitalDemodulator3_get_OuterFECMethod(IBDA_DigitalDemodulator3* This,FECMethod *pFECMethod) {
    return This->lpVtbl->get_OuterFECMethod(This,pFECMethod);
}
static inline HRESULT IBDA_DigitalDemodulator3_put_OuterFECRate(IBDA_DigitalDemodulator3* This,BinaryConvolutionCodeRate *pFECRate) {
    return This->lpVtbl->put_OuterFECRate(This,pFECRate);
}
static inline HRESULT IBDA_DigitalDemodulator3_get_OuterFECRate(IBDA_DigitalDemodulator3* This,BinaryConvolutionCodeRate *pFECRate) {
    return This->lpVtbl->get_OuterFECRate(This,pFECRate);
}
static inline HRESULT IBDA_DigitalDemodulator3_put_SymbolRate(IBDA_DigitalDemodulator3* This,ULONG *pSymbolRate) {
    return This->lpVtbl->put_SymbolRate(This,pSymbolRate);
}
static inline HRESULT IBDA_DigitalDemodulator3_get_SymbolRate(IBDA_DigitalDemodulator3* This,ULONG *pSymbolRate) {
    return This->lpVtbl->get_SymbolRate(This,pSymbolRate);
}
static inline HRESULT IBDA_DigitalDemodulator3_put_SpectralInversion(IBDA_DigitalDemodulator3* This,SpectralInversion *pSpectralInversion) {
    return This->lpVtbl->put_SpectralInversion(This,pSpectralInversion);
}
static inline HRESULT IBDA_DigitalDemodulator3_get_SpectralInversion(IBDA_DigitalDemodulator3* This,SpectralInversion *pSpectralInversion) {
    return This->lpVtbl->get_SpectralInversion(This,pSpectralInversion);
}
/*** IBDA_DigitalDemodulator2 methods ***/
static inline HRESULT IBDA_DigitalDemodulator3_put_GuardInterval(IBDA_DigitalDemodulator3* This,GuardInterval *pGuardInterval) {
    return This->lpVtbl->put_GuardInterval(This,pGuardInterval);
}
static inline HRESULT IBDA_DigitalDemodulator3_get_GuardInterval(IBDA_DigitalDemodulator3* This,GuardInterval *pGuardInterval) {
    return This->lpVtbl->get_GuardInterval(This,pGuardInterval);
}
static inline HRESULT IBDA_DigitalDemodulator3_put_TransmissionMode(IBDA_DigitalDemodulator3* This,TransmissionMode *pTransmissionMode) {
    return This->lpVtbl->put_TransmissionMode(This,pTransmissionMode);
}
static inline HRESULT IBDA_DigitalDemodulator3_get_TransmissionMode(IBDA_DigitalDemodulator3* This,TransmissionMode *pTransmissionMode) {
    return This->lpVtbl->get_TransmissionMode(This,pTransmissionMode);
}
static inline HRESULT IBDA_DigitalDemodulator3_put_RollOff(IBDA_DigitalDemodulator3* This,RollOff *pRollOff) {
    return This->lpVtbl->put_RollOff(This,pRollOff);
}
static inline HRESULT IBDA_DigitalDemodulator3_get_RollOff(IBDA_DigitalDemodulator3* This,RollOff *pRollOff) {
    return This->lpVtbl->get_RollOff(This,pRollOff);
}
static inline HRESULT IBDA_DigitalDemodulator3_put_Pilot(IBDA_DigitalDemodulator3* This,Pilot *pPilot) {
    return This->lpVtbl->put_Pilot(This,pPilot);
}
static inline HRESULT IBDA_DigitalDemodulator3_get_Pilot(IBDA_DigitalDemodulator3* This,Pilot *pPilot) {
    return This->lpVtbl->get_Pilot(This,pPilot);
}
/*** IBDA_DigitalDemodulator3 methods ***/
static inline HRESULT IBDA_DigitalDemodulator3_put_SignalTimeouts(IBDA_DigitalDemodulator3* This,BDA_SIGNAL_TIMEOUTS *pSignalTimeouts) {
    return This->lpVtbl->put_SignalTimeouts(This,pSignalTimeouts);
}
static inline HRESULT IBDA_DigitalDemodulator3_get_SignalTimeouts(IBDA_DigitalDemodulator3* This,BDA_SIGNAL_TIMEOUTS *pSignalTimeouts) {
    return This->lpVtbl->get_SignalTimeouts(This,pSignalTimeouts);
}
static inline HRESULT IBDA_DigitalDemodulator3_put_PLPNumber(IBDA_DigitalDemodulator3* This,ULONG *pPLPNumber) {
    return This->lpVtbl->put_PLPNumber(This,pPLPNumber);
}
static inline HRESULT IBDA_DigitalDemodulator3_get_PLPNumber(IBDA_DigitalDemodulator3* This,ULONG *pPLPNumber) {
    return This->lpVtbl->get_PLPNumber(This,pPLPNumber);
}
#endif
#endif

#endif


#endif  /* __IBDA_DigitalDemodulator3_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ICCSubStreamFiltering interface
 */
#ifndef __ICCSubStreamFiltering_INTERFACE_DEFINED__
#define __ICCSubStreamFiltering_INTERFACE_DEFINED__

DEFINE_GUID(IID_ICCSubStreamFiltering, 0x4b2bd7ea, 0x8347, 0x467b, 0x8d,0xbf, 0x62,0xf7,0x84,0x92,0x9c,0xc3);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("4b2bd7ea-8347-467b-8dbf-62f784929cc3")
ICCSubStreamFiltering : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE get_SubstreamTypes(
        LONG *pTypes) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_SubstreamTypes(
        LONG Types) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ICCSubStreamFiltering, 0x4b2bd7ea, 0x8347, 0x467b, 0x8d,0xbf, 0x62,0xf7,0x84,0x92,0x9c,0xc3)
#endif
#else
typedef struct ICCSubStreamFilteringVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ICCSubStreamFiltering *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ICCSubStreamFiltering *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ICCSubStreamFiltering *This);

    /*** ICCSubStreamFiltering methods ***/
    HRESULT (STDMETHODCALLTYPE *get_SubstreamTypes)(
        ICCSubStreamFiltering *This,
        LONG *pTypes);

    HRESULT (STDMETHODCALLTYPE *put_SubstreamTypes)(
        ICCSubStreamFiltering *This,
        LONG Types);

    END_INTERFACE
} ICCSubStreamFilteringVtbl;

interface ICCSubStreamFiltering {
    CONST_VTBL ICCSubStreamFilteringVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ICCSubStreamFiltering_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ICCSubStreamFiltering_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ICCSubStreamFiltering_Release(This) (This)->lpVtbl->Release(This)
/*** ICCSubStreamFiltering methods ***/
#define ICCSubStreamFiltering_get_SubstreamTypes(This,pTypes) (This)->lpVtbl->get_SubstreamTypes(This,pTypes)
#define ICCSubStreamFiltering_put_SubstreamTypes(This,Types) (This)->lpVtbl->put_SubstreamTypes(This,Types)
#else
/*** IUnknown methods ***/
static inline HRESULT ICCSubStreamFiltering_QueryInterface(ICCSubStreamFiltering* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ICCSubStreamFiltering_AddRef(ICCSubStreamFiltering* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ICCSubStreamFiltering_Release(ICCSubStreamFiltering* This) {
    return This->lpVtbl->Release(This);
}
/*** ICCSubStreamFiltering methods ***/
static inline HRESULT ICCSubStreamFiltering_get_SubstreamTypes(ICCSubStreamFiltering* This,LONG *pTypes) {
    return This->lpVtbl->get_SubstreamTypes(This,pTypes);
}
static inline HRESULT ICCSubStreamFiltering_put_SubstreamTypes(ICCSubStreamFiltering* This,LONG Types) {
    return This->lpVtbl->put_SubstreamTypes(This,Types);
}
#endif
#endif

#endif


#endif  /* __ICCSubStreamFiltering_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IBDA_IPSinkControl interface
 */
#ifndef __IBDA_IPSinkControl_INTERFACE_DEFINED__
#define __IBDA_IPSinkControl_INTERFACE_DEFINED__

DEFINE_GUID(IID_IBDA_IPSinkControl, 0x3f4dc8e2, 0x4050, 0x11d3, 0x8f,0x4b, 0x00,0xc0,0x4f,0x79,0x71,0xe2);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("3f4dc8e2-4050-11d3-8f4b-00c04f7971e2")
IBDA_IPSinkControl : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetMulticastList(
        ULONG *pulcbSize,
        BYTE **pbBuffer) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAdapterIPAddress(
        ULONG *pulcbSize,
        BYTE **pbBuffer) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IBDA_IPSinkControl, 0x3f4dc8e2, 0x4050, 0x11d3, 0x8f,0x4b, 0x00,0xc0,0x4f,0x79,0x71,0xe2)
#endif
#else
typedef struct IBDA_IPSinkControlVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IBDA_IPSinkControl *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IBDA_IPSinkControl *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IBDA_IPSinkControl *This);

    /*** IBDA_IPSinkControl methods ***/
    HRESULT (STDMETHODCALLTYPE *GetMulticastList)(
        IBDA_IPSinkControl *This,
        ULONG *pulcbSize,
        BYTE **pbBuffer);

    HRESULT (STDMETHODCALLTYPE *GetAdapterIPAddress)(
        IBDA_IPSinkControl *This,
        ULONG *pulcbSize,
        BYTE **pbBuffer);

    END_INTERFACE
} IBDA_IPSinkControlVtbl;

interface IBDA_IPSinkControl {
    CONST_VTBL IBDA_IPSinkControlVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IBDA_IPSinkControl_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IBDA_IPSinkControl_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IBDA_IPSinkControl_Release(This) (This)->lpVtbl->Release(This)
/*** IBDA_IPSinkControl methods ***/
#define IBDA_IPSinkControl_GetMulticastList(This,pulcbSize,pbBuffer) (This)->lpVtbl->GetMulticastList(This,pulcbSize,pbBuffer)
#define IBDA_IPSinkControl_GetAdapterIPAddress(This,pulcbSize,pbBuffer) (This)->lpVtbl->GetAdapterIPAddress(This,pulcbSize,pbBuffer)
#else
/*** IUnknown methods ***/
static inline HRESULT IBDA_IPSinkControl_QueryInterface(IBDA_IPSinkControl* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IBDA_IPSinkControl_AddRef(IBDA_IPSinkControl* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IBDA_IPSinkControl_Release(IBDA_IPSinkControl* This) {
    return This->lpVtbl->Release(This);
}
/*** IBDA_IPSinkControl methods ***/
static inline HRESULT IBDA_IPSinkControl_GetMulticastList(IBDA_IPSinkControl* This,ULONG *pulcbSize,BYTE **pbBuffer) {
    return This->lpVtbl->GetMulticastList(This,pulcbSize,pbBuffer);
}
static inline HRESULT IBDA_IPSinkControl_GetAdapterIPAddress(IBDA_IPSinkControl* This,ULONG *pulcbSize,BYTE **pbBuffer) {
    return This->lpVtbl->GetAdapterIPAddress(This,pulcbSize,pbBuffer);
}
#endif
#endif

#endif


#endif  /* __IBDA_IPSinkControl_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IBDA_IPSinkInfo interface
 */
#ifndef __IBDA_IPSinkInfo_INTERFACE_DEFINED__
#define __IBDA_IPSinkInfo_INTERFACE_DEFINED__

DEFINE_GUID(IID_IBDA_IPSinkInfo, 0xa750108f, 0x492e, 0x4d51, 0x95,0xf7, 0x64,0x9b,0x23,0xff,0x7a,0xd7);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a750108f-492e-4d51-95f7-649b23ff7ad7")
IBDA_IPSinkInfo : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE get_MulticastList(
        ULONG *pulcbAddresses,
        BYTE **ppbAddressList) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_AdapterIPAddress(
        BSTR *pbstrBuffer) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_AdapterDescription(
        BSTR *pbstrBuffer) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IBDA_IPSinkInfo, 0xa750108f, 0x492e, 0x4d51, 0x95,0xf7, 0x64,0x9b,0x23,0xff,0x7a,0xd7)
#endif
#else
typedef struct IBDA_IPSinkInfoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IBDA_IPSinkInfo *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IBDA_IPSinkInfo *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IBDA_IPSinkInfo *This);

    /*** IBDA_IPSinkInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *get_MulticastList)(
        IBDA_IPSinkInfo *This,
        ULONG *pulcbAddresses,
        BYTE **ppbAddressList);

    HRESULT (STDMETHODCALLTYPE *get_AdapterIPAddress)(
        IBDA_IPSinkInfo *This,
        BSTR *pbstrBuffer);

    HRESULT (STDMETHODCALLTYPE *get_AdapterDescription)(
        IBDA_IPSinkInfo *This,
        BSTR *pbstrBuffer);

    END_INTERFACE
} IBDA_IPSinkInfoVtbl;

interface IBDA_IPSinkInfo {
    CONST_VTBL IBDA_IPSinkInfoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IBDA_IPSinkInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IBDA_IPSinkInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IBDA_IPSinkInfo_Release(This) (This)->lpVtbl->Release(This)
/*** IBDA_IPSinkInfo methods ***/
#define IBDA_IPSinkInfo_get_MulticastList(This,pulcbAddresses,ppbAddressList) (This)->lpVtbl->get_MulticastList(This,pulcbAddresses,ppbAddressList)
#define IBDA_IPSinkInfo_get_AdapterIPAddress(This,pbstrBuffer) (This)->lpVtbl->get_AdapterIPAddress(This,pbstrBuffer)
#define IBDA_IPSinkInfo_get_AdapterDescription(This,pbstrBuffer) (This)->lpVtbl->get_AdapterDescription(This,pbstrBuffer)
#else
/*** IUnknown methods ***/
static inline HRESULT IBDA_IPSinkInfo_QueryInterface(IBDA_IPSinkInfo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IBDA_IPSinkInfo_AddRef(IBDA_IPSinkInfo* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IBDA_IPSinkInfo_Release(IBDA_IPSinkInfo* This) {
    return This->lpVtbl->Release(This);
}
/*** IBDA_IPSinkInfo methods ***/
static inline HRESULT IBDA_IPSinkInfo_get_MulticastList(IBDA_IPSinkInfo* This,ULONG *pulcbAddresses,BYTE **ppbAddressList) {
    return This->lpVtbl->get_MulticastList(This,pulcbAddresses,ppbAddressList);
}
static inline HRESULT IBDA_IPSinkInfo_get_AdapterIPAddress(IBDA_IPSinkInfo* This,BSTR *pbstrBuffer) {
    return This->lpVtbl->get_AdapterIPAddress(This,pbstrBuffer);
}
static inline HRESULT IBDA_IPSinkInfo_get_AdapterDescription(IBDA_IPSinkInfo* This,BSTR *pbstrBuffer) {
    return This->lpVtbl->get_AdapterDescription(This,pbstrBuffer);
}
#endif
#endif

#endif


#endif  /* __IBDA_IPSinkInfo_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IEnumPIDMap interface
 */
#ifndef __IEnumPIDMap_INTERFACE_DEFINED__
#define __IEnumPIDMap_INTERFACE_DEFINED__

DEFINE_GUID(IID_IEnumPIDMap, 0xafb6c2a2, 0x2c41, 0x11d3, 0x8a,0x60, 0x00,0x00,0xf8,0x1e,0x0e,0x4a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("afb6c2a2-2c41-11d3-8a60-0000f81e0e4a")
IEnumPIDMap : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Next(
        ULONG cRequest,
        PID_MAP *pPIDMap,
        ULONG *pcReceived) = 0;

    virtual HRESULT STDMETHODCALLTYPE Skip(
        ULONG cRecords) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reset(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IEnumPIDMap **ppIEnumPIDMap) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IEnumPIDMap, 0xafb6c2a2, 0x2c41, 0x11d3, 0x8a,0x60, 0x00,0x00,0xf8,0x1e,0x0e,0x4a)
#endif
#else
typedef struct IEnumPIDMapVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IEnumPIDMap *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IEnumPIDMap *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IEnumPIDMap *This);

    /*** IEnumPIDMap methods ***/
    HRESULT (STDMETHODCALLTYPE *Next)(
        IEnumPIDMap *This,
        ULONG cRequest,
        PID_MAP *pPIDMap,
        ULONG *pcReceived);

    HRESULT (STDMETHODCALLTYPE *Skip)(
        IEnumPIDMap *This,
        ULONG cRecords);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        IEnumPIDMap *This);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IEnumPIDMap *This,
        IEnumPIDMap **ppIEnumPIDMap);

    END_INTERFACE
} IEnumPIDMapVtbl;

interface IEnumPIDMap {
    CONST_VTBL IEnumPIDMapVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IEnumPIDMap_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IEnumPIDMap_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IEnumPIDMap_Release(This) (This)->lpVtbl->Release(This)
/*** IEnumPIDMap methods ***/
#define IEnumPIDMap_Next(This,cRequest,pPIDMap,pcReceived) (This)->lpVtbl->Next(This,cRequest,pPIDMap,pcReceived)
#define IEnumPIDMap_Skip(This,cRecords) (This)->lpVtbl->Skip(This,cRecords)
#define IEnumPIDMap_Reset(This) (This)->lpVtbl->Reset(This)
#define IEnumPIDMap_Clone(This,ppIEnumPIDMap) (This)->lpVtbl->Clone(This,ppIEnumPIDMap)
#else
/*** IUnknown methods ***/
static inline HRESULT IEnumPIDMap_QueryInterface(IEnumPIDMap* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IEnumPIDMap_AddRef(IEnumPIDMap* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IEnumPIDMap_Release(IEnumPIDMap* This) {
    return This->lpVtbl->Release(This);
}
/*** IEnumPIDMap methods ***/
static inline HRESULT IEnumPIDMap_Next(IEnumPIDMap* This,ULONG cRequest,PID_MAP *pPIDMap,ULONG *pcReceived) {
    return This->lpVtbl->Next(This,cRequest,pPIDMap,pcReceived);
}
static inline HRESULT IEnumPIDMap_Skip(IEnumPIDMap* This,ULONG cRecords) {
    return This->lpVtbl->Skip(This,cRecords);
}
static inline HRESULT IEnumPIDMap_Reset(IEnumPIDMap* This) {
    return This->lpVtbl->Reset(This);
}
static inline HRESULT IEnumPIDMap_Clone(IEnumPIDMap* This,IEnumPIDMap **ppIEnumPIDMap) {
    return This->lpVtbl->Clone(This,ppIEnumPIDMap);
}
#endif
#endif

#endif


#endif  /* __IEnumPIDMap_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMPEG2PIDMap interface
 */
#ifndef __IMPEG2PIDMap_INTERFACE_DEFINED__
#define __IMPEG2PIDMap_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMPEG2PIDMap, 0xafb6c2a1, 0x2c41, 0x11d3, 0x8a,0x60, 0x00,0x00,0xf8,0x1e,0x0e,0x4a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("afb6c2a1-2c41-11d3-8a60-0000f81e0e4a")
IMPEG2PIDMap : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE MapPID(
        ULONG culPID,
        ULONG *pulPID,
        MEDIA_SAMPLE_CONTENT MediaSampleContent) = 0;

    virtual HRESULT STDMETHODCALLTYPE UnmapPID(
        ULONG culPID,
        ULONG *pulPID) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumPIDMap(
        IEnumPIDMap **pIEnumPIDMap) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMPEG2PIDMap, 0xafb6c2a1, 0x2c41, 0x11d3, 0x8a,0x60, 0x00,0x00,0xf8,0x1e,0x0e,0x4a)
#endif
#else
typedef struct IMPEG2PIDMapVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMPEG2PIDMap *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMPEG2PIDMap *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMPEG2PIDMap *This);

    /*** IMPEG2PIDMap methods ***/
    HRESULT (STDMETHODCALLTYPE *MapPID)(
        IMPEG2PIDMap *This,
        ULONG culPID,
        ULONG *pulPID,
        MEDIA_SAMPLE_CONTENT MediaSampleContent);

    HRESULT (STDMETHODCALLTYPE *UnmapPID)(
        IMPEG2PIDMap *This,
        ULONG culPID,
        ULONG *pulPID);

    HRESULT (STDMETHODCALLTYPE *EnumPIDMap)(
        IMPEG2PIDMap *This,
        IEnumPIDMap **pIEnumPIDMap);

    END_INTERFACE
} IMPEG2PIDMapVtbl;

interface IMPEG2PIDMap {
    CONST_VTBL IMPEG2PIDMapVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMPEG2PIDMap_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMPEG2PIDMap_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMPEG2PIDMap_Release(This) (This)->lpVtbl->Release(This)
/*** IMPEG2PIDMap methods ***/
#define IMPEG2PIDMap_MapPID(This,culPID,pulPID,MediaSampleContent) (This)->lpVtbl->MapPID(This,culPID,pulPID,MediaSampleContent)
#define IMPEG2PIDMap_UnmapPID(This,culPID,pulPID) (This)->lpVtbl->UnmapPID(This,culPID,pulPID)
#define IMPEG2PIDMap_EnumPIDMap(This,pIEnumPIDMap) (This)->lpVtbl->EnumPIDMap(This,pIEnumPIDMap)
#else
/*** IUnknown methods ***/
static inline HRESULT IMPEG2PIDMap_QueryInterface(IMPEG2PIDMap* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMPEG2PIDMap_AddRef(IMPEG2PIDMap* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMPEG2PIDMap_Release(IMPEG2PIDMap* This) {
    return This->lpVtbl->Release(This);
}
/*** IMPEG2PIDMap methods ***/
static inline HRESULT IMPEG2PIDMap_MapPID(IMPEG2PIDMap* This,ULONG culPID,ULONG *pulPID,MEDIA_SAMPLE_CONTENT MediaSampleContent) {
    return This->lpVtbl->MapPID(This,culPID,pulPID,MediaSampleContent);
}
static inline HRESULT IMPEG2PIDMap_UnmapPID(IMPEG2PIDMap* This,ULONG culPID,ULONG *pulPID) {
    return This->lpVtbl->UnmapPID(This,culPID,pulPID);
}
static inline HRESULT IMPEG2PIDMap_EnumPIDMap(IMPEG2PIDMap* This,IEnumPIDMap **pIEnumPIDMap) {
    return This->lpVtbl->EnumPIDMap(This,pIEnumPIDMap);
}
#endif
#endif

#endif


#endif  /* __IMPEG2PIDMap_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IFrequencyMap interface
 */
#ifndef __IFrequencyMap_INTERFACE_DEFINED__
#define __IFrequencyMap_INTERFACE_DEFINED__

DEFINE_GUID(IID_IFrequencyMap, 0x06fb45c1, 0x693c, 0x4ea7, 0xb7,0x9f, 0x7a,0x6a,0x54,0xd8,0xde,0xf2);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("06fb45c1-693c-4ea7-b79f-7a6a54d8def2")
IFrequencyMap : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE get_FrequencyMapping(
        ULONG *ulCount,
        ULONG **ppulList) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_FrequencyMapping(
        ULONG ulCount,
        ULONG pList[]) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_CountryCode(
        ULONG *pulCountryCode) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_CountryCode(
        ULONG ulCountryCode) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_DefaultFrequencyMapping(
        ULONG ulCountryCode,
        ULONG *pulCount,
        ULONG **ppulList) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_CountryCodeList(
        ULONG *pulCount,
        ULONG **ppulList) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IFrequencyMap, 0x06fb45c1, 0x693c, 0x4ea7, 0xb7,0x9f, 0x7a,0x6a,0x54,0xd8,0xde,0xf2)
#endif
#else
typedef struct IFrequencyMapVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IFrequencyMap *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IFrequencyMap *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IFrequencyMap *This);

    /*** IFrequencyMap methods ***/
    HRESULT (STDMETHODCALLTYPE *get_FrequencyMapping)(
        IFrequencyMap *This,
        ULONG *ulCount,
        ULONG **ppulList);

    HRESULT (STDMETHODCALLTYPE *put_FrequencyMapping)(
        IFrequencyMap *This,
        ULONG ulCount,
        ULONG pList[]);

    HRESULT (STDMETHODCALLTYPE *get_CountryCode)(
        IFrequencyMap *This,
        ULONG *pulCountryCode);

    HRESULT (STDMETHODCALLTYPE *put_CountryCode)(
        IFrequencyMap *This,
        ULONG ulCountryCode);

    HRESULT (STDMETHODCALLTYPE *get_DefaultFrequencyMapping)(
        IFrequencyMap *This,
        ULONG ulCountryCode,
        ULONG *pulCount,
        ULONG **ppulList);

    HRESULT (STDMETHODCALLTYPE *get_CountryCodeList)(
        IFrequencyMap *This,
        ULONG *pulCount,
        ULONG **ppulList);

    END_INTERFACE
} IFrequencyMapVtbl;

interface IFrequencyMap {
    CONST_VTBL IFrequencyMapVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IFrequencyMap_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IFrequencyMap_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IFrequencyMap_Release(This) (This)->lpVtbl->Release(This)
/*** IFrequencyMap methods ***/
#define IFrequencyMap_get_FrequencyMapping(This,ulCount,ppulList) (This)->lpVtbl->get_FrequencyMapping(This,ulCount,ppulList)
#define IFrequencyMap_put_FrequencyMapping(This,ulCount,pList) (This)->lpVtbl->put_FrequencyMapping(This,ulCount,pList)
#define IFrequencyMap_get_CountryCode(This,pulCountryCode) (This)->lpVtbl->get_CountryCode(This,pulCountryCode)
#define IFrequencyMap_put_CountryCode(This,ulCountryCode) (This)->lpVtbl->put_CountryCode(This,ulCountryCode)
#define IFrequencyMap_get_DefaultFrequencyMapping(This,ulCountryCode,pulCount,ppulList) (This)->lpVtbl->get_DefaultFrequencyMapping(This,ulCountryCode,pulCount,ppulList)
#define IFrequencyMap_get_CountryCodeList(This,pulCount,ppulList) (This)->lpVtbl->get_CountryCodeList(This,pulCount,ppulList)
#else
/*** IUnknown methods ***/
static inline HRESULT IFrequencyMap_QueryInterface(IFrequencyMap* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IFrequencyMap_AddRef(IFrequencyMap* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IFrequencyMap_Release(IFrequencyMap* This) {
    return This->lpVtbl->Release(This);
}
/*** IFrequencyMap methods ***/
static inline HRESULT IFrequencyMap_get_FrequencyMapping(IFrequencyMap* This,ULONG *ulCount,ULONG **ppulList) {
    return This->lpVtbl->get_FrequencyMapping(This,ulCount,ppulList);
}
static inline HRESULT IFrequencyMap_put_FrequencyMapping(IFrequencyMap* This,ULONG ulCount,ULONG pList[]) {
    return This->lpVtbl->put_FrequencyMapping(This,ulCount,pList);
}
static inline HRESULT IFrequencyMap_get_CountryCode(IFrequencyMap* This,ULONG *pulCountryCode) {
    return This->lpVtbl->get_CountryCode(This,pulCountryCode);
}
static inline HRESULT IFrequencyMap_put_CountryCode(IFrequencyMap* This,ULONG ulCountryCode) {
    return This->lpVtbl->put_CountryCode(This,ulCountryCode);
}
static inline HRESULT IFrequencyMap_get_DefaultFrequencyMapping(IFrequencyMap* This,ULONG ulCountryCode,ULONG *pulCount,ULONG **ppulList) {
    return This->lpVtbl->get_DefaultFrequencyMapping(This,ulCountryCode,pulCount,ppulList);
}
static inline HRESULT IFrequencyMap_get_CountryCodeList(IFrequencyMap* This,ULONG *pulCount,ULONG **ppulList) {
    return This->lpVtbl->get_CountryCodeList(This,pulCount,ppulList);
}
#endif
#endif

#endif


#endif  /* __IFrequencyMap_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IBDA_EasMessage interface
 */
#ifndef __IBDA_EasMessage_INTERFACE_DEFINED__
#define __IBDA_EasMessage_INTERFACE_DEFINED__

DEFINE_GUID(IID_IBDA_EasMessage, 0xd806973d, 0x3ebe, 0x46de, 0x8f,0xbb, 0x63,0x58,0xfe,0x78,0x42,0x08);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d806973d-3ebe-46de-8fbb-6358fe784208")
IBDA_EasMessage : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE get_EasMessage(
        ULONG ulEventID,
        IUnknown **ppEASObject) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IBDA_EasMessage, 0xd806973d, 0x3ebe, 0x46de, 0x8f,0xbb, 0x63,0x58,0xfe,0x78,0x42,0x08)
#endif
#else
typedef struct IBDA_EasMessageVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IBDA_EasMessage *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IBDA_EasMessage *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IBDA_EasMessage *This);

    /*** IBDA_EasMessage methods ***/
    HRESULT (STDMETHODCALLTYPE *get_EasMessage)(
        IBDA_EasMessage *This,
        ULONG ulEventID,
        IUnknown **ppEASObject);

    END_INTERFACE
} IBDA_EasMessageVtbl;

interface IBDA_EasMessage {
    CONST_VTBL IBDA_EasMessageVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IBDA_EasMessage_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IBDA_EasMessage_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IBDA_EasMessage_Release(This) (This)->lpVtbl->Release(This)
/*** IBDA_EasMessage methods ***/
#define IBDA_EasMessage_get_EasMessage(This,ulEventID,ppEASObject) (This)->lpVtbl->get_EasMessage(This,ulEventID,ppEASObject)
#else
/*** IUnknown methods ***/
static inline HRESULT IBDA_EasMessage_QueryInterface(IBDA_EasMessage* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IBDA_EasMessage_AddRef(IBDA_EasMessage* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IBDA_EasMessage_Release(IBDA_EasMessage* This) {
    return This->lpVtbl->Release(This);
}
/*** IBDA_EasMessage methods ***/
static inline HRESULT IBDA_EasMessage_get_EasMessage(IBDA_EasMessage* This,ULONG ulEventID,IUnknown **ppEASObject) {
    return This->lpVtbl->get_EasMessage(This,ulEventID,ppEASObject);
}
#endif
#endif

#endif


#endif  /* __IBDA_EasMessage_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IBDA_TransportStreamInfo interface
 */
#ifndef __IBDA_TransportStreamInfo_INTERFACE_DEFINED__
#define __IBDA_TransportStreamInfo_INTERFACE_DEFINED__

DEFINE_GUID(IID_IBDA_TransportStreamInfo, 0x8e882535, 0x5f86, 0x47ab, 0x86,0xcf, 0xc2,0x81,0xa7,0x2a,0x05,0x49);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("8e882535-5f86-47ab-86cf-c281a72a0549")
IBDA_TransportStreamInfo : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE get_PatTableTickCount(
        ULONG *pPatTickCount) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IBDA_TransportStreamInfo, 0x8e882535, 0x5f86, 0x47ab, 0x86,0xcf, 0xc2,0x81,0xa7,0x2a,0x05,0x49)
#endif
#else
typedef struct IBDA_TransportStreamInfoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IBDA_TransportStreamInfo *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IBDA_TransportStreamInfo *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IBDA_TransportStreamInfo *This);

    /*** IBDA_TransportStreamInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *get_PatTableTickCount)(
        IBDA_TransportStreamInfo *This,
        ULONG *pPatTickCount);

    END_INTERFACE
} IBDA_TransportStreamInfoVtbl;

interface IBDA_TransportStreamInfo {
    CONST_VTBL IBDA_TransportStreamInfoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IBDA_TransportStreamInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IBDA_TransportStreamInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IBDA_TransportStreamInfo_Release(This) (This)->lpVtbl->Release(This)
/*** IBDA_TransportStreamInfo methods ***/
#define IBDA_TransportStreamInfo_get_PatTableTickCount(This,pPatTickCount) (This)->lpVtbl->get_PatTableTickCount(This,pPatTickCount)
#else
/*** IUnknown methods ***/
static inline HRESULT IBDA_TransportStreamInfo_QueryInterface(IBDA_TransportStreamInfo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IBDA_TransportStreamInfo_AddRef(IBDA_TransportStreamInfo* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IBDA_TransportStreamInfo_Release(IBDA_TransportStreamInfo* This) {
    return This->lpVtbl->Release(This);
}
/*** IBDA_TransportStreamInfo methods ***/
static inline HRESULT IBDA_TransportStreamInfo_get_PatTableTickCount(IBDA_TransportStreamInfo* This,ULONG *pPatTickCount) {
    return This->lpVtbl->get_PatTableTickCount(This,pPatTickCount);
}
#endif
#endif

#endif


#endif  /* __IBDA_TransportStreamInfo_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IBDA_ConditionalAccess interface
 */
#ifndef __IBDA_ConditionalAccess_INTERFACE_DEFINED__
#define __IBDA_ConditionalAccess_INTERFACE_DEFINED__

DEFINE_GUID(IID_IBDA_ConditionalAccess, 0xcd51f1e0, 0x7be9, 0x4123, 0x84,0x82, 0xa2,0xa7,0x96,0xc0,0xa6,0xb0);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("cd51f1e0-7be9-4123-8482-a2a796c0a6b0")
IBDA_ConditionalAccess : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE get_SmartCardStatus(
        SmartCardStatusType *pCardStatus,
        SmartCardAssociationType *pCardAssociation,
        BSTR *pbstrCardError,
        VARIANT_BOOL *pfOOBLocked) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_SmartCardInfo(
        BSTR *pbstrCardName,
        BSTR *pbstrCardManufacturer,
        VARIANT_BOOL *pfDaylightSavings,
        BYTE *pbyRatingRegion,
        LONG *plTimeZoneOffsetMinutes,
        BSTR *pbstrLanguage,
        EALocationCodeType *pEALocationCode) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_SmartCardApplications(
        ULONG *pulcApplications,
        ULONG ulcApplicationsMax,
        SmartCardApplication rgApplications[]) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Entitlement(
        USHORT usVirtualChannel,
        EntitlementType *pEntitlement) = 0;

    virtual HRESULT STDMETHODCALLTYPE TuneByChannel(
        USHORT usVirtualChannel) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetProgram(
        USHORT usProgramNumber) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddProgram(
        USHORT usProgramNumber) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveProgram(
        USHORT usProgramNumber) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetModuleUI(
        BYTE byDialogNumber,
        BSTR *pbstrURL) = 0;

    virtual HRESULT STDMETHODCALLTYPE InformUIClosed(
        BYTE byDialogNumber,
        UICloseReasonType CloseReason) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IBDA_ConditionalAccess, 0xcd51f1e0, 0x7be9, 0x4123, 0x84,0x82, 0xa2,0xa7,0x96,0xc0,0xa6,0xb0)
#endif
#else
typedef struct IBDA_ConditionalAccessVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IBDA_ConditionalAccess *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IBDA_ConditionalAccess *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IBDA_ConditionalAccess *This);

    /*** IBDA_ConditionalAccess methods ***/
    HRESULT (STDMETHODCALLTYPE *get_SmartCardStatus)(
        IBDA_ConditionalAccess *This,
        SmartCardStatusType *pCardStatus,
        SmartCardAssociationType *pCardAssociation,
        BSTR *pbstrCardError,
        VARIANT_BOOL *pfOOBLocked);

    HRESULT (STDMETHODCALLTYPE *get_SmartCardInfo)(
        IBDA_ConditionalAccess *This,
        BSTR *pbstrCardName,
        BSTR *pbstrCardManufacturer,
        VARIANT_BOOL *pfDaylightSavings,
        BYTE *pbyRatingRegion,
        LONG *plTimeZoneOffsetMinutes,
        BSTR *pbstrLanguage,
        EALocationCodeType *pEALocationCode);

    HRESULT (STDMETHODCALLTYPE *get_SmartCardApplications)(
        IBDA_ConditionalAccess *This,
        ULONG *pulcApplications,
        ULONG ulcApplicationsMax,
        SmartCardApplication rgApplications[]);

    HRESULT (STDMETHODCALLTYPE *get_Entitlement)(
        IBDA_ConditionalAccess *This,
        USHORT usVirtualChannel,
        EntitlementType *pEntitlement);

    HRESULT (STDMETHODCALLTYPE *TuneByChannel)(
        IBDA_ConditionalAccess *This,
        USHORT usVirtualChannel);

    HRESULT (STDMETHODCALLTYPE *SetProgram)(
        IBDA_ConditionalAccess *This,
        USHORT usProgramNumber);

    HRESULT (STDMETHODCALLTYPE *AddProgram)(
        IBDA_ConditionalAccess *This,
        USHORT usProgramNumber);

    HRESULT (STDMETHODCALLTYPE *RemoveProgram)(
        IBDA_ConditionalAccess *This,
        USHORT usProgramNumber);

    HRESULT (STDMETHODCALLTYPE *GetModuleUI)(
        IBDA_ConditionalAccess *This,
        BYTE byDialogNumber,
        BSTR *pbstrURL);

    HRESULT (STDMETHODCALLTYPE *InformUIClosed)(
        IBDA_ConditionalAccess *This,
        BYTE byDialogNumber,
        UICloseReasonType CloseReason);

    END_INTERFACE
} IBDA_ConditionalAccessVtbl;

interface IBDA_ConditionalAccess {
    CONST_VTBL IBDA_ConditionalAccessVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IBDA_ConditionalAccess_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IBDA_ConditionalAccess_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IBDA_ConditionalAccess_Release(This) (This)->lpVtbl->Release(This)
/*** IBDA_ConditionalAccess methods ***/
#define IBDA_ConditionalAccess_get_SmartCardStatus(This,pCardStatus,pCardAssociation,pbstrCardError,pfOOBLocked) (This)->lpVtbl->get_SmartCardStatus(This,pCardStatus,pCardAssociation,pbstrCardError,pfOOBLocked)
#define IBDA_ConditionalAccess_get_SmartCardInfo(This,pbstrCardName,pbstrCardManufacturer,pfDaylightSavings,pbyRatingRegion,plTimeZoneOffsetMinutes,pbstrLanguage,pEALocationCode) (This)->lpVtbl->get_SmartCardInfo(This,pbstrCardName,pbstrCardManufacturer,pfDaylightSavings,pbyRatingRegion,plTimeZoneOffsetMinutes,pbstrLanguage,pEALocationCode)
#define IBDA_ConditionalAccess_get_SmartCardApplications(This,pulcApplications,ulcApplicationsMax,rgApplications) (This)->lpVtbl->get_SmartCardApplications(This,pulcApplications,ulcApplicationsMax,rgApplications)
#define IBDA_ConditionalAccess_get_Entitlement(This,usVirtualChannel,pEntitlement) (This)->lpVtbl->get_Entitlement(This,usVirtualChannel,pEntitlement)
#define IBDA_ConditionalAccess_TuneByChannel(This,usVirtualChannel) (This)->lpVtbl->TuneByChannel(This,usVirtualChannel)
#define IBDA_ConditionalAccess_SetProgram(This,usProgramNumber) (This)->lpVtbl->SetProgram(This,usProgramNumber)
#define IBDA_ConditionalAccess_AddProgram(This,usProgramNumber) (This)->lpVtbl->AddProgram(This,usProgramNumber)
#define IBDA_ConditionalAccess_RemoveProgram(This,usProgramNumber) (This)->lpVtbl->RemoveProgram(This,usProgramNumber)
#define IBDA_ConditionalAccess_GetModuleUI(This,byDialogNumber,pbstrURL) (This)->lpVtbl->GetModuleUI(This,byDialogNumber,pbstrURL)
#define IBDA_ConditionalAccess_InformUIClosed(This,byDialogNumber,CloseReason) (This)->lpVtbl->InformUIClosed(This,byDialogNumber,CloseReason)
#else
/*** IUnknown methods ***/
static inline HRESULT IBDA_ConditionalAccess_QueryInterface(IBDA_ConditionalAccess* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IBDA_ConditionalAccess_AddRef(IBDA_ConditionalAccess* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IBDA_ConditionalAccess_Release(IBDA_ConditionalAccess* This) {
    return This->lpVtbl->Release(This);
}
/*** IBDA_ConditionalAccess methods ***/
static inline HRESULT IBDA_ConditionalAccess_get_SmartCardStatus(IBDA_ConditionalAccess* This,SmartCardStatusType *pCardStatus,SmartCardAssociationType *pCardAssociation,BSTR *pbstrCardError,VARIANT_BOOL *pfOOBLocked) {
    return This->lpVtbl->get_SmartCardStatus(This,pCardStatus,pCardAssociation,pbstrCardError,pfOOBLocked);
}
static inline HRESULT IBDA_ConditionalAccess_get_SmartCardInfo(IBDA_ConditionalAccess* This,BSTR *pbstrCardName,BSTR *pbstrCardManufacturer,VARIANT_BOOL *pfDaylightSavings,BYTE *pbyRatingRegion,LONG *plTimeZoneOffsetMinutes,BSTR *pbstrLanguage,EALocationCodeType *pEALocationCode) {
    return This->lpVtbl->get_SmartCardInfo(This,pbstrCardName,pbstrCardManufacturer,pfDaylightSavings,pbyRatingRegion,plTimeZoneOffsetMinutes,pbstrLanguage,pEALocationCode);
}
static inline HRESULT IBDA_ConditionalAccess_get_SmartCardApplications(IBDA_ConditionalAccess* This,ULONG *pulcApplications,ULONG ulcApplicationsMax,SmartCardApplication rgApplications[]) {
    return This->lpVtbl->get_SmartCardApplications(This,pulcApplications,ulcApplicationsMax,rgApplications);
}
static inline HRESULT IBDA_ConditionalAccess_get_Entitlement(IBDA_ConditionalAccess* This,USHORT usVirtualChannel,EntitlementType *pEntitlement) {
    return This->lpVtbl->get_Entitlement(This,usVirtualChannel,pEntitlement);
}
static inline HRESULT IBDA_ConditionalAccess_TuneByChannel(IBDA_ConditionalAccess* This,USHORT usVirtualChannel) {
    return This->lpVtbl->TuneByChannel(This,usVirtualChannel);
}
static inline HRESULT IBDA_ConditionalAccess_SetProgram(IBDA_ConditionalAccess* This,USHORT usProgramNumber) {
    return This->lpVtbl->SetProgram(This,usProgramNumber);
}
static inline HRESULT IBDA_ConditionalAccess_AddProgram(IBDA_ConditionalAccess* This,USHORT usProgramNumber) {
    return This->lpVtbl->AddProgram(This,usProgramNumber);
}
static inline HRESULT IBDA_ConditionalAccess_RemoveProgram(IBDA_ConditionalAccess* This,USHORT usProgramNumber) {
    return This->lpVtbl->RemoveProgram(This,usProgramNumber);
}
static inline HRESULT IBDA_ConditionalAccess_GetModuleUI(IBDA_ConditionalAccess* This,BYTE byDialogNumber,BSTR *pbstrURL) {
    return This->lpVtbl->GetModuleUI(This,byDialogNumber,pbstrURL);
}
static inline HRESULT IBDA_ConditionalAccess_InformUIClosed(IBDA_ConditionalAccess* This,BYTE byDialogNumber,UICloseReasonType CloseReason) {
    return This->lpVtbl->InformUIClosed(This,byDialogNumber,CloseReason);
}
#endif
#endif

#endif


#endif  /* __IBDA_ConditionalAccess_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IBDA_DiagnosticProperties interface
 */
#ifndef __IBDA_DiagnosticProperties_INTERFACE_DEFINED__
#define __IBDA_DiagnosticProperties_INTERFACE_DEFINED__

DEFINE_GUID(IID_IBDA_DiagnosticProperties, 0x20e80cb5, 0xc543, 0x4c1b, 0x8e,0xb3, 0x49,0xe7,0x19,0xee,0xe7,0xd4);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("20e80cb5-c543-4c1b-8eb3-49e719eee7d4")
IBDA_DiagnosticProperties : public IPropertyBag
{
};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IBDA_DiagnosticProperties, 0x20e80cb5, 0xc543, 0x4c1b, 0x8e,0xb3, 0x49,0xe7,0x19,0xee,0xe7,0xd4)
#endif
#else
typedef struct IBDA_DiagnosticPropertiesVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IBDA_DiagnosticProperties *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IBDA_DiagnosticProperties *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IBDA_DiagnosticProperties *This);

    /*** IPropertyBag methods ***/
    HRESULT (STDMETHODCALLTYPE *Read)(
        IBDA_DiagnosticProperties *This,
        LPCOLESTR pszPropName,
        VARIANT *pVar,
        IErrorLog *pErrorLog);

    HRESULT (STDMETHODCALLTYPE *Write)(
        IBDA_DiagnosticProperties *This,
        LPCOLESTR pszPropName,
        VARIANT *pVar);

    END_INTERFACE
} IBDA_DiagnosticPropertiesVtbl;

interface IBDA_DiagnosticProperties {
    CONST_VTBL IBDA_DiagnosticPropertiesVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IBDA_DiagnosticProperties_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IBDA_DiagnosticProperties_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IBDA_DiagnosticProperties_Release(This) (This)->lpVtbl->Release(This)
/*** IPropertyBag methods ***/
#define IBDA_DiagnosticProperties_Read(This,pszPropName,pVar,pErrorLog) (This)->lpVtbl->Read(This,pszPropName,pVar,pErrorLog)
#define IBDA_DiagnosticProperties_Write(This,pszPropName,pVar) (This)->lpVtbl->Write(This,pszPropName,pVar)
#else
/*** IUnknown methods ***/
static inline HRESULT IBDA_DiagnosticProperties_QueryInterface(IBDA_DiagnosticProperties* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IBDA_DiagnosticProperties_AddRef(IBDA_DiagnosticProperties* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IBDA_DiagnosticProperties_Release(IBDA_DiagnosticProperties* This) {
    return This->lpVtbl->Release(This);
}
/*** IPropertyBag methods ***/
static inline HRESULT IBDA_DiagnosticProperties_Read(IBDA_DiagnosticProperties* This,LPCOLESTR pszPropName,VARIANT *pVar,IErrorLog *pErrorLog) {
    return This->lpVtbl->Read(This,pszPropName,pVar,pErrorLog);
}
static inline HRESULT IBDA_DiagnosticProperties_Write(IBDA_DiagnosticProperties* This,LPCOLESTR pszPropName,VARIANT *pVar) {
    return This->lpVtbl->Write(This,pszPropName,pVar);
}
#endif
#endif

#endif


#endif  /* __IBDA_DiagnosticProperties_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IBDA_DRM interface
 */
#ifndef __IBDA_DRM_INTERFACE_DEFINED__
#define __IBDA_DRM_INTERFACE_DEFINED__

DEFINE_GUID(IID_IBDA_DRM, 0xf98d88b0, 0x1992, 0x4cd6, 0xa6,0xd9, 0xb9,0xaf,0xab,0x99,0x33,0x0d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("f98d88b0-1992-4cd6-a6d9-b9afab99330d")
IBDA_DRM : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetDRMPairingStatus(
        DWORD *pdwStatus,
        HRESULT *phError) = 0;

    virtual HRESULT STDMETHODCALLTYPE PerformDRMPairing(
        WINBOOL fSync) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IBDA_DRM, 0xf98d88b0, 0x1992, 0x4cd6, 0xa6,0xd9, 0xb9,0xaf,0xab,0x99,0x33,0x0d)
#endif
#else
typedef struct IBDA_DRMVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IBDA_DRM *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IBDA_DRM *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IBDA_DRM *This);

    /*** IBDA_DRM methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDRMPairingStatus)(
        IBDA_DRM *This,
        DWORD *pdwStatus,
        HRESULT *phError);

    HRESULT (STDMETHODCALLTYPE *PerformDRMPairing)(
        IBDA_DRM *This,
        WINBOOL fSync);

    END_INTERFACE
} IBDA_DRMVtbl;

interface IBDA_DRM {
    CONST_VTBL IBDA_DRMVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IBDA_DRM_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IBDA_DRM_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IBDA_DRM_Release(This) (This)->lpVtbl->Release(This)
/*** IBDA_DRM methods ***/
#define IBDA_DRM_GetDRMPairingStatus(This,pdwStatus,phError) (This)->lpVtbl->GetDRMPairingStatus(This,pdwStatus,phError)
#define IBDA_DRM_PerformDRMPairing(This,fSync) (This)->lpVtbl->PerformDRMPairing(This,fSync)
#else
/*** IUnknown methods ***/
static inline HRESULT IBDA_DRM_QueryInterface(IBDA_DRM* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IBDA_DRM_AddRef(IBDA_DRM* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IBDA_DRM_Release(IBDA_DRM* This) {
    return This->lpVtbl->Release(This);
}
/*** IBDA_DRM methods ***/
static inline HRESULT IBDA_DRM_GetDRMPairingStatus(IBDA_DRM* This,DWORD *pdwStatus,HRESULT *phError) {
    return This->lpVtbl->GetDRMPairingStatus(This,pdwStatus,phError);
}
static inline HRESULT IBDA_DRM_PerformDRMPairing(IBDA_DRM* This,WINBOOL fSync) {
    return This->lpVtbl->PerformDRMPairing(This,fSync);
}
#endif
#endif

#endif


#endif  /* __IBDA_DRM_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IBDA_NameValueService interface
 */
#ifndef __IBDA_NameValueService_INTERFACE_DEFINED__
#define __IBDA_NameValueService_INTERFACE_DEFINED__

DEFINE_GUID(IID_IBDA_NameValueService, 0x7f0b3150, 0x7b81, 0x4ad4, 0x98,0xe3, 0x7e,0x90,0x97,0x09,0x43,0x01);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("7f0b3150-7b81-4ad4-98e3-7e9097094301")
IBDA_NameValueService : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetValueNameByIndex(
        ULONG ulIndex,
        BSTR *pbstrName) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetValue(
        BSTR bstrName,
        BSTR bstrLanguage,
        BSTR *pbstrValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetValue(
        ULONG ulDialogRequest,
        BSTR bstrLanguage,
        BSTR bstrName,
        BSTR bstrValue,
        ULONG ulReserved) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IBDA_NameValueService, 0x7f0b3150, 0x7b81, 0x4ad4, 0x98,0xe3, 0x7e,0x90,0x97,0x09,0x43,0x01)
#endif
#else
typedef struct IBDA_NameValueServiceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IBDA_NameValueService *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IBDA_NameValueService *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IBDA_NameValueService *This);

    /*** IBDA_NameValueService methods ***/
    HRESULT (STDMETHODCALLTYPE *GetValueNameByIndex)(
        IBDA_NameValueService *This,
        ULONG ulIndex,
        BSTR *pbstrName);

    HRESULT (STDMETHODCALLTYPE *GetValue)(
        IBDA_NameValueService *This,
        BSTR bstrName,
        BSTR bstrLanguage,
        BSTR *pbstrValue);

    HRESULT (STDMETHODCALLTYPE *SetValue)(
        IBDA_NameValueService *This,
        ULONG ulDialogRequest,
        BSTR bstrLanguage,
        BSTR bstrName,
        BSTR bstrValue,
        ULONG ulReserved);

    END_INTERFACE
} IBDA_NameValueServiceVtbl;

interface IBDA_NameValueService {
    CONST_VTBL IBDA_NameValueServiceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IBDA_NameValueService_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IBDA_NameValueService_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IBDA_NameValueService_Release(This) (This)->lpVtbl->Release(This)
/*** IBDA_NameValueService methods ***/
#define IBDA_NameValueService_GetValueNameByIndex(This,ulIndex,pbstrName) (This)->lpVtbl->GetValueNameByIndex(This,ulIndex,pbstrName)
#define IBDA_NameValueService_GetValue(This,bstrName,bstrLanguage,pbstrValue) (This)->lpVtbl->GetValue(This,bstrName,bstrLanguage,pbstrValue)
#define IBDA_NameValueService_SetValue(This,ulDialogRequest,bstrLanguage,bstrName,bstrValue,ulReserved) (This)->lpVtbl->SetValue(This,ulDialogRequest,bstrLanguage,bstrName,bstrValue,ulReserved)
#else
/*** IUnknown methods ***/
static inline HRESULT IBDA_NameValueService_QueryInterface(IBDA_NameValueService* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IBDA_NameValueService_AddRef(IBDA_NameValueService* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IBDA_NameValueService_Release(IBDA_NameValueService* This) {
    return This->lpVtbl->Release(This);
}
/*** IBDA_NameValueService methods ***/
static inline HRESULT IBDA_NameValueService_GetValueNameByIndex(IBDA_NameValueService* This,ULONG ulIndex,BSTR *pbstrName) {
    return This->lpVtbl->GetValueNameByIndex(This,ulIndex,pbstrName);
}
static inline HRESULT IBDA_NameValueService_GetValue(IBDA_NameValueService* This,BSTR bstrName,BSTR bstrLanguage,BSTR *pbstrValue) {
    return This->lpVtbl->GetValue(This,bstrName,bstrLanguage,pbstrValue);
}
static inline HRESULT IBDA_NameValueService_SetValue(IBDA_NameValueService* This,ULONG ulDialogRequest,BSTR bstrLanguage,BSTR bstrName,BSTR bstrValue,ULONG ulReserved) {
    return This->lpVtbl->SetValue(This,ulDialogRequest,bstrLanguage,bstrName,bstrValue,ulReserved);
}
#endif
#endif

#endif


#endif  /* __IBDA_NameValueService_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IBDA_ConditionalAccessEx interface
 */
#ifndef __IBDA_ConditionalAccessEx_INTERFACE_DEFINED__
#define __IBDA_ConditionalAccessEx_INTERFACE_DEFINED__

DEFINE_GUID(IID_IBDA_ConditionalAccessEx, 0x497c3418, 0x23cb, 0x44ba, 0xbb,0x62, 0x76,0x9f,0x50,0x6f,0xce,0xa7);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("497c3418-23cb-44ba-bb62-769f506fcea7")
IBDA_ConditionalAccessEx : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE CheckEntitlementToken(
        ULONG ulDialogRequest,
        BSTR bstrLanguage,
        BDA_CONDITIONALACCESS_REQUESTTYPE RequestType,
        ULONG ulcbEntitlementTokenLen,
        BYTE *pbEntitlementToken,
        ULONG *pulDescrambleStatus) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetCaptureToken(
        ULONG ulcbCaptureTokenLen,
        BYTE *pbCaptureToken) = 0;

    virtual HRESULT STDMETHODCALLTYPE OpenBroadcastMmi(
        ULONG ulDialogRequest,
        BSTR bstrLanguage,
        ULONG EventId) = 0;

    virtual HRESULT STDMETHODCALLTYPE CloseMmiDialog(
        ULONG ulDialogRequest,
        BSTR bstrLanguage,
        ULONG ulDialogNumber,
        BDA_CONDITIONALACCESS_MMICLOSEREASON ReasonCode,
        ULONG *pulSessionResult) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateDialogRequestNumber(
        ULONG *pulDialogRequestNumber) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IBDA_ConditionalAccessEx, 0x497c3418, 0x23cb, 0x44ba, 0xbb,0x62, 0x76,0x9f,0x50,0x6f,0xce,0xa7)
#endif
#else
typedef struct IBDA_ConditionalAccessExVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IBDA_ConditionalAccessEx *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IBDA_ConditionalAccessEx *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IBDA_ConditionalAccessEx *This);

    /*** IBDA_ConditionalAccessEx methods ***/
    HRESULT (STDMETHODCALLTYPE *CheckEntitlementToken)(
        IBDA_ConditionalAccessEx *This,
        ULONG ulDialogRequest,
        BSTR bstrLanguage,
        BDA_CONDITIONALACCESS_REQUESTTYPE RequestType,
        ULONG ulcbEntitlementTokenLen,
        BYTE *pbEntitlementToken,
        ULONG *pulDescrambleStatus);

    HRESULT (STDMETHODCALLTYPE *SetCaptureToken)(
        IBDA_ConditionalAccessEx *This,
        ULONG ulcbCaptureTokenLen,
        BYTE *pbCaptureToken);

    HRESULT (STDMETHODCALLTYPE *OpenBroadcastMmi)(
        IBDA_ConditionalAccessEx *This,
        ULONG ulDialogRequest,
        BSTR bstrLanguage,
        ULONG EventId);

    HRESULT (STDMETHODCALLTYPE *CloseMmiDialog)(
        IBDA_ConditionalAccessEx *This,
        ULONG ulDialogRequest,
        BSTR bstrLanguage,
        ULONG ulDialogNumber,
        BDA_CONDITIONALACCESS_MMICLOSEREASON ReasonCode,
        ULONG *pulSessionResult);

    HRESULT (STDMETHODCALLTYPE *CreateDialogRequestNumber)(
        IBDA_ConditionalAccessEx *This,
        ULONG *pulDialogRequestNumber);

    END_INTERFACE
} IBDA_ConditionalAccessExVtbl;

interface IBDA_ConditionalAccessEx {
    CONST_VTBL IBDA_ConditionalAccessExVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IBDA_ConditionalAccessEx_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IBDA_ConditionalAccessEx_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IBDA_ConditionalAccessEx_Release(This) (This)->lpVtbl->Release(This)
/*** IBDA_ConditionalAccessEx methods ***/
#define IBDA_ConditionalAccessEx_CheckEntitlementToken(This,ulDialogRequest,bstrLanguage,RequestType,ulcbEntitlementTokenLen,pbEntitlementToken,pulDescrambleStatus) (This)->lpVtbl->CheckEntitlementToken(This,ulDialogRequest,bstrLanguage,RequestType,ulcbEntitlementTokenLen,pbEntitlementToken,pulDescrambleStatus)
#define IBDA_ConditionalAccessEx_SetCaptureToken(This,ulcbCaptureTokenLen,pbCaptureToken) (This)->lpVtbl->SetCaptureToken(This,ulcbCaptureTokenLen,pbCaptureToken)
#define IBDA_ConditionalAccessEx_OpenBroadcastMmi(This,ulDialogRequest,bstrLanguage,EventId) (This)->lpVtbl->OpenBroadcastMmi(This,ulDialogRequest,bstrLanguage,EventId)
#define IBDA_ConditionalAccessEx_CloseMmiDialog(This,ulDialogRequest,bstrLanguage,ulDialogNumber,ReasonCode,pulSessionResult) (This)->lpVtbl->CloseMmiDialog(This,ulDialogRequest,bstrLanguage,ulDialogNumber,ReasonCode,pulSessionResult)
#define IBDA_ConditionalAccessEx_CreateDialogRequestNumber(This,pulDialogRequestNumber) (This)->lpVtbl->CreateDialogRequestNumber(This,pulDialogRequestNumber)
#else
/*** IUnknown methods ***/
static inline HRESULT IBDA_ConditionalAccessEx_QueryInterface(IBDA_ConditionalAccessEx* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IBDA_ConditionalAccessEx_AddRef(IBDA_ConditionalAccessEx* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IBDA_ConditionalAccessEx_Release(IBDA_ConditionalAccessEx* This) {
    return This->lpVtbl->Release(This);
}
/*** IBDA_ConditionalAccessEx methods ***/
static inline HRESULT IBDA_ConditionalAccessEx_CheckEntitlementToken(IBDA_ConditionalAccessEx* This,ULONG ulDialogRequest,BSTR bstrLanguage,BDA_CONDITIONALACCESS_REQUESTTYPE RequestType,ULONG ulcbEntitlementTokenLen,BYTE *pbEntitlementToken,ULONG *pulDescrambleStatus) {
    return This->lpVtbl->CheckEntitlementToken(This,ulDialogRequest,bstrLanguage,RequestType,ulcbEntitlementTokenLen,pbEntitlementToken,pulDescrambleStatus);
}
static inline HRESULT IBDA_ConditionalAccessEx_SetCaptureToken(IBDA_ConditionalAccessEx* This,ULONG ulcbCaptureTokenLen,BYTE *pbCaptureToken) {
    return This->lpVtbl->SetCaptureToken(This,ulcbCaptureTokenLen,pbCaptureToken);
}
static inline HRESULT IBDA_ConditionalAccessEx_OpenBroadcastMmi(IBDA_ConditionalAccessEx* This,ULONG ulDialogRequest,BSTR bstrLanguage,ULONG EventId) {
    return This->lpVtbl->OpenBroadcastMmi(This,ulDialogRequest,bstrLanguage,EventId);
}
static inline HRESULT IBDA_ConditionalAccessEx_CloseMmiDialog(IBDA_ConditionalAccessEx* This,ULONG ulDialogRequest,BSTR bstrLanguage,ULONG ulDialogNumber,BDA_CONDITIONALACCESS_MMICLOSEREASON ReasonCode,ULONG *pulSessionResult) {
    return This->lpVtbl->CloseMmiDialog(This,ulDialogRequest,bstrLanguage,ulDialogNumber,ReasonCode,pulSessionResult);
}
static inline HRESULT IBDA_ConditionalAccessEx_CreateDialogRequestNumber(IBDA_ConditionalAccessEx* This,ULONG *pulDialogRequestNumber) {
    return This->lpVtbl->CreateDialogRequestNumber(This,pulDialogRequestNumber);
}
#endif
#endif

#endif


#endif  /* __IBDA_ConditionalAccessEx_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IBDA_ISDBConditionalAccess interface
 */
#ifndef __IBDA_ISDBConditionalAccess_INTERFACE_DEFINED__
#define __IBDA_ISDBConditionalAccess_INTERFACE_DEFINED__

DEFINE_GUID(IID_IBDA_ISDBConditionalAccess, 0x5e68c627, 0x16c2, 0x4e6c, 0xb1,0xe2, 0xd0,0x01,0x70,0xcd,0xaa,0x0f);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("5e68c627-16c2-4e6c-b1e2-d00170cdaa0f")
IBDA_ISDBConditionalAccess : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetIsdbCasRequest(
        ULONG ulRequestId,
        ULONG ulcbRequestBufferLen,
        BYTE *pbRequestBuffer) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IBDA_ISDBConditionalAccess, 0x5e68c627, 0x16c2, 0x4e6c, 0xb1,0xe2, 0xd0,0x01,0x70,0xcd,0xaa,0x0f)
#endif
#else
typedef struct IBDA_ISDBConditionalAccessVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IBDA_ISDBConditionalAccess *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IBDA_ISDBConditionalAccess *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IBDA_ISDBConditionalAccess *This);

    /*** IBDA_ISDBConditionalAccess methods ***/
    HRESULT (STDMETHODCALLTYPE *SetIsdbCasRequest)(
        IBDA_ISDBConditionalAccess *This,
        ULONG ulRequestId,
        ULONG ulcbRequestBufferLen,
        BYTE *pbRequestBuffer);

    END_INTERFACE
} IBDA_ISDBConditionalAccessVtbl;

interface IBDA_ISDBConditionalAccess {
    CONST_VTBL IBDA_ISDBConditionalAccessVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IBDA_ISDBConditionalAccess_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IBDA_ISDBConditionalAccess_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IBDA_ISDBConditionalAccess_Release(This) (This)->lpVtbl->Release(This)
/*** IBDA_ISDBConditionalAccess methods ***/
#define IBDA_ISDBConditionalAccess_SetIsdbCasRequest(This,ulRequestId,ulcbRequestBufferLen,pbRequestBuffer) (This)->lpVtbl->SetIsdbCasRequest(This,ulRequestId,ulcbRequestBufferLen,pbRequestBuffer)
#else
/*** IUnknown methods ***/
static inline HRESULT IBDA_ISDBConditionalAccess_QueryInterface(IBDA_ISDBConditionalAccess* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IBDA_ISDBConditionalAccess_AddRef(IBDA_ISDBConditionalAccess* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IBDA_ISDBConditionalAccess_Release(IBDA_ISDBConditionalAccess* This) {
    return This->lpVtbl->Release(This);
}
/*** IBDA_ISDBConditionalAccess methods ***/
static inline HRESULT IBDA_ISDBConditionalAccess_SetIsdbCasRequest(IBDA_ISDBConditionalAccess* This,ULONG ulRequestId,ULONG ulcbRequestBufferLen,BYTE *pbRequestBuffer) {
    return This->lpVtbl->SetIsdbCasRequest(This,ulRequestId,ulcbRequestBufferLen,pbRequestBuffer);
}
#endif
#endif

#endif


#endif  /* __IBDA_ISDBConditionalAccess_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IBDA_EventingService interface
 */
#ifndef __IBDA_EventingService_INTERFACE_DEFINED__
#define __IBDA_EventingService_INTERFACE_DEFINED__

DEFINE_GUID(IID_IBDA_EventingService, 0x207c413f, 0x00dc, 0x4c61, 0xba,0xd6, 0x6f,0xee,0x1f,0xf0,0x70,0x64);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("207c413f-00dc-4c61-bad6-6fee1ff07064")
IBDA_EventingService : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE CompleteEvent(
        ULONG ulEventID,
        ULONG ulEventResult) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IBDA_EventingService, 0x207c413f, 0x00dc, 0x4c61, 0xba,0xd6, 0x6f,0xee,0x1f,0xf0,0x70,0x64)
#endif
#else
typedef struct IBDA_EventingServiceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IBDA_EventingService *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IBDA_EventingService *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IBDA_EventingService *This);

    /*** IBDA_EventingService methods ***/
    HRESULT (STDMETHODCALLTYPE *CompleteEvent)(
        IBDA_EventingService *This,
        ULONG ulEventID,
        ULONG ulEventResult);

    END_INTERFACE
} IBDA_EventingServiceVtbl;

interface IBDA_EventingService {
    CONST_VTBL IBDA_EventingServiceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IBDA_EventingService_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IBDA_EventingService_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IBDA_EventingService_Release(This) (This)->lpVtbl->Release(This)
/*** IBDA_EventingService methods ***/
#define IBDA_EventingService_CompleteEvent(This,ulEventID,ulEventResult) (This)->lpVtbl->CompleteEvent(This,ulEventID,ulEventResult)
#else
/*** IUnknown methods ***/
static inline HRESULT IBDA_EventingService_QueryInterface(IBDA_EventingService* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IBDA_EventingService_AddRef(IBDA_EventingService* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IBDA_EventingService_Release(IBDA_EventingService* This) {
    return This->lpVtbl->Release(This);
}
/*** IBDA_EventingService methods ***/
static inline HRESULT IBDA_EventingService_CompleteEvent(IBDA_EventingService* This,ULONG ulEventID,ULONG ulEventResult) {
    return This->lpVtbl->CompleteEvent(This,ulEventID,ulEventResult);
}
#endif
#endif

#endif


#endif  /* __IBDA_EventingService_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IBDA_AUX interface
 */
#ifndef __IBDA_AUX_INTERFACE_DEFINED__
#define __IBDA_AUX_INTERFACE_DEFINED__

DEFINE_GUID(IID_IBDA_AUX, 0x7def4c09, 0x6e66, 0x4567, 0xa8,0x19, 0xf0,0xe1,0x7f,0x4a,0x81,0xab);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("7def4c09-6e66-4567-a819-f0e17f4a81ab")
IBDA_AUX : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE QueryCapabilities(
        DWORD *pdwNumAuxInputsBSTR) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumCapability(
        DWORD dwIndex,
        DWORD *dwInputID,
        GUID *pConnectorType,
        DWORD *ConnTypeNum,
        DWORD *NumVideoStds,
        ULONGLONG *AnalogStds) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IBDA_AUX, 0x7def4c09, 0x6e66, 0x4567, 0xa8,0x19, 0xf0,0xe1,0x7f,0x4a,0x81,0xab)
#endif
#else
typedef struct IBDA_AUXVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IBDA_AUX *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IBDA_AUX *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IBDA_AUX *This);

    /*** IBDA_AUX methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryCapabilities)(
        IBDA_AUX *This,
        DWORD *pdwNumAuxInputsBSTR);

    HRESULT (STDMETHODCALLTYPE *EnumCapability)(
        IBDA_AUX *This,
        DWORD dwIndex,
        DWORD *dwInputID,
        GUID *pConnectorType,
        DWORD *ConnTypeNum,
        DWORD *NumVideoStds,
        ULONGLONG *AnalogStds);

    END_INTERFACE
} IBDA_AUXVtbl;

interface IBDA_AUX {
    CONST_VTBL IBDA_AUXVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IBDA_AUX_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IBDA_AUX_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IBDA_AUX_Release(This) (This)->lpVtbl->Release(This)
/*** IBDA_AUX methods ***/
#define IBDA_AUX_QueryCapabilities(This,pdwNumAuxInputsBSTR) (This)->lpVtbl->QueryCapabilities(This,pdwNumAuxInputsBSTR)
#define IBDA_AUX_EnumCapability(This,dwIndex,dwInputID,pConnectorType,ConnTypeNum,NumVideoStds,AnalogStds) (This)->lpVtbl->EnumCapability(This,dwIndex,dwInputID,pConnectorType,ConnTypeNum,NumVideoStds,AnalogStds)
#else
/*** IUnknown methods ***/
static inline HRESULT IBDA_AUX_QueryInterface(IBDA_AUX* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IBDA_AUX_AddRef(IBDA_AUX* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IBDA_AUX_Release(IBDA_AUX* This) {
    return This->lpVtbl->Release(This);
}
/*** IBDA_AUX methods ***/
static inline HRESULT IBDA_AUX_QueryCapabilities(IBDA_AUX* This,DWORD *pdwNumAuxInputsBSTR) {
    return This->lpVtbl->QueryCapabilities(This,pdwNumAuxInputsBSTR);
}
static inline HRESULT IBDA_AUX_EnumCapability(IBDA_AUX* This,DWORD dwIndex,DWORD *dwInputID,GUID *pConnectorType,DWORD *ConnTypeNum,DWORD *NumVideoStds,ULONGLONG *AnalogStds) {
    return This->lpVtbl->EnumCapability(This,dwIndex,dwInputID,pConnectorType,ConnTypeNum,NumVideoStds,AnalogStds);
}
#endif
#endif

#endif


#endif  /* __IBDA_AUX_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IBDA_Encoder interface
 */
#ifndef __IBDA_Encoder_INTERFACE_DEFINED__
#define __IBDA_Encoder_INTERFACE_DEFINED__

DEFINE_GUID(IID_IBDA_Encoder, 0x3a8bad59, 0x59fe, 0x4559, 0xa0,0xba, 0x39,0x6c,0xfa,0xa9,0x8a,0xe3);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("3a8bad59-59fe-4559-a0ba-396cfaa98ae3")
IBDA_Encoder : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE QueryCapabilities(
        DWORD *NumAudioFmts,
        DWORD *NumVideoFmts) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumAudioCapability(
        DWORD FmtIndex,
        DWORD *MethodID,
        DWORD *AlgorithmType,
        DWORD *SamplingRate,
        DWORD *BitDepth,
        DWORD *NumChannels) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumVideoCapability(
        DWORD FmtIndex,
        DWORD *MethodID,
        DWORD *AlgorithmType,
        DWORD *VerticalSize,
        DWORD *HorizontalSize,
        DWORD *AspectRatio,
        DWORD *FrameRateCode,
        DWORD *ProgressiveSequence) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetParameters(
        DWORD AudioBitrateMode,
        DWORD AudioBitrate,
        DWORD AudioMethodID,
        DWORD AudioProgram,
        DWORD VideoBitrateMode,
        DWORD VideoBitrate,
        DWORD VideoMethodID) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetState(
        DWORD *AudioBitrateMax,
        DWORD *AudioBitrateMin,
        DWORD *AudioBitrateMode,
        DWORD *AudioBitrateStepping,
        DWORD *AudioBitrate,
        DWORD *AudioMethodID,
        DWORD *AvailableAudioPrograms,
        DWORD *AudioProgram,
        DWORD *VideoBitrateMax,
        DWORD *VideoBitrateMin,
        DWORD *VideoBitrateMode,
        DWORD *VideoBitrate,
        DWORD *VideoBitrateStepping,
        DWORD *VideoMethodID,
        DWORD *SignalSourceID,
        ULONGLONG *SignalFormat,
        WINBOOL *SignalLock,
        LONG *SignalLevel,
        DWORD *SignalToNoiseRatio) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IBDA_Encoder, 0x3a8bad59, 0x59fe, 0x4559, 0xa0,0xba, 0x39,0x6c,0xfa,0xa9,0x8a,0xe3)
#endif
#else
typedef struct IBDA_EncoderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IBDA_Encoder *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IBDA_Encoder *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IBDA_Encoder *This);

    /*** IBDA_Encoder methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryCapabilities)(
        IBDA_Encoder *This,
        DWORD *NumAudioFmts,
        DWORD *NumVideoFmts);

    HRESULT (STDMETHODCALLTYPE *EnumAudioCapability)(
        IBDA_Encoder *This,
        DWORD FmtIndex,
        DWORD *MethodID,
        DWORD *AlgorithmType,
        DWORD *SamplingRate,
        DWORD *BitDepth,
        DWORD *NumChannels);

    HRESULT (STDMETHODCALLTYPE *EnumVideoCapability)(
        IBDA_Encoder *This,
        DWORD FmtIndex,
        DWORD *MethodID,
        DWORD *AlgorithmType,
        DWORD *VerticalSize,
        DWORD *HorizontalSize,
        DWORD *AspectRatio,
        DWORD *FrameRateCode,
        DWORD *ProgressiveSequence);

    HRESULT (STDMETHODCALLTYPE *SetParameters)(
        IBDA_Encoder *This,
        DWORD AudioBitrateMode,
        DWORD AudioBitrate,
        DWORD AudioMethodID,
        DWORD AudioProgram,
        DWORD VideoBitrateMode,
        DWORD VideoBitrate,
        DWORD VideoMethodID);

    HRESULT (STDMETHODCALLTYPE *GetState)(
        IBDA_Encoder *This,
        DWORD *AudioBitrateMax,
        DWORD *AudioBitrateMin,
        DWORD *AudioBitrateMode,
        DWORD *AudioBitrateStepping,
        DWORD *AudioBitrate,
        DWORD *AudioMethodID,
        DWORD *AvailableAudioPrograms,
        DWORD *AudioProgram,
        DWORD *VideoBitrateMax,
        DWORD *VideoBitrateMin,
        DWORD *VideoBitrateMode,
        DWORD *VideoBitrate,
        DWORD *VideoBitrateStepping,
        DWORD *VideoMethodID,
        DWORD *SignalSourceID,
        ULONGLONG *SignalFormat,
        WINBOOL *SignalLock,
        LONG *SignalLevel,
        DWORD *SignalToNoiseRatio);

    END_INTERFACE
} IBDA_EncoderVtbl;

interface IBDA_Encoder {
    CONST_VTBL IBDA_EncoderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IBDA_Encoder_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IBDA_Encoder_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IBDA_Encoder_Release(This) (This)->lpVtbl->Release(This)
/*** IBDA_Encoder methods ***/
#define IBDA_Encoder_QueryCapabilities(This,NumAudioFmts,NumVideoFmts) (This)->lpVtbl->QueryCapabilities(This,NumAudioFmts,NumVideoFmts)
#define IBDA_Encoder_EnumAudioCapability(This,FmtIndex,MethodID,AlgorithmType,SamplingRate,BitDepth,NumChannels) (This)->lpVtbl->EnumAudioCapability(This,FmtIndex,MethodID,AlgorithmType,SamplingRate,BitDepth,NumChannels)
#define IBDA_Encoder_EnumVideoCapability(This,FmtIndex,MethodID,AlgorithmType,VerticalSize,HorizontalSize,AspectRatio,FrameRateCode,ProgressiveSequence) (This)->lpVtbl->EnumVideoCapability(This,FmtIndex,MethodID,AlgorithmType,VerticalSize,HorizontalSize,AspectRatio,FrameRateCode,ProgressiveSequence)
#define IBDA_Encoder_SetParameters(This,AudioBitrateMode,AudioBitrate,AudioMethodID,AudioProgram,VideoBitrateMode,VideoBitrate,VideoMethodID) (This)->lpVtbl->SetParameters(This,AudioBitrateMode,AudioBitrate,AudioMethodID,AudioProgram,VideoBitrateMode,VideoBitrate,VideoMethodID)
#define IBDA_Encoder_GetState(This,AudioBitrateMax,AudioBitrateMin,AudioBitrateMode,AudioBitrateStepping,AudioBitrate,AudioMethodID,AvailableAudioPrograms,AudioProgram,VideoBitrateMax,VideoBitrateMin,VideoBitrateMode,VideoBitrate,VideoBitrateStepping,VideoMethodID,SignalSourceID,SignalFormat,SignalLock,SignalLevel,SignalToNoiseRatio) (This)->lpVtbl->GetState(This,AudioBitrateMax,AudioBitrateMin,AudioBitrateMode,AudioBitrateStepping,AudioBitrate,AudioMethodID,AvailableAudioPrograms,AudioProgram,VideoBitrateMax,VideoBitrateMin,VideoBitrateMode,VideoBitrate,VideoBitrateStepping,VideoMethodID,SignalSourceID,SignalFormat,SignalLock,SignalLevel,SignalToNoiseRatio)
#else
/*** IUnknown methods ***/
static inline HRESULT IBDA_Encoder_QueryInterface(IBDA_Encoder* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IBDA_Encoder_AddRef(IBDA_Encoder* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IBDA_Encoder_Release(IBDA_Encoder* This) {
    return This->lpVtbl->Release(This);
}
/*** IBDA_Encoder methods ***/
static inline HRESULT IBDA_Encoder_QueryCapabilities(IBDA_Encoder* This,DWORD *NumAudioFmts,DWORD *NumVideoFmts) {
    return This->lpVtbl->QueryCapabilities(This,NumAudioFmts,NumVideoFmts);
}
static inline HRESULT IBDA_Encoder_EnumAudioCapability(IBDA_Encoder* This,DWORD FmtIndex,DWORD *MethodID,DWORD *AlgorithmType,DWORD *SamplingRate,DWORD *BitDepth,DWORD *NumChannels) {
    return This->lpVtbl->EnumAudioCapability(This,FmtIndex,MethodID,AlgorithmType,SamplingRate,BitDepth,NumChannels);
}
static inline HRESULT IBDA_Encoder_EnumVideoCapability(IBDA_Encoder* This,DWORD FmtIndex,DWORD *MethodID,DWORD *AlgorithmType,DWORD *VerticalSize,DWORD *HorizontalSize,DWORD *AspectRatio,DWORD *FrameRateCode,DWORD *ProgressiveSequence) {
    return This->lpVtbl->EnumVideoCapability(This,FmtIndex,MethodID,AlgorithmType,VerticalSize,HorizontalSize,AspectRatio,FrameRateCode,ProgressiveSequence);
}
static inline HRESULT IBDA_Encoder_SetParameters(IBDA_Encoder* This,DWORD AudioBitrateMode,DWORD AudioBitrate,DWORD AudioMethodID,DWORD AudioProgram,DWORD VideoBitrateMode,DWORD VideoBitrate,DWORD VideoMethodID) {
    return This->lpVtbl->SetParameters(This,AudioBitrateMode,AudioBitrate,AudioMethodID,AudioProgram,VideoBitrateMode,VideoBitrate,VideoMethodID);
}
static inline HRESULT IBDA_Encoder_GetState(IBDA_Encoder* This,DWORD *AudioBitrateMax,DWORD *AudioBitrateMin,DWORD *AudioBitrateMode,DWORD *AudioBitrateStepping,DWORD *AudioBitrate,DWORD *AudioMethodID,DWORD *AvailableAudioPrograms,DWORD *AudioProgram,DWORD *VideoBitrateMax,DWORD *VideoBitrateMin,DWORD *VideoBitrateMode,DWORD *VideoBitrate,DWORD *VideoBitrateStepping,DWORD *VideoMethodID,DWORD *SignalSourceID,ULONGLONG *SignalFormat,WINBOOL *SignalLock,LONG *SignalLevel,DWORD *SignalToNoiseRatio) {
    return This->lpVtbl->GetState(This,AudioBitrateMax,AudioBitrateMin,AudioBitrateMode,AudioBitrateStepping,AudioBitrate,AudioMethodID,AvailableAudioPrograms,AudioProgram,VideoBitrateMax,VideoBitrateMin,VideoBitrateMode,VideoBitrate,VideoBitrateStepping,VideoMethodID,SignalSourceID,SignalFormat,SignalLock,SignalLevel,SignalToNoiseRatio);
}
#endif
#endif

#endif


#endif  /* __IBDA_Encoder_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IBDA_FDC interface
 */
#ifndef __IBDA_FDC_INTERFACE_DEFINED__
#define __IBDA_FDC_INTERFACE_DEFINED__

DEFINE_GUID(IID_IBDA_FDC, 0x138adc7e, 0x58ae, 0x437f, 0xb0,0xb4, 0xc9,0xfe,0x19,0xd5,0xb4,0xac);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("138adc7e-58ae-437f-b0b4-c9fe19d5b4ac")
IBDA_FDC : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetStatus(
        DWORD *CurrentBitrate,
        WINBOOL *CarrierLock,
        DWORD *CurrentFrequency,
        WINBOOL *CurrentSpectrumInversion,
        BSTR *CurrentPIDList,
        BSTR *CurrentTIDList,
        WINBOOL *Overflow) = 0;

    virtual HRESULT STDMETHODCALLTYPE RequestTables(
        BSTR TableIDs) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddPid(
        BSTR PidsToAdd,
        DWORD *RemainingFilterEntries) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemovePid(
        BSTR PidsToRemove) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddTid(
        BSTR TidsToAdd,
        BSTR *CurrentTidList) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveTid(
        BSTR TidsToRemove) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTableSection(
        DWORD *Pid,
        DWORD MaxBufferSize,
        DWORD *ActualSize,
        BYTE *SecBuffer) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IBDA_FDC, 0x138adc7e, 0x58ae, 0x437f, 0xb0,0xb4, 0xc9,0xfe,0x19,0xd5,0xb4,0xac)
#endif
#else
typedef struct IBDA_FDCVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IBDA_FDC *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IBDA_FDC *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IBDA_FDC *This);

    /*** IBDA_FDC methods ***/
    HRESULT (STDMETHODCALLTYPE *GetStatus)(
        IBDA_FDC *This,
        DWORD *CurrentBitrate,
        WINBOOL *CarrierLock,
        DWORD *CurrentFrequency,
        WINBOOL *CurrentSpectrumInversion,
        BSTR *CurrentPIDList,
        BSTR *CurrentTIDList,
        WINBOOL *Overflow);

    HRESULT (STDMETHODCALLTYPE *RequestTables)(
        IBDA_FDC *This,
        BSTR TableIDs);

    HRESULT (STDMETHODCALLTYPE *AddPid)(
        IBDA_FDC *This,
        BSTR PidsToAdd,
        DWORD *RemainingFilterEntries);

    HRESULT (STDMETHODCALLTYPE *RemovePid)(
        IBDA_FDC *This,
        BSTR PidsToRemove);

    HRESULT (STDMETHODCALLTYPE *AddTid)(
        IBDA_FDC *This,
        BSTR TidsToAdd,
        BSTR *CurrentTidList);

    HRESULT (STDMETHODCALLTYPE *RemoveTid)(
        IBDA_FDC *This,
        BSTR TidsToRemove);

    HRESULT (STDMETHODCALLTYPE *GetTableSection)(
        IBDA_FDC *This,
        DWORD *Pid,
        DWORD MaxBufferSize,
        DWORD *ActualSize,
        BYTE *SecBuffer);

    END_INTERFACE
} IBDA_FDCVtbl;

interface IBDA_FDC {
    CONST_VTBL IBDA_FDCVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IBDA_FDC_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IBDA_FDC_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IBDA_FDC_Release(This) (This)->lpVtbl->Release(This)
/*** IBDA_FDC methods ***/
#define IBDA_FDC_GetStatus(This,CurrentBitrate,CarrierLock,CurrentFrequency,CurrentSpectrumInversion,CurrentPIDList,CurrentTIDList,Overflow) (This)->lpVtbl->GetStatus(This,CurrentBitrate,CarrierLock,CurrentFrequency,CurrentSpectrumInversion,CurrentPIDList,CurrentTIDList,Overflow)
#define IBDA_FDC_RequestTables(This,TableIDs) (This)->lpVtbl->RequestTables(This,TableIDs)
#define IBDA_FDC_AddPid(This,PidsToAdd,RemainingFilterEntries) (This)->lpVtbl->AddPid(This,PidsToAdd,RemainingFilterEntries)
#define IBDA_FDC_RemovePid(This,PidsToRemove) (This)->lpVtbl->RemovePid(This,PidsToRemove)
#define IBDA_FDC_AddTid(This,TidsToAdd,CurrentTidList) (This)->lpVtbl->AddTid(This,TidsToAdd,CurrentTidList)
#define IBDA_FDC_RemoveTid(This,TidsToRemove) (This)->lpVtbl->RemoveTid(This,TidsToRemove)
#define IBDA_FDC_GetTableSection(This,Pid,MaxBufferSize,ActualSize,SecBuffer) (This)->lpVtbl->GetTableSection(This,Pid,MaxBufferSize,ActualSize,SecBuffer)
#else
/*** IUnknown methods ***/
static inline HRESULT IBDA_FDC_QueryInterface(IBDA_FDC* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IBDA_FDC_AddRef(IBDA_FDC* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IBDA_FDC_Release(IBDA_FDC* This) {
    return This->lpVtbl->Release(This);
}
/*** IBDA_FDC methods ***/
static inline HRESULT IBDA_FDC_GetStatus(IBDA_FDC* This,DWORD *CurrentBitrate,WINBOOL *CarrierLock,DWORD *CurrentFrequency,WINBOOL *CurrentSpectrumInversion,BSTR *CurrentPIDList,BSTR *CurrentTIDList,WINBOOL *Overflow) {
    return This->lpVtbl->GetStatus(This,CurrentBitrate,CarrierLock,CurrentFrequency,CurrentSpectrumInversion,CurrentPIDList,CurrentTIDList,Overflow);
}
static inline HRESULT IBDA_FDC_RequestTables(IBDA_FDC* This,BSTR TableIDs) {
    return This->lpVtbl->RequestTables(This,TableIDs);
}
static inline HRESULT IBDA_FDC_AddPid(IBDA_FDC* This,BSTR PidsToAdd,DWORD *RemainingFilterEntries) {
    return This->lpVtbl->AddPid(This,PidsToAdd,RemainingFilterEntries);
}
static inline HRESULT IBDA_FDC_RemovePid(IBDA_FDC* This,BSTR PidsToRemove) {
    return This->lpVtbl->RemovePid(This,PidsToRemove);
}
static inline HRESULT IBDA_FDC_AddTid(IBDA_FDC* This,BSTR TidsToAdd,BSTR *CurrentTidList) {
    return This->lpVtbl->AddTid(This,TidsToAdd,CurrentTidList);
}
static inline HRESULT IBDA_FDC_RemoveTid(IBDA_FDC* This,BSTR TidsToRemove) {
    return This->lpVtbl->RemoveTid(This,TidsToRemove);
}
static inline HRESULT IBDA_FDC_GetTableSection(IBDA_FDC* This,DWORD *Pid,DWORD MaxBufferSize,DWORD *ActualSize,BYTE *SecBuffer) {
    return This->lpVtbl->GetTableSection(This,Pid,MaxBufferSize,ActualSize,SecBuffer);
}
#endif
#endif

#endif


#endif  /* __IBDA_FDC_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IBDA_GuideDataDeliveryService interface
 */
#ifndef __IBDA_GuideDataDeliveryService_INTERFACE_DEFINED__
#define __IBDA_GuideDataDeliveryService_INTERFACE_DEFINED__

DEFINE_GUID(IID_IBDA_GuideDataDeliveryService, 0xc0afcb73, 0x23e7, 0x4bc6, 0xba,0xfa, 0xfd,0xc1,0x67,0xb4,0x71,0x9f);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("c0afcb73-23e7-4bc6-bafa-fdc167b4719f")
IBDA_GuideDataDeliveryService : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetGuideDataType(
        GUID *pguidDataType) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetGuideData(
        ULONG *pulcbBufferLen,
        BYTE *pbBuffer,
        ULONG *pulGuideDataPercentageProgress) = 0;

    virtual HRESULT STDMETHODCALLTYPE RequestGuideDataUpdate(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTuneXmlFromServiceIdx(
        ULONG64 ul64ServiceIdx,
        BSTR *pbstrTuneXml) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetServices(
        ULONG *pulcbBufferLen,
        BYTE *pbBuffer) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetServiceInfoFromTuneXml(
        BSTR bstrTuneXml,
        BSTR *pbstrServiceDescription) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IBDA_GuideDataDeliveryService, 0xc0afcb73, 0x23e7, 0x4bc6, 0xba,0xfa, 0xfd,0xc1,0x67,0xb4,0x71,0x9f)
#endif
#else
typedef struct IBDA_GuideDataDeliveryServiceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IBDA_GuideDataDeliveryService *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IBDA_GuideDataDeliveryService *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IBDA_GuideDataDeliveryService *This);

    /*** IBDA_GuideDataDeliveryService methods ***/
    HRESULT (STDMETHODCALLTYPE *GetGuideDataType)(
        IBDA_GuideDataDeliveryService *This,
        GUID *pguidDataType);

    HRESULT (STDMETHODCALLTYPE *GetGuideData)(
        IBDA_GuideDataDeliveryService *This,
        ULONG *pulcbBufferLen,
        BYTE *pbBuffer,
        ULONG *pulGuideDataPercentageProgress);

    HRESULT (STDMETHODCALLTYPE *RequestGuideDataUpdate)(
        IBDA_GuideDataDeliveryService *This);

    HRESULT (STDMETHODCALLTYPE *GetTuneXmlFromServiceIdx)(
        IBDA_GuideDataDeliveryService *This,
        ULONG64 ul64ServiceIdx,
        BSTR *pbstrTuneXml);

    HRESULT (STDMETHODCALLTYPE *GetServices)(
        IBDA_GuideDataDeliveryService *This,
        ULONG *pulcbBufferLen,
        BYTE *pbBuffer);

    HRESULT (STDMETHODCALLTYPE *GetServiceInfoFromTuneXml)(
        IBDA_GuideDataDeliveryService *This,
        BSTR bstrTuneXml,
        BSTR *pbstrServiceDescription);

    END_INTERFACE
} IBDA_GuideDataDeliveryServiceVtbl;

interface IBDA_GuideDataDeliveryService {
    CONST_VTBL IBDA_GuideDataDeliveryServiceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IBDA_GuideDataDeliveryService_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IBDA_GuideDataDeliveryService_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IBDA_GuideDataDeliveryService_Release(This) (This)->lpVtbl->Release(This)
/*** IBDA_GuideDataDeliveryService methods ***/
#define IBDA_GuideDataDeliveryService_GetGuideDataType(This,pguidDataType) (This)->lpVtbl->GetGuideDataType(This,pguidDataType)
#define IBDA_GuideDataDeliveryService_GetGuideData(This,pulcbBufferLen,pbBuffer,pulGuideDataPercentageProgress) (This)->lpVtbl->GetGuideData(This,pulcbBufferLen,pbBuffer,pulGuideDataPercentageProgress)
#define IBDA_GuideDataDeliveryService_RequestGuideDataUpdate(This) (This)->lpVtbl->RequestGuideDataUpdate(This)
#define IBDA_GuideDataDeliveryService_GetTuneXmlFromServiceIdx(This,ul64ServiceIdx,pbstrTuneXml) (This)->lpVtbl->GetTuneXmlFromServiceIdx(This,ul64ServiceIdx,pbstrTuneXml)
#define IBDA_GuideDataDeliveryService_GetServices(This,pulcbBufferLen,pbBuffer) (This)->lpVtbl->GetServices(This,pulcbBufferLen,pbBuffer)
#define IBDA_GuideDataDeliveryService_GetServiceInfoFromTuneXml(This,bstrTuneXml,pbstrServiceDescription) (This)->lpVtbl->GetServiceInfoFromTuneXml(This,bstrTuneXml,pbstrServiceDescription)
#else
/*** IUnknown methods ***/
static inline HRESULT IBDA_GuideDataDeliveryService_QueryInterface(IBDA_GuideDataDeliveryService* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IBDA_GuideDataDeliveryService_AddRef(IBDA_GuideDataDeliveryService* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IBDA_GuideDataDeliveryService_Release(IBDA_GuideDataDeliveryService* This) {
    return This->lpVtbl->Release(This);
}
/*** IBDA_GuideDataDeliveryService methods ***/
static inline HRESULT IBDA_GuideDataDeliveryService_GetGuideDataType(IBDA_GuideDataDeliveryService* This,GUID *pguidDataType) {
    return This->lpVtbl->GetGuideDataType(This,pguidDataType);
}
static inline HRESULT IBDA_GuideDataDeliveryService_GetGuideData(IBDA_GuideDataDeliveryService* This,ULONG *pulcbBufferLen,BYTE *pbBuffer,ULONG *pulGuideDataPercentageProgress) {
    return This->lpVtbl->GetGuideData(This,pulcbBufferLen,pbBuffer,pulGuideDataPercentageProgress);
}
static inline HRESULT IBDA_GuideDataDeliveryService_RequestGuideDataUpdate(IBDA_GuideDataDeliveryService* This) {
    return This->lpVtbl->RequestGuideDataUpdate(This);
}
static inline HRESULT IBDA_GuideDataDeliveryService_GetTuneXmlFromServiceIdx(IBDA_GuideDataDeliveryService* This,ULONG64 ul64ServiceIdx,BSTR *pbstrTuneXml) {
    return This->lpVtbl->GetTuneXmlFromServiceIdx(This,ul64ServiceIdx,pbstrTuneXml);
}
static inline HRESULT IBDA_GuideDataDeliveryService_GetServices(IBDA_GuideDataDeliveryService* This,ULONG *pulcbBufferLen,BYTE *pbBuffer) {
    return This->lpVtbl->GetServices(This,pulcbBufferLen,pbBuffer);
}
static inline HRESULT IBDA_GuideDataDeliveryService_GetServiceInfoFromTuneXml(IBDA_GuideDataDeliveryService* This,BSTR bstrTuneXml,BSTR *pbstrServiceDescription) {
    return This->lpVtbl->GetServiceInfoFromTuneXml(This,bstrTuneXml,pbstrServiceDescription);
}
#endif
#endif

#endif


#endif  /* __IBDA_GuideDataDeliveryService_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IBDA_DRMService interface
 */
#ifndef __IBDA_DRMService_INTERFACE_DEFINED__
#define __IBDA_DRMService_INTERFACE_DEFINED__

DEFINE_GUID(IID_IBDA_DRMService, 0xbff6b5bb, 0xb0ae, 0x484c, 0x9d,0xca, 0x73,0x52,0x8f,0xb0,0xb4,0x6e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("bff6b5bb-b0ae-484c-9dca-73528fb0b46e")
IBDA_DRMService : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetDRM(
        GUID *puuidNewDrm) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDRMStatus(
        BSTR *pbstrDrmUuidList,
        GUID *DrmUuid) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IBDA_DRMService, 0xbff6b5bb, 0xb0ae, 0x484c, 0x9d,0xca, 0x73,0x52,0x8f,0xb0,0xb4,0x6e)
#endif
#else
typedef struct IBDA_DRMServiceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IBDA_DRMService *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IBDA_DRMService *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IBDA_DRMService *This);

    /*** IBDA_DRMService methods ***/
    HRESULT (STDMETHODCALLTYPE *SetDRM)(
        IBDA_DRMService *This,
        GUID *puuidNewDrm);

    HRESULT (STDMETHODCALLTYPE *GetDRMStatus)(
        IBDA_DRMService *This,
        BSTR *pbstrDrmUuidList,
        GUID *DrmUuid);

    END_INTERFACE
} IBDA_DRMServiceVtbl;

interface IBDA_DRMService {
    CONST_VTBL IBDA_DRMServiceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IBDA_DRMService_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IBDA_DRMService_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IBDA_DRMService_Release(This) (This)->lpVtbl->Release(This)
/*** IBDA_DRMService methods ***/
#define IBDA_DRMService_SetDRM(This,puuidNewDrm) (This)->lpVtbl->SetDRM(This,puuidNewDrm)
#define IBDA_DRMService_GetDRMStatus(This,pbstrDrmUuidList,DrmUuid) (This)->lpVtbl->GetDRMStatus(This,pbstrDrmUuidList,DrmUuid)
#else
/*** IUnknown methods ***/
static inline HRESULT IBDA_DRMService_QueryInterface(IBDA_DRMService* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IBDA_DRMService_AddRef(IBDA_DRMService* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IBDA_DRMService_Release(IBDA_DRMService* This) {
    return This->lpVtbl->Release(This);
}
/*** IBDA_DRMService methods ***/
static inline HRESULT IBDA_DRMService_SetDRM(IBDA_DRMService* This,GUID *puuidNewDrm) {
    return This->lpVtbl->SetDRM(This,puuidNewDrm);
}
static inline HRESULT IBDA_DRMService_GetDRMStatus(IBDA_DRMService* This,BSTR *pbstrDrmUuidList,GUID *DrmUuid) {
    return This->lpVtbl->GetDRMStatus(This,pbstrDrmUuidList,DrmUuid);
}
#endif
#endif

#endif


#endif  /* __IBDA_DRMService_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IBDA_WMDRMSession interface
 */
#ifndef __IBDA_WMDRMSession_INTERFACE_DEFINED__
#define __IBDA_WMDRMSession_INTERFACE_DEFINED__

DEFINE_GUID(IID_IBDA_WMDRMSession, 0x4be6fa3d, 0x07cd, 0x4139, 0x8b,0x80, 0x8c,0x18,0xba,0x3a,0xec,0x88);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("4be6fa3d-07cd-4139-8b80-8c18ba3aec88")
IBDA_WMDRMSession : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetStatus(
        ULONG *MaxCaptureToken,
        ULONG *MaxStreamingPid,
        ULONG *MaxLicense,
        ULONG *MinSecurityLevel,
        ULONG *RevInfoSequenceNumber,
        ULONGLONG *RevInfoIssuedTime,
        ULONG *RevInfoTTL,
        ULONG *RevListVersion,
        ULONG *ulState) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetRevInfo(
        ULONG ulRevInfoLen,
        BYTE *pbRevInfo) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetCrl(
        ULONG ulCrlLen,
        BYTE *pbCrlLen) = 0;

    virtual HRESULT STDMETHODCALLTYPE TransactMessage(
        ULONG ulcbRequest,
        BYTE *pbRequest,
        ULONG *pulcbResponse,
        BYTE *pbResponse) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetLicense(
        GUID *uuidKey,
        ULONG *pulPackageLen,
        BYTE *pbPackage) = 0;

    virtual HRESULT STDMETHODCALLTYPE ReissueLicense(
        GUID *uuidKey) = 0;

    virtual HRESULT STDMETHODCALLTYPE RenewLicense(
        ULONG ulInXmrLicenseLen,
        BYTE *pbInXmrLicense,
        ULONG ulEntitlementTokenLen,
        BYTE *pbEntitlementToken,
        ULONG *pulDescrambleStatus,
        ULONG *pulOutXmrLicenseLen,
        BYTE *pbOutXmrLicense) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetKeyInfo(
        ULONG *pulKeyInfoLen,
        BYTE *pbKeyInfo) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IBDA_WMDRMSession, 0x4be6fa3d, 0x07cd, 0x4139, 0x8b,0x80, 0x8c,0x18,0xba,0x3a,0xec,0x88)
#endif
#else
typedef struct IBDA_WMDRMSessionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IBDA_WMDRMSession *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IBDA_WMDRMSession *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IBDA_WMDRMSession *This);

    /*** IBDA_WMDRMSession methods ***/
    HRESULT (STDMETHODCALLTYPE *GetStatus)(
        IBDA_WMDRMSession *This,
        ULONG *MaxCaptureToken,
        ULONG *MaxStreamingPid,
        ULONG *MaxLicense,
        ULONG *MinSecurityLevel,
        ULONG *RevInfoSequenceNumber,
        ULONGLONG *RevInfoIssuedTime,
        ULONG *RevInfoTTL,
        ULONG *RevListVersion,
        ULONG *ulState);

    HRESULT (STDMETHODCALLTYPE *SetRevInfo)(
        IBDA_WMDRMSession *This,
        ULONG ulRevInfoLen,
        BYTE *pbRevInfo);

    HRESULT (STDMETHODCALLTYPE *SetCrl)(
        IBDA_WMDRMSession *This,
        ULONG ulCrlLen,
        BYTE *pbCrlLen);

    HRESULT (STDMETHODCALLTYPE *TransactMessage)(
        IBDA_WMDRMSession *This,
        ULONG ulcbRequest,
        BYTE *pbRequest,
        ULONG *pulcbResponse,
        BYTE *pbResponse);

    HRESULT (STDMETHODCALLTYPE *GetLicense)(
        IBDA_WMDRMSession *This,
        GUID *uuidKey,
        ULONG *pulPackageLen,
        BYTE *pbPackage);

    HRESULT (STDMETHODCALLTYPE *ReissueLicense)(
        IBDA_WMDRMSession *This,
        GUID *uuidKey);

    HRESULT (STDMETHODCALLTYPE *RenewLicense)(
        IBDA_WMDRMSession *This,
        ULONG ulInXmrLicenseLen,
        BYTE *pbInXmrLicense,
        ULONG ulEntitlementTokenLen,
        BYTE *pbEntitlementToken,
        ULONG *pulDescrambleStatus,
        ULONG *pulOutXmrLicenseLen,
        BYTE *pbOutXmrLicense);

    HRESULT (STDMETHODCALLTYPE *GetKeyInfo)(
        IBDA_WMDRMSession *This,
        ULONG *pulKeyInfoLen,
        BYTE *pbKeyInfo);

    END_INTERFACE
} IBDA_WMDRMSessionVtbl;

interface IBDA_WMDRMSession {
    CONST_VTBL IBDA_WMDRMSessionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IBDA_WMDRMSession_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IBDA_WMDRMSession_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IBDA_WMDRMSession_Release(This) (This)->lpVtbl->Release(This)
/*** IBDA_WMDRMSession methods ***/
#define IBDA_WMDRMSession_GetStatus(This,MaxCaptureToken,MaxStreamingPid,MaxLicense,MinSecurityLevel,RevInfoSequenceNumber,RevInfoIssuedTime,RevInfoTTL,RevListVersion,ulState) (This)->lpVtbl->GetStatus(This,MaxCaptureToken,MaxStreamingPid,MaxLicense,MinSecurityLevel,RevInfoSequenceNumber,RevInfoIssuedTime,RevInfoTTL,RevListVersion,ulState)
#define IBDA_WMDRMSession_SetRevInfo(This,ulRevInfoLen,pbRevInfo) (This)->lpVtbl->SetRevInfo(This,ulRevInfoLen,pbRevInfo)
#define IBDA_WMDRMSession_SetCrl(This,ulCrlLen,pbCrlLen) (This)->lpVtbl->SetCrl(This,ulCrlLen,pbCrlLen)
#define IBDA_WMDRMSession_TransactMessage(This,ulcbRequest,pbRequest,pulcbResponse,pbResponse) (This)->lpVtbl->TransactMessage(This,ulcbRequest,pbRequest,pulcbResponse,pbResponse)
#define IBDA_WMDRMSession_GetLicense(This,uuidKey,pulPackageLen,pbPackage) (This)->lpVtbl->GetLicense(This,uuidKey,pulPackageLen,pbPackage)
#define IBDA_WMDRMSession_ReissueLicense(This,uuidKey) (This)->lpVtbl->ReissueLicense(This,uuidKey)
#define IBDA_WMDRMSession_RenewLicense(This,ulInXmrLicenseLen,pbInXmrLicense,ulEntitlementTokenLen,pbEntitlementToken,pulDescrambleStatus,pulOutXmrLicenseLen,pbOutXmrLicense) (This)->lpVtbl->RenewLicense(This,ulInXmrLicenseLen,pbInXmrLicense,ulEntitlementTokenLen,pbEntitlementToken,pulDescrambleStatus,pulOutXmrLicenseLen,pbOutXmrLicense)
#define IBDA_WMDRMSession_GetKeyInfo(This,pulKeyInfoLen,pbKeyInfo) (This)->lpVtbl->GetKeyInfo(This,pulKeyInfoLen,pbKeyInfo)
#else
/*** IUnknown methods ***/
static inline HRESULT IBDA_WMDRMSession_QueryInterface(IBDA_WMDRMSession* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IBDA_WMDRMSession_AddRef(IBDA_WMDRMSession* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IBDA_WMDRMSession_Release(IBDA_WMDRMSession* This) {
    return This->lpVtbl->Release(This);
}
/*** IBDA_WMDRMSession methods ***/
static inline HRESULT IBDA_WMDRMSession_GetStatus(IBDA_WMDRMSession* This,ULONG *MaxCaptureToken,ULONG *MaxStreamingPid,ULONG *MaxLicense,ULONG *MinSecurityLevel,ULONG *RevInfoSequenceNumber,ULONGLONG *RevInfoIssuedTime,ULONG *RevInfoTTL,ULONG *RevListVersion,ULONG *ulState) {
    return This->lpVtbl->GetStatus(This,MaxCaptureToken,MaxStreamingPid,MaxLicense,MinSecurityLevel,RevInfoSequenceNumber,RevInfoIssuedTime,RevInfoTTL,RevListVersion,ulState);
}
static inline HRESULT IBDA_WMDRMSession_SetRevInfo(IBDA_WMDRMSession* This,ULONG ulRevInfoLen,BYTE *pbRevInfo) {
    return This->lpVtbl->SetRevInfo(This,ulRevInfoLen,pbRevInfo);
}
static inline HRESULT IBDA_WMDRMSession_SetCrl(IBDA_WMDRMSession* This,ULONG ulCrlLen,BYTE *pbCrlLen) {
    return This->lpVtbl->SetCrl(This,ulCrlLen,pbCrlLen);
}
static inline HRESULT IBDA_WMDRMSession_TransactMessage(IBDA_WMDRMSession* This,ULONG ulcbRequest,BYTE *pbRequest,ULONG *pulcbResponse,BYTE *pbResponse) {
    return This->lpVtbl->TransactMessage(This,ulcbRequest,pbRequest,pulcbResponse,pbResponse);
}
static inline HRESULT IBDA_WMDRMSession_GetLicense(IBDA_WMDRMSession* This,GUID *uuidKey,ULONG *pulPackageLen,BYTE *pbPackage) {
    return This->lpVtbl->GetLicense(This,uuidKey,pulPackageLen,pbPackage);
}
static inline HRESULT IBDA_WMDRMSession_ReissueLicense(IBDA_WMDRMSession* This,GUID *uuidKey) {
    return This->lpVtbl->ReissueLicense(This,uuidKey);
}
static inline HRESULT IBDA_WMDRMSession_RenewLicense(IBDA_WMDRMSession* This,ULONG ulInXmrLicenseLen,BYTE *pbInXmrLicense,ULONG ulEntitlementTokenLen,BYTE *pbEntitlementToken,ULONG *pulDescrambleStatus,ULONG *pulOutXmrLicenseLen,BYTE *pbOutXmrLicense) {
    return This->lpVtbl->RenewLicense(This,ulInXmrLicenseLen,pbInXmrLicense,ulEntitlementTokenLen,pbEntitlementToken,pulDescrambleStatus,pulOutXmrLicenseLen,pbOutXmrLicense);
}
static inline HRESULT IBDA_WMDRMSession_GetKeyInfo(IBDA_WMDRMSession* This,ULONG *pulKeyInfoLen,BYTE *pbKeyInfo) {
    return This->lpVtbl->GetKeyInfo(This,pulKeyInfoLen,pbKeyInfo);
}
#endif
#endif

#endif


#endif  /* __IBDA_WMDRMSession_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IBDA_WMDRMTuner interface
 */
#ifndef __IBDA_WMDRMTuner_INTERFACE_DEFINED__
#define __IBDA_WMDRMTuner_INTERFACE_DEFINED__

DEFINE_GUID(IID_IBDA_WMDRMTuner, 0x86d979cf, 0xa8a7, 0x4f94, 0xb5,0xfb, 0x14,0xc0,0xac,0xa6,0x8f,0xe6);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("86d979cf-a8a7-4f94-b5fb-14c0aca68fe6")
IBDA_WMDRMTuner : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE PurchaseEntitlement(
        ULONG ulDialogRequest,
        BSTR bstrLanguage,
        ULONG ulPurchaseTokenLen,
        BYTE *pbPurchaseToken,
        ULONG *pulDescrambleStatus,
        ULONG *pulCaptureTokenLen,
        BYTE *pbCaptureToken) = 0;

    virtual HRESULT STDMETHODCALLTYPE CancelCaptureToken(
        ULONG ulCaptureTokenLen,
        BYTE *pbCaptureToken) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetPidProtection(
        ULONG ulPid,
        GUID *uuidKey) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPidProtection(
        ULONG pulPid,
        GUID *uuidKey) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSyncValue(
        ULONG ulSyncValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStartCodeProfile(
        ULONG *pulStartCodeProfileLen,
        BYTE *pbStartCodeProfile) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IBDA_WMDRMTuner, 0x86d979cf, 0xa8a7, 0x4f94, 0xb5,0xfb, 0x14,0xc0,0xac,0xa6,0x8f,0xe6)
#endif
#else
typedef struct IBDA_WMDRMTunerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IBDA_WMDRMTuner *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IBDA_WMDRMTuner *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IBDA_WMDRMTuner *This);

    /*** IBDA_WMDRMTuner methods ***/
    HRESULT (STDMETHODCALLTYPE *PurchaseEntitlement)(
        IBDA_WMDRMTuner *This,
        ULONG ulDialogRequest,
        BSTR bstrLanguage,
        ULONG ulPurchaseTokenLen,
        BYTE *pbPurchaseToken,
        ULONG *pulDescrambleStatus,
        ULONG *pulCaptureTokenLen,
        BYTE *pbCaptureToken);

    HRESULT (STDMETHODCALLTYPE *CancelCaptureToken)(
        IBDA_WMDRMTuner *This,
        ULONG ulCaptureTokenLen,
        BYTE *pbCaptureToken);

    HRESULT (STDMETHODCALLTYPE *SetPidProtection)(
        IBDA_WMDRMTuner *This,
        ULONG ulPid,
        GUID *uuidKey);

    HRESULT (STDMETHODCALLTYPE *GetPidProtection)(
        IBDA_WMDRMTuner *This,
        ULONG pulPid,
        GUID *uuidKey);

    HRESULT (STDMETHODCALLTYPE *SetSyncValue)(
        IBDA_WMDRMTuner *This,
        ULONG ulSyncValue);

    HRESULT (STDMETHODCALLTYPE *GetStartCodeProfile)(
        IBDA_WMDRMTuner *This,
        ULONG *pulStartCodeProfileLen,
        BYTE *pbStartCodeProfile);

    END_INTERFACE
} IBDA_WMDRMTunerVtbl;

interface IBDA_WMDRMTuner {
    CONST_VTBL IBDA_WMDRMTunerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IBDA_WMDRMTuner_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IBDA_WMDRMTuner_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IBDA_WMDRMTuner_Release(This) (This)->lpVtbl->Release(This)
/*** IBDA_WMDRMTuner methods ***/
#define IBDA_WMDRMTuner_PurchaseEntitlement(This,ulDialogRequest,bstrLanguage,ulPurchaseTokenLen,pbPurchaseToken,pulDescrambleStatus,pulCaptureTokenLen,pbCaptureToken) (This)->lpVtbl->PurchaseEntitlement(This,ulDialogRequest,bstrLanguage,ulPurchaseTokenLen,pbPurchaseToken,pulDescrambleStatus,pulCaptureTokenLen,pbCaptureToken)
#define IBDA_WMDRMTuner_CancelCaptureToken(This,ulCaptureTokenLen,pbCaptureToken) (This)->lpVtbl->CancelCaptureToken(This,ulCaptureTokenLen,pbCaptureToken)
#define IBDA_WMDRMTuner_SetPidProtection(This,ulPid,uuidKey) (This)->lpVtbl->SetPidProtection(This,ulPid,uuidKey)
#define IBDA_WMDRMTuner_GetPidProtection(This,pulPid,uuidKey) (This)->lpVtbl->GetPidProtection(This,pulPid,uuidKey)
#define IBDA_WMDRMTuner_SetSyncValue(This,ulSyncValue) (This)->lpVtbl->SetSyncValue(This,ulSyncValue)
#define IBDA_WMDRMTuner_GetStartCodeProfile(This,pulStartCodeProfileLen,pbStartCodeProfile) (This)->lpVtbl->GetStartCodeProfile(This,pulStartCodeProfileLen,pbStartCodeProfile)
#else
/*** IUnknown methods ***/
static inline HRESULT IBDA_WMDRMTuner_QueryInterface(IBDA_WMDRMTuner* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IBDA_WMDRMTuner_AddRef(IBDA_WMDRMTuner* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IBDA_WMDRMTuner_Release(IBDA_WMDRMTuner* This) {
    return This->lpVtbl->Release(This);
}
/*** IBDA_WMDRMTuner methods ***/
static inline HRESULT IBDA_WMDRMTuner_PurchaseEntitlement(IBDA_WMDRMTuner* This,ULONG ulDialogRequest,BSTR bstrLanguage,ULONG ulPurchaseTokenLen,BYTE *pbPurchaseToken,ULONG *pulDescrambleStatus,ULONG *pulCaptureTokenLen,BYTE *pbCaptureToken) {
    return This->lpVtbl->PurchaseEntitlement(This,ulDialogRequest,bstrLanguage,ulPurchaseTokenLen,pbPurchaseToken,pulDescrambleStatus,pulCaptureTokenLen,pbCaptureToken);
}
static inline HRESULT IBDA_WMDRMTuner_CancelCaptureToken(IBDA_WMDRMTuner* This,ULONG ulCaptureTokenLen,BYTE *pbCaptureToken) {
    return This->lpVtbl->CancelCaptureToken(This,ulCaptureTokenLen,pbCaptureToken);
}
static inline HRESULT IBDA_WMDRMTuner_SetPidProtection(IBDA_WMDRMTuner* This,ULONG ulPid,GUID *uuidKey) {
    return This->lpVtbl->SetPidProtection(This,ulPid,uuidKey);
}
static inline HRESULT IBDA_WMDRMTuner_GetPidProtection(IBDA_WMDRMTuner* This,ULONG pulPid,GUID *uuidKey) {
    return This->lpVtbl->GetPidProtection(This,pulPid,uuidKey);
}
static inline HRESULT IBDA_WMDRMTuner_SetSyncValue(IBDA_WMDRMTuner* This,ULONG ulSyncValue) {
    return This->lpVtbl->SetSyncValue(This,ulSyncValue);
}
static inline HRESULT IBDA_WMDRMTuner_GetStartCodeProfile(IBDA_WMDRMTuner* This,ULONG *pulStartCodeProfileLen,BYTE *pbStartCodeProfile) {
    return This->lpVtbl->GetStartCodeProfile(This,pulStartCodeProfileLen,pbStartCodeProfile);
}
#endif
#endif

#endif


#endif  /* __IBDA_WMDRMTuner_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IBDA_DRIDRMService interface
 */
#ifndef __IBDA_DRIDRMService_INTERFACE_DEFINED__
#define __IBDA_DRIDRMService_INTERFACE_DEFINED__

DEFINE_GUID(IID_IBDA_DRIDRMService, 0x1f9bc2a5, 0x44a3, 0x4c52, 0xaa,0xb1, 0x0b,0xbc,0xe5,0xa1,0x38,0x1d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("1f9bc2a5-44a3-4c52-aab1-0bbce5a1381d")
IBDA_DRIDRMService : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetDRM(
        BSTR bstrNewDrm) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDRMStatus(
        BSTR *pbstrDrmUuidList,
        GUID *DrmUuid) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPairingStatus(
        BDA_DrmPairingError *penumPairingStatus) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IBDA_DRIDRMService, 0x1f9bc2a5, 0x44a3, 0x4c52, 0xaa,0xb1, 0x0b,0xbc,0xe5,0xa1,0x38,0x1d)
#endif
#else
typedef struct IBDA_DRIDRMServiceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IBDA_DRIDRMService *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IBDA_DRIDRMService *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IBDA_DRIDRMService *This);

    /*** IBDA_DRIDRMService methods ***/
    HRESULT (STDMETHODCALLTYPE *SetDRM)(
        IBDA_DRIDRMService *This,
        BSTR bstrNewDrm);

    HRESULT (STDMETHODCALLTYPE *GetDRMStatus)(
        IBDA_DRIDRMService *This,
        BSTR *pbstrDrmUuidList,
        GUID *DrmUuid);

    HRESULT (STDMETHODCALLTYPE *GetPairingStatus)(
        IBDA_DRIDRMService *This,
        BDA_DrmPairingError *penumPairingStatus);

    END_INTERFACE
} IBDA_DRIDRMServiceVtbl;

interface IBDA_DRIDRMService {
    CONST_VTBL IBDA_DRIDRMServiceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IBDA_DRIDRMService_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IBDA_DRIDRMService_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IBDA_DRIDRMService_Release(This) (This)->lpVtbl->Release(This)
/*** IBDA_DRIDRMService methods ***/
#define IBDA_DRIDRMService_SetDRM(This,bstrNewDrm) (This)->lpVtbl->SetDRM(This,bstrNewDrm)
#define IBDA_DRIDRMService_GetDRMStatus(This,pbstrDrmUuidList,DrmUuid) (This)->lpVtbl->GetDRMStatus(This,pbstrDrmUuidList,DrmUuid)
#define IBDA_DRIDRMService_GetPairingStatus(This,penumPairingStatus) (This)->lpVtbl->GetPairingStatus(This,penumPairingStatus)
#else
/*** IUnknown methods ***/
static inline HRESULT IBDA_DRIDRMService_QueryInterface(IBDA_DRIDRMService* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IBDA_DRIDRMService_AddRef(IBDA_DRIDRMService* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IBDA_DRIDRMService_Release(IBDA_DRIDRMService* This) {
    return This->lpVtbl->Release(This);
}
/*** IBDA_DRIDRMService methods ***/
static inline HRESULT IBDA_DRIDRMService_SetDRM(IBDA_DRIDRMService* This,BSTR bstrNewDrm) {
    return This->lpVtbl->SetDRM(This,bstrNewDrm);
}
static inline HRESULT IBDA_DRIDRMService_GetDRMStatus(IBDA_DRIDRMService* This,BSTR *pbstrDrmUuidList,GUID *DrmUuid) {
    return This->lpVtbl->GetDRMStatus(This,pbstrDrmUuidList,DrmUuid);
}
static inline HRESULT IBDA_DRIDRMService_GetPairingStatus(IBDA_DRIDRMService* This,BDA_DrmPairingError *penumPairingStatus) {
    return This->lpVtbl->GetPairingStatus(This,penumPairingStatus);
}
#endif
#endif

#endif


#endif  /* __IBDA_DRIDRMService_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IBDA_DRIWMDRMSession interface
 */
#ifndef __IBDA_DRIWMDRMSession_INTERFACE_DEFINED__
#define __IBDA_DRIWMDRMSession_INTERFACE_DEFINED__

DEFINE_GUID(IID_IBDA_DRIWMDRMSession, 0x05c690f8, 0x56db, 0x4bb2, 0xb0,0x53, 0x79,0xc1,0x20,0x98,0xbb,0x26);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("05c690f8-56db-4bb2-b053-79c12098bb26")
IBDA_DRIWMDRMSession : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE AcknowledgeLicense(
        HRESULT hrLicenseAck) = 0;

    virtual HRESULT STDMETHODCALLTYPE ProcessLicenseChallenge(
        DWORD dwcbLicenseMessage,
        BYTE *pbLicenseMessage,
        DWORD *pdwcbLicenseResponse,
        BYTE **ppbLicenseResponse) = 0;

    virtual HRESULT STDMETHODCALLTYPE ProcessRegistrationChallenge(
        DWORD dwcbRegistrationMessage,
        BYTE *pbRegistrationMessage,
        DWORD *pdwcbRegistrationResponse,
        BYTE **ppbRegistrationResponse) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetRevInfo(
        DWORD dwRevInfoLen,
        BYTE *pbRevInfo,
        DWORD *pdwResponse) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetCrl(
        DWORD dwCrlLen,
        BYTE *pbCrlLen,
        DWORD *pdwResponse) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetHMSAssociationData(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetLastCardeaError(
        DWORD *pdwError) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IBDA_DRIWMDRMSession, 0x05c690f8, 0x56db, 0x4bb2, 0xb0,0x53, 0x79,0xc1,0x20,0x98,0xbb,0x26)
#endif
#else
typedef struct IBDA_DRIWMDRMSessionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IBDA_DRIWMDRMSession *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IBDA_DRIWMDRMSession *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IBDA_DRIWMDRMSession *This);

    /*** IBDA_DRIWMDRMSession methods ***/
    HRESULT (STDMETHODCALLTYPE *AcknowledgeLicense)(
        IBDA_DRIWMDRMSession *This,
        HRESULT hrLicenseAck);

    HRESULT (STDMETHODCALLTYPE *ProcessLicenseChallenge)(
        IBDA_DRIWMDRMSession *This,
        DWORD dwcbLicenseMessage,
        BYTE *pbLicenseMessage,
        DWORD *pdwcbLicenseResponse,
        BYTE **ppbLicenseResponse);

    HRESULT (STDMETHODCALLTYPE *ProcessRegistrationChallenge)(
        IBDA_DRIWMDRMSession *This,
        DWORD dwcbRegistrationMessage,
        BYTE *pbRegistrationMessage,
        DWORD *pdwcbRegistrationResponse,
        BYTE **ppbRegistrationResponse);

    HRESULT (STDMETHODCALLTYPE *SetRevInfo)(
        IBDA_DRIWMDRMSession *This,
        DWORD dwRevInfoLen,
        BYTE *pbRevInfo,
        DWORD *pdwResponse);

    HRESULT (STDMETHODCALLTYPE *SetCrl)(
        IBDA_DRIWMDRMSession *This,
        DWORD dwCrlLen,
        BYTE *pbCrlLen,
        DWORD *pdwResponse);

    HRESULT (STDMETHODCALLTYPE *GetHMSAssociationData)(
        IBDA_DRIWMDRMSession *This);

    HRESULT (STDMETHODCALLTYPE *GetLastCardeaError)(
        IBDA_DRIWMDRMSession *This,
        DWORD *pdwError);

    END_INTERFACE
} IBDA_DRIWMDRMSessionVtbl;

interface IBDA_DRIWMDRMSession {
    CONST_VTBL IBDA_DRIWMDRMSessionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IBDA_DRIWMDRMSession_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IBDA_DRIWMDRMSession_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IBDA_DRIWMDRMSession_Release(This) (This)->lpVtbl->Release(This)
/*** IBDA_DRIWMDRMSession methods ***/
#define IBDA_DRIWMDRMSession_AcknowledgeLicense(This,hrLicenseAck) (This)->lpVtbl->AcknowledgeLicense(This,hrLicenseAck)
#define IBDA_DRIWMDRMSession_ProcessLicenseChallenge(This,dwcbLicenseMessage,pbLicenseMessage,pdwcbLicenseResponse,ppbLicenseResponse) (This)->lpVtbl->ProcessLicenseChallenge(This,dwcbLicenseMessage,pbLicenseMessage,pdwcbLicenseResponse,ppbLicenseResponse)
#define IBDA_DRIWMDRMSession_ProcessRegistrationChallenge(This,dwcbRegistrationMessage,pbRegistrationMessage,pdwcbRegistrationResponse,ppbRegistrationResponse) (This)->lpVtbl->ProcessRegistrationChallenge(This,dwcbRegistrationMessage,pbRegistrationMessage,pdwcbRegistrationResponse,ppbRegistrationResponse)
#define IBDA_DRIWMDRMSession_SetRevInfo(This,dwRevInfoLen,pbRevInfo,pdwResponse) (This)->lpVtbl->SetRevInfo(This,dwRevInfoLen,pbRevInfo,pdwResponse)
#define IBDA_DRIWMDRMSession_SetCrl(This,dwCrlLen,pbCrlLen,pdwResponse) (This)->lpVtbl->SetCrl(This,dwCrlLen,pbCrlLen,pdwResponse)
#define IBDA_DRIWMDRMSession_GetHMSAssociationData(This) (This)->lpVtbl->GetHMSAssociationData(This)
#define IBDA_DRIWMDRMSession_GetLastCardeaError(This,pdwError) (This)->lpVtbl->GetLastCardeaError(This,pdwError)
#else
/*** IUnknown methods ***/
static inline HRESULT IBDA_DRIWMDRMSession_QueryInterface(IBDA_DRIWMDRMSession* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IBDA_DRIWMDRMSession_AddRef(IBDA_DRIWMDRMSession* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IBDA_DRIWMDRMSession_Release(IBDA_DRIWMDRMSession* This) {
    return This->lpVtbl->Release(This);
}
/*** IBDA_DRIWMDRMSession methods ***/
static inline HRESULT IBDA_DRIWMDRMSession_AcknowledgeLicense(IBDA_DRIWMDRMSession* This,HRESULT hrLicenseAck) {
    return This->lpVtbl->AcknowledgeLicense(This,hrLicenseAck);
}
static inline HRESULT IBDA_DRIWMDRMSession_ProcessLicenseChallenge(IBDA_DRIWMDRMSession* This,DWORD dwcbLicenseMessage,BYTE *pbLicenseMessage,DWORD *pdwcbLicenseResponse,BYTE **ppbLicenseResponse) {
    return This->lpVtbl->ProcessLicenseChallenge(This,dwcbLicenseMessage,pbLicenseMessage,pdwcbLicenseResponse,ppbLicenseResponse);
}
static inline HRESULT IBDA_DRIWMDRMSession_ProcessRegistrationChallenge(IBDA_DRIWMDRMSession* This,DWORD dwcbRegistrationMessage,BYTE *pbRegistrationMessage,DWORD *pdwcbRegistrationResponse,BYTE **ppbRegistrationResponse) {
    return This->lpVtbl->ProcessRegistrationChallenge(This,dwcbRegistrationMessage,pbRegistrationMessage,pdwcbRegistrationResponse,ppbRegistrationResponse);
}
static inline HRESULT IBDA_DRIWMDRMSession_SetRevInfo(IBDA_DRIWMDRMSession* This,DWORD dwRevInfoLen,BYTE *pbRevInfo,DWORD *pdwResponse) {
    return This->lpVtbl->SetRevInfo(This,dwRevInfoLen,pbRevInfo,pdwResponse);
}
static inline HRESULT IBDA_DRIWMDRMSession_SetCrl(IBDA_DRIWMDRMSession* This,DWORD dwCrlLen,BYTE *pbCrlLen,DWORD *pdwResponse) {
    return This->lpVtbl->SetCrl(This,dwCrlLen,pbCrlLen,pdwResponse);
}
static inline HRESULT IBDA_DRIWMDRMSession_GetHMSAssociationData(IBDA_DRIWMDRMSession* This) {
    return This->lpVtbl->GetHMSAssociationData(This);
}
static inline HRESULT IBDA_DRIWMDRMSession_GetLastCardeaError(IBDA_DRIWMDRMSession* This,DWORD *pdwError) {
    return This->lpVtbl->GetLastCardeaError(This,pdwError);
}
#endif
#endif

#endif


#endif  /* __IBDA_DRIWMDRMSession_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IBDA_MUX interface
 */
#ifndef __IBDA_MUX_INTERFACE_DEFINED__
#define __IBDA_MUX_INTERFACE_DEFINED__

DEFINE_GUID(IID_IBDA_MUX, 0x942aafec, 0x4c05, 0x4c74, 0xb8,0xeb, 0x87,0x06,0xc2,0xa4,0x94,0x3f);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("942aafec-4c05-4c74-b8eb-8706c2a4943f")
IBDA_MUX : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetPidList(
        ULONG ulPidListCount,
        BDA_MUX_PIDLISTITEM *pbPidListBuffer) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPidList(
        ULONG *pulPidListCount,
        BDA_MUX_PIDLISTITEM *pbPidListBuffer) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IBDA_MUX, 0x942aafec, 0x4c05, 0x4c74, 0xb8,0xeb, 0x87,0x06,0xc2,0xa4,0x94,0x3f)
#endif
#else
typedef struct IBDA_MUXVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IBDA_MUX *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IBDA_MUX *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IBDA_MUX *This);

    /*** IBDA_MUX methods ***/
    HRESULT (STDMETHODCALLTYPE *SetPidList)(
        IBDA_MUX *This,
        ULONG ulPidListCount,
        BDA_MUX_PIDLISTITEM *pbPidListBuffer);

    HRESULT (STDMETHODCALLTYPE *GetPidList)(
        IBDA_MUX *This,
        ULONG *pulPidListCount,
        BDA_MUX_PIDLISTITEM *pbPidListBuffer);

    END_INTERFACE
} IBDA_MUXVtbl;

interface IBDA_MUX {
    CONST_VTBL IBDA_MUXVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IBDA_MUX_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IBDA_MUX_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IBDA_MUX_Release(This) (This)->lpVtbl->Release(This)
/*** IBDA_MUX methods ***/
#define IBDA_MUX_SetPidList(This,ulPidListCount,pbPidListBuffer) (This)->lpVtbl->SetPidList(This,ulPidListCount,pbPidListBuffer)
#define IBDA_MUX_GetPidList(This,pulPidListCount,pbPidListBuffer) (This)->lpVtbl->GetPidList(This,pulPidListCount,pbPidListBuffer)
#else
/*** IUnknown methods ***/
static inline HRESULT IBDA_MUX_QueryInterface(IBDA_MUX* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IBDA_MUX_AddRef(IBDA_MUX* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IBDA_MUX_Release(IBDA_MUX* This) {
    return This->lpVtbl->Release(This);
}
/*** IBDA_MUX methods ***/
static inline HRESULT IBDA_MUX_SetPidList(IBDA_MUX* This,ULONG ulPidListCount,BDA_MUX_PIDLISTITEM *pbPidListBuffer) {
    return This->lpVtbl->SetPidList(This,ulPidListCount,pbPidListBuffer);
}
static inline HRESULT IBDA_MUX_GetPidList(IBDA_MUX* This,ULONG *pulPidListCount,BDA_MUX_PIDLISTITEM *pbPidListBuffer) {
    return This->lpVtbl->GetPidList(This,pulPidListCount,pbPidListBuffer);
}
#endif
#endif

#endif


#endif  /* __IBDA_MUX_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IBDA_TransportStreamSelector interface
 */
#ifndef __IBDA_TransportStreamSelector_INTERFACE_DEFINED__
#define __IBDA_TransportStreamSelector_INTERFACE_DEFINED__

DEFINE_GUID(IID_IBDA_TransportStreamSelector, 0x1dcfafe9, 0xb45e, 0x41b3, 0xbb,0x2a, 0x56,0x1e,0xb1,0x29,0xae,0x98);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("1dcfafe9-b45e-41b3-bb2a-561eb129ae98")
IBDA_TransportStreamSelector : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetTSID(
        USHORT usTSID) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTSInformation(
        ULONG *pulTSInformationBufferLen,
        BYTE *pbTSInformationBuffer) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IBDA_TransportStreamSelector, 0x1dcfafe9, 0xb45e, 0x41b3, 0xbb,0x2a, 0x56,0x1e,0xb1,0x29,0xae,0x98)
#endif
#else
typedef struct IBDA_TransportStreamSelectorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IBDA_TransportStreamSelector *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IBDA_TransportStreamSelector *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IBDA_TransportStreamSelector *This);

    /*** IBDA_TransportStreamSelector methods ***/
    HRESULT (STDMETHODCALLTYPE *SetTSID)(
        IBDA_TransportStreamSelector *This,
        USHORT usTSID);

    HRESULT (STDMETHODCALLTYPE *GetTSInformation)(
        IBDA_TransportStreamSelector *This,
        ULONG *pulTSInformationBufferLen,
        BYTE *pbTSInformationBuffer);

    END_INTERFACE
} IBDA_TransportStreamSelectorVtbl;

interface IBDA_TransportStreamSelector {
    CONST_VTBL IBDA_TransportStreamSelectorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IBDA_TransportStreamSelector_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IBDA_TransportStreamSelector_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IBDA_TransportStreamSelector_Release(This) (This)->lpVtbl->Release(This)
/*** IBDA_TransportStreamSelector methods ***/
#define IBDA_TransportStreamSelector_SetTSID(This,usTSID) (This)->lpVtbl->SetTSID(This,usTSID)
#define IBDA_TransportStreamSelector_GetTSInformation(This,pulTSInformationBufferLen,pbTSInformationBuffer) (This)->lpVtbl->GetTSInformation(This,pulTSInformationBufferLen,pbTSInformationBuffer)
#else
/*** IUnknown methods ***/
static inline HRESULT IBDA_TransportStreamSelector_QueryInterface(IBDA_TransportStreamSelector* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IBDA_TransportStreamSelector_AddRef(IBDA_TransportStreamSelector* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IBDA_TransportStreamSelector_Release(IBDA_TransportStreamSelector* This) {
    return This->lpVtbl->Release(This);
}
/*** IBDA_TransportStreamSelector methods ***/
static inline HRESULT IBDA_TransportStreamSelector_SetTSID(IBDA_TransportStreamSelector* This,USHORT usTSID) {
    return This->lpVtbl->SetTSID(This,usTSID);
}
static inline HRESULT IBDA_TransportStreamSelector_GetTSInformation(IBDA_TransportStreamSelector* This,ULONG *pulTSInformationBufferLen,BYTE *pbTSInformationBuffer) {
    return This->lpVtbl->GetTSInformation(This,pulTSInformationBufferLen,pbTSInformationBuffer);
}
#endif
#endif

#endif


#endif  /* __IBDA_TransportStreamSelector_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IBDA_UserActivityService interface
 */
#ifndef __IBDA_UserActivityService_INTERFACE_DEFINED__
#define __IBDA_UserActivityService_INTERFACE_DEFINED__

DEFINE_GUID(IID_IBDA_UserActivityService, 0x53b14189, 0xe478, 0x4b7a, 0xa1,0xff, 0x50,0x6d,0xb4,0xb9,0x9d,0xfe);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("53b14189-e478-4b7a-a1ff-506db4b99dfe")
IBDA_UserActivityService : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetCurrentTunerUseReason(
        DWORD dwUseReason) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetUserActivityInterval(
        DWORD *pdwActivityInterval) = 0;

    virtual HRESULT STDMETHODCALLTYPE UserActivityDetected(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IBDA_UserActivityService, 0x53b14189, 0xe478, 0x4b7a, 0xa1,0xff, 0x50,0x6d,0xb4,0xb9,0x9d,0xfe)
#endif
#else
typedef struct IBDA_UserActivityServiceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IBDA_UserActivityService *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IBDA_UserActivityService *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IBDA_UserActivityService *This);

    /*** IBDA_UserActivityService methods ***/
    HRESULT (STDMETHODCALLTYPE *SetCurrentTunerUseReason)(
        IBDA_UserActivityService *This,
        DWORD dwUseReason);

    HRESULT (STDMETHODCALLTYPE *GetUserActivityInterval)(
        IBDA_UserActivityService *This,
        DWORD *pdwActivityInterval);

    HRESULT (STDMETHODCALLTYPE *UserActivityDetected)(
        IBDA_UserActivityService *This);

    END_INTERFACE
} IBDA_UserActivityServiceVtbl;

interface IBDA_UserActivityService {
    CONST_VTBL IBDA_UserActivityServiceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IBDA_UserActivityService_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IBDA_UserActivityService_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IBDA_UserActivityService_Release(This) (This)->lpVtbl->Release(This)
/*** IBDA_UserActivityService methods ***/
#define IBDA_UserActivityService_SetCurrentTunerUseReason(This,dwUseReason) (This)->lpVtbl->SetCurrentTunerUseReason(This,dwUseReason)
#define IBDA_UserActivityService_GetUserActivityInterval(This,pdwActivityInterval) (This)->lpVtbl->GetUserActivityInterval(This,pdwActivityInterval)
#define IBDA_UserActivityService_UserActivityDetected(This) (This)->lpVtbl->UserActivityDetected(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IBDA_UserActivityService_QueryInterface(IBDA_UserActivityService* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IBDA_UserActivityService_AddRef(IBDA_UserActivityService* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IBDA_UserActivityService_Release(IBDA_UserActivityService* This) {
    return This->lpVtbl->Release(This);
}
/*** IBDA_UserActivityService methods ***/
static inline HRESULT IBDA_UserActivityService_SetCurrentTunerUseReason(IBDA_UserActivityService* This,DWORD dwUseReason) {
    return This->lpVtbl->SetCurrentTunerUseReason(This,dwUseReason);
}
static inline HRESULT IBDA_UserActivityService_GetUserActivityInterval(IBDA_UserActivityService* This,DWORD *pdwActivityInterval) {
    return This->lpVtbl->GetUserActivityInterval(This,pdwActivityInterval);
}
static inline HRESULT IBDA_UserActivityService_UserActivityDetected(IBDA_UserActivityService* This) {
    return This->lpVtbl->UserActivityDetected(This);
}
#endif
#endif

#endif


#endif  /* __IBDA_UserActivityService_INTERFACE_DEFINED__ */

#define SID_BDA_EasMessage __uuidof (IBDA_EasMessage)
#define SID_BDA_TransportStreamInfo __uuidof (IBDA_TransportStreamInfo)
#define SID_BDA_ConditionalAccess __uuidof (IBDA_ConditionalAccess)
#define SID_BDA_DiagnosticProperties __uuidof (IBDA_DiagnosticProperties)
#define SID_BDA_DRM __uuidof (IBDA_DRM)
#define SID_BDA_NameValueService __uuidof (IBDA_NameValueService)
#define SID_BDA_ConditionalAccessEx __uuidof (IBDA_ConditionalAccessEx)
#define SID_BDA_ISDBConditionalAccess __uuidof (IBDA_ISDBConditionalAccess)
#define SID_BDA_EventingService __uuidof (IBDA_EventingService)
#define SID_BDA_AUX __uuidof (IBDA_AUX)
#define SID_BDA_Encoder __uuidof (IBDA_Encoder)
#define SID_BDA_FDC __uuidof (IBDA_FDC
#define SID_BDA_GuideDataDeliveryService  __uuidof (IBDA_GuideDataDeliveryService)
#define SID_BDA_DRMService __uuidof (IBDA_DRMService)
#define SID_BDA_WMDRMSession __uuidof (IBDA_WMDRMSession)
#define SID_BDA_WMDRMTuner __uuidof (IBDA_WMDRMTuner)
#define SID_BDA_DRIDRMService __uuidof (IBDA_DRIDRMService)
#define SID_BDA_DRIWMDRMSession __uuidof (IBDA_DRIWMDRMSession)
#define SID_BDA_MUX __uuidof (IBDA_MUX)
#define SID_BDA_TransportStreamSelector __uuidof (IBDA_TransportStreamSelector)
#define SID_BDA_UserActivityService __uuidof (IBDA_UserActivityService)

DEFINE_GUID(CLSID_PBDA_Encoder_DATA_TYPE, 0x728fd6bc, 0x5546, 0x4716, 0xb1, 0x03, 0xf8, 0x99, 0xf5, 0xa1, 0xfa, 0x68);
DEFINE_GUID(CLSID_PBDA_FDC_DATA_TYPE, 0xe7dbf9a0, 0x22ab, 0x4047, 0x8e, 0x67, 0xef, 0x9a, 0xd5, 0x4, 0xe7, 0x29);
DEFINE_GUID(CLSID_PBDA_GDDS_DATA_TYPE, 0xC80C0DF3, 0x6052, 0x4c16, 0x9F, 0x56, 0xC4, 0x4C, 0x21, 0xF7, 0x3C, 0x45);
DEFINE_GUID(PBDA_AUX_CONNECTOR_TYPE_SVideo, 0xa0e905f4,0x24c9,0x4a54, 0xb7, 0x61, 0x21, 0x33, 0x55, 0xef, 0xc1, 0x3A);
DEFINE_GUID(PBDA_AUX_CONNECTOR_TYPE_Composite, 0xf6298b4c,0xc725,0x4d42, 0x84, 0x9b, 0x41, 0x0b, 0xbb, 0x14, 0xea, 0x62);
DEFINE_GUID(CLSID_PBDA_AUX_DATA_TYPE, 0xfd456373, 0x3323, 0x4090, 0xad, 0xca, 0x8e, 0xd4, 0x5f, 0x55, 0xcf, 0x10);
#endif
/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER BSTR_UserSize     (ULONG *, ULONG, BSTR *);
unsigned char * __RPC_USER BSTR_UserMarshal  (ULONG *, unsigned char *, BSTR *);
unsigned char * __RPC_USER BSTR_UserUnmarshal(ULONG *, unsigned char *, BSTR *);
void            __RPC_USER BSTR_UserFree     (ULONG *, BSTR *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __bdaiface_h__ */
