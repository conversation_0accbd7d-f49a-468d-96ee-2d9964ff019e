.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crt_set_subject_alt_othername" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crt_set_subject_alt_othername \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crt_set_subject_alt_othername(gnutls_x509_crt_t " crt ", const char * " oid ", const void * " data ", unsigned int " data_size ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "gnutls_x509_crt_t crt" 12
a certificate of type \fBgnutls_x509_crt_t\fP
.IP "const char * oid" 12
The other name OID
.IP "const void * data" 12
The data to be set
.IP "unsigned int data_size" 12
The size of data to be set
.IP "unsigned int flags" 12
GNUTLS_FSAN_SET to clear previous data or GNUTLS_FSAN_APPEND to append. 
.SH "DESCRIPTION"
This function will set an "othername" to the subject alternative name certificate
extension.

The values set are set as binary values and are expected to have the proper DER encoding.
For convenience the flags \fBGNUTLS_FSAN_ENCODE_OCTET_STRING\fP and \fBGNUTLS_FSAN_ENCODE_UTF8_STRING\fP
can be used to encode the provided data.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "SINCE"
3.5.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
