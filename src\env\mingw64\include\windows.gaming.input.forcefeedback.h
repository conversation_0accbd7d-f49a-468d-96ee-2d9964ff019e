/*** Autogenerated by WIDL 10.8 from include/windows.gaming.input.forcefeedback.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __windows_gaming_input_forcefeedback_h__
#define __windows_gaming_input_forcefeedback_h__

/* Forward declarations */

#ifndef ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect ABI::Windows::Gaming::Input::ForceFeedback::IForceFeedbackEffect
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace ForceFeedback {
                    interface IForceFeedbackEffect;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor ABI::Windows::Gaming::Input::ForceFeedback::IForceFeedbackMotor
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace ForceFeedback {
                    interface IForceFeedbackMotor;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffect_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffect_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffect __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffect;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffect ABI::Windows::Gaming::Input::ForceFeedback::IPeriodicForceEffect
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace ForceFeedback {
                    interface IPeriodicForceEffect;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffectFactory_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffectFactory_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffectFactory __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffectFactory;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffectFactory ABI::Windows::Gaming::Input::ForceFeedback::IPeriodicForceEffectFactory
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace ForceFeedback {
                    interface IPeriodicForceEffectFactory;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffect_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffect_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffect __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffect;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffect ABI::Windows::Gaming::Input::ForceFeedback::IConditionForceEffect
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace ForceFeedback {
                    interface IConditionForceEffect;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffectFactory_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffectFactory_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffectFactory __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffectFactory;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffectFactory ABI::Windows::Gaming::Input::ForceFeedback::IConditionForceEffectFactory
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace ForceFeedback {
                    interface IConditionForceEffectFactory;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConstantForceEffect_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConstantForceEffect_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConstantForceEffect __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConstantForceEffect;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConstantForceEffect ABI::Windows::Gaming::Input::ForceFeedback::IConstantForceEffect
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace ForceFeedback {
                    interface IConstantForceEffect;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIRampForceEffect_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIRampForceEffect_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIRampForceEffect __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIRampForceEffect;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIRampForceEffect ABI::Windows::Gaming::Input::ForceFeedback::IRampForceEffect
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace ForceFeedback {
                    interface IRampForceEffect;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CForceFeedbackMotor_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CForceFeedbackMotor_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace ForceFeedback {
                    class ForceFeedbackMotor;
                }
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CForceFeedbackMotor __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CForceFeedbackMotor;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CForceFeedbackMotor_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CPeriodicForceEffect_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CPeriodicForceEffect_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace ForceFeedback {
                    class PeriodicForceEffect;
                }
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CPeriodicForceEffect __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CPeriodicForceEffect;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CPeriodicForceEffect_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CConditionForceEffect_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CConditionForceEffect_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace ForceFeedback {
                    class ConditionForceEffect;
                }
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CConditionForceEffect __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CConditionForceEffect;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CConditionForceEffect_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CConstantForceEffect_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CConstantForceEffect_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace ForceFeedback {
                    class ConstantForceEffect;
                }
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CConstantForceEffect __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CConstantForceEffect;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CConstantForceEffect_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CRampForceEffect_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CRampForceEffect_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace ForceFeedback {
                    class RampForceEffect;
                }
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CRampForceEffect __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CRampForceEffect;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CRampForceEffect_FWD_DEFINED__ */

#ifndef ____FIAsyncOperationCompletedHandler_1_ForceFeedbackLoadEffectResult_FWD_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_ForceFeedbackLoadEffectResult_FWD_DEFINED__
typedef interface __FIAsyncOperationCompletedHandler_1_ForceFeedbackLoadEffectResult __FIAsyncOperationCompletedHandler_1_ForceFeedbackLoadEffectResult;
#ifdef __cplusplus
#define __FIAsyncOperationCompletedHandler_1_ForceFeedbackLoadEffectResult ABI::Windows::Foundation::IAsyncOperationCompletedHandler<ABI::Windows::Gaming::Input::ForceFeedback::ForceFeedbackLoadEffectResult >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_ForceFeedbackLoadEffectResult_FWD_DEFINED__
#define ____FIAsyncOperation_1_ForceFeedbackLoadEffectResult_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_ForceFeedbackLoadEffectResult __FIAsyncOperation_1_ForceFeedbackLoadEffectResult;
#ifdef __cplusplus
#define __FIAsyncOperation_1_ForceFeedbackLoadEffectResult ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Gaming::Input::ForceFeedback::ForceFeedbackLoadEffectResult >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_FWD_DEFINED__
#define ____FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_FWD_DEFINED__
typedef interface __FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor __FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor;
#ifdef __cplusplus
#define __FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Gaming::Input::ForceFeedback::ForceFeedbackMotor* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterable_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_FWD_DEFINED__
#define ____FIIterable_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_FWD_DEFINED__
typedef interface __FIIterable_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor __FIIterable_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor;
#ifdef __cplusplus
#define __FIIterable_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Gaming::Input::ForceFeedback::ForceFeedbackMotor* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor __FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Gaming::Input::ForceFeedback::ForceFeedbackMotor* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_FWD_DEFINED__
#define ____FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_FWD_DEFINED__
typedef interface __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor;
#ifdef __cplusplus
#define __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor ABI::Windows::Foundation::Collections::IVector<ABI::Windows::Gaming::Input::ForceFeedback::ForceFeedbackMotor* >
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <inspectable.h>
#include <asyncinfo.h>
#include <eventtoken.h>
#include <windowscontracts.h>
#include <windows.foundation.h>
#include <windows.foundation.numerics.h>

#ifdef __cplusplus
extern "C" {
#endif

#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CForceFeedbackEffectAxes __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CForceFeedbackEffectAxes;
#endif /* __cplusplus */

#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CForceFeedbackEffectState __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CForceFeedbackEffectState;
#endif /* __cplusplus */

#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CForceFeedbackLoadEffectResult __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CForceFeedbackLoadEffectResult;
#endif /* __cplusplus */

#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CPeriodicForceEffectKind __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CPeriodicForceEffectKind;
#endif /* __cplusplus */

#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CConditionForceEffectKind __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CConditionForceEffectKind;
#endif /* __cplusplus */

#ifndef ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect ABI::Windows::Gaming::Input::ForceFeedback::IForceFeedbackEffect
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace ForceFeedback {
                    interface IForceFeedbackEffect;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffect_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffect_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffect __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffect;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffect ABI::Windows::Gaming::Input::ForceFeedback::IPeriodicForceEffect
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace ForceFeedback {
                    interface IPeriodicForceEffect;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffectFactory_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffectFactory_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffectFactory __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffectFactory;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffectFactory ABI::Windows::Gaming::Input::ForceFeedback::IPeriodicForceEffectFactory
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace ForceFeedback {
                    interface IPeriodicForceEffectFactory;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffect_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffect_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffect __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffect;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffect ABI::Windows::Gaming::Input::ForceFeedback::IConditionForceEffect
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace ForceFeedback {
                    interface IConditionForceEffect;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffectFactory_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffectFactory_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffectFactory __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffectFactory;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffectFactory ABI::Windows::Gaming::Input::ForceFeedback::IConditionForceEffectFactory
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace ForceFeedback {
                    interface IConditionForceEffectFactory;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConstantForceEffect_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConstantForceEffect_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConstantForceEffect __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConstantForceEffect;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConstantForceEffect ABI::Windows::Gaming::Input::ForceFeedback::IConstantForceEffect
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace ForceFeedback {
                    interface IConstantForceEffect;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIRampForceEffect_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIRampForceEffect_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIRampForceEffect __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIRampForceEffect;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIRampForceEffect ABI::Windows::Gaming::Input::ForceFeedback::IRampForceEffect
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace ForceFeedback {
                    interface IRampForceEffect;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_ForceFeedbackLoadEffectResult_FWD_DEFINED__
#define ____FIAsyncOperation_1_ForceFeedbackLoadEffectResult_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_ForceFeedbackLoadEffectResult __FIAsyncOperation_1_ForceFeedbackLoadEffectResult;
#ifdef __cplusplus
#define __FIAsyncOperation_1_ForceFeedbackLoadEffectResult ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Gaming::Input::ForceFeedback::ForceFeedbackLoadEffectResult >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_FWD_DEFINED__
#define ____FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_FWD_DEFINED__
typedef interface __FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor __FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor;
#ifdef __cplusplus
#define __FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Gaming::Input::ForceFeedback::ForceFeedbackMotor* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterable_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_FWD_DEFINED__
#define ____FIIterable_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_FWD_DEFINED__
typedef interface __FIIterable_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor __FIIterable_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor;
#ifdef __cplusplus
#define __FIIterable_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Gaming::Input::ForceFeedback::ForceFeedbackMotor* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor __FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Gaming::Input::ForceFeedback::ForceFeedbackMotor* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_FWD_DEFINED__
#define ____FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_FWD_DEFINED__
typedef interface __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor;
#ifdef __cplusplus
#define __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor ABI::Windows::Foundation::Collections::IVector<ABI::Windows::Gaming::Input::ForceFeedback::ForceFeedbackMotor* >
#endif /* __cplusplus */
#endif

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace ForceFeedback {
                    enum ForceFeedbackEffectAxes {
                        ForceFeedbackEffectAxes_None = 0x0,
                        ForceFeedbackEffectAxes_X = 0x1,
                        ForceFeedbackEffectAxes_Y = 0x2,
                        ForceFeedbackEffectAxes_Z = 0x4
                    };
                }
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CForceFeedbackEffectAxes {
    ForceFeedbackEffectAxes_None = 0x0,
    ForceFeedbackEffectAxes_X = 0x1,
    ForceFeedbackEffectAxes_Y = 0x2,
    ForceFeedbackEffectAxes_Z = 0x4
};
#ifdef WIDL_using_Windows_Gaming_Input_ForceFeedback
#define ForceFeedbackEffectAxes __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CForceFeedbackEffectAxes
#endif /* WIDL_using_Windows_Gaming_Input_ForceFeedback */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace ForceFeedback {
                    enum ForceFeedbackEffectState {
                        ForceFeedbackEffectState_Stopped = 0,
                        ForceFeedbackEffectState_Running = 1,
                        ForceFeedbackEffectState_Paused = 2,
                        ForceFeedbackEffectState_Faulted = 3
                    };
                }
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CForceFeedbackEffectState {
    ForceFeedbackEffectState_Stopped = 0,
    ForceFeedbackEffectState_Running = 1,
    ForceFeedbackEffectState_Paused = 2,
    ForceFeedbackEffectState_Faulted = 3
};
#ifdef WIDL_using_Windows_Gaming_Input_ForceFeedback
#define ForceFeedbackEffectState __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CForceFeedbackEffectState
#endif /* WIDL_using_Windows_Gaming_Input_ForceFeedback */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace ForceFeedback {
                    enum ForceFeedbackLoadEffectResult {
                        ForceFeedbackLoadEffectResult_Succeeded = 0,
                        ForceFeedbackLoadEffectResult_EffectStorageFull = 1,
                        ForceFeedbackLoadEffectResult_EffectNotSupported = 2
                    };
                }
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CForceFeedbackLoadEffectResult {
    ForceFeedbackLoadEffectResult_Succeeded = 0,
    ForceFeedbackLoadEffectResult_EffectStorageFull = 1,
    ForceFeedbackLoadEffectResult_EffectNotSupported = 2
};
#ifdef WIDL_using_Windows_Gaming_Input_ForceFeedback
#define ForceFeedbackLoadEffectResult __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CForceFeedbackLoadEffectResult
#endif /* WIDL_using_Windows_Gaming_Input_ForceFeedback */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace ForceFeedback {
                    enum PeriodicForceEffectKind {
                        PeriodicForceEffectKind_SquareWave = 0,
                        PeriodicForceEffectKind_SineWave = 1,
                        PeriodicForceEffectKind_TriangleWave = 2,
                        PeriodicForceEffectKind_SawtoothWaveUp = 3,
                        PeriodicForceEffectKind_SawtoothWaveDown = 4
                    };
                }
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CPeriodicForceEffectKind {
    PeriodicForceEffectKind_SquareWave = 0,
    PeriodicForceEffectKind_SineWave = 1,
    PeriodicForceEffectKind_TriangleWave = 2,
    PeriodicForceEffectKind_SawtoothWaveUp = 3,
    PeriodicForceEffectKind_SawtoothWaveDown = 4
};
#ifdef WIDL_using_Windows_Gaming_Input_ForceFeedback
#define PeriodicForceEffectKind __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CPeriodicForceEffectKind
#endif /* WIDL_using_Windows_Gaming_Input_ForceFeedback */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace ForceFeedback {
                    enum ConditionForceEffectKind {
                        ConditionForceEffectKind_Spring = 0,
                        ConditionForceEffectKind_Damper = 1,
                        ConditionForceEffectKind_Inertia = 2,
                        ConditionForceEffectKind_Friction = 3
                    };
                }
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CConditionForceEffectKind {
    ConditionForceEffectKind_Spring = 0,
    ConditionForceEffectKind_Damper = 1,
    ConditionForceEffectKind_Inertia = 2,
    ConditionForceEffectKind_Friction = 3
};
#ifdef WIDL_using_Windows_Gaming_Input_ForceFeedback
#define ConditionForceEffectKind __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CConditionForceEffectKind
#endif /* WIDL_using_Windows_Gaming_Input_ForceFeedback */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */
/*****************************************************************************
 * IForceFeedbackEffect interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
#ifndef ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect, 0xa17fba0c, 0x2ae4, 0x48c2, 0x80,0x63, 0xea,0xbd,0x07,0x77,0xcb,0x89);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace ForceFeedback {
                    MIDL_INTERFACE("a17fba0c-2ae4-48c2-8063-eabd0777cb89")
                    IForceFeedbackEffect : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE get_Gain(
                            DOUBLE *value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE put_Gain(
                            DOUBLE value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_State(
                            ABI::Windows::Gaming::Input::ForceFeedback::ForceFeedbackEffectState *value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE Start(
                            ) = 0;

                        virtual HRESULT STDMETHODCALLTYPE Stop(
                            ) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect, 0xa17fba0c, 0x2ae4, 0x48c2, 0x80,0x63, 0xea,0xbd,0x07,0x77,0xcb,0x89)
#endif
#else
typedef struct __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffectVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect *This,
        TrustLevel *trustLevel);

    /*** IForceFeedbackEffect methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Gain)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect *This,
        DOUBLE *value);

    HRESULT (STDMETHODCALLTYPE *put_Gain)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect *This,
        DOUBLE value);

    HRESULT (STDMETHODCALLTYPE *get_State)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect *This,
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CForceFeedbackEffectState *value);

    HRESULT (STDMETHODCALLTYPE *Start)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect *This);

    HRESULT (STDMETHODCALLTYPE *Stop)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect *This);

    END_INTERFACE
} __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffectVtbl;

interface __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect {
    CONST_VTBL __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffectVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IForceFeedbackEffect methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect_get_Gain(This,value) (This)->lpVtbl->get_Gain(This,value)
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect_put_Gain(This,value) (This)->lpVtbl->put_Gain(This,value)
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect_get_State(This,value) (This)->lpVtbl->get_State(This,value)
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect_Start(This) (This)->lpVtbl->Start(This)
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect_Stop(This) (This)->lpVtbl->Stop(This)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect_QueryInterface(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect_AddRef(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect_Release(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect_GetIids(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect_GetRuntimeClassName(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect_GetTrustLevel(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IForceFeedbackEffect methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect_get_Gain(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect* This,DOUBLE *value) {
    return This->lpVtbl->get_Gain(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect_put_Gain(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect* This,DOUBLE value) {
    return This->lpVtbl->put_Gain(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect_get_State(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect* This,__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CForceFeedbackEffectState *value) {
    return This->lpVtbl->get_State(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect_Start(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect* This) {
    return This->lpVtbl->Start(This);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect_Stop(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect* This) {
    return This->lpVtbl->Stop(This);
}
#endif
#ifdef WIDL_using_Windows_Gaming_Input_ForceFeedback
#define IID_IForceFeedbackEffect IID___x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect
#define IForceFeedbackEffectVtbl __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffectVtbl
#define IForceFeedbackEffect __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect
#define IForceFeedbackEffect_QueryInterface __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect_QueryInterface
#define IForceFeedbackEffect_AddRef __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect_AddRef
#define IForceFeedbackEffect_Release __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect_Release
#define IForceFeedbackEffect_GetIids __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect_GetIids
#define IForceFeedbackEffect_GetRuntimeClassName __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect_GetRuntimeClassName
#define IForceFeedbackEffect_GetTrustLevel __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect_GetTrustLevel
#define IForceFeedbackEffect_get_Gain __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect_get_Gain
#define IForceFeedbackEffect_put_Gain __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect_put_Gain
#define IForceFeedbackEffect_get_State __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect_get_State
#define IForceFeedbackEffect_Start __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect_Start
#define IForceFeedbackEffect_Stop __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect_Stop
#endif /* WIDL_using_Windows_Gaming_Input_ForceFeedback */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */

/*****************************************************************************
 * IForceFeedbackMotor interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
#ifndef ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor, 0x8d3d417c, 0xa5ea, 0x4516, 0x80,0x26, 0x2b,0x00,0xf7,0x4e,0xf6,0xe5);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace ForceFeedback {
                    MIDL_INTERFACE("8d3d417c-a5ea-4516-8026-2b00f74ef6e5")
                    IForceFeedbackMotor : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE get_AreEffectsPaused(
                            boolean *value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_MasterGain(
                            DOUBLE *value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE put_MasterGain(
                            DOUBLE value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_IsEnabled(
                            boolean *value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_SupportedAxes(
                            ABI::Windows::Gaming::Input::ForceFeedback::ForceFeedbackEffectAxes *value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE LoadEffectAsync(
                            ABI::Windows::Gaming::Input::ForceFeedback::IForceFeedbackEffect *effect,
                            ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Gaming::Input::ForceFeedback::ForceFeedbackLoadEffectResult > **async_op) = 0;

                        virtual HRESULT STDMETHODCALLTYPE PauseAllEffects(
                            ) = 0;

                        virtual HRESULT STDMETHODCALLTYPE ResumeAllEffects(
                            ) = 0;

                        virtual HRESULT STDMETHODCALLTYPE StopAllEffects(
                            ) = 0;

                        virtual HRESULT STDMETHODCALLTYPE TryDisableAsync(
                            ABI::Windows::Foundation::IAsyncOperation<boolean > **async_op) = 0;

                        virtual HRESULT STDMETHODCALLTYPE TryEnableAsync(
                            ABI::Windows::Foundation::IAsyncOperation<boolean > **async_op) = 0;

                        virtual HRESULT STDMETHODCALLTYPE TryResetAsync(
                            ABI::Windows::Foundation::IAsyncOperation<boolean > **async_op) = 0;

                        virtual HRESULT STDMETHODCALLTYPE TryUnloadEffectAsync(
                            ABI::Windows::Gaming::Input::ForceFeedback::IForceFeedbackEffect *effect,
                            ABI::Windows::Foundation::IAsyncOperation<boolean > **async_op) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor, 0x8d3d417c, 0xa5ea, 0x4516, 0x80,0x26, 0x2b,0x00,0xf7,0x4e,0xf6,0xe5)
#endif
#else
typedef struct __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor *This,
        TrustLevel *trustLevel);

    /*** IForceFeedbackMotor methods ***/
    HRESULT (STDMETHODCALLTYPE *get_AreEffectsPaused)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *get_MasterGain)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor *This,
        DOUBLE *value);

    HRESULT (STDMETHODCALLTYPE *put_MasterGain)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor *This,
        DOUBLE value);

    HRESULT (STDMETHODCALLTYPE *get_IsEnabled)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *get_SupportedAxes)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor *This,
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CForceFeedbackEffectAxes *value);

    HRESULT (STDMETHODCALLTYPE *LoadEffectAsync)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor *This,
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect *effect,
        __FIAsyncOperation_1_ForceFeedbackLoadEffectResult **async_op);

    HRESULT (STDMETHODCALLTYPE *PauseAllEffects)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor *This);

    HRESULT (STDMETHODCALLTYPE *ResumeAllEffects)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor *This);

    HRESULT (STDMETHODCALLTYPE *StopAllEffects)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor *This);

    HRESULT (STDMETHODCALLTYPE *TryDisableAsync)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor *This,
        __FIAsyncOperation_1_boolean **async_op);

    HRESULT (STDMETHODCALLTYPE *TryEnableAsync)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor *This,
        __FIAsyncOperation_1_boolean **async_op);

    HRESULT (STDMETHODCALLTYPE *TryResetAsync)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor *This,
        __FIAsyncOperation_1_boolean **async_op);

    HRESULT (STDMETHODCALLTYPE *TryUnloadEffectAsync)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor *This,
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect *effect,
        __FIAsyncOperation_1_boolean **async_op);

    END_INTERFACE
} __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotorVtbl;

interface __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor {
    CONST_VTBL __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IForceFeedbackMotor methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor_get_AreEffectsPaused(This,value) (This)->lpVtbl->get_AreEffectsPaused(This,value)
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor_get_MasterGain(This,value) (This)->lpVtbl->get_MasterGain(This,value)
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor_put_MasterGain(This,value) (This)->lpVtbl->put_MasterGain(This,value)
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor_get_IsEnabled(This,value) (This)->lpVtbl->get_IsEnabled(This,value)
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor_get_SupportedAxes(This,value) (This)->lpVtbl->get_SupportedAxes(This,value)
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor_LoadEffectAsync(This,effect,async_op) (This)->lpVtbl->LoadEffectAsync(This,effect,async_op)
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor_PauseAllEffects(This) (This)->lpVtbl->PauseAllEffects(This)
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor_ResumeAllEffects(This) (This)->lpVtbl->ResumeAllEffects(This)
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor_StopAllEffects(This) (This)->lpVtbl->StopAllEffects(This)
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor_TryDisableAsync(This,async_op) (This)->lpVtbl->TryDisableAsync(This,async_op)
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor_TryEnableAsync(This,async_op) (This)->lpVtbl->TryEnableAsync(This,async_op)
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor_TryResetAsync(This,async_op) (This)->lpVtbl->TryResetAsync(This,async_op)
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor_TryUnloadEffectAsync(This,effect,async_op) (This)->lpVtbl->TryUnloadEffectAsync(This,effect,async_op)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor_QueryInterface(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor_AddRef(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor_Release(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor_GetIids(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor_GetRuntimeClassName(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor_GetTrustLevel(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IForceFeedbackMotor methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor_get_AreEffectsPaused(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor* This,boolean *value) {
    return This->lpVtbl->get_AreEffectsPaused(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor_get_MasterGain(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor* This,DOUBLE *value) {
    return This->lpVtbl->get_MasterGain(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor_put_MasterGain(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor* This,DOUBLE value) {
    return This->lpVtbl->put_MasterGain(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor_get_IsEnabled(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor* This,boolean *value) {
    return This->lpVtbl->get_IsEnabled(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor_get_SupportedAxes(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor* This,__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CForceFeedbackEffectAxes *value) {
    return This->lpVtbl->get_SupportedAxes(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor_LoadEffectAsync(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor* This,__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect *effect,__FIAsyncOperation_1_ForceFeedbackLoadEffectResult **async_op) {
    return This->lpVtbl->LoadEffectAsync(This,effect,async_op);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor_PauseAllEffects(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor* This) {
    return This->lpVtbl->PauseAllEffects(This);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor_ResumeAllEffects(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor* This) {
    return This->lpVtbl->ResumeAllEffects(This);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor_StopAllEffects(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor* This) {
    return This->lpVtbl->StopAllEffects(This);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor_TryDisableAsync(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor* This,__FIAsyncOperation_1_boolean **async_op) {
    return This->lpVtbl->TryDisableAsync(This,async_op);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor_TryEnableAsync(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor* This,__FIAsyncOperation_1_boolean **async_op) {
    return This->lpVtbl->TryEnableAsync(This,async_op);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor_TryResetAsync(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor* This,__FIAsyncOperation_1_boolean **async_op) {
    return This->lpVtbl->TryResetAsync(This,async_op);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor_TryUnloadEffectAsync(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor* This,__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect *effect,__FIAsyncOperation_1_boolean **async_op) {
    return This->lpVtbl->TryUnloadEffectAsync(This,effect,async_op);
}
#endif
#ifdef WIDL_using_Windows_Gaming_Input_ForceFeedback
#define IID_IForceFeedbackMotor IID___x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor
#define IForceFeedbackMotorVtbl __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotorVtbl
#define IForceFeedbackMotor __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor
#define IForceFeedbackMotor_QueryInterface __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor_QueryInterface
#define IForceFeedbackMotor_AddRef __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor_AddRef
#define IForceFeedbackMotor_Release __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor_Release
#define IForceFeedbackMotor_GetIids __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor_GetIids
#define IForceFeedbackMotor_GetRuntimeClassName __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor_GetRuntimeClassName
#define IForceFeedbackMotor_GetTrustLevel __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor_GetTrustLevel
#define IForceFeedbackMotor_get_AreEffectsPaused __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor_get_AreEffectsPaused
#define IForceFeedbackMotor_get_MasterGain __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor_get_MasterGain
#define IForceFeedbackMotor_put_MasterGain __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor_put_MasterGain
#define IForceFeedbackMotor_get_IsEnabled __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor_get_IsEnabled
#define IForceFeedbackMotor_get_SupportedAxes __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor_get_SupportedAxes
#define IForceFeedbackMotor_LoadEffectAsync __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor_LoadEffectAsync
#define IForceFeedbackMotor_PauseAllEffects __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor_PauseAllEffects
#define IForceFeedbackMotor_ResumeAllEffects __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor_ResumeAllEffects
#define IForceFeedbackMotor_StopAllEffects __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor_StopAllEffects
#define IForceFeedbackMotor_TryDisableAsync __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor_TryDisableAsync
#define IForceFeedbackMotor_TryEnableAsync __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor_TryEnableAsync
#define IForceFeedbackMotor_TryResetAsync __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor_TryResetAsync
#define IForceFeedbackMotor_TryUnloadEffectAsync __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor_TryUnloadEffectAsync
#endif /* WIDL_using_Windows_Gaming_Input_ForceFeedback */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */

/*****************************************************************************
 * IPeriodicForceEffect interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
#ifndef ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffect_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffect_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffect, 0x5c5138d7, 0xfc75, 0x4d52, 0x9a,0x0a, 0xef,0xe4,0xca,0xb5,0xfe,0x64);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace ForceFeedback {
                    MIDL_INTERFACE("5c5138d7-fc75-4d52-9a0a-efe4cab5fe64")
                    IPeriodicForceEffect : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE get_Kind(
                            ABI::Windows::Gaming::Input::ForceFeedback::PeriodicForceEffectKind *value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE SetParameters(
                            ABI::Windows::Foundation::Numerics::Vector3 vector,
                            FLOAT frequency,
                            FLOAT phase,
                            FLOAT bias,
                            ABI::Windows::Foundation::TimeSpan duration) = 0;

                        virtual HRESULT STDMETHODCALLTYPE SetParametersWithEnvelope(
                            ABI::Windows::Foundation::Numerics::Vector3 vector,
                            FLOAT frequency,
                            FLOAT phase,
                            FLOAT bias,
                            FLOAT attack_gain,
                            FLOAT sustain_gain,
                            FLOAT release_gain,
                            ABI::Windows::Foundation::TimeSpan start_delay,
                            ABI::Windows::Foundation::TimeSpan attack_duration,
                            ABI::Windows::Foundation::TimeSpan sustain_duration,
                            ABI::Windows::Foundation::TimeSpan release_duration,
                            UINT32 repeat_count) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffect, 0x5c5138d7, 0xfc75, 0x4d52, 0x9a,0x0a, 0xef,0xe4,0xca,0xb5,0xfe,0x64)
#endif
#else
typedef struct __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffectVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffect *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffect *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffect *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffect *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffect *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffect *This,
        TrustLevel *trustLevel);

    /*** IPeriodicForceEffect methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Kind)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffect *This,
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CPeriodicForceEffectKind *value);

    HRESULT (STDMETHODCALLTYPE *SetParameters)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffect *This,
        __x_ABI_CWindows_CFoundation_CNumerics_CVector3 vector,
        FLOAT frequency,
        FLOAT phase,
        FLOAT bias,
        __x_ABI_CWindows_CFoundation_CTimeSpan duration);

    HRESULT (STDMETHODCALLTYPE *SetParametersWithEnvelope)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffect *This,
        __x_ABI_CWindows_CFoundation_CNumerics_CVector3 vector,
        FLOAT frequency,
        FLOAT phase,
        FLOAT bias,
        FLOAT attack_gain,
        FLOAT sustain_gain,
        FLOAT release_gain,
        __x_ABI_CWindows_CFoundation_CTimeSpan start_delay,
        __x_ABI_CWindows_CFoundation_CTimeSpan attack_duration,
        __x_ABI_CWindows_CFoundation_CTimeSpan sustain_duration,
        __x_ABI_CWindows_CFoundation_CTimeSpan release_duration,
        UINT32 repeat_count);

    END_INTERFACE
} __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffectVtbl;

interface __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffect {
    CONST_VTBL __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffectVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffect_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffect_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffect_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffect_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffect_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffect_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IPeriodicForceEffect methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffect_get_Kind(This,value) (This)->lpVtbl->get_Kind(This,value)
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffect_SetParameters(This,vector,frequency,phase,bias,duration) (This)->lpVtbl->SetParameters(This,vector,frequency,phase,bias,duration)
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffect_SetParametersWithEnvelope(This,vector,frequency,phase,bias,attack_gain,sustain_gain,release_gain,start_delay,attack_duration,sustain_duration,release_duration,repeat_count) (This)->lpVtbl->SetParametersWithEnvelope(This,vector,frequency,phase,bias,attack_gain,sustain_gain,release_gain,start_delay,attack_duration,sustain_duration,release_duration,repeat_count)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffect_QueryInterface(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffect* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffect_AddRef(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffect* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffect_Release(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffect* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffect_GetIids(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffect* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffect_GetRuntimeClassName(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffect* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffect_GetTrustLevel(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffect* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IPeriodicForceEffect methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffect_get_Kind(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffect* This,__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CPeriodicForceEffectKind *value) {
    return This->lpVtbl->get_Kind(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffect_SetParameters(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffect* This,__x_ABI_CWindows_CFoundation_CNumerics_CVector3 vector,FLOAT frequency,FLOAT phase,FLOAT bias,__x_ABI_CWindows_CFoundation_CTimeSpan duration) {
    return This->lpVtbl->SetParameters(This,vector,frequency,phase,bias,duration);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffect_SetParametersWithEnvelope(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffect* This,__x_ABI_CWindows_CFoundation_CNumerics_CVector3 vector,FLOAT frequency,FLOAT phase,FLOAT bias,FLOAT attack_gain,FLOAT sustain_gain,FLOAT release_gain,__x_ABI_CWindows_CFoundation_CTimeSpan start_delay,__x_ABI_CWindows_CFoundation_CTimeSpan attack_duration,__x_ABI_CWindows_CFoundation_CTimeSpan sustain_duration,__x_ABI_CWindows_CFoundation_CTimeSpan release_duration,UINT32 repeat_count) {
    return This->lpVtbl->SetParametersWithEnvelope(This,vector,frequency,phase,bias,attack_gain,sustain_gain,release_gain,start_delay,attack_duration,sustain_duration,release_duration,repeat_count);
}
#endif
#ifdef WIDL_using_Windows_Gaming_Input_ForceFeedback
#define IID_IPeriodicForceEffect IID___x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffect
#define IPeriodicForceEffectVtbl __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffectVtbl
#define IPeriodicForceEffect __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffect
#define IPeriodicForceEffect_QueryInterface __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffect_QueryInterface
#define IPeriodicForceEffect_AddRef __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffect_AddRef
#define IPeriodicForceEffect_Release __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffect_Release
#define IPeriodicForceEffect_GetIids __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffect_GetIids
#define IPeriodicForceEffect_GetRuntimeClassName __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffect_GetRuntimeClassName
#define IPeriodicForceEffect_GetTrustLevel __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffect_GetTrustLevel
#define IPeriodicForceEffect_get_Kind __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffect_get_Kind
#define IPeriodicForceEffect_SetParameters __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffect_SetParameters
#define IPeriodicForceEffect_SetParametersWithEnvelope __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffect_SetParametersWithEnvelope
#endif /* WIDL_using_Windows_Gaming_Input_ForceFeedback */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffect_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */

/*****************************************************************************
 * IPeriodicForceEffectFactory interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
#ifndef ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffectFactory_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffectFactory_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffectFactory, 0x6f62eb1a, 0x9851, 0x477b, 0xb3,0x18, 0x35,0xec,0xaa,0x15,0x07,0x0f);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace ForceFeedback {
                    MIDL_INTERFACE("6f62eb1a-9851-477b-b318-35ecaa15070f")
                    IPeriodicForceEffectFactory : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE CreateInstance(
                            ABI::Windows::Gaming::Input::ForceFeedback::PeriodicForceEffectKind kind,
                            ABI::Windows::Gaming::Input::ForceFeedback::IForceFeedbackEffect **value) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffectFactory, 0x6f62eb1a, 0x9851, 0x477b, 0xb3,0x18, 0x35,0xec,0xaa,0x15,0x07,0x0f)
#endif
#else
typedef struct __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffectFactoryVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffectFactory *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffectFactory *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffectFactory *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffectFactory *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffectFactory *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffectFactory *This,
        TrustLevel *trustLevel);

    /*** IPeriodicForceEffectFactory methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateInstance)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffectFactory *This,
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CPeriodicForceEffectKind kind,
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect **value);

    END_INTERFACE
} __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffectFactoryVtbl;

interface __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffectFactory {
    CONST_VTBL __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffectFactoryVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffectFactory_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffectFactory_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffectFactory_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffectFactory_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffectFactory_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffectFactory_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IPeriodicForceEffectFactory methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffectFactory_CreateInstance(This,kind,value) (This)->lpVtbl->CreateInstance(This,kind,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffectFactory_QueryInterface(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffectFactory* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffectFactory_AddRef(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffectFactory* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffectFactory_Release(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffectFactory* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffectFactory_GetIids(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffectFactory* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffectFactory_GetRuntimeClassName(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffectFactory* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffectFactory_GetTrustLevel(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffectFactory* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IPeriodicForceEffectFactory methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffectFactory_CreateInstance(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffectFactory* This,__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CPeriodicForceEffectKind kind,__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect **value) {
    return This->lpVtbl->CreateInstance(This,kind,value);
}
#endif
#ifdef WIDL_using_Windows_Gaming_Input_ForceFeedback
#define IID_IPeriodicForceEffectFactory IID___x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffectFactory
#define IPeriodicForceEffectFactoryVtbl __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffectFactoryVtbl
#define IPeriodicForceEffectFactory __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffectFactory
#define IPeriodicForceEffectFactory_QueryInterface __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffectFactory_QueryInterface
#define IPeriodicForceEffectFactory_AddRef __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffectFactory_AddRef
#define IPeriodicForceEffectFactory_Release __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffectFactory_Release
#define IPeriodicForceEffectFactory_GetIids __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffectFactory_GetIids
#define IPeriodicForceEffectFactory_GetRuntimeClassName __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffectFactory_GetRuntimeClassName
#define IPeriodicForceEffectFactory_GetTrustLevel __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffectFactory_GetTrustLevel
#define IPeriodicForceEffectFactory_CreateInstance __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffectFactory_CreateInstance
#endif /* WIDL_using_Windows_Gaming_Input_ForceFeedback */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIPeriodicForceEffectFactory_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */

/*****************************************************************************
 * IConditionForceEffect interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
#ifndef ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffect_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffect_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffect, 0x32d1ea68, 0x3695, 0x4e69, 0x85,0xc0, 0xcd,0x19,0x44,0x18,0x91,0x40);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace ForceFeedback {
                    MIDL_INTERFACE("32d1ea68-3695-4e69-85c0-cd1944189140")
                    IConditionForceEffect : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE get_Kind(
                            ABI::Windows::Gaming::Input::ForceFeedback::ConditionForceEffectKind *value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE SetParameters(
                            ABI::Windows::Foundation::Numerics::Vector3 direction,
                            FLOAT positive_coeff,
                            FLOAT negative_coeff,
                            FLOAT max_positive_magnitude,
                            FLOAT max_negative_magnitude,
                            FLOAT deadzone,
                            FLOAT bias) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffect, 0x32d1ea68, 0x3695, 0x4e69, 0x85,0xc0, 0xcd,0x19,0x44,0x18,0x91,0x40)
#endif
#else
typedef struct __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffectVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffect *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffect *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffect *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffect *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffect *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffect *This,
        TrustLevel *trustLevel);

    /*** IConditionForceEffect methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Kind)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffect *This,
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CConditionForceEffectKind *value);

    HRESULT (STDMETHODCALLTYPE *SetParameters)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffect *This,
        __x_ABI_CWindows_CFoundation_CNumerics_CVector3 direction,
        FLOAT positive_coeff,
        FLOAT negative_coeff,
        FLOAT max_positive_magnitude,
        FLOAT max_negative_magnitude,
        FLOAT deadzone,
        FLOAT bias);

    END_INTERFACE
} __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffectVtbl;

interface __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffect {
    CONST_VTBL __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffectVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffect_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffect_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffect_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffect_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffect_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffect_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IConditionForceEffect methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffect_get_Kind(This,value) (This)->lpVtbl->get_Kind(This,value)
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffect_SetParameters(This,direction,positive_coeff,negative_coeff,max_positive_magnitude,max_negative_magnitude,deadzone,bias) (This)->lpVtbl->SetParameters(This,direction,positive_coeff,negative_coeff,max_positive_magnitude,max_negative_magnitude,deadzone,bias)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffect_QueryInterface(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffect* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffect_AddRef(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffect* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffect_Release(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffect* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffect_GetIids(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffect* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffect_GetRuntimeClassName(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffect* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffect_GetTrustLevel(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffect* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IConditionForceEffect methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffect_get_Kind(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffect* This,__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CConditionForceEffectKind *value) {
    return This->lpVtbl->get_Kind(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffect_SetParameters(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffect* This,__x_ABI_CWindows_CFoundation_CNumerics_CVector3 direction,FLOAT positive_coeff,FLOAT negative_coeff,FLOAT max_positive_magnitude,FLOAT max_negative_magnitude,FLOAT deadzone,FLOAT bias) {
    return This->lpVtbl->SetParameters(This,direction,positive_coeff,negative_coeff,max_positive_magnitude,max_negative_magnitude,deadzone,bias);
}
#endif
#ifdef WIDL_using_Windows_Gaming_Input_ForceFeedback
#define IID_IConditionForceEffect IID___x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffect
#define IConditionForceEffectVtbl __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffectVtbl
#define IConditionForceEffect __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffect
#define IConditionForceEffect_QueryInterface __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffect_QueryInterface
#define IConditionForceEffect_AddRef __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffect_AddRef
#define IConditionForceEffect_Release __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffect_Release
#define IConditionForceEffect_GetIids __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffect_GetIids
#define IConditionForceEffect_GetRuntimeClassName __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffect_GetRuntimeClassName
#define IConditionForceEffect_GetTrustLevel __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffect_GetTrustLevel
#define IConditionForceEffect_get_Kind __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffect_get_Kind
#define IConditionForceEffect_SetParameters __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffect_SetParameters
#endif /* WIDL_using_Windows_Gaming_Input_ForceFeedback */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffect_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */

/*****************************************************************************
 * IConditionForceEffectFactory interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
#ifndef ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffectFactory_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffectFactory_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffectFactory, 0x91a99264, 0x1810, 0x4eb6, 0xa7,0x73, 0xbf,0xd3,0xb8,0xcd,0xdb,0xab);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace ForceFeedback {
                    MIDL_INTERFACE("91a99264-1810-4eb6-a773-bfd3b8cddbab")
                    IConditionForceEffectFactory : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE CreateInstance(
                            ABI::Windows::Gaming::Input::ForceFeedback::ConditionForceEffectKind kind,
                            ABI::Windows::Gaming::Input::ForceFeedback::IForceFeedbackEffect **value) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffectFactory, 0x91a99264, 0x1810, 0x4eb6, 0xa7,0x73, 0xbf,0xd3,0xb8,0xcd,0xdb,0xab)
#endif
#else
typedef struct __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffectFactoryVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffectFactory *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffectFactory *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffectFactory *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffectFactory *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffectFactory *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffectFactory *This,
        TrustLevel *trustLevel);

    /*** IConditionForceEffectFactory methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateInstance)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffectFactory *This,
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CConditionForceEffectKind kind,
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect **value);

    END_INTERFACE
} __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffectFactoryVtbl;

interface __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffectFactory {
    CONST_VTBL __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffectFactoryVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffectFactory_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffectFactory_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffectFactory_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffectFactory_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffectFactory_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffectFactory_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IConditionForceEffectFactory methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffectFactory_CreateInstance(This,kind,value) (This)->lpVtbl->CreateInstance(This,kind,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffectFactory_QueryInterface(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffectFactory* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffectFactory_AddRef(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffectFactory* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffectFactory_Release(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffectFactory* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffectFactory_GetIids(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffectFactory* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffectFactory_GetRuntimeClassName(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffectFactory* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffectFactory_GetTrustLevel(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffectFactory* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IConditionForceEffectFactory methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffectFactory_CreateInstance(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffectFactory* This,__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CConditionForceEffectKind kind,__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackEffect **value) {
    return This->lpVtbl->CreateInstance(This,kind,value);
}
#endif
#ifdef WIDL_using_Windows_Gaming_Input_ForceFeedback
#define IID_IConditionForceEffectFactory IID___x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffectFactory
#define IConditionForceEffectFactoryVtbl __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffectFactoryVtbl
#define IConditionForceEffectFactory __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffectFactory
#define IConditionForceEffectFactory_QueryInterface __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffectFactory_QueryInterface
#define IConditionForceEffectFactory_AddRef __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffectFactory_AddRef
#define IConditionForceEffectFactory_Release __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffectFactory_Release
#define IConditionForceEffectFactory_GetIids __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffectFactory_GetIids
#define IConditionForceEffectFactory_GetRuntimeClassName __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffectFactory_GetRuntimeClassName
#define IConditionForceEffectFactory_GetTrustLevel __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffectFactory_GetTrustLevel
#define IConditionForceEffectFactory_CreateInstance __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffectFactory_CreateInstance
#endif /* WIDL_using_Windows_Gaming_Input_ForceFeedback */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConditionForceEffectFactory_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */

/*****************************************************************************
 * IConstantForceEffect interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
#ifndef ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConstantForceEffect_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConstantForceEffect_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConstantForceEffect, 0x9bfa0140, 0xf3c7, 0x415c, 0xb0,0x68, 0x0f,0x06,0x87,0x34,0xbc,0xe0);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace ForceFeedback {
                    MIDL_INTERFACE("9bfa0140-f3c7-415c-b068-0f068734bce0")
                    IConstantForceEffect : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE SetParameters(
                            ABI::Windows::Foundation::Numerics::Vector3 vector,
                            ABI::Windows::Foundation::TimeSpan duration) = 0;

                        virtual HRESULT STDMETHODCALLTYPE SetParametersWithEnvelope(
                            ABI::Windows::Foundation::Numerics::Vector3 vector,
                            FLOAT attack_gain,
                            FLOAT sustain_gain,
                            FLOAT release_gain,
                            ABI::Windows::Foundation::TimeSpan start_delay,
                            ABI::Windows::Foundation::TimeSpan attack_duration,
                            ABI::Windows::Foundation::TimeSpan sustain_duration,
                            ABI::Windows::Foundation::TimeSpan release_duration,
                            UINT32 repeat_count) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConstantForceEffect, 0x9bfa0140, 0xf3c7, 0x415c, 0xb0,0x68, 0x0f,0x06,0x87,0x34,0xbc,0xe0)
#endif
#else
typedef struct __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConstantForceEffectVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConstantForceEffect *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConstantForceEffect *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConstantForceEffect *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConstantForceEffect *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConstantForceEffect *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConstantForceEffect *This,
        TrustLevel *trustLevel);

    /*** IConstantForceEffect methods ***/
    HRESULT (STDMETHODCALLTYPE *SetParameters)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConstantForceEffect *This,
        __x_ABI_CWindows_CFoundation_CNumerics_CVector3 vector,
        __x_ABI_CWindows_CFoundation_CTimeSpan duration);

    HRESULT (STDMETHODCALLTYPE *SetParametersWithEnvelope)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConstantForceEffect *This,
        __x_ABI_CWindows_CFoundation_CNumerics_CVector3 vector,
        FLOAT attack_gain,
        FLOAT sustain_gain,
        FLOAT release_gain,
        __x_ABI_CWindows_CFoundation_CTimeSpan start_delay,
        __x_ABI_CWindows_CFoundation_CTimeSpan attack_duration,
        __x_ABI_CWindows_CFoundation_CTimeSpan sustain_duration,
        __x_ABI_CWindows_CFoundation_CTimeSpan release_duration,
        UINT32 repeat_count);

    END_INTERFACE
} __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConstantForceEffectVtbl;

interface __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConstantForceEffect {
    CONST_VTBL __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConstantForceEffectVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConstantForceEffect_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConstantForceEffect_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConstantForceEffect_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConstantForceEffect_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConstantForceEffect_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConstantForceEffect_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IConstantForceEffect methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConstantForceEffect_SetParameters(This,vector,duration) (This)->lpVtbl->SetParameters(This,vector,duration)
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConstantForceEffect_SetParametersWithEnvelope(This,vector,attack_gain,sustain_gain,release_gain,start_delay,attack_duration,sustain_duration,release_duration,repeat_count) (This)->lpVtbl->SetParametersWithEnvelope(This,vector,attack_gain,sustain_gain,release_gain,start_delay,attack_duration,sustain_duration,release_duration,repeat_count)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConstantForceEffect_QueryInterface(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConstantForceEffect* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConstantForceEffect_AddRef(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConstantForceEffect* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConstantForceEffect_Release(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConstantForceEffect* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConstantForceEffect_GetIids(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConstantForceEffect* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConstantForceEffect_GetRuntimeClassName(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConstantForceEffect* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConstantForceEffect_GetTrustLevel(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConstantForceEffect* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IConstantForceEffect methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConstantForceEffect_SetParameters(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConstantForceEffect* This,__x_ABI_CWindows_CFoundation_CNumerics_CVector3 vector,__x_ABI_CWindows_CFoundation_CTimeSpan duration) {
    return This->lpVtbl->SetParameters(This,vector,duration);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConstantForceEffect_SetParametersWithEnvelope(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConstantForceEffect* This,__x_ABI_CWindows_CFoundation_CNumerics_CVector3 vector,FLOAT attack_gain,FLOAT sustain_gain,FLOAT release_gain,__x_ABI_CWindows_CFoundation_CTimeSpan start_delay,__x_ABI_CWindows_CFoundation_CTimeSpan attack_duration,__x_ABI_CWindows_CFoundation_CTimeSpan sustain_duration,__x_ABI_CWindows_CFoundation_CTimeSpan release_duration,UINT32 repeat_count) {
    return This->lpVtbl->SetParametersWithEnvelope(This,vector,attack_gain,sustain_gain,release_gain,start_delay,attack_duration,sustain_duration,release_duration,repeat_count);
}
#endif
#ifdef WIDL_using_Windows_Gaming_Input_ForceFeedback
#define IID_IConstantForceEffect IID___x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConstantForceEffect
#define IConstantForceEffectVtbl __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConstantForceEffectVtbl
#define IConstantForceEffect __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConstantForceEffect
#define IConstantForceEffect_QueryInterface __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConstantForceEffect_QueryInterface
#define IConstantForceEffect_AddRef __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConstantForceEffect_AddRef
#define IConstantForceEffect_Release __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConstantForceEffect_Release
#define IConstantForceEffect_GetIids __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConstantForceEffect_GetIids
#define IConstantForceEffect_GetRuntimeClassName __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConstantForceEffect_GetRuntimeClassName
#define IConstantForceEffect_GetTrustLevel __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConstantForceEffect_GetTrustLevel
#define IConstantForceEffect_SetParameters __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConstantForceEffect_SetParameters
#define IConstantForceEffect_SetParametersWithEnvelope __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConstantForceEffect_SetParametersWithEnvelope
#endif /* WIDL_using_Windows_Gaming_Input_ForceFeedback */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIConstantForceEffect_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */

/*****************************************************************************
 * IRampForceEffect interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
#ifndef ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIRampForceEffect_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIRampForceEffect_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIRampForceEffect, 0xf1f81259, 0x1ca6, 0x4080, 0xb5,0x6d, 0xb4,0x3f,0x33,0x54,0xd0,0x52);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace ForceFeedback {
                    MIDL_INTERFACE("f1f81259-1ca6-4080-b56d-b43f3354d052")
                    IRampForceEffect : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE SetParameters(
                            ABI::Windows::Foundation::Numerics::Vector3 start_vector,
                            ABI::Windows::Foundation::Numerics::Vector3 end_vector,
                            ABI::Windows::Foundation::TimeSpan duration) = 0;

                        virtual HRESULT STDMETHODCALLTYPE SetParametersWithEnvelope(
                            ABI::Windows::Foundation::Numerics::Vector3 start_vector,
                            ABI::Windows::Foundation::Numerics::Vector3 end_vector,
                            FLOAT attack_gain,
                            FLOAT sustain_gain,
                            FLOAT release_gain,
                            ABI::Windows::Foundation::TimeSpan start_delay,
                            ABI::Windows::Foundation::TimeSpan attack_duration,
                            ABI::Windows::Foundation::TimeSpan sustain_duration,
                            ABI::Windows::Foundation::TimeSpan release_duration,
                            UINT32 repeat_count) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIRampForceEffect, 0xf1f81259, 0x1ca6, 0x4080, 0xb5,0x6d, 0xb4,0x3f,0x33,0x54,0xd0,0x52)
#endif
#else
typedef struct __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIRampForceEffectVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIRampForceEffect *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIRampForceEffect *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIRampForceEffect *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIRampForceEffect *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIRampForceEffect *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIRampForceEffect *This,
        TrustLevel *trustLevel);

    /*** IRampForceEffect methods ***/
    HRESULT (STDMETHODCALLTYPE *SetParameters)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIRampForceEffect *This,
        __x_ABI_CWindows_CFoundation_CNumerics_CVector3 start_vector,
        __x_ABI_CWindows_CFoundation_CNumerics_CVector3 end_vector,
        __x_ABI_CWindows_CFoundation_CTimeSpan duration);

    HRESULT (STDMETHODCALLTYPE *SetParametersWithEnvelope)(
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIRampForceEffect *This,
        __x_ABI_CWindows_CFoundation_CNumerics_CVector3 start_vector,
        __x_ABI_CWindows_CFoundation_CNumerics_CVector3 end_vector,
        FLOAT attack_gain,
        FLOAT sustain_gain,
        FLOAT release_gain,
        __x_ABI_CWindows_CFoundation_CTimeSpan start_delay,
        __x_ABI_CWindows_CFoundation_CTimeSpan attack_duration,
        __x_ABI_CWindows_CFoundation_CTimeSpan sustain_duration,
        __x_ABI_CWindows_CFoundation_CTimeSpan release_duration,
        UINT32 repeat_count);

    END_INTERFACE
} __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIRampForceEffectVtbl;

interface __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIRampForceEffect {
    CONST_VTBL __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIRampForceEffectVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIRampForceEffect_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIRampForceEffect_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIRampForceEffect_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIRampForceEffect_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIRampForceEffect_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIRampForceEffect_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IRampForceEffect methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIRampForceEffect_SetParameters(This,start_vector,end_vector,duration) (This)->lpVtbl->SetParameters(This,start_vector,end_vector,duration)
#define __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIRampForceEffect_SetParametersWithEnvelope(This,start_vector,end_vector,attack_gain,sustain_gain,release_gain,start_delay,attack_duration,sustain_duration,release_duration,repeat_count) (This)->lpVtbl->SetParametersWithEnvelope(This,start_vector,end_vector,attack_gain,sustain_gain,release_gain,start_delay,attack_duration,sustain_duration,release_duration,repeat_count)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIRampForceEffect_QueryInterface(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIRampForceEffect* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIRampForceEffect_AddRef(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIRampForceEffect* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIRampForceEffect_Release(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIRampForceEffect* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIRampForceEffect_GetIids(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIRampForceEffect* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIRampForceEffect_GetRuntimeClassName(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIRampForceEffect* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIRampForceEffect_GetTrustLevel(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIRampForceEffect* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IRampForceEffect methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIRampForceEffect_SetParameters(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIRampForceEffect* This,__x_ABI_CWindows_CFoundation_CNumerics_CVector3 start_vector,__x_ABI_CWindows_CFoundation_CNumerics_CVector3 end_vector,__x_ABI_CWindows_CFoundation_CTimeSpan duration) {
    return This->lpVtbl->SetParameters(This,start_vector,end_vector,duration);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIRampForceEffect_SetParametersWithEnvelope(__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIRampForceEffect* This,__x_ABI_CWindows_CFoundation_CNumerics_CVector3 start_vector,__x_ABI_CWindows_CFoundation_CNumerics_CVector3 end_vector,FLOAT attack_gain,FLOAT sustain_gain,FLOAT release_gain,__x_ABI_CWindows_CFoundation_CTimeSpan start_delay,__x_ABI_CWindows_CFoundation_CTimeSpan attack_duration,__x_ABI_CWindows_CFoundation_CTimeSpan sustain_duration,__x_ABI_CWindows_CFoundation_CTimeSpan release_duration,UINT32 repeat_count) {
    return This->lpVtbl->SetParametersWithEnvelope(This,start_vector,end_vector,attack_gain,sustain_gain,release_gain,start_delay,attack_duration,sustain_duration,release_duration,repeat_count);
}
#endif
#ifdef WIDL_using_Windows_Gaming_Input_ForceFeedback
#define IID_IRampForceEffect IID___x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIRampForceEffect
#define IRampForceEffectVtbl __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIRampForceEffectVtbl
#define IRampForceEffect __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIRampForceEffect
#define IRampForceEffect_QueryInterface __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIRampForceEffect_QueryInterface
#define IRampForceEffect_AddRef __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIRampForceEffect_AddRef
#define IRampForceEffect_Release __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIRampForceEffect_Release
#define IRampForceEffect_GetIids __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIRampForceEffect_GetIids
#define IRampForceEffect_GetRuntimeClassName __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIRampForceEffect_GetRuntimeClassName
#define IRampForceEffect_GetTrustLevel __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIRampForceEffect_GetTrustLevel
#define IRampForceEffect_SetParameters __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIRampForceEffect_SetParameters
#define IRampForceEffect_SetParametersWithEnvelope __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIRampForceEffect_SetParametersWithEnvelope
#endif /* WIDL_using_Windows_Gaming_Input_ForceFeedback */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIRampForceEffect_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */

/*
 * Class Windows.Gaming.Input.ForceFeedback.ForceFeedbackMotor
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
#ifndef RUNTIMECLASS_Windows_Gaming_Input_ForceFeedback_ForceFeedbackMotor_DEFINED
#define RUNTIMECLASS_Windows_Gaming_Input_ForceFeedback_ForceFeedbackMotor_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Gaming_Input_ForceFeedback_ForceFeedbackMotor[] = {'W','i','n','d','o','w','s','.','G','a','m','i','n','g','.','I','n','p','u','t','.','F','o','r','c','e','F','e','e','d','b','a','c','k','.','F','o','r','c','e','F','e','e','d','b','a','c','k','M','o','t','o','r',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Gaming_Input_ForceFeedback_ForceFeedbackMotor[] = L"Windows.Gaming.Input.ForceFeedback.ForceFeedbackMotor";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Gaming_Input_ForceFeedback_ForceFeedbackMotor[] = {'W','i','n','d','o','w','s','.','G','a','m','i','n','g','.','I','n','p','u','t','.','F','o','r','c','e','F','e','e','d','b','a','c','k','.','F','o','r','c','e','F','e','e','d','b','a','c','k','M','o','t','o','r',0};
#endif
#endif /* RUNTIMECLASS_Windows_Gaming_Input_ForceFeedback_ForceFeedbackMotor_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */

/*
 * Class Windows.Gaming.Input.ForceFeedback.PeriodicForceEffect
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
#ifndef RUNTIMECLASS_Windows_Gaming_Input_ForceFeedback_PeriodicForceEffect_DEFINED
#define RUNTIMECLASS_Windows_Gaming_Input_ForceFeedback_PeriodicForceEffect_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Gaming_Input_ForceFeedback_PeriodicForceEffect[] = {'W','i','n','d','o','w','s','.','G','a','m','i','n','g','.','I','n','p','u','t','.','F','o','r','c','e','F','e','e','d','b','a','c','k','.','P','e','r','i','o','d','i','c','F','o','r','c','e','E','f','f','e','c','t',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Gaming_Input_ForceFeedback_PeriodicForceEffect[] = L"Windows.Gaming.Input.ForceFeedback.PeriodicForceEffect";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Gaming_Input_ForceFeedback_PeriodicForceEffect[] = {'W','i','n','d','o','w','s','.','G','a','m','i','n','g','.','I','n','p','u','t','.','F','o','r','c','e','F','e','e','d','b','a','c','k','.','P','e','r','i','o','d','i','c','F','o','r','c','e','E','f','f','e','c','t',0};
#endif
#endif /* RUNTIMECLASS_Windows_Gaming_Input_ForceFeedback_PeriodicForceEffect_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */

/*
 * Class Windows.Gaming.Input.ForceFeedback.ConditionForceEffect
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
#ifndef RUNTIMECLASS_Windows_Gaming_Input_ForceFeedback_ConditionForceEffect_DEFINED
#define RUNTIMECLASS_Windows_Gaming_Input_ForceFeedback_ConditionForceEffect_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Gaming_Input_ForceFeedback_ConditionForceEffect[] = {'W','i','n','d','o','w','s','.','G','a','m','i','n','g','.','I','n','p','u','t','.','F','o','r','c','e','F','e','e','d','b','a','c','k','.','C','o','n','d','i','t','i','o','n','F','o','r','c','e','E','f','f','e','c','t',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Gaming_Input_ForceFeedback_ConditionForceEffect[] = L"Windows.Gaming.Input.ForceFeedback.ConditionForceEffect";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Gaming_Input_ForceFeedback_ConditionForceEffect[] = {'W','i','n','d','o','w','s','.','G','a','m','i','n','g','.','I','n','p','u','t','.','F','o','r','c','e','F','e','e','d','b','a','c','k','.','C','o','n','d','i','t','i','o','n','F','o','r','c','e','E','f','f','e','c','t',0};
#endif
#endif /* RUNTIMECLASS_Windows_Gaming_Input_ForceFeedback_ConditionForceEffect_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */

/*
 * Class Windows.Gaming.Input.ForceFeedback.ConstantForceEffect
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
#ifndef RUNTIMECLASS_Windows_Gaming_Input_ForceFeedback_ConstantForceEffect_DEFINED
#define RUNTIMECLASS_Windows_Gaming_Input_ForceFeedback_ConstantForceEffect_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Gaming_Input_ForceFeedback_ConstantForceEffect[] = {'W','i','n','d','o','w','s','.','G','a','m','i','n','g','.','I','n','p','u','t','.','F','o','r','c','e','F','e','e','d','b','a','c','k','.','C','o','n','s','t','a','n','t','F','o','r','c','e','E','f','f','e','c','t',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Gaming_Input_ForceFeedback_ConstantForceEffect[] = L"Windows.Gaming.Input.ForceFeedback.ConstantForceEffect";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Gaming_Input_ForceFeedback_ConstantForceEffect[] = {'W','i','n','d','o','w','s','.','G','a','m','i','n','g','.','I','n','p','u','t','.','F','o','r','c','e','F','e','e','d','b','a','c','k','.','C','o','n','s','t','a','n','t','F','o','r','c','e','E','f','f','e','c','t',0};
#endif
#endif /* RUNTIMECLASS_Windows_Gaming_Input_ForceFeedback_ConstantForceEffect_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */

/*
 * Class Windows.Gaming.Input.ForceFeedback.RampForceEffect
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
#ifndef RUNTIMECLASS_Windows_Gaming_Input_ForceFeedback_RampForceEffect_DEFINED
#define RUNTIMECLASS_Windows_Gaming_Input_ForceFeedback_RampForceEffect_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Gaming_Input_ForceFeedback_RampForceEffect[] = {'W','i','n','d','o','w','s','.','G','a','m','i','n','g','.','I','n','p','u','t','.','F','o','r','c','e','F','e','e','d','b','a','c','k','.','R','a','m','p','F','o','r','c','e','E','f','f','e','c','t',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Gaming_Input_ForceFeedback_RampForceEffect[] = L"Windows.Gaming.Input.ForceFeedback.RampForceEffect";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Gaming_Input_ForceFeedback_RampForceEffect[] = {'W','i','n','d','o','w','s','.','G','a','m','i','n','g','.','I','n','p','u','t','.','F','o','r','c','e','F','e','e','d','b','a','c','k','.','R','a','m','p','F','o','r','c','e','E','f','f','e','c','t',0};
#endif
#endif /* RUNTIMECLASS_Windows_Gaming_Input_ForceFeedback_RampForceEffect_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */

/*****************************************************************************
 * IAsyncOperationCompletedHandler<ABI::Windows::Gaming::Input::ForceFeedback::ForceFeedbackLoadEffectResult > interface
 */
#ifndef ____FIAsyncOperationCompletedHandler_1_ForceFeedbackLoadEffectResult_INTERFACE_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_ForceFeedbackLoadEffectResult_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperationCompletedHandler_1_ForceFeedbackLoadEffectResult, 0xf8220a41, 0xf738, 0x51e8, 0x89,0xba, 0x76,0xbb,0xd6,0x61,0x58,0xcb);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("f8220a41-f738-51e8-89ba-76bbd66158cb")
            IAsyncOperationCompletedHandler<ABI::Windows::Gaming::Input::ForceFeedback::ForceFeedbackLoadEffectResult > : IAsyncOperationCompletedHandler_impl<ABI::Windows::Gaming::Input::ForceFeedback::ForceFeedbackLoadEffectResult >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperationCompletedHandler_1_ForceFeedbackLoadEffectResult, 0xf8220a41, 0xf738, 0x51e8, 0x89,0xba, 0x76,0xbb,0xd6,0x61,0x58,0xcb)
#endif
#else
typedef struct __FIAsyncOperationCompletedHandler_1_ForceFeedbackLoadEffectResultVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperationCompletedHandler_1_ForceFeedbackLoadEffectResult *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperationCompletedHandler_1_ForceFeedbackLoadEffectResult *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperationCompletedHandler_1_ForceFeedbackLoadEffectResult *This);

    /*** IAsyncOperationCompletedHandler<ABI::Windows::Gaming::Input::ForceFeedback::ForceFeedbackLoadEffectResult > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIAsyncOperationCompletedHandler_1_ForceFeedbackLoadEffectResult *This,
        __FIAsyncOperation_1_ForceFeedbackLoadEffectResult *info,
        AsyncStatus status);

    END_INTERFACE
} __FIAsyncOperationCompletedHandler_1_ForceFeedbackLoadEffectResultVtbl;

interface __FIAsyncOperationCompletedHandler_1_ForceFeedbackLoadEffectResult {
    CONST_VTBL __FIAsyncOperationCompletedHandler_1_ForceFeedbackLoadEffectResultVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperationCompletedHandler_1_ForceFeedbackLoadEffectResult_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperationCompletedHandler_1_ForceFeedbackLoadEffectResult_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperationCompletedHandler_1_ForceFeedbackLoadEffectResult_Release(This) (This)->lpVtbl->Release(This)
/*** IAsyncOperationCompletedHandler<ABI::Windows::Gaming::Input::ForceFeedback::ForceFeedbackLoadEffectResult > methods ***/
#define __FIAsyncOperationCompletedHandler_1_ForceFeedbackLoadEffectResult_Invoke(This,info,status) (This)->lpVtbl->Invoke(This,info,status)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_ForceFeedbackLoadEffectResult_QueryInterface(__FIAsyncOperationCompletedHandler_1_ForceFeedbackLoadEffectResult* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_ForceFeedbackLoadEffectResult_AddRef(__FIAsyncOperationCompletedHandler_1_ForceFeedbackLoadEffectResult* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_ForceFeedbackLoadEffectResult_Release(__FIAsyncOperationCompletedHandler_1_ForceFeedbackLoadEffectResult* This) {
    return This->lpVtbl->Release(This);
}
/*** IAsyncOperationCompletedHandler<ABI::Windows::Gaming::Input::ForceFeedback::ForceFeedbackLoadEffectResult > methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_ForceFeedbackLoadEffectResult_Invoke(__FIAsyncOperationCompletedHandler_1_ForceFeedbackLoadEffectResult* This,__FIAsyncOperation_1_ForceFeedbackLoadEffectResult *info,AsyncStatus status) {
    return This->lpVtbl->Invoke(This,info,status);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperationCompletedHandler_ForceFeedbackLoadEffectResult IID___FIAsyncOperationCompletedHandler_1_ForceFeedbackLoadEffectResult
#define IAsyncOperationCompletedHandler_ForceFeedbackLoadEffectResultVtbl __FIAsyncOperationCompletedHandler_1_ForceFeedbackLoadEffectResultVtbl
#define IAsyncOperationCompletedHandler_ForceFeedbackLoadEffectResult __FIAsyncOperationCompletedHandler_1_ForceFeedbackLoadEffectResult
#define IAsyncOperationCompletedHandler_ForceFeedbackLoadEffectResult_QueryInterface __FIAsyncOperationCompletedHandler_1_ForceFeedbackLoadEffectResult_QueryInterface
#define IAsyncOperationCompletedHandler_ForceFeedbackLoadEffectResult_AddRef __FIAsyncOperationCompletedHandler_1_ForceFeedbackLoadEffectResult_AddRef
#define IAsyncOperationCompletedHandler_ForceFeedbackLoadEffectResult_Release __FIAsyncOperationCompletedHandler_1_ForceFeedbackLoadEffectResult_Release
#define IAsyncOperationCompletedHandler_ForceFeedbackLoadEffectResult_Invoke __FIAsyncOperationCompletedHandler_1_ForceFeedbackLoadEffectResult_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperationCompletedHandler_1_ForceFeedbackLoadEffectResult_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperation<ABI::Windows::Gaming::Input::ForceFeedback::ForceFeedbackLoadEffectResult > interface
 */
#ifndef ____FIAsyncOperation_1_ForceFeedbackLoadEffectResult_INTERFACE_DEFINED__
#define ____FIAsyncOperation_1_ForceFeedbackLoadEffectResult_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperation_1_ForceFeedbackLoadEffectResult, 0x21f834fc, 0xe845, 0x5ab9, 0xbf,0x85, 0x95,0x34,0xe2,0x39,0x77,0x98);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("21f834fc-e845-5ab9-bf85-9534e2397798")
            IAsyncOperation<ABI::Windows::Gaming::Input::ForceFeedback::ForceFeedbackLoadEffectResult > : IAsyncOperation_impl<ABI::Windows::Gaming::Input::ForceFeedback::ForceFeedbackLoadEffectResult >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperation_1_ForceFeedbackLoadEffectResult, 0x21f834fc, 0xe845, 0x5ab9, 0xbf,0x85, 0x95,0x34,0xe2,0x39,0x77,0x98)
#endif
#else
typedef struct __FIAsyncOperation_1_ForceFeedbackLoadEffectResultVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperation_1_ForceFeedbackLoadEffectResult *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperation_1_ForceFeedbackLoadEffectResult *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperation_1_ForceFeedbackLoadEffectResult *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIAsyncOperation_1_ForceFeedbackLoadEffectResult *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIAsyncOperation_1_ForceFeedbackLoadEffectResult *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIAsyncOperation_1_ForceFeedbackLoadEffectResult *This,
        TrustLevel *trustLevel);

    /*** IAsyncOperation<ABI::Windows::Gaming::Input::ForceFeedback::ForceFeedbackLoadEffectResult > methods ***/
    HRESULT (STDMETHODCALLTYPE *put_Completed)(
        __FIAsyncOperation_1_ForceFeedbackLoadEffectResult *This,
        __FIAsyncOperationCompletedHandler_1_ForceFeedbackLoadEffectResult *handler);

    HRESULT (STDMETHODCALLTYPE *get_Completed)(
        __FIAsyncOperation_1_ForceFeedbackLoadEffectResult *This,
        __FIAsyncOperationCompletedHandler_1_ForceFeedbackLoadEffectResult **handler);

    HRESULT (STDMETHODCALLTYPE *GetResults)(
        __FIAsyncOperation_1_ForceFeedbackLoadEffectResult *This,
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CForceFeedbackLoadEffectResult *results);

    END_INTERFACE
} __FIAsyncOperation_1_ForceFeedbackLoadEffectResultVtbl;

interface __FIAsyncOperation_1_ForceFeedbackLoadEffectResult {
    CONST_VTBL __FIAsyncOperation_1_ForceFeedbackLoadEffectResultVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperation_1_ForceFeedbackLoadEffectResult_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperation_1_ForceFeedbackLoadEffectResult_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperation_1_ForceFeedbackLoadEffectResult_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIAsyncOperation_1_ForceFeedbackLoadEffectResult_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIAsyncOperation_1_ForceFeedbackLoadEffectResult_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIAsyncOperation_1_ForceFeedbackLoadEffectResult_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAsyncOperation<ABI::Windows::Gaming::Input::ForceFeedback::ForceFeedbackLoadEffectResult > methods ***/
#define __FIAsyncOperation_1_ForceFeedbackLoadEffectResult_put_Completed(This,handler) (This)->lpVtbl->put_Completed(This,handler)
#define __FIAsyncOperation_1_ForceFeedbackLoadEffectResult_get_Completed(This,handler) (This)->lpVtbl->get_Completed(This,handler)
#define __FIAsyncOperation_1_ForceFeedbackLoadEffectResult_GetResults(This,results) (This)->lpVtbl->GetResults(This,results)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperation_1_ForceFeedbackLoadEffectResult_QueryInterface(__FIAsyncOperation_1_ForceFeedbackLoadEffectResult* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperation_1_ForceFeedbackLoadEffectResult_AddRef(__FIAsyncOperation_1_ForceFeedbackLoadEffectResult* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperation_1_ForceFeedbackLoadEffectResult_Release(__FIAsyncOperation_1_ForceFeedbackLoadEffectResult* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIAsyncOperation_1_ForceFeedbackLoadEffectResult_GetIids(__FIAsyncOperation_1_ForceFeedbackLoadEffectResult* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIAsyncOperation_1_ForceFeedbackLoadEffectResult_GetRuntimeClassName(__FIAsyncOperation_1_ForceFeedbackLoadEffectResult* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIAsyncOperation_1_ForceFeedbackLoadEffectResult_GetTrustLevel(__FIAsyncOperation_1_ForceFeedbackLoadEffectResult* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAsyncOperation<ABI::Windows::Gaming::Input::ForceFeedback::ForceFeedbackLoadEffectResult > methods ***/
static inline HRESULT __FIAsyncOperation_1_ForceFeedbackLoadEffectResult_put_Completed(__FIAsyncOperation_1_ForceFeedbackLoadEffectResult* This,__FIAsyncOperationCompletedHandler_1_ForceFeedbackLoadEffectResult *handler) {
    return This->lpVtbl->put_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_ForceFeedbackLoadEffectResult_get_Completed(__FIAsyncOperation_1_ForceFeedbackLoadEffectResult* This,__FIAsyncOperationCompletedHandler_1_ForceFeedbackLoadEffectResult **handler) {
    return This->lpVtbl->get_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_ForceFeedbackLoadEffectResult_GetResults(__FIAsyncOperation_1_ForceFeedbackLoadEffectResult* This,__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CForceFeedbackLoadEffectResult *results) {
    return This->lpVtbl->GetResults(This,results);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperation_ForceFeedbackLoadEffectResult IID___FIAsyncOperation_1_ForceFeedbackLoadEffectResult
#define IAsyncOperation_ForceFeedbackLoadEffectResultVtbl __FIAsyncOperation_1_ForceFeedbackLoadEffectResultVtbl
#define IAsyncOperation_ForceFeedbackLoadEffectResult __FIAsyncOperation_1_ForceFeedbackLoadEffectResult
#define IAsyncOperation_ForceFeedbackLoadEffectResult_QueryInterface __FIAsyncOperation_1_ForceFeedbackLoadEffectResult_QueryInterface
#define IAsyncOperation_ForceFeedbackLoadEffectResult_AddRef __FIAsyncOperation_1_ForceFeedbackLoadEffectResult_AddRef
#define IAsyncOperation_ForceFeedbackLoadEffectResult_Release __FIAsyncOperation_1_ForceFeedbackLoadEffectResult_Release
#define IAsyncOperation_ForceFeedbackLoadEffectResult_GetIids __FIAsyncOperation_1_ForceFeedbackLoadEffectResult_GetIids
#define IAsyncOperation_ForceFeedbackLoadEffectResult_GetRuntimeClassName __FIAsyncOperation_1_ForceFeedbackLoadEffectResult_GetRuntimeClassName
#define IAsyncOperation_ForceFeedbackLoadEffectResult_GetTrustLevel __FIAsyncOperation_1_ForceFeedbackLoadEffectResult_GetTrustLevel
#define IAsyncOperation_ForceFeedbackLoadEffectResult_put_Completed __FIAsyncOperation_1_ForceFeedbackLoadEffectResult_put_Completed
#define IAsyncOperation_ForceFeedbackLoadEffectResult_get_Completed __FIAsyncOperation_1_ForceFeedbackLoadEffectResult_get_Completed
#define IAsyncOperation_ForceFeedbackLoadEffectResult_GetResults __FIAsyncOperation_1_ForceFeedbackLoadEffectResult_GetResults
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperation_1_ForceFeedbackLoadEffectResult_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IIterator<ABI::Windows::Gaming::Input::ForceFeedback::ForceFeedbackMotor* > interface
 */
#ifndef ____FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_INTERFACE_DEFINED__
#define ____FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor, 0x64cf69e0, 0x5464, 0x5b72, 0xbd,0x4b, 0x82,0xf7,0xc3,0xd0,0x38,0x6d);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("64cf69e0-5464-5b72-bd4b-82f7c3d0386d")
                IIterator<ABI::Windows::Gaming::Input::ForceFeedback::ForceFeedbackMotor* > : IIterator_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Gaming::Input::ForceFeedback::ForceFeedbackMotor*, ABI::Windows::Gaming::Input::ForceFeedback::IForceFeedbackMotor* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor, 0x64cf69e0, 0x5464, 0x5b72, 0xbd,0x4b, 0x82,0xf7,0xc3,0xd0,0x38,0x6d)
#endif
#else
typedef struct __FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor *This,
        TrustLevel *trustLevel);

    /*** IIterator<ABI::Windows::Gaming::Input::ForceFeedback::ForceFeedbackMotor* > methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Current)(
        __FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor *This,
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor **value);

    HRESULT (STDMETHODCALLTYPE *get_HasCurrent)(
        __FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *MoveNext)(
        __FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor *This,
        UINT32 items_size,
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor **items,
        UINT32 *value);

    END_INTERFACE
} __FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotorVtbl;

interface __FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor {
    CONST_VTBL __FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterator<ABI::Windows::Gaming::Input::ForceFeedback::ForceFeedbackMotor* > methods ***/
#define __FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_get_Current(This,value) (This)->lpVtbl->get_Current(This,value)
#define __FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_get_HasCurrent(This,value) (This)->lpVtbl->get_HasCurrent(This,value)
#define __FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_MoveNext(This,value) (This)->lpVtbl->MoveNext(This,value)
#define __FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_GetMany(This,items_size,items,value) (This)->lpVtbl->GetMany(This,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_QueryInterface(__FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_AddRef(__FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_Release(__FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_GetIids(__FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_GetRuntimeClassName(__FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_GetTrustLevel(__FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterator<ABI::Windows::Gaming::Input::ForceFeedback::ForceFeedbackMotor* > methods ***/
static inline HRESULT __FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_get_Current(__FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor* This,__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor **value) {
    return This->lpVtbl->get_Current(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_get_HasCurrent(__FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor* This,boolean *value) {
    return This->lpVtbl->get_HasCurrent(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_MoveNext(__FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor* This,boolean *value) {
    return This->lpVtbl->MoveNext(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_GetMany(__FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor* This,UINT32 items_size,__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterator_ForceFeedbackMotor IID___FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor
#define IIterator_ForceFeedbackMotorVtbl __FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotorVtbl
#define IIterator_ForceFeedbackMotor __FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor
#define IIterator_ForceFeedbackMotor_QueryInterface __FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_QueryInterface
#define IIterator_ForceFeedbackMotor_AddRef __FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_AddRef
#define IIterator_ForceFeedbackMotor_Release __FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_Release
#define IIterator_ForceFeedbackMotor_GetIids __FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_GetIids
#define IIterator_ForceFeedbackMotor_GetRuntimeClassName __FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_GetRuntimeClassName
#define IIterator_ForceFeedbackMotor_GetTrustLevel __FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_GetTrustLevel
#define IIterator_ForceFeedbackMotor_get_Current __FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_get_Current
#define IIterator_ForceFeedbackMotor_get_HasCurrent __FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_get_HasCurrent
#define IIterator_ForceFeedbackMotor_MoveNext __FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_MoveNext
#define IIterator_ForceFeedbackMotor_GetMany __FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IIterable<ABI::Windows::Gaming::Input::ForceFeedback::ForceFeedbackMotor* > interface
 */
#ifndef ____FIIterable_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_INTERFACE_DEFINED__
#define ____FIIterable_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterable_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor, 0xc14440d1, 0xfea0, 0x5147, 0xae,0xd8, 0x9b,0x85,0x23,0x9d,0xa8,0x82);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("c14440d1-fea0-5147-aed8-9b85239da882")
                IIterable<ABI::Windows::Gaming::Input::ForceFeedback::ForceFeedbackMotor* > : IIterable_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Gaming::Input::ForceFeedback::ForceFeedbackMotor*, ABI::Windows::Gaming::Input::ForceFeedback::IForceFeedbackMotor* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterable_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor, 0xc14440d1, 0xfea0, 0x5147, 0xae,0xd8, 0x9b,0x85,0x23,0x9d,0xa8,0x82)
#endif
#else
typedef struct __FIIterable_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterable_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterable_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterable_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterable_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterable_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterable_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor *This,
        TrustLevel *trustLevel);

    /*** IIterable<ABI::Windows::Gaming::Input::ForceFeedback::ForceFeedbackMotor* > methods ***/
    HRESULT (STDMETHODCALLTYPE *First)(
        __FIIterable_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor *This,
        __FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor **value);

    END_INTERFACE
} __FIIterable_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotorVtbl;

interface __FIIterable_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor {
    CONST_VTBL __FIIterable_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterable_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterable_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterable_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterable_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterable_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterable_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterable<ABI::Windows::Gaming::Input::ForceFeedback::ForceFeedbackMotor* > methods ***/
#define __FIIterable_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_First(This,value) (This)->lpVtbl->First(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterable_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_QueryInterface(__FIIterable_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterable_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_AddRef(__FIIterable_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterable_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_Release(__FIIterable_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterable_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_GetIids(__FIIterable_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterable_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_GetRuntimeClassName(__FIIterable_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterable_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_GetTrustLevel(__FIIterable_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterable<ABI::Windows::Gaming::Input::ForceFeedback::ForceFeedbackMotor* > methods ***/
static inline HRESULT __FIIterable_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_First(__FIIterable_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor* This,__FIIterator_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor **value) {
    return This->lpVtbl->First(This,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterable_ForceFeedbackMotor IID___FIIterable_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor
#define IIterable_ForceFeedbackMotorVtbl __FIIterable_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotorVtbl
#define IIterable_ForceFeedbackMotor __FIIterable_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor
#define IIterable_ForceFeedbackMotor_QueryInterface __FIIterable_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_QueryInterface
#define IIterable_ForceFeedbackMotor_AddRef __FIIterable_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_AddRef
#define IIterable_ForceFeedbackMotor_Release __FIIterable_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_Release
#define IIterable_ForceFeedbackMotor_GetIids __FIIterable_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_GetIids
#define IIterable_ForceFeedbackMotor_GetRuntimeClassName __FIIterable_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_GetRuntimeClassName
#define IIterable_ForceFeedbackMotor_GetTrustLevel __FIIterable_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_GetTrustLevel
#define IIterable_ForceFeedbackMotor_First __FIIterable_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_First
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterable_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVectorView<ABI::Windows::Gaming::Input::ForceFeedback::ForceFeedbackMotor* > interface
 */
#ifndef ____FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_INTERFACE_DEFINED__
#define ____FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor, 0x5bfc5070, 0x101d, 0x5fbb, 0x8d,0x5f, 0xce,0x5c,0x23,0xbe,0xcd,0xd9);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("5bfc5070-101d-5fbb-8d5f-ce5c23becdd9")
                IVectorView<ABI::Windows::Gaming::Input::ForceFeedback::ForceFeedbackMotor* > : IVectorView_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Gaming::Input::ForceFeedback::ForceFeedbackMotor*, ABI::Windows::Gaming::Input::ForceFeedback::IForceFeedbackMotor* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor, 0x5bfc5070, 0x101d, 0x5fbb, 0x8d,0x5f, 0xce,0x5c,0x23,0xbe,0xcd,0xd9)
#endif
#else
typedef struct __FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor *This,
        TrustLevel *trustLevel);

    /*** IVectorView<ABI::Windows::Gaming::Input::ForceFeedback::ForceFeedbackMotor* > methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAt)(
        __FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor *This,
        UINT32 index,
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *IndexOf)(
        __FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor *This,
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor *element,
        UINT32 *index,
        BOOLEAN *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor *This,
        UINT32 start_index,
        UINT32 items_size,
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor **items,
        UINT32 *value);

    END_INTERFACE
} __FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotorVtbl;

interface __FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor {
    CONST_VTBL __FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IVectorView<ABI::Windows::Gaming::Input::ForceFeedback::ForceFeedbackMotor* > methods ***/
#define __FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_GetAt(This,index,value) (This)->lpVtbl->GetAt(This,index,value)
#define __FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_get_Size(This,value) (This)->lpVtbl->get_Size(This,value)
#define __FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_IndexOf(This,element,index,value) (This)->lpVtbl->IndexOf(This,element,index,value)
#define __FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_GetMany(This,start_index,items_size,items,value) (This)->lpVtbl->GetMany(This,start_index,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_QueryInterface(__FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_AddRef(__FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_Release(__FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_GetIids(__FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_GetRuntimeClassName(__FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_GetTrustLevel(__FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IVectorView<ABI::Windows::Gaming::Input::ForceFeedback::ForceFeedbackMotor* > methods ***/
static inline HRESULT __FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_GetAt(__FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor* This,UINT32 index,__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor **value) {
    return This->lpVtbl->GetAt(This,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_get_Size(__FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor* This,UINT32 *value) {
    return This->lpVtbl->get_Size(This,value);
}
static inline HRESULT __FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_IndexOf(__FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor* This,__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor *element,UINT32 *index,BOOLEAN *value) {
    return This->lpVtbl->IndexOf(This,element,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_GetMany(__FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor* This,UINT32 start_index,UINT32 items_size,__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,start_index,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IVectorView_ForceFeedbackMotor IID___FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor
#define IVectorView_ForceFeedbackMotorVtbl __FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotorVtbl
#define IVectorView_ForceFeedbackMotor __FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor
#define IVectorView_ForceFeedbackMotor_QueryInterface __FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_QueryInterface
#define IVectorView_ForceFeedbackMotor_AddRef __FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_AddRef
#define IVectorView_ForceFeedbackMotor_Release __FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_Release
#define IVectorView_ForceFeedbackMotor_GetIids __FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_GetIids
#define IVectorView_ForceFeedbackMotor_GetRuntimeClassName __FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_GetRuntimeClassName
#define IVectorView_ForceFeedbackMotor_GetTrustLevel __FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_GetTrustLevel
#define IVectorView_ForceFeedbackMotor_GetAt __FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_GetAt
#define IVectorView_ForceFeedbackMotor_get_Size __FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_get_Size
#define IVectorView_ForceFeedbackMotor_IndexOf __FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_IndexOf
#define IVectorView_ForceFeedbackMotor_GetMany __FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVector<ABI::Windows::Gaming::Input::ForceFeedback::ForceFeedbackMotor* > interface
 */
#ifndef ____FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_INTERFACE_DEFINED__
#define ____FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor, 0xb695f739, 0x4ebe, 0x5fd0, 0xa1,0xff, 0x5b,0xa3,0x23,0xb1,0x63,0x55);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("b695f739-4ebe-5fd0-a1ff-5ba323b16355")
                IVector<ABI::Windows::Gaming::Input::ForceFeedback::ForceFeedbackMotor* > : IVector_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Gaming::Input::ForceFeedback::ForceFeedbackMotor*, ABI::Windows::Gaming::Input::ForceFeedback::IForceFeedbackMotor* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor, 0xb695f739, 0x4ebe, 0x5fd0, 0xa1,0xff, 0x5b,0xa3,0x23,0xb1,0x63,0x55)
#endif
#else
typedef struct __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor *This,
        TrustLevel *trustLevel);

    /*** IVector<ABI::Windows::Gaming::Input::ForceFeedback::ForceFeedbackMotor* > methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAt)(
        __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor *This,
        UINT32 index,
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *GetView)(
        __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor *This,
        __FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor **value);

    HRESULT (STDMETHODCALLTYPE *IndexOf)(
        __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor *This,
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor *element,
        UINT32 *index,
        BOOLEAN *value);

    HRESULT (STDMETHODCALLTYPE *SetAt)(
        __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor *This,
        UINT32 index,
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor *value);

    HRESULT (STDMETHODCALLTYPE *InsertAt)(
        __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor *This,
        UINT32 index,
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor *value);

    HRESULT (STDMETHODCALLTYPE *RemoveAt)(
        __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor *This,
        UINT32 index);

    HRESULT (STDMETHODCALLTYPE *Append)(
        __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor *This,
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor *value);

    HRESULT (STDMETHODCALLTYPE *RemoveAtEnd)(
        __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor *This);

    HRESULT (STDMETHODCALLTYPE *Clear)(
        __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor *This);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor *This,
        UINT32 start_index,
        UINT32 items_size,
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor **items,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *ReplaceAll)(
        __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor *This,
        UINT32 count,
        __x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor **items);

    END_INTERFACE
} __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotorVtbl;

interface __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor {
    CONST_VTBL __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IVector<ABI::Windows::Gaming::Input::ForceFeedback::ForceFeedbackMotor* > methods ***/
#define __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_GetAt(This,index,value) (This)->lpVtbl->GetAt(This,index,value)
#define __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_get_Size(This,value) (This)->lpVtbl->get_Size(This,value)
#define __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_GetView(This,value) (This)->lpVtbl->GetView(This,value)
#define __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_IndexOf(This,element,index,value) (This)->lpVtbl->IndexOf(This,element,index,value)
#define __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_SetAt(This,index,value) (This)->lpVtbl->SetAt(This,index,value)
#define __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_InsertAt(This,index,value) (This)->lpVtbl->InsertAt(This,index,value)
#define __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_RemoveAt(This,index) (This)->lpVtbl->RemoveAt(This,index)
#define __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_Append(This,value) (This)->lpVtbl->Append(This,value)
#define __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_RemoveAtEnd(This) (This)->lpVtbl->RemoveAtEnd(This)
#define __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_Clear(This) (This)->lpVtbl->Clear(This)
#define __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_GetMany(This,start_index,items_size,items,value) (This)->lpVtbl->GetMany(This,start_index,items_size,items,value)
#define __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_ReplaceAll(This,count,items) (This)->lpVtbl->ReplaceAll(This,count,items)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_QueryInterface(__FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_AddRef(__FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_Release(__FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_GetIids(__FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_GetRuntimeClassName(__FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_GetTrustLevel(__FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IVector<ABI::Windows::Gaming::Input::ForceFeedback::ForceFeedbackMotor* > methods ***/
static inline HRESULT __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_GetAt(__FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor* This,UINT32 index,__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor **value) {
    return This->lpVtbl->GetAt(This,index,value);
}
static inline HRESULT __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_get_Size(__FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor* This,UINT32 *value) {
    return This->lpVtbl->get_Size(This,value);
}
static inline HRESULT __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_GetView(__FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor* This,__FIVectorView_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor **value) {
    return This->lpVtbl->GetView(This,value);
}
static inline HRESULT __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_IndexOf(__FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor* This,__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor *element,UINT32 *index,BOOLEAN *value) {
    return This->lpVtbl->IndexOf(This,element,index,value);
}
static inline HRESULT __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_SetAt(__FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor* This,UINT32 index,__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor *value) {
    return This->lpVtbl->SetAt(This,index,value);
}
static inline HRESULT __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_InsertAt(__FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor* This,UINT32 index,__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor *value) {
    return This->lpVtbl->InsertAt(This,index,value);
}
static inline HRESULT __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_RemoveAt(__FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor* This,UINT32 index) {
    return This->lpVtbl->RemoveAt(This,index);
}
static inline HRESULT __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_Append(__FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor* This,__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor *value) {
    return This->lpVtbl->Append(This,value);
}
static inline HRESULT __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_RemoveAtEnd(__FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor* This) {
    return This->lpVtbl->RemoveAtEnd(This);
}
static inline HRESULT __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_Clear(__FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor* This) {
    return This->lpVtbl->Clear(This);
}
static inline HRESULT __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_GetMany(__FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor* This,UINT32 start_index,UINT32 items_size,__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,start_index,items_size,items,value);
}
static inline HRESULT __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_ReplaceAll(__FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor* This,UINT32 count,__x_ABI_CWindows_CGaming_CInput_CForceFeedback_CIForceFeedbackMotor **items) {
    return This->lpVtbl->ReplaceAll(This,count,items);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IVector_ForceFeedbackMotor IID___FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor
#define IVector_ForceFeedbackMotorVtbl __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotorVtbl
#define IVector_ForceFeedbackMotor __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor
#define IVector_ForceFeedbackMotor_QueryInterface __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_QueryInterface
#define IVector_ForceFeedbackMotor_AddRef __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_AddRef
#define IVector_ForceFeedbackMotor_Release __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_Release
#define IVector_ForceFeedbackMotor_GetIids __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_GetIids
#define IVector_ForceFeedbackMotor_GetRuntimeClassName __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_GetRuntimeClassName
#define IVector_ForceFeedbackMotor_GetTrustLevel __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_GetTrustLevel
#define IVector_ForceFeedbackMotor_GetAt __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_GetAt
#define IVector_ForceFeedbackMotor_get_Size __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_get_Size
#define IVector_ForceFeedbackMotor_GetView __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_GetView
#define IVector_ForceFeedbackMotor_IndexOf __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_IndexOf
#define IVector_ForceFeedbackMotor_SetAt __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_SetAt
#define IVector_ForceFeedbackMotor_InsertAt __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_InsertAt
#define IVector_ForceFeedbackMotor_RemoveAt __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_RemoveAt
#define IVector_ForceFeedbackMotor_Append __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_Append
#define IVector_ForceFeedbackMotor_RemoveAtEnd __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_RemoveAtEnd
#define IVector_ForceFeedbackMotor_Clear __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_Clear
#define IVector_ForceFeedbackMotor_GetMany __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_GetMany
#define IVector_ForceFeedbackMotor_ReplaceAll __FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_ReplaceAll
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIVector_1_Windows__CGaming__CInput__CForceFeedback__CForceFeedbackMotor_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __windows_gaming_input_forcefeedback_h__ */
