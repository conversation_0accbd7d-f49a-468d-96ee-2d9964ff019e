.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crt_set_subject_alternative_name" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crt_set_subject_alternative_name \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crt_set_subject_alternative_name(gnutls_x509_crt_t " crt ", gnutls_x509_subject_alt_name_t " type ", const char * " data_string ");"
.SH ARGUMENTS
.IP "gnutls_x509_crt_t crt" 12
a certificate of type \fBgnutls_x509_crt_t\fP
.IP "gnutls_x509_subject_alt_name_t type" 12
is one of the gnutls_x509_subject_alt_name_t enumerations
.IP "const char * data_string" 12
The data to be set, a (0) terminated string
.SH "DESCRIPTION"
This function will set the subject alternative name certificate
extension. This function assumes that data can be expressed as a null
terminated string.

The name of the function is unfortunate since it is inconsistent with
\fBgnutls_x509_crt_get_subject_alt_name()\fP.

See \fBgnutls_x509_crt_set_subject_alt_name()\fP for more information.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
