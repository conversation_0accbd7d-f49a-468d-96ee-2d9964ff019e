// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/framework/cpp_shape_inference.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fcpp_5fshape_5finference_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fcpp_5fshape_5finference_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/full_type.pb.h"
#include "tensorflow/core/framework/tensor_shape.pb.h"
#include "tensorflow/core/framework/types.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fframework_2fcpp_5fshape_5finference_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fframework_2fcpp_5fshape_5finference_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fframework_2fcpp_5fshape_5finference_2eproto;
namespace tensorflow {
namespace core {
class CppShapeInferenceInputsNeeded;
struct CppShapeInferenceInputsNeededDefaultTypeInternal;
extern CppShapeInferenceInputsNeededDefaultTypeInternal _CppShapeInferenceInputsNeeded_default_instance_;
class CppShapeInferenceResult;
struct CppShapeInferenceResultDefaultTypeInternal;
extern CppShapeInferenceResultDefaultTypeInternal _CppShapeInferenceResult_default_instance_;
class CppShapeInferenceResult_HandleData;
struct CppShapeInferenceResult_HandleDataDefaultTypeInternal;
extern CppShapeInferenceResult_HandleDataDefaultTypeInternal _CppShapeInferenceResult_HandleData_default_instance_;
class CppShapeInferenceResult_HandleShapeAndType;
struct CppShapeInferenceResult_HandleShapeAndTypeDefaultTypeInternal;
extern CppShapeInferenceResult_HandleShapeAndTypeDefaultTypeInternal _CppShapeInferenceResult_HandleShapeAndType_default_instance_;
}  // namespace core
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::core::CppShapeInferenceInputsNeeded* Arena::CreateMaybeMessage<::tensorflow::core::CppShapeInferenceInputsNeeded>(Arena*);
template<> ::tensorflow::core::CppShapeInferenceResult* Arena::CreateMaybeMessage<::tensorflow::core::CppShapeInferenceResult>(Arena*);
template<> ::tensorflow::core::CppShapeInferenceResult_HandleData* Arena::CreateMaybeMessage<::tensorflow::core::CppShapeInferenceResult_HandleData>(Arena*);
template<> ::tensorflow::core::CppShapeInferenceResult_HandleShapeAndType* Arena::CreateMaybeMessage<::tensorflow::core::CppShapeInferenceResult_HandleShapeAndType>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {
namespace core {

// ===================================================================

class CppShapeInferenceResult_HandleShapeAndType final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.core.CppShapeInferenceResult.HandleShapeAndType) */ {
 public:
  inline CppShapeInferenceResult_HandleShapeAndType() : CppShapeInferenceResult_HandleShapeAndType(nullptr) {}
  ~CppShapeInferenceResult_HandleShapeAndType() override;
  explicit PROTOBUF_CONSTEXPR CppShapeInferenceResult_HandleShapeAndType(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CppShapeInferenceResult_HandleShapeAndType(const CppShapeInferenceResult_HandleShapeAndType& from);
  CppShapeInferenceResult_HandleShapeAndType(CppShapeInferenceResult_HandleShapeAndType&& from) noexcept
    : CppShapeInferenceResult_HandleShapeAndType() {
    *this = ::std::move(from);
  }

  inline CppShapeInferenceResult_HandleShapeAndType& operator=(const CppShapeInferenceResult_HandleShapeAndType& from) {
    CopyFrom(from);
    return *this;
  }
  inline CppShapeInferenceResult_HandleShapeAndType& operator=(CppShapeInferenceResult_HandleShapeAndType&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CppShapeInferenceResult_HandleShapeAndType& default_instance() {
    return *internal_default_instance();
  }
  static inline const CppShapeInferenceResult_HandleShapeAndType* internal_default_instance() {
    return reinterpret_cast<const CppShapeInferenceResult_HandleShapeAndType*>(
               &_CppShapeInferenceResult_HandleShapeAndType_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(CppShapeInferenceResult_HandleShapeAndType& a, CppShapeInferenceResult_HandleShapeAndType& b) {
    a.Swap(&b);
  }
  inline void Swap(CppShapeInferenceResult_HandleShapeAndType* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CppShapeInferenceResult_HandleShapeAndType* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CppShapeInferenceResult_HandleShapeAndType* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CppShapeInferenceResult_HandleShapeAndType>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CppShapeInferenceResult_HandleShapeAndType& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CppShapeInferenceResult_HandleShapeAndType& from) {
    CppShapeInferenceResult_HandleShapeAndType::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CppShapeInferenceResult_HandleShapeAndType* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.core.CppShapeInferenceResult.HandleShapeAndType";
  }
  protected:
  explicit CppShapeInferenceResult_HandleShapeAndType(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kShapeFieldNumber = 1,
    kTypeFieldNumber = 4,
    kDtypeFieldNumber = 2,
  };
  // .tensorflow.TensorShapeProto shape = 1;
  bool has_shape() const;
  private:
  bool _internal_has_shape() const;
  public:
  void clear_shape();
  const ::tensorflow::TensorShapeProto& shape() const;
  PROTOBUF_NODISCARD ::tensorflow::TensorShapeProto* release_shape();
  ::tensorflow::TensorShapeProto* mutable_shape();
  void set_allocated_shape(::tensorflow::TensorShapeProto* shape);
  private:
  const ::tensorflow::TensorShapeProto& _internal_shape() const;
  ::tensorflow::TensorShapeProto* _internal_mutable_shape();
  public:
  void unsafe_arena_set_allocated_shape(
      ::tensorflow::TensorShapeProto* shape);
  ::tensorflow::TensorShapeProto* unsafe_arena_release_shape();

  // .tensorflow.FullTypeDef type = 4;
  bool has_type() const;
  private:
  bool _internal_has_type() const;
  public:
  void clear_type();
  const ::tensorflow::FullTypeDef& type() const;
  PROTOBUF_NODISCARD ::tensorflow::FullTypeDef* release_type();
  ::tensorflow::FullTypeDef* mutable_type();
  void set_allocated_type(::tensorflow::FullTypeDef* type);
  private:
  const ::tensorflow::FullTypeDef& _internal_type() const;
  ::tensorflow::FullTypeDef* _internal_mutable_type();
  public:
  void unsafe_arena_set_allocated_type(
      ::tensorflow::FullTypeDef* type);
  ::tensorflow::FullTypeDef* unsafe_arena_release_type();

  // .tensorflow.DataType dtype = 2;
  void clear_dtype();
  ::tensorflow::DataType dtype() const;
  void set_dtype(::tensorflow::DataType value);
  private:
  ::tensorflow::DataType _internal_dtype() const;
  void _internal_set_dtype(::tensorflow::DataType value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.core.CppShapeInferenceResult.HandleShapeAndType)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::tensorflow::TensorShapeProto* shape_;
    ::tensorflow::FullTypeDef* type_;
    int dtype_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fcpp_5fshape_5finference_2eproto;
};
// -------------------------------------------------------------------

class CppShapeInferenceResult_HandleData final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.core.CppShapeInferenceResult.HandleData) */ {
 public:
  inline CppShapeInferenceResult_HandleData() : CppShapeInferenceResult_HandleData(nullptr) {}
  ~CppShapeInferenceResult_HandleData() override;
  explicit PROTOBUF_CONSTEXPR CppShapeInferenceResult_HandleData(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CppShapeInferenceResult_HandleData(const CppShapeInferenceResult_HandleData& from);
  CppShapeInferenceResult_HandleData(CppShapeInferenceResult_HandleData&& from) noexcept
    : CppShapeInferenceResult_HandleData() {
    *this = ::std::move(from);
  }

  inline CppShapeInferenceResult_HandleData& operator=(const CppShapeInferenceResult_HandleData& from) {
    CopyFrom(from);
    return *this;
  }
  inline CppShapeInferenceResult_HandleData& operator=(CppShapeInferenceResult_HandleData&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CppShapeInferenceResult_HandleData& default_instance() {
    return *internal_default_instance();
  }
  static inline const CppShapeInferenceResult_HandleData* internal_default_instance() {
    return reinterpret_cast<const CppShapeInferenceResult_HandleData*>(
               &_CppShapeInferenceResult_HandleData_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(CppShapeInferenceResult_HandleData& a, CppShapeInferenceResult_HandleData& b) {
    a.Swap(&b);
  }
  inline void Swap(CppShapeInferenceResult_HandleData* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CppShapeInferenceResult_HandleData* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CppShapeInferenceResult_HandleData* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CppShapeInferenceResult_HandleData>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CppShapeInferenceResult_HandleData& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CppShapeInferenceResult_HandleData& from) {
    CppShapeInferenceResult_HandleData::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CppShapeInferenceResult_HandleData* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.core.CppShapeInferenceResult.HandleData";
  }
  protected:
  explicit CppShapeInferenceResult_HandleData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kShapeAndTypeFieldNumber = 2,
    kIsSetFieldNumber = 1,
  };
  // repeated .tensorflow.core.CppShapeInferenceResult.HandleShapeAndType shape_and_type = 2;
  int shape_and_type_size() const;
  private:
  int _internal_shape_and_type_size() const;
  public:
  void clear_shape_and_type();
  ::tensorflow::core::CppShapeInferenceResult_HandleShapeAndType* mutable_shape_and_type(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::core::CppShapeInferenceResult_HandleShapeAndType >*
      mutable_shape_and_type();
  private:
  const ::tensorflow::core::CppShapeInferenceResult_HandleShapeAndType& _internal_shape_and_type(int index) const;
  ::tensorflow::core::CppShapeInferenceResult_HandleShapeAndType* _internal_add_shape_and_type();
  public:
  const ::tensorflow::core::CppShapeInferenceResult_HandleShapeAndType& shape_and_type(int index) const;
  ::tensorflow::core::CppShapeInferenceResult_HandleShapeAndType* add_shape_and_type();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::core::CppShapeInferenceResult_HandleShapeAndType >&
      shape_and_type() const;

  // bool is_set = 1;
  void clear_is_set();
  bool is_set() const;
  void set_is_set(bool value);
  private:
  bool _internal_is_set() const;
  void _internal_set_is_set(bool value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.core.CppShapeInferenceResult.HandleData)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::core::CppShapeInferenceResult_HandleShapeAndType > shape_and_type_;
    bool is_set_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fcpp_5fshape_5finference_2eproto;
};
// -------------------------------------------------------------------

class CppShapeInferenceResult final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.core.CppShapeInferenceResult) */ {
 public:
  inline CppShapeInferenceResult() : CppShapeInferenceResult(nullptr) {}
  ~CppShapeInferenceResult() override;
  explicit PROTOBUF_CONSTEXPR CppShapeInferenceResult(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CppShapeInferenceResult(const CppShapeInferenceResult& from);
  CppShapeInferenceResult(CppShapeInferenceResult&& from) noexcept
    : CppShapeInferenceResult() {
    *this = ::std::move(from);
  }

  inline CppShapeInferenceResult& operator=(const CppShapeInferenceResult& from) {
    CopyFrom(from);
    return *this;
  }
  inline CppShapeInferenceResult& operator=(CppShapeInferenceResult&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CppShapeInferenceResult& default_instance() {
    return *internal_default_instance();
  }
  static inline const CppShapeInferenceResult* internal_default_instance() {
    return reinterpret_cast<const CppShapeInferenceResult*>(
               &_CppShapeInferenceResult_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(CppShapeInferenceResult& a, CppShapeInferenceResult& b) {
    a.Swap(&b);
  }
  inline void Swap(CppShapeInferenceResult* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CppShapeInferenceResult* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CppShapeInferenceResult* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CppShapeInferenceResult>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CppShapeInferenceResult& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CppShapeInferenceResult& from) {
    CppShapeInferenceResult::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CppShapeInferenceResult* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.core.CppShapeInferenceResult";
  }
  protected:
  explicit CppShapeInferenceResult(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef CppShapeInferenceResult_HandleShapeAndType HandleShapeAndType;
  typedef CppShapeInferenceResult_HandleData HandleData;

  // accessors -------------------------------------------------------

  enum : int {
    kShapeFieldNumber = 1,
    kHandleDataFieldNumber = 4,
  };
  // .tensorflow.TensorShapeProto shape = 1;
  bool has_shape() const;
  private:
  bool _internal_has_shape() const;
  public:
  void clear_shape();
  const ::tensorflow::TensorShapeProto& shape() const;
  PROTOBUF_NODISCARD ::tensorflow::TensorShapeProto* release_shape();
  ::tensorflow::TensorShapeProto* mutable_shape();
  void set_allocated_shape(::tensorflow::TensorShapeProto* shape);
  private:
  const ::tensorflow::TensorShapeProto& _internal_shape() const;
  ::tensorflow::TensorShapeProto* _internal_mutable_shape();
  public:
  void unsafe_arena_set_allocated_shape(
      ::tensorflow::TensorShapeProto* shape);
  ::tensorflow::TensorShapeProto* unsafe_arena_release_shape();

  // .tensorflow.core.CppShapeInferenceResult.HandleData handle_data = 4;
  bool has_handle_data() const;
  private:
  bool _internal_has_handle_data() const;
  public:
  void clear_handle_data();
  const ::tensorflow::core::CppShapeInferenceResult_HandleData& handle_data() const;
  PROTOBUF_NODISCARD ::tensorflow::core::CppShapeInferenceResult_HandleData* release_handle_data();
  ::tensorflow::core::CppShapeInferenceResult_HandleData* mutable_handle_data();
  void set_allocated_handle_data(::tensorflow::core::CppShapeInferenceResult_HandleData* handle_data);
  private:
  const ::tensorflow::core::CppShapeInferenceResult_HandleData& _internal_handle_data() const;
  ::tensorflow::core::CppShapeInferenceResult_HandleData* _internal_mutable_handle_data();
  public:
  void unsafe_arena_set_allocated_handle_data(
      ::tensorflow::core::CppShapeInferenceResult_HandleData* handle_data);
  ::tensorflow::core::CppShapeInferenceResult_HandleData* unsafe_arena_release_handle_data();

  // @@protoc_insertion_point(class_scope:tensorflow.core.CppShapeInferenceResult)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::tensorflow::TensorShapeProto* shape_;
    ::tensorflow::core::CppShapeInferenceResult_HandleData* handle_data_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fcpp_5fshape_5finference_2eproto;
};
// -------------------------------------------------------------------

class CppShapeInferenceInputsNeeded final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.core.CppShapeInferenceInputsNeeded) */ {
 public:
  inline CppShapeInferenceInputsNeeded() : CppShapeInferenceInputsNeeded(nullptr) {}
  ~CppShapeInferenceInputsNeeded() override;
  explicit PROTOBUF_CONSTEXPR CppShapeInferenceInputsNeeded(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CppShapeInferenceInputsNeeded(const CppShapeInferenceInputsNeeded& from);
  CppShapeInferenceInputsNeeded(CppShapeInferenceInputsNeeded&& from) noexcept
    : CppShapeInferenceInputsNeeded() {
    *this = ::std::move(from);
  }

  inline CppShapeInferenceInputsNeeded& operator=(const CppShapeInferenceInputsNeeded& from) {
    CopyFrom(from);
    return *this;
  }
  inline CppShapeInferenceInputsNeeded& operator=(CppShapeInferenceInputsNeeded&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CppShapeInferenceInputsNeeded& default_instance() {
    return *internal_default_instance();
  }
  static inline const CppShapeInferenceInputsNeeded* internal_default_instance() {
    return reinterpret_cast<const CppShapeInferenceInputsNeeded*>(
               &_CppShapeInferenceInputsNeeded_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(CppShapeInferenceInputsNeeded& a, CppShapeInferenceInputsNeeded& b) {
    a.Swap(&b);
  }
  inline void Swap(CppShapeInferenceInputsNeeded* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CppShapeInferenceInputsNeeded* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CppShapeInferenceInputsNeeded* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CppShapeInferenceInputsNeeded>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CppShapeInferenceInputsNeeded& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CppShapeInferenceInputsNeeded& from) {
    CppShapeInferenceInputsNeeded::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CppShapeInferenceInputsNeeded* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.core.CppShapeInferenceInputsNeeded";
  }
  protected:
  explicit CppShapeInferenceInputsNeeded(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kInputTensorsNeededFieldNumber = 1,
    kInputTensorsAsShapesNeededFieldNumber = 2,
  };
  // repeated int32 input_tensors_needed = 1;
  int input_tensors_needed_size() const;
  private:
  int _internal_input_tensors_needed_size() const;
  public:
  void clear_input_tensors_needed();
  private:
  int32_t _internal_input_tensors_needed(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
      _internal_input_tensors_needed() const;
  void _internal_add_input_tensors_needed(int32_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
      _internal_mutable_input_tensors_needed();
  public:
  int32_t input_tensors_needed(int index) const;
  void set_input_tensors_needed(int index, int32_t value);
  void add_input_tensors_needed(int32_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
      input_tensors_needed() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
      mutable_input_tensors_needed();

  // repeated int32 input_tensors_as_shapes_needed = 2;
  int input_tensors_as_shapes_needed_size() const;
  private:
  int _internal_input_tensors_as_shapes_needed_size() const;
  public:
  void clear_input_tensors_as_shapes_needed();
  private:
  int32_t _internal_input_tensors_as_shapes_needed(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
      _internal_input_tensors_as_shapes_needed() const;
  void _internal_add_input_tensors_as_shapes_needed(int32_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
      _internal_mutable_input_tensors_as_shapes_needed();
  public:
  int32_t input_tensors_as_shapes_needed(int index) const;
  void set_input_tensors_as_shapes_needed(int index, int32_t value);
  void add_input_tensors_as_shapes_needed(int32_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
      input_tensors_as_shapes_needed() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
      mutable_input_tensors_as_shapes_needed();

  // @@protoc_insertion_point(class_scope:tensorflow.core.CppShapeInferenceInputsNeeded)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t > input_tensors_needed_;
    mutable std::atomic<int> _input_tensors_needed_cached_byte_size_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t > input_tensors_as_shapes_needed_;
    mutable std::atomic<int> _input_tensors_as_shapes_needed_cached_byte_size_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fcpp_5fshape_5finference_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// CppShapeInferenceResult_HandleShapeAndType

// .tensorflow.TensorShapeProto shape = 1;
inline bool CppShapeInferenceResult_HandleShapeAndType::_internal_has_shape() const {
  return this != internal_default_instance() && _impl_.shape_ != nullptr;
}
inline bool CppShapeInferenceResult_HandleShapeAndType::has_shape() const {
  return _internal_has_shape();
}
inline const ::tensorflow::TensorShapeProto& CppShapeInferenceResult_HandleShapeAndType::_internal_shape() const {
  const ::tensorflow::TensorShapeProto* p = _impl_.shape_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::TensorShapeProto&>(
      ::tensorflow::_TensorShapeProto_default_instance_);
}
inline const ::tensorflow::TensorShapeProto& CppShapeInferenceResult_HandleShapeAndType::shape() const {
  // @@protoc_insertion_point(field_get:tensorflow.core.CppShapeInferenceResult.HandleShapeAndType.shape)
  return _internal_shape();
}
inline void CppShapeInferenceResult_HandleShapeAndType::unsafe_arena_set_allocated_shape(
    ::tensorflow::TensorShapeProto* shape) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.shape_);
  }
  _impl_.shape_ = shape;
  if (shape) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.core.CppShapeInferenceResult.HandleShapeAndType.shape)
}
inline ::tensorflow::TensorShapeProto* CppShapeInferenceResult_HandleShapeAndType::release_shape() {
  
  ::tensorflow::TensorShapeProto* temp = _impl_.shape_;
  _impl_.shape_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::TensorShapeProto* CppShapeInferenceResult_HandleShapeAndType::unsafe_arena_release_shape() {
  // @@protoc_insertion_point(field_release:tensorflow.core.CppShapeInferenceResult.HandleShapeAndType.shape)
  
  ::tensorflow::TensorShapeProto* temp = _impl_.shape_;
  _impl_.shape_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorShapeProto* CppShapeInferenceResult_HandleShapeAndType::_internal_mutable_shape() {
  
  if (_impl_.shape_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorShapeProto>(GetArenaForAllocation());
    _impl_.shape_ = p;
  }
  return _impl_.shape_;
}
inline ::tensorflow::TensorShapeProto* CppShapeInferenceResult_HandleShapeAndType::mutable_shape() {
  ::tensorflow::TensorShapeProto* _msg = _internal_mutable_shape();
  // @@protoc_insertion_point(field_mutable:tensorflow.core.CppShapeInferenceResult.HandleShapeAndType.shape)
  return _msg;
}
inline void CppShapeInferenceResult_HandleShapeAndType::set_allocated_shape(::tensorflow::TensorShapeProto* shape) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.shape_);
  }
  if (shape) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(shape));
    if (message_arena != submessage_arena) {
      shape = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, shape, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.shape_ = shape;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.core.CppShapeInferenceResult.HandleShapeAndType.shape)
}

// .tensorflow.DataType dtype = 2;
inline void CppShapeInferenceResult_HandleShapeAndType::clear_dtype() {
  _impl_.dtype_ = 0;
}
inline ::tensorflow::DataType CppShapeInferenceResult_HandleShapeAndType::_internal_dtype() const {
  return static_cast< ::tensorflow::DataType >(_impl_.dtype_);
}
inline ::tensorflow::DataType CppShapeInferenceResult_HandleShapeAndType::dtype() const {
  // @@protoc_insertion_point(field_get:tensorflow.core.CppShapeInferenceResult.HandleShapeAndType.dtype)
  return _internal_dtype();
}
inline void CppShapeInferenceResult_HandleShapeAndType::_internal_set_dtype(::tensorflow::DataType value) {
  
  _impl_.dtype_ = value;
}
inline void CppShapeInferenceResult_HandleShapeAndType::set_dtype(::tensorflow::DataType value) {
  _internal_set_dtype(value);
  // @@protoc_insertion_point(field_set:tensorflow.core.CppShapeInferenceResult.HandleShapeAndType.dtype)
}

// .tensorflow.FullTypeDef type = 4;
inline bool CppShapeInferenceResult_HandleShapeAndType::_internal_has_type() const {
  return this != internal_default_instance() && _impl_.type_ != nullptr;
}
inline bool CppShapeInferenceResult_HandleShapeAndType::has_type() const {
  return _internal_has_type();
}
inline const ::tensorflow::FullTypeDef& CppShapeInferenceResult_HandleShapeAndType::_internal_type() const {
  const ::tensorflow::FullTypeDef* p = _impl_.type_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::FullTypeDef&>(
      ::tensorflow::_FullTypeDef_default_instance_);
}
inline const ::tensorflow::FullTypeDef& CppShapeInferenceResult_HandleShapeAndType::type() const {
  // @@protoc_insertion_point(field_get:tensorflow.core.CppShapeInferenceResult.HandleShapeAndType.type)
  return _internal_type();
}
inline void CppShapeInferenceResult_HandleShapeAndType::unsafe_arena_set_allocated_type(
    ::tensorflow::FullTypeDef* type) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.type_);
  }
  _impl_.type_ = type;
  if (type) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.core.CppShapeInferenceResult.HandleShapeAndType.type)
}
inline ::tensorflow::FullTypeDef* CppShapeInferenceResult_HandleShapeAndType::release_type() {
  
  ::tensorflow::FullTypeDef* temp = _impl_.type_;
  _impl_.type_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::FullTypeDef* CppShapeInferenceResult_HandleShapeAndType::unsafe_arena_release_type() {
  // @@protoc_insertion_point(field_release:tensorflow.core.CppShapeInferenceResult.HandleShapeAndType.type)
  
  ::tensorflow::FullTypeDef* temp = _impl_.type_;
  _impl_.type_ = nullptr;
  return temp;
}
inline ::tensorflow::FullTypeDef* CppShapeInferenceResult_HandleShapeAndType::_internal_mutable_type() {
  
  if (_impl_.type_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::FullTypeDef>(GetArenaForAllocation());
    _impl_.type_ = p;
  }
  return _impl_.type_;
}
inline ::tensorflow::FullTypeDef* CppShapeInferenceResult_HandleShapeAndType::mutable_type() {
  ::tensorflow::FullTypeDef* _msg = _internal_mutable_type();
  // @@protoc_insertion_point(field_mutable:tensorflow.core.CppShapeInferenceResult.HandleShapeAndType.type)
  return _msg;
}
inline void CppShapeInferenceResult_HandleShapeAndType::set_allocated_type(::tensorflow::FullTypeDef* type) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.type_);
  }
  if (type) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(type));
    if (message_arena != submessage_arena) {
      type = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, type, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.type_ = type;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.core.CppShapeInferenceResult.HandleShapeAndType.type)
}

// -------------------------------------------------------------------

// CppShapeInferenceResult_HandleData

// bool is_set = 1;
inline void CppShapeInferenceResult_HandleData::clear_is_set() {
  _impl_.is_set_ = false;
}
inline bool CppShapeInferenceResult_HandleData::_internal_is_set() const {
  return _impl_.is_set_;
}
inline bool CppShapeInferenceResult_HandleData::is_set() const {
  // @@protoc_insertion_point(field_get:tensorflow.core.CppShapeInferenceResult.HandleData.is_set)
  return _internal_is_set();
}
inline void CppShapeInferenceResult_HandleData::_internal_set_is_set(bool value) {
  
  _impl_.is_set_ = value;
}
inline void CppShapeInferenceResult_HandleData::set_is_set(bool value) {
  _internal_set_is_set(value);
  // @@protoc_insertion_point(field_set:tensorflow.core.CppShapeInferenceResult.HandleData.is_set)
}

// repeated .tensorflow.core.CppShapeInferenceResult.HandleShapeAndType shape_and_type = 2;
inline int CppShapeInferenceResult_HandleData::_internal_shape_and_type_size() const {
  return _impl_.shape_and_type_.size();
}
inline int CppShapeInferenceResult_HandleData::shape_and_type_size() const {
  return _internal_shape_and_type_size();
}
inline void CppShapeInferenceResult_HandleData::clear_shape_and_type() {
  _impl_.shape_and_type_.Clear();
}
inline ::tensorflow::core::CppShapeInferenceResult_HandleShapeAndType* CppShapeInferenceResult_HandleData::mutable_shape_and_type(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.core.CppShapeInferenceResult.HandleData.shape_and_type)
  return _impl_.shape_and_type_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::core::CppShapeInferenceResult_HandleShapeAndType >*
CppShapeInferenceResult_HandleData::mutable_shape_and_type() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.core.CppShapeInferenceResult.HandleData.shape_and_type)
  return &_impl_.shape_and_type_;
}
inline const ::tensorflow::core::CppShapeInferenceResult_HandleShapeAndType& CppShapeInferenceResult_HandleData::_internal_shape_and_type(int index) const {
  return _impl_.shape_and_type_.Get(index);
}
inline const ::tensorflow::core::CppShapeInferenceResult_HandleShapeAndType& CppShapeInferenceResult_HandleData::shape_and_type(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.core.CppShapeInferenceResult.HandleData.shape_and_type)
  return _internal_shape_and_type(index);
}
inline ::tensorflow::core::CppShapeInferenceResult_HandleShapeAndType* CppShapeInferenceResult_HandleData::_internal_add_shape_and_type() {
  return _impl_.shape_and_type_.Add();
}
inline ::tensorflow::core::CppShapeInferenceResult_HandleShapeAndType* CppShapeInferenceResult_HandleData::add_shape_and_type() {
  ::tensorflow::core::CppShapeInferenceResult_HandleShapeAndType* _add = _internal_add_shape_and_type();
  // @@protoc_insertion_point(field_add:tensorflow.core.CppShapeInferenceResult.HandleData.shape_and_type)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::core::CppShapeInferenceResult_HandleShapeAndType >&
CppShapeInferenceResult_HandleData::shape_and_type() const {
  // @@protoc_insertion_point(field_list:tensorflow.core.CppShapeInferenceResult.HandleData.shape_and_type)
  return _impl_.shape_and_type_;
}

// -------------------------------------------------------------------

// CppShapeInferenceResult

// .tensorflow.TensorShapeProto shape = 1;
inline bool CppShapeInferenceResult::_internal_has_shape() const {
  return this != internal_default_instance() && _impl_.shape_ != nullptr;
}
inline bool CppShapeInferenceResult::has_shape() const {
  return _internal_has_shape();
}
inline const ::tensorflow::TensorShapeProto& CppShapeInferenceResult::_internal_shape() const {
  const ::tensorflow::TensorShapeProto* p = _impl_.shape_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::TensorShapeProto&>(
      ::tensorflow::_TensorShapeProto_default_instance_);
}
inline const ::tensorflow::TensorShapeProto& CppShapeInferenceResult::shape() const {
  // @@protoc_insertion_point(field_get:tensorflow.core.CppShapeInferenceResult.shape)
  return _internal_shape();
}
inline void CppShapeInferenceResult::unsafe_arena_set_allocated_shape(
    ::tensorflow::TensorShapeProto* shape) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.shape_);
  }
  _impl_.shape_ = shape;
  if (shape) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.core.CppShapeInferenceResult.shape)
}
inline ::tensorflow::TensorShapeProto* CppShapeInferenceResult::release_shape() {
  
  ::tensorflow::TensorShapeProto* temp = _impl_.shape_;
  _impl_.shape_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::TensorShapeProto* CppShapeInferenceResult::unsafe_arena_release_shape() {
  // @@protoc_insertion_point(field_release:tensorflow.core.CppShapeInferenceResult.shape)
  
  ::tensorflow::TensorShapeProto* temp = _impl_.shape_;
  _impl_.shape_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorShapeProto* CppShapeInferenceResult::_internal_mutable_shape() {
  
  if (_impl_.shape_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorShapeProto>(GetArenaForAllocation());
    _impl_.shape_ = p;
  }
  return _impl_.shape_;
}
inline ::tensorflow::TensorShapeProto* CppShapeInferenceResult::mutable_shape() {
  ::tensorflow::TensorShapeProto* _msg = _internal_mutable_shape();
  // @@protoc_insertion_point(field_mutable:tensorflow.core.CppShapeInferenceResult.shape)
  return _msg;
}
inline void CppShapeInferenceResult::set_allocated_shape(::tensorflow::TensorShapeProto* shape) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.shape_);
  }
  if (shape) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(shape));
    if (message_arena != submessage_arena) {
      shape = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, shape, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.shape_ = shape;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.core.CppShapeInferenceResult.shape)
}

// .tensorflow.core.CppShapeInferenceResult.HandleData handle_data = 4;
inline bool CppShapeInferenceResult::_internal_has_handle_data() const {
  return this != internal_default_instance() && _impl_.handle_data_ != nullptr;
}
inline bool CppShapeInferenceResult::has_handle_data() const {
  return _internal_has_handle_data();
}
inline void CppShapeInferenceResult::clear_handle_data() {
  if (GetArenaForAllocation() == nullptr && _impl_.handle_data_ != nullptr) {
    delete _impl_.handle_data_;
  }
  _impl_.handle_data_ = nullptr;
}
inline const ::tensorflow::core::CppShapeInferenceResult_HandleData& CppShapeInferenceResult::_internal_handle_data() const {
  const ::tensorflow::core::CppShapeInferenceResult_HandleData* p = _impl_.handle_data_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::core::CppShapeInferenceResult_HandleData&>(
      ::tensorflow::core::_CppShapeInferenceResult_HandleData_default_instance_);
}
inline const ::tensorflow::core::CppShapeInferenceResult_HandleData& CppShapeInferenceResult::handle_data() const {
  // @@protoc_insertion_point(field_get:tensorflow.core.CppShapeInferenceResult.handle_data)
  return _internal_handle_data();
}
inline void CppShapeInferenceResult::unsafe_arena_set_allocated_handle_data(
    ::tensorflow::core::CppShapeInferenceResult_HandleData* handle_data) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.handle_data_);
  }
  _impl_.handle_data_ = handle_data;
  if (handle_data) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.core.CppShapeInferenceResult.handle_data)
}
inline ::tensorflow::core::CppShapeInferenceResult_HandleData* CppShapeInferenceResult::release_handle_data() {
  
  ::tensorflow::core::CppShapeInferenceResult_HandleData* temp = _impl_.handle_data_;
  _impl_.handle_data_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::core::CppShapeInferenceResult_HandleData* CppShapeInferenceResult::unsafe_arena_release_handle_data() {
  // @@protoc_insertion_point(field_release:tensorflow.core.CppShapeInferenceResult.handle_data)
  
  ::tensorflow::core::CppShapeInferenceResult_HandleData* temp = _impl_.handle_data_;
  _impl_.handle_data_ = nullptr;
  return temp;
}
inline ::tensorflow::core::CppShapeInferenceResult_HandleData* CppShapeInferenceResult::_internal_mutable_handle_data() {
  
  if (_impl_.handle_data_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::core::CppShapeInferenceResult_HandleData>(GetArenaForAllocation());
    _impl_.handle_data_ = p;
  }
  return _impl_.handle_data_;
}
inline ::tensorflow::core::CppShapeInferenceResult_HandleData* CppShapeInferenceResult::mutable_handle_data() {
  ::tensorflow::core::CppShapeInferenceResult_HandleData* _msg = _internal_mutable_handle_data();
  // @@protoc_insertion_point(field_mutable:tensorflow.core.CppShapeInferenceResult.handle_data)
  return _msg;
}
inline void CppShapeInferenceResult::set_allocated_handle_data(::tensorflow::core::CppShapeInferenceResult_HandleData* handle_data) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.handle_data_;
  }
  if (handle_data) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(handle_data);
    if (message_arena != submessage_arena) {
      handle_data = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, handle_data, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.handle_data_ = handle_data;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.core.CppShapeInferenceResult.handle_data)
}

// -------------------------------------------------------------------

// CppShapeInferenceInputsNeeded

// repeated int32 input_tensors_needed = 1;
inline int CppShapeInferenceInputsNeeded::_internal_input_tensors_needed_size() const {
  return _impl_.input_tensors_needed_.size();
}
inline int CppShapeInferenceInputsNeeded::input_tensors_needed_size() const {
  return _internal_input_tensors_needed_size();
}
inline void CppShapeInferenceInputsNeeded::clear_input_tensors_needed() {
  _impl_.input_tensors_needed_.Clear();
}
inline int32_t CppShapeInferenceInputsNeeded::_internal_input_tensors_needed(int index) const {
  return _impl_.input_tensors_needed_.Get(index);
}
inline int32_t CppShapeInferenceInputsNeeded::input_tensors_needed(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.core.CppShapeInferenceInputsNeeded.input_tensors_needed)
  return _internal_input_tensors_needed(index);
}
inline void CppShapeInferenceInputsNeeded::set_input_tensors_needed(int index, int32_t value) {
  _impl_.input_tensors_needed_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.core.CppShapeInferenceInputsNeeded.input_tensors_needed)
}
inline void CppShapeInferenceInputsNeeded::_internal_add_input_tensors_needed(int32_t value) {
  _impl_.input_tensors_needed_.Add(value);
}
inline void CppShapeInferenceInputsNeeded::add_input_tensors_needed(int32_t value) {
  _internal_add_input_tensors_needed(value);
  // @@protoc_insertion_point(field_add:tensorflow.core.CppShapeInferenceInputsNeeded.input_tensors_needed)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
CppShapeInferenceInputsNeeded::_internal_input_tensors_needed() const {
  return _impl_.input_tensors_needed_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
CppShapeInferenceInputsNeeded::input_tensors_needed() const {
  // @@protoc_insertion_point(field_list:tensorflow.core.CppShapeInferenceInputsNeeded.input_tensors_needed)
  return _internal_input_tensors_needed();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
CppShapeInferenceInputsNeeded::_internal_mutable_input_tensors_needed() {
  return &_impl_.input_tensors_needed_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
CppShapeInferenceInputsNeeded::mutable_input_tensors_needed() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.core.CppShapeInferenceInputsNeeded.input_tensors_needed)
  return _internal_mutable_input_tensors_needed();
}

// repeated int32 input_tensors_as_shapes_needed = 2;
inline int CppShapeInferenceInputsNeeded::_internal_input_tensors_as_shapes_needed_size() const {
  return _impl_.input_tensors_as_shapes_needed_.size();
}
inline int CppShapeInferenceInputsNeeded::input_tensors_as_shapes_needed_size() const {
  return _internal_input_tensors_as_shapes_needed_size();
}
inline void CppShapeInferenceInputsNeeded::clear_input_tensors_as_shapes_needed() {
  _impl_.input_tensors_as_shapes_needed_.Clear();
}
inline int32_t CppShapeInferenceInputsNeeded::_internal_input_tensors_as_shapes_needed(int index) const {
  return _impl_.input_tensors_as_shapes_needed_.Get(index);
}
inline int32_t CppShapeInferenceInputsNeeded::input_tensors_as_shapes_needed(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.core.CppShapeInferenceInputsNeeded.input_tensors_as_shapes_needed)
  return _internal_input_tensors_as_shapes_needed(index);
}
inline void CppShapeInferenceInputsNeeded::set_input_tensors_as_shapes_needed(int index, int32_t value) {
  _impl_.input_tensors_as_shapes_needed_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.core.CppShapeInferenceInputsNeeded.input_tensors_as_shapes_needed)
}
inline void CppShapeInferenceInputsNeeded::_internal_add_input_tensors_as_shapes_needed(int32_t value) {
  _impl_.input_tensors_as_shapes_needed_.Add(value);
}
inline void CppShapeInferenceInputsNeeded::add_input_tensors_as_shapes_needed(int32_t value) {
  _internal_add_input_tensors_as_shapes_needed(value);
  // @@protoc_insertion_point(field_add:tensorflow.core.CppShapeInferenceInputsNeeded.input_tensors_as_shapes_needed)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
CppShapeInferenceInputsNeeded::_internal_input_tensors_as_shapes_needed() const {
  return _impl_.input_tensors_as_shapes_needed_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
CppShapeInferenceInputsNeeded::input_tensors_as_shapes_needed() const {
  // @@protoc_insertion_point(field_list:tensorflow.core.CppShapeInferenceInputsNeeded.input_tensors_as_shapes_needed)
  return _internal_input_tensors_as_shapes_needed();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
CppShapeInferenceInputsNeeded::_internal_mutable_input_tensors_as_shapes_needed() {
  return &_impl_.input_tensors_as_shapes_needed_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
CppShapeInferenceInputsNeeded::mutable_input_tensors_as_shapes_needed() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.core.CppShapeInferenceInputsNeeded.input_tensors_as_shapes_needed)
  return _internal_mutable_input_tensors_as_shapes_needed();
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace core
}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fcpp_5fshape_5finference_2eproto
