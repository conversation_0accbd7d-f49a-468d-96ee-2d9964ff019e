/* Copyright 2019 The TensorFlow Authors. All Rights Reserved.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
==============================================================================*/
#ifndef TENSORFLOW_CORE_PROFILER_UTILS_XPLANE_UTILS_H_
#define TENSORFLOW_CORE_PROFILER_UTILS_XPLANE_UTILS_H_

#include <algorithm>
#include <cstdint>
#include <optional>
#include <vector>

#include "xla/tsl/profiler/utils/xplane_utils.h"

namespace tensorflow {
namespace profiler {

using tsl::profiler::AddFlowsToXplane;               // NOLINT
using tsl::profiler::AggregateXPlane;                // NOLINT
using tsl::profiler::FindLinesWithId;                // NOLINT
using tsl::profiler::FindLineWithId;                 // NOLINT
using tsl::profiler::FindLineWithName;               // NOLINT
using tsl::profiler::FindMutablePlanes;              // NOLINT
using tsl::profiler::FindMutablePlanesWithPrefix;    // NOLINT
using tsl::profiler::FindMutablePlaneWithName;       // NOLINT
using tsl::profiler::FindOrAddMutablePlaneWithName;  // NOLINT
using tsl::profiler::FindOrAddMutableStat;           // NOLINT
using tsl::profiler::FindPlanes;                     // NOLINT
using tsl::profiler::FindPlanesWithNames;            // NOLINT
using tsl::profiler::FindPlanesWithPrefix;           // NOLINT
using tsl::profiler::FindPlaneWithName;              // NOLINT
using tsl::profiler::GetDevicePlaneFingerprint;      // NOLINT
using tsl::profiler::GetSortedEvents;                // NOLINT
using tsl::profiler::GetStartTimestampNs;            // NOLINT
using tsl::profiler::IsEmpty;                        // NOLINT
using tsl::profiler::MergePlanes;                    // NOLINT
using tsl::profiler::NormalizeTimestamps;            // NOLINT
using tsl::profiler::RemoveEmptyLines;               // NOLINT
using tsl::profiler::RemoveEmptyPlanes;              // NOLINT
using tsl::profiler::RemoveEvents;                   // NOLINT
using tsl::profiler::RemoveLine;                     // NOLINT
using tsl::profiler::RemovePlane;                    // NOLINT
using tsl::profiler::RemovePlanes;                   // NOLINT
using tsl::profiler::SortPlanesById;                 // NOLINT
using tsl::profiler::SortXLinesBy;                   // NOLINT
using tsl::profiler::SortXPlane;                     // NOLINT
using tsl::profiler::SortXSpace;                     // NOLINT
using tsl::profiler::XEventContextTracker;           // NOLINT
using tsl::profiler::XEventsComparator;              // NOLINT
using tsl::profiler::XEventTimespan;                 // NOLINT
using tsl::profiler::XLinesComparatorByName;         // NOLINT

}  // namespace profiler
}  // namespace tensorflow

#endif  // TENSORFLOW_CORE_PROFILER_UTILS_XPLANE_UTILS_H_
