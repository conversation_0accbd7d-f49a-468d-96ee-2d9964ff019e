// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/framework/variable.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fvariable_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fvariable_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fframework_2fvariable_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fframework_2fvariable_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fframework_2fvariable_2eproto;
namespace tensorflow {
class SaveSliceInfoDef;
struct SaveSliceInfoDefDefaultTypeInternal;
extern SaveSliceInfoDefDefaultTypeInternal _SaveSliceInfoDef_default_instance_;
class VariableDef;
struct VariableDefDefaultTypeInternal;
extern VariableDefDefaultTypeInternal _VariableDef_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::SaveSliceInfoDef* Arena::CreateMaybeMessage<::tensorflow::SaveSliceInfoDef>(Arena*);
template<> ::tensorflow::VariableDef* Arena::CreateMaybeMessage<::tensorflow::VariableDef>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

enum VariableSynchronization : int {
  VARIABLE_SYNCHRONIZATION_AUTO = 0,
  VARIABLE_SYNCHRONIZATION_NONE = 1,
  VARIABLE_SYNCHRONIZATION_ON_WRITE = 2,
  VARIABLE_SYNCHRONIZATION_ON_READ = 3,
  VariableSynchronization_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  VariableSynchronization_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool VariableSynchronization_IsValid(int value);
constexpr VariableSynchronization VariableSynchronization_MIN = VARIABLE_SYNCHRONIZATION_AUTO;
constexpr VariableSynchronization VariableSynchronization_MAX = VARIABLE_SYNCHRONIZATION_ON_READ;
constexpr int VariableSynchronization_ARRAYSIZE = VariableSynchronization_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* VariableSynchronization_descriptor();
template<typename T>
inline const std::string& VariableSynchronization_Name(T enum_t_value) {
  static_assert(::std::is_same<T, VariableSynchronization>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function VariableSynchronization_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    VariableSynchronization_descriptor(), enum_t_value);
}
inline bool VariableSynchronization_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, VariableSynchronization* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<VariableSynchronization>(
    VariableSynchronization_descriptor(), name, value);
}
enum VariableAggregation : int {
  VARIABLE_AGGREGATION_NONE = 0,
  VARIABLE_AGGREGATION_SUM = 1,
  VARIABLE_AGGREGATION_MEAN = 2,
  VARIABLE_AGGREGATION_ONLY_FIRST_REPLICA = 3,
  VariableAggregation_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  VariableAggregation_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool VariableAggregation_IsValid(int value);
constexpr VariableAggregation VariableAggregation_MIN = VARIABLE_AGGREGATION_NONE;
constexpr VariableAggregation VariableAggregation_MAX = VARIABLE_AGGREGATION_ONLY_FIRST_REPLICA;
constexpr int VariableAggregation_ARRAYSIZE = VariableAggregation_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* VariableAggregation_descriptor();
template<typename T>
inline const std::string& VariableAggregation_Name(T enum_t_value) {
  static_assert(::std::is_same<T, VariableAggregation>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function VariableAggregation_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    VariableAggregation_descriptor(), enum_t_value);
}
inline bool VariableAggregation_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, VariableAggregation* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<VariableAggregation>(
    VariableAggregation_descriptor(), name, value);
}
// ===================================================================

class VariableDef final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.VariableDef) */ {
 public:
  inline VariableDef() : VariableDef(nullptr) {}
  ~VariableDef() override;
  explicit PROTOBUF_CONSTEXPR VariableDef(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  VariableDef(const VariableDef& from);
  VariableDef(VariableDef&& from) noexcept
    : VariableDef() {
    *this = ::std::move(from);
  }

  inline VariableDef& operator=(const VariableDef& from) {
    CopyFrom(from);
    return *this;
  }
  inline VariableDef& operator=(VariableDef&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const VariableDef& default_instance() {
    return *internal_default_instance();
  }
  static inline const VariableDef* internal_default_instance() {
    return reinterpret_cast<const VariableDef*>(
               &_VariableDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(VariableDef& a, VariableDef& b) {
    a.Swap(&b);
  }
  inline void Swap(VariableDef* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(VariableDef* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  VariableDef* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<VariableDef>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const VariableDef& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const VariableDef& from) {
    VariableDef::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(VariableDef* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.VariableDef";
  }
  protected:
  explicit VariableDef(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kVariableNameFieldNumber = 1,
    kInitializerNameFieldNumber = 2,
    kSnapshotNameFieldNumber = 3,
    kInitialValueNameFieldNumber = 6,
    kSaveSliceInfoDefFieldNumber = 4,
    kIsResourceFieldNumber = 5,
    kTrainableFieldNumber = 7,
    kSynchronizationFieldNumber = 8,
    kAggregationFieldNumber = 9,
  };
  // string variable_name = 1;
  void clear_variable_name();
  const std::string& variable_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_variable_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_variable_name();
  PROTOBUF_NODISCARD std::string* release_variable_name();
  void set_allocated_variable_name(std::string* variable_name);
  private:
  const std::string& _internal_variable_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_variable_name(const std::string& value);
  std::string* _internal_mutable_variable_name();
  public:

  // string initializer_name = 2;
  void clear_initializer_name();
  const std::string& initializer_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_initializer_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_initializer_name();
  PROTOBUF_NODISCARD std::string* release_initializer_name();
  void set_allocated_initializer_name(std::string* initializer_name);
  private:
  const std::string& _internal_initializer_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_initializer_name(const std::string& value);
  std::string* _internal_mutable_initializer_name();
  public:

  // string snapshot_name = 3;
  void clear_snapshot_name();
  const std::string& snapshot_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_snapshot_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_snapshot_name();
  PROTOBUF_NODISCARD std::string* release_snapshot_name();
  void set_allocated_snapshot_name(std::string* snapshot_name);
  private:
  const std::string& _internal_snapshot_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_snapshot_name(const std::string& value);
  std::string* _internal_mutable_snapshot_name();
  public:

  // string initial_value_name = 6;
  void clear_initial_value_name();
  const std::string& initial_value_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_initial_value_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_initial_value_name();
  PROTOBUF_NODISCARD std::string* release_initial_value_name();
  void set_allocated_initial_value_name(std::string* initial_value_name);
  private:
  const std::string& _internal_initial_value_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_initial_value_name(const std::string& value);
  std::string* _internal_mutable_initial_value_name();
  public:

  // .tensorflow.SaveSliceInfoDef save_slice_info_def = 4;
  bool has_save_slice_info_def() const;
  private:
  bool _internal_has_save_slice_info_def() const;
  public:
  void clear_save_slice_info_def();
  const ::tensorflow::SaveSliceInfoDef& save_slice_info_def() const;
  PROTOBUF_NODISCARD ::tensorflow::SaveSliceInfoDef* release_save_slice_info_def();
  ::tensorflow::SaveSliceInfoDef* mutable_save_slice_info_def();
  void set_allocated_save_slice_info_def(::tensorflow::SaveSliceInfoDef* save_slice_info_def);
  private:
  const ::tensorflow::SaveSliceInfoDef& _internal_save_slice_info_def() const;
  ::tensorflow::SaveSliceInfoDef* _internal_mutable_save_slice_info_def();
  public:
  void unsafe_arena_set_allocated_save_slice_info_def(
      ::tensorflow::SaveSliceInfoDef* save_slice_info_def);
  ::tensorflow::SaveSliceInfoDef* unsafe_arena_release_save_slice_info_def();

  // bool is_resource = 5;
  void clear_is_resource();
  bool is_resource() const;
  void set_is_resource(bool value);
  private:
  bool _internal_is_resource() const;
  void _internal_set_is_resource(bool value);
  public:

  // bool trainable = 7;
  void clear_trainable();
  bool trainable() const;
  void set_trainable(bool value);
  private:
  bool _internal_trainable() const;
  void _internal_set_trainable(bool value);
  public:

  // .tensorflow.VariableSynchronization synchronization = 8;
  void clear_synchronization();
  ::tensorflow::VariableSynchronization synchronization() const;
  void set_synchronization(::tensorflow::VariableSynchronization value);
  private:
  ::tensorflow::VariableSynchronization _internal_synchronization() const;
  void _internal_set_synchronization(::tensorflow::VariableSynchronization value);
  public:

  // .tensorflow.VariableAggregation aggregation = 9;
  void clear_aggregation();
  ::tensorflow::VariableAggregation aggregation() const;
  void set_aggregation(::tensorflow::VariableAggregation value);
  private:
  ::tensorflow::VariableAggregation _internal_aggregation() const;
  void _internal_set_aggregation(::tensorflow::VariableAggregation value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.VariableDef)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr variable_name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr initializer_name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr snapshot_name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr initial_value_name_;
    ::tensorflow::SaveSliceInfoDef* save_slice_info_def_;
    bool is_resource_;
    bool trainable_;
    int synchronization_;
    int aggregation_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fvariable_2eproto;
};
// -------------------------------------------------------------------

class SaveSliceInfoDef final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.SaveSliceInfoDef) */ {
 public:
  inline SaveSliceInfoDef() : SaveSliceInfoDef(nullptr) {}
  ~SaveSliceInfoDef() override;
  explicit PROTOBUF_CONSTEXPR SaveSliceInfoDef(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SaveSliceInfoDef(const SaveSliceInfoDef& from);
  SaveSliceInfoDef(SaveSliceInfoDef&& from) noexcept
    : SaveSliceInfoDef() {
    *this = ::std::move(from);
  }

  inline SaveSliceInfoDef& operator=(const SaveSliceInfoDef& from) {
    CopyFrom(from);
    return *this;
  }
  inline SaveSliceInfoDef& operator=(SaveSliceInfoDef&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SaveSliceInfoDef& default_instance() {
    return *internal_default_instance();
  }
  static inline const SaveSliceInfoDef* internal_default_instance() {
    return reinterpret_cast<const SaveSliceInfoDef*>(
               &_SaveSliceInfoDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(SaveSliceInfoDef& a, SaveSliceInfoDef& b) {
    a.Swap(&b);
  }
  inline void Swap(SaveSliceInfoDef* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SaveSliceInfoDef* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SaveSliceInfoDef* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SaveSliceInfoDef>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SaveSliceInfoDef& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const SaveSliceInfoDef& from) {
    SaveSliceInfoDef::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SaveSliceInfoDef* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.SaveSliceInfoDef";
  }
  protected:
  explicit SaveSliceInfoDef(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFullShapeFieldNumber = 2,
    kVarOffsetFieldNumber = 3,
    kVarShapeFieldNumber = 4,
    kFullNameFieldNumber = 1,
  };
  // repeated int64 full_shape = 2;
  int full_shape_size() const;
  private:
  int _internal_full_shape_size() const;
  public:
  void clear_full_shape();
  private:
  int64_t _internal_full_shape(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      _internal_full_shape() const;
  void _internal_add_full_shape(int64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      _internal_mutable_full_shape();
  public:
  int64_t full_shape(int index) const;
  void set_full_shape(int index, int64_t value);
  void add_full_shape(int64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      full_shape() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      mutable_full_shape();

  // repeated int64 var_offset = 3;
  int var_offset_size() const;
  private:
  int _internal_var_offset_size() const;
  public:
  void clear_var_offset();
  private:
  int64_t _internal_var_offset(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      _internal_var_offset() const;
  void _internal_add_var_offset(int64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      _internal_mutable_var_offset();
  public:
  int64_t var_offset(int index) const;
  void set_var_offset(int index, int64_t value);
  void add_var_offset(int64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      var_offset() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      mutable_var_offset();

  // repeated int64 var_shape = 4;
  int var_shape_size() const;
  private:
  int _internal_var_shape_size() const;
  public:
  void clear_var_shape();
  private:
  int64_t _internal_var_shape(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      _internal_var_shape() const;
  void _internal_add_var_shape(int64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      _internal_mutable_var_shape();
  public:
  int64_t var_shape(int index) const;
  void set_var_shape(int index, int64_t value);
  void add_var_shape(int64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      var_shape() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      mutable_var_shape();

  // string full_name = 1;
  void clear_full_name();
  const std::string& full_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_full_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_full_name();
  PROTOBUF_NODISCARD std::string* release_full_name();
  void set_allocated_full_name(std::string* full_name);
  private:
  const std::string& _internal_full_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_full_name(const std::string& value);
  std::string* _internal_mutable_full_name();
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.SaveSliceInfoDef)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t > full_shape_;
    mutable std::atomic<int> _full_shape_cached_byte_size_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t > var_offset_;
    mutable std::atomic<int> _var_offset_cached_byte_size_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t > var_shape_;
    mutable std::atomic<int> _var_shape_cached_byte_size_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr full_name_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fvariable_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// VariableDef

// string variable_name = 1;
inline void VariableDef::clear_variable_name() {
  _impl_.variable_name_.ClearToEmpty();
}
inline const std::string& VariableDef::variable_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.VariableDef.variable_name)
  return _internal_variable_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void VariableDef::set_variable_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.variable_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.VariableDef.variable_name)
}
inline std::string* VariableDef::mutable_variable_name() {
  std::string* _s = _internal_mutable_variable_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.VariableDef.variable_name)
  return _s;
}
inline const std::string& VariableDef::_internal_variable_name() const {
  return _impl_.variable_name_.Get();
}
inline void VariableDef::_internal_set_variable_name(const std::string& value) {
  
  _impl_.variable_name_.Set(value, GetArenaForAllocation());
}
inline std::string* VariableDef::_internal_mutable_variable_name() {
  
  return _impl_.variable_name_.Mutable(GetArenaForAllocation());
}
inline std::string* VariableDef::release_variable_name() {
  // @@protoc_insertion_point(field_release:tensorflow.VariableDef.variable_name)
  return _impl_.variable_name_.Release();
}
inline void VariableDef::set_allocated_variable_name(std::string* variable_name) {
  if (variable_name != nullptr) {
    
  } else {
    
  }
  _impl_.variable_name_.SetAllocated(variable_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.variable_name_.IsDefault()) {
    _impl_.variable_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.VariableDef.variable_name)
}

// string initial_value_name = 6;
inline void VariableDef::clear_initial_value_name() {
  _impl_.initial_value_name_.ClearToEmpty();
}
inline const std::string& VariableDef::initial_value_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.VariableDef.initial_value_name)
  return _internal_initial_value_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void VariableDef::set_initial_value_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.initial_value_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.VariableDef.initial_value_name)
}
inline std::string* VariableDef::mutable_initial_value_name() {
  std::string* _s = _internal_mutable_initial_value_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.VariableDef.initial_value_name)
  return _s;
}
inline const std::string& VariableDef::_internal_initial_value_name() const {
  return _impl_.initial_value_name_.Get();
}
inline void VariableDef::_internal_set_initial_value_name(const std::string& value) {
  
  _impl_.initial_value_name_.Set(value, GetArenaForAllocation());
}
inline std::string* VariableDef::_internal_mutable_initial_value_name() {
  
  return _impl_.initial_value_name_.Mutable(GetArenaForAllocation());
}
inline std::string* VariableDef::release_initial_value_name() {
  // @@protoc_insertion_point(field_release:tensorflow.VariableDef.initial_value_name)
  return _impl_.initial_value_name_.Release();
}
inline void VariableDef::set_allocated_initial_value_name(std::string* initial_value_name) {
  if (initial_value_name != nullptr) {
    
  } else {
    
  }
  _impl_.initial_value_name_.SetAllocated(initial_value_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.initial_value_name_.IsDefault()) {
    _impl_.initial_value_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.VariableDef.initial_value_name)
}

// string initializer_name = 2;
inline void VariableDef::clear_initializer_name() {
  _impl_.initializer_name_.ClearToEmpty();
}
inline const std::string& VariableDef::initializer_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.VariableDef.initializer_name)
  return _internal_initializer_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void VariableDef::set_initializer_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.initializer_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.VariableDef.initializer_name)
}
inline std::string* VariableDef::mutable_initializer_name() {
  std::string* _s = _internal_mutable_initializer_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.VariableDef.initializer_name)
  return _s;
}
inline const std::string& VariableDef::_internal_initializer_name() const {
  return _impl_.initializer_name_.Get();
}
inline void VariableDef::_internal_set_initializer_name(const std::string& value) {
  
  _impl_.initializer_name_.Set(value, GetArenaForAllocation());
}
inline std::string* VariableDef::_internal_mutable_initializer_name() {
  
  return _impl_.initializer_name_.Mutable(GetArenaForAllocation());
}
inline std::string* VariableDef::release_initializer_name() {
  // @@protoc_insertion_point(field_release:tensorflow.VariableDef.initializer_name)
  return _impl_.initializer_name_.Release();
}
inline void VariableDef::set_allocated_initializer_name(std::string* initializer_name) {
  if (initializer_name != nullptr) {
    
  } else {
    
  }
  _impl_.initializer_name_.SetAllocated(initializer_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.initializer_name_.IsDefault()) {
    _impl_.initializer_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.VariableDef.initializer_name)
}

// string snapshot_name = 3;
inline void VariableDef::clear_snapshot_name() {
  _impl_.snapshot_name_.ClearToEmpty();
}
inline const std::string& VariableDef::snapshot_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.VariableDef.snapshot_name)
  return _internal_snapshot_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void VariableDef::set_snapshot_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.snapshot_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.VariableDef.snapshot_name)
}
inline std::string* VariableDef::mutable_snapshot_name() {
  std::string* _s = _internal_mutable_snapshot_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.VariableDef.snapshot_name)
  return _s;
}
inline const std::string& VariableDef::_internal_snapshot_name() const {
  return _impl_.snapshot_name_.Get();
}
inline void VariableDef::_internal_set_snapshot_name(const std::string& value) {
  
  _impl_.snapshot_name_.Set(value, GetArenaForAllocation());
}
inline std::string* VariableDef::_internal_mutable_snapshot_name() {
  
  return _impl_.snapshot_name_.Mutable(GetArenaForAllocation());
}
inline std::string* VariableDef::release_snapshot_name() {
  // @@protoc_insertion_point(field_release:tensorflow.VariableDef.snapshot_name)
  return _impl_.snapshot_name_.Release();
}
inline void VariableDef::set_allocated_snapshot_name(std::string* snapshot_name) {
  if (snapshot_name != nullptr) {
    
  } else {
    
  }
  _impl_.snapshot_name_.SetAllocated(snapshot_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.snapshot_name_.IsDefault()) {
    _impl_.snapshot_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.VariableDef.snapshot_name)
}

// .tensorflow.SaveSliceInfoDef save_slice_info_def = 4;
inline bool VariableDef::_internal_has_save_slice_info_def() const {
  return this != internal_default_instance() && _impl_.save_slice_info_def_ != nullptr;
}
inline bool VariableDef::has_save_slice_info_def() const {
  return _internal_has_save_slice_info_def();
}
inline void VariableDef::clear_save_slice_info_def() {
  if (GetArenaForAllocation() == nullptr && _impl_.save_slice_info_def_ != nullptr) {
    delete _impl_.save_slice_info_def_;
  }
  _impl_.save_slice_info_def_ = nullptr;
}
inline const ::tensorflow::SaveSliceInfoDef& VariableDef::_internal_save_slice_info_def() const {
  const ::tensorflow::SaveSliceInfoDef* p = _impl_.save_slice_info_def_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::SaveSliceInfoDef&>(
      ::tensorflow::_SaveSliceInfoDef_default_instance_);
}
inline const ::tensorflow::SaveSliceInfoDef& VariableDef::save_slice_info_def() const {
  // @@protoc_insertion_point(field_get:tensorflow.VariableDef.save_slice_info_def)
  return _internal_save_slice_info_def();
}
inline void VariableDef::unsafe_arena_set_allocated_save_slice_info_def(
    ::tensorflow::SaveSliceInfoDef* save_slice_info_def) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.save_slice_info_def_);
  }
  _impl_.save_slice_info_def_ = save_slice_info_def;
  if (save_slice_info_def) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.VariableDef.save_slice_info_def)
}
inline ::tensorflow::SaveSliceInfoDef* VariableDef::release_save_slice_info_def() {
  
  ::tensorflow::SaveSliceInfoDef* temp = _impl_.save_slice_info_def_;
  _impl_.save_slice_info_def_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::SaveSliceInfoDef* VariableDef::unsafe_arena_release_save_slice_info_def() {
  // @@protoc_insertion_point(field_release:tensorflow.VariableDef.save_slice_info_def)
  
  ::tensorflow::SaveSliceInfoDef* temp = _impl_.save_slice_info_def_;
  _impl_.save_slice_info_def_ = nullptr;
  return temp;
}
inline ::tensorflow::SaveSliceInfoDef* VariableDef::_internal_mutable_save_slice_info_def() {
  
  if (_impl_.save_slice_info_def_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::SaveSliceInfoDef>(GetArenaForAllocation());
    _impl_.save_slice_info_def_ = p;
  }
  return _impl_.save_slice_info_def_;
}
inline ::tensorflow::SaveSliceInfoDef* VariableDef::mutable_save_slice_info_def() {
  ::tensorflow::SaveSliceInfoDef* _msg = _internal_mutable_save_slice_info_def();
  // @@protoc_insertion_point(field_mutable:tensorflow.VariableDef.save_slice_info_def)
  return _msg;
}
inline void VariableDef::set_allocated_save_slice_info_def(::tensorflow::SaveSliceInfoDef* save_slice_info_def) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.save_slice_info_def_;
  }
  if (save_slice_info_def) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(save_slice_info_def);
    if (message_arena != submessage_arena) {
      save_slice_info_def = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, save_slice_info_def, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.save_slice_info_def_ = save_slice_info_def;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.VariableDef.save_slice_info_def)
}

// bool is_resource = 5;
inline void VariableDef::clear_is_resource() {
  _impl_.is_resource_ = false;
}
inline bool VariableDef::_internal_is_resource() const {
  return _impl_.is_resource_;
}
inline bool VariableDef::is_resource() const {
  // @@protoc_insertion_point(field_get:tensorflow.VariableDef.is_resource)
  return _internal_is_resource();
}
inline void VariableDef::_internal_set_is_resource(bool value) {
  
  _impl_.is_resource_ = value;
}
inline void VariableDef::set_is_resource(bool value) {
  _internal_set_is_resource(value);
  // @@protoc_insertion_point(field_set:tensorflow.VariableDef.is_resource)
}

// bool trainable = 7;
inline void VariableDef::clear_trainable() {
  _impl_.trainable_ = false;
}
inline bool VariableDef::_internal_trainable() const {
  return _impl_.trainable_;
}
inline bool VariableDef::trainable() const {
  // @@protoc_insertion_point(field_get:tensorflow.VariableDef.trainable)
  return _internal_trainable();
}
inline void VariableDef::_internal_set_trainable(bool value) {
  
  _impl_.trainable_ = value;
}
inline void VariableDef::set_trainable(bool value) {
  _internal_set_trainable(value);
  // @@protoc_insertion_point(field_set:tensorflow.VariableDef.trainable)
}

// .tensorflow.VariableSynchronization synchronization = 8;
inline void VariableDef::clear_synchronization() {
  _impl_.synchronization_ = 0;
}
inline ::tensorflow::VariableSynchronization VariableDef::_internal_synchronization() const {
  return static_cast< ::tensorflow::VariableSynchronization >(_impl_.synchronization_);
}
inline ::tensorflow::VariableSynchronization VariableDef::synchronization() const {
  // @@protoc_insertion_point(field_get:tensorflow.VariableDef.synchronization)
  return _internal_synchronization();
}
inline void VariableDef::_internal_set_synchronization(::tensorflow::VariableSynchronization value) {
  
  _impl_.synchronization_ = value;
}
inline void VariableDef::set_synchronization(::tensorflow::VariableSynchronization value) {
  _internal_set_synchronization(value);
  // @@protoc_insertion_point(field_set:tensorflow.VariableDef.synchronization)
}

// .tensorflow.VariableAggregation aggregation = 9;
inline void VariableDef::clear_aggregation() {
  _impl_.aggregation_ = 0;
}
inline ::tensorflow::VariableAggregation VariableDef::_internal_aggregation() const {
  return static_cast< ::tensorflow::VariableAggregation >(_impl_.aggregation_);
}
inline ::tensorflow::VariableAggregation VariableDef::aggregation() const {
  // @@protoc_insertion_point(field_get:tensorflow.VariableDef.aggregation)
  return _internal_aggregation();
}
inline void VariableDef::_internal_set_aggregation(::tensorflow::VariableAggregation value) {
  
  _impl_.aggregation_ = value;
}
inline void VariableDef::set_aggregation(::tensorflow::VariableAggregation value) {
  _internal_set_aggregation(value);
  // @@protoc_insertion_point(field_set:tensorflow.VariableDef.aggregation)
}

// -------------------------------------------------------------------

// SaveSliceInfoDef

// string full_name = 1;
inline void SaveSliceInfoDef::clear_full_name() {
  _impl_.full_name_.ClearToEmpty();
}
inline const std::string& SaveSliceInfoDef::full_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.SaveSliceInfoDef.full_name)
  return _internal_full_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SaveSliceInfoDef::set_full_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.full_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.SaveSliceInfoDef.full_name)
}
inline std::string* SaveSliceInfoDef::mutable_full_name() {
  std::string* _s = _internal_mutable_full_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.SaveSliceInfoDef.full_name)
  return _s;
}
inline const std::string& SaveSliceInfoDef::_internal_full_name() const {
  return _impl_.full_name_.Get();
}
inline void SaveSliceInfoDef::_internal_set_full_name(const std::string& value) {
  
  _impl_.full_name_.Set(value, GetArenaForAllocation());
}
inline std::string* SaveSliceInfoDef::_internal_mutable_full_name() {
  
  return _impl_.full_name_.Mutable(GetArenaForAllocation());
}
inline std::string* SaveSliceInfoDef::release_full_name() {
  // @@protoc_insertion_point(field_release:tensorflow.SaveSliceInfoDef.full_name)
  return _impl_.full_name_.Release();
}
inline void SaveSliceInfoDef::set_allocated_full_name(std::string* full_name) {
  if (full_name != nullptr) {
    
  } else {
    
  }
  _impl_.full_name_.SetAllocated(full_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.full_name_.IsDefault()) {
    _impl_.full_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SaveSliceInfoDef.full_name)
}

// repeated int64 full_shape = 2;
inline int SaveSliceInfoDef::_internal_full_shape_size() const {
  return _impl_.full_shape_.size();
}
inline int SaveSliceInfoDef::full_shape_size() const {
  return _internal_full_shape_size();
}
inline void SaveSliceInfoDef::clear_full_shape() {
  _impl_.full_shape_.Clear();
}
inline int64_t SaveSliceInfoDef::_internal_full_shape(int index) const {
  return _impl_.full_shape_.Get(index);
}
inline int64_t SaveSliceInfoDef::full_shape(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.SaveSliceInfoDef.full_shape)
  return _internal_full_shape(index);
}
inline void SaveSliceInfoDef::set_full_shape(int index, int64_t value) {
  _impl_.full_shape_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.SaveSliceInfoDef.full_shape)
}
inline void SaveSliceInfoDef::_internal_add_full_shape(int64_t value) {
  _impl_.full_shape_.Add(value);
}
inline void SaveSliceInfoDef::add_full_shape(int64_t value) {
  _internal_add_full_shape(value);
  // @@protoc_insertion_point(field_add:tensorflow.SaveSliceInfoDef.full_shape)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
SaveSliceInfoDef::_internal_full_shape() const {
  return _impl_.full_shape_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
SaveSliceInfoDef::full_shape() const {
  // @@protoc_insertion_point(field_list:tensorflow.SaveSliceInfoDef.full_shape)
  return _internal_full_shape();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
SaveSliceInfoDef::_internal_mutable_full_shape() {
  return &_impl_.full_shape_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
SaveSliceInfoDef::mutable_full_shape() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.SaveSliceInfoDef.full_shape)
  return _internal_mutable_full_shape();
}

// repeated int64 var_offset = 3;
inline int SaveSliceInfoDef::_internal_var_offset_size() const {
  return _impl_.var_offset_.size();
}
inline int SaveSliceInfoDef::var_offset_size() const {
  return _internal_var_offset_size();
}
inline void SaveSliceInfoDef::clear_var_offset() {
  _impl_.var_offset_.Clear();
}
inline int64_t SaveSliceInfoDef::_internal_var_offset(int index) const {
  return _impl_.var_offset_.Get(index);
}
inline int64_t SaveSliceInfoDef::var_offset(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.SaveSliceInfoDef.var_offset)
  return _internal_var_offset(index);
}
inline void SaveSliceInfoDef::set_var_offset(int index, int64_t value) {
  _impl_.var_offset_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.SaveSliceInfoDef.var_offset)
}
inline void SaveSliceInfoDef::_internal_add_var_offset(int64_t value) {
  _impl_.var_offset_.Add(value);
}
inline void SaveSliceInfoDef::add_var_offset(int64_t value) {
  _internal_add_var_offset(value);
  // @@protoc_insertion_point(field_add:tensorflow.SaveSliceInfoDef.var_offset)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
SaveSliceInfoDef::_internal_var_offset() const {
  return _impl_.var_offset_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
SaveSliceInfoDef::var_offset() const {
  // @@protoc_insertion_point(field_list:tensorflow.SaveSliceInfoDef.var_offset)
  return _internal_var_offset();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
SaveSliceInfoDef::_internal_mutable_var_offset() {
  return &_impl_.var_offset_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
SaveSliceInfoDef::mutable_var_offset() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.SaveSliceInfoDef.var_offset)
  return _internal_mutable_var_offset();
}

// repeated int64 var_shape = 4;
inline int SaveSliceInfoDef::_internal_var_shape_size() const {
  return _impl_.var_shape_.size();
}
inline int SaveSliceInfoDef::var_shape_size() const {
  return _internal_var_shape_size();
}
inline void SaveSliceInfoDef::clear_var_shape() {
  _impl_.var_shape_.Clear();
}
inline int64_t SaveSliceInfoDef::_internal_var_shape(int index) const {
  return _impl_.var_shape_.Get(index);
}
inline int64_t SaveSliceInfoDef::var_shape(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.SaveSliceInfoDef.var_shape)
  return _internal_var_shape(index);
}
inline void SaveSliceInfoDef::set_var_shape(int index, int64_t value) {
  _impl_.var_shape_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.SaveSliceInfoDef.var_shape)
}
inline void SaveSliceInfoDef::_internal_add_var_shape(int64_t value) {
  _impl_.var_shape_.Add(value);
}
inline void SaveSliceInfoDef::add_var_shape(int64_t value) {
  _internal_add_var_shape(value);
  // @@protoc_insertion_point(field_add:tensorflow.SaveSliceInfoDef.var_shape)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
SaveSliceInfoDef::_internal_var_shape() const {
  return _impl_.var_shape_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
SaveSliceInfoDef::var_shape() const {
  // @@protoc_insertion_point(field_list:tensorflow.SaveSliceInfoDef.var_shape)
  return _internal_var_shape();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
SaveSliceInfoDef::_internal_mutable_var_shape() {
  return &_impl_.var_shape_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
SaveSliceInfoDef::mutable_var_shape() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.SaveSliceInfoDef.var_shape)
  return _internal_mutable_var_shape();
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::tensorflow::VariableSynchronization> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::VariableSynchronization>() {
  return ::tensorflow::VariableSynchronization_descriptor();
}
template <> struct is_proto_enum< ::tensorflow::VariableAggregation> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::VariableAggregation>() {
  return ::tensorflow::VariableAggregation_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fvariable_2eproto
