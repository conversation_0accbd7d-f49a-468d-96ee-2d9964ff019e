// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/lite/toco/toco_flags.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2flite_2ftoco_2ftoco_5fflags_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2flite_2ftoco_2ftoco_5fflags_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/compiler/mlir/lite/debug/debug_options.pb.h"
#include "tensorflow/compiler/mlir/quantization/stablehlo/quantization_config.pb.h"
#include "tensorflow/compiler/mlir/quantization/stablehlo/quantization_options.pb.h"
#include "tensorflow/lite/toco/types.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2flite_2ftoco_2ftoco_5fflags_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2flite_2ftoco_2ftoco_5fflags_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2flite_2ftoco_2ftoco_5fflags_2eproto;
namespace toco {
class TocoFlags;
struct TocoFlagsDefaultTypeInternal;
extern TocoFlagsDefaultTypeInternal _TocoFlags_default_instance_;
}  // namespace toco
PROTOBUF_NAMESPACE_OPEN
template<> ::toco::TocoFlags* Arena::CreateMaybeMessage<::toco::TocoFlags>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace toco {

enum TocoFlags_ModelOriginFramework : int {
  TocoFlags_ModelOriginFramework_UNSET = 0,
  TocoFlags_ModelOriginFramework_TENSORFLOW = 1,
  TocoFlags_ModelOriginFramework_KERAS = 2,
  TocoFlags_ModelOriginFramework_JAX = 3,
  TocoFlags_ModelOriginFramework_PYTORCH = 4
};
bool TocoFlags_ModelOriginFramework_IsValid(int value);
constexpr TocoFlags_ModelOriginFramework TocoFlags_ModelOriginFramework_ModelOriginFramework_MIN = TocoFlags_ModelOriginFramework_UNSET;
constexpr TocoFlags_ModelOriginFramework TocoFlags_ModelOriginFramework_ModelOriginFramework_MAX = TocoFlags_ModelOriginFramework_PYTORCH;
constexpr int TocoFlags_ModelOriginFramework_ModelOriginFramework_ARRAYSIZE = TocoFlags_ModelOriginFramework_ModelOriginFramework_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* TocoFlags_ModelOriginFramework_descriptor();
template<typename T>
inline const std::string& TocoFlags_ModelOriginFramework_Name(T enum_t_value) {
  static_assert(::std::is_same<T, TocoFlags_ModelOriginFramework>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function TocoFlags_ModelOriginFramework_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    TocoFlags_ModelOriginFramework_descriptor(), enum_t_value);
}
inline bool TocoFlags_ModelOriginFramework_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, TocoFlags_ModelOriginFramework* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<TocoFlags_ModelOriginFramework>(
    TocoFlags_ModelOriginFramework_descriptor(), name, value);
}
enum FileFormat : int {
  FILE_FORMAT_UNKNOWN = 0,
  TENSORFLOW_GRAPHDEF = 1,
  TFLITE = 2,
  GRAPHVIZ_DOT = 3
};
bool FileFormat_IsValid(int value);
constexpr FileFormat FileFormat_MIN = FILE_FORMAT_UNKNOWN;
constexpr FileFormat FileFormat_MAX = GRAPHVIZ_DOT;
constexpr int FileFormat_ARRAYSIZE = FileFormat_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* FileFormat_descriptor();
template<typename T>
inline const std::string& FileFormat_Name(T enum_t_value) {
  static_assert(::std::is_same<T, FileFormat>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function FileFormat_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    FileFormat_descriptor(), enum_t_value);
}
inline bool FileFormat_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, FileFormat* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<FileFormat>(
    FileFormat_descriptor(), name, value);
}
// ===================================================================

class TocoFlags final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:toco.TocoFlags) */ {
 public:
  inline TocoFlags() : TocoFlags(nullptr) {}
  ~TocoFlags() override;
  explicit PROTOBUF_CONSTEXPR TocoFlags(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TocoFlags(const TocoFlags& from);
  TocoFlags(TocoFlags&& from) noexcept
    : TocoFlags() {
    *this = ::std::move(from);
  }

  inline TocoFlags& operator=(const TocoFlags& from) {
    CopyFrom(from);
    return *this;
  }
  inline TocoFlags& operator=(TocoFlags&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TocoFlags& default_instance() {
    return *internal_default_instance();
  }
  static inline const TocoFlags* internal_default_instance() {
    return reinterpret_cast<const TocoFlags*>(
               &_TocoFlags_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(TocoFlags& a, TocoFlags& b) {
    a.Swap(&b);
  }
  inline void Swap(TocoFlags* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TocoFlags* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TocoFlags* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TocoFlags>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TocoFlags& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const TocoFlags& from) {
    TocoFlags::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TocoFlags* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "toco.TocoFlags";
  }
  protected:
  explicit TocoFlags(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef TocoFlags_ModelOriginFramework ModelOriginFramework;
  static constexpr ModelOriginFramework UNSET =
    TocoFlags_ModelOriginFramework_UNSET;
  static constexpr ModelOriginFramework TENSORFLOW =
    TocoFlags_ModelOriginFramework_TENSORFLOW;
  static constexpr ModelOriginFramework KERAS =
    TocoFlags_ModelOriginFramework_KERAS;
  static constexpr ModelOriginFramework JAX =
    TocoFlags_ModelOriginFramework_JAX;
  static constexpr ModelOriginFramework PYTORCH =
    TocoFlags_ModelOriginFramework_PYTORCH;
  static inline bool ModelOriginFramework_IsValid(int value) {
    return TocoFlags_ModelOriginFramework_IsValid(value);
  }
  static constexpr ModelOriginFramework ModelOriginFramework_MIN =
    TocoFlags_ModelOriginFramework_ModelOriginFramework_MIN;
  static constexpr ModelOriginFramework ModelOriginFramework_MAX =
    TocoFlags_ModelOriginFramework_ModelOriginFramework_MAX;
  static constexpr int ModelOriginFramework_ARRAYSIZE =
    TocoFlags_ModelOriginFramework_ModelOriginFramework_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  ModelOriginFramework_descriptor() {
    return TocoFlags_ModelOriginFramework_descriptor();
  }
  template<typename T>
  static inline const std::string& ModelOriginFramework_Name(T enum_t_value) {
    static_assert(::std::is_same<T, ModelOriginFramework>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function ModelOriginFramework_Name.");
    return TocoFlags_ModelOriginFramework_Name(enum_t_value);
  }
  static inline bool ModelOriginFramework_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      ModelOriginFramework* value) {
    return TocoFlags_ModelOriginFramework_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kCustomOpdefsFieldNumber = 32,
    kSelectUserTfOpsFieldNumber = 33,
    kSupportedBackendsFieldNumber = 41,
    kDumpGraphvizDirFieldNumber = 24,
    kConversionSummaryDirFieldNumber = 31,
    kTfQuantizationModeFieldNumber = 45,
    kQdqConversionModeFieldNumber = 60,
    kQuantizationOptionsFieldNumber = 54,
    kDebugOptionsFieldNumber = 56,
    kQuantizationConfigFieldNumber = 61,
    kInputFormatFieldNumber = 1,
    kOutputFormatFieldNumber = 2,
    kInferenceTypeFieldNumber = 4,
    kDefaultRangesMinFieldNumber = 5,
    kDefaultRangesMaxFieldNumber = 6,
    kDropFakeQuantFieldNumber = 7,
    kReorderAcrossFakeQuantFieldNumber = 8,
    kAllowCustomOpsFieldNumber = 10,
    kDropControlDependencyFieldNumber = 12,
    kInferenceInputTypeFieldNumber = 11,
    kDefaultInt16RangesMinFieldNumber = 15,
    kDefaultInt16RangesMaxFieldNumber = 16,
    kDebugDisableRecurrentCellFusionFieldNumber = 13,
    kPropagateFakeQuantNumBitsFieldNumber = 14,
    kAllowNudgingWeightsToUseFastGemmKernelFieldNumber = 17,
    kQuantizeWeightsFieldNumber = 20,
    kDumpGraphvizIncludeVideoFieldNumber = 25,
    kPostTrainingQuantizeFieldNumber = 26,
    kEnableSelectTfOpsFieldNumber = 27,
    kForceSelectTfOpsFieldNumber = 28,
    kQuantizeToFloat16FieldNumber = 29,
    kUnfoldBatchmatmulFieldNumber = 35,
    kAllowBfloat16FieldNumber = 38,
    kAllowAllSelectTfOpsFieldNumber = 39,
    kAccumulationTypeFieldNumber = 37,
    kUnfoldLargeSplatConstantFieldNumber = 40,
    kDefaultToSingleBatchInTensorListOpsFieldNumber = 42,
    kDisablePerChannelQuantizationFieldNumber = 43,
    kEnableMlirDynamicRangeQuantizerFieldNumber = 44,
    kDisableInferTensorRangeFieldNumber = 46,
    kUseFakeQuantNumBitsFieldNumber = 47,
    kEnableDynamicUpdateSliceFieldNumber = 48,
    kPreserveAssertOpFieldNumber = 49,
    kGuaranteeAllFuncsOneUseFieldNumber = 50,
    kConvertToStablehloFieldNumber = 51,
    kEnableMlirVariableQuantizationFieldNumber = 52,
    kDisableFuseMulAndFcFieldNumber = 53,
    kEnableHloToTfConversionFieldNumber = 55,
    kUseBufferOffsetFieldNumber = 57,
    kLegalizeCustomTensorListOpsFieldNumber = 58,
    kReduceTypePrecisionFieldNumber = 59,
    kDisablePerChannelQuantizationForDenseLayersFieldNumber = 62,
    kEnableCompositeDirectLoweringFieldNumber = 63,
    kCanonicalizingInfAsMinMaxFloatFieldNumber = 65,
    kModelOriginFrameworkFieldNumber = 64,
    kSplitTfliteLstmInputsFieldNumber = 19,
    kAllowDynamicTensorsFieldNumber = 30,
    kEnableTfliteResourceVariablesFieldNumber = 34,
    kLowerTensorListOpsFieldNumber = 36,
    kDedupeArrayMinSizeBytesFieldNumber = 18,
  };
  // repeated string custom_opdefs = 32 [deprecated = true];
  PROTOBUF_DEPRECATED int custom_opdefs_size() const;
  private:
  int _internal_custom_opdefs_size() const;
  public:
  PROTOBUF_DEPRECATED void clear_custom_opdefs();
  PROTOBUF_DEPRECATED const std::string& custom_opdefs(int index) const;
  PROTOBUF_DEPRECATED std::string* mutable_custom_opdefs(int index);
  PROTOBUF_DEPRECATED void set_custom_opdefs(int index, const std::string& value);
  PROTOBUF_DEPRECATED void set_custom_opdefs(int index, std::string&& value);
  PROTOBUF_DEPRECATED void set_custom_opdefs(int index, const char* value);
  PROTOBUF_DEPRECATED void set_custom_opdefs(int index, const char* value, size_t size);
  PROTOBUF_DEPRECATED std::string* add_custom_opdefs();
  PROTOBUF_DEPRECATED void add_custom_opdefs(const std::string& value);
  PROTOBUF_DEPRECATED void add_custom_opdefs(std::string&& value);
  PROTOBUF_DEPRECATED void add_custom_opdefs(const char* value);
  PROTOBUF_DEPRECATED void add_custom_opdefs(const char* value, size_t size);
  PROTOBUF_DEPRECATED const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& custom_opdefs() const;
  PROTOBUF_DEPRECATED ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_custom_opdefs();
  private:
  const std::string& _internal_custom_opdefs(int index) const;
  std::string* _internal_add_custom_opdefs();
  public:

  // repeated string select_user_tf_ops = 33;
  int select_user_tf_ops_size() const;
  private:
  int _internal_select_user_tf_ops_size() const;
  public:
  void clear_select_user_tf_ops();
  const std::string& select_user_tf_ops(int index) const;
  std::string* mutable_select_user_tf_ops(int index);
  void set_select_user_tf_ops(int index, const std::string& value);
  void set_select_user_tf_ops(int index, std::string&& value);
  void set_select_user_tf_ops(int index, const char* value);
  void set_select_user_tf_ops(int index, const char* value, size_t size);
  std::string* add_select_user_tf_ops();
  void add_select_user_tf_ops(const std::string& value);
  void add_select_user_tf_ops(std::string&& value);
  void add_select_user_tf_ops(const char* value);
  void add_select_user_tf_ops(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& select_user_tf_ops() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_select_user_tf_ops();
  private:
  const std::string& _internal_select_user_tf_ops(int index) const;
  std::string* _internal_add_select_user_tf_ops();
  public:

  // repeated string supported_backends = 41;
  int supported_backends_size() const;
  private:
  int _internal_supported_backends_size() const;
  public:
  void clear_supported_backends();
  const std::string& supported_backends(int index) const;
  std::string* mutable_supported_backends(int index);
  void set_supported_backends(int index, const std::string& value);
  void set_supported_backends(int index, std::string&& value);
  void set_supported_backends(int index, const char* value);
  void set_supported_backends(int index, const char* value, size_t size);
  std::string* add_supported_backends();
  void add_supported_backends(const std::string& value);
  void add_supported_backends(std::string&& value);
  void add_supported_backends(const char* value);
  void add_supported_backends(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& supported_backends() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_supported_backends();
  private:
  const std::string& _internal_supported_backends(int index) const;
  std::string* _internal_add_supported_backends();
  public:

  // optional string dump_graphviz_dir = 24;
  bool has_dump_graphviz_dir() const;
  private:
  bool _internal_has_dump_graphviz_dir() const;
  public:
  void clear_dump_graphviz_dir();
  const std::string& dump_graphviz_dir() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_dump_graphviz_dir(ArgT0&& arg0, ArgT... args);
  std::string* mutable_dump_graphviz_dir();
  PROTOBUF_NODISCARD std::string* release_dump_graphviz_dir();
  void set_allocated_dump_graphviz_dir(std::string* dump_graphviz_dir);
  private:
  const std::string& _internal_dump_graphviz_dir() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_dump_graphviz_dir(const std::string& value);
  std::string* _internal_mutable_dump_graphviz_dir();
  public:

  // optional string conversion_summary_dir = 31;
  bool has_conversion_summary_dir() const;
  private:
  bool _internal_has_conversion_summary_dir() const;
  public:
  void clear_conversion_summary_dir();
  const std::string& conversion_summary_dir() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_conversion_summary_dir(ArgT0&& arg0, ArgT... args);
  std::string* mutable_conversion_summary_dir();
  PROTOBUF_NODISCARD std::string* release_conversion_summary_dir();
  void set_allocated_conversion_summary_dir(std::string* conversion_summary_dir);
  private:
  const std::string& _internal_conversion_summary_dir() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_conversion_summary_dir(const std::string& value);
  std::string* _internal_mutable_conversion_summary_dir();
  public:

  // optional string tf_quantization_mode = 45;
  bool has_tf_quantization_mode() const;
  private:
  bool _internal_has_tf_quantization_mode() const;
  public:
  void clear_tf_quantization_mode();
  const std::string& tf_quantization_mode() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_tf_quantization_mode(ArgT0&& arg0, ArgT... args);
  std::string* mutable_tf_quantization_mode();
  PROTOBUF_NODISCARD std::string* release_tf_quantization_mode();
  void set_allocated_tf_quantization_mode(std::string* tf_quantization_mode);
  private:
  const std::string& _internal_tf_quantization_mode() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_tf_quantization_mode(const std::string& value);
  std::string* _internal_mutable_tf_quantization_mode();
  public:

  // optional string qdq_conversion_mode = 60 [default = "NONE"];
  bool has_qdq_conversion_mode() const;
  private:
  bool _internal_has_qdq_conversion_mode() const;
  public:
  void clear_qdq_conversion_mode();
  const std::string& qdq_conversion_mode() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_qdq_conversion_mode(ArgT0&& arg0, ArgT... args);
  std::string* mutable_qdq_conversion_mode();
  PROTOBUF_NODISCARD std::string* release_qdq_conversion_mode();
  void set_allocated_qdq_conversion_mode(std::string* qdq_conversion_mode);
  private:
  const std::string& _internal_qdq_conversion_mode() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_qdq_conversion_mode(const std::string& value);
  std::string* _internal_mutable_qdq_conversion_mode();
  public:

  // optional .stablehlo.quantization.QuantizationOptions quantization_options = 54 [deprecated = true];
  PROTOBUF_DEPRECATED bool has_quantization_options() const;
  private:
  bool _internal_has_quantization_options() const;
  public:
  PROTOBUF_DEPRECATED void clear_quantization_options();
  PROTOBUF_DEPRECATED const ::stablehlo::quantization::QuantizationOptions& quantization_options() const;
  PROTOBUF_NODISCARD PROTOBUF_DEPRECATED ::stablehlo::quantization::QuantizationOptions* release_quantization_options();
  PROTOBUF_DEPRECATED ::stablehlo::quantization::QuantizationOptions* mutable_quantization_options();
  PROTOBUF_DEPRECATED void set_allocated_quantization_options(::stablehlo::quantization::QuantizationOptions* quantization_options);
  private:
  const ::stablehlo::quantization::QuantizationOptions& _internal_quantization_options() const;
  ::stablehlo::quantization::QuantizationOptions* _internal_mutable_quantization_options();
  public:
  PROTOBUF_DEPRECATED void unsafe_arena_set_allocated_quantization_options(
      ::stablehlo::quantization::QuantizationOptions* quantization_options);
  PROTOBUF_DEPRECATED ::stablehlo::quantization::QuantizationOptions* unsafe_arena_release_quantization_options();

  // optional .tensorflow.converter.DebugOptions debug_options = 56;
  bool has_debug_options() const;
  private:
  bool _internal_has_debug_options() const;
  public:
  void clear_debug_options();
  const ::tensorflow::converter::DebugOptions& debug_options() const;
  PROTOBUF_NODISCARD ::tensorflow::converter::DebugOptions* release_debug_options();
  ::tensorflow::converter::DebugOptions* mutable_debug_options();
  void set_allocated_debug_options(::tensorflow::converter::DebugOptions* debug_options);
  private:
  const ::tensorflow::converter::DebugOptions& _internal_debug_options() const;
  ::tensorflow::converter::DebugOptions* _internal_mutable_debug_options();
  public:
  void unsafe_arena_set_allocated_debug_options(
      ::tensorflow::converter::DebugOptions* debug_options);
  ::tensorflow::converter::DebugOptions* unsafe_arena_release_debug_options();

  // optional .stablehlo.quantization.QuantizationConfig quantization_config = 61;
  bool has_quantization_config() const;
  private:
  bool _internal_has_quantization_config() const;
  public:
  void clear_quantization_config();
  const ::stablehlo::quantization::QuantizationConfig& quantization_config() const;
  PROTOBUF_NODISCARD ::stablehlo::quantization::QuantizationConfig* release_quantization_config();
  ::stablehlo::quantization::QuantizationConfig* mutable_quantization_config();
  void set_allocated_quantization_config(::stablehlo::quantization::QuantizationConfig* quantization_config);
  private:
  const ::stablehlo::quantization::QuantizationConfig& _internal_quantization_config() const;
  ::stablehlo::quantization::QuantizationConfig* _internal_mutable_quantization_config();
  public:
  void unsafe_arena_set_allocated_quantization_config(
      ::stablehlo::quantization::QuantizationConfig* quantization_config);
  ::stablehlo::quantization::QuantizationConfig* unsafe_arena_release_quantization_config();

  // optional .toco.FileFormat input_format = 1;
  bool has_input_format() const;
  private:
  bool _internal_has_input_format() const;
  public:
  void clear_input_format();
  ::toco::FileFormat input_format() const;
  void set_input_format(::toco::FileFormat value);
  private:
  ::toco::FileFormat _internal_input_format() const;
  void _internal_set_input_format(::toco::FileFormat value);
  public:

  // optional .toco.FileFormat output_format = 2;
  bool has_output_format() const;
  private:
  bool _internal_has_output_format() const;
  public:
  void clear_output_format();
  ::toco::FileFormat output_format() const;
  void set_output_format(::toco::FileFormat value);
  private:
  ::toco::FileFormat _internal_output_format() const;
  void _internal_set_output_format(::toco::FileFormat value);
  public:

  // optional .toco.IODataType inference_type = 4;
  bool has_inference_type() const;
  private:
  bool _internal_has_inference_type() const;
  public:
  void clear_inference_type();
  ::toco::IODataType inference_type() const;
  void set_inference_type(::toco::IODataType value);
  private:
  ::toco::IODataType _internal_inference_type() const;
  void _internal_set_inference_type(::toco::IODataType value);
  public:

  // optional float default_ranges_min = 5;
  bool has_default_ranges_min() const;
  private:
  bool _internal_has_default_ranges_min() const;
  public:
  void clear_default_ranges_min();
  float default_ranges_min() const;
  void set_default_ranges_min(float value);
  private:
  float _internal_default_ranges_min() const;
  void _internal_set_default_ranges_min(float value);
  public:

  // optional float default_ranges_max = 6;
  bool has_default_ranges_max() const;
  private:
  bool _internal_has_default_ranges_max() const;
  public:
  void clear_default_ranges_max();
  float default_ranges_max() const;
  void set_default_ranges_max(float value);
  private:
  float _internal_default_ranges_max() const;
  void _internal_set_default_ranges_max(float value);
  public:

  // optional bool drop_fake_quant = 7;
  bool has_drop_fake_quant() const;
  private:
  bool _internal_has_drop_fake_quant() const;
  public:
  void clear_drop_fake_quant();
  bool drop_fake_quant() const;
  void set_drop_fake_quant(bool value);
  private:
  bool _internal_drop_fake_quant() const;
  void _internal_set_drop_fake_quant(bool value);
  public:

  // optional bool reorder_across_fake_quant = 8;
  bool has_reorder_across_fake_quant() const;
  private:
  bool _internal_has_reorder_across_fake_quant() const;
  public:
  void clear_reorder_across_fake_quant();
  bool reorder_across_fake_quant() const;
  void set_reorder_across_fake_quant(bool value);
  private:
  bool _internal_reorder_across_fake_quant() const;
  void _internal_set_reorder_across_fake_quant(bool value);
  public:

  // optional bool allow_custom_ops = 10;
  bool has_allow_custom_ops() const;
  private:
  bool _internal_has_allow_custom_ops() const;
  public:
  void clear_allow_custom_ops();
  bool allow_custom_ops() const;
  void set_allow_custom_ops(bool value);
  private:
  bool _internal_allow_custom_ops() const;
  void _internal_set_allow_custom_ops(bool value);
  public:

  // optional bool drop_control_dependency = 12;
  bool has_drop_control_dependency() const;
  private:
  bool _internal_has_drop_control_dependency() const;
  public:
  void clear_drop_control_dependency();
  bool drop_control_dependency() const;
  void set_drop_control_dependency(bool value);
  private:
  bool _internal_drop_control_dependency() const;
  void _internal_set_drop_control_dependency(bool value);
  public:

  // optional .toco.IODataType inference_input_type = 11;
  bool has_inference_input_type() const;
  private:
  bool _internal_has_inference_input_type() const;
  public:
  void clear_inference_input_type();
  ::toco::IODataType inference_input_type() const;
  void set_inference_input_type(::toco::IODataType value);
  private:
  ::toco::IODataType _internal_inference_input_type() const;
  void _internal_set_inference_input_type(::toco::IODataType value);
  public:

  // optional float default_int16_ranges_min = 15;
  bool has_default_int16_ranges_min() const;
  private:
  bool _internal_has_default_int16_ranges_min() const;
  public:
  void clear_default_int16_ranges_min();
  float default_int16_ranges_min() const;
  void set_default_int16_ranges_min(float value);
  private:
  float _internal_default_int16_ranges_min() const;
  void _internal_set_default_int16_ranges_min(float value);
  public:

  // optional float default_int16_ranges_max = 16;
  bool has_default_int16_ranges_max() const;
  private:
  bool _internal_has_default_int16_ranges_max() const;
  public:
  void clear_default_int16_ranges_max();
  float default_int16_ranges_max() const;
  void set_default_int16_ranges_max(float value);
  private:
  float _internal_default_int16_ranges_max() const;
  void _internal_set_default_int16_ranges_max(float value);
  public:

  // optional bool debug_disable_recurrent_cell_fusion = 13;
  bool has_debug_disable_recurrent_cell_fusion() const;
  private:
  bool _internal_has_debug_disable_recurrent_cell_fusion() const;
  public:
  void clear_debug_disable_recurrent_cell_fusion();
  bool debug_disable_recurrent_cell_fusion() const;
  void set_debug_disable_recurrent_cell_fusion(bool value);
  private:
  bool _internal_debug_disable_recurrent_cell_fusion() const;
  void _internal_set_debug_disable_recurrent_cell_fusion(bool value);
  public:

  // optional bool propagate_fake_quant_num_bits = 14;
  bool has_propagate_fake_quant_num_bits() const;
  private:
  bool _internal_has_propagate_fake_quant_num_bits() const;
  public:
  void clear_propagate_fake_quant_num_bits();
  bool propagate_fake_quant_num_bits() const;
  void set_propagate_fake_quant_num_bits(bool value);
  private:
  bool _internal_propagate_fake_quant_num_bits() const;
  void _internal_set_propagate_fake_quant_num_bits(bool value);
  public:

  // optional bool allow_nudging_weights_to_use_fast_gemm_kernel = 17;
  bool has_allow_nudging_weights_to_use_fast_gemm_kernel() const;
  private:
  bool _internal_has_allow_nudging_weights_to_use_fast_gemm_kernel() const;
  public:
  void clear_allow_nudging_weights_to_use_fast_gemm_kernel();
  bool allow_nudging_weights_to_use_fast_gemm_kernel() const;
  void set_allow_nudging_weights_to_use_fast_gemm_kernel(bool value);
  private:
  bool _internal_allow_nudging_weights_to_use_fast_gemm_kernel() const;
  void _internal_set_allow_nudging_weights_to_use_fast_gemm_kernel(bool value);
  public:

  // optional bool quantize_weights = 20 [default = false];
  bool has_quantize_weights() const;
  private:
  bool _internal_has_quantize_weights() const;
  public:
  void clear_quantize_weights();
  bool quantize_weights() const;
  void set_quantize_weights(bool value);
  private:
  bool _internal_quantize_weights() const;
  void _internal_set_quantize_weights(bool value);
  public:

  // optional bool dump_graphviz_include_video = 25;
  bool has_dump_graphviz_include_video() const;
  private:
  bool _internal_has_dump_graphviz_include_video() const;
  public:
  void clear_dump_graphviz_include_video();
  bool dump_graphviz_include_video() const;
  void set_dump_graphviz_include_video(bool value);
  private:
  bool _internal_dump_graphviz_include_video() const;
  void _internal_set_dump_graphviz_include_video(bool value);
  public:

  // optional bool post_training_quantize = 26 [default = false];
  bool has_post_training_quantize() const;
  private:
  bool _internal_has_post_training_quantize() const;
  public:
  void clear_post_training_quantize();
  bool post_training_quantize() const;
  void set_post_training_quantize(bool value);
  private:
  bool _internal_post_training_quantize() const;
  void _internal_set_post_training_quantize(bool value);
  public:

  // optional bool enable_select_tf_ops = 27 [default = false];
  bool has_enable_select_tf_ops() const;
  private:
  bool _internal_has_enable_select_tf_ops() const;
  public:
  void clear_enable_select_tf_ops();
  bool enable_select_tf_ops() const;
  void set_enable_select_tf_ops(bool value);
  private:
  bool _internal_enable_select_tf_ops() const;
  void _internal_set_enable_select_tf_ops(bool value);
  public:

  // optional bool force_select_tf_ops = 28 [default = false];
  bool has_force_select_tf_ops() const;
  private:
  bool _internal_has_force_select_tf_ops() const;
  public:
  void clear_force_select_tf_ops();
  bool force_select_tf_ops() const;
  void set_force_select_tf_ops(bool value);
  private:
  bool _internal_force_select_tf_ops() const;
  void _internal_set_force_select_tf_ops(bool value);
  public:

  // optional bool quantize_to_float16 = 29 [default = false];
  bool has_quantize_to_float16() const;
  private:
  bool _internal_has_quantize_to_float16() const;
  public:
  void clear_quantize_to_float16();
  bool quantize_to_float16() const;
  void set_quantize_to_float16(bool value);
  private:
  bool _internal_quantize_to_float16() const;
  void _internal_set_quantize_to_float16(bool value);
  public:

  // optional bool unfold_batchmatmul = 35 [default = false];
  bool has_unfold_batchmatmul() const;
  private:
  bool _internal_has_unfold_batchmatmul() const;
  public:
  void clear_unfold_batchmatmul();
  bool unfold_batchmatmul() const;
  void set_unfold_batchmatmul(bool value);
  private:
  bool _internal_unfold_batchmatmul() const;
  void _internal_set_unfold_batchmatmul(bool value);
  public:

  // optional bool allow_bfloat16 = 38 [default = false];
  bool has_allow_bfloat16() const;
  private:
  bool _internal_has_allow_bfloat16() const;
  public:
  void clear_allow_bfloat16();
  bool allow_bfloat16() const;
  void set_allow_bfloat16(bool value);
  private:
  bool _internal_allow_bfloat16() const;
  void _internal_set_allow_bfloat16(bool value);
  public:

  // optional bool allow_all_select_tf_ops = 39;
  bool has_allow_all_select_tf_ops() const;
  private:
  bool _internal_has_allow_all_select_tf_ops() const;
  public:
  void clear_allow_all_select_tf_ops();
  bool allow_all_select_tf_ops() const;
  void set_allow_all_select_tf_ops(bool value);
  private:
  bool _internal_allow_all_select_tf_ops() const;
  void _internal_set_allow_all_select_tf_ops(bool value);
  public:

  // optional .toco.IODataType accumulation_type = 37;
  bool has_accumulation_type() const;
  private:
  bool _internal_has_accumulation_type() const;
  public:
  void clear_accumulation_type();
  ::toco::IODataType accumulation_type() const;
  void set_accumulation_type(::toco::IODataType value);
  private:
  ::toco::IODataType _internal_accumulation_type() const;
  void _internal_set_accumulation_type(::toco::IODataType value);
  public:

  // optional bool unfold_large_splat_constant = 40 [default = false];
  bool has_unfold_large_splat_constant() const;
  private:
  bool _internal_has_unfold_large_splat_constant() const;
  public:
  void clear_unfold_large_splat_constant();
  bool unfold_large_splat_constant() const;
  void set_unfold_large_splat_constant(bool value);
  private:
  bool _internal_unfold_large_splat_constant() const;
  void _internal_set_unfold_large_splat_constant(bool value);
  public:

  // optional bool default_to_single_batch_in_tensor_list_ops = 42 [default = false];
  bool has_default_to_single_batch_in_tensor_list_ops() const;
  private:
  bool _internal_has_default_to_single_batch_in_tensor_list_ops() const;
  public:
  void clear_default_to_single_batch_in_tensor_list_ops();
  bool default_to_single_batch_in_tensor_list_ops() const;
  void set_default_to_single_batch_in_tensor_list_ops(bool value);
  private:
  bool _internal_default_to_single_batch_in_tensor_list_ops() const;
  void _internal_set_default_to_single_batch_in_tensor_list_ops(bool value);
  public:

  // optional bool disable_per_channel_quantization = 43 [default = false];
  bool has_disable_per_channel_quantization() const;
  private:
  bool _internal_has_disable_per_channel_quantization() const;
  public:
  void clear_disable_per_channel_quantization();
  bool disable_per_channel_quantization() const;
  void set_disable_per_channel_quantization(bool value);
  private:
  bool _internal_disable_per_channel_quantization() const;
  void _internal_set_disable_per_channel_quantization(bool value);
  public:

  // optional bool enable_mlir_dynamic_range_quantizer = 44 [default = false];
  bool has_enable_mlir_dynamic_range_quantizer() const;
  private:
  bool _internal_has_enable_mlir_dynamic_range_quantizer() const;
  public:
  void clear_enable_mlir_dynamic_range_quantizer();
  bool enable_mlir_dynamic_range_quantizer() const;
  void set_enable_mlir_dynamic_range_quantizer(bool value);
  private:
  bool _internal_enable_mlir_dynamic_range_quantizer() const;
  void _internal_set_enable_mlir_dynamic_range_quantizer(bool value);
  public:

  // optional bool disable_infer_tensor_range = 46 [default = false];
  bool has_disable_infer_tensor_range() const;
  private:
  bool _internal_has_disable_infer_tensor_range() const;
  public:
  void clear_disable_infer_tensor_range();
  bool disable_infer_tensor_range() const;
  void set_disable_infer_tensor_range(bool value);
  private:
  bool _internal_disable_infer_tensor_range() const;
  void _internal_set_disable_infer_tensor_range(bool value);
  public:

  // optional bool use_fake_quant_num_bits = 47 [default = false];
  bool has_use_fake_quant_num_bits() const;
  private:
  bool _internal_has_use_fake_quant_num_bits() const;
  public:
  void clear_use_fake_quant_num_bits();
  bool use_fake_quant_num_bits() const;
  void set_use_fake_quant_num_bits(bool value);
  private:
  bool _internal_use_fake_quant_num_bits() const;
  void _internal_set_use_fake_quant_num_bits(bool value);
  public:

  // optional bool enable_dynamic_update_slice = 48 [default = false];
  bool has_enable_dynamic_update_slice() const;
  private:
  bool _internal_has_enable_dynamic_update_slice() const;
  public:
  void clear_enable_dynamic_update_slice();
  bool enable_dynamic_update_slice() const;
  void set_enable_dynamic_update_slice(bool value);
  private:
  bool _internal_enable_dynamic_update_slice() const;
  void _internal_set_enable_dynamic_update_slice(bool value);
  public:

  // optional bool preserve_assert_op = 49 [default = false];
  bool has_preserve_assert_op() const;
  private:
  bool _internal_has_preserve_assert_op() const;
  public:
  void clear_preserve_assert_op();
  bool preserve_assert_op() const;
  void set_preserve_assert_op(bool value);
  private:
  bool _internal_preserve_assert_op() const;
  void _internal_set_preserve_assert_op(bool value);
  public:

  // optional bool guarantee_all_funcs_one_use = 50 [default = false];
  bool has_guarantee_all_funcs_one_use() const;
  private:
  bool _internal_has_guarantee_all_funcs_one_use() const;
  public:
  void clear_guarantee_all_funcs_one_use();
  bool guarantee_all_funcs_one_use() const;
  void set_guarantee_all_funcs_one_use(bool value);
  private:
  bool _internal_guarantee_all_funcs_one_use() const;
  void _internal_set_guarantee_all_funcs_one_use(bool value);
  public:

  // optional bool convert_to_stablehlo = 51 [default = false];
  bool has_convert_to_stablehlo() const;
  private:
  bool _internal_has_convert_to_stablehlo() const;
  public:
  void clear_convert_to_stablehlo();
  bool convert_to_stablehlo() const;
  void set_convert_to_stablehlo(bool value);
  private:
  bool _internal_convert_to_stablehlo() const;
  void _internal_set_convert_to_stablehlo(bool value);
  public:

  // optional bool enable_mlir_variable_quantization = 52 [default = false];
  bool has_enable_mlir_variable_quantization() const;
  private:
  bool _internal_has_enable_mlir_variable_quantization() const;
  public:
  void clear_enable_mlir_variable_quantization();
  bool enable_mlir_variable_quantization() const;
  void set_enable_mlir_variable_quantization(bool value);
  private:
  bool _internal_enable_mlir_variable_quantization() const;
  void _internal_set_enable_mlir_variable_quantization(bool value);
  public:

  // optional bool disable_fuse_mul_and_fc = 53 [default = false];
  bool has_disable_fuse_mul_and_fc() const;
  private:
  bool _internal_has_disable_fuse_mul_and_fc() const;
  public:
  void clear_disable_fuse_mul_and_fc();
  bool disable_fuse_mul_and_fc() const;
  void set_disable_fuse_mul_and_fc(bool value);
  private:
  bool _internal_disable_fuse_mul_and_fc() const;
  void _internal_set_disable_fuse_mul_and_fc(bool value);
  public:

  // optional bool enable_hlo_to_tf_conversion = 55 [default = false, deprecated = true];
  PROTOBUF_DEPRECATED bool has_enable_hlo_to_tf_conversion() const;
  private:
  bool _internal_has_enable_hlo_to_tf_conversion() const;
  public:
  PROTOBUF_DEPRECATED void clear_enable_hlo_to_tf_conversion();
  PROTOBUF_DEPRECATED bool enable_hlo_to_tf_conversion() const;
  PROTOBUF_DEPRECATED void set_enable_hlo_to_tf_conversion(bool value);
  private:
  bool _internal_enable_hlo_to_tf_conversion() const;
  void _internal_set_enable_hlo_to_tf_conversion(bool value);
  public:

  // optional bool use_buffer_offset = 57 [default = false];
  bool has_use_buffer_offset() const;
  private:
  bool _internal_has_use_buffer_offset() const;
  public:
  void clear_use_buffer_offset();
  bool use_buffer_offset() const;
  void set_use_buffer_offset(bool value);
  private:
  bool _internal_use_buffer_offset() const;
  void _internal_set_use_buffer_offset(bool value);
  public:

  // optional bool legalize_custom_tensor_list_ops = 58 [default = false];
  bool has_legalize_custom_tensor_list_ops() const;
  private:
  bool _internal_has_legalize_custom_tensor_list_ops() const;
  public:
  void clear_legalize_custom_tensor_list_ops();
  bool legalize_custom_tensor_list_ops() const;
  void set_legalize_custom_tensor_list_ops(bool value);
  private:
  bool _internal_legalize_custom_tensor_list_ops() const;
  void _internal_set_legalize_custom_tensor_list_ops(bool value);
  public:

  // optional bool reduce_type_precision = 59 [default = false];
  bool has_reduce_type_precision() const;
  private:
  bool _internal_has_reduce_type_precision() const;
  public:
  void clear_reduce_type_precision();
  bool reduce_type_precision() const;
  void set_reduce_type_precision(bool value);
  private:
  bool _internal_reduce_type_precision() const;
  void _internal_set_reduce_type_precision(bool value);
  public:

  // optional bool disable_per_channel_quantization_for_dense_layers = 62 [default = false];
  bool has_disable_per_channel_quantization_for_dense_layers() const;
  private:
  bool _internal_has_disable_per_channel_quantization_for_dense_layers() const;
  public:
  void clear_disable_per_channel_quantization_for_dense_layers();
  bool disable_per_channel_quantization_for_dense_layers() const;
  void set_disable_per_channel_quantization_for_dense_layers(bool value);
  private:
  bool _internal_disable_per_channel_quantization_for_dense_layers() const;
  void _internal_set_disable_per_channel_quantization_for_dense_layers(bool value);
  public:

  // optional bool enable_composite_direct_lowering = 63 [default = false];
  bool has_enable_composite_direct_lowering() const;
  private:
  bool _internal_has_enable_composite_direct_lowering() const;
  public:
  void clear_enable_composite_direct_lowering();
  bool enable_composite_direct_lowering() const;
  void set_enable_composite_direct_lowering(bool value);
  private:
  bool _internal_enable_composite_direct_lowering() const;
  void _internal_set_enable_composite_direct_lowering(bool value);
  public:

  // optional bool canonicalizing_inf_as_min_max_float = 65 [default = false];
  bool has_canonicalizing_inf_as_min_max_float() const;
  private:
  bool _internal_has_canonicalizing_inf_as_min_max_float() const;
  public:
  void clear_canonicalizing_inf_as_min_max_float();
  bool canonicalizing_inf_as_min_max_float() const;
  void set_canonicalizing_inf_as_min_max_float(bool value);
  private:
  bool _internal_canonicalizing_inf_as_min_max_float() const;
  void _internal_set_canonicalizing_inf_as_min_max_float(bool value);
  public:

  // optional .toco.TocoFlags.ModelOriginFramework model_origin_framework = 64 [default = UNSET];
  bool has_model_origin_framework() const;
  private:
  bool _internal_has_model_origin_framework() const;
  public:
  void clear_model_origin_framework();
  ::toco::TocoFlags_ModelOriginFramework model_origin_framework() const;
  void set_model_origin_framework(::toco::TocoFlags_ModelOriginFramework value);
  private:
  ::toco::TocoFlags_ModelOriginFramework _internal_model_origin_framework() const;
  void _internal_set_model_origin_framework(::toco::TocoFlags_ModelOriginFramework value);
  public:

  // optional bool split_tflite_lstm_inputs = 19 [default = true];
  bool has_split_tflite_lstm_inputs() const;
  private:
  bool _internal_has_split_tflite_lstm_inputs() const;
  public:
  void clear_split_tflite_lstm_inputs();
  bool split_tflite_lstm_inputs() const;
  void set_split_tflite_lstm_inputs(bool value);
  private:
  bool _internal_split_tflite_lstm_inputs() const;
  void _internal_set_split_tflite_lstm_inputs(bool value);
  public:

  // optional bool allow_dynamic_tensors = 30 [default = true];
  bool has_allow_dynamic_tensors() const;
  private:
  bool _internal_has_allow_dynamic_tensors() const;
  public:
  void clear_allow_dynamic_tensors();
  bool allow_dynamic_tensors() const;
  void set_allow_dynamic_tensors(bool value);
  private:
  bool _internal_allow_dynamic_tensors() const;
  void _internal_set_allow_dynamic_tensors(bool value);
  public:

  // optional bool enable_tflite_resource_variables = 34 [default = true];
  bool has_enable_tflite_resource_variables() const;
  private:
  bool _internal_has_enable_tflite_resource_variables() const;
  public:
  void clear_enable_tflite_resource_variables();
  bool enable_tflite_resource_variables() const;
  void set_enable_tflite_resource_variables(bool value);
  private:
  bool _internal_enable_tflite_resource_variables() const;
  void _internal_set_enable_tflite_resource_variables(bool value);
  public:

  // optional bool lower_tensor_list_ops = 36 [default = true];
  bool has_lower_tensor_list_ops() const;
  private:
  bool _internal_has_lower_tensor_list_ops() const;
  public:
  void clear_lower_tensor_list_ops();
  bool lower_tensor_list_ops() const;
  void set_lower_tensor_list_ops(bool value);
  private:
  bool _internal_lower_tensor_list_ops() const;
  void _internal_set_lower_tensor_list_ops(bool value);
  public:

  // optional int64 dedupe_array_min_size_bytes = 18 [default = 64];
  bool has_dedupe_array_min_size_bytes() const;
  private:
  bool _internal_has_dedupe_array_min_size_bytes() const;
  public:
  void clear_dedupe_array_min_size_bytes();
  int64_t dedupe_array_min_size_bytes() const;
  void set_dedupe_array_min_size_bytes(int64_t value);
  private:
  int64_t _internal_dedupe_array_min_size_bytes() const;
  void _internal_set_dedupe_array_min_size_bytes(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:toco.TocoFlags)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::HasBits<2> _has_bits_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> custom_opdefs_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> select_user_tf_ops_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> supported_backends_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr dump_graphviz_dir_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr conversion_summary_dir_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr tf_quantization_mode_;
    static const ::PROTOBUF_NAMESPACE_ID::internal::LazyString _i_give_permission_to_break_this_code_default_qdq_conversion_mode_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr qdq_conversion_mode_;
    ::stablehlo::quantization::QuantizationOptions* quantization_options_;
    ::tensorflow::converter::DebugOptions* debug_options_;
    ::stablehlo::quantization::QuantizationConfig* quantization_config_;
    int input_format_;
    int output_format_;
    int inference_type_;
    float default_ranges_min_;
    float default_ranges_max_;
    bool drop_fake_quant_;
    bool reorder_across_fake_quant_;
    bool allow_custom_ops_;
    bool drop_control_dependency_;
    int inference_input_type_;
    float default_int16_ranges_min_;
    float default_int16_ranges_max_;
    bool debug_disable_recurrent_cell_fusion_;
    bool propagate_fake_quant_num_bits_;
    bool allow_nudging_weights_to_use_fast_gemm_kernel_;
    bool quantize_weights_;
    bool dump_graphviz_include_video_;
    bool post_training_quantize_;
    bool enable_select_tf_ops_;
    bool force_select_tf_ops_;
    bool quantize_to_float16_;
    bool unfold_batchmatmul_;
    bool allow_bfloat16_;
    bool allow_all_select_tf_ops_;
    int accumulation_type_;
    bool unfold_large_splat_constant_;
    bool default_to_single_batch_in_tensor_list_ops_;
    bool disable_per_channel_quantization_;
    bool enable_mlir_dynamic_range_quantizer_;
    bool disable_infer_tensor_range_;
    bool use_fake_quant_num_bits_;
    bool enable_dynamic_update_slice_;
    bool preserve_assert_op_;
    bool guarantee_all_funcs_one_use_;
    bool convert_to_stablehlo_;
    bool enable_mlir_variable_quantization_;
    bool disable_fuse_mul_and_fc_;
    bool enable_hlo_to_tf_conversion_;
    bool use_buffer_offset_;
    bool legalize_custom_tensor_list_ops_;
    bool reduce_type_precision_;
    bool disable_per_channel_quantization_for_dense_layers_;
    bool enable_composite_direct_lowering_;
    bool canonicalizing_inf_as_min_max_float_;
    int model_origin_framework_;
    bool split_tflite_lstm_inputs_;
    bool allow_dynamic_tensors_;
    bool enable_tflite_resource_variables_;
    bool lower_tensor_list_ops_;
    int64_t dedupe_array_min_size_bytes_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2flite_2ftoco_2ftoco_5fflags_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// TocoFlags

// optional .toco.FileFormat input_format = 1;
inline bool TocoFlags::_internal_has_input_format() const {
  bool value = (_impl_._has_bits_[0] & 0x00000080u) != 0;
  return value;
}
inline bool TocoFlags::has_input_format() const {
  return _internal_has_input_format();
}
inline void TocoFlags::clear_input_format() {
  _impl_.input_format_ = 0;
  _impl_._has_bits_[0] &= ~0x00000080u;
}
inline ::toco::FileFormat TocoFlags::_internal_input_format() const {
  return static_cast< ::toco::FileFormat >(_impl_.input_format_);
}
inline ::toco::FileFormat TocoFlags::input_format() const {
  // @@protoc_insertion_point(field_get:toco.TocoFlags.input_format)
  return _internal_input_format();
}
inline void TocoFlags::_internal_set_input_format(::toco::FileFormat value) {
  assert(::toco::FileFormat_IsValid(value));
  _impl_._has_bits_[0] |= 0x00000080u;
  _impl_.input_format_ = value;
}
inline void TocoFlags::set_input_format(::toco::FileFormat value) {
  _internal_set_input_format(value);
  // @@protoc_insertion_point(field_set:toco.TocoFlags.input_format)
}

// optional .toco.FileFormat output_format = 2;
inline bool TocoFlags::_internal_has_output_format() const {
  bool value = (_impl_._has_bits_[0] & 0x00000100u) != 0;
  return value;
}
inline bool TocoFlags::has_output_format() const {
  return _internal_has_output_format();
}
inline void TocoFlags::clear_output_format() {
  _impl_.output_format_ = 0;
  _impl_._has_bits_[0] &= ~0x00000100u;
}
inline ::toco::FileFormat TocoFlags::_internal_output_format() const {
  return static_cast< ::toco::FileFormat >(_impl_.output_format_);
}
inline ::toco::FileFormat TocoFlags::output_format() const {
  // @@protoc_insertion_point(field_get:toco.TocoFlags.output_format)
  return _internal_output_format();
}
inline void TocoFlags::_internal_set_output_format(::toco::FileFormat value) {
  assert(::toco::FileFormat_IsValid(value));
  _impl_._has_bits_[0] |= 0x00000100u;
  _impl_.output_format_ = value;
}
inline void TocoFlags::set_output_format(::toco::FileFormat value) {
  _internal_set_output_format(value);
  // @@protoc_insertion_point(field_set:toco.TocoFlags.output_format)
}

// optional .toco.IODataType inference_input_type = 11;
inline bool TocoFlags::_internal_has_inference_input_type() const {
  bool value = (_impl_._has_bits_[0] & 0x00010000u) != 0;
  return value;
}
inline bool TocoFlags::has_inference_input_type() const {
  return _internal_has_inference_input_type();
}
inline void TocoFlags::clear_inference_input_type() {
  _impl_.inference_input_type_ = 0;
  _impl_._has_bits_[0] &= ~0x00010000u;
}
inline ::toco::IODataType TocoFlags::_internal_inference_input_type() const {
  return static_cast< ::toco::IODataType >(_impl_.inference_input_type_);
}
inline ::toco::IODataType TocoFlags::inference_input_type() const {
  // @@protoc_insertion_point(field_get:toco.TocoFlags.inference_input_type)
  return _internal_inference_input_type();
}
inline void TocoFlags::_internal_set_inference_input_type(::toco::IODataType value) {
  assert(::toco::IODataType_IsValid(value));
  _impl_._has_bits_[0] |= 0x00010000u;
  _impl_.inference_input_type_ = value;
}
inline void TocoFlags::set_inference_input_type(::toco::IODataType value) {
  _internal_set_inference_input_type(value);
  // @@protoc_insertion_point(field_set:toco.TocoFlags.inference_input_type)
}

// optional .toco.IODataType inference_type = 4;
inline bool TocoFlags::_internal_has_inference_type() const {
  bool value = (_impl_._has_bits_[0] & 0x00000200u) != 0;
  return value;
}
inline bool TocoFlags::has_inference_type() const {
  return _internal_has_inference_type();
}
inline void TocoFlags::clear_inference_type() {
  _impl_.inference_type_ = 0;
  _impl_._has_bits_[0] &= ~0x00000200u;
}
inline ::toco::IODataType TocoFlags::_internal_inference_type() const {
  return static_cast< ::toco::IODataType >(_impl_.inference_type_);
}
inline ::toco::IODataType TocoFlags::inference_type() const {
  // @@protoc_insertion_point(field_get:toco.TocoFlags.inference_type)
  return _internal_inference_type();
}
inline void TocoFlags::_internal_set_inference_type(::toco::IODataType value) {
  assert(::toco::IODataType_IsValid(value));
  _impl_._has_bits_[0] |= 0x00000200u;
  _impl_.inference_type_ = value;
}
inline void TocoFlags::set_inference_type(::toco::IODataType value) {
  _internal_set_inference_type(value);
  // @@protoc_insertion_point(field_set:toco.TocoFlags.inference_type)
}

// optional float default_ranges_min = 5;
inline bool TocoFlags::_internal_has_default_ranges_min() const {
  bool value = (_impl_._has_bits_[0] & 0x00000400u) != 0;
  return value;
}
inline bool TocoFlags::has_default_ranges_min() const {
  return _internal_has_default_ranges_min();
}
inline void TocoFlags::clear_default_ranges_min() {
  _impl_.default_ranges_min_ = 0;
  _impl_._has_bits_[0] &= ~0x00000400u;
}
inline float TocoFlags::_internal_default_ranges_min() const {
  return _impl_.default_ranges_min_;
}
inline float TocoFlags::default_ranges_min() const {
  // @@protoc_insertion_point(field_get:toco.TocoFlags.default_ranges_min)
  return _internal_default_ranges_min();
}
inline void TocoFlags::_internal_set_default_ranges_min(float value) {
  _impl_._has_bits_[0] |= 0x00000400u;
  _impl_.default_ranges_min_ = value;
}
inline void TocoFlags::set_default_ranges_min(float value) {
  _internal_set_default_ranges_min(value);
  // @@protoc_insertion_point(field_set:toco.TocoFlags.default_ranges_min)
}

// optional float default_ranges_max = 6;
inline bool TocoFlags::_internal_has_default_ranges_max() const {
  bool value = (_impl_._has_bits_[0] & 0x00000800u) != 0;
  return value;
}
inline bool TocoFlags::has_default_ranges_max() const {
  return _internal_has_default_ranges_max();
}
inline void TocoFlags::clear_default_ranges_max() {
  _impl_.default_ranges_max_ = 0;
  _impl_._has_bits_[0] &= ~0x00000800u;
}
inline float TocoFlags::_internal_default_ranges_max() const {
  return _impl_.default_ranges_max_;
}
inline float TocoFlags::default_ranges_max() const {
  // @@protoc_insertion_point(field_get:toco.TocoFlags.default_ranges_max)
  return _internal_default_ranges_max();
}
inline void TocoFlags::_internal_set_default_ranges_max(float value) {
  _impl_._has_bits_[0] |= 0x00000800u;
  _impl_.default_ranges_max_ = value;
}
inline void TocoFlags::set_default_ranges_max(float value) {
  _internal_set_default_ranges_max(value);
  // @@protoc_insertion_point(field_set:toco.TocoFlags.default_ranges_max)
}

// optional float default_int16_ranges_min = 15;
inline bool TocoFlags::_internal_has_default_int16_ranges_min() const {
  bool value = (_impl_._has_bits_[0] & 0x00020000u) != 0;
  return value;
}
inline bool TocoFlags::has_default_int16_ranges_min() const {
  return _internal_has_default_int16_ranges_min();
}
inline void TocoFlags::clear_default_int16_ranges_min() {
  _impl_.default_int16_ranges_min_ = 0;
  _impl_._has_bits_[0] &= ~0x00020000u;
}
inline float TocoFlags::_internal_default_int16_ranges_min() const {
  return _impl_.default_int16_ranges_min_;
}
inline float TocoFlags::default_int16_ranges_min() const {
  // @@protoc_insertion_point(field_get:toco.TocoFlags.default_int16_ranges_min)
  return _internal_default_int16_ranges_min();
}
inline void TocoFlags::_internal_set_default_int16_ranges_min(float value) {
  _impl_._has_bits_[0] |= 0x00020000u;
  _impl_.default_int16_ranges_min_ = value;
}
inline void TocoFlags::set_default_int16_ranges_min(float value) {
  _internal_set_default_int16_ranges_min(value);
  // @@protoc_insertion_point(field_set:toco.TocoFlags.default_int16_ranges_min)
}

// optional float default_int16_ranges_max = 16;
inline bool TocoFlags::_internal_has_default_int16_ranges_max() const {
  bool value = (_impl_._has_bits_[0] & 0x00040000u) != 0;
  return value;
}
inline bool TocoFlags::has_default_int16_ranges_max() const {
  return _internal_has_default_int16_ranges_max();
}
inline void TocoFlags::clear_default_int16_ranges_max() {
  _impl_.default_int16_ranges_max_ = 0;
  _impl_._has_bits_[0] &= ~0x00040000u;
}
inline float TocoFlags::_internal_default_int16_ranges_max() const {
  return _impl_.default_int16_ranges_max_;
}
inline float TocoFlags::default_int16_ranges_max() const {
  // @@protoc_insertion_point(field_get:toco.TocoFlags.default_int16_ranges_max)
  return _internal_default_int16_ranges_max();
}
inline void TocoFlags::_internal_set_default_int16_ranges_max(float value) {
  _impl_._has_bits_[0] |= 0x00040000u;
  _impl_.default_int16_ranges_max_ = value;
}
inline void TocoFlags::set_default_int16_ranges_max(float value) {
  _internal_set_default_int16_ranges_max(value);
  // @@protoc_insertion_point(field_set:toco.TocoFlags.default_int16_ranges_max)
}

// optional bool drop_fake_quant = 7;
inline bool TocoFlags::_internal_has_drop_fake_quant() const {
  bool value = (_impl_._has_bits_[0] & 0x00001000u) != 0;
  return value;
}
inline bool TocoFlags::has_drop_fake_quant() const {
  return _internal_has_drop_fake_quant();
}
inline void TocoFlags::clear_drop_fake_quant() {
  _impl_.drop_fake_quant_ = false;
  _impl_._has_bits_[0] &= ~0x00001000u;
}
inline bool TocoFlags::_internal_drop_fake_quant() const {
  return _impl_.drop_fake_quant_;
}
inline bool TocoFlags::drop_fake_quant() const {
  // @@protoc_insertion_point(field_get:toco.TocoFlags.drop_fake_quant)
  return _internal_drop_fake_quant();
}
inline void TocoFlags::_internal_set_drop_fake_quant(bool value) {
  _impl_._has_bits_[0] |= 0x00001000u;
  _impl_.drop_fake_quant_ = value;
}
inline void TocoFlags::set_drop_fake_quant(bool value) {
  _internal_set_drop_fake_quant(value);
  // @@protoc_insertion_point(field_set:toco.TocoFlags.drop_fake_quant)
}

// optional bool reorder_across_fake_quant = 8;
inline bool TocoFlags::_internal_has_reorder_across_fake_quant() const {
  bool value = (_impl_._has_bits_[0] & 0x00002000u) != 0;
  return value;
}
inline bool TocoFlags::has_reorder_across_fake_quant() const {
  return _internal_has_reorder_across_fake_quant();
}
inline void TocoFlags::clear_reorder_across_fake_quant() {
  _impl_.reorder_across_fake_quant_ = false;
  _impl_._has_bits_[0] &= ~0x00002000u;
}
inline bool TocoFlags::_internal_reorder_across_fake_quant() const {
  return _impl_.reorder_across_fake_quant_;
}
inline bool TocoFlags::reorder_across_fake_quant() const {
  // @@protoc_insertion_point(field_get:toco.TocoFlags.reorder_across_fake_quant)
  return _internal_reorder_across_fake_quant();
}
inline void TocoFlags::_internal_set_reorder_across_fake_quant(bool value) {
  _impl_._has_bits_[0] |= 0x00002000u;
  _impl_.reorder_across_fake_quant_ = value;
}
inline void TocoFlags::set_reorder_across_fake_quant(bool value) {
  _internal_set_reorder_across_fake_quant(value);
  // @@protoc_insertion_point(field_set:toco.TocoFlags.reorder_across_fake_quant)
}

// optional bool allow_custom_ops = 10;
inline bool TocoFlags::_internal_has_allow_custom_ops() const {
  bool value = (_impl_._has_bits_[0] & 0x00004000u) != 0;
  return value;
}
inline bool TocoFlags::has_allow_custom_ops() const {
  return _internal_has_allow_custom_ops();
}
inline void TocoFlags::clear_allow_custom_ops() {
  _impl_.allow_custom_ops_ = false;
  _impl_._has_bits_[0] &= ~0x00004000u;
}
inline bool TocoFlags::_internal_allow_custom_ops() const {
  return _impl_.allow_custom_ops_;
}
inline bool TocoFlags::allow_custom_ops() const {
  // @@protoc_insertion_point(field_get:toco.TocoFlags.allow_custom_ops)
  return _internal_allow_custom_ops();
}
inline void TocoFlags::_internal_set_allow_custom_ops(bool value) {
  _impl_._has_bits_[0] |= 0x00004000u;
  _impl_.allow_custom_ops_ = value;
}
inline void TocoFlags::set_allow_custom_ops(bool value) {
  _internal_set_allow_custom_ops(value);
  // @@protoc_insertion_point(field_set:toco.TocoFlags.allow_custom_ops)
}

// optional bool drop_control_dependency = 12;
inline bool TocoFlags::_internal_has_drop_control_dependency() const {
  bool value = (_impl_._has_bits_[0] & 0x00008000u) != 0;
  return value;
}
inline bool TocoFlags::has_drop_control_dependency() const {
  return _internal_has_drop_control_dependency();
}
inline void TocoFlags::clear_drop_control_dependency() {
  _impl_.drop_control_dependency_ = false;
  _impl_._has_bits_[0] &= ~0x00008000u;
}
inline bool TocoFlags::_internal_drop_control_dependency() const {
  return _impl_.drop_control_dependency_;
}
inline bool TocoFlags::drop_control_dependency() const {
  // @@protoc_insertion_point(field_get:toco.TocoFlags.drop_control_dependency)
  return _internal_drop_control_dependency();
}
inline void TocoFlags::_internal_set_drop_control_dependency(bool value) {
  _impl_._has_bits_[0] |= 0x00008000u;
  _impl_.drop_control_dependency_ = value;
}
inline void TocoFlags::set_drop_control_dependency(bool value) {
  _internal_set_drop_control_dependency(value);
  // @@protoc_insertion_point(field_set:toco.TocoFlags.drop_control_dependency)
}

// optional bool debug_disable_recurrent_cell_fusion = 13;
inline bool TocoFlags::_internal_has_debug_disable_recurrent_cell_fusion() const {
  bool value = (_impl_._has_bits_[0] & 0x00080000u) != 0;
  return value;
}
inline bool TocoFlags::has_debug_disable_recurrent_cell_fusion() const {
  return _internal_has_debug_disable_recurrent_cell_fusion();
}
inline void TocoFlags::clear_debug_disable_recurrent_cell_fusion() {
  _impl_.debug_disable_recurrent_cell_fusion_ = false;
  _impl_._has_bits_[0] &= ~0x00080000u;
}
inline bool TocoFlags::_internal_debug_disable_recurrent_cell_fusion() const {
  return _impl_.debug_disable_recurrent_cell_fusion_;
}
inline bool TocoFlags::debug_disable_recurrent_cell_fusion() const {
  // @@protoc_insertion_point(field_get:toco.TocoFlags.debug_disable_recurrent_cell_fusion)
  return _internal_debug_disable_recurrent_cell_fusion();
}
inline void TocoFlags::_internal_set_debug_disable_recurrent_cell_fusion(bool value) {
  _impl_._has_bits_[0] |= 0x00080000u;
  _impl_.debug_disable_recurrent_cell_fusion_ = value;
}
inline void TocoFlags::set_debug_disable_recurrent_cell_fusion(bool value) {
  _internal_set_debug_disable_recurrent_cell_fusion(value);
  // @@protoc_insertion_point(field_set:toco.TocoFlags.debug_disable_recurrent_cell_fusion)
}

// optional bool propagate_fake_quant_num_bits = 14;
inline bool TocoFlags::_internal_has_propagate_fake_quant_num_bits() const {
  bool value = (_impl_._has_bits_[0] & 0x00100000u) != 0;
  return value;
}
inline bool TocoFlags::has_propagate_fake_quant_num_bits() const {
  return _internal_has_propagate_fake_quant_num_bits();
}
inline void TocoFlags::clear_propagate_fake_quant_num_bits() {
  _impl_.propagate_fake_quant_num_bits_ = false;
  _impl_._has_bits_[0] &= ~0x00100000u;
}
inline bool TocoFlags::_internal_propagate_fake_quant_num_bits() const {
  return _impl_.propagate_fake_quant_num_bits_;
}
inline bool TocoFlags::propagate_fake_quant_num_bits() const {
  // @@protoc_insertion_point(field_get:toco.TocoFlags.propagate_fake_quant_num_bits)
  return _internal_propagate_fake_quant_num_bits();
}
inline void TocoFlags::_internal_set_propagate_fake_quant_num_bits(bool value) {
  _impl_._has_bits_[0] |= 0x00100000u;
  _impl_.propagate_fake_quant_num_bits_ = value;
}
inline void TocoFlags::set_propagate_fake_quant_num_bits(bool value) {
  _internal_set_propagate_fake_quant_num_bits(value);
  // @@protoc_insertion_point(field_set:toco.TocoFlags.propagate_fake_quant_num_bits)
}

// optional bool allow_nudging_weights_to_use_fast_gemm_kernel = 17;
inline bool TocoFlags::_internal_has_allow_nudging_weights_to_use_fast_gemm_kernel() const {
  bool value = (_impl_._has_bits_[0] & 0x00200000u) != 0;
  return value;
}
inline bool TocoFlags::has_allow_nudging_weights_to_use_fast_gemm_kernel() const {
  return _internal_has_allow_nudging_weights_to_use_fast_gemm_kernel();
}
inline void TocoFlags::clear_allow_nudging_weights_to_use_fast_gemm_kernel() {
  _impl_.allow_nudging_weights_to_use_fast_gemm_kernel_ = false;
  _impl_._has_bits_[0] &= ~0x00200000u;
}
inline bool TocoFlags::_internal_allow_nudging_weights_to_use_fast_gemm_kernel() const {
  return _impl_.allow_nudging_weights_to_use_fast_gemm_kernel_;
}
inline bool TocoFlags::allow_nudging_weights_to_use_fast_gemm_kernel() const {
  // @@protoc_insertion_point(field_get:toco.TocoFlags.allow_nudging_weights_to_use_fast_gemm_kernel)
  return _internal_allow_nudging_weights_to_use_fast_gemm_kernel();
}
inline void TocoFlags::_internal_set_allow_nudging_weights_to_use_fast_gemm_kernel(bool value) {
  _impl_._has_bits_[0] |= 0x00200000u;
  _impl_.allow_nudging_weights_to_use_fast_gemm_kernel_ = value;
}
inline void TocoFlags::set_allow_nudging_weights_to_use_fast_gemm_kernel(bool value) {
  _internal_set_allow_nudging_weights_to_use_fast_gemm_kernel(value);
  // @@protoc_insertion_point(field_set:toco.TocoFlags.allow_nudging_weights_to_use_fast_gemm_kernel)
}

// optional int64 dedupe_array_min_size_bytes = 18 [default = 64];
inline bool TocoFlags::_internal_has_dedupe_array_min_size_bytes() const {
  bool value = (_impl_._has_bits_[1] & 0x01000000u) != 0;
  return value;
}
inline bool TocoFlags::has_dedupe_array_min_size_bytes() const {
  return _internal_has_dedupe_array_min_size_bytes();
}
inline void TocoFlags::clear_dedupe_array_min_size_bytes() {
  _impl_.dedupe_array_min_size_bytes_ = int64_t{64};
  _impl_._has_bits_[1] &= ~0x01000000u;
}
inline int64_t TocoFlags::_internal_dedupe_array_min_size_bytes() const {
  return _impl_.dedupe_array_min_size_bytes_;
}
inline int64_t TocoFlags::dedupe_array_min_size_bytes() const {
  // @@protoc_insertion_point(field_get:toco.TocoFlags.dedupe_array_min_size_bytes)
  return _internal_dedupe_array_min_size_bytes();
}
inline void TocoFlags::_internal_set_dedupe_array_min_size_bytes(int64_t value) {
  _impl_._has_bits_[1] |= 0x01000000u;
  _impl_.dedupe_array_min_size_bytes_ = value;
}
inline void TocoFlags::set_dedupe_array_min_size_bytes(int64_t value) {
  _internal_set_dedupe_array_min_size_bytes(value);
  // @@protoc_insertion_point(field_set:toco.TocoFlags.dedupe_array_min_size_bytes)
}

// optional bool split_tflite_lstm_inputs = 19 [default = true];
inline bool TocoFlags::_internal_has_split_tflite_lstm_inputs() const {
  bool value = (_impl_._has_bits_[1] & 0x00100000u) != 0;
  return value;
}
inline bool TocoFlags::has_split_tflite_lstm_inputs() const {
  return _internal_has_split_tflite_lstm_inputs();
}
inline void TocoFlags::clear_split_tflite_lstm_inputs() {
  _impl_.split_tflite_lstm_inputs_ = true;
  _impl_._has_bits_[1] &= ~0x00100000u;
}
inline bool TocoFlags::_internal_split_tflite_lstm_inputs() const {
  return _impl_.split_tflite_lstm_inputs_;
}
inline bool TocoFlags::split_tflite_lstm_inputs() const {
  // @@protoc_insertion_point(field_get:toco.TocoFlags.split_tflite_lstm_inputs)
  return _internal_split_tflite_lstm_inputs();
}
inline void TocoFlags::_internal_set_split_tflite_lstm_inputs(bool value) {
  _impl_._has_bits_[1] |= 0x00100000u;
  _impl_.split_tflite_lstm_inputs_ = value;
}
inline void TocoFlags::set_split_tflite_lstm_inputs(bool value) {
  _internal_set_split_tflite_lstm_inputs(value);
  // @@protoc_insertion_point(field_set:toco.TocoFlags.split_tflite_lstm_inputs)
}

// optional bool quantize_weights = 20 [default = false];
inline bool TocoFlags::_internal_has_quantize_weights() const {
  bool value = (_impl_._has_bits_[0] & 0x00400000u) != 0;
  return value;
}
inline bool TocoFlags::has_quantize_weights() const {
  return _internal_has_quantize_weights();
}
inline void TocoFlags::clear_quantize_weights() {
  _impl_.quantize_weights_ = false;
  _impl_._has_bits_[0] &= ~0x00400000u;
}
inline bool TocoFlags::_internal_quantize_weights() const {
  return _impl_.quantize_weights_;
}
inline bool TocoFlags::quantize_weights() const {
  // @@protoc_insertion_point(field_get:toco.TocoFlags.quantize_weights)
  return _internal_quantize_weights();
}
inline void TocoFlags::_internal_set_quantize_weights(bool value) {
  _impl_._has_bits_[0] |= 0x00400000u;
  _impl_.quantize_weights_ = value;
}
inline void TocoFlags::set_quantize_weights(bool value) {
  _internal_set_quantize_weights(value);
  // @@protoc_insertion_point(field_set:toco.TocoFlags.quantize_weights)
}

// optional string dump_graphviz_dir = 24;
inline bool TocoFlags::_internal_has_dump_graphviz_dir() const {
  bool value = (_impl_._has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool TocoFlags::has_dump_graphviz_dir() const {
  return _internal_has_dump_graphviz_dir();
}
inline void TocoFlags::clear_dump_graphviz_dir() {
  _impl_.dump_graphviz_dir_.ClearToEmpty();
  _impl_._has_bits_[0] &= ~0x00000001u;
}
inline const std::string& TocoFlags::dump_graphviz_dir() const {
  // @@protoc_insertion_point(field_get:toco.TocoFlags.dump_graphviz_dir)
  return _internal_dump_graphviz_dir();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void TocoFlags::set_dump_graphviz_dir(ArgT0&& arg0, ArgT... args) {
 _impl_._has_bits_[0] |= 0x00000001u;
 _impl_.dump_graphviz_dir_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:toco.TocoFlags.dump_graphviz_dir)
}
inline std::string* TocoFlags::mutable_dump_graphviz_dir() {
  std::string* _s = _internal_mutable_dump_graphviz_dir();
  // @@protoc_insertion_point(field_mutable:toco.TocoFlags.dump_graphviz_dir)
  return _s;
}
inline const std::string& TocoFlags::_internal_dump_graphviz_dir() const {
  return _impl_.dump_graphviz_dir_.Get();
}
inline void TocoFlags::_internal_set_dump_graphviz_dir(const std::string& value) {
  _impl_._has_bits_[0] |= 0x00000001u;
  _impl_.dump_graphviz_dir_.Set(value, GetArenaForAllocation());
}
inline std::string* TocoFlags::_internal_mutable_dump_graphviz_dir() {
  _impl_._has_bits_[0] |= 0x00000001u;
  return _impl_.dump_graphviz_dir_.Mutable(GetArenaForAllocation());
}
inline std::string* TocoFlags::release_dump_graphviz_dir() {
  // @@protoc_insertion_point(field_release:toco.TocoFlags.dump_graphviz_dir)
  if (!_internal_has_dump_graphviz_dir()) {
    return nullptr;
  }
  _impl_._has_bits_[0] &= ~0x00000001u;
  auto* p = _impl_.dump_graphviz_dir_.Release();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.dump_graphviz_dir_.IsDefault()) {
    _impl_.dump_graphviz_dir_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void TocoFlags::set_allocated_dump_graphviz_dir(std::string* dump_graphviz_dir) {
  if (dump_graphviz_dir != nullptr) {
    _impl_._has_bits_[0] |= 0x00000001u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000001u;
  }
  _impl_.dump_graphviz_dir_.SetAllocated(dump_graphviz_dir, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.dump_graphviz_dir_.IsDefault()) {
    _impl_.dump_graphviz_dir_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:toco.TocoFlags.dump_graphviz_dir)
}

// optional bool dump_graphviz_include_video = 25;
inline bool TocoFlags::_internal_has_dump_graphviz_include_video() const {
  bool value = (_impl_._has_bits_[0] & 0x00800000u) != 0;
  return value;
}
inline bool TocoFlags::has_dump_graphviz_include_video() const {
  return _internal_has_dump_graphviz_include_video();
}
inline void TocoFlags::clear_dump_graphviz_include_video() {
  _impl_.dump_graphviz_include_video_ = false;
  _impl_._has_bits_[0] &= ~0x00800000u;
}
inline bool TocoFlags::_internal_dump_graphviz_include_video() const {
  return _impl_.dump_graphviz_include_video_;
}
inline bool TocoFlags::dump_graphviz_include_video() const {
  // @@protoc_insertion_point(field_get:toco.TocoFlags.dump_graphviz_include_video)
  return _internal_dump_graphviz_include_video();
}
inline void TocoFlags::_internal_set_dump_graphviz_include_video(bool value) {
  _impl_._has_bits_[0] |= 0x00800000u;
  _impl_.dump_graphviz_include_video_ = value;
}
inline void TocoFlags::set_dump_graphviz_include_video(bool value) {
  _internal_set_dump_graphviz_include_video(value);
  // @@protoc_insertion_point(field_set:toco.TocoFlags.dump_graphviz_include_video)
}

// optional bool post_training_quantize = 26 [default = false];
inline bool TocoFlags::_internal_has_post_training_quantize() const {
  bool value = (_impl_._has_bits_[0] & 0x01000000u) != 0;
  return value;
}
inline bool TocoFlags::has_post_training_quantize() const {
  return _internal_has_post_training_quantize();
}
inline void TocoFlags::clear_post_training_quantize() {
  _impl_.post_training_quantize_ = false;
  _impl_._has_bits_[0] &= ~0x01000000u;
}
inline bool TocoFlags::_internal_post_training_quantize() const {
  return _impl_.post_training_quantize_;
}
inline bool TocoFlags::post_training_quantize() const {
  // @@protoc_insertion_point(field_get:toco.TocoFlags.post_training_quantize)
  return _internal_post_training_quantize();
}
inline void TocoFlags::_internal_set_post_training_quantize(bool value) {
  _impl_._has_bits_[0] |= 0x01000000u;
  _impl_.post_training_quantize_ = value;
}
inline void TocoFlags::set_post_training_quantize(bool value) {
  _internal_set_post_training_quantize(value);
  // @@protoc_insertion_point(field_set:toco.TocoFlags.post_training_quantize)
}

// optional bool enable_select_tf_ops = 27 [default = false];
inline bool TocoFlags::_internal_has_enable_select_tf_ops() const {
  bool value = (_impl_._has_bits_[0] & 0x02000000u) != 0;
  return value;
}
inline bool TocoFlags::has_enable_select_tf_ops() const {
  return _internal_has_enable_select_tf_ops();
}
inline void TocoFlags::clear_enable_select_tf_ops() {
  _impl_.enable_select_tf_ops_ = false;
  _impl_._has_bits_[0] &= ~0x02000000u;
}
inline bool TocoFlags::_internal_enable_select_tf_ops() const {
  return _impl_.enable_select_tf_ops_;
}
inline bool TocoFlags::enable_select_tf_ops() const {
  // @@protoc_insertion_point(field_get:toco.TocoFlags.enable_select_tf_ops)
  return _internal_enable_select_tf_ops();
}
inline void TocoFlags::_internal_set_enable_select_tf_ops(bool value) {
  _impl_._has_bits_[0] |= 0x02000000u;
  _impl_.enable_select_tf_ops_ = value;
}
inline void TocoFlags::set_enable_select_tf_ops(bool value) {
  _internal_set_enable_select_tf_ops(value);
  // @@protoc_insertion_point(field_set:toco.TocoFlags.enable_select_tf_ops)
}

// optional bool force_select_tf_ops = 28 [default = false];
inline bool TocoFlags::_internal_has_force_select_tf_ops() const {
  bool value = (_impl_._has_bits_[0] & 0x04000000u) != 0;
  return value;
}
inline bool TocoFlags::has_force_select_tf_ops() const {
  return _internal_has_force_select_tf_ops();
}
inline void TocoFlags::clear_force_select_tf_ops() {
  _impl_.force_select_tf_ops_ = false;
  _impl_._has_bits_[0] &= ~0x04000000u;
}
inline bool TocoFlags::_internal_force_select_tf_ops() const {
  return _impl_.force_select_tf_ops_;
}
inline bool TocoFlags::force_select_tf_ops() const {
  // @@protoc_insertion_point(field_get:toco.TocoFlags.force_select_tf_ops)
  return _internal_force_select_tf_ops();
}
inline void TocoFlags::_internal_set_force_select_tf_ops(bool value) {
  _impl_._has_bits_[0] |= 0x04000000u;
  _impl_.force_select_tf_ops_ = value;
}
inline void TocoFlags::set_force_select_tf_ops(bool value) {
  _internal_set_force_select_tf_ops(value);
  // @@protoc_insertion_point(field_set:toco.TocoFlags.force_select_tf_ops)
}

// optional bool quantize_to_float16 = 29 [default = false];
inline bool TocoFlags::_internal_has_quantize_to_float16() const {
  bool value = (_impl_._has_bits_[0] & 0x08000000u) != 0;
  return value;
}
inline bool TocoFlags::has_quantize_to_float16() const {
  return _internal_has_quantize_to_float16();
}
inline void TocoFlags::clear_quantize_to_float16() {
  _impl_.quantize_to_float16_ = false;
  _impl_._has_bits_[0] &= ~0x08000000u;
}
inline bool TocoFlags::_internal_quantize_to_float16() const {
  return _impl_.quantize_to_float16_;
}
inline bool TocoFlags::quantize_to_float16() const {
  // @@protoc_insertion_point(field_get:toco.TocoFlags.quantize_to_float16)
  return _internal_quantize_to_float16();
}
inline void TocoFlags::_internal_set_quantize_to_float16(bool value) {
  _impl_._has_bits_[0] |= 0x08000000u;
  _impl_.quantize_to_float16_ = value;
}
inline void TocoFlags::set_quantize_to_float16(bool value) {
  _internal_set_quantize_to_float16(value);
  // @@protoc_insertion_point(field_set:toco.TocoFlags.quantize_to_float16)
}

// optional bool allow_dynamic_tensors = 30 [default = true];
inline bool TocoFlags::_internal_has_allow_dynamic_tensors() const {
  bool value = (_impl_._has_bits_[1] & 0x00200000u) != 0;
  return value;
}
inline bool TocoFlags::has_allow_dynamic_tensors() const {
  return _internal_has_allow_dynamic_tensors();
}
inline void TocoFlags::clear_allow_dynamic_tensors() {
  _impl_.allow_dynamic_tensors_ = true;
  _impl_._has_bits_[1] &= ~0x00200000u;
}
inline bool TocoFlags::_internal_allow_dynamic_tensors() const {
  return _impl_.allow_dynamic_tensors_;
}
inline bool TocoFlags::allow_dynamic_tensors() const {
  // @@protoc_insertion_point(field_get:toco.TocoFlags.allow_dynamic_tensors)
  return _internal_allow_dynamic_tensors();
}
inline void TocoFlags::_internal_set_allow_dynamic_tensors(bool value) {
  _impl_._has_bits_[1] |= 0x00200000u;
  _impl_.allow_dynamic_tensors_ = value;
}
inline void TocoFlags::set_allow_dynamic_tensors(bool value) {
  _internal_set_allow_dynamic_tensors(value);
  // @@protoc_insertion_point(field_set:toco.TocoFlags.allow_dynamic_tensors)
}

// optional string conversion_summary_dir = 31;
inline bool TocoFlags::_internal_has_conversion_summary_dir() const {
  bool value = (_impl_._has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool TocoFlags::has_conversion_summary_dir() const {
  return _internal_has_conversion_summary_dir();
}
inline void TocoFlags::clear_conversion_summary_dir() {
  _impl_.conversion_summary_dir_.ClearToEmpty();
  _impl_._has_bits_[0] &= ~0x00000002u;
}
inline const std::string& TocoFlags::conversion_summary_dir() const {
  // @@protoc_insertion_point(field_get:toco.TocoFlags.conversion_summary_dir)
  return _internal_conversion_summary_dir();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void TocoFlags::set_conversion_summary_dir(ArgT0&& arg0, ArgT... args) {
 _impl_._has_bits_[0] |= 0x00000002u;
 _impl_.conversion_summary_dir_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:toco.TocoFlags.conversion_summary_dir)
}
inline std::string* TocoFlags::mutable_conversion_summary_dir() {
  std::string* _s = _internal_mutable_conversion_summary_dir();
  // @@protoc_insertion_point(field_mutable:toco.TocoFlags.conversion_summary_dir)
  return _s;
}
inline const std::string& TocoFlags::_internal_conversion_summary_dir() const {
  return _impl_.conversion_summary_dir_.Get();
}
inline void TocoFlags::_internal_set_conversion_summary_dir(const std::string& value) {
  _impl_._has_bits_[0] |= 0x00000002u;
  _impl_.conversion_summary_dir_.Set(value, GetArenaForAllocation());
}
inline std::string* TocoFlags::_internal_mutable_conversion_summary_dir() {
  _impl_._has_bits_[0] |= 0x00000002u;
  return _impl_.conversion_summary_dir_.Mutable(GetArenaForAllocation());
}
inline std::string* TocoFlags::release_conversion_summary_dir() {
  // @@protoc_insertion_point(field_release:toco.TocoFlags.conversion_summary_dir)
  if (!_internal_has_conversion_summary_dir()) {
    return nullptr;
  }
  _impl_._has_bits_[0] &= ~0x00000002u;
  auto* p = _impl_.conversion_summary_dir_.Release();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.conversion_summary_dir_.IsDefault()) {
    _impl_.conversion_summary_dir_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void TocoFlags::set_allocated_conversion_summary_dir(std::string* conversion_summary_dir) {
  if (conversion_summary_dir != nullptr) {
    _impl_._has_bits_[0] |= 0x00000002u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000002u;
  }
  _impl_.conversion_summary_dir_.SetAllocated(conversion_summary_dir, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.conversion_summary_dir_.IsDefault()) {
    _impl_.conversion_summary_dir_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:toco.TocoFlags.conversion_summary_dir)
}

// repeated string custom_opdefs = 32 [deprecated = true];
inline int TocoFlags::_internal_custom_opdefs_size() const {
  return _impl_.custom_opdefs_.size();
}
inline int TocoFlags::custom_opdefs_size() const {
  return _internal_custom_opdefs_size();
}
inline void TocoFlags::clear_custom_opdefs() {
  _impl_.custom_opdefs_.Clear();
}
inline std::string* TocoFlags::add_custom_opdefs() {
  std::string* _s = _internal_add_custom_opdefs();
  // @@protoc_insertion_point(field_add_mutable:toco.TocoFlags.custom_opdefs)
  return _s;
}
inline const std::string& TocoFlags::_internal_custom_opdefs(int index) const {
  return _impl_.custom_opdefs_.Get(index);
}
inline const std::string& TocoFlags::custom_opdefs(int index) const {
  // @@protoc_insertion_point(field_get:toco.TocoFlags.custom_opdefs)
  return _internal_custom_opdefs(index);
}
inline std::string* TocoFlags::mutable_custom_opdefs(int index) {
  // @@protoc_insertion_point(field_mutable:toco.TocoFlags.custom_opdefs)
  return _impl_.custom_opdefs_.Mutable(index);
}
inline void TocoFlags::set_custom_opdefs(int index, const std::string& value) {
  _impl_.custom_opdefs_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:toco.TocoFlags.custom_opdefs)
}
inline void TocoFlags::set_custom_opdefs(int index, std::string&& value) {
  _impl_.custom_opdefs_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:toco.TocoFlags.custom_opdefs)
}
inline void TocoFlags::set_custom_opdefs(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.custom_opdefs_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:toco.TocoFlags.custom_opdefs)
}
inline void TocoFlags::set_custom_opdefs(int index, const char* value, size_t size) {
  _impl_.custom_opdefs_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:toco.TocoFlags.custom_opdefs)
}
inline std::string* TocoFlags::_internal_add_custom_opdefs() {
  return _impl_.custom_opdefs_.Add();
}
inline void TocoFlags::add_custom_opdefs(const std::string& value) {
  _impl_.custom_opdefs_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:toco.TocoFlags.custom_opdefs)
}
inline void TocoFlags::add_custom_opdefs(std::string&& value) {
  _impl_.custom_opdefs_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:toco.TocoFlags.custom_opdefs)
}
inline void TocoFlags::add_custom_opdefs(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.custom_opdefs_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:toco.TocoFlags.custom_opdefs)
}
inline void TocoFlags::add_custom_opdefs(const char* value, size_t size) {
  _impl_.custom_opdefs_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:toco.TocoFlags.custom_opdefs)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
TocoFlags::custom_opdefs() const {
  // @@protoc_insertion_point(field_list:toco.TocoFlags.custom_opdefs)
  return _impl_.custom_opdefs_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
TocoFlags::mutable_custom_opdefs() {
  // @@protoc_insertion_point(field_mutable_list:toco.TocoFlags.custom_opdefs)
  return &_impl_.custom_opdefs_;
}

// repeated string select_user_tf_ops = 33;
inline int TocoFlags::_internal_select_user_tf_ops_size() const {
  return _impl_.select_user_tf_ops_.size();
}
inline int TocoFlags::select_user_tf_ops_size() const {
  return _internal_select_user_tf_ops_size();
}
inline void TocoFlags::clear_select_user_tf_ops() {
  _impl_.select_user_tf_ops_.Clear();
}
inline std::string* TocoFlags::add_select_user_tf_ops() {
  std::string* _s = _internal_add_select_user_tf_ops();
  // @@protoc_insertion_point(field_add_mutable:toco.TocoFlags.select_user_tf_ops)
  return _s;
}
inline const std::string& TocoFlags::_internal_select_user_tf_ops(int index) const {
  return _impl_.select_user_tf_ops_.Get(index);
}
inline const std::string& TocoFlags::select_user_tf_ops(int index) const {
  // @@protoc_insertion_point(field_get:toco.TocoFlags.select_user_tf_ops)
  return _internal_select_user_tf_ops(index);
}
inline std::string* TocoFlags::mutable_select_user_tf_ops(int index) {
  // @@protoc_insertion_point(field_mutable:toco.TocoFlags.select_user_tf_ops)
  return _impl_.select_user_tf_ops_.Mutable(index);
}
inline void TocoFlags::set_select_user_tf_ops(int index, const std::string& value) {
  _impl_.select_user_tf_ops_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:toco.TocoFlags.select_user_tf_ops)
}
inline void TocoFlags::set_select_user_tf_ops(int index, std::string&& value) {
  _impl_.select_user_tf_ops_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:toco.TocoFlags.select_user_tf_ops)
}
inline void TocoFlags::set_select_user_tf_ops(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.select_user_tf_ops_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:toco.TocoFlags.select_user_tf_ops)
}
inline void TocoFlags::set_select_user_tf_ops(int index, const char* value, size_t size) {
  _impl_.select_user_tf_ops_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:toco.TocoFlags.select_user_tf_ops)
}
inline std::string* TocoFlags::_internal_add_select_user_tf_ops() {
  return _impl_.select_user_tf_ops_.Add();
}
inline void TocoFlags::add_select_user_tf_ops(const std::string& value) {
  _impl_.select_user_tf_ops_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:toco.TocoFlags.select_user_tf_ops)
}
inline void TocoFlags::add_select_user_tf_ops(std::string&& value) {
  _impl_.select_user_tf_ops_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:toco.TocoFlags.select_user_tf_ops)
}
inline void TocoFlags::add_select_user_tf_ops(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.select_user_tf_ops_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:toco.TocoFlags.select_user_tf_ops)
}
inline void TocoFlags::add_select_user_tf_ops(const char* value, size_t size) {
  _impl_.select_user_tf_ops_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:toco.TocoFlags.select_user_tf_ops)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
TocoFlags::select_user_tf_ops() const {
  // @@protoc_insertion_point(field_list:toco.TocoFlags.select_user_tf_ops)
  return _impl_.select_user_tf_ops_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
TocoFlags::mutable_select_user_tf_ops() {
  // @@protoc_insertion_point(field_mutable_list:toco.TocoFlags.select_user_tf_ops)
  return &_impl_.select_user_tf_ops_;
}

// optional bool enable_tflite_resource_variables = 34 [default = true];
inline bool TocoFlags::_internal_has_enable_tflite_resource_variables() const {
  bool value = (_impl_._has_bits_[1] & 0x00400000u) != 0;
  return value;
}
inline bool TocoFlags::has_enable_tflite_resource_variables() const {
  return _internal_has_enable_tflite_resource_variables();
}
inline void TocoFlags::clear_enable_tflite_resource_variables() {
  _impl_.enable_tflite_resource_variables_ = true;
  _impl_._has_bits_[1] &= ~0x00400000u;
}
inline bool TocoFlags::_internal_enable_tflite_resource_variables() const {
  return _impl_.enable_tflite_resource_variables_;
}
inline bool TocoFlags::enable_tflite_resource_variables() const {
  // @@protoc_insertion_point(field_get:toco.TocoFlags.enable_tflite_resource_variables)
  return _internal_enable_tflite_resource_variables();
}
inline void TocoFlags::_internal_set_enable_tflite_resource_variables(bool value) {
  _impl_._has_bits_[1] |= 0x00400000u;
  _impl_.enable_tflite_resource_variables_ = value;
}
inline void TocoFlags::set_enable_tflite_resource_variables(bool value) {
  _internal_set_enable_tflite_resource_variables(value);
  // @@protoc_insertion_point(field_set:toco.TocoFlags.enable_tflite_resource_variables)
}

// optional bool unfold_batchmatmul = 35 [default = false];
inline bool TocoFlags::_internal_has_unfold_batchmatmul() const {
  bool value = (_impl_._has_bits_[0] & 0x10000000u) != 0;
  return value;
}
inline bool TocoFlags::has_unfold_batchmatmul() const {
  return _internal_has_unfold_batchmatmul();
}
inline void TocoFlags::clear_unfold_batchmatmul() {
  _impl_.unfold_batchmatmul_ = false;
  _impl_._has_bits_[0] &= ~0x10000000u;
}
inline bool TocoFlags::_internal_unfold_batchmatmul() const {
  return _impl_.unfold_batchmatmul_;
}
inline bool TocoFlags::unfold_batchmatmul() const {
  // @@protoc_insertion_point(field_get:toco.TocoFlags.unfold_batchmatmul)
  return _internal_unfold_batchmatmul();
}
inline void TocoFlags::_internal_set_unfold_batchmatmul(bool value) {
  _impl_._has_bits_[0] |= 0x10000000u;
  _impl_.unfold_batchmatmul_ = value;
}
inline void TocoFlags::set_unfold_batchmatmul(bool value) {
  _internal_set_unfold_batchmatmul(value);
  // @@protoc_insertion_point(field_set:toco.TocoFlags.unfold_batchmatmul)
}

// optional bool lower_tensor_list_ops = 36 [default = true];
inline bool TocoFlags::_internal_has_lower_tensor_list_ops() const {
  bool value = (_impl_._has_bits_[1] & 0x00800000u) != 0;
  return value;
}
inline bool TocoFlags::has_lower_tensor_list_ops() const {
  return _internal_has_lower_tensor_list_ops();
}
inline void TocoFlags::clear_lower_tensor_list_ops() {
  _impl_.lower_tensor_list_ops_ = true;
  _impl_._has_bits_[1] &= ~0x00800000u;
}
inline bool TocoFlags::_internal_lower_tensor_list_ops() const {
  return _impl_.lower_tensor_list_ops_;
}
inline bool TocoFlags::lower_tensor_list_ops() const {
  // @@protoc_insertion_point(field_get:toco.TocoFlags.lower_tensor_list_ops)
  return _internal_lower_tensor_list_ops();
}
inline void TocoFlags::_internal_set_lower_tensor_list_ops(bool value) {
  _impl_._has_bits_[1] |= 0x00800000u;
  _impl_.lower_tensor_list_ops_ = value;
}
inline void TocoFlags::set_lower_tensor_list_ops(bool value) {
  _internal_set_lower_tensor_list_ops(value);
  // @@protoc_insertion_point(field_set:toco.TocoFlags.lower_tensor_list_ops)
}

// optional .toco.IODataType accumulation_type = 37;
inline bool TocoFlags::_internal_has_accumulation_type() const {
  bool value = (_impl_._has_bits_[0] & 0x80000000u) != 0;
  return value;
}
inline bool TocoFlags::has_accumulation_type() const {
  return _internal_has_accumulation_type();
}
inline void TocoFlags::clear_accumulation_type() {
  _impl_.accumulation_type_ = 0;
  _impl_._has_bits_[0] &= ~0x80000000u;
}
inline ::toco::IODataType TocoFlags::_internal_accumulation_type() const {
  return static_cast< ::toco::IODataType >(_impl_.accumulation_type_);
}
inline ::toco::IODataType TocoFlags::accumulation_type() const {
  // @@protoc_insertion_point(field_get:toco.TocoFlags.accumulation_type)
  return _internal_accumulation_type();
}
inline void TocoFlags::_internal_set_accumulation_type(::toco::IODataType value) {
  assert(::toco::IODataType_IsValid(value));
  _impl_._has_bits_[0] |= 0x80000000u;
  _impl_.accumulation_type_ = value;
}
inline void TocoFlags::set_accumulation_type(::toco::IODataType value) {
  _internal_set_accumulation_type(value);
  // @@protoc_insertion_point(field_set:toco.TocoFlags.accumulation_type)
}

// optional bool allow_bfloat16 = 38 [default = false];
inline bool TocoFlags::_internal_has_allow_bfloat16() const {
  bool value = (_impl_._has_bits_[0] & 0x20000000u) != 0;
  return value;
}
inline bool TocoFlags::has_allow_bfloat16() const {
  return _internal_has_allow_bfloat16();
}
inline void TocoFlags::clear_allow_bfloat16() {
  _impl_.allow_bfloat16_ = false;
  _impl_._has_bits_[0] &= ~0x20000000u;
}
inline bool TocoFlags::_internal_allow_bfloat16() const {
  return _impl_.allow_bfloat16_;
}
inline bool TocoFlags::allow_bfloat16() const {
  // @@protoc_insertion_point(field_get:toco.TocoFlags.allow_bfloat16)
  return _internal_allow_bfloat16();
}
inline void TocoFlags::_internal_set_allow_bfloat16(bool value) {
  _impl_._has_bits_[0] |= 0x20000000u;
  _impl_.allow_bfloat16_ = value;
}
inline void TocoFlags::set_allow_bfloat16(bool value) {
  _internal_set_allow_bfloat16(value);
  // @@protoc_insertion_point(field_set:toco.TocoFlags.allow_bfloat16)
}

// optional bool allow_all_select_tf_ops = 39;
inline bool TocoFlags::_internal_has_allow_all_select_tf_ops() const {
  bool value = (_impl_._has_bits_[0] & 0x40000000u) != 0;
  return value;
}
inline bool TocoFlags::has_allow_all_select_tf_ops() const {
  return _internal_has_allow_all_select_tf_ops();
}
inline void TocoFlags::clear_allow_all_select_tf_ops() {
  _impl_.allow_all_select_tf_ops_ = false;
  _impl_._has_bits_[0] &= ~0x40000000u;
}
inline bool TocoFlags::_internal_allow_all_select_tf_ops() const {
  return _impl_.allow_all_select_tf_ops_;
}
inline bool TocoFlags::allow_all_select_tf_ops() const {
  // @@protoc_insertion_point(field_get:toco.TocoFlags.allow_all_select_tf_ops)
  return _internal_allow_all_select_tf_ops();
}
inline void TocoFlags::_internal_set_allow_all_select_tf_ops(bool value) {
  _impl_._has_bits_[0] |= 0x40000000u;
  _impl_.allow_all_select_tf_ops_ = value;
}
inline void TocoFlags::set_allow_all_select_tf_ops(bool value) {
  _internal_set_allow_all_select_tf_ops(value);
  // @@protoc_insertion_point(field_set:toco.TocoFlags.allow_all_select_tf_ops)
}

// optional bool unfold_large_splat_constant = 40 [default = false];
inline bool TocoFlags::_internal_has_unfold_large_splat_constant() const {
  bool value = (_impl_._has_bits_[1] & 0x00000001u) != 0;
  return value;
}
inline bool TocoFlags::has_unfold_large_splat_constant() const {
  return _internal_has_unfold_large_splat_constant();
}
inline void TocoFlags::clear_unfold_large_splat_constant() {
  _impl_.unfold_large_splat_constant_ = false;
  _impl_._has_bits_[1] &= ~0x00000001u;
}
inline bool TocoFlags::_internal_unfold_large_splat_constant() const {
  return _impl_.unfold_large_splat_constant_;
}
inline bool TocoFlags::unfold_large_splat_constant() const {
  // @@protoc_insertion_point(field_get:toco.TocoFlags.unfold_large_splat_constant)
  return _internal_unfold_large_splat_constant();
}
inline void TocoFlags::_internal_set_unfold_large_splat_constant(bool value) {
  _impl_._has_bits_[1] |= 0x00000001u;
  _impl_.unfold_large_splat_constant_ = value;
}
inline void TocoFlags::set_unfold_large_splat_constant(bool value) {
  _internal_set_unfold_large_splat_constant(value);
  // @@protoc_insertion_point(field_set:toco.TocoFlags.unfold_large_splat_constant)
}

// repeated string supported_backends = 41;
inline int TocoFlags::_internal_supported_backends_size() const {
  return _impl_.supported_backends_.size();
}
inline int TocoFlags::supported_backends_size() const {
  return _internal_supported_backends_size();
}
inline void TocoFlags::clear_supported_backends() {
  _impl_.supported_backends_.Clear();
}
inline std::string* TocoFlags::add_supported_backends() {
  std::string* _s = _internal_add_supported_backends();
  // @@protoc_insertion_point(field_add_mutable:toco.TocoFlags.supported_backends)
  return _s;
}
inline const std::string& TocoFlags::_internal_supported_backends(int index) const {
  return _impl_.supported_backends_.Get(index);
}
inline const std::string& TocoFlags::supported_backends(int index) const {
  // @@protoc_insertion_point(field_get:toco.TocoFlags.supported_backends)
  return _internal_supported_backends(index);
}
inline std::string* TocoFlags::mutable_supported_backends(int index) {
  // @@protoc_insertion_point(field_mutable:toco.TocoFlags.supported_backends)
  return _impl_.supported_backends_.Mutable(index);
}
inline void TocoFlags::set_supported_backends(int index, const std::string& value) {
  _impl_.supported_backends_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:toco.TocoFlags.supported_backends)
}
inline void TocoFlags::set_supported_backends(int index, std::string&& value) {
  _impl_.supported_backends_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:toco.TocoFlags.supported_backends)
}
inline void TocoFlags::set_supported_backends(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.supported_backends_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:toco.TocoFlags.supported_backends)
}
inline void TocoFlags::set_supported_backends(int index, const char* value, size_t size) {
  _impl_.supported_backends_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:toco.TocoFlags.supported_backends)
}
inline std::string* TocoFlags::_internal_add_supported_backends() {
  return _impl_.supported_backends_.Add();
}
inline void TocoFlags::add_supported_backends(const std::string& value) {
  _impl_.supported_backends_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:toco.TocoFlags.supported_backends)
}
inline void TocoFlags::add_supported_backends(std::string&& value) {
  _impl_.supported_backends_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:toco.TocoFlags.supported_backends)
}
inline void TocoFlags::add_supported_backends(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.supported_backends_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:toco.TocoFlags.supported_backends)
}
inline void TocoFlags::add_supported_backends(const char* value, size_t size) {
  _impl_.supported_backends_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:toco.TocoFlags.supported_backends)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
TocoFlags::supported_backends() const {
  // @@protoc_insertion_point(field_list:toco.TocoFlags.supported_backends)
  return _impl_.supported_backends_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
TocoFlags::mutable_supported_backends() {
  // @@protoc_insertion_point(field_mutable_list:toco.TocoFlags.supported_backends)
  return &_impl_.supported_backends_;
}

// optional bool default_to_single_batch_in_tensor_list_ops = 42 [default = false];
inline bool TocoFlags::_internal_has_default_to_single_batch_in_tensor_list_ops() const {
  bool value = (_impl_._has_bits_[1] & 0x00000002u) != 0;
  return value;
}
inline bool TocoFlags::has_default_to_single_batch_in_tensor_list_ops() const {
  return _internal_has_default_to_single_batch_in_tensor_list_ops();
}
inline void TocoFlags::clear_default_to_single_batch_in_tensor_list_ops() {
  _impl_.default_to_single_batch_in_tensor_list_ops_ = false;
  _impl_._has_bits_[1] &= ~0x00000002u;
}
inline bool TocoFlags::_internal_default_to_single_batch_in_tensor_list_ops() const {
  return _impl_.default_to_single_batch_in_tensor_list_ops_;
}
inline bool TocoFlags::default_to_single_batch_in_tensor_list_ops() const {
  // @@protoc_insertion_point(field_get:toco.TocoFlags.default_to_single_batch_in_tensor_list_ops)
  return _internal_default_to_single_batch_in_tensor_list_ops();
}
inline void TocoFlags::_internal_set_default_to_single_batch_in_tensor_list_ops(bool value) {
  _impl_._has_bits_[1] |= 0x00000002u;
  _impl_.default_to_single_batch_in_tensor_list_ops_ = value;
}
inline void TocoFlags::set_default_to_single_batch_in_tensor_list_ops(bool value) {
  _internal_set_default_to_single_batch_in_tensor_list_ops(value);
  // @@protoc_insertion_point(field_set:toco.TocoFlags.default_to_single_batch_in_tensor_list_ops)
}

// optional bool disable_per_channel_quantization = 43 [default = false];
inline bool TocoFlags::_internal_has_disable_per_channel_quantization() const {
  bool value = (_impl_._has_bits_[1] & 0x00000004u) != 0;
  return value;
}
inline bool TocoFlags::has_disable_per_channel_quantization() const {
  return _internal_has_disable_per_channel_quantization();
}
inline void TocoFlags::clear_disable_per_channel_quantization() {
  _impl_.disable_per_channel_quantization_ = false;
  _impl_._has_bits_[1] &= ~0x00000004u;
}
inline bool TocoFlags::_internal_disable_per_channel_quantization() const {
  return _impl_.disable_per_channel_quantization_;
}
inline bool TocoFlags::disable_per_channel_quantization() const {
  // @@protoc_insertion_point(field_get:toco.TocoFlags.disable_per_channel_quantization)
  return _internal_disable_per_channel_quantization();
}
inline void TocoFlags::_internal_set_disable_per_channel_quantization(bool value) {
  _impl_._has_bits_[1] |= 0x00000004u;
  _impl_.disable_per_channel_quantization_ = value;
}
inline void TocoFlags::set_disable_per_channel_quantization(bool value) {
  _internal_set_disable_per_channel_quantization(value);
  // @@protoc_insertion_point(field_set:toco.TocoFlags.disable_per_channel_quantization)
}

// optional bool enable_mlir_dynamic_range_quantizer = 44 [default = false];
inline bool TocoFlags::_internal_has_enable_mlir_dynamic_range_quantizer() const {
  bool value = (_impl_._has_bits_[1] & 0x00000008u) != 0;
  return value;
}
inline bool TocoFlags::has_enable_mlir_dynamic_range_quantizer() const {
  return _internal_has_enable_mlir_dynamic_range_quantizer();
}
inline void TocoFlags::clear_enable_mlir_dynamic_range_quantizer() {
  _impl_.enable_mlir_dynamic_range_quantizer_ = false;
  _impl_._has_bits_[1] &= ~0x00000008u;
}
inline bool TocoFlags::_internal_enable_mlir_dynamic_range_quantizer() const {
  return _impl_.enable_mlir_dynamic_range_quantizer_;
}
inline bool TocoFlags::enable_mlir_dynamic_range_quantizer() const {
  // @@protoc_insertion_point(field_get:toco.TocoFlags.enable_mlir_dynamic_range_quantizer)
  return _internal_enable_mlir_dynamic_range_quantizer();
}
inline void TocoFlags::_internal_set_enable_mlir_dynamic_range_quantizer(bool value) {
  _impl_._has_bits_[1] |= 0x00000008u;
  _impl_.enable_mlir_dynamic_range_quantizer_ = value;
}
inline void TocoFlags::set_enable_mlir_dynamic_range_quantizer(bool value) {
  _internal_set_enable_mlir_dynamic_range_quantizer(value);
  // @@protoc_insertion_point(field_set:toco.TocoFlags.enable_mlir_dynamic_range_quantizer)
}

// optional string tf_quantization_mode = 45;
inline bool TocoFlags::_internal_has_tf_quantization_mode() const {
  bool value = (_impl_._has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool TocoFlags::has_tf_quantization_mode() const {
  return _internal_has_tf_quantization_mode();
}
inline void TocoFlags::clear_tf_quantization_mode() {
  _impl_.tf_quantization_mode_.ClearToEmpty();
  _impl_._has_bits_[0] &= ~0x00000004u;
}
inline const std::string& TocoFlags::tf_quantization_mode() const {
  // @@protoc_insertion_point(field_get:toco.TocoFlags.tf_quantization_mode)
  return _internal_tf_quantization_mode();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void TocoFlags::set_tf_quantization_mode(ArgT0&& arg0, ArgT... args) {
 _impl_._has_bits_[0] |= 0x00000004u;
 _impl_.tf_quantization_mode_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:toco.TocoFlags.tf_quantization_mode)
}
inline std::string* TocoFlags::mutable_tf_quantization_mode() {
  std::string* _s = _internal_mutable_tf_quantization_mode();
  // @@protoc_insertion_point(field_mutable:toco.TocoFlags.tf_quantization_mode)
  return _s;
}
inline const std::string& TocoFlags::_internal_tf_quantization_mode() const {
  return _impl_.tf_quantization_mode_.Get();
}
inline void TocoFlags::_internal_set_tf_quantization_mode(const std::string& value) {
  _impl_._has_bits_[0] |= 0x00000004u;
  _impl_.tf_quantization_mode_.Set(value, GetArenaForAllocation());
}
inline std::string* TocoFlags::_internal_mutable_tf_quantization_mode() {
  _impl_._has_bits_[0] |= 0x00000004u;
  return _impl_.tf_quantization_mode_.Mutable(GetArenaForAllocation());
}
inline std::string* TocoFlags::release_tf_quantization_mode() {
  // @@protoc_insertion_point(field_release:toco.TocoFlags.tf_quantization_mode)
  if (!_internal_has_tf_quantization_mode()) {
    return nullptr;
  }
  _impl_._has_bits_[0] &= ~0x00000004u;
  auto* p = _impl_.tf_quantization_mode_.Release();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.tf_quantization_mode_.IsDefault()) {
    _impl_.tf_quantization_mode_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void TocoFlags::set_allocated_tf_quantization_mode(std::string* tf_quantization_mode) {
  if (tf_quantization_mode != nullptr) {
    _impl_._has_bits_[0] |= 0x00000004u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000004u;
  }
  _impl_.tf_quantization_mode_.SetAllocated(tf_quantization_mode, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.tf_quantization_mode_.IsDefault()) {
    _impl_.tf_quantization_mode_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:toco.TocoFlags.tf_quantization_mode)
}

// optional bool disable_infer_tensor_range = 46 [default = false];
inline bool TocoFlags::_internal_has_disable_infer_tensor_range() const {
  bool value = (_impl_._has_bits_[1] & 0x00000010u) != 0;
  return value;
}
inline bool TocoFlags::has_disable_infer_tensor_range() const {
  return _internal_has_disable_infer_tensor_range();
}
inline void TocoFlags::clear_disable_infer_tensor_range() {
  _impl_.disable_infer_tensor_range_ = false;
  _impl_._has_bits_[1] &= ~0x00000010u;
}
inline bool TocoFlags::_internal_disable_infer_tensor_range() const {
  return _impl_.disable_infer_tensor_range_;
}
inline bool TocoFlags::disable_infer_tensor_range() const {
  // @@protoc_insertion_point(field_get:toco.TocoFlags.disable_infer_tensor_range)
  return _internal_disable_infer_tensor_range();
}
inline void TocoFlags::_internal_set_disable_infer_tensor_range(bool value) {
  _impl_._has_bits_[1] |= 0x00000010u;
  _impl_.disable_infer_tensor_range_ = value;
}
inline void TocoFlags::set_disable_infer_tensor_range(bool value) {
  _internal_set_disable_infer_tensor_range(value);
  // @@protoc_insertion_point(field_set:toco.TocoFlags.disable_infer_tensor_range)
}

// optional bool use_fake_quant_num_bits = 47 [default = false];
inline bool TocoFlags::_internal_has_use_fake_quant_num_bits() const {
  bool value = (_impl_._has_bits_[1] & 0x00000020u) != 0;
  return value;
}
inline bool TocoFlags::has_use_fake_quant_num_bits() const {
  return _internal_has_use_fake_quant_num_bits();
}
inline void TocoFlags::clear_use_fake_quant_num_bits() {
  _impl_.use_fake_quant_num_bits_ = false;
  _impl_._has_bits_[1] &= ~0x00000020u;
}
inline bool TocoFlags::_internal_use_fake_quant_num_bits() const {
  return _impl_.use_fake_quant_num_bits_;
}
inline bool TocoFlags::use_fake_quant_num_bits() const {
  // @@protoc_insertion_point(field_get:toco.TocoFlags.use_fake_quant_num_bits)
  return _internal_use_fake_quant_num_bits();
}
inline void TocoFlags::_internal_set_use_fake_quant_num_bits(bool value) {
  _impl_._has_bits_[1] |= 0x00000020u;
  _impl_.use_fake_quant_num_bits_ = value;
}
inline void TocoFlags::set_use_fake_quant_num_bits(bool value) {
  _internal_set_use_fake_quant_num_bits(value);
  // @@protoc_insertion_point(field_set:toco.TocoFlags.use_fake_quant_num_bits)
}

// optional bool enable_dynamic_update_slice = 48 [default = false];
inline bool TocoFlags::_internal_has_enable_dynamic_update_slice() const {
  bool value = (_impl_._has_bits_[1] & 0x00000040u) != 0;
  return value;
}
inline bool TocoFlags::has_enable_dynamic_update_slice() const {
  return _internal_has_enable_dynamic_update_slice();
}
inline void TocoFlags::clear_enable_dynamic_update_slice() {
  _impl_.enable_dynamic_update_slice_ = false;
  _impl_._has_bits_[1] &= ~0x00000040u;
}
inline bool TocoFlags::_internal_enable_dynamic_update_slice() const {
  return _impl_.enable_dynamic_update_slice_;
}
inline bool TocoFlags::enable_dynamic_update_slice() const {
  // @@protoc_insertion_point(field_get:toco.TocoFlags.enable_dynamic_update_slice)
  return _internal_enable_dynamic_update_slice();
}
inline void TocoFlags::_internal_set_enable_dynamic_update_slice(bool value) {
  _impl_._has_bits_[1] |= 0x00000040u;
  _impl_.enable_dynamic_update_slice_ = value;
}
inline void TocoFlags::set_enable_dynamic_update_slice(bool value) {
  _internal_set_enable_dynamic_update_slice(value);
  // @@protoc_insertion_point(field_set:toco.TocoFlags.enable_dynamic_update_slice)
}

// optional bool preserve_assert_op = 49 [default = false];
inline bool TocoFlags::_internal_has_preserve_assert_op() const {
  bool value = (_impl_._has_bits_[1] & 0x00000080u) != 0;
  return value;
}
inline bool TocoFlags::has_preserve_assert_op() const {
  return _internal_has_preserve_assert_op();
}
inline void TocoFlags::clear_preserve_assert_op() {
  _impl_.preserve_assert_op_ = false;
  _impl_._has_bits_[1] &= ~0x00000080u;
}
inline bool TocoFlags::_internal_preserve_assert_op() const {
  return _impl_.preserve_assert_op_;
}
inline bool TocoFlags::preserve_assert_op() const {
  // @@protoc_insertion_point(field_get:toco.TocoFlags.preserve_assert_op)
  return _internal_preserve_assert_op();
}
inline void TocoFlags::_internal_set_preserve_assert_op(bool value) {
  _impl_._has_bits_[1] |= 0x00000080u;
  _impl_.preserve_assert_op_ = value;
}
inline void TocoFlags::set_preserve_assert_op(bool value) {
  _internal_set_preserve_assert_op(value);
  // @@protoc_insertion_point(field_set:toco.TocoFlags.preserve_assert_op)
}

// optional bool guarantee_all_funcs_one_use = 50 [default = false];
inline bool TocoFlags::_internal_has_guarantee_all_funcs_one_use() const {
  bool value = (_impl_._has_bits_[1] & 0x00000100u) != 0;
  return value;
}
inline bool TocoFlags::has_guarantee_all_funcs_one_use() const {
  return _internal_has_guarantee_all_funcs_one_use();
}
inline void TocoFlags::clear_guarantee_all_funcs_one_use() {
  _impl_.guarantee_all_funcs_one_use_ = false;
  _impl_._has_bits_[1] &= ~0x00000100u;
}
inline bool TocoFlags::_internal_guarantee_all_funcs_one_use() const {
  return _impl_.guarantee_all_funcs_one_use_;
}
inline bool TocoFlags::guarantee_all_funcs_one_use() const {
  // @@protoc_insertion_point(field_get:toco.TocoFlags.guarantee_all_funcs_one_use)
  return _internal_guarantee_all_funcs_one_use();
}
inline void TocoFlags::_internal_set_guarantee_all_funcs_one_use(bool value) {
  _impl_._has_bits_[1] |= 0x00000100u;
  _impl_.guarantee_all_funcs_one_use_ = value;
}
inline void TocoFlags::set_guarantee_all_funcs_one_use(bool value) {
  _internal_set_guarantee_all_funcs_one_use(value);
  // @@protoc_insertion_point(field_set:toco.TocoFlags.guarantee_all_funcs_one_use)
}

// optional bool convert_to_stablehlo = 51 [default = false];
inline bool TocoFlags::_internal_has_convert_to_stablehlo() const {
  bool value = (_impl_._has_bits_[1] & 0x00000200u) != 0;
  return value;
}
inline bool TocoFlags::has_convert_to_stablehlo() const {
  return _internal_has_convert_to_stablehlo();
}
inline void TocoFlags::clear_convert_to_stablehlo() {
  _impl_.convert_to_stablehlo_ = false;
  _impl_._has_bits_[1] &= ~0x00000200u;
}
inline bool TocoFlags::_internal_convert_to_stablehlo() const {
  return _impl_.convert_to_stablehlo_;
}
inline bool TocoFlags::convert_to_stablehlo() const {
  // @@protoc_insertion_point(field_get:toco.TocoFlags.convert_to_stablehlo)
  return _internal_convert_to_stablehlo();
}
inline void TocoFlags::_internal_set_convert_to_stablehlo(bool value) {
  _impl_._has_bits_[1] |= 0x00000200u;
  _impl_.convert_to_stablehlo_ = value;
}
inline void TocoFlags::set_convert_to_stablehlo(bool value) {
  _internal_set_convert_to_stablehlo(value);
  // @@protoc_insertion_point(field_set:toco.TocoFlags.convert_to_stablehlo)
}

// optional bool enable_mlir_variable_quantization = 52 [default = false];
inline bool TocoFlags::_internal_has_enable_mlir_variable_quantization() const {
  bool value = (_impl_._has_bits_[1] & 0x00000400u) != 0;
  return value;
}
inline bool TocoFlags::has_enable_mlir_variable_quantization() const {
  return _internal_has_enable_mlir_variable_quantization();
}
inline void TocoFlags::clear_enable_mlir_variable_quantization() {
  _impl_.enable_mlir_variable_quantization_ = false;
  _impl_._has_bits_[1] &= ~0x00000400u;
}
inline bool TocoFlags::_internal_enable_mlir_variable_quantization() const {
  return _impl_.enable_mlir_variable_quantization_;
}
inline bool TocoFlags::enable_mlir_variable_quantization() const {
  // @@protoc_insertion_point(field_get:toco.TocoFlags.enable_mlir_variable_quantization)
  return _internal_enable_mlir_variable_quantization();
}
inline void TocoFlags::_internal_set_enable_mlir_variable_quantization(bool value) {
  _impl_._has_bits_[1] |= 0x00000400u;
  _impl_.enable_mlir_variable_quantization_ = value;
}
inline void TocoFlags::set_enable_mlir_variable_quantization(bool value) {
  _internal_set_enable_mlir_variable_quantization(value);
  // @@protoc_insertion_point(field_set:toco.TocoFlags.enable_mlir_variable_quantization)
}

// optional bool disable_fuse_mul_and_fc = 53 [default = false];
inline bool TocoFlags::_internal_has_disable_fuse_mul_and_fc() const {
  bool value = (_impl_._has_bits_[1] & 0x00000800u) != 0;
  return value;
}
inline bool TocoFlags::has_disable_fuse_mul_and_fc() const {
  return _internal_has_disable_fuse_mul_and_fc();
}
inline void TocoFlags::clear_disable_fuse_mul_and_fc() {
  _impl_.disable_fuse_mul_and_fc_ = false;
  _impl_._has_bits_[1] &= ~0x00000800u;
}
inline bool TocoFlags::_internal_disable_fuse_mul_and_fc() const {
  return _impl_.disable_fuse_mul_and_fc_;
}
inline bool TocoFlags::disable_fuse_mul_and_fc() const {
  // @@protoc_insertion_point(field_get:toco.TocoFlags.disable_fuse_mul_and_fc)
  return _internal_disable_fuse_mul_and_fc();
}
inline void TocoFlags::_internal_set_disable_fuse_mul_and_fc(bool value) {
  _impl_._has_bits_[1] |= 0x00000800u;
  _impl_.disable_fuse_mul_and_fc_ = value;
}
inline void TocoFlags::set_disable_fuse_mul_and_fc(bool value) {
  _internal_set_disable_fuse_mul_and_fc(value);
  // @@protoc_insertion_point(field_set:toco.TocoFlags.disable_fuse_mul_and_fc)
}

// optional .stablehlo.quantization.QuantizationOptions quantization_options = 54 [deprecated = true];
inline bool TocoFlags::_internal_has_quantization_options() const {
  bool value = (_impl_._has_bits_[0] & 0x00000010u) != 0;
  PROTOBUF_ASSUME(!value || _impl_.quantization_options_ != nullptr);
  return value;
}
inline bool TocoFlags::has_quantization_options() const {
  return _internal_has_quantization_options();
}
inline const ::stablehlo::quantization::QuantizationOptions& TocoFlags::_internal_quantization_options() const {
  const ::stablehlo::quantization::QuantizationOptions* p = _impl_.quantization_options_;
  return p != nullptr ? *p : reinterpret_cast<const ::stablehlo::quantization::QuantizationOptions&>(
      ::stablehlo::quantization::_QuantizationOptions_default_instance_);
}
inline const ::stablehlo::quantization::QuantizationOptions& TocoFlags::quantization_options() const {
  // @@protoc_insertion_point(field_get:toco.TocoFlags.quantization_options)
  return _internal_quantization_options();
}
inline void TocoFlags::unsafe_arena_set_allocated_quantization_options(
    ::stablehlo::quantization::QuantizationOptions* quantization_options) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.quantization_options_);
  }
  _impl_.quantization_options_ = quantization_options;
  if (quantization_options) {
    _impl_._has_bits_[0] |= 0x00000010u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000010u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:toco.TocoFlags.quantization_options)
}
inline ::stablehlo::quantization::QuantizationOptions* TocoFlags::release_quantization_options() {
  _impl_._has_bits_[0] &= ~0x00000010u;
  ::stablehlo::quantization::QuantizationOptions* temp = _impl_.quantization_options_;
  _impl_.quantization_options_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::stablehlo::quantization::QuantizationOptions* TocoFlags::unsafe_arena_release_quantization_options() {
  // @@protoc_insertion_point(field_release:toco.TocoFlags.quantization_options)
  _impl_._has_bits_[0] &= ~0x00000010u;
  ::stablehlo::quantization::QuantizationOptions* temp = _impl_.quantization_options_;
  _impl_.quantization_options_ = nullptr;
  return temp;
}
inline ::stablehlo::quantization::QuantizationOptions* TocoFlags::_internal_mutable_quantization_options() {
  _impl_._has_bits_[0] |= 0x00000010u;
  if (_impl_.quantization_options_ == nullptr) {
    auto* p = CreateMaybeMessage<::stablehlo::quantization::QuantizationOptions>(GetArenaForAllocation());
    _impl_.quantization_options_ = p;
  }
  return _impl_.quantization_options_;
}
inline ::stablehlo::quantization::QuantizationOptions* TocoFlags::mutable_quantization_options() {
  ::stablehlo::quantization::QuantizationOptions* _msg = _internal_mutable_quantization_options();
  // @@protoc_insertion_point(field_mutable:toco.TocoFlags.quantization_options)
  return _msg;
}
inline void TocoFlags::set_allocated_quantization_options(::stablehlo::quantization::QuantizationOptions* quantization_options) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.quantization_options_);
  }
  if (quantization_options) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(quantization_options));
    if (message_arena != submessage_arena) {
      quantization_options = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, quantization_options, submessage_arena);
    }
    _impl_._has_bits_[0] |= 0x00000010u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000010u;
  }
  _impl_.quantization_options_ = quantization_options;
  // @@protoc_insertion_point(field_set_allocated:toco.TocoFlags.quantization_options)
}

// optional bool enable_hlo_to_tf_conversion = 55 [default = false, deprecated = true];
inline bool TocoFlags::_internal_has_enable_hlo_to_tf_conversion() const {
  bool value = (_impl_._has_bits_[1] & 0x00001000u) != 0;
  return value;
}
inline bool TocoFlags::has_enable_hlo_to_tf_conversion() const {
  return _internal_has_enable_hlo_to_tf_conversion();
}
inline void TocoFlags::clear_enable_hlo_to_tf_conversion() {
  _impl_.enable_hlo_to_tf_conversion_ = false;
  _impl_._has_bits_[1] &= ~0x00001000u;
}
inline bool TocoFlags::_internal_enable_hlo_to_tf_conversion() const {
  return _impl_.enable_hlo_to_tf_conversion_;
}
inline bool TocoFlags::enable_hlo_to_tf_conversion() const {
  // @@protoc_insertion_point(field_get:toco.TocoFlags.enable_hlo_to_tf_conversion)
  return _internal_enable_hlo_to_tf_conversion();
}
inline void TocoFlags::_internal_set_enable_hlo_to_tf_conversion(bool value) {
  _impl_._has_bits_[1] |= 0x00001000u;
  _impl_.enable_hlo_to_tf_conversion_ = value;
}
inline void TocoFlags::set_enable_hlo_to_tf_conversion(bool value) {
  _internal_set_enable_hlo_to_tf_conversion(value);
  // @@protoc_insertion_point(field_set:toco.TocoFlags.enable_hlo_to_tf_conversion)
}

// optional .tensorflow.converter.DebugOptions debug_options = 56;
inline bool TocoFlags::_internal_has_debug_options() const {
  bool value = (_impl_._has_bits_[0] & 0x00000020u) != 0;
  PROTOBUF_ASSUME(!value || _impl_.debug_options_ != nullptr);
  return value;
}
inline bool TocoFlags::has_debug_options() const {
  return _internal_has_debug_options();
}
inline const ::tensorflow::converter::DebugOptions& TocoFlags::_internal_debug_options() const {
  const ::tensorflow::converter::DebugOptions* p = _impl_.debug_options_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::converter::DebugOptions&>(
      ::tensorflow::converter::_DebugOptions_default_instance_);
}
inline const ::tensorflow::converter::DebugOptions& TocoFlags::debug_options() const {
  // @@protoc_insertion_point(field_get:toco.TocoFlags.debug_options)
  return _internal_debug_options();
}
inline void TocoFlags::unsafe_arena_set_allocated_debug_options(
    ::tensorflow::converter::DebugOptions* debug_options) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.debug_options_);
  }
  _impl_.debug_options_ = debug_options;
  if (debug_options) {
    _impl_._has_bits_[0] |= 0x00000020u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000020u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:toco.TocoFlags.debug_options)
}
inline ::tensorflow::converter::DebugOptions* TocoFlags::release_debug_options() {
  _impl_._has_bits_[0] &= ~0x00000020u;
  ::tensorflow::converter::DebugOptions* temp = _impl_.debug_options_;
  _impl_.debug_options_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::converter::DebugOptions* TocoFlags::unsafe_arena_release_debug_options() {
  // @@protoc_insertion_point(field_release:toco.TocoFlags.debug_options)
  _impl_._has_bits_[0] &= ~0x00000020u;
  ::tensorflow::converter::DebugOptions* temp = _impl_.debug_options_;
  _impl_.debug_options_ = nullptr;
  return temp;
}
inline ::tensorflow::converter::DebugOptions* TocoFlags::_internal_mutable_debug_options() {
  _impl_._has_bits_[0] |= 0x00000020u;
  if (_impl_.debug_options_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::converter::DebugOptions>(GetArenaForAllocation());
    _impl_.debug_options_ = p;
  }
  return _impl_.debug_options_;
}
inline ::tensorflow::converter::DebugOptions* TocoFlags::mutable_debug_options() {
  ::tensorflow::converter::DebugOptions* _msg = _internal_mutable_debug_options();
  // @@protoc_insertion_point(field_mutable:toco.TocoFlags.debug_options)
  return _msg;
}
inline void TocoFlags::set_allocated_debug_options(::tensorflow::converter::DebugOptions* debug_options) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.debug_options_);
  }
  if (debug_options) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(debug_options));
    if (message_arena != submessage_arena) {
      debug_options = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, debug_options, submessage_arena);
    }
    _impl_._has_bits_[0] |= 0x00000020u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000020u;
  }
  _impl_.debug_options_ = debug_options;
  // @@protoc_insertion_point(field_set_allocated:toco.TocoFlags.debug_options)
}

// optional bool use_buffer_offset = 57 [default = false];
inline bool TocoFlags::_internal_has_use_buffer_offset() const {
  bool value = (_impl_._has_bits_[1] & 0x00002000u) != 0;
  return value;
}
inline bool TocoFlags::has_use_buffer_offset() const {
  return _internal_has_use_buffer_offset();
}
inline void TocoFlags::clear_use_buffer_offset() {
  _impl_.use_buffer_offset_ = false;
  _impl_._has_bits_[1] &= ~0x00002000u;
}
inline bool TocoFlags::_internal_use_buffer_offset() const {
  return _impl_.use_buffer_offset_;
}
inline bool TocoFlags::use_buffer_offset() const {
  // @@protoc_insertion_point(field_get:toco.TocoFlags.use_buffer_offset)
  return _internal_use_buffer_offset();
}
inline void TocoFlags::_internal_set_use_buffer_offset(bool value) {
  _impl_._has_bits_[1] |= 0x00002000u;
  _impl_.use_buffer_offset_ = value;
}
inline void TocoFlags::set_use_buffer_offset(bool value) {
  _internal_set_use_buffer_offset(value);
  // @@protoc_insertion_point(field_set:toco.TocoFlags.use_buffer_offset)
}

// optional bool legalize_custom_tensor_list_ops = 58 [default = false];
inline bool TocoFlags::_internal_has_legalize_custom_tensor_list_ops() const {
  bool value = (_impl_._has_bits_[1] & 0x00004000u) != 0;
  return value;
}
inline bool TocoFlags::has_legalize_custom_tensor_list_ops() const {
  return _internal_has_legalize_custom_tensor_list_ops();
}
inline void TocoFlags::clear_legalize_custom_tensor_list_ops() {
  _impl_.legalize_custom_tensor_list_ops_ = false;
  _impl_._has_bits_[1] &= ~0x00004000u;
}
inline bool TocoFlags::_internal_legalize_custom_tensor_list_ops() const {
  return _impl_.legalize_custom_tensor_list_ops_;
}
inline bool TocoFlags::legalize_custom_tensor_list_ops() const {
  // @@protoc_insertion_point(field_get:toco.TocoFlags.legalize_custom_tensor_list_ops)
  return _internal_legalize_custom_tensor_list_ops();
}
inline void TocoFlags::_internal_set_legalize_custom_tensor_list_ops(bool value) {
  _impl_._has_bits_[1] |= 0x00004000u;
  _impl_.legalize_custom_tensor_list_ops_ = value;
}
inline void TocoFlags::set_legalize_custom_tensor_list_ops(bool value) {
  _internal_set_legalize_custom_tensor_list_ops(value);
  // @@protoc_insertion_point(field_set:toco.TocoFlags.legalize_custom_tensor_list_ops)
}

// optional bool reduce_type_precision = 59 [default = false];
inline bool TocoFlags::_internal_has_reduce_type_precision() const {
  bool value = (_impl_._has_bits_[1] & 0x00008000u) != 0;
  return value;
}
inline bool TocoFlags::has_reduce_type_precision() const {
  return _internal_has_reduce_type_precision();
}
inline void TocoFlags::clear_reduce_type_precision() {
  _impl_.reduce_type_precision_ = false;
  _impl_._has_bits_[1] &= ~0x00008000u;
}
inline bool TocoFlags::_internal_reduce_type_precision() const {
  return _impl_.reduce_type_precision_;
}
inline bool TocoFlags::reduce_type_precision() const {
  // @@protoc_insertion_point(field_get:toco.TocoFlags.reduce_type_precision)
  return _internal_reduce_type_precision();
}
inline void TocoFlags::_internal_set_reduce_type_precision(bool value) {
  _impl_._has_bits_[1] |= 0x00008000u;
  _impl_.reduce_type_precision_ = value;
}
inline void TocoFlags::set_reduce_type_precision(bool value) {
  _internal_set_reduce_type_precision(value);
  // @@protoc_insertion_point(field_set:toco.TocoFlags.reduce_type_precision)
}

// optional string qdq_conversion_mode = 60 [default = "NONE"];
inline bool TocoFlags::_internal_has_qdq_conversion_mode() const {
  bool value = (_impl_._has_bits_[0] & 0x00000008u) != 0;
  return value;
}
inline bool TocoFlags::has_qdq_conversion_mode() const {
  return _internal_has_qdq_conversion_mode();
}
inline void TocoFlags::clear_qdq_conversion_mode() {
  _impl_.qdq_conversion_mode_.ClearToDefault(::toco::TocoFlags::Impl_::_i_give_permission_to_break_this_code_default_qdq_conversion_mode_, GetArenaForAllocation());
  _impl_._has_bits_[0] &= ~0x00000008u;
}
inline const std::string& TocoFlags::qdq_conversion_mode() const {
  // @@protoc_insertion_point(field_get:toco.TocoFlags.qdq_conversion_mode)
  if (_impl_.qdq_conversion_mode_.IsDefault()) return Impl_::_i_give_permission_to_break_this_code_default_qdq_conversion_mode_.get();
  return _internal_qdq_conversion_mode();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void TocoFlags::set_qdq_conversion_mode(ArgT0&& arg0, ArgT... args) {
 _impl_._has_bits_[0] |= 0x00000008u;
 _impl_.qdq_conversion_mode_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:toco.TocoFlags.qdq_conversion_mode)
}
inline std::string* TocoFlags::mutable_qdq_conversion_mode() {
  std::string* _s = _internal_mutable_qdq_conversion_mode();
  // @@protoc_insertion_point(field_mutable:toco.TocoFlags.qdq_conversion_mode)
  return _s;
}
inline const std::string& TocoFlags::_internal_qdq_conversion_mode() const {
  return _impl_.qdq_conversion_mode_.Get();
}
inline void TocoFlags::_internal_set_qdq_conversion_mode(const std::string& value) {
  _impl_._has_bits_[0] |= 0x00000008u;
  _impl_.qdq_conversion_mode_.Set(value, GetArenaForAllocation());
}
inline std::string* TocoFlags::_internal_mutable_qdq_conversion_mode() {
  _impl_._has_bits_[0] |= 0x00000008u;
  return _impl_.qdq_conversion_mode_.Mutable(::toco::TocoFlags::Impl_::_i_give_permission_to_break_this_code_default_qdq_conversion_mode_, GetArenaForAllocation());
}
inline std::string* TocoFlags::release_qdq_conversion_mode() {
  // @@protoc_insertion_point(field_release:toco.TocoFlags.qdq_conversion_mode)
  if (!_internal_has_qdq_conversion_mode()) {
    return nullptr;
  }
  _impl_._has_bits_[0] &= ~0x00000008u;
  auto* p = _impl_.qdq_conversion_mode_.Release();
  return p;
}
inline void TocoFlags::set_allocated_qdq_conversion_mode(std::string* qdq_conversion_mode) {
  if (qdq_conversion_mode != nullptr) {
    _impl_._has_bits_[0] |= 0x00000008u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000008u;
  }
  _impl_.qdq_conversion_mode_.SetAllocated(qdq_conversion_mode, GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:toco.TocoFlags.qdq_conversion_mode)
}

// optional .stablehlo.quantization.QuantizationConfig quantization_config = 61;
inline bool TocoFlags::_internal_has_quantization_config() const {
  bool value = (_impl_._has_bits_[0] & 0x00000040u) != 0;
  PROTOBUF_ASSUME(!value || _impl_.quantization_config_ != nullptr);
  return value;
}
inline bool TocoFlags::has_quantization_config() const {
  return _internal_has_quantization_config();
}
inline const ::stablehlo::quantization::QuantizationConfig& TocoFlags::_internal_quantization_config() const {
  const ::stablehlo::quantization::QuantizationConfig* p = _impl_.quantization_config_;
  return p != nullptr ? *p : reinterpret_cast<const ::stablehlo::quantization::QuantizationConfig&>(
      ::stablehlo::quantization::_QuantizationConfig_default_instance_);
}
inline const ::stablehlo::quantization::QuantizationConfig& TocoFlags::quantization_config() const {
  // @@protoc_insertion_point(field_get:toco.TocoFlags.quantization_config)
  return _internal_quantization_config();
}
inline void TocoFlags::unsafe_arena_set_allocated_quantization_config(
    ::stablehlo::quantization::QuantizationConfig* quantization_config) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.quantization_config_);
  }
  _impl_.quantization_config_ = quantization_config;
  if (quantization_config) {
    _impl_._has_bits_[0] |= 0x00000040u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000040u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:toco.TocoFlags.quantization_config)
}
inline ::stablehlo::quantization::QuantizationConfig* TocoFlags::release_quantization_config() {
  _impl_._has_bits_[0] &= ~0x00000040u;
  ::stablehlo::quantization::QuantizationConfig* temp = _impl_.quantization_config_;
  _impl_.quantization_config_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::stablehlo::quantization::QuantizationConfig* TocoFlags::unsafe_arena_release_quantization_config() {
  // @@protoc_insertion_point(field_release:toco.TocoFlags.quantization_config)
  _impl_._has_bits_[0] &= ~0x00000040u;
  ::stablehlo::quantization::QuantizationConfig* temp = _impl_.quantization_config_;
  _impl_.quantization_config_ = nullptr;
  return temp;
}
inline ::stablehlo::quantization::QuantizationConfig* TocoFlags::_internal_mutable_quantization_config() {
  _impl_._has_bits_[0] |= 0x00000040u;
  if (_impl_.quantization_config_ == nullptr) {
    auto* p = CreateMaybeMessage<::stablehlo::quantization::QuantizationConfig>(GetArenaForAllocation());
    _impl_.quantization_config_ = p;
  }
  return _impl_.quantization_config_;
}
inline ::stablehlo::quantization::QuantizationConfig* TocoFlags::mutable_quantization_config() {
  ::stablehlo::quantization::QuantizationConfig* _msg = _internal_mutable_quantization_config();
  // @@protoc_insertion_point(field_mutable:toco.TocoFlags.quantization_config)
  return _msg;
}
inline void TocoFlags::set_allocated_quantization_config(::stablehlo::quantization::QuantizationConfig* quantization_config) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.quantization_config_);
  }
  if (quantization_config) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(quantization_config));
    if (message_arena != submessage_arena) {
      quantization_config = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, quantization_config, submessage_arena);
    }
    _impl_._has_bits_[0] |= 0x00000040u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000040u;
  }
  _impl_.quantization_config_ = quantization_config;
  // @@protoc_insertion_point(field_set_allocated:toco.TocoFlags.quantization_config)
}

// optional bool disable_per_channel_quantization_for_dense_layers = 62 [default = false];
inline bool TocoFlags::_internal_has_disable_per_channel_quantization_for_dense_layers() const {
  bool value = (_impl_._has_bits_[1] & 0x00010000u) != 0;
  return value;
}
inline bool TocoFlags::has_disable_per_channel_quantization_for_dense_layers() const {
  return _internal_has_disable_per_channel_quantization_for_dense_layers();
}
inline void TocoFlags::clear_disable_per_channel_quantization_for_dense_layers() {
  _impl_.disable_per_channel_quantization_for_dense_layers_ = false;
  _impl_._has_bits_[1] &= ~0x00010000u;
}
inline bool TocoFlags::_internal_disable_per_channel_quantization_for_dense_layers() const {
  return _impl_.disable_per_channel_quantization_for_dense_layers_;
}
inline bool TocoFlags::disable_per_channel_quantization_for_dense_layers() const {
  // @@protoc_insertion_point(field_get:toco.TocoFlags.disable_per_channel_quantization_for_dense_layers)
  return _internal_disable_per_channel_quantization_for_dense_layers();
}
inline void TocoFlags::_internal_set_disable_per_channel_quantization_for_dense_layers(bool value) {
  _impl_._has_bits_[1] |= 0x00010000u;
  _impl_.disable_per_channel_quantization_for_dense_layers_ = value;
}
inline void TocoFlags::set_disable_per_channel_quantization_for_dense_layers(bool value) {
  _internal_set_disable_per_channel_quantization_for_dense_layers(value);
  // @@protoc_insertion_point(field_set:toco.TocoFlags.disable_per_channel_quantization_for_dense_layers)
}

// optional bool enable_composite_direct_lowering = 63 [default = false];
inline bool TocoFlags::_internal_has_enable_composite_direct_lowering() const {
  bool value = (_impl_._has_bits_[1] & 0x00020000u) != 0;
  return value;
}
inline bool TocoFlags::has_enable_composite_direct_lowering() const {
  return _internal_has_enable_composite_direct_lowering();
}
inline void TocoFlags::clear_enable_composite_direct_lowering() {
  _impl_.enable_composite_direct_lowering_ = false;
  _impl_._has_bits_[1] &= ~0x00020000u;
}
inline bool TocoFlags::_internal_enable_composite_direct_lowering() const {
  return _impl_.enable_composite_direct_lowering_;
}
inline bool TocoFlags::enable_composite_direct_lowering() const {
  // @@protoc_insertion_point(field_get:toco.TocoFlags.enable_composite_direct_lowering)
  return _internal_enable_composite_direct_lowering();
}
inline void TocoFlags::_internal_set_enable_composite_direct_lowering(bool value) {
  _impl_._has_bits_[1] |= 0x00020000u;
  _impl_.enable_composite_direct_lowering_ = value;
}
inline void TocoFlags::set_enable_composite_direct_lowering(bool value) {
  _internal_set_enable_composite_direct_lowering(value);
  // @@protoc_insertion_point(field_set:toco.TocoFlags.enable_composite_direct_lowering)
}

// optional .toco.TocoFlags.ModelOriginFramework model_origin_framework = 64 [default = UNSET];
inline bool TocoFlags::_internal_has_model_origin_framework() const {
  bool value = (_impl_._has_bits_[1] & 0x00080000u) != 0;
  return value;
}
inline bool TocoFlags::has_model_origin_framework() const {
  return _internal_has_model_origin_framework();
}
inline void TocoFlags::clear_model_origin_framework() {
  _impl_.model_origin_framework_ = 0;
  _impl_._has_bits_[1] &= ~0x00080000u;
}
inline ::toco::TocoFlags_ModelOriginFramework TocoFlags::_internal_model_origin_framework() const {
  return static_cast< ::toco::TocoFlags_ModelOriginFramework >(_impl_.model_origin_framework_);
}
inline ::toco::TocoFlags_ModelOriginFramework TocoFlags::model_origin_framework() const {
  // @@protoc_insertion_point(field_get:toco.TocoFlags.model_origin_framework)
  return _internal_model_origin_framework();
}
inline void TocoFlags::_internal_set_model_origin_framework(::toco::TocoFlags_ModelOriginFramework value) {
  assert(::toco::TocoFlags_ModelOriginFramework_IsValid(value));
  _impl_._has_bits_[1] |= 0x00080000u;
  _impl_.model_origin_framework_ = value;
}
inline void TocoFlags::set_model_origin_framework(::toco::TocoFlags_ModelOriginFramework value) {
  _internal_set_model_origin_framework(value);
  // @@protoc_insertion_point(field_set:toco.TocoFlags.model_origin_framework)
}

// optional bool canonicalizing_inf_as_min_max_float = 65 [default = false];
inline bool TocoFlags::_internal_has_canonicalizing_inf_as_min_max_float() const {
  bool value = (_impl_._has_bits_[1] & 0x00040000u) != 0;
  return value;
}
inline bool TocoFlags::has_canonicalizing_inf_as_min_max_float() const {
  return _internal_has_canonicalizing_inf_as_min_max_float();
}
inline void TocoFlags::clear_canonicalizing_inf_as_min_max_float() {
  _impl_.canonicalizing_inf_as_min_max_float_ = false;
  _impl_._has_bits_[1] &= ~0x00040000u;
}
inline bool TocoFlags::_internal_canonicalizing_inf_as_min_max_float() const {
  return _impl_.canonicalizing_inf_as_min_max_float_;
}
inline bool TocoFlags::canonicalizing_inf_as_min_max_float() const {
  // @@protoc_insertion_point(field_get:toco.TocoFlags.canonicalizing_inf_as_min_max_float)
  return _internal_canonicalizing_inf_as_min_max_float();
}
inline void TocoFlags::_internal_set_canonicalizing_inf_as_min_max_float(bool value) {
  _impl_._has_bits_[1] |= 0x00040000u;
  _impl_.canonicalizing_inf_as_min_max_float_ = value;
}
inline void TocoFlags::set_canonicalizing_inf_as_min_max_float(bool value) {
  _internal_set_canonicalizing_inf_as_min_max_float(value);
  // @@protoc_insertion_point(field_set:toco.TocoFlags.canonicalizing_inf_as_min_max_float)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace toco

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::toco::TocoFlags_ModelOriginFramework> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::toco::TocoFlags_ModelOriginFramework>() {
  return ::toco::TocoFlags_ModelOriginFramework_descriptor();
}
template <> struct is_proto_enum< ::toco::FileFormat> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::toco::FileFormat>() {
  return ::toco::FileFormat_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2flite_2ftoco_2ftoco_5fflags_2eproto
