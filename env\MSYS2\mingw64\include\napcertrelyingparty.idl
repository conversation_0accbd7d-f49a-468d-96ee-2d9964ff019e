/**
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER within this package.
 */

import "naptypes.idl";
import "unknwn.idl";

cpp_quote("#include <winapifamily.h>")
cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)")
cpp_quote("")
cpp_quote("#include <wincrypt.h>")
cpp_quote("")
cpp_quote("__MINGW_ATTRIB_UNUSED static const UINT32 NapAfwZonePropId = CERT_FIRST_USER_PROP_ID;")
cpp_quote("__MINGW_ATTRIB_UNUSED static const UINT32 NapAfwProtectionLevelPropId = CERT_FIRST_USER_PROP_ID + 1;")
cpp_quote("")
cpp_quote("EXTERN_C const CLSID CLSID_NapCertRelyingParty;")

[object, uuid (62c02ffc-4c77-4158-8754-782d5b4ee66e), pointer_default (unique)] interface INapCertRelyingParty : IUnknown {
  HRESULT SubscribeCertByGroup ([in] EnforcementEntityId id,[in] const BSTR subscriberName,[in, unique] const VARIANT *reserved,[out] WINBOOL *certExists);
  HRESULT UnSubscribeCertByGroup ([in] EnforcementEntityId id,[in, unique] const VARIANT *reserved);
  HRESULT GetSubscribedRelyingParties ([out] EnforcementEntityCount *count,[out, size_is (,*count)] EnforcementEntityId **relyingParties);
};
cpp_quote("#endif")
