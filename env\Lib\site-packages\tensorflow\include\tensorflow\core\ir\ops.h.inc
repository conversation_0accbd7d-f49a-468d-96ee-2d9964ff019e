/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|* Op Declarations                                                            *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|* From: ops.td                                                               *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

namespace mlir {
namespace tfg {
class CaseOp;
} // namespace tfg
} // namespace mlir
namespace mlir {
namespace tfg {
class CaseRegionOp;
} // namespace tfg
} // namespace mlir
namespace mlir {
namespace tfg {
class ConditionOp;
} // namespace tfg
} // namespace mlir
namespace mlir {
namespace tfg {
class ForOp;
} // namespace tfg
} // namespace mlir
namespace mlir {
namespace tfg {
class ForRegionOp;
} // namespace tfg
} // namespace mlir
namespace mlir {
namespace tfg {
class GetResultOp;
} // namespace tfg
} // namespace mlir
namespace mlir {
namespace tfg {
class GraphFuncOp;
} // namespace tfg
} // namespace mlir
namespace mlir {
namespace tfg {
class GraphOp;
} // namespace tfg
} // namespace mlir
namespace mlir {
namespace tfg {
class IfOp;
} // namespace tfg
} // namespace mlir
namespace mlir {
namespace tfg {
class IfRegionOp;
} // namespace tfg
} // namespace mlir
namespace mlir {
namespace tfg {
class ReturnOp;
} // namespace tfg
} // namespace mlir
namespace mlir {
namespace tfg {
class StatefulCaseOp;
} // namespace tfg
} // namespace mlir
namespace mlir {
namespace tfg {
class StatefulCaseRegionOp;
} // namespace tfg
} // namespace mlir
namespace mlir {
namespace tfg {
class StatefulIfOp;
} // namespace tfg
} // namespace mlir
namespace mlir {
namespace tfg {
class StatefulIfRegionOp;
} // namespace tfg
} // namespace mlir
namespace mlir {
namespace tfg {
class StatefulWhileOp;
} // namespace tfg
} // namespace mlir
namespace mlir {
namespace tfg {
class StatefulWhileRegionOp;
} // namespace tfg
} // namespace mlir
namespace mlir {
namespace tfg {
class StatelessCaseOp;
} // namespace tfg
} // namespace mlir
namespace mlir {
namespace tfg {
class StatelessCaseRegionOp;
} // namespace tfg
} // namespace mlir
namespace mlir {
namespace tfg {
class StatelessIfOp;
} // namespace tfg
} // namespace mlir
namespace mlir {
namespace tfg {
class StatelessIfRegionOp;
} // namespace tfg
} // namespace mlir
namespace mlir {
namespace tfg {
class StatelessWhileOp;
} // namespace tfg
} // namespace mlir
namespace mlir {
namespace tfg {
class StatelessWhileRegionOp;
} // namespace tfg
} // namespace mlir
namespace mlir {
namespace tfg {
class WhileOp;
} // namespace tfg
} // namespace mlir
namespace mlir {
namespace tfg {
class WhileRegionOp;
} // namespace tfg
} // namespace mlir
namespace mlir {
namespace tfg {
class YieldOp;
} // namespace tfg
} // namespace mlir
#ifdef GET_OP_CLASSES
#undef GET_OP_CLASSES

namespace mlir {
namespace tfg {

//===----------------------------------------------------------------------===//
// ::mlir::tfg::CaseOp declarations
//===----------------------------------------------------------------------===//

namespace detail {
class CaseOpGenericAdaptorBase {
public:
  struct Properties {
    using TinTy = ::mlir::ArrayAttr;
    TinTy Tin;

    auto getTin() {
      auto &propStorage = this->Tin;
      return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(propStorage);
    }
    void setTin(const ::mlir::ArrayAttr &propValue) {
      this->Tin = propValue;
    }
    using ToutTy = ::mlir::ArrayAttr;
    ToutTy Tout;

    auto getTout() {
      auto &propStorage = this->Tout;
      return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(propStorage);
    }
    void setTout(const ::mlir::ArrayAttr &propValue) {
      this->Tout = propValue;
    }
    using branchesTy = ::mlir::ArrayAttr;
    branchesTy branches;

    auto getBranches() {
      auto &propStorage = this->branches;
      return ::llvm::cast<::mlir::ArrayAttr>(propStorage);
    }
    void setBranches(const ::mlir::ArrayAttr &propValue) {
      this->branches = propValue;
    }
    using output_shapesTy = ::mlir::ArrayAttr;
    output_shapesTy output_shapes;

    auto getOutputShapes() {
      auto &propStorage = this->output_shapes;
      return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(propStorage);
    }
    void setOutputShapes(const ::mlir::ArrayAttr &propValue) {
      this->output_shapes = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.Tin == this->Tin &&
        rhs.Tout == this->Tout &&
        rhs.branches == this->branches &&
        rhs.output_shapes == this->output_shapes &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  CaseOpGenericAdaptorBase(::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : odsAttrs(attrs), properties(properties), odsRegions(regions) {  if (odsAttrs)
      odsOpName.emplace("tfg.Case", odsAttrs.getContext());
  }

  CaseOpGenericAdaptorBase(CaseOp op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes() {
    return odsAttrs;
  }

  ::mlir::ArrayAttr getBranchesAttr() {
    auto attr = ::llvm::cast<::mlir::ArrayAttr>(getProperties().branches);
    return attr;
  }

  ::mlir::ArrayAttr getBranches();
  ::mlir::ArrayAttr getTinAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().Tin);
    return attr;
  }

  ::std::optional< ::mlir::ArrayAttr > getTin();
  ::mlir::ArrayAttr getToutAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().Tout);
    return attr;
  }

  ::std::optional< ::mlir::ArrayAttr > getTout();
  ::mlir::ArrayAttr getOutputShapesAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().output_shapes);
    return attr;
  }

  ::std::optional< ::mlir::ArrayAttr > getOutputShapes();
};
} // namespace detail
template <typename RangeT>
class CaseOpGenericAdaptor : public detail::CaseOpGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::CaseOpGenericAdaptorBase;
public:
  CaseOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  CaseOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : CaseOpGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  CaseOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr) : CaseOpGenericAdaptor(values, attrs, Properties{}, {}) {}

  CaseOpGenericAdaptor(RangeT values, const CaseOpGenericAdaptorBase &base) : Base(base), odsOperands(values) {}

  template <typename LateInst = CaseOp, typename = std::enable_if_t<std::is_same_v<LateInst, CaseOp>>>
  CaseOpGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getBranchIndex() {
    return (*getODSOperands(0).begin());
  }

  RangeT getArgs() {
    return getODSOperands(1);
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class CaseOpAdaptor : public CaseOpGenericAdaptor<::mlir::ValueRange> {
public:
  using CaseOpGenericAdaptor::CaseOpGenericAdaptor;
  CaseOpAdaptor(CaseOp op);

  ::llvm::LogicalResult verify(::mlir::Location loc);
};
class CaseOp : public ::mlir::Op<CaseOp, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::AtLeastNResults<1>::Impl, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::AtLeastNOperands<1>::Impl, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait, ::mlir::SymbolUserOpInterface::Trait, ::mlir::OpAsmOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = CaseOpAdaptor;
  template <typename RangeT>
  using GenericAdaptor = CaseOpGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("Tin"), ::llvm::StringRef("Tout"), ::llvm::StringRef("branches"), ::llvm::StringRef("output_shapes")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getTinAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getTinAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  ::mlir::StringAttr getToutAttrName() {
    return getAttributeNameForIndex(1);
  }

  static ::mlir::StringAttr getToutAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 1);
  }

  ::mlir::StringAttr getBranchesAttrName() {
    return getAttributeNameForIndex(2);
  }

  static ::mlir::StringAttr getBranchesAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 2);
  }

  ::mlir::StringAttr getOutputShapesAttrName() {
    return getAttributeNameForIndex(3);
  }

  static ::mlir::StringAttr getOutputShapesAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 3);
  }

  void getAsmResultNames(::mlir::OpAsmSetValueNameFn setNameFn);
  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("tfg.Case");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(getOperation()->operand_begin(), valueRange.first),
             std::next(getOperation()->operand_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::TypedValue<::mlir::Type> getBranchIndex() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::Type>>(*getODSOperands(0).begin());
  }

  ::mlir::Operation::operand_range getArgs() {
    return getODSOperands(1);
  }

  ::mlir::OpOperand &getBranchIndexMutable() {
    auto range = getODSOperandIndexAndLength(0);
    return getOperation()->getOpOperand(range.first);
  }

  ::mlir::MutableOperandRange getArgsMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index) {
    auto valueRange = getODSResultIndexAndLength(index);
    return {std::next(getOperation()->result_begin(), valueRange.first),
             std::next(getOperation()->result_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::Operation::result_range getOuts() {
    return getODSResults(0);
  }

  ::mlir::TypedValue<::mlir::tf_type::ControlType> getCtl() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::tf_type::ControlType>>(*getODSResults(1).begin());
  }

  static ::llvm::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::llvm::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::llvm::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::ArrayAttr getBranchesAttr() {
    return ::llvm::cast<::mlir::ArrayAttr>(getProperties().branches);
  }

  ::mlir::ArrayAttr getBranches();
  ::mlir::ArrayAttr getTinAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().Tin);
  }

  ::std::optional< ::mlir::ArrayAttr > getTin();
  ::mlir::ArrayAttr getToutAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().Tout);
  }

  ::std::optional< ::mlir::ArrayAttr > getTout();
  ::mlir::ArrayAttr getOutputShapesAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().output_shapes);
  }

  ::std::optional< ::mlir::ArrayAttr > getOutputShapes();
  void setBranchesAttr(::mlir::ArrayAttr attr) {
    getProperties().branches = attr;
  }

  void setTinAttr(::mlir::ArrayAttr attr) {
    getProperties().Tin = attr;
  }

  void setToutAttr(::mlir::ArrayAttr attr) {
    getProperties().Tout = attr;
  }

  void setOutputShapesAttr(::mlir::ArrayAttr attr) {
    getProperties().output_shapes = attr;
  }

  ::mlir::Attribute removeTinAttr() {
      auto &attr = getProperties().Tin;
      attr = {};
      return attr;
  }

  ::mlir::Attribute removeToutAttr() {
      auto &attr = getProperties().Tout;
      attr = {};
      return attr;
  }

  ::mlir::Attribute removeOutputShapesAttr() {
      auto &attr = getProperties().output_shapes;
      attr = {};
      return attr;
  }

  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, TypeRange results, Value branch_index, ValueRange args, ArrayAttr branches);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange outs, ::mlir::Type ctl, ::mlir::Value branch_index, ::mlir::ValueRange args, ::mlir::ArrayAttr branches, /*optional*/::mlir::ArrayAttr Tin, /*optional*/::mlir::ArrayAttr Tout, /*optional*/::mlir::ArrayAttr output_shapes);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value branch_index, ::mlir::ValueRange args, ::mlir::ArrayAttr branches, /*optional*/::mlir::ArrayAttr Tin, /*optional*/::mlir::ArrayAttr Tout, /*optional*/::mlir::ArrayAttr output_shapes);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::llvm::LogicalResult verifyInvariantsImpl();
  ::llvm::LogicalResult verifyInvariants();
  ::llvm::LogicalResult verifySymbolUses(::mlir::SymbolTableCollection &symbolTable);
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 4 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace tfg
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::tfg::CaseOp)

namespace mlir {
namespace tfg {

//===----------------------------------------------------------------------===//
// ::mlir::tfg::CaseRegionOp declarations
//===----------------------------------------------------------------------===//

namespace detail {
class CaseRegionOpGenericAdaptorBase {
public:
  struct Properties {
    using branch_attrsTy = ::mlir::ArrayAttr;
    branch_attrsTy branch_attrs;

    auto getBranchAttrs() {
      auto &propStorage = this->branch_attrs;
      return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(propStorage);
    }
    void setBranchAttrs(const ::mlir::ArrayAttr &propValue) {
      this->branch_attrs = propValue;
    }
    using region_attrsTy = ::mlir::ArrayAttr;
    region_attrsTy region_attrs;

    auto getRegionAttrs() {
      auto &propStorage = this->region_attrs;
      return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(propStorage);
    }
    void setRegionAttrs(const ::mlir::ArrayAttr &propValue) {
      this->region_attrs = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.branch_attrs == this->branch_attrs &&
        rhs.region_attrs == this->region_attrs &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  CaseRegionOpGenericAdaptorBase(::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : odsAttrs(attrs), properties(properties), odsRegions(regions) {  if (odsAttrs)
      odsOpName.emplace("tfg.CaseRegion", odsAttrs.getContext());
  }

  CaseRegionOpGenericAdaptorBase(CaseRegionOp op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes() {
    return odsAttrs;
  }

  ::mlir::ArrayAttr getBranchAttrsAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().branch_attrs);
    return attr;
  }

  ::std::optional< ::mlir::ArrayAttr > getBranchAttrs();
  ::mlir::ArrayAttr getRegionAttrsAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().region_attrs);
    return attr;
  }

  ::std::optional< ::mlir::ArrayAttr > getRegionAttrs();
  ::mlir::RegionRange getBranches() {
    return odsRegions.drop_front(0);
  }

  ::mlir::RegionRange getRegions() {
    return odsRegions;
  }

};
} // namespace detail
template <typename RangeT>
class CaseRegionOpGenericAdaptor : public detail::CaseRegionOpGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::CaseRegionOpGenericAdaptorBase;
public:
  CaseRegionOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  CaseRegionOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : CaseRegionOpGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  CaseRegionOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr) : CaseRegionOpGenericAdaptor(values, attrs, Properties{}, {}) {}

  CaseRegionOpGenericAdaptor(RangeT values, const CaseRegionOpGenericAdaptorBase &base) : Base(base), odsOperands(values) {}

  template <typename LateInst = CaseRegionOp, typename = std::enable_if_t<std::is_same_v<LateInst, CaseRegionOp>>>
  CaseRegionOpGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getBranchIndex() {
    return (*getODSOperands(0).begin());
  }

  RangeT getCtls() {
    return getODSOperands(1);
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class CaseRegionOpAdaptor : public CaseRegionOpGenericAdaptor<::mlir::ValueRange> {
public:
  using CaseRegionOpGenericAdaptor::CaseRegionOpGenericAdaptor;
  CaseRegionOpAdaptor(CaseRegionOp op);

  ::llvm::LogicalResult verify(::mlir::Location loc);
};
class CaseRegionOp : public ::mlir::Op<CaseRegionOp, ::mlir::OpTrait::VariadicRegions, ::mlir::OpTrait::AtLeastNResults<1>::Impl, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::AtLeastNOperands<1>::Impl, ::mlir::OpTrait::SingleBlock, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait, ::mlir::OpTrait::HasOnlyGraphRegion, ::mlir::RegionKindInterface::Trait, ::mlir::OpAsmOpInterface::Trait, ::mlir::tfg::PreservedAttributesInterface::Trait, ::mlir::RegionBranchOpInterface::Trait, ::mlir::tfg::ControlArgumentInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = CaseRegionOpAdaptor;
  template <typename RangeT>
  using GenericAdaptor = CaseRegionOpGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("branch_attrs"), ::llvm::StringRef("region_attrs")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getBranchAttrsAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getBranchAttrsAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  ::mlir::StringAttr getRegionAttrsAttrName() {
    return getAttributeNameForIndex(1);
  }

  static ::mlir::StringAttr getRegionAttrsAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 1);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("tfg.CaseRegion");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(getOperation()->operand_begin(), valueRange.first),
             std::next(getOperation()->operand_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::TypedValue<::mlir::TensorType> getBranchIndex() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::TensorType>>(*getODSOperands(0).begin());
  }

  ::mlir::Operation::operand_range getCtls() {
    return getODSOperands(1);
  }

  ::mlir::OpOperand &getBranchIndexMutable() {
    auto range = getODSOperandIndexAndLength(0);
    return getOperation()->getOpOperand(range.first);
  }

  ::mlir::MutableOperandRange getCtlsMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index) {
    auto valueRange = getODSResultIndexAndLength(index);
    return {std::next(getOperation()->result_begin(), valueRange.first),
             std::next(getOperation()->result_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::Operation::result_range getOuts() {
    return getODSResults(0);
  }

  ::mlir::TypedValue<::mlir::tf_type::ControlType> getCtl() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::tf_type::ControlType>>(*getODSResults(1).begin());
  }

  ::mlir::MutableArrayRef<::mlir::Region> getBranches() {
    return (*this)->getRegions().drop_front(0);
  }

  static ::llvm::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::llvm::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::llvm::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::ArrayAttr getBranchAttrsAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().branch_attrs);
  }

  ::std::optional< ::mlir::ArrayAttr > getBranchAttrs();
  ::mlir::ArrayAttr getRegionAttrsAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().region_attrs);
  }

  ::std::optional< ::mlir::ArrayAttr > getRegionAttrs();
  void setBranchAttrsAttr(::mlir::ArrayAttr attr) {
    getProperties().branch_attrs = attr;
  }

  void setRegionAttrsAttr(::mlir::ArrayAttr attr) {
    getProperties().region_attrs = attr;
  }

  ::mlir::Attribute removeBranchAttrsAttr() {
      auto &attr = getProperties().branch_attrs;
      attr = {};
      return attr;
  }

  ::mlir::Attribute removeRegionAttrsAttr() {
      auto &attr = getProperties().region_attrs;
      attr = {};
      return attr;
  }

  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange outs, ::mlir::Type ctl, ::mlir::Value branch_index, ::mlir::ValueRange ctls, /*optional*/::mlir::ArrayAttr branch_attrs, /*optional*/::mlir::ArrayAttr region_attrs, unsigned branchesCount);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value branch_index, ::mlir::ValueRange ctls, /*optional*/::mlir::ArrayAttr branch_attrs, /*optional*/::mlir::ArrayAttr region_attrs, unsigned branchesCount);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes, unsigned numRegions);
  ::llvm::LogicalResult verifyInvariantsImpl();
  ::llvm::LogicalResult verifyInvariants();
  ::llvm::LogicalResult verify();
  void getAsmResultNames(::mlir::OpAsmSetValueNameFn setNameFn);
  static ::llvm::StringRef getDefaultDialect();
  void getEntrySuccessorRegions(::llvm::ArrayRef<::mlir::Attribute> operands, ::llvm::SmallVectorImpl<::mlir::RegionSuccessor> &regions);
  void getSuccessorRegions(::mlir::RegionBranchPoint point, ::llvm::SmallVectorImpl<::mlir::RegionSuccessor> &regions);
  void getRegionInvocationBounds(::llvm::ArrayRef<::mlir::Attribute> operands, ::llvm::SmallVectorImpl<::mlir::InvocationBounds> &invocationBounds);
  static mlir::BlockArgument getDataValueOf(BlockArgument ctl);
  static mlir::BlockArgument getControlTokenOf(BlockArgument data);
  static mlir::BlockArgument getDataValue(Region &region, unsigned idx);
  static mlir::BlockArgument getControlToken(Region &region, unsigned idx);
  static ::mlir::ParseResult parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result);
  void print(::mlir::OpAsmPrinter &_odsPrinter);
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 2 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
  Block &getBranchBlock(unsigned idx) { return getBranches()[idx].front(); }
  YieldOp branch_yield(unsigned idx);

  bool areTypesCompatible(Type lhs, Type rhs) {
    return tf_type::HasCompatibleElementTypes(lhs, rhs);
  }

  RegionAttr getPreservedAttrs(unsigned index) {
    if (auto attrs = getRegionAttrsAttr())
      return attrs[index].cast<RegionAttr>();
    return {};
  }
  void setPreservedAttrs(unsigned index, RegionAttr attrs) {
    SmallVector<Attribute> array = llvm::to_vector(getRegionAttrsAttr());
    array[index] = attrs;
    setRegionAttrsAttr(ArrayAttr::get(getContext(), array));
  }
};
} // namespace tfg
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::tfg::CaseRegionOp)

namespace mlir {
namespace tfg {

//===----------------------------------------------------------------------===//
// ::mlir::tfg::ConditionOp declarations
//===----------------------------------------------------------------------===//

namespace detail {
class ConditionOpGenericAdaptorBase {
public:
  struct Properties {
    using operandSegmentSizesTy = std::array<int32_t, 3>;
    operandSegmentSizesTy operandSegmentSizes;
    ::llvm::ArrayRef<int32_t> getOperandSegmentSizes() const {
      auto &propStorage = this->operandSegmentSizes;
      return propStorage;
    }
    void setOperandSegmentSizes(::llvm::ArrayRef<int32_t> propValue) {
      auto &propStorage = this->operandSegmentSizes;
      ::llvm::copy(propValue, propStorage.begin());
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.operandSegmentSizes == this->operandSegmentSizes &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  ConditionOpGenericAdaptorBase(::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : odsAttrs(attrs), properties(properties), odsRegions(regions) {  if (odsAttrs)
      odsOpName.emplace("tfg.condition", odsAttrs.getContext());
  }

  ConditionOpGenericAdaptorBase(ConditionOp op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes() {
    return odsAttrs;
  }

};
} // namespace detail
template <typename RangeT>
class ConditionOpGenericAdaptor : public detail::ConditionOpGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::ConditionOpGenericAdaptorBase;
public:
  ConditionOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  ConditionOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : ConditionOpGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  ConditionOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs) : ConditionOpGenericAdaptor(values, attrs, Properties{}, {}) {}

  ConditionOpGenericAdaptor(RangeT values, const ConditionOpGenericAdaptorBase &base) : Base(base), odsOperands(values) {}

  template <typename LateInst = ConditionOp, typename = std::enable_if_t<std::is_same_v<LateInst, ConditionOp>>>
  ConditionOpGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getCond() {
    return (*getODSOperands(0).begin());
  }

  RangeT getArgs() {
    return getODSOperands(1);
  }

  RangeT getCtls() {
    return getODSOperands(2);
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class ConditionOpAdaptor : public ConditionOpGenericAdaptor<::mlir::ValueRange> {
public:
  using ConditionOpGenericAdaptor::ConditionOpGenericAdaptor;
  ConditionOpAdaptor(ConditionOp op);

  ::llvm::LogicalResult verify(::mlir::Location loc);
};
class ConditionOp : public ::mlir::Op<ConditionOp, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::ZeroResults, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::AtLeastNOperands<1>::Impl, ::mlir::OpTrait::AttrSizedOperandSegments, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait, ::mlir::ConditionallySpeculatable::Trait, ::mlir::OpTrait::AlwaysSpeculatableImplTrait, ::mlir::MemoryEffectOpInterface::Trait, ::mlir::OpTrait::ReturnLike, ::mlir::OpTrait::IsTerminator, ::mlir::RegionBranchTerminatorOpInterface::Trait, ::mlir::OpTrait::IntrinsicOperation> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = ConditionOpAdaptor;
  template <typename RangeT>
  using GenericAdaptor = ConditionOpGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("operandSegmentSizes")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getOperandSegmentSizesAttrName() {
   return (*this)->getName().getAttributeNames().back();
  }

  static ::mlir::StringAttr getOperandSegmentSizesAttrName(::mlir::OperationName name) {
   return name.getAttributeNames().back();
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("tfg.condition");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(getOperation()->operand_begin(), valueRange.first),
             std::next(getOperation()->operand_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::TypedValue<::mlir::TensorType> getCond() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::TensorType>>(*getODSOperands(0).begin());
  }

  ::mlir::Operation::operand_range getArgs() {
    return getODSOperands(1);
  }

  ::mlir::Operation::operand_range getCtls() {
    return getODSOperands(2);
  }

  ::mlir::OpOperand &getCondMutable() {
    auto range = getODSOperandIndexAndLength(0);
    return getOperation()->getOpOperand(range.first);
  }

  ::mlir::MutableOperandRange getArgsMutable();
  ::mlir::MutableOperandRange getCtlsMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index) {
    return {index, 1};
  }

  ::mlir::Operation::result_range getODSResults(unsigned index) {
    auto valueRange = getODSResultIndexAndLength(index);
    return {std::next(getOperation()->result_begin(), valueRange.first),
             std::next(getOperation()->result_begin(), valueRange.first + valueRange.second)};
  }

  static ::llvm::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::llvm::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::llvm::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Value cond, ::mlir::ValueRange args, ::mlir::ValueRange ctls);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value cond, ::mlir::ValueRange args, ::mlir::ValueRange ctls);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::llvm::LogicalResult verifyInvariantsImpl();
  ::llvm::LogicalResult verifyInvariants();
  ::mlir::MutableOperandRange getMutableSuccessorOperands(::mlir::RegionBranchPoint point);
  static ::mlir::ParseResult parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result);
  void print(::mlir::OpAsmPrinter &_odsPrinter);
  void getEffects(::llvm::SmallVectorImpl<::mlir::SideEffects::EffectInstance<::mlir::MemoryEffects::Effect>> &effects);
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    return {};
  }

public:
};
} // namespace tfg
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::tfg::ConditionOp)

namespace mlir {
namespace tfg {

//===----------------------------------------------------------------------===//
// ::mlir::tfg::ForOp declarations
//===----------------------------------------------------------------------===//

namespace detail {
class ForOpGenericAdaptorBase {
public:
  struct Properties {
    using TTy = ::mlir::ArrayAttr;
    TTy T;

    auto getT() {
      auto &propStorage = this->T;
      return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(propStorage);
    }
    void setT(const ::mlir::ArrayAttr &propValue) {
      this->T = propValue;
    }
    using bodyTy = ::mlir::tf_type::FuncAttr;
    bodyTy body;

    auto getBody() {
      auto &propStorage = this->body;
      return ::llvm::cast<::mlir::tf_type::FuncAttr>(propStorage);
    }
    void setBody(const ::mlir::tf_type::FuncAttr &propValue) {
      this->body = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.T == this->T &&
        rhs.body == this->body &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  ForOpGenericAdaptorBase(::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : odsAttrs(attrs), properties(properties), odsRegions(regions) {  if (odsAttrs)
      odsOpName.emplace("tfg.For", odsAttrs.getContext());
  }

  ForOpGenericAdaptorBase(ForOp op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes() {
    return odsAttrs;
  }

  ::mlir::tf_type::FuncAttr getBodyAttr() {
    auto attr = ::llvm::cast<::mlir::tf_type::FuncAttr>(getProperties().body);
    return attr;
  }

  ::mlir::tf_type::FuncAttr getBody();
  ::mlir::ArrayAttr getTAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().T);
    return attr;
  }

  ::std::optional< ::mlir::ArrayAttr > getT();
};
} // namespace detail
template <typename RangeT>
class ForOpGenericAdaptor : public detail::ForOpGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::ForOpGenericAdaptorBase;
public:
  ForOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  ForOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : ForOpGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  ForOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr) : ForOpGenericAdaptor(values, attrs, Properties{}, {}) {}

  ForOpGenericAdaptor(RangeT values, const ForOpGenericAdaptorBase &base) : Base(base), odsOperands(values) {}

  template <typename LateInst = ForOp, typename = std::enable_if_t<std::is_same_v<LateInst, ForOp>>>
  ForOpGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getStart() {
    return (*getODSOperands(0).begin());
  }

  ValueT getLimit() {
    return (*getODSOperands(1).begin());
  }

  ValueT getDelta() {
    return (*getODSOperands(2).begin());
  }

  RangeT getArgs() {
    return getODSOperands(3);
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class ForOpAdaptor : public ForOpGenericAdaptor<::mlir::ValueRange> {
public:
  using ForOpGenericAdaptor::ForOpGenericAdaptor;
  ForOpAdaptor(ForOp op);

  ::llvm::LogicalResult verify(::mlir::Location loc);
};
class ForOp : public ::mlir::Op<ForOp, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::AtLeastNResults<1>::Impl, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::AtLeastNOperands<3>::Impl, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait, ::mlir::SymbolUserOpInterface::Trait, ::mlir::OpAsmOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = ForOpAdaptor;
  template <typename RangeT>
  using GenericAdaptor = ForOpGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("T"), ::llvm::StringRef("body")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getTAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getTAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  ::mlir::StringAttr getBodyAttrName() {
    return getAttributeNameForIndex(1);
  }

  static ::mlir::StringAttr getBodyAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 1);
  }

  void getAsmResultNames(::mlir::OpAsmSetValueNameFn setNameFn);
  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("tfg.For");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(getOperation()->operand_begin(), valueRange.first),
             std::next(getOperation()->operand_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::TypedValue<::mlir::Type> getStart() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::Type>>(*getODSOperands(0).begin());
  }

  ::mlir::TypedValue<::mlir::Type> getLimit() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::Type>>(*getODSOperands(1).begin());
  }

  ::mlir::TypedValue<::mlir::Type> getDelta() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::Type>>(*getODSOperands(2).begin());
  }

  ::mlir::Operation::operand_range getArgs() {
    return getODSOperands(3);
  }

  ::mlir::OpOperand &getStartMutable() {
    auto range = getODSOperandIndexAndLength(0);
    return getOperation()->getOpOperand(range.first);
  }

  ::mlir::OpOperand &getLimitMutable() {
    auto range = getODSOperandIndexAndLength(1);
    return getOperation()->getOpOperand(range.first);
  }

  ::mlir::OpOperand &getDeltaMutable() {
    auto range = getODSOperandIndexAndLength(2);
    return getOperation()->getOpOperand(range.first);
  }

  ::mlir::MutableOperandRange getArgsMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index) {
    auto valueRange = getODSResultIndexAndLength(index);
    return {std::next(getOperation()->result_begin(), valueRange.first),
             std::next(getOperation()->result_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::Operation::result_range getOuts() {
    return getODSResults(0);
  }

  ::mlir::TypedValue<::mlir::tf_type::ControlType> getCtl() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::tf_type::ControlType>>(*getODSResults(1).begin());
  }

  static ::llvm::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::llvm::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::llvm::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::tf_type::FuncAttr getBodyAttr() {
    return ::llvm::cast<::mlir::tf_type::FuncAttr>(getProperties().body);
  }

  ::mlir::tf_type::FuncAttr getBody();
  ::mlir::ArrayAttr getTAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().T);
  }

  ::std::optional< ::mlir::ArrayAttr > getT();
  void setBodyAttr(::mlir::tf_type::FuncAttr attr) {
    getProperties().body = attr;
  }

  void setTAttr(::mlir::ArrayAttr attr) {
    getProperties().T = attr;
  }

  ::mlir::Attribute removeTAttr() {
      auto &attr = getProperties().T;
      attr = {};
      return attr;
  }

  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, TypeRange results, Value start, Value limit, Value delta, ValueRange args, FuncAttr body);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange outs, ::mlir::Type ctl, ::mlir::Value start, ::mlir::Value limit, ::mlir::Value delta, ::mlir::ValueRange args, ::mlir::tf_type::FuncAttr body, /*optional*/::mlir::ArrayAttr T);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value start, ::mlir::Value limit, ::mlir::Value delta, ::mlir::ValueRange args, ::mlir::tf_type::FuncAttr body, /*optional*/::mlir::ArrayAttr T);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::llvm::LogicalResult verifyInvariantsImpl();
  ::llvm::LogicalResult verifyInvariants();
  ::llvm::LogicalResult verifySymbolUses(::mlir::SymbolTableCollection &symbolTable);
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 2 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace tfg
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::tfg::ForOp)

namespace mlir {
namespace tfg {

//===----------------------------------------------------------------------===//
// ::mlir::tfg::ForRegionOp declarations
//===----------------------------------------------------------------------===//

namespace detail {
class ForRegionOpGenericAdaptorBase {
public:
  struct Properties {
    using body_attrsTy = ::mlir::DictionaryAttr;
    body_attrsTy body_attrs;

    auto getBodyAttrs() {
      auto &propStorage = this->body_attrs;
      return ::llvm::dyn_cast_or_null<::mlir::DictionaryAttr>(propStorage);
    }
    void setBodyAttrs(const ::mlir::DictionaryAttr &propValue) {
      this->body_attrs = propValue;
    }
    using region_attrsTy = ::mlir::tfg::RegionAttr;
    region_attrsTy region_attrs;

    auto getRegionAttrs() {
      auto &propStorage = this->region_attrs;
      return ::llvm::dyn_cast_or_null<::mlir::tfg::RegionAttr>(propStorage);
    }
    void setRegionAttrs(const ::mlir::tfg::RegionAttr &propValue) {
      this->region_attrs = propValue;
    }
    using operandSegmentSizesTy = std::array<int32_t, 5>;
    operandSegmentSizesTy operandSegmentSizes;
    ::llvm::ArrayRef<int32_t> getOperandSegmentSizes() const {
      auto &propStorage = this->operandSegmentSizes;
      return propStorage;
    }
    void setOperandSegmentSizes(::llvm::ArrayRef<int32_t> propValue) {
      auto &propStorage = this->operandSegmentSizes;
      ::llvm::copy(propValue, propStorage.begin());
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.body_attrs == this->body_attrs &&
        rhs.region_attrs == this->region_attrs &&
        rhs.operandSegmentSizes == this->operandSegmentSizes &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  ForRegionOpGenericAdaptorBase(::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : odsAttrs(attrs), properties(properties), odsRegions(regions) {  if (odsAttrs)
      odsOpName.emplace("tfg.ForRegion", odsAttrs.getContext());
  }

  ForRegionOpGenericAdaptorBase(ForRegionOp op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes() {
    return odsAttrs;
  }

  ::mlir::DictionaryAttr getBodyAttrsAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::DictionaryAttr>(getProperties().body_attrs);
    return attr;
  }

  ::std::optional< ::mlir::DictionaryAttr > getBodyAttrs();
  ::mlir::tfg::RegionAttr getRegionAttrsAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::tfg::RegionAttr>(getProperties().region_attrs);
    return attr;
  }

  ::std::optional<::mlir::tfg::RegionAttr> getRegionAttrs();
  ::mlir::Region &getBodyRegion() {
    return *odsRegions[0];
  }

  ::mlir::RegionRange getRegions() {
    return odsRegions;
  }

};
} // namespace detail
template <typename RangeT>
class ForRegionOpGenericAdaptor : public detail::ForRegionOpGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::ForRegionOpGenericAdaptorBase;
public:
  ForRegionOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  ForRegionOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : ForRegionOpGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  ForRegionOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs) : ForRegionOpGenericAdaptor(values, attrs, Properties{}, {}) {}

  ForRegionOpGenericAdaptor(RangeT values, const ForRegionOpGenericAdaptorBase &base) : Base(base), odsOperands(values) {}

  template <typename LateInst = ForRegionOp, typename = std::enable_if_t<std::is_same_v<LateInst, ForRegionOp>>>
  ForRegionOpGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getStart() {
    return (*getODSOperands(0).begin());
  }

  ValueT getLimit() {
    return (*getODSOperands(1).begin());
  }

  ValueT getDelta() {
    return (*getODSOperands(2).begin());
  }

  RangeT getInit() {
    return getODSOperands(3);
  }

  RangeT getCtls() {
    return getODSOperands(4);
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class ForRegionOpAdaptor : public ForRegionOpGenericAdaptor<::mlir::ValueRange> {
public:
  using ForRegionOpGenericAdaptor::ForRegionOpGenericAdaptor;
  ForRegionOpAdaptor(ForRegionOp op);

  ::llvm::LogicalResult verify(::mlir::Location loc);
};
class ForRegionOp : public ::mlir::Op<ForRegionOp, ::mlir::OpTrait::OneRegion, ::mlir::OpTrait::AtLeastNResults<1>::Impl, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::AtLeastNOperands<3>::Impl, ::mlir::OpTrait::SingleBlock, ::mlir::OpTrait::AttrSizedOperandSegments, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait, ::mlir::OpTrait::HasOnlyGraphRegion, ::mlir::RegionKindInterface::Trait, ::mlir::OpAsmOpInterface::Trait, ::mlir::tfg::PreservedAttributesInterface::Trait, ::mlir::RegionBranchOpInterface::Trait, ::mlir::tfg::ControlArgumentInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = ForRegionOpAdaptor;
  template <typename RangeT>
  using GenericAdaptor = ForRegionOpGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("body_attrs"), ::llvm::StringRef("region_attrs"), ::llvm::StringRef("operandSegmentSizes")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getBodyAttrsAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getBodyAttrsAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  ::mlir::StringAttr getRegionAttrsAttrName() {
    return getAttributeNameForIndex(1);
  }

  static ::mlir::StringAttr getRegionAttrsAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 1);
  }

  ::mlir::StringAttr getOperandSegmentSizesAttrName() {
   return (*this)->getName().getAttributeNames().back();
  }

  static ::mlir::StringAttr getOperandSegmentSizesAttrName(::mlir::OperationName name) {
   return name.getAttributeNames().back();
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("tfg.ForRegion");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(getOperation()->operand_begin(), valueRange.first),
             std::next(getOperation()->operand_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::TypedValue<::mlir::TensorType> getStart() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::TensorType>>(*getODSOperands(0).begin());
  }

  ::mlir::TypedValue<::mlir::TensorType> getLimit() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::TensorType>>(*getODSOperands(1).begin());
  }

  ::mlir::TypedValue<::mlir::TensorType> getDelta() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::TensorType>>(*getODSOperands(2).begin());
  }

  ::mlir::Operation::operand_range getInit() {
    return getODSOperands(3);
  }

  ::mlir::Operation::operand_range getCtls() {
    return getODSOperands(4);
  }

  ::mlir::OpOperand &getStartMutable() {
    auto range = getODSOperandIndexAndLength(0);
    return getOperation()->getOpOperand(range.first);
  }

  ::mlir::OpOperand &getLimitMutable() {
    auto range = getODSOperandIndexAndLength(1);
    return getOperation()->getOpOperand(range.first);
  }

  ::mlir::OpOperand &getDeltaMutable() {
    auto range = getODSOperandIndexAndLength(2);
    return getOperation()->getOpOperand(range.first);
  }

  ::mlir::MutableOperandRange getInitMutable();
  ::mlir::MutableOperandRange getCtlsMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index) {
    auto valueRange = getODSResultIndexAndLength(index);
    return {std::next(getOperation()->result_begin(), valueRange.first),
             std::next(getOperation()->result_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::Operation::result_range getOuts() {
    return getODSResults(0);
  }

  ::mlir::TypedValue<::mlir::tf_type::ControlType> getCtl() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::tf_type::ControlType>>(*getODSResults(1).begin());
  }

  ::mlir::Region &getBodyRegion() {
    return (*this)->getRegion(0);
  }

  static ::llvm::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::llvm::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::llvm::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::DictionaryAttr getBodyAttrsAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::DictionaryAttr>(getProperties().body_attrs);
  }

  ::std::optional< ::mlir::DictionaryAttr > getBodyAttrs();
  ::mlir::tfg::RegionAttr getRegionAttrsAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::tfg::RegionAttr>(getProperties().region_attrs);
  }

  ::std::optional<::mlir::tfg::RegionAttr> getRegionAttrs();
  void setBodyAttrsAttr(::mlir::DictionaryAttr attr) {
    getProperties().body_attrs = attr;
  }

  void setRegionAttrsAttr(::mlir::tfg::RegionAttr attr) {
    getProperties().region_attrs = attr;
  }

  ::mlir::Attribute removeBodyAttrsAttr() {
      auto &attr = getProperties().body_attrs;
      attr = {};
      return attr;
  }

  ::mlir::Attribute removeRegionAttrsAttr() {
      auto &attr = getProperties().region_attrs;
      attr = {};
      return attr;
  }

  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange outs, ::mlir::Type ctl, ::mlir::Value start, ::mlir::Value limit, ::mlir::Value delta, ::mlir::ValueRange init, ::mlir::ValueRange ctls, /*optional*/::mlir::DictionaryAttr body_attrs, /*optional*/::mlir::tfg::RegionAttr region_attrs);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value start, ::mlir::Value limit, ::mlir::Value delta, ::mlir::ValueRange init, ::mlir::ValueRange ctls, /*optional*/::mlir::DictionaryAttr body_attrs, /*optional*/::mlir::tfg::RegionAttr region_attrs);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::llvm::LogicalResult verifyInvariantsImpl();
  ::llvm::LogicalResult verifyInvariants();
  ::llvm::LogicalResult verify();
  void getAsmResultNames(::mlir::OpAsmSetValueNameFn setNameFn);
  static ::llvm::StringRef getDefaultDialect();
  ::mlir::OperandRange getEntrySuccessorOperands(::mlir::RegionBranchPoint point);
  void getSuccessorRegions(::mlir::RegionBranchPoint point, ::llvm::SmallVectorImpl<::mlir::RegionSuccessor> &regions);
  static mlir::BlockArgument getDataValueOf(BlockArgument ctl);
  static mlir::BlockArgument getControlTokenOf(BlockArgument data);
  static mlir::BlockArgument getDataValue(Region &region, unsigned idx);
  static mlir::BlockArgument getControlToken(Region &region, unsigned idx);
  static ::mlir::ParseResult parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result);
  void print(::mlir::OpAsmPrinter &_odsPrinter);
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 2 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
  Block &getBodyBlock() { return getBodyRegion().front(); }
  YieldOp getBodyYield();

  bool areTypesCompatible(Type lhs, Type rhs) {
    return tf_type::HasCompatibleElementTypes(lhs, rhs);
  }

  RegionAttr getPreservedAttrs(unsigned index) {
    return getRegionAttrsAttr();
  }
  void setPreservedAttrs(unsigned index, RegionAttr attrs) {
    setRegionAttrsAttr(attrs);
  }
};
} // namespace tfg
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::tfg::ForRegionOp)

namespace mlir {
namespace tfg {

//===----------------------------------------------------------------------===//
// ::mlir::tfg::GetResultOp declarations
//===----------------------------------------------------------------------===//

namespace detail {
class GetResultOpGenericAdaptorBase {
public:
  struct Properties {
    using nameTy = ::mlir::StringAttr;
    nameTy name;

    auto getName() {
      auto &propStorage = this->name;
      return ::llvm::cast<::mlir::StringAttr>(propStorage);
    }
    void setName(const ::mlir::StringAttr &propValue) {
      this->name = propValue;
    }
    using numberTy = ::mlir::IntegerAttr;
    numberTy number;

    auto getNumber() {
      auto &propStorage = this->number;
      return ::llvm::cast<::mlir::IntegerAttr>(propStorage);
    }
    void setNumber(const ::mlir::IntegerAttr &propValue) {
      this->number = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.name == this->name &&
        rhs.number == this->number &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  GetResultOpGenericAdaptorBase(::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : odsAttrs(attrs), properties(properties), odsRegions(regions) {  if (odsAttrs)
      odsOpName.emplace("tfg.get_result", odsAttrs.getContext());
  }

  GetResultOpGenericAdaptorBase(GetResultOp op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize) {
    return {index, 1};
  }

  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes() {
    return odsAttrs;
  }

  ::mlir::StringAttr getNameAttr() {
    auto attr = ::llvm::cast<::mlir::StringAttr>(getProperties().name);
    return attr;
  }

  ::llvm::StringRef getName();
  ::mlir::IntegerAttr getNumberAttr() {
    auto attr = ::llvm::cast<::mlir::IntegerAttr>(getProperties().number);
    return attr;
  }

  uint32_t getNumber();
};
} // namespace detail
template <typename RangeT>
class GetResultOpGenericAdaptor : public detail::GetResultOpGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::GetResultOpGenericAdaptorBase;
public:
  GetResultOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  GetResultOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : GetResultOpGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  GetResultOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr) : GetResultOpGenericAdaptor(values, attrs, Properties{}, {}) {}

  GetResultOpGenericAdaptor(RangeT values, const GetResultOpGenericAdaptorBase &base) : Base(base), odsOperands(values) {}

  template <typename LateInst = GetResultOp, typename = std::enable_if_t<std::is_same_v<LateInst, GetResultOp>>>
  GetResultOpGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getValue() {
    return (*getODSOperands(0).begin());
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class GetResultOpAdaptor : public GetResultOpGenericAdaptor<::mlir::ValueRange> {
public:
  using GetResultOpGenericAdaptor::GetResultOpGenericAdaptor;
  GetResultOpAdaptor(GetResultOp op);

  ::llvm::LogicalResult verify(::mlir::Location loc);
};
class GetResultOp : public ::mlir::Op<GetResultOp, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::OneResult, ::mlir::OpTrait::OneTypedResult<::mlir::tf_type::OpaqueTensorType>::Impl, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::OneOperand, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait, ::mlir::ConditionallySpeculatable::Trait, ::mlir::OpTrait::AlwaysSpeculatableImplTrait, ::mlir::MemoryEffectOpInterface::Trait, ::mlir::OpTrait::IntrinsicOperation, ::mlir::InferTypeOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = GetResultOpAdaptor;
  template <typename RangeT>
  using GenericAdaptor = GetResultOpGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("name"), ::llvm::StringRef("number")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getNameAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getNameAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  ::mlir::StringAttr getNumberAttrName() {
    return getAttributeNameForIndex(1);
  }

  static ::mlir::StringAttr getNumberAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 1);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("tfg.get_result");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return {index, 1};
  }

  ::mlir::Operation::operand_range getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(getOperation()->operand_begin(), valueRange.first),
             std::next(getOperation()->operand_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::TypedValue<::mlir::tf_type::OpaqueTensorType> getValue() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::tf_type::OpaqueTensorType>>(*getODSOperands(0).begin());
  }

  ::mlir::OpOperand &getValueMutable() {
    auto range = getODSOperandIndexAndLength(0);
    return getOperation()->getOpOperand(range.first);
  }

  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index) {
    return {index, 1};
  }

  ::mlir::Operation::result_range getODSResults(unsigned index) {
    auto valueRange = getODSResultIndexAndLength(index);
    return {std::next(getOperation()->result_begin(), valueRange.first),
             std::next(getOperation()->result_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::TypedValue<::mlir::tf_type::OpaqueTensorType> getResult() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::tf_type::OpaqueTensorType>>(*getODSResults(0).begin());
  }

  static ::llvm::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::llvm::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::llvm::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::StringAttr getNameAttr() {
    return ::llvm::cast<::mlir::StringAttr>(getProperties().name);
  }

  ::llvm::StringRef getName();
  ::mlir::IntegerAttr getNumberAttr() {
    return ::llvm::cast<::mlir::IntegerAttr>(getProperties().number);
  }

  uint32_t getNumber();
  void setNameAttr(::mlir::StringAttr attr) {
    getProperties().name = attr;
  }

  void setName(::llvm::StringRef attrValue);
  void setNumberAttr(::mlir::IntegerAttr attr) {
    getProperties().number = attr;
  }

  void setNumber(uint32_t attrValue);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type result, ::mlir::Value value, ::mlir::StringAttr name, ::mlir::IntegerAttr number);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Value value, ::mlir::StringAttr name, ::mlir::IntegerAttr number);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value value, ::mlir::StringAttr name, ::mlir::IntegerAttr number);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type result, ::mlir::Value value, ::llvm::StringRef name, uint32_t number);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Value value, ::llvm::StringRef name, uint32_t number);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value value, ::llvm::StringRef name, uint32_t number);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::llvm::LogicalResult verifyInvariantsImpl();
  ::llvm::LogicalResult verifyInvariants();
  static ::llvm::LogicalResult inferReturnTypes(::mlir::MLIRContext *context, ::std::optional<::mlir::Location> location, ::mlir::ValueRange operands, ::mlir::DictionaryAttr attributes, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions, ::llvm::SmallVectorImpl<::mlir::Type>&inferredReturnTypes);
  static ::mlir::ParseResult parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result);
  void print(::mlir::OpAsmPrinter &_odsPrinter);
  void getEffects(::llvm::SmallVectorImpl<::mlir::SideEffects::EffectInstance<::mlir::MemoryEffects::Effect>> &effects);
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 2 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace tfg
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::tfg::GetResultOp)

namespace mlir {
namespace tfg {

//===----------------------------------------------------------------------===//
// ::mlir::tfg::GraphFuncOp declarations
//===----------------------------------------------------------------------===//

namespace detail {
class GraphFuncOpGenericAdaptorBase {
public:
  struct Properties {
    using arg_attrsTy = ::mlir::ArrayAttr;
    arg_attrsTy arg_attrs;

    auto getArgAttrs() {
      auto &propStorage = this->arg_attrs;
      return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(propStorage);
    }
    void setArgAttrs(const ::mlir::ArrayAttr &propValue) {
      this->arg_attrs = propValue;
    }
    using control_outputTy = ::mlir::ArrayAttr;
    control_outputTy control_output;

    auto getControlOutput() {
      auto &propStorage = this->control_output;
      return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(propStorage);
    }
    void setControlOutput(const ::mlir::ArrayAttr &propValue) {
      this->control_output = propValue;
    }
    using descriptionTy = ::mlir::StringAttr;
    descriptionTy description;

    auto getDescription() {
      auto &propStorage = this->description;
      return ::llvm::dyn_cast_or_null<::mlir::StringAttr>(propStorage);
    }
    void setDescription(const ::mlir::StringAttr &propValue) {
      this->description = propValue;
    }
    using function_typeTy = ::mlir::TypeAttr;
    function_typeTy function_type;

    auto getFunctionType() {
      auto &propStorage = this->function_type;
      return ::llvm::cast<::mlir::TypeAttr>(propStorage);
    }
    void setFunctionType(const ::mlir::TypeAttr &propValue) {
      this->function_type = propValue;
    }
    using genericTy = ::mlir::UnitAttr;
    genericTy generic;

    auto getGeneric() {
      auto &propStorage = this->generic;
      return ::llvm::dyn_cast_or_null<::mlir::UnitAttr>(propStorage);
    }
    void setGeneric(const ::mlir::UnitAttr &propValue) {
      this->generic = propValue;
    }
    using gradientTy = ::mlir::FlatSymbolRefAttr;
    gradientTy gradient;

    auto getGradient() {
      auto &propStorage = this->gradient;
      return ::llvm::dyn_cast_or_null<::mlir::FlatSymbolRefAttr>(propStorage);
    }
    void setGradient(const ::mlir::FlatSymbolRefAttr &propValue) {
      this->gradient = propValue;
    }
    using is_statefulTy = ::mlir::UnitAttr;
    is_statefulTy is_stateful;

    auto getIsStateful() {
      auto &propStorage = this->is_stateful;
      return ::llvm::dyn_cast_or_null<::mlir::UnitAttr>(propStorage);
    }
    void setIsStateful(const ::mlir::UnitAttr &propValue) {
      this->is_stateful = propValue;
    }
    using res_attrsTy = ::mlir::ArrayAttr;
    res_attrsTy res_attrs;

    auto getResAttrs() {
      auto &propStorage = this->res_attrs;
      return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(propStorage);
    }
    void setResAttrs(const ::mlir::ArrayAttr &propValue) {
      this->res_attrs = propValue;
    }
    using resource_arg_unique_ids_keysTy = ::mlir::DenseIntElementsAttr;
    resource_arg_unique_ids_keysTy resource_arg_unique_ids_keys;

    auto getResourceArgUniqueIdsKeys() {
      auto &propStorage = this->resource_arg_unique_ids_keys;
      return ::llvm::dyn_cast_or_null<::mlir::DenseIntElementsAttr>(propStorage);
    }
    void setResourceArgUniqueIdsKeys(const ::mlir::DenseIntElementsAttr &propValue) {
      this->resource_arg_unique_ids_keys = propValue;
    }
    using resource_arg_unique_ids_valuesTy = ::mlir::DenseIntElementsAttr;
    resource_arg_unique_ids_valuesTy resource_arg_unique_ids_values;

    auto getResourceArgUniqueIdsValues() {
      auto &propStorage = this->resource_arg_unique_ids_values;
      return ::llvm::dyn_cast_or_null<::mlir::DenseIntElementsAttr>(propStorage);
    }
    void setResourceArgUniqueIdsValues(const ::mlir::DenseIntElementsAttr &propValue) {
      this->resource_arg_unique_ids_values = propValue;
    }
    using sym_nameTy = ::mlir::StringAttr;
    sym_nameTy sym_name;

    auto getSymName() {
      auto &propStorage = this->sym_name;
      return ::llvm::cast<::mlir::StringAttr>(propStorage);
    }
    void setSymName(const ::mlir::StringAttr &propValue) {
      this->sym_name = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.arg_attrs == this->arg_attrs &&
        rhs.control_output == this->control_output &&
        rhs.description == this->description &&
        rhs.function_type == this->function_type &&
        rhs.generic == this->generic &&
        rhs.gradient == this->gradient &&
        rhs.is_stateful == this->is_stateful &&
        rhs.res_attrs == this->res_attrs &&
        rhs.resource_arg_unique_ids_keys == this->resource_arg_unique_ids_keys &&
        rhs.resource_arg_unique_ids_values == this->resource_arg_unique_ids_values &&
        rhs.sym_name == this->sym_name &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  GraphFuncOpGenericAdaptorBase(::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : odsAttrs(attrs), properties(properties), odsRegions(regions) {  if (odsAttrs)
      odsOpName.emplace("tfg.func", odsAttrs.getContext());
  }

  GraphFuncOpGenericAdaptorBase(GraphFuncOp op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize) {
    return {index, 1};
  }

  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes() {
    return odsAttrs;
  }

  ::mlir::UnitAttr getGenericAttr();
  bool getGeneric();
  ::mlir::StringAttr getSymNameAttr() {
    auto attr = ::llvm::cast<::mlir::StringAttr>(getProperties().sym_name);
    return attr;
  }

  ::llvm::StringRef getSymName();
  ::mlir::TypeAttr getFunctionTypeAttr() {
    auto attr = ::llvm::cast<::mlir::TypeAttr>(getProperties().function_type);
    return attr;
  }

  ::mlir::FunctionType getFunctionType();
  ::mlir::ArrayAttr getArgAttrsAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().arg_attrs);
    return attr;
  }

  ::std::optional< ::mlir::ArrayAttr > getArgAttrs();
  ::mlir::ArrayAttr getResAttrsAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().res_attrs);
    return attr;
  }

  ::std::optional< ::mlir::ArrayAttr > getResAttrs();
  ::mlir::StringAttr getDescriptionAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::StringAttr>(getProperties().description);
    return attr;
  }

  ::std::optional< ::llvm::StringRef > getDescription();
  ::mlir::UnitAttr getIsStatefulAttr();
  bool getIsStateful();
  ::mlir::FlatSymbolRefAttr getGradientAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::FlatSymbolRefAttr>(getProperties().gradient);
    return attr;
  }

  ::std::optional< ::llvm::StringRef > getGradient();
  ::mlir::DenseIntElementsAttr getResourceArgUniqueIdsKeysAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::DenseIntElementsAttr>(getProperties().resource_arg_unique_ids_keys);
    return attr;
  }

  ::std::optional< ::mlir::DenseIntElementsAttr > getResourceArgUniqueIdsKeys();
  ::mlir::DenseIntElementsAttr getResourceArgUniqueIdsValuesAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::DenseIntElementsAttr>(getProperties().resource_arg_unique_ids_values);
    return attr;
  }

  ::std::optional< ::mlir::DenseIntElementsAttr > getResourceArgUniqueIdsValues();
  ::mlir::ArrayAttr getControlOutputAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().control_output);
    return attr;
  }

  ::std::optional< ::mlir::ArrayAttr > getControlOutput();
  ::mlir::Region &getBody() {
    return *odsRegions[0];
  }

  ::mlir::RegionRange getRegions() {
    return odsRegions;
  }

};
} // namespace detail
template <typename RangeT>
class GraphFuncOpGenericAdaptor : public detail::GraphFuncOpGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::GraphFuncOpGenericAdaptorBase;
public:
  GraphFuncOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  GraphFuncOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : GraphFuncOpGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  GraphFuncOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr) : GraphFuncOpGenericAdaptor(values, attrs, Properties{}, {}) {}

  GraphFuncOpGenericAdaptor(RangeT values, const GraphFuncOpGenericAdaptorBase &base) : Base(base), odsOperands(values) {}

  template <typename LateInst = GraphFuncOp, typename = std::enable_if_t<std::is_same_v<LateInst, GraphFuncOp>>>
  GraphFuncOpGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class GraphFuncOpAdaptor : public GraphFuncOpGenericAdaptor<::mlir::ValueRange> {
public:
  using GraphFuncOpGenericAdaptor::GraphFuncOpGenericAdaptor;
  GraphFuncOpAdaptor(GraphFuncOp op);

  ::llvm::LogicalResult verify(::mlir::Location loc);
};
class GraphFuncOp : public ::mlir::Op<GraphFuncOp, ::mlir::OpTrait::OneRegion, ::mlir::OpTrait::ZeroResults, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::ZeroOperands, ::mlir::OpTrait::SingleBlock, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait, ::mlir::RegionKindInterface::Trait, ::mlir::OpTrait::HasOnlyGraphRegion, ::mlir::SymbolOpInterface::Trait, ::mlir::CallableOpInterface::Trait, ::mlir::FunctionOpInterface::Trait, ::mlir::OpTrait::IsIsolatedFromAbove, ::mlir::OpAsmOpInterface::Trait, ::mlir::tfg::ControlArgumentInterface::Trait, ::mlir::OpTrait::IntrinsicOperation> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = GraphFuncOpAdaptor;
  template <typename RangeT>
  using GenericAdaptor = GraphFuncOpGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("arg_attrs"), ::llvm::StringRef("control_output"), ::llvm::StringRef("description"), ::llvm::StringRef("function_type"), ::llvm::StringRef("generic"), ::llvm::StringRef("gradient"), ::llvm::StringRef("is_stateful"), ::llvm::StringRef("res_attrs"), ::llvm::StringRef("resource_arg_unique_ids_keys"), ::llvm::StringRef("resource_arg_unique_ids_values"), ::llvm::StringRef("sym_name")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getArgAttrsAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getArgAttrsAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  ::mlir::StringAttr getControlOutputAttrName() {
    return getAttributeNameForIndex(1);
  }

  static ::mlir::StringAttr getControlOutputAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 1);
  }

  ::mlir::StringAttr getDescriptionAttrName() {
    return getAttributeNameForIndex(2);
  }

  static ::mlir::StringAttr getDescriptionAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 2);
  }

  ::mlir::StringAttr getFunctionTypeAttrName() {
    return getAttributeNameForIndex(3);
  }

  static ::mlir::StringAttr getFunctionTypeAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 3);
  }

  ::mlir::StringAttr getGenericAttrName() {
    return getAttributeNameForIndex(4);
  }

  static ::mlir::StringAttr getGenericAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 4);
  }

  ::mlir::StringAttr getGradientAttrName() {
    return getAttributeNameForIndex(5);
  }

  static ::mlir::StringAttr getGradientAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 5);
  }

  ::mlir::StringAttr getIsStatefulAttrName() {
    return getAttributeNameForIndex(6);
  }

  static ::mlir::StringAttr getIsStatefulAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 6);
  }

  ::mlir::StringAttr getResAttrsAttrName() {
    return getAttributeNameForIndex(7);
  }

  static ::mlir::StringAttr getResAttrsAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 7);
  }

  ::mlir::StringAttr getResourceArgUniqueIdsKeysAttrName() {
    return getAttributeNameForIndex(8);
  }

  static ::mlir::StringAttr getResourceArgUniqueIdsKeysAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 8);
  }

  ::mlir::StringAttr getResourceArgUniqueIdsValuesAttrName() {
    return getAttributeNameForIndex(9);
  }

  static ::mlir::StringAttr getResourceArgUniqueIdsValuesAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 9);
  }

  ::mlir::StringAttr getSymNameAttrName() {
    return getAttributeNameForIndex(10);
  }

  static ::mlir::StringAttr getSymNameAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 10);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("tfg.func");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return {index, 1};
  }

  ::mlir::Operation::operand_range getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(getOperation()->operand_begin(), valueRange.first),
             std::next(getOperation()->operand_begin(), valueRange.first + valueRange.second)};
  }

  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index) {
    return {index, 1};
  }

  ::mlir::Operation::result_range getODSResults(unsigned index) {
    auto valueRange = getODSResultIndexAndLength(index);
    return {std::next(getOperation()->result_begin(), valueRange.first),
             std::next(getOperation()->result_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::Region &getBody() {
    return (*this)->getRegion(0);
  }

  static ::llvm::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::llvm::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::llvm::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::UnitAttr getGenericAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::UnitAttr>(getProperties().generic);
  }

  bool getGeneric();
  ::mlir::StringAttr getSymNameAttr() {
    return ::llvm::cast<::mlir::StringAttr>(getProperties().sym_name);
  }

  ::llvm::StringRef getSymName();
  ::mlir::TypeAttr getFunctionTypeAttr() {
    return ::llvm::cast<::mlir::TypeAttr>(getProperties().function_type);
  }

  ::mlir::FunctionType getFunctionType();
  ::mlir::ArrayAttr getArgAttrsAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().arg_attrs);
  }

  ::std::optional< ::mlir::ArrayAttr > getArgAttrs();
  ::mlir::ArrayAttr getResAttrsAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().res_attrs);
  }

  ::std::optional< ::mlir::ArrayAttr > getResAttrs();
  ::mlir::StringAttr getDescriptionAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::StringAttr>(getProperties().description);
  }

  ::std::optional< ::llvm::StringRef > getDescription();
  ::mlir::UnitAttr getIsStatefulAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::UnitAttr>(getProperties().is_stateful);
  }

  bool getIsStateful();
  ::mlir::FlatSymbolRefAttr getGradientAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::FlatSymbolRefAttr>(getProperties().gradient);
  }

  ::std::optional< ::llvm::StringRef > getGradient();
  ::mlir::DenseIntElementsAttr getResourceArgUniqueIdsKeysAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::DenseIntElementsAttr>(getProperties().resource_arg_unique_ids_keys);
  }

  ::std::optional< ::mlir::DenseIntElementsAttr > getResourceArgUniqueIdsKeys();
  ::mlir::DenseIntElementsAttr getResourceArgUniqueIdsValuesAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::DenseIntElementsAttr>(getProperties().resource_arg_unique_ids_values);
  }

  ::std::optional< ::mlir::DenseIntElementsAttr > getResourceArgUniqueIdsValues();
  ::mlir::ArrayAttr getControlOutputAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().control_output);
  }

  ::std::optional< ::mlir::ArrayAttr > getControlOutput();
  void setGenericAttr(::mlir::UnitAttr attr) {
    getProperties().generic = attr;
  }

  void setGeneric(bool attrValue);
  void setSymNameAttr(::mlir::StringAttr attr) {
    getProperties().sym_name = attr;
  }

  void setSymName(::llvm::StringRef attrValue);
  void setFunctionTypeAttr(::mlir::TypeAttr attr) {
    getProperties().function_type = attr;
  }

  void setFunctionType(::mlir::FunctionType attrValue);
  void setArgAttrsAttr(::mlir::ArrayAttr attr) {
    getProperties().arg_attrs = attr;
  }

  void setResAttrsAttr(::mlir::ArrayAttr attr) {
    getProperties().res_attrs = attr;
  }

  void setDescriptionAttr(::mlir::StringAttr attr) {
    getProperties().description = attr;
  }

  void setDescription(::std::optional<::llvm::StringRef> attrValue);
  void setIsStatefulAttr(::mlir::UnitAttr attr) {
    getProperties().is_stateful = attr;
  }

  void setIsStateful(bool attrValue);
  void setGradientAttr(::mlir::FlatSymbolRefAttr attr) {
    getProperties().gradient = attr;
  }

  void setGradient(::std::optional<::llvm::StringRef> attrValue);
  void setResourceArgUniqueIdsKeysAttr(::mlir::DenseIntElementsAttr attr) {
    getProperties().resource_arg_unique_ids_keys = attr;
  }

  void setResourceArgUniqueIdsValuesAttr(::mlir::DenseIntElementsAttr attr) {
    getProperties().resource_arg_unique_ids_values = attr;
  }

  void setControlOutputAttr(::mlir::ArrayAttr attr) {
    getProperties().control_output = attr;
  }

  ::mlir::Attribute removeGenericAttr() {
      auto &attr = getProperties().generic;
      attr = {};
      return attr;
  }

  ::mlir::Attribute removeArgAttrsAttr() {
      auto &attr = getProperties().arg_attrs;
      attr = {};
      return attr;
  }

  ::mlir::Attribute removeResAttrsAttr() {
      auto &attr = getProperties().res_attrs;
      attr = {};
      return attr;
  }

  ::mlir::Attribute removeDescriptionAttr() {
      auto &attr = getProperties().description;
      attr = {};
      return attr;
  }

  ::mlir::Attribute removeIsStatefulAttr() {
      auto &attr = getProperties().is_stateful;
      attr = {};
      return attr;
  }

  ::mlir::Attribute removeGradientAttr() {
      auto &attr = getProperties().gradient;
      attr = {};
      return attr;
  }

  ::mlir::Attribute removeResourceArgUniqueIdsKeysAttr() {
      auto &attr = getProperties().resource_arg_unique_ids_keys;
      attr = {};
      return attr;
  }

  ::mlir::Attribute removeResourceArgUniqueIdsValuesAttr() {
      auto &attr = getProperties().resource_arg_unique_ids_values;
      attr = {};
      return attr;
  }

  ::mlir::Attribute removeControlOutputAttr() {
      auto &attr = getProperties().control_output;
      attr = {};
      return attr;
  }

  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, const Twine &sym_name, FunctionType type, bool generic);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, /*optional*/::mlir::UnitAttr generic, ::mlir::StringAttr sym_name, ::mlir::TypeAttr function_type, /*optional*/::mlir::ArrayAttr arg_attrs, /*optional*/::mlir::ArrayAttr res_attrs, /*optional*/::mlir::StringAttr description, /*optional*/::mlir::UnitAttr is_stateful, /*optional*/::mlir::FlatSymbolRefAttr gradient, /*optional*/::mlir::DenseIntElementsAttr resource_arg_unique_ids_keys, /*optional*/::mlir::DenseIntElementsAttr resource_arg_unique_ids_values, /*optional*/::mlir::ArrayAttr control_output);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, /*optional*/::mlir::UnitAttr generic, ::mlir::StringAttr sym_name, ::mlir::TypeAttr function_type, /*optional*/::mlir::ArrayAttr arg_attrs, /*optional*/::mlir::ArrayAttr res_attrs, /*optional*/::mlir::StringAttr description, /*optional*/::mlir::UnitAttr is_stateful, /*optional*/::mlir::FlatSymbolRefAttr gradient, /*optional*/::mlir::DenseIntElementsAttr resource_arg_unique_ids_keys, /*optional*/::mlir::DenseIntElementsAttr resource_arg_unique_ids_values, /*optional*/::mlir::ArrayAttr control_output);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, /*optional*/bool generic, ::llvm::StringRef sym_name, ::mlir::FunctionType function_type, /*optional*/::mlir::ArrayAttr arg_attrs, /*optional*/::mlir::ArrayAttr res_attrs, /*optional*/::mlir::StringAttr description, /*optional*/bool is_stateful, /*optional*/::mlir::FlatSymbolRefAttr gradient, /*optional*/::mlir::DenseIntElementsAttr resource_arg_unique_ids_keys, /*optional*/::mlir::DenseIntElementsAttr resource_arg_unique_ids_values, /*optional*/::mlir::ArrayAttr control_output);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, /*optional*/bool generic, ::llvm::StringRef sym_name, ::mlir::FunctionType function_type, /*optional*/::mlir::ArrayAttr arg_attrs, /*optional*/::mlir::ArrayAttr res_attrs, /*optional*/::mlir::StringAttr description, /*optional*/bool is_stateful, /*optional*/::mlir::FlatSymbolRefAttr gradient, /*optional*/::mlir::DenseIntElementsAttr resource_arg_unique_ids_keys, /*optional*/::mlir::DenseIntElementsAttr resource_arg_unique_ids_values, /*optional*/::mlir::ArrayAttr control_output);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  static ::mlir::ParseResult parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result);
  void print(::mlir::OpAsmPrinter &p);
  ::llvm::LogicalResult verifyInvariantsImpl();
  ::llvm::LogicalResult verifyInvariants();
  ::llvm::LogicalResult verify();
  static ::llvm::LogicalResult canonicalize(GraphFuncOp op, ::mlir::PatternRewriter &rewriter);
  static void getCanonicalizationPatterns(::mlir::RewritePatternSet &results, ::mlir::MLIRContext *context);
  static mlir::BlockArgument getDataValueOf(BlockArgument ctl);
  static mlir::BlockArgument getControlTokenOf(BlockArgument data);
  static mlir::BlockArgument getDataValue(Region &region, unsigned idx);
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 11 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
  TFGraphDialect *getDialect() {
    return cast<TFGraphDialect>((*this)->getDialect());
  }

  // Returns true if this function is marked for JIT compilation.
  bool isMarkedForCompilation();

  // Return the function called by this operation if this operation is a call
  // and the function exists in the symbol_table, otherwise return null.
  static GraphFuncOp getCalledFunction(Operation *op,
                                       SymbolTable &symbol_table);

  //===------------------------------------------------------------------===//
  // OpAsmOpInterface
  //===------------------------------------------------------------------===//

  // This will filter the `tfg.` prefix in front of operations inside the
  // func body.
  static StringRef getDefaultDialect() {
    return "tfg";
  }

  /// Get a special name to use when printing block arguments.
  void getAsmBlockArgumentNames(Region &region, OpAsmSetValueNameFn set_name_fn);

  //===------------------------------------------------------------------===//
  // CallableOpInterface
  //===------------------------------------------------------------------===//

  // Returns the region on the current operation that is callable. This may
  // return null in the case of an external callable object, e.g. an external
  // function.
  Region *getCallableRegion() {
    return isExternal() ? nullptr : &this->getRegion();
  }

  /// Returns the argument types of this function.
  ArrayRef<Type> getArgumentTypes() { return getFunctionType().getInputs(); }

  /// Returns the result types of this function.
  ArrayRef<Type> getResultTypes() { return getFunctionType().getResults(); }

  //===------------------------------------------------------------------===//
  // FunctionOpInterface Methods
  //===------------------------------------------------------------------===//

  LogicalResult verifyType();
  LogicalResult verifyBody();

  //===------------------------------------------------------------------===//
  // SymbolOpInterface Methods
  //===------------------------------------------------------------------===//

  bool isDeclaration() { return isExternal(); }
};
} // namespace tfg
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::tfg::GraphFuncOp)

namespace mlir {
namespace tfg {

//===----------------------------------------------------------------------===//
// ::mlir::tfg::GraphOp declarations
//===----------------------------------------------------------------------===//

namespace detail {
class GraphOpGenericAdaptorBase {
public:
  struct Properties {
    using versionTy = ::mlir::tf_type::VersionAttr;
    versionTy version;

    auto getVersion() {
      auto &propStorage = this->version;
      return ::llvm::cast<::mlir::tf_type::VersionAttr>(propStorage);
    }
    void setVersion(const ::mlir::tf_type::VersionAttr &propValue) {
      this->version = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.version == this->version &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  GraphOpGenericAdaptorBase(::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : odsAttrs(attrs), properties(properties), odsRegions(regions) {  if (odsAttrs)
      odsOpName.emplace("tfg.graph", odsAttrs.getContext());
  }

  GraphOpGenericAdaptorBase(GraphOp op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize) {
    return {index, 1};
  }

  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes() {
    return odsAttrs;
  }

  ::mlir::tf_type::VersionAttr getVersionAttr() {
    auto attr = ::llvm::cast<::mlir::tf_type::VersionAttr>(getProperties().version);
    return attr;
  }

  ::mlir::tf_type::VersionAttr getVersion();
  ::mlir::Region &getNodes() {
    return *odsRegions[0];
  }

  ::mlir::RegionRange getRegions() {
    return odsRegions;
  }

};
} // namespace detail
template <typename RangeT>
class GraphOpGenericAdaptor : public detail::GraphOpGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::GraphOpGenericAdaptorBase;
public:
  GraphOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  GraphOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : GraphOpGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  GraphOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr) : GraphOpGenericAdaptor(values, attrs, Properties{}, {}) {}

  GraphOpGenericAdaptor(RangeT values, const GraphOpGenericAdaptorBase &base) : Base(base), odsOperands(values) {}

  template <typename LateInst = GraphOp, typename = std::enable_if_t<std::is_same_v<LateInst, GraphOp>>>
  GraphOpGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class GraphOpAdaptor : public GraphOpGenericAdaptor<::mlir::ValueRange> {
public:
  using GraphOpGenericAdaptor::GraphOpGenericAdaptor;
  GraphOpAdaptor(GraphOp op);

  ::llvm::LogicalResult verify(::mlir::Location loc);
};
class GraphOp : public ::mlir::Op<GraphOp, ::mlir::OpTrait::OneRegion, ::mlir::OpTrait::ZeroResults, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::ZeroOperands, ::mlir::OpTrait::SingleBlock, ::mlir::OpTrait::NoTerminator, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait, ::mlir::OpTrait::HasOnlyGraphRegion, ::mlir::OpTrait::IsIsolatedFromAbove, ::mlir::OpAsmOpInterface::Trait, ::mlir::RegionKindInterface::Trait, ::mlir::OpTrait::IntrinsicOperation> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = GraphOpAdaptor;
  template <typename RangeT>
  using GenericAdaptor = GraphOpGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("version")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getVersionAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getVersionAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("tfg.graph");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return {index, 1};
  }

  ::mlir::Operation::operand_range getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(getOperation()->operand_begin(), valueRange.first),
             std::next(getOperation()->operand_begin(), valueRange.first + valueRange.second)};
  }

  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index) {
    return {index, 1};
  }

  ::mlir::Operation::result_range getODSResults(unsigned index) {
    auto valueRange = getODSResultIndexAndLength(index);
    return {std::next(getOperation()->result_begin(), valueRange.first),
             std::next(getOperation()->result_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::Region &getNodes() {
    return (*this)->getRegion(0);
  }

  static ::llvm::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::llvm::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::llvm::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::tf_type::VersionAttr getVersionAttr() {
    return ::llvm::cast<::mlir::tf_type::VersionAttr>(getProperties().version);
  }

  ::mlir::tf_type::VersionAttr getVersion();
  void setVersionAttr(::mlir::tf_type::VersionAttr attr) {
    getProperties().version = attr;
  }

  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::tf_type::VersionAttr version);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::tf_type::VersionAttr version);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::llvm::LogicalResult verifyInvariantsImpl();
  ::llvm::LogicalResult verifyInvariants();
  ::llvm::LogicalResult verify();
  static ::mlir::ParseResult parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result);
  void print(::mlir::OpAsmPrinter &_odsPrinter);
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 1 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
  TFGraphDialect *getDialect() {
    return cast<TFGraphDialect>((*this)->getDialect());
  }

  // Override the getOps() inherited from the OneRegion trait to return a
  // range of TFOp instead of the generic mlir::Operation.
  auto getOps() { return getRegion().template getOps<TFOp>(); }

  //===------------------------------------------------------------------===//
  // OpAsmOpInterface
  //===------------------------------------------------------------------===//

  // This will filter the `tfg.` prefix in front of operations inside the
  // graph body.
  static StringRef getDefaultDialect() {
    return "tfg";
  }
};
} // namespace tfg
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::tfg::GraphOp)

namespace mlir {
namespace tfg {

//===----------------------------------------------------------------------===//
// ::mlir::tfg::IfOp declarations
//===----------------------------------------------------------------------===//

namespace detail {
class IfOpGenericAdaptorBase {
public:
  struct Properties {
    using TcondTy = ::mlir::TypeAttr;
    TcondTy Tcond;

    auto getTcond() {
      auto &propStorage = this->Tcond;
      return ::llvm::dyn_cast_or_null<::mlir::TypeAttr>(propStorage);
    }
    void setTcond(const ::mlir::TypeAttr &propValue) {
      this->Tcond = propValue;
    }
    using TinTy = ::mlir::ArrayAttr;
    TinTy Tin;

    auto getTin() {
      auto &propStorage = this->Tin;
      return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(propStorage);
    }
    void setTin(const ::mlir::ArrayAttr &propValue) {
      this->Tin = propValue;
    }
    using ToutTy = ::mlir::ArrayAttr;
    ToutTy Tout;

    auto getTout() {
      auto &propStorage = this->Tout;
      return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(propStorage);
    }
    void setTout(const ::mlir::ArrayAttr &propValue) {
      this->Tout = propValue;
    }
    using else_branchTy = ::mlir::tf_type::FuncAttr;
    else_branchTy else_branch;

    auto getElseBranch() {
      auto &propStorage = this->else_branch;
      return ::llvm::cast<::mlir::tf_type::FuncAttr>(propStorage);
    }
    void setElseBranch(const ::mlir::tf_type::FuncAttr &propValue) {
      this->else_branch = propValue;
    }
    using output_shapesTy = ::mlir::ArrayAttr;
    output_shapesTy output_shapes;

    auto getOutputShapes() {
      auto &propStorage = this->output_shapes;
      return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(propStorage);
    }
    void setOutputShapes(const ::mlir::ArrayAttr &propValue) {
      this->output_shapes = propValue;
    }
    using then_branchTy = ::mlir::tf_type::FuncAttr;
    then_branchTy then_branch;

    auto getThenBranch() {
      auto &propStorage = this->then_branch;
      return ::llvm::cast<::mlir::tf_type::FuncAttr>(propStorage);
    }
    void setThenBranch(const ::mlir::tf_type::FuncAttr &propValue) {
      this->then_branch = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.Tcond == this->Tcond &&
        rhs.Tin == this->Tin &&
        rhs.Tout == this->Tout &&
        rhs.else_branch == this->else_branch &&
        rhs.output_shapes == this->output_shapes &&
        rhs.then_branch == this->then_branch &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  IfOpGenericAdaptorBase(::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : odsAttrs(attrs), properties(properties), odsRegions(regions) {  if (odsAttrs)
      odsOpName.emplace("tfg.If", odsAttrs.getContext());
  }

  IfOpGenericAdaptorBase(IfOp op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes() {
    return odsAttrs;
  }

  ::mlir::tf_type::FuncAttr getThenBranchAttr() {
    auto attr = ::llvm::cast<::mlir::tf_type::FuncAttr>(getProperties().then_branch);
    return attr;
  }

  ::mlir::tf_type::FuncAttr getThenBranch();
  ::mlir::tf_type::FuncAttr getElseBranchAttr() {
    auto attr = ::llvm::cast<::mlir::tf_type::FuncAttr>(getProperties().else_branch);
    return attr;
  }

  ::mlir::tf_type::FuncAttr getElseBranch();
  ::mlir::TypeAttr getTcondAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::TypeAttr>(getProperties().Tcond);
    return attr;
  }

  ::std::optional<::mlir::Type> getTcond();
  ::mlir::ArrayAttr getTinAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().Tin);
    return attr;
  }

  ::std::optional< ::mlir::ArrayAttr > getTin();
  ::mlir::ArrayAttr getToutAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().Tout);
    return attr;
  }

  ::std::optional< ::mlir::ArrayAttr > getTout();
  ::mlir::ArrayAttr getOutputShapesAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().output_shapes);
    return attr;
  }

  ::std::optional< ::mlir::ArrayAttr > getOutputShapes();
};
} // namespace detail
template <typename RangeT>
class IfOpGenericAdaptor : public detail::IfOpGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::IfOpGenericAdaptorBase;
public:
  IfOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  IfOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : IfOpGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  IfOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr) : IfOpGenericAdaptor(values, attrs, Properties{}, {}) {}

  IfOpGenericAdaptor(RangeT values, const IfOpGenericAdaptorBase &base) : Base(base), odsOperands(values) {}

  template <typename LateInst = IfOp, typename = std::enable_if_t<std::is_same_v<LateInst, IfOp>>>
  IfOpGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getCond() {
    return (*getODSOperands(0).begin());
  }

  RangeT getArgs() {
    return getODSOperands(1);
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class IfOpAdaptor : public IfOpGenericAdaptor<::mlir::ValueRange> {
public:
  using IfOpGenericAdaptor::IfOpGenericAdaptor;
  IfOpAdaptor(IfOp op);

  ::llvm::LogicalResult verify(::mlir::Location loc);
};
class IfOp : public ::mlir::Op<IfOp, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::AtLeastNResults<1>::Impl, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::AtLeastNOperands<1>::Impl, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait, ::mlir::SymbolUserOpInterface::Trait, ::mlir::OpAsmOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = IfOpAdaptor;
  template <typename RangeT>
  using GenericAdaptor = IfOpGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("Tcond"), ::llvm::StringRef("Tin"), ::llvm::StringRef("Tout"), ::llvm::StringRef("else_branch"), ::llvm::StringRef("output_shapes"), ::llvm::StringRef("then_branch")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getTcondAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getTcondAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  ::mlir::StringAttr getTinAttrName() {
    return getAttributeNameForIndex(1);
  }

  static ::mlir::StringAttr getTinAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 1);
  }

  ::mlir::StringAttr getToutAttrName() {
    return getAttributeNameForIndex(2);
  }

  static ::mlir::StringAttr getToutAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 2);
  }

  ::mlir::StringAttr getElseBranchAttrName() {
    return getAttributeNameForIndex(3);
  }

  static ::mlir::StringAttr getElseBranchAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 3);
  }

  ::mlir::StringAttr getOutputShapesAttrName() {
    return getAttributeNameForIndex(4);
  }

  static ::mlir::StringAttr getOutputShapesAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 4);
  }

  ::mlir::StringAttr getThenBranchAttrName() {
    return getAttributeNameForIndex(5);
  }

  static ::mlir::StringAttr getThenBranchAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 5);
  }

  void getAsmResultNames(::mlir::OpAsmSetValueNameFn setNameFn);
  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("tfg.If");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(getOperation()->operand_begin(), valueRange.first),
             std::next(getOperation()->operand_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::TypedValue<::mlir::Type> getCond() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::Type>>(*getODSOperands(0).begin());
  }

  ::mlir::Operation::operand_range getArgs() {
    return getODSOperands(1);
  }

  ::mlir::OpOperand &getCondMutable() {
    auto range = getODSOperandIndexAndLength(0);
    return getOperation()->getOpOperand(range.first);
  }

  ::mlir::MutableOperandRange getArgsMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index) {
    auto valueRange = getODSResultIndexAndLength(index);
    return {std::next(getOperation()->result_begin(), valueRange.first),
             std::next(getOperation()->result_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::Operation::result_range getOuts() {
    return getODSResults(0);
  }

  ::mlir::TypedValue<::mlir::tf_type::ControlType> getCtl() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::tf_type::ControlType>>(*getODSResults(1).begin());
  }

  static ::llvm::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::llvm::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::llvm::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::tf_type::FuncAttr getThenBranchAttr() {
    return ::llvm::cast<::mlir::tf_type::FuncAttr>(getProperties().then_branch);
  }

  ::mlir::tf_type::FuncAttr getThenBranch();
  ::mlir::tf_type::FuncAttr getElseBranchAttr() {
    return ::llvm::cast<::mlir::tf_type::FuncAttr>(getProperties().else_branch);
  }

  ::mlir::tf_type::FuncAttr getElseBranch();
  ::mlir::TypeAttr getTcondAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::TypeAttr>(getProperties().Tcond);
  }

  ::std::optional<::mlir::Type> getTcond();
  ::mlir::ArrayAttr getTinAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().Tin);
  }

  ::std::optional< ::mlir::ArrayAttr > getTin();
  ::mlir::ArrayAttr getToutAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().Tout);
  }

  ::std::optional< ::mlir::ArrayAttr > getTout();
  ::mlir::ArrayAttr getOutputShapesAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().output_shapes);
  }

  ::std::optional< ::mlir::ArrayAttr > getOutputShapes();
  void setThenBranchAttr(::mlir::tf_type::FuncAttr attr) {
    getProperties().then_branch = attr;
  }

  void setElseBranchAttr(::mlir::tf_type::FuncAttr attr) {
    getProperties().else_branch = attr;
  }

  void setTcondAttr(::mlir::TypeAttr attr) {
    getProperties().Tcond = attr;
  }

  void setTcond(::std::optional<::mlir::Type> attrValue);
  void setTinAttr(::mlir::ArrayAttr attr) {
    getProperties().Tin = attr;
  }

  void setToutAttr(::mlir::ArrayAttr attr) {
    getProperties().Tout = attr;
  }

  void setOutputShapesAttr(::mlir::ArrayAttr attr) {
    getProperties().output_shapes = attr;
  }

  ::mlir::Attribute removeTcondAttr() {
      auto &attr = getProperties().Tcond;
      attr = {};
      return attr;
  }

  ::mlir::Attribute removeTinAttr() {
      auto &attr = getProperties().Tin;
      attr = {};
      return attr;
  }

  ::mlir::Attribute removeToutAttr() {
      auto &attr = getProperties().Tout;
      attr = {};
      return attr;
  }

  ::mlir::Attribute removeOutputShapesAttr() {
      auto &attr = getProperties().output_shapes;
      attr = {};
      return attr;
  }

  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, TypeRange results, Value cond, ValueRange args, FuncAttr then_branch, FuncAttr else_branch);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange outs, ::mlir::Type ctl, ::mlir::Value cond, ::mlir::ValueRange args, ::mlir::tf_type::FuncAttr then_branch, ::mlir::tf_type::FuncAttr else_branch, /*optional*/::mlir::TypeAttr Tcond, /*optional*/::mlir::ArrayAttr Tin, /*optional*/::mlir::ArrayAttr Tout, /*optional*/::mlir::ArrayAttr output_shapes);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value cond, ::mlir::ValueRange args, ::mlir::tf_type::FuncAttr then_branch, ::mlir::tf_type::FuncAttr else_branch, /*optional*/::mlir::TypeAttr Tcond, /*optional*/::mlir::ArrayAttr Tin, /*optional*/::mlir::ArrayAttr Tout, /*optional*/::mlir::ArrayAttr output_shapes);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::llvm::LogicalResult verifyInvariantsImpl();
  ::llvm::LogicalResult verifyInvariants();
  ::llvm::LogicalResult verifySymbolUses(::mlir::SymbolTableCollection &symbolTable);
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 6 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace tfg
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::tfg::IfOp)

namespace mlir {
namespace tfg {

//===----------------------------------------------------------------------===//
// ::mlir::tfg::IfRegionOp declarations
//===----------------------------------------------------------------------===//

namespace detail {
class IfRegionOpGenericAdaptorBase {
public:
  struct Properties {
    using else_attrsTy = ::mlir::DictionaryAttr;
    else_attrsTy else_attrs;

    auto getElseAttrs() {
      auto &propStorage = this->else_attrs;
      return ::llvm::dyn_cast_or_null<::mlir::DictionaryAttr>(propStorage);
    }
    void setElseAttrs(const ::mlir::DictionaryAttr &propValue) {
      this->else_attrs = propValue;
    }
    using else_region_attrsTy = ::mlir::tfg::RegionAttr;
    else_region_attrsTy else_region_attrs;

    auto getElseRegionAttrs() {
      auto &propStorage = this->else_region_attrs;
      return ::llvm::dyn_cast_or_null<::mlir::tfg::RegionAttr>(propStorage);
    }
    void setElseRegionAttrs(const ::mlir::tfg::RegionAttr &propValue) {
      this->else_region_attrs = propValue;
    }
    using then_attrsTy = ::mlir::DictionaryAttr;
    then_attrsTy then_attrs;

    auto getThenAttrs() {
      auto &propStorage = this->then_attrs;
      return ::llvm::dyn_cast_or_null<::mlir::DictionaryAttr>(propStorage);
    }
    void setThenAttrs(const ::mlir::DictionaryAttr &propValue) {
      this->then_attrs = propValue;
    }
    using then_region_attrsTy = ::mlir::tfg::RegionAttr;
    then_region_attrsTy then_region_attrs;

    auto getThenRegionAttrs() {
      auto &propStorage = this->then_region_attrs;
      return ::llvm::dyn_cast_or_null<::mlir::tfg::RegionAttr>(propStorage);
    }
    void setThenRegionAttrs(const ::mlir::tfg::RegionAttr &propValue) {
      this->then_region_attrs = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.else_attrs == this->else_attrs &&
        rhs.else_region_attrs == this->else_region_attrs &&
        rhs.then_attrs == this->then_attrs &&
        rhs.then_region_attrs == this->then_region_attrs &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  IfRegionOpGenericAdaptorBase(::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : odsAttrs(attrs), properties(properties), odsRegions(regions) {  if (odsAttrs)
      odsOpName.emplace("tfg.IfRegion", odsAttrs.getContext());
  }

  IfRegionOpGenericAdaptorBase(IfRegionOp op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes() {
    return odsAttrs;
  }

  ::mlir::DictionaryAttr getThenAttrsAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::DictionaryAttr>(getProperties().then_attrs);
    return attr;
  }

  ::std::optional< ::mlir::DictionaryAttr > getThenAttrs();
  ::mlir::DictionaryAttr getElseAttrsAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::DictionaryAttr>(getProperties().else_attrs);
    return attr;
  }

  ::std::optional< ::mlir::DictionaryAttr > getElseAttrs();
  ::mlir::tfg::RegionAttr getThenRegionAttrsAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::tfg::RegionAttr>(getProperties().then_region_attrs);
    return attr;
  }

  ::std::optional<::mlir::tfg::RegionAttr> getThenRegionAttrs();
  ::mlir::tfg::RegionAttr getElseRegionAttrsAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::tfg::RegionAttr>(getProperties().else_region_attrs);
    return attr;
  }

  ::std::optional<::mlir::tfg::RegionAttr> getElseRegionAttrs();
  ::mlir::Region &getThenRegion() {
    return *odsRegions[0];
  }

  ::mlir::Region &getElseRegion() {
    return *odsRegions[1];
  }

  ::mlir::RegionRange getRegions() {
    return odsRegions;
  }

};
} // namespace detail
template <typename RangeT>
class IfRegionOpGenericAdaptor : public detail::IfRegionOpGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::IfRegionOpGenericAdaptorBase;
public:
  IfRegionOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  IfRegionOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : IfRegionOpGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  IfRegionOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr) : IfRegionOpGenericAdaptor(values, attrs, Properties{}, {}) {}

  IfRegionOpGenericAdaptor(RangeT values, const IfRegionOpGenericAdaptorBase &base) : Base(base), odsOperands(values) {}

  template <typename LateInst = IfRegionOp, typename = std::enable_if_t<std::is_same_v<LateInst, IfRegionOp>>>
  IfRegionOpGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getCond() {
    return (*getODSOperands(0).begin());
  }

  RangeT getCtls() {
    return getODSOperands(1);
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class IfRegionOpAdaptor : public IfRegionOpGenericAdaptor<::mlir::ValueRange> {
public:
  using IfRegionOpGenericAdaptor::IfRegionOpGenericAdaptor;
  IfRegionOpAdaptor(IfRegionOp op);

  ::llvm::LogicalResult verify(::mlir::Location loc);
};
class IfRegionOp : public ::mlir::Op<IfRegionOp, ::mlir::OpTrait::NRegions<2>::Impl, ::mlir::OpTrait::AtLeastNResults<1>::Impl, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::AtLeastNOperands<1>::Impl, ::mlir::OpTrait::SingleBlock, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait, ::mlir::OpTrait::HasOnlyGraphRegion, ::mlir::RegionKindInterface::Trait, ::mlir::OpAsmOpInterface::Trait, ::mlir::tfg::PreservedAttributesInterface::Trait, ::mlir::RegionBranchOpInterface::Trait, ::mlir::tfg::ControlArgumentInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = IfRegionOpAdaptor;
  template <typename RangeT>
  using GenericAdaptor = IfRegionOpGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("else_attrs"), ::llvm::StringRef("else_region_attrs"), ::llvm::StringRef("then_attrs"), ::llvm::StringRef("then_region_attrs")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getElseAttrsAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getElseAttrsAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  ::mlir::StringAttr getElseRegionAttrsAttrName() {
    return getAttributeNameForIndex(1);
  }

  static ::mlir::StringAttr getElseRegionAttrsAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 1);
  }

  ::mlir::StringAttr getThenAttrsAttrName() {
    return getAttributeNameForIndex(2);
  }

  static ::mlir::StringAttr getThenAttrsAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 2);
  }

  ::mlir::StringAttr getThenRegionAttrsAttrName() {
    return getAttributeNameForIndex(3);
  }

  static ::mlir::StringAttr getThenRegionAttrsAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 3);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("tfg.IfRegion");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(getOperation()->operand_begin(), valueRange.first),
             std::next(getOperation()->operand_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::TypedValue<::mlir::TensorType> getCond() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::TensorType>>(*getODSOperands(0).begin());
  }

  ::mlir::Operation::operand_range getCtls() {
    return getODSOperands(1);
  }

  ::mlir::OpOperand &getCondMutable() {
    auto range = getODSOperandIndexAndLength(0);
    return getOperation()->getOpOperand(range.first);
  }

  ::mlir::MutableOperandRange getCtlsMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index) {
    auto valueRange = getODSResultIndexAndLength(index);
    return {std::next(getOperation()->result_begin(), valueRange.first),
             std::next(getOperation()->result_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::Operation::result_range getOuts() {
    return getODSResults(0);
  }

  ::mlir::TypedValue<::mlir::tf_type::ControlType> getCtl() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::tf_type::ControlType>>(*getODSResults(1).begin());
  }

  ::mlir::Region &getThenRegion() {
    return (*this)->getRegion(0);
  }

  ::mlir::Region &getElseRegion() {
    return (*this)->getRegion(1);
  }

  static ::llvm::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::llvm::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::llvm::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::DictionaryAttr getThenAttrsAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::DictionaryAttr>(getProperties().then_attrs);
  }

  ::std::optional< ::mlir::DictionaryAttr > getThenAttrs();
  ::mlir::DictionaryAttr getElseAttrsAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::DictionaryAttr>(getProperties().else_attrs);
  }

  ::std::optional< ::mlir::DictionaryAttr > getElseAttrs();
  ::mlir::tfg::RegionAttr getThenRegionAttrsAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::tfg::RegionAttr>(getProperties().then_region_attrs);
  }

  ::std::optional<::mlir::tfg::RegionAttr> getThenRegionAttrs();
  ::mlir::tfg::RegionAttr getElseRegionAttrsAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::tfg::RegionAttr>(getProperties().else_region_attrs);
  }

  ::std::optional<::mlir::tfg::RegionAttr> getElseRegionAttrs();
  void setThenAttrsAttr(::mlir::DictionaryAttr attr) {
    getProperties().then_attrs = attr;
  }

  void setElseAttrsAttr(::mlir::DictionaryAttr attr) {
    getProperties().else_attrs = attr;
  }

  void setThenRegionAttrsAttr(::mlir::tfg::RegionAttr attr) {
    getProperties().then_region_attrs = attr;
  }

  void setElseRegionAttrsAttr(::mlir::tfg::RegionAttr attr) {
    getProperties().else_region_attrs = attr;
  }

  ::mlir::Attribute removeThenAttrsAttr() {
      auto &attr = getProperties().then_attrs;
      attr = {};
      return attr;
  }

  ::mlir::Attribute removeElseAttrsAttr() {
      auto &attr = getProperties().else_attrs;
      attr = {};
      return attr;
  }

  ::mlir::Attribute removeThenRegionAttrsAttr() {
      auto &attr = getProperties().then_region_attrs;
      attr = {};
      return attr;
  }

  ::mlir::Attribute removeElseRegionAttrsAttr() {
      auto &attr = getProperties().else_region_attrs;
      attr = {};
      return attr;
  }

  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange outs, ::mlir::Type ctl, ::mlir::Value cond, ::mlir::ValueRange ctls, /*optional*/::mlir::DictionaryAttr then_attrs, /*optional*/::mlir::DictionaryAttr else_attrs, /*optional*/::mlir::tfg::RegionAttr then_region_attrs, /*optional*/::mlir::tfg::RegionAttr else_region_attrs);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value cond, ::mlir::ValueRange ctls, /*optional*/::mlir::DictionaryAttr then_attrs, /*optional*/::mlir::DictionaryAttr else_attrs, /*optional*/::mlir::tfg::RegionAttr then_region_attrs, /*optional*/::mlir::tfg::RegionAttr else_region_attrs);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::llvm::LogicalResult verifyInvariantsImpl();
  ::llvm::LogicalResult verifyInvariants();
  ::llvm::LogicalResult verify();
  void getAsmResultNames(::mlir::OpAsmSetValueNameFn setNameFn);
  static ::llvm::StringRef getDefaultDialect();
  void getEntrySuccessorRegions(::llvm::ArrayRef<::mlir::Attribute> operands, ::llvm::SmallVectorImpl<::mlir::RegionSuccessor> &regions);
  void getSuccessorRegions(::mlir::RegionBranchPoint point, ::llvm::SmallVectorImpl<::mlir::RegionSuccessor> &regions);
  void getRegionInvocationBounds(::llvm::ArrayRef<::mlir::Attribute> operands, ::llvm::SmallVectorImpl<::mlir::InvocationBounds> &invocationBounds);
  static mlir::BlockArgument getDataValueOf(BlockArgument ctl);
  static mlir::BlockArgument getControlTokenOf(BlockArgument data);
  static mlir::BlockArgument getDataValue(Region &region, unsigned idx);
  static mlir::BlockArgument getControlToken(Region &region, unsigned idx);
  static ::mlir::ParseResult parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result);
  void print(::mlir::OpAsmPrinter &_odsPrinter);
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 4 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
  Block &getThenBlock() { return getThenRegion().front(); }
  Block &getElseBlock() { return getElseRegion().front(); }
  YieldOp getThenYield();
  YieldOp getElseYield();

  bool areTypesCompatible(Type lhs, Type rhs) {
    return tf_type::HasCompatibleElementTypes(lhs, rhs);
  }

  RegionAttr getPreservedAttrs(unsigned index) {
    if (index == 0) return getThenRegionAttrsAttr();
    return getElseRegionAttrsAttr();
  }
  void setPreservedAttrs(unsigned index, RegionAttr attrs) {
    if (index == 0) setThenRegionAttrsAttr(attrs);
    else setElseRegionAttrsAttr(attrs);
  }
};
} // namespace tfg
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::tfg::IfRegionOp)

namespace mlir {
namespace tfg {

//===----------------------------------------------------------------------===//
// ::mlir::tfg::ReturnOp declarations
//===----------------------------------------------------------------------===//

namespace detail {
class ReturnOpGenericAdaptorBase {
public:
  struct Properties {
    using control_ret_attrsTy = ::mlir::ArrayAttr;
    control_ret_attrsTy control_ret_attrs;

    auto getControlRetAttrs() {
      auto &propStorage = this->control_ret_attrs;
      return ::llvm::cast<::mlir::ArrayAttr>(propStorage);
    }
    void setControlRetAttrs(const ::mlir::ArrayAttr &propValue) {
      this->control_ret_attrs = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.control_ret_attrs == this->control_ret_attrs &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  ReturnOpGenericAdaptorBase(::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : odsAttrs(attrs), properties(properties), odsRegions(regions) {  if (odsAttrs)
      odsOpName.emplace("tfg.return", odsAttrs.getContext());
  }

  ReturnOpGenericAdaptorBase(ReturnOp op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes() {
    return odsAttrs;
  }

  ::mlir::ArrayAttr getControlRetAttrsAttr() {
    auto attr = ::llvm::cast<::mlir::ArrayAttr>(getProperties().control_ret_attrs);
    return attr;
  }

  ::mlir::ArrayAttr getControlRetAttrs();
};
} // namespace detail
template <typename RangeT>
class ReturnOpGenericAdaptor : public detail::ReturnOpGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::ReturnOpGenericAdaptorBase;
public:
  ReturnOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  ReturnOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : ReturnOpGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  ReturnOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr) : ReturnOpGenericAdaptor(values, attrs, Properties{}, {}) {}

  ReturnOpGenericAdaptor(RangeT values, const ReturnOpGenericAdaptorBase &base) : Base(base), odsOperands(values) {}

  template <typename LateInst = ReturnOp, typename = std::enable_if_t<std::is_same_v<LateInst, ReturnOp>>>
  ReturnOpGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class ReturnOpAdaptor : public ReturnOpGenericAdaptor<::mlir::ValueRange> {
public:
  using ReturnOpGenericAdaptor::ReturnOpGenericAdaptor;
  ReturnOpAdaptor(ReturnOp op);

  ::llvm::LogicalResult verify(::mlir::Location loc);
};
class ReturnOp : public ::mlir::Op<ReturnOp, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::ZeroResults, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::VariadicOperands, ::mlir::OpTrait::HasParent<GraphFuncOp>::Impl, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait, ::mlir::ConditionallySpeculatable::Trait, ::mlir::OpTrait::AlwaysSpeculatableImplTrait, ::mlir::MemoryEffectOpInterface::Trait, ::mlir::OpTrait::ReturnLike, ::mlir::OpTrait::IsTerminator, ::mlir::OpTrait::IntrinsicOperation> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = ReturnOpAdaptor;
  template <typename RangeT>
  using GenericAdaptor = ReturnOpGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("control_ret_attrs")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getControlRetAttrsAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getControlRetAttrsAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("tfg.return");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(getOperation()->operand_begin(), valueRange.first),
             std::next(getOperation()->operand_begin(), valueRange.first + valueRange.second)};
  }

  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index) {
    return {index, 1};
  }

  ::mlir::Operation::result_range getODSResults(unsigned index) {
    auto valueRange = getODSResultIndexAndLength(index);
    return {std::next(getOperation()->result_begin(), valueRange.first),
             std::next(getOperation()->result_begin(), valueRange.first + valueRange.second)};
  }

  static ::llvm::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::llvm::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::llvm::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::ArrayAttr getControlRetAttrsAttr() {
    return ::llvm::cast<::mlir::ArrayAttr>(getProperties().control_ret_attrs);
  }

  ::mlir::ArrayAttr getControlRetAttrs();
  void setControlRetAttrsAttr(::mlir::ArrayAttr attr) {
    getProperties().control_ret_attrs = attr;
  }

  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ValueRange operands, ValueRange control_operands);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::ValueRange odsArg_0, ::mlir::ArrayAttr control_ret_attrs);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange odsArg_0, ::mlir::ArrayAttr control_ret_attrs);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  static ::mlir::ParseResult parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result);
  void print(::mlir::OpAsmPrinter &p);
  ::llvm::LogicalResult verifyInvariantsImpl();
  ::llvm::LogicalResult verifyInvariants();
  ::llvm::LogicalResult verify();
  void getEffects(::llvm::SmallVectorImpl<::mlir::SideEffects::EffectInstance<::mlir::MemoryEffects::Effect>> &effects);
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 1 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace tfg
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::tfg::ReturnOp)

namespace mlir {
namespace tfg {

//===----------------------------------------------------------------------===//
// ::mlir::tfg::StatefulCaseOp declarations
//===----------------------------------------------------------------------===//

namespace detail {
class StatefulCaseOpGenericAdaptorBase {
public:
  struct Properties {
    using TinTy = ::mlir::ArrayAttr;
    TinTy Tin;

    auto getTin() {
      auto &propStorage = this->Tin;
      return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(propStorage);
    }
    void setTin(const ::mlir::ArrayAttr &propValue) {
      this->Tin = propValue;
    }
    using ToutTy = ::mlir::ArrayAttr;
    ToutTy Tout;

    auto getTout() {
      auto &propStorage = this->Tout;
      return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(propStorage);
    }
    void setTout(const ::mlir::ArrayAttr &propValue) {
      this->Tout = propValue;
    }
    using branchesTy = ::mlir::ArrayAttr;
    branchesTy branches;

    auto getBranches() {
      auto &propStorage = this->branches;
      return ::llvm::cast<::mlir::ArrayAttr>(propStorage);
    }
    void setBranches(const ::mlir::ArrayAttr &propValue) {
      this->branches = propValue;
    }
    using output_shapesTy = ::mlir::ArrayAttr;
    output_shapesTy output_shapes;

    auto getOutputShapes() {
      auto &propStorage = this->output_shapes;
      return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(propStorage);
    }
    void setOutputShapes(const ::mlir::ArrayAttr &propValue) {
      this->output_shapes = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.Tin == this->Tin &&
        rhs.Tout == this->Tout &&
        rhs.branches == this->branches &&
        rhs.output_shapes == this->output_shapes &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  StatefulCaseOpGenericAdaptorBase(::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : odsAttrs(attrs), properties(properties), odsRegions(regions) {  if (odsAttrs)
      odsOpName.emplace("tfg.StatefulCase", odsAttrs.getContext());
  }

  StatefulCaseOpGenericAdaptorBase(StatefulCaseOp op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes() {
    return odsAttrs;
  }

  ::mlir::ArrayAttr getBranchesAttr() {
    auto attr = ::llvm::cast<::mlir::ArrayAttr>(getProperties().branches);
    return attr;
  }

  ::mlir::ArrayAttr getBranches();
  ::mlir::ArrayAttr getTinAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().Tin);
    return attr;
  }

  ::std::optional< ::mlir::ArrayAttr > getTin();
  ::mlir::ArrayAttr getToutAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().Tout);
    return attr;
  }

  ::std::optional< ::mlir::ArrayAttr > getTout();
  ::mlir::ArrayAttr getOutputShapesAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().output_shapes);
    return attr;
  }

  ::std::optional< ::mlir::ArrayAttr > getOutputShapes();
};
} // namespace detail
template <typename RangeT>
class StatefulCaseOpGenericAdaptor : public detail::StatefulCaseOpGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::StatefulCaseOpGenericAdaptorBase;
public:
  StatefulCaseOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  StatefulCaseOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : StatefulCaseOpGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  StatefulCaseOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr) : StatefulCaseOpGenericAdaptor(values, attrs, Properties{}, {}) {}

  StatefulCaseOpGenericAdaptor(RangeT values, const StatefulCaseOpGenericAdaptorBase &base) : Base(base), odsOperands(values) {}

  template <typename LateInst = StatefulCaseOp, typename = std::enable_if_t<std::is_same_v<LateInst, StatefulCaseOp>>>
  StatefulCaseOpGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getBranchIndex() {
    return (*getODSOperands(0).begin());
  }

  RangeT getArgs() {
    return getODSOperands(1);
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class StatefulCaseOpAdaptor : public StatefulCaseOpGenericAdaptor<::mlir::ValueRange> {
public:
  using StatefulCaseOpGenericAdaptor::StatefulCaseOpGenericAdaptor;
  StatefulCaseOpAdaptor(StatefulCaseOp op);

  ::llvm::LogicalResult verify(::mlir::Location loc);
};
class StatefulCaseOp : public ::mlir::Op<StatefulCaseOp, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::AtLeastNResults<1>::Impl, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::AtLeastNOperands<1>::Impl, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait, ::mlir::SymbolUserOpInterface::Trait, ::mlir::OpAsmOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = StatefulCaseOpAdaptor;
  template <typename RangeT>
  using GenericAdaptor = StatefulCaseOpGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("Tin"), ::llvm::StringRef("Tout"), ::llvm::StringRef("branches"), ::llvm::StringRef("output_shapes")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getTinAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getTinAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  ::mlir::StringAttr getToutAttrName() {
    return getAttributeNameForIndex(1);
  }

  static ::mlir::StringAttr getToutAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 1);
  }

  ::mlir::StringAttr getBranchesAttrName() {
    return getAttributeNameForIndex(2);
  }

  static ::mlir::StringAttr getBranchesAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 2);
  }

  ::mlir::StringAttr getOutputShapesAttrName() {
    return getAttributeNameForIndex(3);
  }

  static ::mlir::StringAttr getOutputShapesAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 3);
  }

  void getAsmResultNames(::mlir::OpAsmSetValueNameFn setNameFn);
  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("tfg.StatefulCase");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(getOperation()->operand_begin(), valueRange.first),
             std::next(getOperation()->operand_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::TypedValue<::mlir::Type> getBranchIndex() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::Type>>(*getODSOperands(0).begin());
  }

  ::mlir::Operation::operand_range getArgs() {
    return getODSOperands(1);
  }

  ::mlir::OpOperand &getBranchIndexMutable() {
    auto range = getODSOperandIndexAndLength(0);
    return getOperation()->getOpOperand(range.first);
  }

  ::mlir::MutableOperandRange getArgsMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index) {
    auto valueRange = getODSResultIndexAndLength(index);
    return {std::next(getOperation()->result_begin(), valueRange.first),
             std::next(getOperation()->result_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::Operation::result_range getOuts() {
    return getODSResults(0);
  }

  ::mlir::TypedValue<::mlir::tf_type::ControlType> getCtl() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::tf_type::ControlType>>(*getODSResults(1).begin());
  }

  static ::llvm::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::llvm::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::llvm::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::ArrayAttr getBranchesAttr() {
    return ::llvm::cast<::mlir::ArrayAttr>(getProperties().branches);
  }

  ::mlir::ArrayAttr getBranches();
  ::mlir::ArrayAttr getTinAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().Tin);
  }

  ::std::optional< ::mlir::ArrayAttr > getTin();
  ::mlir::ArrayAttr getToutAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().Tout);
  }

  ::std::optional< ::mlir::ArrayAttr > getTout();
  ::mlir::ArrayAttr getOutputShapesAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().output_shapes);
  }

  ::std::optional< ::mlir::ArrayAttr > getOutputShapes();
  void setBranchesAttr(::mlir::ArrayAttr attr) {
    getProperties().branches = attr;
  }

  void setTinAttr(::mlir::ArrayAttr attr) {
    getProperties().Tin = attr;
  }

  void setToutAttr(::mlir::ArrayAttr attr) {
    getProperties().Tout = attr;
  }

  void setOutputShapesAttr(::mlir::ArrayAttr attr) {
    getProperties().output_shapes = attr;
  }

  ::mlir::Attribute removeTinAttr() {
      auto &attr = getProperties().Tin;
      attr = {};
      return attr;
  }

  ::mlir::Attribute removeToutAttr() {
      auto &attr = getProperties().Tout;
      attr = {};
      return attr;
  }

  ::mlir::Attribute removeOutputShapesAttr() {
      auto &attr = getProperties().output_shapes;
      attr = {};
      return attr;
  }

  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, TypeRange results, Value branch_index, ValueRange args, ArrayAttr branches);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange outs, ::mlir::Type ctl, ::mlir::Value branch_index, ::mlir::ValueRange args, ::mlir::ArrayAttr branches, /*optional*/::mlir::ArrayAttr Tin, /*optional*/::mlir::ArrayAttr Tout, /*optional*/::mlir::ArrayAttr output_shapes);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value branch_index, ::mlir::ValueRange args, ::mlir::ArrayAttr branches, /*optional*/::mlir::ArrayAttr Tin, /*optional*/::mlir::ArrayAttr Tout, /*optional*/::mlir::ArrayAttr output_shapes);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::llvm::LogicalResult verifyInvariantsImpl();
  ::llvm::LogicalResult verifyInvariants();
  ::llvm::LogicalResult verifySymbolUses(::mlir::SymbolTableCollection &symbolTable);
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 4 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace tfg
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::tfg::StatefulCaseOp)

namespace mlir {
namespace tfg {

//===----------------------------------------------------------------------===//
// ::mlir::tfg::StatefulCaseRegionOp declarations
//===----------------------------------------------------------------------===//

namespace detail {
class StatefulCaseRegionOpGenericAdaptorBase {
public:
  struct Properties {
    using branch_attrsTy = ::mlir::ArrayAttr;
    branch_attrsTy branch_attrs;

    auto getBranchAttrs() {
      auto &propStorage = this->branch_attrs;
      return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(propStorage);
    }
    void setBranchAttrs(const ::mlir::ArrayAttr &propValue) {
      this->branch_attrs = propValue;
    }
    using region_attrsTy = ::mlir::ArrayAttr;
    region_attrsTy region_attrs;

    auto getRegionAttrs() {
      auto &propStorage = this->region_attrs;
      return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(propStorage);
    }
    void setRegionAttrs(const ::mlir::ArrayAttr &propValue) {
      this->region_attrs = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.branch_attrs == this->branch_attrs &&
        rhs.region_attrs == this->region_attrs &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  StatefulCaseRegionOpGenericAdaptorBase(::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : odsAttrs(attrs), properties(properties), odsRegions(regions) {  if (odsAttrs)
      odsOpName.emplace("tfg.StatefulCaseRegion", odsAttrs.getContext());
  }

  StatefulCaseRegionOpGenericAdaptorBase(StatefulCaseRegionOp op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes() {
    return odsAttrs;
  }

  ::mlir::ArrayAttr getBranchAttrsAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().branch_attrs);
    return attr;
  }

  ::std::optional< ::mlir::ArrayAttr > getBranchAttrs();
  ::mlir::ArrayAttr getRegionAttrsAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().region_attrs);
    return attr;
  }

  ::std::optional< ::mlir::ArrayAttr > getRegionAttrs();
  ::mlir::RegionRange getBranches() {
    return odsRegions.drop_front(0);
  }

  ::mlir::RegionRange getRegions() {
    return odsRegions;
  }

};
} // namespace detail
template <typename RangeT>
class StatefulCaseRegionOpGenericAdaptor : public detail::StatefulCaseRegionOpGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::StatefulCaseRegionOpGenericAdaptorBase;
public:
  StatefulCaseRegionOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  StatefulCaseRegionOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : StatefulCaseRegionOpGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  StatefulCaseRegionOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr) : StatefulCaseRegionOpGenericAdaptor(values, attrs, Properties{}, {}) {}

  StatefulCaseRegionOpGenericAdaptor(RangeT values, const StatefulCaseRegionOpGenericAdaptorBase &base) : Base(base), odsOperands(values) {}

  template <typename LateInst = StatefulCaseRegionOp, typename = std::enable_if_t<std::is_same_v<LateInst, StatefulCaseRegionOp>>>
  StatefulCaseRegionOpGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getBranchIndex() {
    return (*getODSOperands(0).begin());
  }

  RangeT getCtls() {
    return getODSOperands(1);
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class StatefulCaseRegionOpAdaptor : public StatefulCaseRegionOpGenericAdaptor<::mlir::ValueRange> {
public:
  using StatefulCaseRegionOpGenericAdaptor::StatefulCaseRegionOpGenericAdaptor;
  StatefulCaseRegionOpAdaptor(StatefulCaseRegionOp op);

  ::llvm::LogicalResult verify(::mlir::Location loc);
};
class StatefulCaseRegionOp : public ::mlir::Op<StatefulCaseRegionOp, ::mlir::OpTrait::VariadicRegions, ::mlir::OpTrait::AtLeastNResults<1>::Impl, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::AtLeastNOperands<1>::Impl, ::mlir::OpTrait::SingleBlock, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait, ::mlir::OpTrait::HasOnlyGraphRegion, ::mlir::RegionKindInterface::Trait, ::mlir::OpAsmOpInterface::Trait, ::mlir::tfg::PreservedAttributesInterface::Trait, ::mlir::RegionBranchOpInterface::Trait, ::mlir::tfg::ControlArgumentInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = StatefulCaseRegionOpAdaptor;
  template <typename RangeT>
  using GenericAdaptor = StatefulCaseRegionOpGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("branch_attrs"), ::llvm::StringRef("region_attrs")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getBranchAttrsAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getBranchAttrsAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  ::mlir::StringAttr getRegionAttrsAttrName() {
    return getAttributeNameForIndex(1);
  }

  static ::mlir::StringAttr getRegionAttrsAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 1);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("tfg.StatefulCaseRegion");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(getOperation()->operand_begin(), valueRange.first),
             std::next(getOperation()->operand_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::TypedValue<::mlir::TensorType> getBranchIndex() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::TensorType>>(*getODSOperands(0).begin());
  }

  ::mlir::Operation::operand_range getCtls() {
    return getODSOperands(1);
  }

  ::mlir::OpOperand &getBranchIndexMutable() {
    auto range = getODSOperandIndexAndLength(0);
    return getOperation()->getOpOperand(range.first);
  }

  ::mlir::MutableOperandRange getCtlsMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index) {
    auto valueRange = getODSResultIndexAndLength(index);
    return {std::next(getOperation()->result_begin(), valueRange.first),
             std::next(getOperation()->result_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::Operation::result_range getOuts() {
    return getODSResults(0);
  }

  ::mlir::TypedValue<::mlir::tf_type::ControlType> getCtl() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::tf_type::ControlType>>(*getODSResults(1).begin());
  }

  ::mlir::MutableArrayRef<::mlir::Region> getBranches() {
    return (*this)->getRegions().drop_front(0);
  }

  static ::llvm::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::llvm::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::llvm::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::ArrayAttr getBranchAttrsAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().branch_attrs);
  }

  ::std::optional< ::mlir::ArrayAttr > getBranchAttrs();
  ::mlir::ArrayAttr getRegionAttrsAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().region_attrs);
  }

  ::std::optional< ::mlir::ArrayAttr > getRegionAttrs();
  void setBranchAttrsAttr(::mlir::ArrayAttr attr) {
    getProperties().branch_attrs = attr;
  }

  void setRegionAttrsAttr(::mlir::ArrayAttr attr) {
    getProperties().region_attrs = attr;
  }

  ::mlir::Attribute removeBranchAttrsAttr() {
      auto &attr = getProperties().branch_attrs;
      attr = {};
      return attr;
  }

  ::mlir::Attribute removeRegionAttrsAttr() {
      auto &attr = getProperties().region_attrs;
      attr = {};
      return attr;
  }

  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange outs, ::mlir::Type ctl, ::mlir::Value branch_index, ::mlir::ValueRange ctls, /*optional*/::mlir::ArrayAttr branch_attrs, /*optional*/::mlir::ArrayAttr region_attrs, unsigned branchesCount);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value branch_index, ::mlir::ValueRange ctls, /*optional*/::mlir::ArrayAttr branch_attrs, /*optional*/::mlir::ArrayAttr region_attrs, unsigned branchesCount);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes, unsigned numRegions);
  ::llvm::LogicalResult verifyInvariantsImpl();
  ::llvm::LogicalResult verifyInvariants();
  ::llvm::LogicalResult verify();
  void getAsmResultNames(::mlir::OpAsmSetValueNameFn setNameFn);
  static ::llvm::StringRef getDefaultDialect();
  void getEntrySuccessorRegions(::llvm::ArrayRef<::mlir::Attribute> operands, ::llvm::SmallVectorImpl<::mlir::RegionSuccessor> &regions);
  void getSuccessorRegions(::mlir::RegionBranchPoint point, ::llvm::SmallVectorImpl<::mlir::RegionSuccessor> &regions);
  void getRegionInvocationBounds(::llvm::ArrayRef<::mlir::Attribute> operands, ::llvm::SmallVectorImpl<::mlir::InvocationBounds> &invocationBounds);
  static mlir::BlockArgument getDataValueOf(BlockArgument ctl);
  static mlir::BlockArgument getControlTokenOf(BlockArgument data);
  static mlir::BlockArgument getDataValue(Region &region, unsigned idx);
  static mlir::BlockArgument getControlToken(Region &region, unsigned idx);
  static ::mlir::ParseResult parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result);
  void print(::mlir::OpAsmPrinter &_odsPrinter);
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 2 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
  Block &getBranchBlock(unsigned idx) { return getBranches()[idx].front(); }
  YieldOp branch_yield(unsigned idx);

  bool areTypesCompatible(Type lhs, Type rhs) {
    return tf_type::HasCompatibleElementTypes(lhs, rhs);
  }

  RegionAttr getPreservedAttrs(unsigned index) {
    if (auto attrs = getRegionAttrsAttr())
      return attrs[index].cast<RegionAttr>();
    return {};
  }
  void setPreservedAttrs(unsigned index, RegionAttr attrs) {
    SmallVector<Attribute> array = llvm::to_vector(getRegionAttrsAttr());
    array[index] = attrs;
    setRegionAttrsAttr(ArrayAttr::get(getContext(), array));
  }
};
} // namespace tfg
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::tfg::StatefulCaseRegionOp)

namespace mlir {
namespace tfg {

//===----------------------------------------------------------------------===//
// ::mlir::tfg::StatefulIfOp declarations
//===----------------------------------------------------------------------===//

namespace detail {
class StatefulIfOpGenericAdaptorBase {
public:
  struct Properties {
    using TcondTy = ::mlir::TypeAttr;
    TcondTy Tcond;

    auto getTcond() {
      auto &propStorage = this->Tcond;
      return ::llvm::dyn_cast_or_null<::mlir::TypeAttr>(propStorage);
    }
    void setTcond(const ::mlir::TypeAttr &propValue) {
      this->Tcond = propValue;
    }
    using TinTy = ::mlir::ArrayAttr;
    TinTy Tin;

    auto getTin() {
      auto &propStorage = this->Tin;
      return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(propStorage);
    }
    void setTin(const ::mlir::ArrayAttr &propValue) {
      this->Tin = propValue;
    }
    using ToutTy = ::mlir::ArrayAttr;
    ToutTy Tout;

    auto getTout() {
      auto &propStorage = this->Tout;
      return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(propStorage);
    }
    void setTout(const ::mlir::ArrayAttr &propValue) {
      this->Tout = propValue;
    }
    using else_branchTy = ::mlir::tf_type::FuncAttr;
    else_branchTy else_branch;

    auto getElseBranch() {
      auto &propStorage = this->else_branch;
      return ::llvm::cast<::mlir::tf_type::FuncAttr>(propStorage);
    }
    void setElseBranch(const ::mlir::tf_type::FuncAttr &propValue) {
      this->else_branch = propValue;
    }
    using output_shapesTy = ::mlir::ArrayAttr;
    output_shapesTy output_shapes;

    auto getOutputShapes() {
      auto &propStorage = this->output_shapes;
      return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(propStorage);
    }
    void setOutputShapes(const ::mlir::ArrayAttr &propValue) {
      this->output_shapes = propValue;
    }
    using then_branchTy = ::mlir::tf_type::FuncAttr;
    then_branchTy then_branch;

    auto getThenBranch() {
      auto &propStorage = this->then_branch;
      return ::llvm::cast<::mlir::tf_type::FuncAttr>(propStorage);
    }
    void setThenBranch(const ::mlir::tf_type::FuncAttr &propValue) {
      this->then_branch = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.Tcond == this->Tcond &&
        rhs.Tin == this->Tin &&
        rhs.Tout == this->Tout &&
        rhs.else_branch == this->else_branch &&
        rhs.output_shapes == this->output_shapes &&
        rhs.then_branch == this->then_branch &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  StatefulIfOpGenericAdaptorBase(::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : odsAttrs(attrs), properties(properties), odsRegions(regions) {  if (odsAttrs)
      odsOpName.emplace("tfg.StatefulIf", odsAttrs.getContext());
  }

  StatefulIfOpGenericAdaptorBase(StatefulIfOp op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes() {
    return odsAttrs;
  }

  ::mlir::tf_type::FuncAttr getThenBranchAttr() {
    auto attr = ::llvm::cast<::mlir::tf_type::FuncAttr>(getProperties().then_branch);
    return attr;
  }

  ::mlir::tf_type::FuncAttr getThenBranch();
  ::mlir::tf_type::FuncAttr getElseBranchAttr() {
    auto attr = ::llvm::cast<::mlir::tf_type::FuncAttr>(getProperties().else_branch);
    return attr;
  }

  ::mlir::tf_type::FuncAttr getElseBranch();
  ::mlir::TypeAttr getTcondAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::TypeAttr>(getProperties().Tcond);
    return attr;
  }

  ::std::optional<::mlir::Type> getTcond();
  ::mlir::ArrayAttr getTinAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().Tin);
    return attr;
  }

  ::std::optional< ::mlir::ArrayAttr > getTin();
  ::mlir::ArrayAttr getToutAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().Tout);
    return attr;
  }

  ::std::optional< ::mlir::ArrayAttr > getTout();
  ::mlir::ArrayAttr getOutputShapesAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().output_shapes);
    return attr;
  }

  ::std::optional< ::mlir::ArrayAttr > getOutputShapes();
};
} // namespace detail
template <typename RangeT>
class StatefulIfOpGenericAdaptor : public detail::StatefulIfOpGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::StatefulIfOpGenericAdaptorBase;
public:
  StatefulIfOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  StatefulIfOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : StatefulIfOpGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  StatefulIfOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr) : StatefulIfOpGenericAdaptor(values, attrs, Properties{}, {}) {}

  StatefulIfOpGenericAdaptor(RangeT values, const StatefulIfOpGenericAdaptorBase &base) : Base(base), odsOperands(values) {}

  template <typename LateInst = StatefulIfOp, typename = std::enable_if_t<std::is_same_v<LateInst, StatefulIfOp>>>
  StatefulIfOpGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getCond() {
    return (*getODSOperands(0).begin());
  }

  RangeT getArgs() {
    return getODSOperands(1);
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class StatefulIfOpAdaptor : public StatefulIfOpGenericAdaptor<::mlir::ValueRange> {
public:
  using StatefulIfOpGenericAdaptor::StatefulIfOpGenericAdaptor;
  StatefulIfOpAdaptor(StatefulIfOp op);

  ::llvm::LogicalResult verify(::mlir::Location loc);
};
class StatefulIfOp : public ::mlir::Op<StatefulIfOp, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::AtLeastNResults<1>::Impl, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::AtLeastNOperands<1>::Impl, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait, ::mlir::SymbolUserOpInterface::Trait, ::mlir::OpAsmOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = StatefulIfOpAdaptor;
  template <typename RangeT>
  using GenericAdaptor = StatefulIfOpGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("Tcond"), ::llvm::StringRef("Tin"), ::llvm::StringRef("Tout"), ::llvm::StringRef("else_branch"), ::llvm::StringRef("output_shapes"), ::llvm::StringRef("then_branch")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getTcondAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getTcondAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  ::mlir::StringAttr getTinAttrName() {
    return getAttributeNameForIndex(1);
  }

  static ::mlir::StringAttr getTinAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 1);
  }

  ::mlir::StringAttr getToutAttrName() {
    return getAttributeNameForIndex(2);
  }

  static ::mlir::StringAttr getToutAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 2);
  }

  ::mlir::StringAttr getElseBranchAttrName() {
    return getAttributeNameForIndex(3);
  }

  static ::mlir::StringAttr getElseBranchAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 3);
  }

  ::mlir::StringAttr getOutputShapesAttrName() {
    return getAttributeNameForIndex(4);
  }

  static ::mlir::StringAttr getOutputShapesAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 4);
  }

  ::mlir::StringAttr getThenBranchAttrName() {
    return getAttributeNameForIndex(5);
  }

  static ::mlir::StringAttr getThenBranchAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 5);
  }

  void getAsmResultNames(::mlir::OpAsmSetValueNameFn setNameFn);
  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("tfg.StatefulIf");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(getOperation()->operand_begin(), valueRange.first),
             std::next(getOperation()->operand_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::TypedValue<::mlir::Type> getCond() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::Type>>(*getODSOperands(0).begin());
  }

  ::mlir::Operation::operand_range getArgs() {
    return getODSOperands(1);
  }

  ::mlir::OpOperand &getCondMutable() {
    auto range = getODSOperandIndexAndLength(0);
    return getOperation()->getOpOperand(range.first);
  }

  ::mlir::MutableOperandRange getArgsMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index) {
    auto valueRange = getODSResultIndexAndLength(index);
    return {std::next(getOperation()->result_begin(), valueRange.first),
             std::next(getOperation()->result_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::Operation::result_range getOuts() {
    return getODSResults(0);
  }

  ::mlir::TypedValue<::mlir::tf_type::ControlType> getCtl() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::tf_type::ControlType>>(*getODSResults(1).begin());
  }

  static ::llvm::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::llvm::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::llvm::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::tf_type::FuncAttr getThenBranchAttr() {
    return ::llvm::cast<::mlir::tf_type::FuncAttr>(getProperties().then_branch);
  }

  ::mlir::tf_type::FuncAttr getThenBranch();
  ::mlir::tf_type::FuncAttr getElseBranchAttr() {
    return ::llvm::cast<::mlir::tf_type::FuncAttr>(getProperties().else_branch);
  }

  ::mlir::tf_type::FuncAttr getElseBranch();
  ::mlir::TypeAttr getTcondAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::TypeAttr>(getProperties().Tcond);
  }

  ::std::optional<::mlir::Type> getTcond();
  ::mlir::ArrayAttr getTinAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().Tin);
  }

  ::std::optional< ::mlir::ArrayAttr > getTin();
  ::mlir::ArrayAttr getToutAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().Tout);
  }

  ::std::optional< ::mlir::ArrayAttr > getTout();
  ::mlir::ArrayAttr getOutputShapesAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().output_shapes);
  }

  ::std::optional< ::mlir::ArrayAttr > getOutputShapes();
  void setThenBranchAttr(::mlir::tf_type::FuncAttr attr) {
    getProperties().then_branch = attr;
  }

  void setElseBranchAttr(::mlir::tf_type::FuncAttr attr) {
    getProperties().else_branch = attr;
  }

  void setTcondAttr(::mlir::TypeAttr attr) {
    getProperties().Tcond = attr;
  }

  void setTcond(::std::optional<::mlir::Type> attrValue);
  void setTinAttr(::mlir::ArrayAttr attr) {
    getProperties().Tin = attr;
  }

  void setToutAttr(::mlir::ArrayAttr attr) {
    getProperties().Tout = attr;
  }

  void setOutputShapesAttr(::mlir::ArrayAttr attr) {
    getProperties().output_shapes = attr;
  }

  ::mlir::Attribute removeTcondAttr() {
      auto &attr = getProperties().Tcond;
      attr = {};
      return attr;
  }

  ::mlir::Attribute removeTinAttr() {
      auto &attr = getProperties().Tin;
      attr = {};
      return attr;
  }

  ::mlir::Attribute removeToutAttr() {
      auto &attr = getProperties().Tout;
      attr = {};
      return attr;
  }

  ::mlir::Attribute removeOutputShapesAttr() {
      auto &attr = getProperties().output_shapes;
      attr = {};
      return attr;
  }

  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, TypeRange results, Value cond, ValueRange args, FuncAttr then_branch, FuncAttr else_branch);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange outs, ::mlir::Type ctl, ::mlir::Value cond, ::mlir::ValueRange args, ::mlir::tf_type::FuncAttr then_branch, ::mlir::tf_type::FuncAttr else_branch, /*optional*/::mlir::TypeAttr Tcond, /*optional*/::mlir::ArrayAttr Tin, /*optional*/::mlir::ArrayAttr Tout, /*optional*/::mlir::ArrayAttr output_shapes);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value cond, ::mlir::ValueRange args, ::mlir::tf_type::FuncAttr then_branch, ::mlir::tf_type::FuncAttr else_branch, /*optional*/::mlir::TypeAttr Tcond, /*optional*/::mlir::ArrayAttr Tin, /*optional*/::mlir::ArrayAttr Tout, /*optional*/::mlir::ArrayAttr output_shapes);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::llvm::LogicalResult verifyInvariantsImpl();
  ::llvm::LogicalResult verifyInvariants();
  ::llvm::LogicalResult verifySymbolUses(::mlir::SymbolTableCollection &symbolTable);
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 6 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace tfg
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::tfg::StatefulIfOp)

namespace mlir {
namespace tfg {

//===----------------------------------------------------------------------===//
// ::mlir::tfg::StatefulIfRegionOp declarations
//===----------------------------------------------------------------------===//

namespace detail {
class StatefulIfRegionOpGenericAdaptorBase {
public:
  struct Properties {
    using else_attrsTy = ::mlir::DictionaryAttr;
    else_attrsTy else_attrs;

    auto getElseAttrs() {
      auto &propStorage = this->else_attrs;
      return ::llvm::dyn_cast_or_null<::mlir::DictionaryAttr>(propStorage);
    }
    void setElseAttrs(const ::mlir::DictionaryAttr &propValue) {
      this->else_attrs = propValue;
    }
    using else_region_attrsTy = ::mlir::tfg::RegionAttr;
    else_region_attrsTy else_region_attrs;

    auto getElseRegionAttrs() {
      auto &propStorage = this->else_region_attrs;
      return ::llvm::dyn_cast_or_null<::mlir::tfg::RegionAttr>(propStorage);
    }
    void setElseRegionAttrs(const ::mlir::tfg::RegionAttr &propValue) {
      this->else_region_attrs = propValue;
    }
    using then_attrsTy = ::mlir::DictionaryAttr;
    then_attrsTy then_attrs;

    auto getThenAttrs() {
      auto &propStorage = this->then_attrs;
      return ::llvm::dyn_cast_or_null<::mlir::DictionaryAttr>(propStorage);
    }
    void setThenAttrs(const ::mlir::DictionaryAttr &propValue) {
      this->then_attrs = propValue;
    }
    using then_region_attrsTy = ::mlir::tfg::RegionAttr;
    then_region_attrsTy then_region_attrs;

    auto getThenRegionAttrs() {
      auto &propStorage = this->then_region_attrs;
      return ::llvm::dyn_cast_or_null<::mlir::tfg::RegionAttr>(propStorage);
    }
    void setThenRegionAttrs(const ::mlir::tfg::RegionAttr &propValue) {
      this->then_region_attrs = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.else_attrs == this->else_attrs &&
        rhs.else_region_attrs == this->else_region_attrs &&
        rhs.then_attrs == this->then_attrs &&
        rhs.then_region_attrs == this->then_region_attrs &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  StatefulIfRegionOpGenericAdaptorBase(::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : odsAttrs(attrs), properties(properties), odsRegions(regions) {  if (odsAttrs)
      odsOpName.emplace("tfg.StatefulIfRegion", odsAttrs.getContext());
  }

  StatefulIfRegionOpGenericAdaptorBase(StatefulIfRegionOp op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes() {
    return odsAttrs;
  }

  ::mlir::DictionaryAttr getThenAttrsAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::DictionaryAttr>(getProperties().then_attrs);
    return attr;
  }

  ::std::optional< ::mlir::DictionaryAttr > getThenAttrs();
  ::mlir::DictionaryAttr getElseAttrsAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::DictionaryAttr>(getProperties().else_attrs);
    return attr;
  }

  ::std::optional< ::mlir::DictionaryAttr > getElseAttrs();
  ::mlir::tfg::RegionAttr getThenRegionAttrsAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::tfg::RegionAttr>(getProperties().then_region_attrs);
    return attr;
  }

  ::std::optional<::mlir::tfg::RegionAttr> getThenRegionAttrs();
  ::mlir::tfg::RegionAttr getElseRegionAttrsAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::tfg::RegionAttr>(getProperties().else_region_attrs);
    return attr;
  }

  ::std::optional<::mlir::tfg::RegionAttr> getElseRegionAttrs();
  ::mlir::Region &getThenRegion() {
    return *odsRegions[0];
  }

  ::mlir::Region &getElseRegion() {
    return *odsRegions[1];
  }

  ::mlir::RegionRange getRegions() {
    return odsRegions;
  }

};
} // namespace detail
template <typename RangeT>
class StatefulIfRegionOpGenericAdaptor : public detail::StatefulIfRegionOpGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::StatefulIfRegionOpGenericAdaptorBase;
public:
  StatefulIfRegionOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  StatefulIfRegionOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : StatefulIfRegionOpGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  StatefulIfRegionOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr) : StatefulIfRegionOpGenericAdaptor(values, attrs, Properties{}, {}) {}

  StatefulIfRegionOpGenericAdaptor(RangeT values, const StatefulIfRegionOpGenericAdaptorBase &base) : Base(base), odsOperands(values) {}

  template <typename LateInst = StatefulIfRegionOp, typename = std::enable_if_t<std::is_same_v<LateInst, StatefulIfRegionOp>>>
  StatefulIfRegionOpGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getCond() {
    return (*getODSOperands(0).begin());
  }

  RangeT getCtls() {
    return getODSOperands(1);
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class StatefulIfRegionOpAdaptor : public StatefulIfRegionOpGenericAdaptor<::mlir::ValueRange> {
public:
  using StatefulIfRegionOpGenericAdaptor::StatefulIfRegionOpGenericAdaptor;
  StatefulIfRegionOpAdaptor(StatefulIfRegionOp op);

  ::llvm::LogicalResult verify(::mlir::Location loc);
};
class StatefulIfRegionOp : public ::mlir::Op<StatefulIfRegionOp, ::mlir::OpTrait::NRegions<2>::Impl, ::mlir::OpTrait::AtLeastNResults<1>::Impl, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::AtLeastNOperands<1>::Impl, ::mlir::OpTrait::SingleBlock, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait, ::mlir::OpTrait::HasOnlyGraphRegion, ::mlir::RegionKindInterface::Trait, ::mlir::OpAsmOpInterface::Trait, ::mlir::tfg::PreservedAttributesInterface::Trait, ::mlir::RegionBranchOpInterface::Trait, ::mlir::tfg::ControlArgumentInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = StatefulIfRegionOpAdaptor;
  template <typename RangeT>
  using GenericAdaptor = StatefulIfRegionOpGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("else_attrs"), ::llvm::StringRef("else_region_attrs"), ::llvm::StringRef("then_attrs"), ::llvm::StringRef("then_region_attrs")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getElseAttrsAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getElseAttrsAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  ::mlir::StringAttr getElseRegionAttrsAttrName() {
    return getAttributeNameForIndex(1);
  }

  static ::mlir::StringAttr getElseRegionAttrsAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 1);
  }

  ::mlir::StringAttr getThenAttrsAttrName() {
    return getAttributeNameForIndex(2);
  }

  static ::mlir::StringAttr getThenAttrsAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 2);
  }

  ::mlir::StringAttr getThenRegionAttrsAttrName() {
    return getAttributeNameForIndex(3);
  }

  static ::mlir::StringAttr getThenRegionAttrsAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 3);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("tfg.StatefulIfRegion");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(getOperation()->operand_begin(), valueRange.first),
             std::next(getOperation()->operand_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::TypedValue<::mlir::TensorType> getCond() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::TensorType>>(*getODSOperands(0).begin());
  }

  ::mlir::Operation::operand_range getCtls() {
    return getODSOperands(1);
  }

  ::mlir::OpOperand &getCondMutable() {
    auto range = getODSOperandIndexAndLength(0);
    return getOperation()->getOpOperand(range.first);
  }

  ::mlir::MutableOperandRange getCtlsMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index) {
    auto valueRange = getODSResultIndexAndLength(index);
    return {std::next(getOperation()->result_begin(), valueRange.first),
             std::next(getOperation()->result_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::Operation::result_range getOuts() {
    return getODSResults(0);
  }

  ::mlir::TypedValue<::mlir::tf_type::ControlType> getCtl() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::tf_type::ControlType>>(*getODSResults(1).begin());
  }

  ::mlir::Region &getThenRegion() {
    return (*this)->getRegion(0);
  }

  ::mlir::Region &getElseRegion() {
    return (*this)->getRegion(1);
  }

  static ::llvm::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::llvm::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::llvm::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::DictionaryAttr getThenAttrsAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::DictionaryAttr>(getProperties().then_attrs);
  }

  ::std::optional< ::mlir::DictionaryAttr > getThenAttrs();
  ::mlir::DictionaryAttr getElseAttrsAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::DictionaryAttr>(getProperties().else_attrs);
  }

  ::std::optional< ::mlir::DictionaryAttr > getElseAttrs();
  ::mlir::tfg::RegionAttr getThenRegionAttrsAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::tfg::RegionAttr>(getProperties().then_region_attrs);
  }

  ::std::optional<::mlir::tfg::RegionAttr> getThenRegionAttrs();
  ::mlir::tfg::RegionAttr getElseRegionAttrsAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::tfg::RegionAttr>(getProperties().else_region_attrs);
  }

  ::std::optional<::mlir::tfg::RegionAttr> getElseRegionAttrs();
  void setThenAttrsAttr(::mlir::DictionaryAttr attr) {
    getProperties().then_attrs = attr;
  }

  void setElseAttrsAttr(::mlir::DictionaryAttr attr) {
    getProperties().else_attrs = attr;
  }

  void setThenRegionAttrsAttr(::mlir::tfg::RegionAttr attr) {
    getProperties().then_region_attrs = attr;
  }

  void setElseRegionAttrsAttr(::mlir::tfg::RegionAttr attr) {
    getProperties().else_region_attrs = attr;
  }

  ::mlir::Attribute removeThenAttrsAttr() {
      auto &attr = getProperties().then_attrs;
      attr = {};
      return attr;
  }

  ::mlir::Attribute removeElseAttrsAttr() {
      auto &attr = getProperties().else_attrs;
      attr = {};
      return attr;
  }

  ::mlir::Attribute removeThenRegionAttrsAttr() {
      auto &attr = getProperties().then_region_attrs;
      attr = {};
      return attr;
  }

  ::mlir::Attribute removeElseRegionAttrsAttr() {
      auto &attr = getProperties().else_region_attrs;
      attr = {};
      return attr;
  }

  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange outs, ::mlir::Type ctl, ::mlir::Value cond, ::mlir::ValueRange ctls, /*optional*/::mlir::DictionaryAttr then_attrs, /*optional*/::mlir::DictionaryAttr else_attrs, /*optional*/::mlir::tfg::RegionAttr then_region_attrs, /*optional*/::mlir::tfg::RegionAttr else_region_attrs);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value cond, ::mlir::ValueRange ctls, /*optional*/::mlir::DictionaryAttr then_attrs, /*optional*/::mlir::DictionaryAttr else_attrs, /*optional*/::mlir::tfg::RegionAttr then_region_attrs, /*optional*/::mlir::tfg::RegionAttr else_region_attrs);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::llvm::LogicalResult verifyInvariantsImpl();
  ::llvm::LogicalResult verifyInvariants();
  ::llvm::LogicalResult verify();
  void getAsmResultNames(::mlir::OpAsmSetValueNameFn setNameFn);
  static ::llvm::StringRef getDefaultDialect();
  void getEntrySuccessorRegions(::llvm::ArrayRef<::mlir::Attribute> operands, ::llvm::SmallVectorImpl<::mlir::RegionSuccessor> &regions);
  void getSuccessorRegions(::mlir::RegionBranchPoint point, ::llvm::SmallVectorImpl<::mlir::RegionSuccessor> &regions);
  void getRegionInvocationBounds(::llvm::ArrayRef<::mlir::Attribute> operands, ::llvm::SmallVectorImpl<::mlir::InvocationBounds> &invocationBounds);
  static mlir::BlockArgument getDataValueOf(BlockArgument ctl);
  static mlir::BlockArgument getControlTokenOf(BlockArgument data);
  static mlir::BlockArgument getDataValue(Region &region, unsigned idx);
  static mlir::BlockArgument getControlToken(Region &region, unsigned idx);
  static ::mlir::ParseResult parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result);
  void print(::mlir::OpAsmPrinter &_odsPrinter);
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 4 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
  Block &getThenBlock() { return getThenRegion().front(); }
  Block &getElseBlock() { return getElseRegion().front(); }
  YieldOp getThenYield();
  YieldOp getElseYield();

  bool areTypesCompatible(Type lhs, Type rhs) {
    return tf_type::HasCompatibleElementTypes(lhs, rhs);
  }

  RegionAttr getPreservedAttrs(unsigned index) {
    if (index == 0) return getThenRegionAttrsAttr();
    return getElseRegionAttrsAttr();
  }
  void setPreservedAttrs(unsigned index, RegionAttr attrs) {
    if (index == 0) setThenRegionAttrsAttr(attrs);
    else setElseRegionAttrsAttr(attrs);
  }
};
} // namespace tfg
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::tfg::StatefulIfRegionOp)

namespace mlir {
namespace tfg {

//===----------------------------------------------------------------------===//
// ::mlir::tfg::StatefulWhileOp declarations
//===----------------------------------------------------------------------===//

namespace detail {
class StatefulWhileOpGenericAdaptorBase {
public:
  struct Properties {
    using TTy = ::mlir::ArrayAttr;
    TTy T;

    auto getT() {
      auto &propStorage = this->T;
      return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(propStorage);
    }
    void setT(const ::mlir::ArrayAttr &propValue) {
      this->T = propValue;
    }
    using bodyTy = ::mlir::tf_type::FuncAttr;
    bodyTy body;

    auto getBody() {
      auto &propStorage = this->body;
      return ::llvm::cast<::mlir::tf_type::FuncAttr>(propStorage);
    }
    void setBody(const ::mlir::tf_type::FuncAttr &propValue) {
      this->body = propValue;
    }
    using condTy = ::mlir::tf_type::FuncAttr;
    condTy cond;

    auto getCond() {
      auto &propStorage = this->cond;
      return ::llvm::cast<::mlir::tf_type::FuncAttr>(propStorage);
    }
    void setCond(const ::mlir::tf_type::FuncAttr &propValue) {
      this->cond = propValue;
    }
    using output_shapesTy = ::mlir::ArrayAttr;
    output_shapesTy output_shapes;

    auto getOutputShapes() {
      auto &propStorage = this->output_shapes;
      return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(propStorage);
    }
    void setOutputShapes(const ::mlir::ArrayAttr &propValue) {
      this->output_shapes = propValue;
    }
    using parallel_iterationsTy = ::mlir::IntegerAttr;
    parallel_iterationsTy parallel_iterations;

    auto getParallelIterations() {
      auto &propStorage = this->parallel_iterations;
      return ::llvm::cast<::mlir::IntegerAttr>(propStorage);
    }
    void setParallelIterations(const ::mlir::IntegerAttr &propValue) {
      this->parallel_iterations = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.T == this->T &&
        rhs.body == this->body &&
        rhs.cond == this->cond &&
        rhs.output_shapes == this->output_shapes &&
        rhs.parallel_iterations == this->parallel_iterations &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  StatefulWhileOpGenericAdaptorBase(::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : odsAttrs(attrs), properties(properties), odsRegions(regions) {  if (odsAttrs)
      odsOpName.emplace("tfg.StatefulWhile", odsAttrs.getContext());
  }

  StatefulWhileOpGenericAdaptorBase(StatefulWhileOp op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes() {
    return odsAttrs;
  }

  ::mlir::tf_type::FuncAttr getCondAttr() {
    auto attr = ::llvm::cast<::mlir::tf_type::FuncAttr>(getProperties().cond);
    return attr;
  }

  ::mlir::tf_type::FuncAttr getCond();
  ::mlir::tf_type::FuncAttr getBodyAttr() {
    auto attr = ::llvm::cast<::mlir::tf_type::FuncAttr>(getProperties().body);
    return attr;
  }

  ::mlir::tf_type::FuncAttr getBody();
  ::mlir::IntegerAttr getParallelIterationsAttr() {
    auto attr = ::llvm::cast<::mlir::IntegerAttr>(getProperties().parallel_iterations);
    return attr;
  }

  uint64_t getParallelIterations();
  ::mlir::ArrayAttr getTAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().T);
    return attr;
  }

  ::std::optional< ::mlir::ArrayAttr > getT();
  ::mlir::ArrayAttr getOutputShapesAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().output_shapes);
    return attr;
  }

  ::std::optional< ::mlir::ArrayAttr > getOutputShapes();
};
} // namespace detail
template <typename RangeT>
class StatefulWhileOpGenericAdaptor : public detail::StatefulWhileOpGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::StatefulWhileOpGenericAdaptorBase;
public:
  StatefulWhileOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  StatefulWhileOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : StatefulWhileOpGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  StatefulWhileOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr) : StatefulWhileOpGenericAdaptor(values, attrs, Properties{}, {}) {}

  StatefulWhileOpGenericAdaptor(RangeT values, const StatefulWhileOpGenericAdaptorBase &base) : Base(base), odsOperands(values) {}

  template <typename LateInst = StatefulWhileOp, typename = std::enable_if_t<std::is_same_v<LateInst, StatefulWhileOp>>>
  StatefulWhileOpGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  RangeT getArgs() {
    return getODSOperands(0);
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class StatefulWhileOpAdaptor : public StatefulWhileOpGenericAdaptor<::mlir::ValueRange> {
public:
  using StatefulWhileOpGenericAdaptor::StatefulWhileOpGenericAdaptor;
  StatefulWhileOpAdaptor(StatefulWhileOp op);

  ::llvm::LogicalResult verify(::mlir::Location loc);
};
class StatefulWhileOp : public ::mlir::Op<StatefulWhileOp, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::AtLeastNResults<1>::Impl, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::VariadicOperands, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait, ::mlir::SymbolUserOpInterface::Trait, ::mlir::OpAsmOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = StatefulWhileOpAdaptor;
  template <typename RangeT>
  using GenericAdaptor = StatefulWhileOpGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("T"), ::llvm::StringRef("body"), ::llvm::StringRef("cond"), ::llvm::StringRef("output_shapes"), ::llvm::StringRef("parallel_iterations")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getTAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getTAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  ::mlir::StringAttr getBodyAttrName() {
    return getAttributeNameForIndex(1);
  }

  static ::mlir::StringAttr getBodyAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 1);
  }

  ::mlir::StringAttr getCondAttrName() {
    return getAttributeNameForIndex(2);
  }

  static ::mlir::StringAttr getCondAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 2);
  }

  ::mlir::StringAttr getOutputShapesAttrName() {
    return getAttributeNameForIndex(3);
  }

  static ::mlir::StringAttr getOutputShapesAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 3);
  }

  ::mlir::StringAttr getParallelIterationsAttrName() {
    return getAttributeNameForIndex(4);
  }

  static ::mlir::StringAttr getParallelIterationsAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 4);
  }

  void getAsmResultNames(::mlir::OpAsmSetValueNameFn setNameFn);
  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("tfg.StatefulWhile");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(getOperation()->operand_begin(), valueRange.first),
             std::next(getOperation()->operand_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::Operation::operand_range getArgs() {
    return getODSOperands(0);
  }

  ::mlir::MutableOperandRange getArgsMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index) {
    auto valueRange = getODSResultIndexAndLength(index);
    return {std::next(getOperation()->result_begin(), valueRange.first),
             std::next(getOperation()->result_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::Operation::result_range getOuts() {
    return getODSResults(0);
  }

  ::mlir::TypedValue<::mlir::tf_type::ControlType> getCtl() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::tf_type::ControlType>>(*getODSResults(1).begin());
  }

  static ::llvm::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::llvm::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::llvm::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::tf_type::FuncAttr getCondAttr() {
    return ::llvm::cast<::mlir::tf_type::FuncAttr>(getProperties().cond);
  }

  ::mlir::tf_type::FuncAttr getCond();
  ::mlir::tf_type::FuncAttr getBodyAttr() {
    return ::llvm::cast<::mlir::tf_type::FuncAttr>(getProperties().body);
  }

  ::mlir::tf_type::FuncAttr getBody();
  ::mlir::IntegerAttr getParallelIterationsAttr() {
    return ::llvm::cast<::mlir::IntegerAttr>(getProperties().parallel_iterations);
  }

  uint64_t getParallelIterations();
  ::mlir::ArrayAttr getTAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().T);
  }

  ::std::optional< ::mlir::ArrayAttr > getT();
  ::mlir::ArrayAttr getOutputShapesAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().output_shapes);
  }

  ::std::optional< ::mlir::ArrayAttr > getOutputShapes();
  void setCondAttr(::mlir::tf_type::FuncAttr attr) {
    getProperties().cond = attr;
  }

  void setBodyAttr(::mlir::tf_type::FuncAttr attr) {
    getProperties().body = attr;
  }

  void setParallelIterationsAttr(::mlir::IntegerAttr attr) {
    getProperties().parallel_iterations = attr;
  }

  void setParallelIterations(uint64_t attrValue);
  void setTAttr(::mlir::ArrayAttr attr) {
    getProperties().T = attr;
  }

  void setOutputShapesAttr(::mlir::ArrayAttr attr) {
    getProperties().output_shapes = attr;
  }

  ::mlir::Attribute removeTAttr() {
      auto &attr = getProperties().T;
      attr = {};
      return attr;
  }

  ::mlir::Attribute removeOutputShapesAttr() {
      auto &attr = getProperties().output_shapes;
      attr = {};
      return attr;
  }

  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, TypeRange results, ValueRange args, FuncAttr cond, FuncAttr body, IntegerAttr parallel_iterations);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange outs, ::mlir::Type ctl, ::mlir::ValueRange args, ::mlir::tf_type::FuncAttr cond, ::mlir::tf_type::FuncAttr body, ::mlir::IntegerAttr parallel_iterations, /*optional*/::mlir::ArrayAttr T, /*optional*/::mlir::ArrayAttr output_shapes);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange args, ::mlir::tf_type::FuncAttr cond, ::mlir::tf_type::FuncAttr body, ::mlir::IntegerAttr parallel_iterations, /*optional*/::mlir::ArrayAttr T, /*optional*/::mlir::ArrayAttr output_shapes);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange outs, ::mlir::Type ctl, ::mlir::ValueRange args, ::mlir::tf_type::FuncAttr cond, ::mlir::tf_type::FuncAttr body, uint64_t parallel_iterations, /*optional*/::mlir::ArrayAttr T, /*optional*/::mlir::ArrayAttr output_shapes);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange args, ::mlir::tf_type::FuncAttr cond, ::mlir::tf_type::FuncAttr body, uint64_t parallel_iterations, /*optional*/::mlir::ArrayAttr T, /*optional*/::mlir::ArrayAttr output_shapes);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::llvm::LogicalResult verifyInvariantsImpl();
  ::llvm::LogicalResult verifyInvariants();
  ::llvm::LogicalResult verifySymbolUses(::mlir::SymbolTableCollection &symbolTable);
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 5 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace tfg
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::tfg::StatefulWhileOp)

namespace mlir {
namespace tfg {

//===----------------------------------------------------------------------===//
// ::mlir::tfg::StatefulWhileRegionOp declarations
//===----------------------------------------------------------------------===//

namespace detail {
class StatefulWhileRegionOpGenericAdaptorBase {
public:
  struct Properties {
    using body_attrsTy = ::mlir::DictionaryAttr;
    body_attrsTy body_attrs;

    auto getBodyAttrs() {
      auto &propStorage = this->body_attrs;
      return ::llvm::dyn_cast_or_null<::mlir::DictionaryAttr>(propStorage);
    }
    void setBodyAttrs(const ::mlir::DictionaryAttr &propValue) {
      this->body_attrs = propValue;
    }
    using body_region_attrsTy = ::mlir::tfg::RegionAttr;
    body_region_attrsTy body_region_attrs;

    auto getBodyRegionAttrs() {
      auto &propStorage = this->body_region_attrs;
      return ::llvm::dyn_cast_or_null<::mlir::tfg::RegionAttr>(propStorage);
    }
    void setBodyRegionAttrs(const ::mlir::tfg::RegionAttr &propValue) {
      this->body_region_attrs = propValue;
    }
    using cond_attrsTy = ::mlir::DictionaryAttr;
    cond_attrsTy cond_attrs;

    auto getCondAttrs() {
      auto &propStorage = this->cond_attrs;
      return ::llvm::dyn_cast_or_null<::mlir::DictionaryAttr>(propStorage);
    }
    void setCondAttrs(const ::mlir::DictionaryAttr &propValue) {
      this->cond_attrs = propValue;
    }
    using cond_region_attrsTy = ::mlir::tfg::RegionAttr;
    cond_region_attrsTy cond_region_attrs;

    auto getCondRegionAttrs() {
      auto &propStorage = this->cond_region_attrs;
      return ::llvm::dyn_cast_or_null<::mlir::tfg::RegionAttr>(propStorage);
    }
    void setCondRegionAttrs(const ::mlir::tfg::RegionAttr &propValue) {
      this->cond_region_attrs = propValue;
    }
    using parallel_iterationsTy = ::mlir::IntegerAttr;
    parallel_iterationsTy parallel_iterations;

    auto getParallelIterations() {
      auto &propStorage = this->parallel_iterations;
      return ::llvm::cast<::mlir::IntegerAttr>(propStorage);
    }
    void setParallelIterations(const ::mlir::IntegerAttr &propValue) {
      this->parallel_iterations = propValue;
    }
    using operandSegmentSizesTy = std::array<int32_t, 2>;
    operandSegmentSizesTy operandSegmentSizes;
    ::llvm::ArrayRef<int32_t> getOperandSegmentSizes() const {
      auto &propStorage = this->operandSegmentSizes;
      return propStorage;
    }
    void setOperandSegmentSizes(::llvm::ArrayRef<int32_t> propValue) {
      auto &propStorage = this->operandSegmentSizes;
      ::llvm::copy(propValue, propStorage.begin());
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.body_attrs == this->body_attrs &&
        rhs.body_region_attrs == this->body_region_attrs &&
        rhs.cond_attrs == this->cond_attrs &&
        rhs.cond_region_attrs == this->cond_region_attrs &&
        rhs.parallel_iterations == this->parallel_iterations &&
        rhs.operandSegmentSizes == this->operandSegmentSizes &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  StatefulWhileRegionOpGenericAdaptorBase(::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : odsAttrs(attrs), properties(properties), odsRegions(regions) {  if (odsAttrs)
      odsOpName.emplace("tfg.StatefulWhileRegion", odsAttrs.getContext());
  }

  StatefulWhileRegionOpGenericAdaptorBase(StatefulWhileRegionOp op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes() {
    return odsAttrs;
  }

  ::mlir::IntegerAttr getParallelIterationsAttr() {
    auto attr = ::llvm::cast<::mlir::IntegerAttr>(getProperties().parallel_iterations);
    return attr;
  }

  uint64_t getParallelIterations();
  ::mlir::DictionaryAttr getCondAttrsAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::DictionaryAttr>(getProperties().cond_attrs);
    return attr;
  }

  ::std::optional< ::mlir::DictionaryAttr > getCondAttrs();
  ::mlir::DictionaryAttr getBodyAttrsAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::DictionaryAttr>(getProperties().body_attrs);
    return attr;
  }

  ::std::optional< ::mlir::DictionaryAttr > getBodyAttrs();
  ::mlir::tfg::RegionAttr getCondRegionAttrsAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::tfg::RegionAttr>(getProperties().cond_region_attrs);
    return attr;
  }

  ::std::optional<::mlir::tfg::RegionAttr> getCondRegionAttrs();
  ::mlir::tfg::RegionAttr getBodyRegionAttrsAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::tfg::RegionAttr>(getProperties().body_region_attrs);
    return attr;
  }

  ::std::optional<::mlir::tfg::RegionAttr> getBodyRegionAttrs();
  ::mlir::Region &getCondRegion() {
    return *odsRegions[0];
  }

  ::mlir::Region &getBodyRegion() {
    return *odsRegions[1];
  }

  ::mlir::RegionRange getRegions() {
    return odsRegions;
  }

};
} // namespace detail
template <typename RangeT>
class StatefulWhileRegionOpGenericAdaptor : public detail::StatefulWhileRegionOpGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::StatefulWhileRegionOpGenericAdaptorBase;
public:
  StatefulWhileRegionOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  StatefulWhileRegionOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : StatefulWhileRegionOpGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  StatefulWhileRegionOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs) : StatefulWhileRegionOpGenericAdaptor(values, attrs, Properties{}, {}) {}

  StatefulWhileRegionOpGenericAdaptor(RangeT values, const StatefulWhileRegionOpGenericAdaptorBase &base) : Base(base), odsOperands(values) {}

  template <typename LateInst = StatefulWhileRegionOp, typename = std::enable_if_t<std::is_same_v<LateInst, StatefulWhileRegionOp>>>
  StatefulWhileRegionOpGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  RangeT getInit() {
    return getODSOperands(0);
  }

  RangeT getCtls() {
    return getODSOperands(1);
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class StatefulWhileRegionOpAdaptor : public StatefulWhileRegionOpGenericAdaptor<::mlir::ValueRange> {
public:
  using StatefulWhileRegionOpGenericAdaptor::StatefulWhileRegionOpGenericAdaptor;
  StatefulWhileRegionOpAdaptor(StatefulWhileRegionOp op);

  ::llvm::LogicalResult verify(::mlir::Location loc);
};
class StatefulWhileRegionOp : public ::mlir::Op<StatefulWhileRegionOp, ::mlir::OpTrait::NRegions<2>::Impl, ::mlir::OpTrait::AtLeastNResults<1>::Impl, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::VariadicOperands, ::mlir::OpTrait::SingleBlock, ::mlir::OpTrait::AttrSizedOperandSegments, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait, ::mlir::OpTrait::HasOnlyGraphRegion, ::mlir::RegionKindInterface::Trait, ::mlir::OpAsmOpInterface::Trait, ::mlir::tfg::PreservedAttributesInterface::Trait, ::mlir::RegionBranchOpInterface::Trait, ::mlir::tfg::ControlArgumentInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = StatefulWhileRegionOpAdaptor;
  template <typename RangeT>
  using GenericAdaptor = StatefulWhileRegionOpGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("body_attrs"), ::llvm::StringRef("body_region_attrs"), ::llvm::StringRef("cond_attrs"), ::llvm::StringRef("cond_region_attrs"), ::llvm::StringRef("parallel_iterations"), ::llvm::StringRef("operandSegmentSizes")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getBodyAttrsAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getBodyAttrsAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  ::mlir::StringAttr getBodyRegionAttrsAttrName() {
    return getAttributeNameForIndex(1);
  }

  static ::mlir::StringAttr getBodyRegionAttrsAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 1);
  }

  ::mlir::StringAttr getCondAttrsAttrName() {
    return getAttributeNameForIndex(2);
  }

  static ::mlir::StringAttr getCondAttrsAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 2);
  }

  ::mlir::StringAttr getCondRegionAttrsAttrName() {
    return getAttributeNameForIndex(3);
  }

  static ::mlir::StringAttr getCondRegionAttrsAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 3);
  }

  ::mlir::StringAttr getParallelIterationsAttrName() {
    return getAttributeNameForIndex(4);
  }

  static ::mlir::StringAttr getParallelIterationsAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 4);
  }

  ::mlir::StringAttr getOperandSegmentSizesAttrName() {
   return (*this)->getName().getAttributeNames().back();
  }

  static ::mlir::StringAttr getOperandSegmentSizesAttrName(::mlir::OperationName name) {
   return name.getAttributeNames().back();
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("tfg.StatefulWhileRegion");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(getOperation()->operand_begin(), valueRange.first),
             std::next(getOperation()->operand_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::Operation::operand_range getInit() {
    return getODSOperands(0);
  }

  ::mlir::Operation::operand_range getCtls() {
    return getODSOperands(1);
  }

  ::mlir::MutableOperandRange getInitMutable();
  ::mlir::MutableOperandRange getCtlsMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index) {
    auto valueRange = getODSResultIndexAndLength(index);
    return {std::next(getOperation()->result_begin(), valueRange.first),
             std::next(getOperation()->result_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::Operation::result_range getOuts() {
    return getODSResults(0);
  }

  ::mlir::TypedValue<::mlir::tf_type::ControlType> getCtl() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::tf_type::ControlType>>(*getODSResults(1).begin());
  }

  ::mlir::Region &getCondRegion() {
    return (*this)->getRegion(0);
  }

  ::mlir::Region &getBodyRegion() {
    return (*this)->getRegion(1);
  }

  static ::llvm::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::llvm::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::llvm::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::IntegerAttr getParallelIterationsAttr() {
    return ::llvm::cast<::mlir::IntegerAttr>(getProperties().parallel_iterations);
  }

  uint64_t getParallelIterations();
  ::mlir::DictionaryAttr getCondAttrsAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::DictionaryAttr>(getProperties().cond_attrs);
  }

  ::std::optional< ::mlir::DictionaryAttr > getCondAttrs();
  ::mlir::DictionaryAttr getBodyAttrsAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::DictionaryAttr>(getProperties().body_attrs);
  }

  ::std::optional< ::mlir::DictionaryAttr > getBodyAttrs();
  ::mlir::tfg::RegionAttr getCondRegionAttrsAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::tfg::RegionAttr>(getProperties().cond_region_attrs);
  }

  ::std::optional<::mlir::tfg::RegionAttr> getCondRegionAttrs();
  ::mlir::tfg::RegionAttr getBodyRegionAttrsAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::tfg::RegionAttr>(getProperties().body_region_attrs);
  }

  ::std::optional<::mlir::tfg::RegionAttr> getBodyRegionAttrs();
  void setParallelIterationsAttr(::mlir::IntegerAttr attr) {
    getProperties().parallel_iterations = attr;
  }

  void setParallelIterations(uint64_t attrValue);
  void setCondAttrsAttr(::mlir::DictionaryAttr attr) {
    getProperties().cond_attrs = attr;
  }

  void setBodyAttrsAttr(::mlir::DictionaryAttr attr) {
    getProperties().body_attrs = attr;
  }

  void setCondRegionAttrsAttr(::mlir::tfg::RegionAttr attr) {
    getProperties().cond_region_attrs = attr;
  }

  void setBodyRegionAttrsAttr(::mlir::tfg::RegionAttr attr) {
    getProperties().body_region_attrs = attr;
  }

  ::mlir::Attribute removeCondAttrsAttr() {
      auto &attr = getProperties().cond_attrs;
      attr = {};
      return attr;
  }

  ::mlir::Attribute removeBodyAttrsAttr() {
      auto &attr = getProperties().body_attrs;
      attr = {};
      return attr;
  }

  ::mlir::Attribute removeCondRegionAttrsAttr() {
      auto &attr = getProperties().cond_region_attrs;
      attr = {};
      return attr;
  }

  ::mlir::Attribute removeBodyRegionAttrsAttr() {
      auto &attr = getProperties().body_region_attrs;
      attr = {};
      return attr;
  }

  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange outs, ::mlir::Type ctl, ::mlir::ValueRange init, ::mlir::ValueRange ctls, ::mlir::IntegerAttr parallel_iterations, /*optional*/::mlir::DictionaryAttr cond_attrs, /*optional*/::mlir::DictionaryAttr body_attrs, /*optional*/::mlir::tfg::RegionAttr cond_region_attrs, /*optional*/::mlir::tfg::RegionAttr body_region_attrs);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange init, ::mlir::ValueRange ctls, ::mlir::IntegerAttr parallel_iterations, /*optional*/::mlir::DictionaryAttr cond_attrs, /*optional*/::mlir::DictionaryAttr body_attrs, /*optional*/::mlir::tfg::RegionAttr cond_region_attrs, /*optional*/::mlir::tfg::RegionAttr body_region_attrs);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange outs, ::mlir::Type ctl, ::mlir::ValueRange init, ::mlir::ValueRange ctls, uint64_t parallel_iterations, /*optional*/::mlir::DictionaryAttr cond_attrs, /*optional*/::mlir::DictionaryAttr body_attrs, /*optional*/::mlir::tfg::RegionAttr cond_region_attrs, /*optional*/::mlir::tfg::RegionAttr body_region_attrs);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange init, ::mlir::ValueRange ctls, uint64_t parallel_iterations, /*optional*/::mlir::DictionaryAttr cond_attrs, /*optional*/::mlir::DictionaryAttr body_attrs, /*optional*/::mlir::tfg::RegionAttr cond_region_attrs, /*optional*/::mlir::tfg::RegionAttr body_region_attrs);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::llvm::LogicalResult verifyInvariantsImpl();
  ::llvm::LogicalResult verifyInvariants();
  ::llvm::LogicalResult verify();
  void getAsmResultNames(::mlir::OpAsmSetValueNameFn setNameFn);
  static ::llvm::StringRef getDefaultDialect();
  ::mlir::OperandRange getEntrySuccessorOperands(::mlir::RegionBranchPoint point);
  void getSuccessorRegions(::mlir::RegionBranchPoint point, ::llvm::SmallVectorImpl<::mlir::RegionSuccessor> &regions);
  static mlir::BlockArgument getDataValueOf(BlockArgument ctl);
  static mlir::BlockArgument getControlTokenOf(BlockArgument data);
  static mlir::BlockArgument getDataValue(Region &region, unsigned idx);
  static mlir::BlockArgument getControlToken(Region &region, unsigned idx);
  static ::mlir::ParseResult parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result);
  void print(::mlir::OpAsmPrinter &_odsPrinter);
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 5 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
  Block &getCondBlock() { return getCondRegion().front(); }
  Block &getBodyBlock() { return getBodyRegion().front(); }
  ConditionOp getCondCondition();
  YieldOp getBodyYield();

  bool areTypesCompatible(Type lhs, Type rhs) {
    return tf_type::HasCompatibleElementTypes(lhs, rhs);
  }

  RegionAttr getPreservedAttrs(unsigned index) {
    if (index == 0) return getCondRegionAttrsAttr();
    return getBodyRegionAttrsAttr();
  }
  void setPreservedAttrs(unsigned index, RegionAttr attrs) {
    if (index == 0) setCondRegionAttrsAttr(attrs);
    else setBodyRegionAttrsAttr(attrs);
  }
};
} // namespace tfg
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::tfg::StatefulWhileRegionOp)

namespace mlir {
namespace tfg {

//===----------------------------------------------------------------------===//
// ::mlir::tfg::StatelessCaseOp declarations
//===----------------------------------------------------------------------===//

namespace detail {
class StatelessCaseOpGenericAdaptorBase {
public:
  struct Properties {
    using TinTy = ::mlir::ArrayAttr;
    TinTy Tin;

    auto getTin() {
      auto &propStorage = this->Tin;
      return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(propStorage);
    }
    void setTin(const ::mlir::ArrayAttr &propValue) {
      this->Tin = propValue;
    }
    using ToutTy = ::mlir::ArrayAttr;
    ToutTy Tout;

    auto getTout() {
      auto &propStorage = this->Tout;
      return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(propStorage);
    }
    void setTout(const ::mlir::ArrayAttr &propValue) {
      this->Tout = propValue;
    }
    using branchesTy = ::mlir::ArrayAttr;
    branchesTy branches;

    auto getBranches() {
      auto &propStorage = this->branches;
      return ::llvm::cast<::mlir::ArrayAttr>(propStorage);
    }
    void setBranches(const ::mlir::ArrayAttr &propValue) {
      this->branches = propValue;
    }
    using output_shapesTy = ::mlir::ArrayAttr;
    output_shapesTy output_shapes;

    auto getOutputShapes() {
      auto &propStorage = this->output_shapes;
      return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(propStorage);
    }
    void setOutputShapes(const ::mlir::ArrayAttr &propValue) {
      this->output_shapes = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.Tin == this->Tin &&
        rhs.Tout == this->Tout &&
        rhs.branches == this->branches &&
        rhs.output_shapes == this->output_shapes &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  StatelessCaseOpGenericAdaptorBase(::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : odsAttrs(attrs), properties(properties), odsRegions(regions) {  if (odsAttrs)
      odsOpName.emplace("tfg.StatelessCase", odsAttrs.getContext());
  }

  StatelessCaseOpGenericAdaptorBase(StatelessCaseOp op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes() {
    return odsAttrs;
  }

  ::mlir::ArrayAttr getBranchesAttr() {
    auto attr = ::llvm::cast<::mlir::ArrayAttr>(getProperties().branches);
    return attr;
  }

  ::mlir::ArrayAttr getBranches();
  ::mlir::ArrayAttr getTinAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().Tin);
    return attr;
  }

  ::std::optional< ::mlir::ArrayAttr > getTin();
  ::mlir::ArrayAttr getToutAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().Tout);
    return attr;
  }

  ::std::optional< ::mlir::ArrayAttr > getTout();
  ::mlir::ArrayAttr getOutputShapesAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().output_shapes);
    return attr;
  }

  ::std::optional< ::mlir::ArrayAttr > getOutputShapes();
};
} // namespace detail
template <typename RangeT>
class StatelessCaseOpGenericAdaptor : public detail::StatelessCaseOpGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::StatelessCaseOpGenericAdaptorBase;
public:
  StatelessCaseOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  StatelessCaseOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : StatelessCaseOpGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  StatelessCaseOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr) : StatelessCaseOpGenericAdaptor(values, attrs, Properties{}, {}) {}

  StatelessCaseOpGenericAdaptor(RangeT values, const StatelessCaseOpGenericAdaptorBase &base) : Base(base), odsOperands(values) {}

  template <typename LateInst = StatelessCaseOp, typename = std::enable_if_t<std::is_same_v<LateInst, StatelessCaseOp>>>
  StatelessCaseOpGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getBranchIndex() {
    return (*getODSOperands(0).begin());
  }

  RangeT getArgs() {
    return getODSOperands(1);
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class StatelessCaseOpAdaptor : public StatelessCaseOpGenericAdaptor<::mlir::ValueRange> {
public:
  using StatelessCaseOpGenericAdaptor::StatelessCaseOpGenericAdaptor;
  StatelessCaseOpAdaptor(StatelessCaseOp op);

  ::llvm::LogicalResult verify(::mlir::Location loc);
};
class StatelessCaseOp : public ::mlir::Op<StatelessCaseOp, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::AtLeastNResults<1>::Impl, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::AtLeastNOperands<1>::Impl, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait, ::mlir::SymbolUserOpInterface::Trait, ::mlir::OpAsmOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = StatelessCaseOpAdaptor;
  template <typename RangeT>
  using GenericAdaptor = StatelessCaseOpGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("Tin"), ::llvm::StringRef("Tout"), ::llvm::StringRef("branches"), ::llvm::StringRef("output_shapes")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getTinAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getTinAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  ::mlir::StringAttr getToutAttrName() {
    return getAttributeNameForIndex(1);
  }

  static ::mlir::StringAttr getToutAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 1);
  }

  ::mlir::StringAttr getBranchesAttrName() {
    return getAttributeNameForIndex(2);
  }

  static ::mlir::StringAttr getBranchesAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 2);
  }

  ::mlir::StringAttr getOutputShapesAttrName() {
    return getAttributeNameForIndex(3);
  }

  static ::mlir::StringAttr getOutputShapesAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 3);
  }

  void getAsmResultNames(::mlir::OpAsmSetValueNameFn setNameFn);
  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("tfg.StatelessCase");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(getOperation()->operand_begin(), valueRange.first),
             std::next(getOperation()->operand_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::TypedValue<::mlir::Type> getBranchIndex() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::Type>>(*getODSOperands(0).begin());
  }

  ::mlir::Operation::operand_range getArgs() {
    return getODSOperands(1);
  }

  ::mlir::OpOperand &getBranchIndexMutable() {
    auto range = getODSOperandIndexAndLength(0);
    return getOperation()->getOpOperand(range.first);
  }

  ::mlir::MutableOperandRange getArgsMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index) {
    auto valueRange = getODSResultIndexAndLength(index);
    return {std::next(getOperation()->result_begin(), valueRange.first),
             std::next(getOperation()->result_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::Operation::result_range getOuts() {
    return getODSResults(0);
  }

  ::mlir::TypedValue<::mlir::tf_type::ControlType> getCtl() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::tf_type::ControlType>>(*getODSResults(1).begin());
  }

  static ::llvm::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::llvm::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::llvm::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::ArrayAttr getBranchesAttr() {
    return ::llvm::cast<::mlir::ArrayAttr>(getProperties().branches);
  }

  ::mlir::ArrayAttr getBranches();
  ::mlir::ArrayAttr getTinAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().Tin);
  }

  ::std::optional< ::mlir::ArrayAttr > getTin();
  ::mlir::ArrayAttr getToutAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().Tout);
  }

  ::std::optional< ::mlir::ArrayAttr > getTout();
  ::mlir::ArrayAttr getOutputShapesAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().output_shapes);
  }

  ::std::optional< ::mlir::ArrayAttr > getOutputShapes();
  void setBranchesAttr(::mlir::ArrayAttr attr) {
    getProperties().branches = attr;
  }

  void setTinAttr(::mlir::ArrayAttr attr) {
    getProperties().Tin = attr;
  }

  void setToutAttr(::mlir::ArrayAttr attr) {
    getProperties().Tout = attr;
  }

  void setOutputShapesAttr(::mlir::ArrayAttr attr) {
    getProperties().output_shapes = attr;
  }

  ::mlir::Attribute removeTinAttr() {
      auto &attr = getProperties().Tin;
      attr = {};
      return attr;
  }

  ::mlir::Attribute removeToutAttr() {
      auto &attr = getProperties().Tout;
      attr = {};
      return attr;
  }

  ::mlir::Attribute removeOutputShapesAttr() {
      auto &attr = getProperties().output_shapes;
      attr = {};
      return attr;
  }

  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, TypeRange results, Value branch_index, ValueRange args, ArrayAttr branches);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange outs, ::mlir::Type ctl, ::mlir::Value branch_index, ::mlir::ValueRange args, ::mlir::ArrayAttr branches, /*optional*/::mlir::ArrayAttr Tin, /*optional*/::mlir::ArrayAttr Tout, /*optional*/::mlir::ArrayAttr output_shapes);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value branch_index, ::mlir::ValueRange args, ::mlir::ArrayAttr branches, /*optional*/::mlir::ArrayAttr Tin, /*optional*/::mlir::ArrayAttr Tout, /*optional*/::mlir::ArrayAttr output_shapes);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::llvm::LogicalResult verifyInvariantsImpl();
  ::llvm::LogicalResult verifyInvariants();
  ::llvm::LogicalResult verifySymbolUses(::mlir::SymbolTableCollection &symbolTable);
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 4 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace tfg
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::tfg::StatelessCaseOp)

namespace mlir {
namespace tfg {

//===----------------------------------------------------------------------===//
// ::mlir::tfg::StatelessCaseRegionOp declarations
//===----------------------------------------------------------------------===//

namespace detail {
class StatelessCaseRegionOpGenericAdaptorBase {
public:
  struct Properties {
    using branch_attrsTy = ::mlir::ArrayAttr;
    branch_attrsTy branch_attrs;

    auto getBranchAttrs() {
      auto &propStorage = this->branch_attrs;
      return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(propStorage);
    }
    void setBranchAttrs(const ::mlir::ArrayAttr &propValue) {
      this->branch_attrs = propValue;
    }
    using region_attrsTy = ::mlir::ArrayAttr;
    region_attrsTy region_attrs;

    auto getRegionAttrs() {
      auto &propStorage = this->region_attrs;
      return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(propStorage);
    }
    void setRegionAttrs(const ::mlir::ArrayAttr &propValue) {
      this->region_attrs = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.branch_attrs == this->branch_attrs &&
        rhs.region_attrs == this->region_attrs &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  StatelessCaseRegionOpGenericAdaptorBase(::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : odsAttrs(attrs), properties(properties), odsRegions(regions) {  if (odsAttrs)
      odsOpName.emplace("tfg.StatelessCaseRegion", odsAttrs.getContext());
  }

  StatelessCaseRegionOpGenericAdaptorBase(StatelessCaseRegionOp op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes() {
    return odsAttrs;
  }

  ::mlir::ArrayAttr getBranchAttrsAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().branch_attrs);
    return attr;
  }

  ::std::optional< ::mlir::ArrayAttr > getBranchAttrs();
  ::mlir::ArrayAttr getRegionAttrsAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().region_attrs);
    return attr;
  }

  ::std::optional< ::mlir::ArrayAttr > getRegionAttrs();
  ::mlir::RegionRange getBranches() {
    return odsRegions.drop_front(0);
  }

  ::mlir::RegionRange getRegions() {
    return odsRegions;
  }

};
} // namespace detail
template <typename RangeT>
class StatelessCaseRegionOpGenericAdaptor : public detail::StatelessCaseRegionOpGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::StatelessCaseRegionOpGenericAdaptorBase;
public:
  StatelessCaseRegionOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  StatelessCaseRegionOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : StatelessCaseRegionOpGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  StatelessCaseRegionOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr) : StatelessCaseRegionOpGenericAdaptor(values, attrs, Properties{}, {}) {}

  StatelessCaseRegionOpGenericAdaptor(RangeT values, const StatelessCaseRegionOpGenericAdaptorBase &base) : Base(base), odsOperands(values) {}

  template <typename LateInst = StatelessCaseRegionOp, typename = std::enable_if_t<std::is_same_v<LateInst, StatelessCaseRegionOp>>>
  StatelessCaseRegionOpGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getBranchIndex() {
    return (*getODSOperands(0).begin());
  }

  RangeT getCtls() {
    return getODSOperands(1);
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class StatelessCaseRegionOpAdaptor : public StatelessCaseRegionOpGenericAdaptor<::mlir::ValueRange> {
public:
  using StatelessCaseRegionOpGenericAdaptor::StatelessCaseRegionOpGenericAdaptor;
  StatelessCaseRegionOpAdaptor(StatelessCaseRegionOp op);

  ::llvm::LogicalResult verify(::mlir::Location loc);
};
class StatelessCaseRegionOp : public ::mlir::Op<StatelessCaseRegionOp, ::mlir::OpTrait::VariadicRegions, ::mlir::OpTrait::AtLeastNResults<1>::Impl, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::AtLeastNOperands<1>::Impl, ::mlir::OpTrait::SingleBlock, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait, ::mlir::OpTrait::HasOnlyGraphRegion, ::mlir::RegionKindInterface::Trait, ::mlir::OpAsmOpInterface::Trait, ::mlir::tfg::PreservedAttributesInterface::Trait, ::mlir::RegionBranchOpInterface::Trait, ::mlir::tfg::ControlArgumentInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = StatelessCaseRegionOpAdaptor;
  template <typename RangeT>
  using GenericAdaptor = StatelessCaseRegionOpGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("branch_attrs"), ::llvm::StringRef("region_attrs")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getBranchAttrsAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getBranchAttrsAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  ::mlir::StringAttr getRegionAttrsAttrName() {
    return getAttributeNameForIndex(1);
  }

  static ::mlir::StringAttr getRegionAttrsAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 1);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("tfg.StatelessCaseRegion");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(getOperation()->operand_begin(), valueRange.first),
             std::next(getOperation()->operand_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::TypedValue<::mlir::TensorType> getBranchIndex() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::TensorType>>(*getODSOperands(0).begin());
  }

  ::mlir::Operation::operand_range getCtls() {
    return getODSOperands(1);
  }

  ::mlir::OpOperand &getBranchIndexMutable() {
    auto range = getODSOperandIndexAndLength(0);
    return getOperation()->getOpOperand(range.first);
  }

  ::mlir::MutableOperandRange getCtlsMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index) {
    auto valueRange = getODSResultIndexAndLength(index);
    return {std::next(getOperation()->result_begin(), valueRange.first),
             std::next(getOperation()->result_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::Operation::result_range getOuts() {
    return getODSResults(0);
  }

  ::mlir::TypedValue<::mlir::tf_type::ControlType> getCtl() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::tf_type::ControlType>>(*getODSResults(1).begin());
  }

  ::mlir::MutableArrayRef<::mlir::Region> getBranches() {
    return (*this)->getRegions().drop_front(0);
  }

  static ::llvm::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::llvm::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::llvm::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::ArrayAttr getBranchAttrsAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().branch_attrs);
  }

  ::std::optional< ::mlir::ArrayAttr > getBranchAttrs();
  ::mlir::ArrayAttr getRegionAttrsAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().region_attrs);
  }

  ::std::optional< ::mlir::ArrayAttr > getRegionAttrs();
  void setBranchAttrsAttr(::mlir::ArrayAttr attr) {
    getProperties().branch_attrs = attr;
  }

  void setRegionAttrsAttr(::mlir::ArrayAttr attr) {
    getProperties().region_attrs = attr;
  }

  ::mlir::Attribute removeBranchAttrsAttr() {
      auto &attr = getProperties().branch_attrs;
      attr = {};
      return attr;
  }

  ::mlir::Attribute removeRegionAttrsAttr() {
      auto &attr = getProperties().region_attrs;
      attr = {};
      return attr;
  }

  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange outs, ::mlir::Type ctl, ::mlir::Value branch_index, ::mlir::ValueRange ctls, /*optional*/::mlir::ArrayAttr branch_attrs, /*optional*/::mlir::ArrayAttr region_attrs, unsigned branchesCount);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value branch_index, ::mlir::ValueRange ctls, /*optional*/::mlir::ArrayAttr branch_attrs, /*optional*/::mlir::ArrayAttr region_attrs, unsigned branchesCount);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes, unsigned numRegions);
  ::llvm::LogicalResult verifyInvariantsImpl();
  ::llvm::LogicalResult verifyInvariants();
  ::llvm::LogicalResult verify();
  void getAsmResultNames(::mlir::OpAsmSetValueNameFn setNameFn);
  static ::llvm::StringRef getDefaultDialect();
  void getEntrySuccessorRegions(::llvm::ArrayRef<::mlir::Attribute> operands, ::llvm::SmallVectorImpl<::mlir::RegionSuccessor> &regions);
  void getSuccessorRegions(::mlir::RegionBranchPoint point, ::llvm::SmallVectorImpl<::mlir::RegionSuccessor> &regions);
  void getRegionInvocationBounds(::llvm::ArrayRef<::mlir::Attribute> operands, ::llvm::SmallVectorImpl<::mlir::InvocationBounds> &invocationBounds);
  static mlir::BlockArgument getDataValueOf(BlockArgument ctl);
  static mlir::BlockArgument getControlTokenOf(BlockArgument data);
  static mlir::BlockArgument getDataValue(Region &region, unsigned idx);
  static mlir::BlockArgument getControlToken(Region &region, unsigned idx);
  static ::mlir::ParseResult parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result);
  void print(::mlir::OpAsmPrinter &_odsPrinter);
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 2 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
  Block &getBranchBlock(unsigned idx) { return getBranches()[idx].front(); }
  YieldOp branch_yield(unsigned idx);

  bool areTypesCompatible(Type lhs, Type rhs) {
    return tf_type::HasCompatibleElementTypes(lhs, rhs);
  }

  RegionAttr getPreservedAttrs(unsigned index) {
    if (auto attrs = getRegionAttrsAttr())
      return attrs[index].cast<RegionAttr>();
    return {};
  }
  void setPreservedAttrs(unsigned index, RegionAttr attrs) {
    SmallVector<Attribute> array = llvm::to_vector(getRegionAttrsAttr());
    array[index] = attrs;
    setRegionAttrsAttr(ArrayAttr::get(getContext(), array));
  }
};
} // namespace tfg
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::tfg::StatelessCaseRegionOp)

namespace mlir {
namespace tfg {

//===----------------------------------------------------------------------===//
// ::mlir::tfg::StatelessIfOp declarations
//===----------------------------------------------------------------------===//

namespace detail {
class StatelessIfOpGenericAdaptorBase {
public:
  struct Properties {
    using TcondTy = ::mlir::TypeAttr;
    TcondTy Tcond;

    auto getTcond() {
      auto &propStorage = this->Tcond;
      return ::llvm::dyn_cast_or_null<::mlir::TypeAttr>(propStorage);
    }
    void setTcond(const ::mlir::TypeAttr &propValue) {
      this->Tcond = propValue;
    }
    using TinTy = ::mlir::ArrayAttr;
    TinTy Tin;

    auto getTin() {
      auto &propStorage = this->Tin;
      return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(propStorage);
    }
    void setTin(const ::mlir::ArrayAttr &propValue) {
      this->Tin = propValue;
    }
    using ToutTy = ::mlir::ArrayAttr;
    ToutTy Tout;

    auto getTout() {
      auto &propStorage = this->Tout;
      return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(propStorage);
    }
    void setTout(const ::mlir::ArrayAttr &propValue) {
      this->Tout = propValue;
    }
    using else_branchTy = ::mlir::tf_type::FuncAttr;
    else_branchTy else_branch;

    auto getElseBranch() {
      auto &propStorage = this->else_branch;
      return ::llvm::cast<::mlir::tf_type::FuncAttr>(propStorage);
    }
    void setElseBranch(const ::mlir::tf_type::FuncAttr &propValue) {
      this->else_branch = propValue;
    }
    using output_shapesTy = ::mlir::ArrayAttr;
    output_shapesTy output_shapes;

    auto getOutputShapes() {
      auto &propStorage = this->output_shapes;
      return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(propStorage);
    }
    void setOutputShapes(const ::mlir::ArrayAttr &propValue) {
      this->output_shapes = propValue;
    }
    using then_branchTy = ::mlir::tf_type::FuncAttr;
    then_branchTy then_branch;

    auto getThenBranch() {
      auto &propStorage = this->then_branch;
      return ::llvm::cast<::mlir::tf_type::FuncAttr>(propStorage);
    }
    void setThenBranch(const ::mlir::tf_type::FuncAttr &propValue) {
      this->then_branch = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.Tcond == this->Tcond &&
        rhs.Tin == this->Tin &&
        rhs.Tout == this->Tout &&
        rhs.else_branch == this->else_branch &&
        rhs.output_shapes == this->output_shapes &&
        rhs.then_branch == this->then_branch &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  StatelessIfOpGenericAdaptorBase(::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : odsAttrs(attrs), properties(properties), odsRegions(regions) {  if (odsAttrs)
      odsOpName.emplace("tfg.StatelessIf", odsAttrs.getContext());
  }

  StatelessIfOpGenericAdaptorBase(StatelessIfOp op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes() {
    return odsAttrs;
  }

  ::mlir::tf_type::FuncAttr getThenBranchAttr() {
    auto attr = ::llvm::cast<::mlir::tf_type::FuncAttr>(getProperties().then_branch);
    return attr;
  }

  ::mlir::tf_type::FuncAttr getThenBranch();
  ::mlir::tf_type::FuncAttr getElseBranchAttr() {
    auto attr = ::llvm::cast<::mlir::tf_type::FuncAttr>(getProperties().else_branch);
    return attr;
  }

  ::mlir::tf_type::FuncAttr getElseBranch();
  ::mlir::TypeAttr getTcondAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::TypeAttr>(getProperties().Tcond);
    return attr;
  }

  ::std::optional<::mlir::Type> getTcond();
  ::mlir::ArrayAttr getTinAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().Tin);
    return attr;
  }

  ::std::optional< ::mlir::ArrayAttr > getTin();
  ::mlir::ArrayAttr getToutAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().Tout);
    return attr;
  }

  ::std::optional< ::mlir::ArrayAttr > getTout();
  ::mlir::ArrayAttr getOutputShapesAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().output_shapes);
    return attr;
  }

  ::std::optional< ::mlir::ArrayAttr > getOutputShapes();
};
} // namespace detail
template <typename RangeT>
class StatelessIfOpGenericAdaptor : public detail::StatelessIfOpGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::StatelessIfOpGenericAdaptorBase;
public:
  StatelessIfOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  StatelessIfOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : StatelessIfOpGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  StatelessIfOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr) : StatelessIfOpGenericAdaptor(values, attrs, Properties{}, {}) {}

  StatelessIfOpGenericAdaptor(RangeT values, const StatelessIfOpGenericAdaptorBase &base) : Base(base), odsOperands(values) {}

  template <typename LateInst = StatelessIfOp, typename = std::enable_if_t<std::is_same_v<LateInst, StatelessIfOp>>>
  StatelessIfOpGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getCond() {
    return (*getODSOperands(0).begin());
  }

  RangeT getArgs() {
    return getODSOperands(1);
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class StatelessIfOpAdaptor : public StatelessIfOpGenericAdaptor<::mlir::ValueRange> {
public:
  using StatelessIfOpGenericAdaptor::StatelessIfOpGenericAdaptor;
  StatelessIfOpAdaptor(StatelessIfOp op);

  ::llvm::LogicalResult verify(::mlir::Location loc);
};
class StatelessIfOp : public ::mlir::Op<StatelessIfOp, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::AtLeastNResults<1>::Impl, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::AtLeastNOperands<1>::Impl, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait, ::mlir::SymbolUserOpInterface::Trait, ::mlir::OpAsmOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = StatelessIfOpAdaptor;
  template <typename RangeT>
  using GenericAdaptor = StatelessIfOpGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("Tcond"), ::llvm::StringRef("Tin"), ::llvm::StringRef("Tout"), ::llvm::StringRef("else_branch"), ::llvm::StringRef("output_shapes"), ::llvm::StringRef("then_branch")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getTcondAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getTcondAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  ::mlir::StringAttr getTinAttrName() {
    return getAttributeNameForIndex(1);
  }

  static ::mlir::StringAttr getTinAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 1);
  }

  ::mlir::StringAttr getToutAttrName() {
    return getAttributeNameForIndex(2);
  }

  static ::mlir::StringAttr getToutAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 2);
  }

  ::mlir::StringAttr getElseBranchAttrName() {
    return getAttributeNameForIndex(3);
  }

  static ::mlir::StringAttr getElseBranchAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 3);
  }

  ::mlir::StringAttr getOutputShapesAttrName() {
    return getAttributeNameForIndex(4);
  }

  static ::mlir::StringAttr getOutputShapesAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 4);
  }

  ::mlir::StringAttr getThenBranchAttrName() {
    return getAttributeNameForIndex(5);
  }

  static ::mlir::StringAttr getThenBranchAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 5);
  }

  void getAsmResultNames(::mlir::OpAsmSetValueNameFn setNameFn);
  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("tfg.StatelessIf");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(getOperation()->operand_begin(), valueRange.first),
             std::next(getOperation()->operand_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::TypedValue<::mlir::Type> getCond() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::Type>>(*getODSOperands(0).begin());
  }

  ::mlir::Operation::operand_range getArgs() {
    return getODSOperands(1);
  }

  ::mlir::OpOperand &getCondMutable() {
    auto range = getODSOperandIndexAndLength(0);
    return getOperation()->getOpOperand(range.first);
  }

  ::mlir::MutableOperandRange getArgsMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index) {
    auto valueRange = getODSResultIndexAndLength(index);
    return {std::next(getOperation()->result_begin(), valueRange.first),
             std::next(getOperation()->result_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::Operation::result_range getOuts() {
    return getODSResults(0);
  }

  ::mlir::TypedValue<::mlir::tf_type::ControlType> getCtl() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::tf_type::ControlType>>(*getODSResults(1).begin());
  }

  static ::llvm::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::llvm::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::llvm::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::tf_type::FuncAttr getThenBranchAttr() {
    return ::llvm::cast<::mlir::tf_type::FuncAttr>(getProperties().then_branch);
  }

  ::mlir::tf_type::FuncAttr getThenBranch();
  ::mlir::tf_type::FuncAttr getElseBranchAttr() {
    return ::llvm::cast<::mlir::tf_type::FuncAttr>(getProperties().else_branch);
  }

  ::mlir::tf_type::FuncAttr getElseBranch();
  ::mlir::TypeAttr getTcondAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::TypeAttr>(getProperties().Tcond);
  }

  ::std::optional<::mlir::Type> getTcond();
  ::mlir::ArrayAttr getTinAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().Tin);
  }

  ::std::optional< ::mlir::ArrayAttr > getTin();
  ::mlir::ArrayAttr getToutAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().Tout);
  }

  ::std::optional< ::mlir::ArrayAttr > getTout();
  ::mlir::ArrayAttr getOutputShapesAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().output_shapes);
  }

  ::std::optional< ::mlir::ArrayAttr > getOutputShapes();
  void setThenBranchAttr(::mlir::tf_type::FuncAttr attr) {
    getProperties().then_branch = attr;
  }

  void setElseBranchAttr(::mlir::tf_type::FuncAttr attr) {
    getProperties().else_branch = attr;
  }

  void setTcondAttr(::mlir::TypeAttr attr) {
    getProperties().Tcond = attr;
  }

  void setTcond(::std::optional<::mlir::Type> attrValue);
  void setTinAttr(::mlir::ArrayAttr attr) {
    getProperties().Tin = attr;
  }

  void setToutAttr(::mlir::ArrayAttr attr) {
    getProperties().Tout = attr;
  }

  void setOutputShapesAttr(::mlir::ArrayAttr attr) {
    getProperties().output_shapes = attr;
  }

  ::mlir::Attribute removeTcondAttr() {
      auto &attr = getProperties().Tcond;
      attr = {};
      return attr;
  }

  ::mlir::Attribute removeTinAttr() {
      auto &attr = getProperties().Tin;
      attr = {};
      return attr;
  }

  ::mlir::Attribute removeToutAttr() {
      auto &attr = getProperties().Tout;
      attr = {};
      return attr;
  }

  ::mlir::Attribute removeOutputShapesAttr() {
      auto &attr = getProperties().output_shapes;
      attr = {};
      return attr;
  }

  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, TypeRange results, Value cond, ValueRange args, FuncAttr then_branch, FuncAttr else_branch);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange outs, ::mlir::Type ctl, ::mlir::Value cond, ::mlir::ValueRange args, ::mlir::tf_type::FuncAttr then_branch, ::mlir::tf_type::FuncAttr else_branch, /*optional*/::mlir::TypeAttr Tcond, /*optional*/::mlir::ArrayAttr Tin, /*optional*/::mlir::ArrayAttr Tout, /*optional*/::mlir::ArrayAttr output_shapes);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value cond, ::mlir::ValueRange args, ::mlir::tf_type::FuncAttr then_branch, ::mlir::tf_type::FuncAttr else_branch, /*optional*/::mlir::TypeAttr Tcond, /*optional*/::mlir::ArrayAttr Tin, /*optional*/::mlir::ArrayAttr Tout, /*optional*/::mlir::ArrayAttr output_shapes);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::llvm::LogicalResult verifyInvariantsImpl();
  ::llvm::LogicalResult verifyInvariants();
  ::llvm::LogicalResult verifySymbolUses(::mlir::SymbolTableCollection &symbolTable);
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 6 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace tfg
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::tfg::StatelessIfOp)

namespace mlir {
namespace tfg {

//===----------------------------------------------------------------------===//
// ::mlir::tfg::StatelessIfRegionOp declarations
//===----------------------------------------------------------------------===//

namespace detail {
class StatelessIfRegionOpGenericAdaptorBase {
public:
  struct Properties {
    using else_attrsTy = ::mlir::DictionaryAttr;
    else_attrsTy else_attrs;

    auto getElseAttrs() {
      auto &propStorage = this->else_attrs;
      return ::llvm::dyn_cast_or_null<::mlir::DictionaryAttr>(propStorage);
    }
    void setElseAttrs(const ::mlir::DictionaryAttr &propValue) {
      this->else_attrs = propValue;
    }
    using else_region_attrsTy = ::mlir::tfg::RegionAttr;
    else_region_attrsTy else_region_attrs;

    auto getElseRegionAttrs() {
      auto &propStorage = this->else_region_attrs;
      return ::llvm::dyn_cast_or_null<::mlir::tfg::RegionAttr>(propStorage);
    }
    void setElseRegionAttrs(const ::mlir::tfg::RegionAttr &propValue) {
      this->else_region_attrs = propValue;
    }
    using then_attrsTy = ::mlir::DictionaryAttr;
    then_attrsTy then_attrs;

    auto getThenAttrs() {
      auto &propStorage = this->then_attrs;
      return ::llvm::dyn_cast_or_null<::mlir::DictionaryAttr>(propStorage);
    }
    void setThenAttrs(const ::mlir::DictionaryAttr &propValue) {
      this->then_attrs = propValue;
    }
    using then_region_attrsTy = ::mlir::tfg::RegionAttr;
    then_region_attrsTy then_region_attrs;

    auto getThenRegionAttrs() {
      auto &propStorage = this->then_region_attrs;
      return ::llvm::dyn_cast_or_null<::mlir::tfg::RegionAttr>(propStorage);
    }
    void setThenRegionAttrs(const ::mlir::tfg::RegionAttr &propValue) {
      this->then_region_attrs = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.else_attrs == this->else_attrs &&
        rhs.else_region_attrs == this->else_region_attrs &&
        rhs.then_attrs == this->then_attrs &&
        rhs.then_region_attrs == this->then_region_attrs &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  StatelessIfRegionOpGenericAdaptorBase(::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : odsAttrs(attrs), properties(properties), odsRegions(regions) {  if (odsAttrs)
      odsOpName.emplace("tfg.StatelessIfRegion", odsAttrs.getContext());
  }

  StatelessIfRegionOpGenericAdaptorBase(StatelessIfRegionOp op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes() {
    return odsAttrs;
  }

  ::mlir::DictionaryAttr getThenAttrsAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::DictionaryAttr>(getProperties().then_attrs);
    return attr;
  }

  ::std::optional< ::mlir::DictionaryAttr > getThenAttrs();
  ::mlir::DictionaryAttr getElseAttrsAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::DictionaryAttr>(getProperties().else_attrs);
    return attr;
  }

  ::std::optional< ::mlir::DictionaryAttr > getElseAttrs();
  ::mlir::tfg::RegionAttr getThenRegionAttrsAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::tfg::RegionAttr>(getProperties().then_region_attrs);
    return attr;
  }

  ::std::optional<::mlir::tfg::RegionAttr> getThenRegionAttrs();
  ::mlir::tfg::RegionAttr getElseRegionAttrsAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::tfg::RegionAttr>(getProperties().else_region_attrs);
    return attr;
  }

  ::std::optional<::mlir::tfg::RegionAttr> getElseRegionAttrs();
  ::mlir::Region &getThenRegion() {
    return *odsRegions[0];
  }

  ::mlir::Region &getElseRegion() {
    return *odsRegions[1];
  }

  ::mlir::RegionRange getRegions() {
    return odsRegions;
  }

};
} // namespace detail
template <typename RangeT>
class StatelessIfRegionOpGenericAdaptor : public detail::StatelessIfRegionOpGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::StatelessIfRegionOpGenericAdaptorBase;
public:
  StatelessIfRegionOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  StatelessIfRegionOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : StatelessIfRegionOpGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  StatelessIfRegionOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr) : StatelessIfRegionOpGenericAdaptor(values, attrs, Properties{}, {}) {}

  StatelessIfRegionOpGenericAdaptor(RangeT values, const StatelessIfRegionOpGenericAdaptorBase &base) : Base(base), odsOperands(values) {}

  template <typename LateInst = StatelessIfRegionOp, typename = std::enable_if_t<std::is_same_v<LateInst, StatelessIfRegionOp>>>
  StatelessIfRegionOpGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getCond() {
    return (*getODSOperands(0).begin());
  }

  RangeT getCtls() {
    return getODSOperands(1);
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class StatelessIfRegionOpAdaptor : public StatelessIfRegionOpGenericAdaptor<::mlir::ValueRange> {
public:
  using StatelessIfRegionOpGenericAdaptor::StatelessIfRegionOpGenericAdaptor;
  StatelessIfRegionOpAdaptor(StatelessIfRegionOp op);

  ::llvm::LogicalResult verify(::mlir::Location loc);
};
class StatelessIfRegionOp : public ::mlir::Op<StatelessIfRegionOp, ::mlir::OpTrait::NRegions<2>::Impl, ::mlir::OpTrait::AtLeastNResults<1>::Impl, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::AtLeastNOperands<1>::Impl, ::mlir::OpTrait::SingleBlock, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait, ::mlir::OpTrait::HasOnlyGraphRegion, ::mlir::RegionKindInterface::Trait, ::mlir::OpAsmOpInterface::Trait, ::mlir::tfg::PreservedAttributesInterface::Trait, ::mlir::RegionBranchOpInterface::Trait, ::mlir::tfg::ControlArgumentInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = StatelessIfRegionOpAdaptor;
  template <typename RangeT>
  using GenericAdaptor = StatelessIfRegionOpGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("else_attrs"), ::llvm::StringRef("else_region_attrs"), ::llvm::StringRef("then_attrs"), ::llvm::StringRef("then_region_attrs")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getElseAttrsAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getElseAttrsAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  ::mlir::StringAttr getElseRegionAttrsAttrName() {
    return getAttributeNameForIndex(1);
  }

  static ::mlir::StringAttr getElseRegionAttrsAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 1);
  }

  ::mlir::StringAttr getThenAttrsAttrName() {
    return getAttributeNameForIndex(2);
  }

  static ::mlir::StringAttr getThenAttrsAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 2);
  }

  ::mlir::StringAttr getThenRegionAttrsAttrName() {
    return getAttributeNameForIndex(3);
  }

  static ::mlir::StringAttr getThenRegionAttrsAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 3);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("tfg.StatelessIfRegion");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(getOperation()->operand_begin(), valueRange.first),
             std::next(getOperation()->operand_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::TypedValue<::mlir::TensorType> getCond() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::TensorType>>(*getODSOperands(0).begin());
  }

  ::mlir::Operation::operand_range getCtls() {
    return getODSOperands(1);
  }

  ::mlir::OpOperand &getCondMutable() {
    auto range = getODSOperandIndexAndLength(0);
    return getOperation()->getOpOperand(range.first);
  }

  ::mlir::MutableOperandRange getCtlsMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index) {
    auto valueRange = getODSResultIndexAndLength(index);
    return {std::next(getOperation()->result_begin(), valueRange.first),
             std::next(getOperation()->result_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::Operation::result_range getOuts() {
    return getODSResults(0);
  }

  ::mlir::TypedValue<::mlir::tf_type::ControlType> getCtl() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::tf_type::ControlType>>(*getODSResults(1).begin());
  }

  ::mlir::Region &getThenRegion() {
    return (*this)->getRegion(0);
  }

  ::mlir::Region &getElseRegion() {
    return (*this)->getRegion(1);
  }

  static ::llvm::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::llvm::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::llvm::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::DictionaryAttr getThenAttrsAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::DictionaryAttr>(getProperties().then_attrs);
  }

  ::std::optional< ::mlir::DictionaryAttr > getThenAttrs();
  ::mlir::DictionaryAttr getElseAttrsAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::DictionaryAttr>(getProperties().else_attrs);
  }

  ::std::optional< ::mlir::DictionaryAttr > getElseAttrs();
  ::mlir::tfg::RegionAttr getThenRegionAttrsAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::tfg::RegionAttr>(getProperties().then_region_attrs);
  }

  ::std::optional<::mlir::tfg::RegionAttr> getThenRegionAttrs();
  ::mlir::tfg::RegionAttr getElseRegionAttrsAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::tfg::RegionAttr>(getProperties().else_region_attrs);
  }

  ::std::optional<::mlir::tfg::RegionAttr> getElseRegionAttrs();
  void setThenAttrsAttr(::mlir::DictionaryAttr attr) {
    getProperties().then_attrs = attr;
  }

  void setElseAttrsAttr(::mlir::DictionaryAttr attr) {
    getProperties().else_attrs = attr;
  }

  void setThenRegionAttrsAttr(::mlir::tfg::RegionAttr attr) {
    getProperties().then_region_attrs = attr;
  }

  void setElseRegionAttrsAttr(::mlir::tfg::RegionAttr attr) {
    getProperties().else_region_attrs = attr;
  }

  ::mlir::Attribute removeThenAttrsAttr() {
      auto &attr = getProperties().then_attrs;
      attr = {};
      return attr;
  }

  ::mlir::Attribute removeElseAttrsAttr() {
      auto &attr = getProperties().else_attrs;
      attr = {};
      return attr;
  }

  ::mlir::Attribute removeThenRegionAttrsAttr() {
      auto &attr = getProperties().then_region_attrs;
      attr = {};
      return attr;
  }

  ::mlir::Attribute removeElseRegionAttrsAttr() {
      auto &attr = getProperties().else_region_attrs;
      attr = {};
      return attr;
  }

  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange outs, ::mlir::Type ctl, ::mlir::Value cond, ::mlir::ValueRange ctls, /*optional*/::mlir::DictionaryAttr then_attrs, /*optional*/::mlir::DictionaryAttr else_attrs, /*optional*/::mlir::tfg::RegionAttr then_region_attrs, /*optional*/::mlir::tfg::RegionAttr else_region_attrs);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value cond, ::mlir::ValueRange ctls, /*optional*/::mlir::DictionaryAttr then_attrs, /*optional*/::mlir::DictionaryAttr else_attrs, /*optional*/::mlir::tfg::RegionAttr then_region_attrs, /*optional*/::mlir::tfg::RegionAttr else_region_attrs);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::llvm::LogicalResult verifyInvariantsImpl();
  ::llvm::LogicalResult verifyInvariants();
  ::llvm::LogicalResult verify();
  void getAsmResultNames(::mlir::OpAsmSetValueNameFn setNameFn);
  static ::llvm::StringRef getDefaultDialect();
  void getEntrySuccessorRegions(::llvm::ArrayRef<::mlir::Attribute> operands, ::llvm::SmallVectorImpl<::mlir::RegionSuccessor> &regions);
  void getSuccessorRegions(::mlir::RegionBranchPoint point, ::llvm::SmallVectorImpl<::mlir::RegionSuccessor> &regions);
  void getRegionInvocationBounds(::llvm::ArrayRef<::mlir::Attribute> operands, ::llvm::SmallVectorImpl<::mlir::InvocationBounds> &invocationBounds);
  static mlir::BlockArgument getDataValueOf(BlockArgument ctl);
  static mlir::BlockArgument getControlTokenOf(BlockArgument data);
  static mlir::BlockArgument getDataValue(Region &region, unsigned idx);
  static mlir::BlockArgument getControlToken(Region &region, unsigned idx);
  static ::mlir::ParseResult parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result);
  void print(::mlir::OpAsmPrinter &_odsPrinter);
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 4 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
  Block &getThenBlock() { return getThenRegion().front(); }
  Block &getElseBlock() { return getElseRegion().front(); }
  YieldOp getThenYield();
  YieldOp getElseYield();

  bool areTypesCompatible(Type lhs, Type rhs) {
    return tf_type::HasCompatibleElementTypes(lhs, rhs);
  }

  RegionAttr getPreservedAttrs(unsigned index) {
    if (index == 0) return getThenRegionAttrsAttr();
    return getElseRegionAttrsAttr();
  }
  void setPreservedAttrs(unsigned index, RegionAttr attrs) {
    if (index == 0) setThenRegionAttrsAttr(attrs);
    else setElseRegionAttrsAttr(attrs);
  }
};
} // namespace tfg
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::tfg::StatelessIfRegionOp)

namespace mlir {
namespace tfg {

//===----------------------------------------------------------------------===//
// ::mlir::tfg::StatelessWhileOp declarations
//===----------------------------------------------------------------------===//

namespace detail {
class StatelessWhileOpGenericAdaptorBase {
public:
  struct Properties {
    using TTy = ::mlir::ArrayAttr;
    TTy T;

    auto getT() {
      auto &propStorage = this->T;
      return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(propStorage);
    }
    void setT(const ::mlir::ArrayAttr &propValue) {
      this->T = propValue;
    }
    using bodyTy = ::mlir::tf_type::FuncAttr;
    bodyTy body;

    auto getBody() {
      auto &propStorage = this->body;
      return ::llvm::cast<::mlir::tf_type::FuncAttr>(propStorage);
    }
    void setBody(const ::mlir::tf_type::FuncAttr &propValue) {
      this->body = propValue;
    }
    using condTy = ::mlir::tf_type::FuncAttr;
    condTy cond;

    auto getCond() {
      auto &propStorage = this->cond;
      return ::llvm::cast<::mlir::tf_type::FuncAttr>(propStorage);
    }
    void setCond(const ::mlir::tf_type::FuncAttr &propValue) {
      this->cond = propValue;
    }
    using output_shapesTy = ::mlir::ArrayAttr;
    output_shapesTy output_shapes;

    auto getOutputShapes() {
      auto &propStorage = this->output_shapes;
      return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(propStorage);
    }
    void setOutputShapes(const ::mlir::ArrayAttr &propValue) {
      this->output_shapes = propValue;
    }
    using parallel_iterationsTy = ::mlir::IntegerAttr;
    parallel_iterationsTy parallel_iterations;

    auto getParallelIterations() {
      auto &propStorage = this->parallel_iterations;
      return ::llvm::cast<::mlir::IntegerAttr>(propStorage);
    }
    void setParallelIterations(const ::mlir::IntegerAttr &propValue) {
      this->parallel_iterations = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.T == this->T &&
        rhs.body == this->body &&
        rhs.cond == this->cond &&
        rhs.output_shapes == this->output_shapes &&
        rhs.parallel_iterations == this->parallel_iterations &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  StatelessWhileOpGenericAdaptorBase(::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : odsAttrs(attrs), properties(properties), odsRegions(regions) {  if (odsAttrs)
      odsOpName.emplace("tfg.StatelessWhile", odsAttrs.getContext());
  }

  StatelessWhileOpGenericAdaptorBase(StatelessWhileOp op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes() {
    return odsAttrs;
  }

  ::mlir::tf_type::FuncAttr getCondAttr() {
    auto attr = ::llvm::cast<::mlir::tf_type::FuncAttr>(getProperties().cond);
    return attr;
  }

  ::mlir::tf_type::FuncAttr getCond();
  ::mlir::tf_type::FuncAttr getBodyAttr() {
    auto attr = ::llvm::cast<::mlir::tf_type::FuncAttr>(getProperties().body);
    return attr;
  }

  ::mlir::tf_type::FuncAttr getBody();
  ::mlir::IntegerAttr getParallelIterationsAttr() {
    auto attr = ::llvm::cast<::mlir::IntegerAttr>(getProperties().parallel_iterations);
    return attr;
  }

  uint64_t getParallelIterations();
  ::mlir::ArrayAttr getTAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().T);
    return attr;
  }

  ::std::optional< ::mlir::ArrayAttr > getT();
  ::mlir::ArrayAttr getOutputShapesAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().output_shapes);
    return attr;
  }

  ::std::optional< ::mlir::ArrayAttr > getOutputShapes();
};
} // namespace detail
template <typename RangeT>
class StatelessWhileOpGenericAdaptor : public detail::StatelessWhileOpGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::StatelessWhileOpGenericAdaptorBase;
public:
  StatelessWhileOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  StatelessWhileOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : StatelessWhileOpGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  StatelessWhileOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr) : StatelessWhileOpGenericAdaptor(values, attrs, Properties{}, {}) {}

  StatelessWhileOpGenericAdaptor(RangeT values, const StatelessWhileOpGenericAdaptorBase &base) : Base(base), odsOperands(values) {}

  template <typename LateInst = StatelessWhileOp, typename = std::enable_if_t<std::is_same_v<LateInst, StatelessWhileOp>>>
  StatelessWhileOpGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  RangeT getArgs() {
    return getODSOperands(0);
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class StatelessWhileOpAdaptor : public StatelessWhileOpGenericAdaptor<::mlir::ValueRange> {
public:
  using StatelessWhileOpGenericAdaptor::StatelessWhileOpGenericAdaptor;
  StatelessWhileOpAdaptor(StatelessWhileOp op);

  ::llvm::LogicalResult verify(::mlir::Location loc);
};
class StatelessWhileOp : public ::mlir::Op<StatelessWhileOp, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::AtLeastNResults<1>::Impl, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::VariadicOperands, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait, ::mlir::SymbolUserOpInterface::Trait, ::mlir::OpAsmOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = StatelessWhileOpAdaptor;
  template <typename RangeT>
  using GenericAdaptor = StatelessWhileOpGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("T"), ::llvm::StringRef("body"), ::llvm::StringRef("cond"), ::llvm::StringRef("output_shapes"), ::llvm::StringRef("parallel_iterations")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getTAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getTAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  ::mlir::StringAttr getBodyAttrName() {
    return getAttributeNameForIndex(1);
  }

  static ::mlir::StringAttr getBodyAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 1);
  }

  ::mlir::StringAttr getCondAttrName() {
    return getAttributeNameForIndex(2);
  }

  static ::mlir::StringAttr getCondAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 2);
  }

  ::mlir::StringAttr getOutputShapesAttrName() {
    return getAttributeNameForIndex(3);
  }

  static ::mlir::StringAttr getOutputShapesAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 3);
  }

  ::mlir::StringAttr getParallelIterationsAttrName() {
    return getAttributeNameForIndex(4);
  }

  static ::mlir::StringAttr getParallelIterationsAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 4);
  }

  void getAsmResultNames(::mlir::OpAsmSetValueNameFn setNameFn);
  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("tfg.StatelessWhile");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(getOperation()->operand_begin(), valueRange.first),
             std::next(getOperation()->operand_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::Operation::operand_range getArgs() {
    return getODSOperands(0);
  }

  ::mlir::MutableOperandRange getArgsMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index) {
    auto valueRange = getODSResultIndexAndLength(index);
    return {std::next(getOperation()->result_begin(), valueRange.first),
             std::next(getOperation()->result_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::Operation::result_range getOuts() {
    return getODSResults(0);
  }

  ::mlir::TypedValue<::mlir::tf_type::ControlType> getCtl() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::tf_type::ControlType>>(*getODSResults(1).begin());
  }

  static ::llvm::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::llvm::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::llvm::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::tf_type::FuncAttr getCondAttr() {
    return ::llvm::cast<::mlir::tf_type::FuncAttr>(getProperties().cond);
  }

  ::mlir::tf_type::FuncAttr getCond();
  ::mlir::tf_type::FuncAttr getBodyAttr() {
    return ::llvm::cast<::mlir::tf_type::FuncAttr>(getProperties().body);
  }

  ::mlir::tf_type::FuncAttr getBody();
  ::mlir::IntegerAttr getParallelIterationsAttr() {
    return ::llvm::cast<::mlir::IntegerAttr>(getProperties().parallel_iterations);
  }

  uint64_t getParallelIterations();
  ::mlir::ArrayAttr getTAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().T);
  }

  ::std::optional< ::mlir::ArrayAttr > getT();
  ::mlir::ArrayAttr getOutputShapesAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().output_shapes);
  }

  ::std::optional< ::mlir::ArrayAttr > getOutputShapes();
  void setCondAttr(::mlir::tf_type::FuncAttr attr) {
    getProperties().cond = attr;
  }

  void setBodyAttr(::mlir::tf_type::FuncAttr attr) {
    getProperties().body = attr;
  }

  void setParallelIterationsAttr(::mlir::IntegerAttr attr) {
    getProperties().parallel_iterations = attr;
  }

  void setParallelIterations(uint64_t attrValue);
  void setTAttr(::mlir::ArrayAttr attr) {
    getProperties().T = attr;
  }

  void setOutputShapesAttr(::mlir::ArrayAttr attr) {
    getProperties().output_shapes = attr;
  }

  ::mlir::Attribute removeTAttr() {
      auto &attr = getProperties().T;
      attr = {};
      return attr;
  }

  ::mlir::Attribute removeOutputShapesAttr() {
      auto &attr = getProperties().output_shapes;
      attr = {};
      return attr;
  }

  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, TypeRange results, ValueRange args, FuncAttr cond, FuncAttr body, IntegerAttr parallel_iterations);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange outs, ::mlir::Type ctl, ::mlir::ValueRange args, ::mlir::tf_type::FuncAttr cond, ::mlir::tf_type::FuncAttr body, ::mlir::IntegerAttr parallel_iterations, /*optional*/::mlir::ArrayAttr T, /*optional*/::mlir::ArrayAttr output_shapes);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange args, ::mlir::tf_type::FuncAttr cond, ::mlir::tf_type::FuncAttr body, ::mlir::IntegerAttr parallel_iterations, /*optional*/::mlir::ArrayAttr T, /*optional*/::mlir::ArrayAttr output_shapes);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange outs, ::mlir::Type ctl, ::mlir::ValueRange args, ::mlir::tf_type::FuncAttr cond, ::mlir::tf_type::FuncAttr body, uint64_t parallel_iterations, /*optional*/::mlir::ArrayAttr T, /*optional*/::mlir::ArrayAttr output_shapes);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange args, ::mlir::tf_type::FuncAttr cond, ::mlir::tf_type::FuncAttr body, uint64_t parallel_iterations, /*optional*/::mlir::ArrayAttr T, /*optional*/::mlir::ArrayAttr output_shapes);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::llvm::LogicalResult verifyInvariantsImpl();
  ::llvm::LogicalResult verifyInvariants();
  ::llvm::LogicalResult verifySymbolUses(::mlir::SymbolTableCollection &symbolTable);
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 5 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace tfg
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::tfg::StatelessWhileOp)

namespace mlir {
namespace tfg {

//===----------------------------------------------------------------------===//
// ::mlir::tfg::StatelessWhileRegionOp declarations
//===----------------------------------------------------------------------===//

namespace detail {
class StatelessWhileRegionOpGenericAdaptorBase {
public:
  struct Properties {
    using body_attrsTy = ::mlir::DictionaryAttr;
    body_attrsTy body_attrs;

    auto getBodyAttrs() {
      auto &propStorage = this->body_attrs;
      return ::llvm::dyn_cast_or_null<::mlir::DictionaryAttr>(propStorage);
    }
    void setBodyAttrs(const ::mlir::DictionaryAttr &propValue) {
      this->body_attrs = propValue;
    }
    using body_region_attrsTy = ::mlir::tfg::RegionAttr;
    body_region_attrsTy body_region_attrs;

    auto getBodyRegionAttrs() {
      auto &propStorage = this->body_region_attrs;
      return ::llvm::dyn_cast_or_null<::mlir::tfg::RegionAttr>(propStorage);
    }
    void setBodyRegionAttrs(const ::mlir::tfg::RegionAttr &propValue) {
      this->body_region_attrs = propValue;
    }
    using cond_attrsTy = ::mlir::DictionaryAttr;
    cond_attrsTy cond_attrs;

    auto getCondAttrs() {
      auto &propStorage = this->cond_attrs;
      return ::llvm::dyn_cast_or_null<::mlir::DictionaryAttr>(propStorage);
    }
    void setCondAttrs(const ::mlir::DictionaryAttr &propValue) {
      this->cond_attrs = propValue;
    }
    using cond_region_attrsTy = ::mlir::tfg::RegionAttr;
    cond_region_attrsTy cond_region_attrs;

    auto getCondRegionAttrs() {
      auto &propStorage = this->cond_region_attrs;
      return ::llvm::dyn_cast_or_null<::mlir::tfg::RegionAttr>(propStorage);
    }
    void setCondRegionAttrs(const ::mlir::tfg::RegionAttr &propValue) {
      this->cond_region_attrs = propValue;
    }
    using parallel_iterationsTy = ::mlir::IntegerAttr;
    parallel_iterationsTy parallel_iterations;

    auto getParallelIterations() {
      auto &propStorage = this->parallel_iterations;
      return ::llvm::cast<::mlir::IntegerAttr>(propStorage);
    }
    void setParallelIterations(const ::mlir::IntegerAttr &propValue) {
      this->parallel_iterations = propValue;
    }
    using operandSegmentSizesTy = std::array<int32_t, 2>;
    operandSegmentSizesTy operandSegmentSizes;
    ::llvm::ArrayRef<int32_t> getOperandSegmentSizes() const {
      auto &propStorage = this->operandSegmentSizes;
      return propStorage;
    }
    void setOperandSegmentSizes(::llvm::ArrayRef<int32_t> propValue) {
      auto &propStorage = this->operandSegmentSizes;
      ::llvm::copy(propValue, propStorage.begin());
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.body_attrs == this->body_attrs &&
        rhs.body_region_attrs == this->body_region_attrs &&
        rhs.cond_attrs == this->cond_attrs &&
        rhs.cond_region_attrs == this->cond_region_attrs &&
        rhs.parallel_iterations == this->parallel_iterations &&
        rhs.operandSegmentSizes == this->operandSegmentSizes &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  StatelessWhileRegionOpGenericAdaptorBase(::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : odsAttrs(attrs), properties(properties), odsRegions(regions) {  if (odsAttrs)
      odsOpName.emplace("tfg.StatelessWhileRegion", odsAttrs.getContext());
  }

  StatelessWhileRegionOpGenericAdaptorBase(StatelessWhileRegionOp op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes() {
    return odsAttrs;
  }

  ::mlir::IntegerAttr getParallelIterationsAttr() {
    auto attr = ::llvm::cast<::mlir::IntegerAttr>(getProperties().parallel_iterations);
    return attr;
  }

  uint64_t getParallelIterations();
  ::mlir::DictionaryAttr getCondAttrsAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::DictionaryAttr>(getProperties().cond_attrs);
    return attr;
  }

  ::std::optional< ::mlir::DictionaryAttr > getCondAttrs();
  ::mlir::DictionaryAttr getBodyAttrsAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::DictionaryAttr>(getProperties().body_attrs);
    return attr;
  }

  ::std::optional< ::mlir::DictionaryAttr > getBodyAttrs();
  ::mlir::tfg::RegionAttr getCondRegionAttrsAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::tfg::RegionAttr>(getProperties().cond_region_attrs);
    return attr;
  }

  ::std::optional<::mlir::tfg::RegionAttr> getCondRegionAttrs();
  ::mlir::tfg::RegionAttr getBodyRegionAttrsAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::tfg::RegionAttr>(getProperties().body_region_attrs);
    return attr;
  }

  ::std::optional<::mlir::tfg::RegionAttr> getBodyRegionAttrs();
  ::mlir::Region &getCondRegion() {
    return *odsRegions[0];
  }

  ::mlir::Region &getBodyRegion() {
    return *odsRegions[1];
  }

  ::mlir::RegionRange getRegions() {
    return odsRegions;
  }

};
} // namespace detail
template <typename RangeT>
class StatelessWhileRegionOpGenericAdaptor : public detail::StatelessWhileRegionOpGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::StatelessWhileRegionOpGenericAdaptorBase;
public:
  StatelessWhileRegionOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  StatelessWhileRegionOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : StatelessWhileRegionOpGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  StatelessWhileRegionOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs) : StatelessWhileRegionOpGenericAdaptor(values, attrs, Properties{}, {}) {}

  StatelessWhileRegionOpGenericAdaptor(RangeT values, const StatelessWhileRegionOpGenericAdaptorBase &base) : Base(base), odsOperands(values) {}

  template <typename LateInst = StatelessWhileRegionOp, typename = std::enable_if_t<std::is_same_v<LateInst, StatelessWhileRegionOp>>>
  StatelessWhileRegionOpGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  RangeT getInit() {
    return getODSOperands(0);
  }

  RangeT getCtls() {
    return getODSOperands(1);
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class StatelessWhileRegionOpAdaptor : public StatelessWhileRegionOpGenericAdaptor<::mlir::ValueRange> {
public:
  using StatelessWhileRegionOpGenericAdaptor::StatelessWhileRegionOpGenericAdaptor;
  StatelessWhileRegionOpAdaptor(StatelessWhileRegionOp op);

  ::llvm::LogicalResult verify(::mlir::Location loc);
};
class StatelessWhileRegionOp : public ::mlir::Op<StatelessWhileRegionOp, ::mlir::OpTrait::NRegions<2>::Impl, ::mlir::OpTrait::AtLeastNResults<1>::Impl, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::VariadicOperands, ::mlir::OpTrait::SingleBlock, ::mlir::OpTrait::AttrSizedOperandSegments, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait, ::mlir::OpTrait::HasOnlyGraphRegion, ::mlir::RegionKindInterface::Trait, ::mlir::OpAsmOpInterface::Trait, ::mlir::tfg::PreservedAttributesInterface::Trait, ::mlir::RegionBranchOpInterface::Trait, ::mlir::tfg::ControlArgumentInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = StatelessWhileRegionOpAdaptor;
  template <typename RangeT>
  using GenericAdaptor = StatelessWhileRegionOpGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("body_attrs"), ::llvm::StringRef("body_region_attrs"), ::llvm::StringRef("cond_attrs"), ::llvm::StringRef("cond_region_attrs"), ::llvm::StringRef("parallel_iterations"), ::llvm::StringRef("operandSegmentSizes")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getBodyAttrsAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getBodyAttrsAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  ::mlir::StringAttr getBodyRegionAttrsAttrName() {
    return getAttributeNameForIndex(1);
  }

  static ::mlir::StringAttr getBodyRegionAttrsAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 1);
  }

  ::mlir::StringAttr getCondAttrsAttrName() {
    return getAttributeNameForIndex(2);
  }

  static ::mlir::StringAttr getCondAttrsAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 2);
  }

  ::mlir::StringAttr getCondRegionAttrsAttrName() {
    return getAttributeNameForIndex(3);
  }

  static ::mlir::StringAttr getCondRegionAttrsAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 3);
  }

  ::mlir::StringAttr getParallelIterationsAttrName() {
    return getAttributeNameForIndex(4);
  }

  static ::mlir::StringAttr getParallelIterationsAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 4);
  }

  ::mlir::StringAttr getOperandSegmentSizesAttrName() {
   return (*this)->getName().getAttributeNames().back();
  }

  static ::mlir::StringAttr getOperandSegmentSizesAttrName(::mlir::OperationName name) {
   return name.getAttributeNames().back();
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("tfg.StatelessWhileRegion");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(getOperation()->operand_begin(), valueRange.first),
             std::next(getOperation()->operand_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::Operation::operand_range getInit() {
    return getODSOperands(0);
  }

  ::mlir::Operation::operand_range getCtls() {
    return getODSOperands(1);
  }

  ::mlir::MutableOperandRange getInitMutable();
  ::mlir::MutableOperandRange getCtlsMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index) {
    auto valueRange = getODSResultIndexAndLength(index);
    return {std::next(getOperation()->result_begin(), valueRange.first),
             std::next(getOperation()->result_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::Operation::result_range getOuts() {
    return getODSResults(0);
  }

  ::mlir::TypedValue<::mlir::tf_type::ControlType> getCtl() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::tf_type::ControlType>>(*getODSResults(1).begin());
  }

  ::mlir::Region &getCondRegion() {
    return (*this)->getRegion(0);
  }

  ::mlir::Region &getBodyRegion() {
    return (*this)->getRegion(1);
  }

  static ::llvm::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::llvm::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::llvm::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::IntegerAttr getParallelIterationsAttr() {
    return ::llvm::cast<::mlir::IntegerAttr>(getProperties().parallel_iterations);
  }

  uint64_t getParallelIterations();
  ::mlir::DictionaryAttr getCondAttrsAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::DictionaryAttr>(getProperties().cond_attrs);
  }

  ::std::optional< ::mlir::DictionaryAttr > getCondAttrs();
  ::mlir::DictionaryAttr getBodyAttrsAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::DictionaryAttr>(getProperties().body_attrs);
  }

  ::std::optional< ::mlir::DictionaryAttr > getBodyAttrs();
  ::mlir::tfg::RegionAttr getCondRegionAttrsAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::tfg::RegionAttr>(getProperties().cond_region_attrs);
  }

  ::std::optional<::mlir::tfg::RegionAttr> getCondRegionAttrs();
  ::mlir::tfg::RegionAttr getBodyRegionAttrsAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::tfg::RegionAttr>(getProperties().body_region_attrs);
  }

  ::std::optional<::mlir::tfg::RegionAttr> getBodyRegionAttrs();
  void setParallelIterationsAttr(::mlir::IntegerAttr attr) {
    getProperties().parallel_iterations = attr;
  }

  void setParallelIterations(uint64_t attrValue);
  void setCondAttrsAttr(::mlir::DictionaryAttr attr) {
    getProperties().cond_attrs = attr;
  }

  void setBodyAttrsAttr(::mlir::DictionaryAttr attr) {
    getProperties().body_attrs = attr;
  }

  void setCondRegionAttrsAttr(::mlir::tfg::RegionAttr attr) {
    getProperties().cond_region_attrs = attr;
  }

  void setBodyRegionAttrsAttr(::mlir::tfg::RegionAttr attr) {
    getProperties().body_region_attrs = attr;
  }

  ::mlir::Attribute removeCondAttrsAttr() {
      auto &attr = getProperties().cond_attrs;
      attr = {};
      return attr;
  }

  ::mlir::Attribute removeBodyAttrsAttr() {
      auto &attr = getProperties().body_attrs;
      attr = {};
      return attr;
  }

  ::mlir::Attribute removeCondRegionAttrsAttr() {
      auto &attr = getProperties().cond_region_attrs;
      attr = {};
      return attr;
  }

  ::mlir::Attribute removeBodyRegionAttrsAttr() {
      auto &attr = getProperties().body_region_attrs;
      attr = {};
      return attr;
  }

  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange outs, ::mlir::Type ctl, ::mlir::ValueRange init, ::mlir::ValueRange ctls, ::mlir::IntegerAttr parallel_iterations, /*optional*/::mlir::DictionaryAttr cond_attrs, /*optional*/::mlir::DictionaryAttr body_attrs, /*optional*/::mlir::tfg::RegionAttr cond_region_attrs, /*optional*/::mlir::tfg::RegionAttr body_region_attrs);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange init, ::mlir::ValueRange ctls, ::mlir::IntegerAttr parallel_iterations, /*optional*/::mlir::DictionaryAttr cond_attrs, /*optional*/::mlir::DictionaryAttr body_attrs, /*optional*/::mlir::tfg::RegionAttr cond_region_attrs, /*optional*/::mlir::tfg::RegionAttr body_region_attrs);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange outs, ::mlir::Type ctl, ::mlir::ValueRange init, ::mlir::ValueRange ctls, uint64_t parallel_iterations, /*optional*/::mlir::DictionaryAttr cond_attrs, /*optional*/::mlir::DictionaryAttr body_attrs, /*optional*/::mlir::tfg::RegionAttr cond_region_attrs, /*optional*/::mlir::tfg::RegionAttr body_region_attrs);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange init, ::mlir::ValueRange ctls, uint64_t parallel_iterations, /*optional*/::mlir::DictionaryAttr cond_attrs, /*optional*/::mlir::DictionaryAttr body_attrs, /*optional*/::mlir::tfg::RegionAttr cond_region_attrs, /*optional*/::mlir::tfg::RegionAttr body_region_attrs);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::llvm::LogicalResult verifyInvariantsImpl();
  ::llvm::LogicalResult verifyInvariants();
  ::llvm::LogicalResult verify();
  void getAsmResultNames(::mlir::OpAsmSetValueNameFn setNameFn);
  static ::llvm::StringRef getDefaultDialect();
  ::mlir::OperandRange getEntrySuccessorOperands(::mlir::RegionBranchPoint point);
  void getSuccessorRegions(::mlir::RegionBranchPoint point, ::llvm::SmallVectorImpl<::mlir::RegionSuccessor> &regions);
  static mlir::BlockArgument getDataValueOf(BlockArgument ctl);
  static mlir::BlockArgument getControlTokenOf(BlockArgument data);
  static mlir::BlockArgument getDataValue(Region &region, unsigned idx);
  static mlir::BlockArgument getControlToken(Region &region, unsigned idx);
  static ::mlir::ParseResult parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result);
  void print(::mlir::OpAsmPrinter &_odsPrinter);
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 5 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
  Block &getCondBlock() { return getCondRegion().front(); }
  Block &getBodyBlock() { return getBodyRegion().front(); }
  ConditionOp getCondCondition();
  YieldOp getBodyYield();

  bool areTypesCompatible(Type lhs, Type rhs) {
    return tf_type::HasCompatibleElementTypes(lhs, rhs);
  }

  RegionAttr getPreservedAttrs(unsigned index) {
    if (index == 0) return getCondRegionAttrsAttr();
    return getBodyRegionAttrsAttr();
  }
  void setPreservedAttrs(unsigned index, RegionAttr attrs) {
    if (index == 0) setCondRegionAttrsAttr(attrs);
    else setBodyRegionAttrsAttr(attrs);
  }
};
} // namespace tfg
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::tfg::StatelessWhileRegionOp)

namespace mlir {
namespace tfg {

//===----------------------------------------------------------------------===//
// ::mlir::tfg::WhileOp declarations
//===----------------------------------------------------------------------===//

namespace detail {
class WhileOpGenericAdaptorBase {
public:
  struct Properties {
    using TTy = ::mlir::ArrayAttr;
    TTy T;

    auto getT() {
      auto &propStorage = this->T;
      return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(propStorage);
    }
    void setT(const ::mlir::ArrayAttr &propValue) {
      this->T = propValue;
    }
    using bodyTy = ::mlir::tf_type::FuncAttr;
    bodyTy body;

    auto getBody() {
      auto &propStorage = this->body;
      return ::llvm::cast<::mlir::tf_type::FuncAttr>(propStorage);
    }
    void setBody(const ::mlir::tf_type::FuncAttr &propValue) {
      this->body = propValue;
    }
    using condTy = ::mlir::tf_type::FuncAttr;
    condTy cond;

    auto getCond() {
      auto &propStorage = this->cond;
      return ::llvm::cast<::mlir::tf_type::FuncAttr>(propStorage);
    }
    void setCond(const ::mlir::tf_type::FuncAttr &propValue) {
      this->cond = propValue;
    }
    using output_shapesTy = ::mlir::ArrayAttr;
    output_shapesTy output_shapes;

    auto getOutputShapes() {
      auto &propStorage = this->output_shapes;
      return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(propStorage);
    }
    void setOutputShapes(const ::mlir::ArrayAttr &propValue) {
      this->output_shapes = propValue;
    }
    using parallel_iterationsTy = ::mlir::IntegerAttr;
    parallel_iterationsTy parallel_iterations;

    auto getParallelIterations() {
      auto &propStorage = this->parallel_iterations;
      return ::llvm::cast<::mlir::IntegerAttr>(propStorage);
    }
    void setParallelIterations(const ::mlir::IntegerAttr &propValue) {
      this->parallel_iterations = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.T == this->T &&
        rhs.body == this->body &&
        rhs.cond == this->cond &&
        rhs.output_shapes == this->output_shapes &&
        rhs.parallel_iterations == this->parallel_iterations &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  WhileOpGenericAdaptorBase(::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : odsAttrs(attrs), properties(properties), odsRegions(regions) {  if (odsAttrs)
      odsOpName.emplace("tfg.While", odsAttrs.getContext());
  }

  WhileOpGenericAdaptorBase(WhileOp op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes() {
    return odsAttrs;
  }

  ::mlir::tf_type::FuncAttr getCondAttr() {
    auto attr = ::llvm::cast<::mlir::tf_type::FuncAttr>(getProperties().cond);
    return attr;
  }

  ::mlir::tf_type::FuncAttr getCond();
  ::mlir::tf_type::FuncAttr getBodyAttr() {
    auto attr = ::llvm::cast<::mlir::tf_type::FuncAttr>(getProperties().body);
    return attr;
  }

  ::mlir::tf_type::FuncAttr getBody();
  ::mlir::IntegerAttr getParallelIterationsAttr() {
    auto attr = ::llvm::cast<::mlir::IntegerAttr>(getProperties().parallel_iterations);
    return attr;
  }

  uint64_t getParallelIterations();
  ::mlir::ArrayAttr getTAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().T);
    return attr;
  }

  ::std::optional< ::mlir::ArrayAttr > getT();
  ::mlir::ArrayAttr getOutputShapesAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().output_shapes);
    return attr;
  }

  ::std::optional< ::mlir::ArrayAttr > getOutputShapes();
};
} // namespace detail
template <typename RangeT>
class WhileOpGenericAdaptor : public detail::WhileOpGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::WhileOpGenericAdaptorBase;
public:
  WhileOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  WhileOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : WhileOpGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  WhileOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr) : WhileOpGenericAdaptor(values, attrs, Properties{}, {}) {}

  WhileOpGenericAdaptor(RangeT values, const WhileOpGenericAdaptorBase &base) : Base(base), odsOperands(values) {}

  template <typename LateInst = WhileOp, typename = std::enable_if_t<std::is_same_v<LateInst, WhileOp>>>
  WhileOpGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  RangeT getArgs() {
    return getODSOperands(0);
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class WhileOpAdaptor : public WhileOpGenericAdaptor<::mlir::ValueRange> {
public:
  using WhileOpGenericAdaptor::WhileOpGenericAdaptor;
  WhileOpAdaptor(WhileOp op);

  ::llvm::LogicalResult verify(::mlir::Location loc);
};
class WhileOp : public ::mlir::Op<WhileOp, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::AtLeastNResults<1>::Impl, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::VariadicOperands, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait, ::mlir::SymbolUserOpInterface::Trait, ::mlir::OpAsmOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = WhileOpAdaptor;
  template <typename RangeT>
  using GenericAdaptor = WhileOpGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("T"), ::llvm::StringRef("body"), ::llvm::StringRef("cond"), ::llvm::StringRef("output_shapes"), ::llvm::StringRef("parallel_iterations")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getTAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getTAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  ::mlir::StringAttr getBodyAttrName() {
    return getAttributeNameForIndex(1);
  }

  static ::mlir::StringAttr getBodyAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 1);
  }

  ::mlir::StringAttr getCondAttrName() {
    return getAttributeNameForIndex(2);
  }

  static ::mlir::StringAttr getCondAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 2);
  }

  ::mlir::StringAttr getOutputShapesAttrName() {
    return getAttributeNameForIndex(3);
  }

  static ::mlir::StringAttr getOutputShapesAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 3);
  }

  ::mlir::StringAttr getParallelIterationsAttrName() {
    return getAttributeNameForIndex(4);
  }

  static ::mlir::StringAttr getParallelIterationsAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 4);
  }

  void getAsmResultNames(::mlir::OpAsmSetValueNameFn setNameFn);
  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("tfg.While");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(getOperation()->operand_begin(), valueRange.first),
             std::next(getOperation()->operand_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::Operation::operand_range getArgs() {
    return getODSOperands(0);
  }

  ::mlir::MutableOperandRange getArgsMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index) {
    auto valueRange = getODSResultIndexAndLength(index);
    return {std::next(getOperation()->result_begin(), valueRange.first),
             std::next(getOperation()->result_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::Operation::result_range getOuts() {
    return getODSResults(0);
  }

  ::mlir::TypedValue<::mlir::tf_type::ControlType> getCtl() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::tf_type::ControlType>>(*getODSResults(1).begin());
  }

  static ::llvm::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::llvm::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::llvm::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::tf_type::FuncAttr getCondAttr() {
    return ::llvm::cast<::mlir::tf_type::FuncAttr>(getProperties().cond);
  }

  ::mlir::tf_type::FuncAttr getCond();
  ::mlir::tf_type::FuncAttr getBodyAttr() {
    return ::llvm::cast<::mlir::tf_type::FuncAttr>(getProperties().body);
  }

  ::mlir::tf_type::FuncAttr getBody();
  ::mlir::IntegerAttr getParallelIterationsAttr() {
    return ::llvm::cast<::mlir::IntegerAttr>(getProperties().parallel_iterations);
  }

  uint64_t getParallelIterations();
  ::mlir::ArrayAttr getTAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().T);
  }

  ::std::optional< ::mlir::ArrayAttr > getT();
  ::mlir::ArrayAttr getOutputShapesAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().output_shapes);
  }

  ::std::optional< ::mlir::ArrayAttr > getOutputShapes();
  void setCondAttr(::mlir::tf_type::FuncAttr attr) {
    getProperties().cond = attr;
  }

  void setBodyAttr(::mlir::tf_type::FuncAttr attr) {
    getProperties().body = attr;
  }

  void setParallelIterationsAttr(::mlir::IntegerAttr attr) {
    getProperties().parallel_iterations = attr;
  }

  void setParallelIterations(uint64_t attrValue);
  void setTAttr(::mlir::ArrayAttr attr) {
    getProperties().T = attr;
  }

  void setOutputShapesAttr(::mlir::ArrayAttr attr) {
    getProperties().output_shapes = attr;
  }

  ::mlir::Attribute removeTAttr() {
      auto &attr = getProperties().T;
      attr = {};
      return attr;
  }

  ::mlir::Attribute removeOutputShapesAttr() {
      auto &attr = getProperties().output_shapes;
      attr = {};
      return attr;
  }

  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, TypeRange results, ValueRange args, FuncAttr cond, FuncAttr body, IntegerAttr parallel_iterations);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange outs, ::mlir::Type ctl, ::mlir::ValueRange args, ::mlir::tf_type::FuncAttr cond, ::mlir::tf_type::FuncAttr body, ::mlir::IntegerAttr parallel_iterations, /*optional*/::mlir::ArrayAttr T, /*optional*/::mlir::ArrayAttr output_shapes);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange args, ::mlir::tf_type::FuncAttr cond, ::mlir::tf_type::FuncAttr body, ::mlir::IntegerAttr parallel_iterations, /*optional*/::mlir::ArrayAttr T, /*optional*/::mlir::ArrayAttr output_shapes);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange outs, ::mlir::Type ctl, ::mlir::ValueRange args, ::mlir::tf_type::FuncAttr cond, ::mlir::tf_type::FuncAttr body, uint64_t parallel_iterations, /*optional*/::mlir::ArrayAttr T, /*optional*/::mlir::ArrayAttr output_shapes);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange args, ::mlir::tf_type::FuncAttr cond, ::mlir::tf_type::FuncAttr body, uint64_t parallel_iterations, /*optional*/::mlir::ArrayAttr T, /*optional*/::mlir::ArrayAttr output_shapes);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::llvm::LogicalResult verifyInvariantsImpl();
  ::llvm::LogicalResult verifyInvariants();
  ::llvm::LogicalResult verifySymbolUses(::mlir::SymbolTableCollection &symbolTable);
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 5 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace tfg
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::tfg::WhileOp)

namespace mlir {
namespace tfg {

//===----------------------------------------------------------------------===//
// ::mlir::tfg::WhileRegionOp declarations
//===----------------------------------------------------------------------===//

namespace detail {
class WhileRegionOpGenericAdaptorBase {
public:
  struct Properties {
    using body_attrsTy = ::mlir::DictionaryAttr;
    body_attrsTy body_attrs;

    auto getBodyAttrs() {
      auto &propStorage = this->body_attrs;
      return ::llvm::dyn_cast_or_null<::mlir::DictionaryAttr>(propStorage);
    }
    void setBodyAttrs(const ::mlir::DictionaryAttr &propValue) {
      this->body_attrs = propValue;
    }
    using body_region_attrsTy = ::mlir::tfg::RegionAttr;
    body_region_attrsTy body_region_attrs;

    auto getBodyRegionAttrs() {
      auto &propStorage = this->body_region_attrs;
      return ::llvm::dyn_cast_or_null<::mlir::tfg::RegionAttr>(propStorage);
    }
    void setBodyRegionAttrs(const ::mlir::tfg::RegionAttr &propValue) {
      this->body_region_attrs = propValue;
    }
    using cond_attrsTy = ::mlir::DictionaryAttr;
    cond_attrsTy cond_attrs;

    auto getCondAttrs() {
      auto &propStorage = this->cond_attrs;
      return ::llvm::dyn_cast_or_null<::mlir::DictionaryAttr>(propStorage);
    }
    void setCondAttrs(const ::mlir::DictionaryAttr &propValue) {
      this->cond_attrs = propValue;
    }
    using cond_region_attrsTy = ::mlir::tfg::RegionAttr;
    cond_region_attrsTy cond_region_attrs;

    auto getCondRegionAttrs() {
      auto &propStorage = this->cond_region_attrs;
      return ::llvm::dyn_cast_or_null<::mlir::tfg::RegionAttr>(propStorage);
    }
    void setCondRegionAttrs(const ::mlir::tfg::RegionAttr &propValue) {
      this->cond_region_attrs = propValue;
    }
    using parallel_iterationsTy = ::mlir::IntegerAttr;
    parallel_iterationsTy parallel_iterations;

    auto getParallelIterations() {
      auto &propStorage = this->parallel_iterations;
      return ::llvm::cast<::mlir::IntegerAttr>(propStorage);
    }
    void setParallelIterations(const ::mlir::IntegerAttr &propValue) {
      this->parallel_iterations = propValue;
    }
    using operandSegmentSizesTy = std::array<int32_t, 2>;
    operandSegmentSizesTy operandSegmentSizes;
    ::llvm::ArrayRef<int32_t> getOperandSegmentSizes() const {
      auto &propStorage = this->operandSegmentSizes;
      return propStorage;
    }
    void setOperandSegmentSizes(::llvm::ArrayRef<int32_t> propValue) {
      auto &propStorage = this->operandSegmentSizes;
      ::llvm::copy(propValue, propStorage.begin());
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.body_attrs == this->body_attrs &&
        rhs.body_region_attrs == this->body_region_attrs &&
        rhs.cond_attrs == this->cond_attrs &&
        rhs.cond_region_attrs == this->cond_region_attrs &&
        rhs.parallel_iterations == this->parallel_iterations &&
        rhs.operandSegmentSizes == this->operandSegmentSizes &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  WhileRegionOpGenericAdaptorBase(::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : odsAttrs(attrs), properties(properties), odsRegions(regions) {  if (odsAttrs)
      odsOpName.emplace("tfg.WhileRegion", odsAttrs.getContext());
  }

  WhileRegionOpGenericAdaptorBase(WhileRegionOp op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes() {
    return odsAttrs;
  }

  ::mlir::IntegerAttr getParallelIterationsAttr() {
    auto attr = ::llvm::cast<::mlir::IntegerAttr>(getProperties().parallel_iterations);
    return attr;
  }

  uint64_t getParallelIterations();
  ::mlir::DictionaryAttr getCondAttrsAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::DictionaryAttr>(getProperties().cond_attrs);
    return attr;
  }

  ::std::optional< ::mlir::DictionaryAttr > getCondAttrs();
  ::mlir::DictionaryAttr getBodyAttrsAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::DictionaryAttr>(getProperties().body_attrs);
    return attr;
  }

  ::std::optional< ::mlir::DictionaryAttr > getBodyAttrs();
  ::mlir::tfg::RegionAttr getCondRegionAttrsAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::tfg::RegionAttr>(getProperties().cond_region_attrs);
    return attr;
  }

  ::std::optional<::mlir::tfg::RegionAttr> getCondRegionAttrs();
  ::mlir::tfg::RegionAttr getBodyRegionAttrsAttr() {
    auto attr = ::llvm::dyn_cast_or_null<::mlir::tfg::RegionAttr>(getProperties().body_region_attrs);
    return attr;
  }

  ::std::optional<::mlir::tfg::RegionAttr> getBodyRegionAttrs();
  ::mlir::Region &getCondRegion() {
    return *odsRegions[0];
  }

  ::mlir::Region &getBodyRegion() {
    return *odsRegions[1];
  }

  ::mlir::RegionRange getRegions() {
    return odsRegions;
  }

};
} // namespace detail
template <typename RangeT>
class WhileRegionOpGenericAdaptor : public detail::WhileRegionOpGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::WhileRegionOpGenericAdaptorBase;
public:
  WhileRegionOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  WhileRegionOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : WhileRegionOpGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  WhileRegionOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs) : WhileRegionOpGenericAdaptor(values, attrs, Properties{}, {}) {}

  WhileRegionOpGenericAdaptor(RangeT values, const WhileRegionOpGenericAdaptorBase &base) : Base(base), odsOperands(values) {}

  template <typename LateInst = WhileRegionOp, typename = std::enable_if_t<std::is_same_v<LateInst, WhileRegionOp>>>
  WhileRegionOpGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  RangeT getInit() {
    return getODSOperands(0);
  }

  RangeT getCtls() {
    return getODSOperands(1);
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class WhileRegionOpAdaptor : public WhileRegionOpGenericAdaptor<::mlir::ValueRange> {
public:
  using WhileRegionOpGenericAdaptor::WhileRegionOpGenericAdaptor;
  WhileRegionOpAdaptor(WhileRegionOp op);

  ::llvm::LogicalResult verify(::mlir::Location loc);
};
class WhileRegionOp : public ::mlir::Op<WhileRegionOp, ::mlir::OpTrait::NRegions<2>::Impl, ::mlir::OpTrait::AtLeastNResults<1>::Impl, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::VariadicOperands, ::mlir::OpTrait::SingleBlock, ::mlir::OpTrait::AttrSizedOperandSegments, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait, ::mlir::OpTrait::HasOnlyGraphRegion, ::mlir::RegionKindInterface::Trait, ::mlir::OpAsmOpInterface::Trait, ::mlir::tfg::PreservedAttributesInterface::Trait, ::mlir::RegionBranchOpInterface::Trait, ::mlir::tfg::ControlArgumentInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = WhileRegionOpAdaptor;
  template <typename RangeT>
  using GenericAdaptor = WhileRegionOpGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("body_attrs"), ::llvm::StringRef("body_region_attrs"), ::llvm::StringRef("cond_attrs"), ::llvm::StringRef("cond_region_attrs"), ::llvm::StringRef("parallel_iterations"), ::llvm::StringRef("operandSegmentSizes")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getBodyAttrsAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getBodyAttrsAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  ::mlir::StringAttr getBodyRegionAttrsAttrName() {
    return getAttributeNameForIndex(1);
  }

  static ::mlir::StringAttr getBodyRegionAttrsAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 1);
  }

  ::mlir::StringAttr getCondAttrsAttrName() {
    return getAttributeNameForIndex(2);
  }

  static ::mlir::StringAttr getCondAttrsAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 2);
  }

  ::mlir::StringAttr getCondRegionAttrsAttrName() {
    return getAttributeNameForIndex(3);
  }

  static ::mlir::StringAttr getCondRegionAttrsAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 3);
  }

  ::mlir::StringAttr getParallelIterationsAttrName() {
    return getAttributeNameForIndex(4);
  }

  static ::mlir::StringAttr getParallelIterationsAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 4);
  }

  ::mlir::StringAttr getOperandSegmentSizesAttrName() {
   return (*this)->getName().getAttributeNames().back();
  }

  static ::mlir::StringAttr getOperandSegmentSizesAttrName(::mlir::OperationName name) {
   return name.getAttributeNames().back();
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("tfg.WhileRegion");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(getOperation()->operand_begin(), valueRange.first),
             std::next(getOperation()->operand_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::Operation::operand_range getInit() {
    return getODSOperands(0);
  }

  ::mlir::Operation::operand_range getCtls() {
    return getODSOperands(1);
  }

  ::mlir::MutableOperandRange getInitMutable();
  ::mlir::MutableOperandRange getCtlsMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index) {
    auto valueRange = getODSResultIndexAndLength(index);
    return {std::next(getOperation()->result_begin(), valueRange.first),
             std::next(getOperation()->result_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::Operation::result_range getOuts() {
    return getODSResults(0);
  }

  ::mlir::TypedValue<::mlir::tf_type::ControlType> getCtl() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::tf_type::ControlType>>(*getODSResults(1).begin());
  }

  ::mlir::Region &getCondRegion() {
    return (*this)->getRegion(0);
  }

  ::mlir::Region &getBodyRegion() {
    return (*this)->getRegion(1);
  }

  static ::llvm::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::llvm::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::llvm::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::IntegerAttr getParallelIterationsAttr() {
    return ::llvm::cast<::mlir::IntegerAttr>(getProperties().parallel_iterations);
  }

  uint64_t getParallelIterations();
  ::mlir::DictionaryAttr getCondAttrsAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::DictionaryAttr>(getProperties().cond_attrs);
  }

  ::std::optional< ::mlir::DictionaryAttr > getCondAttrs();
  ::mlir::DictionaryAttr getBodyAttrsAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::DictionaryAttr>(getProperties().body_attrs);
  }

  ::std::optional< ::mlir::DictionaryAttr > getBodyAttrs();
  ::mlir::tfg::RegionAttr getCondRegionAttrsAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::tfg::RegionAttr>(getProperties().cond_region_attrs);
  }

  ::std::optional<::mlir::tfg::RegionAttr> getCondRegionAttrs();
  ::mlir::tfg::RegionAttr getBodyRegionAttrsAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::tfg::RegionAttr>(getProperties().body_region_attrs);
  }

  ::std::optional<::mlir::tfg::RegionAttr> getBodyRegionAttrs();
  void setParallelIterationsAttr(::mlir::IntegerAttr attr) {
    getProperties().parallel_iterations = attr;
  }

  void setParallelIterations(uint64_t attrValue);
  void setCondAttrsAttr(::mlir::DictionaryAttr attr) {
    getProperties().cond_attrs = attr;
  }

  void setBodyAttrsAttr(::mlir::DictionaryAttr attr) {
    getProperties().body_attrs = attr;
  }

  void setCondRegionAttrsAttr(::mlir::tfg::RegionAttr attr) {
    getProperties().cond_region_attrs = attr;
  }

  void setBodyRegionAttrsAttr(::mlir::tfg::RegionAttr attr) {
    getProperties().body_region_attrs = attr;
  }

  ::mlir::Attribute removeCondAttrsAttr() {
      auto &attr = getProperties().cond_attrs;
      attr = {};
      return attr;
  }

  ::mlir::Attribute removeBodyAttrsAttr() {
      auto &attr = getProperties().body_attrs;
      attr = {};
      return attr;
  }

  ::mlir::Attribute removeCondRegionAttrsAttr() {
      auto &attr = getProperties().cond_region_attrs;
      attr = {};
      return attr;
  }

  ::mlir::Attribute removeBodyRegionAttrsAttr() {
      auto &attr = getProperties().body_region_attrs;
      attr = {};
      return attr;
  }

  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange outs, ::mlir::Type ctl, ::mlir::ValueRange init, ::mlir::ValueRange ctls, ::mlir::IntegerAttr parallel_iterations, /*optional*/::mlir::DictionaryAttr cond_attrs, /*optional*/::mlir::DictionaryAttr body_attrs, /*optional*/::mlir::tfg::RegionAttr cond_region_attrs, /*optional*/::mlir::tfg::RegionAttr body_region_attrs);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange init, ::mlir::ValueRange ctls, ::mlir::IntegerAttr parallel_iterations, /*optional*/::mlir::DictionaryAttr cond_attrs, /*optional*/::mlir::DictionaryAttr body_attrs, /*optional*/::mlir::tfg::RegionAttr cond_region_attrs, /*optional*/::mlir::tfg::RegionAttr body_region_attrs);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange outs, ::mlir::Type ctl, ::mlir::ValueRange init, ::mlir::ValueRange ctls, uint64_t parallel_iterations, /*optional*/::mlir::DictionaryAttr cond_attrs, /*optional*/::mlir::DictionaryAttr body_attrs, /*optional*/::mlir::tfg::RegionAttr cond_region_attrs, /*optional*/::mlir::tfg::RegionAttr body_region_attrs);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange init, ::mlir::ValueRange ctls, uint64_t parallel_iterations, /*optional*/::mlir::DictionaryAttr cond_attrs, /*optional*/::mlir::DictionaryAttr body_attrs, /*optional*/::mlir::tfg::RegionAttr cond_region_attrs, /*optional*/::mlir::tfg::RegionAttr body_region_attrs);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::llvm::LogicalResult verifyInvariantsImpl();
  ::llvm::LogicalResult verifyInvariants();
  ::llvm::LogicalResult verify();
  void getAsmResultNames(::mlir::OpAsmSetValueNameFn setNameFn);
  static ::llvm::StringRef getDefaultDialect();
  ::mlir::OperandRange getEntrySuccessorOperands(::mlir::RegionBranchPoint point);
  void getSuccessorRegions(::mlir::RegionBranchPoint point, ::llvm::SmallVectorImpl<::mlir::RegionSuccessor> &regions);
  static mlir::BlockArgument getDataValueOf(BlockArgument ctl);
  static mlir::BlockArgument getControlTokenOf(BlockArgument data);
  static mlir::BlockArgument getDataValue(Region &region, unsigned idx);
  static mlir::BlockArgument getControlToken(Region &region, unsigned idx);
  static ::mlir::ParseResult parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result);
  void print(::mlir::OpAsmPrinter &_odsPrinter);
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 5 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
  Block &getCondBlock() { return getCondRegion().front(); }
  Block &getBodyBlock() { return getBodyRegion().front(); }
  ConditionOp getCondCondition();
  YieldOp getBodyYield();

  bool areTypesCompatible(Type lhs, Type rhs) {
    return tf_type::HasCompatibleElementTypes(lhs, rhs);
  }

  RegionAttr getPreservedAttrs(unsigned index) {
    if (index == 0) return getCondRegionAttrsAttr();
    return getBodyRegionAttrsAttr();
  }
  void setPreservedAttrs(unsigned index, RegionAttr attrs) {
    if (index == 0) setCondRegionAttrsAttr(attrs);
    else setBodyRegionAttrsAttr(attrs);
  }
};
} // namespace tfg
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::tfg::WhileRegionOp)

namespace mlir {
namespace tfg {

//===----------------------------------------------------------------------===//
// ::mlir::tfg::YieldOp declarations
//===----------------------------------------------------------------------===//

namespace detail {
class YieldOpGenericAdaptorBase {
public:
  struct Properties {
    using operandSegmentSizesTy = std::array<int32_t, 2>;
    operandSegmentSizesTy operandSegmentSizes;
    ::llvm::ArrayRef<int32_t> getOperandSegmentSizes() const {
      auto &propStorage = this->operandSegmentSizes;
      return propStorage;
    }
    void setOperandSegmentSizes(::llvm::ArrayRef<int32_t> propValue) {
      auto &propStorage = this->operandSegmentSizes;
      ::llvm::copy(propValue, propStorage.begin());
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.operandSegmentSizes == this->operandSegmentSizes &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  YieldOpGenericAdaptorBase(::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : odsAttrs(attrs), properties(properties), odsRegions(regions) {  if (odsAttrs)
      odsOpName.emplace("tfg.yield", odsAttrs.getContext());
  }

  YieldOpGenericAdaptorBase(YieldOp op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes() {
    return odsAttrs;
  }

};
} // namespace detail
template <typename RangeT>
class YieldOpGenericAdaptor : public detail::YieldOpGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::YieldOpGenericAdaptorBase;
public:
  YieldOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  YieldOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : YieldOpGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  YieldOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs) : YieldOpGenericAdaptor(values, attrs, Properties{}, {}) {}

  YieldOpGenericAdaptor(RangeT values, const YieldOpGenericAdaptorBase &base) : Base(base), odsOperands(values) {}

  template <typename LateInst = YieldOp, typename = std::enable_if_t<std::is_same_v<LateInst, YieldOp>>>
  YieldOpGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  RangeT getArgs() {
    return getODSOperands(0);
  }

  RangeT getCtls() {
    return getODSOperands(1);
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class YieldOpAdaptor : public YieldOpGenericAdaptor<::mlir::ValueRange> {
public:
  using YieldOpGenericAdaptor::YieldOpGenericAdaptor;
  YieldOpAdaptor(YieldOp op);

  ::llvm::LogicalResult verify(::mlir::Location loc);
};
class YieldOp : public ::mlir::Op<YieldOp, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::ZeroResults, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::VariadicOperands, ::mlir::OpTrait::AttrSizedOperandSegments, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait, ::mlir::ConditionallySpeculatable::Trait, ::mlir::OpTrait::AlwaysSpeculatableImplTrait, ::mlir::MemoryEffectOpInterface::Trait, ::mlir::OpTrait::ReturnLike, ::mlir::OpTrait::IsTerminator, ::mlir::RegionBranchTerminatorOpInterface::Trait, ::mlir::OpTrait::IntrinsicOperation> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = YieldOpAdaptor;
  template <typename RangeT>
  using GenericAdaptor = YieldOpGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("operandSegmentSizes")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getOperandSegmentSizesAttrName() {
   return (*this)->getName().getAttributeNames().back();
  }

  static ::mlir::StringAttr getOperandSegmentSizesAttrName(::mlir::OperationName name) {
   return name.getAttributeNames().back();
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("tfg.yield");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(getOperation()->operand_begin(), valueRange.first),
             std::next(getOperation()->operand_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::Operation::operand_range getArgs() {
    return getODSOperands(0);
  }

  ::mlir::Operation::operand_range getCtls() {
    return getODSOperands(1);
  }

  ::mlir::MutableOperandRange getArgsMutable();
  ::mlir::MutableOperandRange getCtlsMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index) {
    return {index, 1};
  }

  ::mlir::Operation::result_range getODSResults(unsigned index) {
    auto valueRange = getODSResultIndexAndLength(index);
    return {std::next(getOperation()->result_begin(), valueRange.first),
             std::next(getOperation()->result_begin(), valueRange.first + valueRange.second)};
  }

  static ::llvm::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::llvm::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::llvm::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::ValueRange args, ::mlir::ValueRange ctls);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange args, ::mlir::ValueRange ctls);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::llvm::LogicalResult verifyInvariantsImpl();
  ::llvm::LogicalResult verifyInvariants();
  ::mlir::MutableOperandRange getMutableSuccessorOperands(::mlir::RegionBranchPoint point);
  static ::mlir::ParseResult parse(::mlir::OpAsmParser &parser, ::mlir::OperationState &result);
  void print(::mlir::OpAsmPrinter &_odsPrinter);
  void getEffects(::llvm::SmallVectorImpl<::mlir::SideEffects::EffectInstance<::mlir::MemoryEffects::Effect>> &effects);
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    return {};
  }

public:
};
} // namespace tfg
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::tfg::YieldOp)


#endif  // GET_OP_CLASSES

