/*** Autogenerated by WIDL 10.8 from include/windows.ui.xaml.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __windows_ui_xaml_h__
#define __windows_ui_xaml_h__

/* Forward declarations */

#ifndef ____x_ABI_CWindows_CUI_CXaml_CICreateDefaultValueCallback_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CXaml_CICreateDefaultValueCallback_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CXaml_CICreateDefaultValueCallback __x_ABI_CWindows_CUI_CXaml_CICreateDefaultValueCallback;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CXaml_CICreateDefaultValueCallback ABI::Windows::UI::Xaml::ICreateDefaultValueCallback
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Xaml {
                interface ICreateDefaultValueCallback;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedCallback_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedCallback_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedCallback __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedCallback;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedCallback ABI::Windows::UI::Xaml::IDependencyPropertyChangedCallback
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Xaml {
                interface IDependencyPropertyChangedCallback;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CXaml_CIPropertyChangedCallback_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CXaml_CIPropertyChangedCallback_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CXaml_CIPropertyChangedCallback __x_ABI_CWindows_CUI_CXaml_CIPropertyChangedCallback;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CXaml_CIPropertyChangedCallback ABI::Windows::UI::Xaml::IPropertyChangedCallback
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Xaml {
                interface IPropertyChangedCallback;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CXaml_CIDataTemplateKey_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CXaml_CIDataTemplateKey_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKey __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKey;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKey ABI::Windows::UI::Xaml::IDataTemplateKey
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Xaml {
                interface IDataTemplateKey;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CXaml_CIDataTemplateKeyFactory_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CXaml_CIDataTemplateKeyFactory_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKeyFactory __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKeyFactory;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKeyFactory ABI::Windows::UI::Xaml::IDataTemplateKeyFactory
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Xaml {
                interface IDataTemplateKeyFactory;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CXaml_CIDependencyObject_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CXaml_CIDependencyObject_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CXaml_CIDependencyObject __x_ABI_CWindows_CUI_CXaml_CIDependencyObject;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyObject ABI::Windows::UI::Xaml::IDependencyObject
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Xaml {
                interface IDependencyObject;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CXaml_CIDependencyObject2_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CXaml_CIDependencyObject2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CXaml_CIDependencyObject2 __x_ABI_CWindows_CUI_CXaml_CIDependencyObject2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyObject2 ABI::Windows::UI::Xaml::IDependencyObject2
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Xaml {
                interface IDependencyObject2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CXaml_CIDependencyObjectFactory_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CXaml_CIDependencyObjectFactory_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CXaml_CIDependencyObjectFactory __x_ABI_CWindows_CUI_CXaml_CIDependencyObjectFactory;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyObjectFactory ABI::Windows::UI::Xaml::IDependencyObjectFactory
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Xaml {
                interface IDependencyObjectFactory;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CXaml_CIDependencyProperty_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CXaml_CIDependencyProperty_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CXaml_CIDependencyProperty __x_ABI_CWindows_CUI_CXaml_CIDependencyProperty;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyProperty ABI::Windows::UI::Xaml::IDependencyProperty
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Xaml {
                interface IDependencyProperty;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgs __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgs ABI::Windows::UI::Xaml::IDependencyPropertyChangedEventArgs
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Xaml {
                interface IDependencyPropertyChangedEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStatics __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStatics ABI::Windows::UI::Xaml::IDependencyPropertyStatics
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Xaml {
                interface IDependencyPropertyStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata ABI::Windows::UI::Xaml::IPropertyMetadata
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Xaml {
                interface IPropertyMetadata;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataFactory_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataFactory_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataFactory __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataFactory;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataFactory ABI::Windows::UI::Xaml::IPropertyMetadataFactory
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Xaml {
                interface IPropertyMetadataFactory;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics ABI::Windows::UI::Xaml::IPropertyMetadataStatics
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Xaml {
                interface IPropertyMetadataStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CXaml_CDataTemplateKey_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CXaml_CDataTemplateKey_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Xaml {
                class DataTemplateKey;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CUI_CXaml_CDataTemplateKey __x_ABI_CWindows_CUI_CXaml_CDataTemplateKey;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CUI_CXaml_CDataTemplateKey_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CUI_CXaml_CDependencyObject_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CXaml_CDependencyObject_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Xaml {
                class DependencyObject;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CUI_CXaml_CDependencyObject __x_ABI_CWindows_CUI_CXaml_CDependencyObject;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CUI_CXaml_CDependencyObject_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CUI_CXaml_CDependencyProperty_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CXaml_CDependencyProperty_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Xaml {
                class DependencyProperty;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CUI_CXaml_CDependencyProperty __x_ABI_CWindows_CUI_CXaml_CDependencyProperty;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CUI_CXaml_CDependencyProperty_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CUI_CXaml_CDependencyPropertyChangedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CXaml_CDependencyPropertyChangedEventArgs_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Xaml {
                class DependencyPropertyChangedEventArgs;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CUI_CXaml_CDependencyPropertyChangedEventArgs __x_ABI_CWindows_CUI_CXaml_CDependencyPropertyChangedEventArgs;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CUI_CXaml_CDependencyPropertyChangedEventArgs_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CUI_CXaml_CPropertyMetadata_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CXaml_CPropertyMetadata_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Xaml {
                class PropertyMetadata;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CUI_CXaml_CPropertyMetadata __x_ABI_CWindows_CUI_CXaml_CPropertyMetadata;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CUI_CXaml_CPropertyMetadata_FWD_DEFINED__ */

/* Headers for imported files */

#include <inspectable.h>
#include <asyncinfo.h>
#include <eventtoken.h>
#include <windowscontracts.h>
#include <windows.foundation.h>
#include <windows.applicationmodel.h>
#include <windows.applicationmodel.activation.h>
#include <windows.applicationmodel.core.h>
#include <windows.foundation.numerics.h>
#include <windows.graphics.imaging.h>
#include <windows.ui.h>
#include <windows.ui.composition.h>
#include <windows.ui.core.h>
#include <windows.ui.input.h>
#include <windows.ui.xaml.interop.h>

#ifdef __cplusplus
extern "C" {
#endif

#ifndef ____x_ABI_CWindows_CUI_CXaml_CIDataTemplateKey_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CXaml_CIDataTemplateKey_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKey __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKey;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKey ABI::Windows::UI::Xaml::IDataTemplateKey
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Xaml {
                interface IDataTemplateKey;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CXaml_CIDependencyObject_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CXaml_CIDependencyObject_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CXaml_CIDependencyObject __x_ABI_CWindows_CUI_CXaml_CIDependencyObject;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyObject ABI::Windows::UI::Xaml::IDependencyObject
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Xaml {
                interface IDependencyObject;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CXaml_CIDependencyProperty_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CXaml_CIDependencyProperty_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CXaml_CIDependencyProperty __x_ABI_CWindows_CUI_CXaml_CIDependencyProperty;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyProperty ABI::Windows::UI::Xaml::IDependencyProperty
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Xaml {
                interface IDependencyProperty;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgs __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgs ABI::Windows::UI::Xaml::IDependencyPropertyChangedEventArgs
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Xaml {
                interface IDependencyPropertyChangedEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStatics __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStatics ABI::Windows::UI::Xaml::IDependencyPropertyStatics
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Xaml {
                interface IDependencyPropertyStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata ABI::Windows::UI::Xaml::IPropertyMetadata
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Xaml {
                interface IPropertyMetadata;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataFactory_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataFactory_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataFactory __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataFactory;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataFactory ABI::Windows::UI::Xaml::IPropertyMetadataFactory
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Xaml {
                interface IPropertyMetadataFactory;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics ABI::Windows::UI::Xaml::IPropertyMetadataStatics
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Xaml {
                interface IPropertyMetadataStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

/*****************************************************************************
 * ICreateDefaultValueCallback interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CUI_CXaml_CICreateDefaultValueCallback_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CXaml_CICreateDefaultValueCallback_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CXaml_CICreateDefaultValueCallback, 0xd6ecb12c, 0x15b5, 0x4ec8, 0xb9,0x5c, 0xcd,0xd2,0x08,0xf0,0x81,0x53);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Xaml {
                MIDL_INTERFACE("d6ecb12c-15b5-4ec8-b95c-cdd208f08153")
                ICreateDefaultValueCallback : public IUnknown
                {
                    virtual HRESULT STDMETHODCALLTYPE Invoke(
                        IInspectable **result) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CXaml_CICreateDefaultValueCallback, 0xd6ecb12c, 0x15b5, 0x4ec8, 0xb9,0x5c, 0xcd,0xd2,0x08,0xf0,0x81,0x53)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CXaml_CICreateDefaultValueCallbackVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CXaml_CICreateDefaultValueCallback *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CXaml_CICreateDefaultValueCallback *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CXaml_CICreateDefaultValueCallback *This);

    /*** ICreateDefaultValueCallback methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __x_ABI_CWindows_CUI_CXaml_CICreateDefaultValueCallback *This,
        IInspectable **result);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CXaml_CICreateDefaultValueCallbackVtbl;

interface __x_ABI_CWindows_CUI_CXaml_CICreateDefaultValueCallback {
    CONST_VTBL __x_ABI_CWindows_CUI_CXaml_CICreateDefaultValueCallbackVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CXaml_CICreateDefaultValueCallback_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CXaml_CICreateDefaultValueCallback_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CXaml_CICreateDefaultValueCallback_Release(This) (This)->lpVtbl->Release(This)
/*** ICreateDefaultValueCallback methods ***/
#define __x_ABI_CWindows_CUI_CXaml_CICreateDefaultValueCallback_Invoke(This,result) (This)->lpVtbl->Invoke(This,result)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CICreateDefaultValueCallback_QueryInterface(__x_ABI_CWindows_CUI_CXaml_CICreateDefaultValueCallback* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CXaml_CICreateDefaultValueCallback_AddRef(__x_ABI_CWindows_CUI_CXaml_CICreateDefaultValueCallback* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CXaml_CICreateDefaultValueCallback_Release(__x_ABI_CWindows_CUI_CXaml_CICreateDefaultValueCallback* This) {
    return This->lpVtbl->Release(This);
}
/*** ICreateDefaultValueCallback methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CICreateDefaultValueCallback_Invoke(__x_ABI_CWindows_CUI_CXaml_CICreateDefaultValueCallback* This,IInspectable **result) {
    return This->lpVtbl->Invoke(This,result);
}
#endif
#ifdef WIDL_using_Windows_UI_Xaml
#define IID_ICreateDefaultValueCallback IID___x_ABI_CWindows_CUI_CXaml_CICreateDefaultValueCallback
#define ICreateDefaultValueCallbackVtbl __x_ABI_CWindows_CUI_CXaml_CICreateDefaultValueCallbackVtbl
#define ICreateDefaultValueCallback __x_ABI_CWindows_CUI_CXaml_CICreateDefaultValueCallback
#define ICreateDefaultValueCallback_QueryInterface __x_ABI_CWindows_CUI_CXaml_CICreateDefaultValueCallback_QueryInterface
#define ICreateDefaultValueCallback_AddRef __x_ABI_CWindows_CUI_CXaml_CICreateDefaultValueCallback_AddRef
#define ICreateDefaultValueCallback_Release __x_ABI_CWindows_CUI_CXaml_CICreateDefaultValueCallback_Release
#define ICreateDefaultValueCallback_Invoke __x_ABI_CWindows_CUI_CXaml_CICreateDefaultValueCallback_Invoke
#endif /* WIDL_using_Windows_UI_Xaml */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CXaml_CICreateDefaultValueCallback_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IDependencyPropertyChangedCallback interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedCallback_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedCallback_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedCallback, 0x45883d16, 0x27bf, 0x4bc1, 0xac,0x26, 0x94,0xc1,0x60,0x1f,0x3a,0x49);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Xaml {
                MIDL_INTERFACE("45883d16-27bf-4bc1-ac26-94c1601f3a49")
                IDependencyPropertyChangedCallback : public IUnknown
                {
                    virtual HRESULT STDMETHODCALLTYPE Invoke(
                        ABI::Windows::UI::Xaml::IDependencyObject *sender,
                        ABI::Windows::UI::Xaml::IDependencyProperty *dp) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedCallback, 0x45883d16, 0x27bf, 0x4bc1, 0xac,0x26, 0x94,0xc1,0x60,0x1f,0x3a,0x49)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedCallbackVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedCallback *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedCallback *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedCallback *This);

    /*** IDependencyPropertyChangedCallback methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedCallback *This,
        __x_ABI_CWindows_CUI_CXaml_CIDependencyObject *sender,
        __x_ABI_CWindows_CUI_CXaml_CIDependencyProperty *dp);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedCallbackVtbl;

interface __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedCallback {
    CONST_VTBL __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedCallbackVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedCallback_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedCallback_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedCallback_Release(This) (This)->lpVtbl->Release(This)
/*** IDependencyPropertyChangedCallback methods ***/
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedCallback_Invoke(This,sender,dp) (This)->lpVtbl->Invoke(This,sender,dp)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedCallback_QueryInterface(__x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedCallback* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedCallback_AddRef(__x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedCallback* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedCallback_Release(__x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedCallback* This) {
    return This->lpVtbl->Release(This);
}
/*** IDependencyPropertyChangedCallback methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedCallback_Invoke(__x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedCallback* This,__x_ABI_CWindows_CUI_CXaml_CIDependencyObject *sender,__x_ABI_CWindows_CUI_CXaml_CIDependencyProperty *dp) {
    return This->lpVtbl->Invoke(This,sender,dp);
}
#endif
#ifdef WIDL_using_Windows_UI_Xaml
#define IID_IDependencyPropertyChangedCallback IID___x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedCallback
#define IDependencyPropertyChangedCallbackVtbl __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedCallbackVtbl
#define IDependencyPropertyChangedCallback __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedCallback
#define IDependencyPropertyChangedCallback_QueryInterface __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedCallback_QueryInterface
#define IDependencyPropertyChangedCallback_AddRef __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedCallback_AddRef
#define IDependencyPropertyChangedCallback_Release __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedCallback_Release
#define IDependencyPropertyChangedCallback_Invoke __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedCallback_Invoke
#endif /* WIDL_using_Windows_UI_Xaml */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedCallback_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IPropertyChangedCallback interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CUI_CXaml_CIPropertyChangedCallback_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CXaml_CIPropertyChangedCallback_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CXaml_CIPropertyChangedCallback, 0x5a9f8a25, 0xd142, 0x44a4, 0x82,0x31, 0xfd,0x67,0x67,0x24,0xf2,0x9b);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Xaml {
                MIDL_INTERFACE("5a9f8a25-d142-44a4-8231-fd676724f29b")
                IPropertyChangedCallback : public IUnknown
                {
                    virtual HRESULT STDMETHODCALLTYPE Invoke(
                        ABI::Windows::UI::Xaml::IDependencyObject *obj,
                        ABI::Windows::UI::Xaml::IDependencyPropertyChangedEventArgs *args) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CXaml_CIPropertyChangedCallback, 0x5a9f8a25, 0xd142, 0x44a4, 0x82,0x31, 0xfd,0x67,0x67,0x24,0xf2,0x9b)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CXaml_CIPropertyChangedCallbackVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CXaml_CIPropertyChangedCallback *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CXaml_CIPropertyChangedCallback *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CXaml_CIPropertyChangedCallback *This);

    /*** IPropertyChangedCallback methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __x_ABI_CWindows_CUI_CXaml_CIPropertyChangedCallback *This,
        __x_ABI_CWindows_CUI_CXaml_CIDependencyObject *obj,
        __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgs *args);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CXaml_CIPropertyChangedCallbackVtbl;

interface __x_ABI_CWindows_CUI_CXaml_CIPropertyChangedCallback {
    CONST_VTBL __x_ABI_CWindows_CUI_CXaml_CIPropertyChangedCallbackVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CXaml_CIPropertyChangedCallback_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CXaml_CIPropertyChangedCallback_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CXaml_CIPropertyChangedCallback_Release(This) (This)->lpVtbl->Release(This)
/*** IPropertyChangedCallback methods ***/
#define __x_ABI_CWindows_CUI_CXaml_CIPropertyChangedCallback_Invoke(This,obj,args) (This)->lpVtbl->Invoke(This,obj,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIPropertyChangedCallback_QueryInterface(__x_ABI_CWindows_CUI_CXaml_CIPropertyChangedCallback* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CXaml_CIPropertyChangedCallback_AddRef(__x_ABI_CWindows_CUI_CXaml_CIPropertyChangedCallback* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CXaml_CIPropertyChangedCallback_Release(__x_ABI_CWindows_CUI_CXaml_CIPropertyChangedCallback* This) {
    return This->lpVtbl->Release(This);
}
/*** IPropertyChangedCallback methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIPropertyChangedCallback_Invoke(__x_ABI_CWindows_CUI_CXaml_CIPropertyChangedCallback* This,__x_ABI_CWindows_CUI_CXaml_CIDependencyObject *obj,__x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgs *args) {
    return This->lpVtbl->Invoke(This,obj,args);
}
#endif
#ifdef WIDL_using_Windows_UI_Xaml
#define IID_IPropertyChangedCallback IID___x_ABI_CWindows_CUI_CXaml_CIPropertyChangedCallback
#define IPropertyChangedCallbackVtbl __x_ABI_CWindows_CUI_CXaml_CIPropertyChangedCallbackVtbl
#define IPropertyChangedCallback __x_ABI_CWindows_CUI_CXaml_CIPropertyChangedCallback
#define IPropertyChangedCallback_QueryInterface __x_ABI_CWindows_CUI_CXaml_CIPropertyChangedCallback_QueryInterface
#define IPropertyChangedCallback_AddRef __x_ABI_CWindows_CUI_CXaml_CIPropertyChangedCallback_AddRef
#define IPropertyChangedCallback_Release __x_ABI_CWindows_CUI_CXaml_CIPropertyChangedCallback_Release
#define IPropertyChangedCallback_Invoke __x_ABI_CWindows_CUI_CXaml_CIPropertyChangedCallback_Invoke
#endif /* WIDL_using_Windows_UI_Xaml */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CXaml_CIPropertyChangedCallback_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IDataTemplateKey interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CUI_CXaml_CIDataTemplateKey_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CXaml_CIDataTemplateKey_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CXaml_CIDataTemplateKey, 0x873b6c28, 0xcceb, 0x4b61, 0x86,0xfa, 0xb2,0xce,0xc3,0x9c,0xc2,0xfa);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Xaml {
                MIDL_INTERFACE("873b6c28-cceb-4b61-86fa-b2cec39cc2fa")
                IDataTemplateKey : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_DataType(
                        IInspectable **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE put_DataType(
                        IInspectable *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CXaml_CIDataTemplateKey, 0x873b6c28, 0xcceb, 0x4b61, 0x86,0xfa, 0xb2,0xce,0xc3,0x9c,0xc2,0xfa)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKeyVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKey *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKey *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKey *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKey *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKey *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKey *This,
        TrustLevel *trustLevel);

    /*** IDataTemplateKey methods ***/
    HRESULT (STDMETHODCALLTYPE *get_DataType)(
        __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKey *This,
        IInspectable **value);

    HRESULT (STDMETHODCALLTYPE *put_DataType)(
        __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKey *This,
        IInspectable *value);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKeyVtbl;

interface __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKey {
    CONST_VTBL __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKeyVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKey_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKey_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKey_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKey_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKey_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKey_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IDataTemplateKey methods ***/
#define __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKey_get_DataType(This,value) (This)->lpVtbl->get_DataType(This,value)
#define __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKey_put_DataType(This,value) (This)->lpVtbl->put_DataType(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKey_QueryInterface(__x_ABI_CWindows_CUI_CXaml_CIDataTemplateKey* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKey_AddRef(__x_ABI_CWindows_CUI_CXaml_CIDataTemplateKey* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKey_Release(__x_ABI_CWindows_CUI_CXaml_CIDataTemplateKey* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKey_GetIids(__x_ABI_CWindows_CUI_CXaml_CIDataTemplateKey* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKey_GetRuntimeClassName(__x_ABI_CWindows_CUI_CXaml_CIDataTemplateKey* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKey_GetTrustLevel(__x_ABI_CWindows_CUI_CXaml_CIDataTemplateKey* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IDataTemplateKey methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKey_get_DataType(__x_ABI_CWindows_CUI_CXaml_CIDataTemplateKey* This,IInspectable **value) {
    return This->lpVtbl->get_DataType(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKey_put_DataType(__x_ABI_CWindows_CUI_CXaml_CIDataTemplateKey* This,IInspectable *value) {
    return This->lpVtbl->put_DataType(This,value);
}
#endif
#ifdef WIDL_using_Windows_UI_Xaml
#define IID_IDataTemplateKey IID___x_ABI_CWindows_CUI_CXaml_CIDataTemplateKey
#define IDataTemplateKeyVtbl __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKeyVtbl
#define IDataTemplateKey __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKey
#define IDataTemplateKey_QueryInterface __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKey_QueryInterface
#define IDataTemplateKey_AddRef __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKey_AddRef
#define IDataTemplateKey_Release __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKey_Release
#define IDataTemplateKey_GetIids __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKey_GetIids
#define IDataTemplateKey_GetRuntimeClassName __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKey_GetRuntimeClassName
#define IDataTemplateKey_GetTrustLevel __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKey_GetTrustLevel
#define IDataTemplateKey_get_DataType __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKey_get_DataType
#define IDataTemplateKey_put_DataType __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKey_put_DataType
#endif /* WIDL_using_Windows_UI_Xaml */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CXaml_CIDataTemplateKey_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IDataTemplateKeyFactory interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CUI_CXaml_CIDataTemplateKeyFactory_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CXaml_CIDataTemplateKeyFactory_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CXaml_CIDataTemplateKeyFactory, 0xe96b2959, 0xd982, 0x4152, 0x91,0xcb, 0xde,0x0e,0x4d,0xfd,0x76,0x93);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Xaml {
                MIDL_INTERFACE("e96b2959-d982-4152-91cb-de0e4dfd7693")
                IDataTemplateKeyFactory : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE CreateInstance(
                        IInspectable *base_interface,
                        IInspectable **inner_interface,
                        ABI::Windows::UI::Xaml::IDataTemplateKey **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE CreateInstanceWithType(
                        IInspectable *data_type,
                        IInspectable *base_interface,
                        IInspectable **inner_interface,
                        ABI::Windows::UI::Xaml::IDataTemplateKey **value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CXaml_CIDataTemplateKeyFactory, 0xe96b2959, 0xd982, 0x4152, 0x91,0xcb, 0xde,0x0e,0x4d,0xfd,0x76,0x93)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKeyFactoryVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKeyFactory *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKeyFactory *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKeyFactory *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKeyFactory *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKeyFactory *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKeyFactory *This,
        TrustLevel *trustLevel);

    /*** IDataTemplateKeyFactory methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateInstance)(
        __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKeyFactory *This,
        IInspectable *base_interface,
        IInspectable **inner_interface,
        __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKey **value);

    HRESULT (STDMETHODCALLTYPE *CreateInstanceWithType)(
        __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKeyFactory *This,
        IInspectable *data_type,
        IInspectable *base_interface,
        IInspectable **inner_interface,
        __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKey **value);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKeyFactoryVtbl;

interface __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKeyFactory {
    CONST_VTBL __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKeyFactoryVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKeyFactory_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKeyFactory_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKeyFactory_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKeyFactory_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKeyFactory_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKeyFactory_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IDataTemplateKeyFactory methods ***/
#define __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKeyFactory_CreateInstance(This,base_interface,inner_interface,value) (This)->lpVtbl->CreateInstance(This,base_interface,inner_interface,value)
#define __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKeyFactory_CreateInstanceWithType(This,data_type,base_interface,inner_interface,value) (This)->lpVtbl->CreateInstanceWithType(This,data_type,base_interface,inner_interface,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKeyFactory_QueryInterface(__x_ABI_CWindows_CUI_CXaml_CIDataTemplateKeyFactory* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKeyFactory_AddRef(__x_ABI_CWindows_CUI_CXaml_CIDataTemplateKeyFactory* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKeyFactory_Release(__x_ABI_CWindows_CUI_CXaml_CIDataTemplateKeyFactory* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKeyFactory_GetIids(__x_ABI_CWindows_CUI_CXaml_CIDataTemplateKeyFactory* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKeyFactory_GetRuntimeClassName(__x_ABI_CWindows_CUI_CXaml_CIDataTemplateKeyFactory* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKeyFactory_GetTrustLevel(__x_ABI_CWindows_CUI_CXaml_CIDataTemplateKeyFactory* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IDataTemplateKeyFactory methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKeyFactory_CreateInstance(__x_ABI_CWindows_CUI_CXaml_CIDataTemplateKeyFactory* This,IInspectable *base_interface,IInspectable **inner_interface,__x_ABI_CWindows_CUI_CXaml_CIDataTemplateKey **value) {
    return This->lpVtbl->CreateInstance(This,base_interface,inner_interface,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKeyFactory_CreateInstanceWithType(__x_ABI_CWindows_CUI_CXaml_CIDataTemplateKeyFactory* This,IInspectable *data_type,IInspectable *base_interface,IInspectable **inner_interface,__x_ABI_CWindows_CUI_CXaml_CIDataTemplateKey **value) {
    return This->lpVtbl->CreateInstanceWithType(This,data_type,base_interface,inner_interface,value);
}
#endif
#ifdef WIDL_using_Windows_UI_Xaml
#define IID_IDataTemplateKeyFactory IID___x_ABI_CWindows_CUI_CXaml_CIDataTemplateKeyFactory
#define IDataTemplateKeyFactoryVtbl __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKeyFactoryVtbl
#define IDataTemplateKeyFactory __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKeyFactory
#define IDataTemplateKeyFactory_QueryInterface __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKeyFactory_QueryInterface
#define IDataTemplateKeyFactory_AddRef __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKeyFactory_AddRef
#define IDataTemplateKeyFactory_Release __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKeyFactory_Release
#define IDataTemplateKeyFactory_GetIids __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKeyFactory_GetIids
#define IDataTemplateKeyFactory_GetRuntimeClassName __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKeyFactory_GetRuntimeClassName
#define IDataTemplateKeyFactory_GetTrustLevel __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKeyFactory_GetTrustLevel
#define IDataTemplateKeyFactory_CreateInstance __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKeyFactory_CreateInstance
#define IDataTemplateKeyFactory_CreateInstanceWithType __x_ABI_CWindows_CUI_CXaml_CIDataTemplateKeyFactory_CreateInstanceWithType
#endif /* WIDL_using_Windows_UI_Xaml */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CXaml_CIDataTemplateKeyFactory_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IDependencyObject interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CUI_CXaml_CIDependencyObject_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CXaml_CIDependencyObject_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CXaml_CIDependencyObject, 0x5c526665, 0xf60e, 0x4912, 0xaf,0x59, 0x5f,0xe0,0x68,0x0f,0x08,0x9d);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Xaml {
                MIDL_INTERFACE("5c526665-f60e-4912-af59-5fe0680f089d")
                IDependencyObject : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE GetValue(
                        ABI::Windows::UI::Xaml::IDependencyProperty *dp,
                        IInspectable **result) = 0;

                    virtual HRESULT STDMETHODCALLTYPE SetValue(
                        ABI::Windows::UI::Xaml::IDependencyProperty *dp,
                        IInspectable *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE ClearValue(
                        ABI::Windows::UI::Xaml::IDependencyProperty *dp) = 0;

                    virtual HRESULT STDMETHODCALLTYPE ReadLocalValue(
                        ABI::Windows::UI::Xaml::IDependencyProperty *dp,
                        IInspectable **result) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetAnimationBaseValue(
                        ABI::Windows::UI::Xaml::IDependencyProperty *dp,
                        IInspectable **result) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_Dispatcher(
                        ABI::Windows::UI::Core::ICoreDispatcher **value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CXaml_CIDependencyObject, 0x5c526665, 0xf60e, 0x4912, 0xaf,0x59, 0x5f,0xe0,0x68,0x0f,0x08,0x9d)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CXaml_CIDependencyObjectVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CXaml_CIDependencyObject *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CXaml_CIDependencyObject *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CXaml_CIDependencyObject *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CXaml_CIDependencyObject *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CXaml_CIDependencyObject *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CXaml_CIDependencyObject *This,
        TrustLevel *trustLevel);

    /*** IDependencyObject methods ***/
    HRESULT (STDMETHODCALLTYPE *GetValue)(
        __x_ABI_CWindows_CUI_CXaml_CIDependencyObject *This,
        __x_ABI_CWindows_CUI_CXaml_CIDependencyProperty *dp,
        IInspectable **result);

    HRESULT (STDMETHODCALLTYPE *SetValue)(
        __x_ABI_CWindows_CUI_CXaml_CIDependencyObject *This,
        __x_ABI_CWindows_CUI_CXaml_CIDependencyProperty *dp,
        IInspectable *value);

    HRESULT (STDMETHODCALLTYPE *ClearValue)(
        __x_ABI_CWindows_CUI_CXaml_CIDependencyObject *This,
        __x_ABI_CWindows_CUI_CXaml_CIDependencyProperty *dp);

    HRESULT (STDMETHODCALLTYPE *ReadLocalValue)(
        __x_ABI_CWindows_CUI_CXaml_CIDependencyObject *This,
        __x_ABI_CWindows_CUI_CXaml_CIDependencyProperty *dp,
        IInspectable **result);

    HRESULT (STDMETHODCALLTYPE *GetAnimationBaseValue)(
        __x_ABI_CWindows_CUI_CXaml_CIDependencyObject *This,
        __x_ABI_CWindows_CUI_CXaml_CIDependencyProperty *dp,
        IInspectable **result);

    HRESULT (STDMETHODCALLTYPE *get_Dispatcher)(
        __x_ABI_CWindows_CUI_CXaml_CIDependencyObject *This,
        __x_ABI_CWindows_CUI_CCore_CICoreDispatcher **value);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CXaml_CIDependencyObjectVtbl;

interface __x_ABI_CWindows_CUI_CXaml_CIDependencyObject {
    CONST_VTBL __x_ABI_CWindows_CUI_CXaml_CIDependencyObjectVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyObject_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyObject_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyObject_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyObject_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyObject_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyObject_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IDependencyObject methods ***/
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyObject_GetValue(This,dp,result) (This)->lpVtbl->GetValue(This,dp,result)
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyObject_SetValue(This,dp,value) (This)->lpVtbl->SetValue(This,dp,value)
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyObject_ClearValue(This,dp) (This)->lpVtbl->ClearValue(This,dp)
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyObject_ReadLocalValue(This,dp,result) (This)->lpVtbl->ReadLocalValue(This,dp,result)
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyObject_GetAnimationBaseValue(This,dp,result) (This)->lpVtbl->GetAnimationBaseValue(This,dp,result)
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyObject_get_Dispatcher(This,value) (This)->lpVtbl->get_Dispatcher(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIDependencyObject_QueryInterface(__x_ABI_CWindows_CUI_CXaml_CIDependencyObject* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CXaml_CIDependencyObject_AddRef(__x_ABI_CWindows_CUI_CXaml_CIDependencyObject* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CXaml_CIDependencyObject_Release(__x_ABI_CWindows_CUI_CXaml_CIDependencyObject* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIDependencyObject_GetIids(__x_ABI_CWindows_CUI_CXaml_CIDependencyObject* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIDependencyObject_GetRuntimeClassName(__x_ABI_CWindows_CUI_CXaml_CIDependencyObject* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIDependencyObject_GetTrustLevel(__x_ABI_CWindows_CUI_CXaml_CIDependencyObject* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IDependencyObject methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIDependencyObject_GetValue(__x_ABI_CWindows_CUI_CXaml_CIDependencyObject* This,__x_ABI_CWindows_CUI_CXaml_CIDependencyProperty *dp,IInspectable **result) {
    return This->lpVtbl->GetValue(This,dp,result);
}
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIDependencyObject_SetValue(__x_ABI_CWindows_CUI_CXaml_CIDependencyObject* This,__x_ABI_CWindows_CUI_CXaml_CIDependencyProperty *dp,IInspectable *value) {
    return This->lpVtbl->SetValue(This,dp,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIDependencyObject_ClearValue(__x_ABI_CWindows_CUI_CXaml_CIDependencyObject* This,__x_ABI_CWindows_CUI_CXaml_CIDependencyProperty *dp) {
    return This->lpVtbl->ClearValue(This,dp);
}
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIDependencyObject_ReadLocalValue(__x_ABI_CWindows_CUI_CXaml_CIDependencyObject* This,__x_ABI_CWindows_CUI_CXaml_CIDependencyProperty *dp,IInspectable **result) {
    return This->lpVtbl->ReadLocalValue(This,dp,result);
}
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIDependencyObject_GetAnimationBaseValue(__x_ABI_CWindows_CUI_CXaml_CIDependencyObject* This,__x_ABI_CWindows_CUI_CXaml_CIDependencyProperty *dp,IInspectable **result) {
    return This->lpVtbl->GetAnimationBaseValue(This,dp,result);
}
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIDependencyObject_get_Dispatcher(__x_ABI_CWindows_CUI_CXaml_CIDependencyObject* This,__x_ABI_CWindows_CUI_CCore_CICoreDispatcher **value) {
    return This->lpVtbl->get_Dispatcher(This,value);
}
#endif
#ifdef WIDL_using_Windows_UI_Xaml
#define IID_IDependencyObject IID___x_ABI_CWindows_CUI_CXaml_CIDependencyObject
#define IDependencyObjectVtbl __x_ABI_CWindows_CUI_CXaml_CIDependencyObjectVtbl
#define IDependencyObject __x_ABI_CWindows_CUI_CXaml_CIDependencyObject
#define IDependencyObject_QueryInterface __x_ABI_CWindows_CUI_CXaml_CIDependencyObject_QueryInterface
#define IDependencyObject_AddRef __x_ABI_CWindows_CUI_CXaml_CIDependencyObject_AddRef
#define IDependencyObject_Release __x_ABI_CWindows_CUI_CXaml_CIDependencyObject_Release
#define IDependencyObject_GetIids __x_ABI_CWindows_CUI_CXaml_CIDependencyObject_GetIids
#define IDependencyObject_GetRuntimeClassName __x_ABI_CWindows_CUI_CXaml_CIDependencyObject_GetRuntimeClassName
#define IDependencyObject_GetTrustLevel __x_ABI_CWindows_CUI_CXaml_CIDependencyObject_GetTrustLevel
#define IDependencyObject_GetValue __x_ABI_CWindows_CUI_CXaml_CIDependencyObject_GetValue
#define IDependencyObject_SetValue __x_ABI_CWindows_CUI_CXaml_CIDependencyObject_SetValue
#define IDependencyObject_ClearValue __x_ABI_CWindows_CUI_CXaml_CIDependencyObject_ClearValue
#define IDependencyObject_ReadLocalValue __x_ABI_CWindows_CUI_CXaml_CIDependencyObject_ReadLocalValue
#define IDependencyObject_GetAnimationBaseValue __x_ABI_CWindows_CUI_CXaml_CIDependencyObject_GetAnimationBaseValue
#define IDependencyObject_get_Dispatcher __x_ABI_CWindows_CUI_CXaml_CIDependencyObject_get_Dispatcher
#endif /* WIDL_using_Windows_UI_Xaml */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CXaml_CIDependencyObject_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IDependencyObject2 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CUI_CXaml_CIDependencyObject2_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CXaml_CIDependencyObject2_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CXaml_CIDependencyObject2, 0x29fed85d, 0x3d22, 0x43a1, 0xad,0xd0, 0x17,0x02,0x7c,0x08,0xb2,0x12);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Xaml {
                MIDL_INTERFACE("29fed85d-3d22-43a1-add0-17027c08b212")
                IDependencyObject2 : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE RegisterPropertyChangedCallback(
                        ABI::Windows::UI::Xaml::IDependencyProperty *dp,
                        ABI::Windows::UI::Xaml::IDependencyPropertyChangedCallback *callback,
                        INT64 *result) = 0;

                    virtual HRESULT STDMETHODCALLTYPE UnregisterPropertyChangedCallback(
                        ABI::Windows::UI::Xaml::IDependencyProperty *dp,
                        INT64 token) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CXaml_CIDependencyObject2, 0x29fed85d, 0x3d22, 0x43a1, 0xad,0xd0, 0x17,0x02,0x7c,0x08,0xb2,0x12)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CXaml_CIDependencyObject2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CXaml_CIDependencyObject2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CXaml_CIDependencyObject2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CXaml_CIDependencyObject2 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CXaml_CIDependencyObject2 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CXaml_CIDependencyObject2 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CXaml_CIDependencyObject2 *This,
        TrustLevel *trustLevel);

    /*** IDependencyObject2 methods ***/
    HRESULT (STDMETHODCALLTYPE *RegisterPropertyChangedCallback)(
        __x_ABI_CWindows_CUI_CXaml_CIDependencyObject2 *This,
        __x_ABI_CWindows_CUI_CXaml_CIDependencyProperty *dp,
        __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedCallback *callback,
        INT64 *result);

    HRESULT (STDMETHODCALLTYPE *UnregisterPropertyChangedCallback)(
        __x_ABI_CWindows_CUI_CXaml_CIDependencyObject2 *This,
        __x_ABI_CWindows_CUI_CXaml_CIDependencyProperty *dp,
        INT64 token);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CXaml_CIDependencyObject2Vtbl;

interface __x_ABI_CWindows_CUI_CXaml_CIDependencyObject2 {
    CONST_VTBL __x_ABI_CWindows_CUI_CXaml_CIDependencyObject2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyObject2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyObject2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyObject2_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyObject2_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyObject2_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyObject2_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IDependencyObject2 methods ***/
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyObject2_RegisterPropertyChangedCallback(This,dp,callback,result) (This)->lpVtbl->RegisterPropertyChangedCallback(This,dp,callback,result)
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyObject2_UnregisterPropertyChangedCallback(This,dp,token) (This)->lpVtbl->UnregisterPropertyChangedCallback(This,dp,token)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIDependencyObject2_QueryInterface(__x_ABI_CWindows_CUI_CXaml_CIDependencyObject2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CXaml_CIDependencyObject2_AddRef(__x_ABI_CWindows_CUI_CXaml_CIDependencyObject2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CXaml_CIDependencyObject2_Release(__x_ABI_CWindows_CUI_CXaml_CIDependencyObject2* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIDependencyObject2_GetIids(__x_ABI_CWindows_CUI_CXaml_CIDependencyObject2* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIDependencyObject2_GetRuntimeClassName(__x_ABI_CWindows_CUI_CXaml_CIDependencyObject2* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIDependencyObject2_GetTrustLevel(__x_ABI_CWindows_CUI_CXaml_CIDependencyObject2* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IDependencyObject2 methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIDependencyObject2_RegisterPropertyChangedCallback(__x_ABI_CWindows_CUI_CXaml_CIDependencyObject2* This,__x_ABI_CWindows_CUI_CXaml_CIDependencyProperty *dp,__x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedCallback *callback,INT64 *result) {
    return This->lpVtbl->RegisterPropertyChangedCallback(This,dp,callback,result);
}
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIDependencyObject2_UnregisterPropertyChangedCallback(__x_ABI_CWindows_CUI_CXaml_CIDependencyObject2* This,__x_ABI_CWindows_CUI_CXaml_CIDependencyProperty *dp,INT64 token) {
    return This->lpVtbl->UnregisterPropertyChangedCallback(This,dp,token);
}
#endif
#ifdef WIDL_using_Windows_UI_Xaml
#define IID_IDependencyObject2 IID___x_ABI_CWindows_CUI_CXaml_CIDependencyObject2
#define IDependencyObject2Vtbl __x_ABI_CWindows_CUI_CXaml_CIDependencyObject2Vtbl
#define IDependencyObject2 __x_ABI_CWindows_CUI_CXaml_CIDependencyObject2
#define IDependencyObject2_QueryInterface __x_ABI_CWindows_CUI_CXaml_CIDependencyObject2_QueryInterface
#define IDependencyObject2_AddRef __x_ABI_CWindows_CUI_CXaml_CIDependencyObject2_AddRef
#define IDependencyObject2_Release __x_ABI_CWindows_CUI_CXaml_CIDependencyObject2_Release
#define IDependencyObject2_GetIids __x_ABI_CWindows_CUI_CXaml_CIDependencyObject2_GetIids
#define IDependencyObject2_GetRuntimeClassName __x_ABI_CWindows_CUI_CXaml_CIDependencyObject2_GetRuntimeClassName
#define IDependencyObject2_GetTrustLevel __x_ABI_CWindows_CUI_CXaml_CIDependencyObject2_GetTrustLevel
#define IDependencyObject2_RegisterPropertyChangedCallback __x_ABI_CWindows_CUI_CXaml_CIDependencyObject2_RegisterPropertyChangedCallback
#define IDependencyObject2_UnregisterPropertyChangedCallback __x_ABI_CWindows_CUI_CXaml_CIDependencyObject2_UnregisterPropertyChangedCallback
#endif /* WIDL_using_Windows_UI_Xaml */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CXaml_CIDependencyObject2_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IDependencyObjectFactory interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CUI_CXaml_CIDependencyObjectFactory_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CXaml_CIDependencyObjectFactory_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CXaml_CIDependencyObjectFactory, 0x9a03af92, 0x7d8a, 0x4937, 0x88,0x4f, 0xec,0xf3,0x4f,0xe0,0x2a,0xcb);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Xaml {
                MIDL_INTERFACE("9a03af92-7d8a-4937-884f-ecf34fe02acb")
                IDependencyObjectFactory : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE CreateInstance(
                        IInspectable *base_interface,
                        IInspectable **inner_interface,
                        ABI::Windows::UI::Xaml::IDependencyObject **value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CXaml_CIDependencyObjectFactory, 0x9a03af92, 0x7d8a, 0x4937, 0x88,0x4f, 0xec,0xf3,0x4f,0xe0,0x2a,0xcb)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CXaml_CIDependencyObjectFactoryVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CXaml_CIDependencyObjectFactory *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CXaml_CIDependencyObjectFactory *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CXaml_CIDependencyObjectFactory *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CXaml_CIDependencyObjectFactory *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CXaml_CIDependencyObjectFactory *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CXaml_CIDependencyObjectFactory *This,
        TrustLevel *trustLevel);

    /*** IDependencyObjectFactory methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateInstance)(
        __x_ABI_CWindows_CUI_CXaml_CIDependencyObjectFactory *This,
        IInspectable *base_interface,
        IInspectable **inner_interface,
        __x_ABI_CWindows_CUI_CXaml_CIDependencyObject **value);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CXaml_CIDependencyObjectFactoryVtbl;

interface __x_ABI_CWindows_CUI_CXaml_CIDependencyObjectFactory {
    CONST_VTBL __x_ABI_CWindows_CUI_CXaml_CIDependencyObjectFactoryVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyObjectFactory_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyObjectFactory_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyObjectFactory_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyObjectFactory_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyObjectFactory_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyObjectFactory_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IDependencyObjectFactory methods ***/
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyObjectFactory_CreateInstance(This,base_interface,inner_interface,value) (This)->lpVtbl->CreateInstance(This,base_interface,inner_interface,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIDependencyObjectFactory_QueryInterface(__x_ABI_CWindows_CUI_CXaml_CIDependencyObjectFactory* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CXaml_CIDependencyObjectFactory_AddRef(__x_ABI_CWindows_CUI_CXaml_CIDependencyObjectFactory* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CXaml_CIDependencyObjectFactory_Release(__x_ABI_CWindows_CUI_CXaml_CIDependencyObjectFactory* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIDependencyObjectFactory_GetIids(__x_ABI_CWindows_CUI_CXaml_CIDependencyObjectFactory* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIDependencyObjectFactory_GetRuntimeClassName(__x_ABI_CWindows_CUI_CXaml_CIDependencyObjectFactory* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIDependencyObjectFactory_GetTrustLevel(__x_ABI_CWindows_CUI_CXaml_CIDependencyObjectFactory* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IDependencyObjectFactory methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIDependencyObjectFactory_CreateInstance(__x_ABI_CWindows_CUI_CXaml_CIDependencyObjectFactory* This,IInspectable *base_interface,IInspectable **inner_interface,__x_ABI_CWindows_CUI_CXaml_CIDependencyObject **value) {
    return This->lpVtbl->CreateInstance(This,base_interface,inner_interface,value);
}
#endif
#ifdef WIDL_using_Windows_UI_Xaml
#define IID_IDependencyObjectFactory IID___x_ABI_CWindows_CUI_CXaml_CIDependencyObjectFactory
#define IDependencyObjectFactoryVtbl __x_ABI_CWindows_CUI_CXaml_CIDependencyObjectFactoryVtbl
#define IDependencyObjectFactory __x_ABI_CWindows_CUI_CXaml_CIDependencyObjectFactory
#define IDependencyObjectFactory_QueryInterface __x_ABI_CWindows_CUI_CXaml_CIDependencyObjectFactory_QueryInterface
#define IDependencyObjectFactory_AddRef __x_ABI_CWindows_CUI_CXaml_CIDependencyObjectFactory_AddRef
#define IDependencyObjectFactory_Release __x_ABI_CWindows_CUI_CXaml_CIDependencyObjectFactory_Release
#define IDependencyObjectFactory_GetIids __x_ABI_CWindows_CUI_CXaml_CIDependencyObjectFactory_GetIids
#define IDependencyObjectFactory_GetRuntimeClassName __x_ABI_CWindows_CUI_CXaml_CIDependencyObjectFactory_GetRuntimeClassName
#define IDependencyObjectFactory_GetTrustLevel __x_ABI_CWindows_CUI_CXaml_CIDependencyObjectFactory_GetTrustLevel
#define IDependencyObjectFactory_CreateInstance __x_ABI_CWindows_CUI_CXaml_CIDependencyObjectFactory_CreateInstance
#endif /* WIDL_using_Windows_UI_Xaml */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CXaml_CIDependencyObjectFactory_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IDependencyProperty interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CUI_CXaml_CIDependencyProperty_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CXaml_CIDependencyProperty_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CXaml_CIDependencyProperty, 0x85b13970, 0x9bc4, 0x4e96, 0xac,0xf1, 0x30,0xc8,0xfd,0x3d,0x55,0xc8);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Xaml {
                MIDL_INTERFACE("85b13970-9bc4-4e96-acf1-30c8fd3d55c8")
                IDependencyProperty : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE GetMetadata(
                        ABI::Windows::UI::Xaml::Interop::TypeName type,
                        ABI::Windows::UI::Xaml::IPropertyMetadata **result) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CXaml_CIDependencyProperty, 0x85b13970, 0x9bc4, 0x4e96, 0xac,0xf1, 0x30,0xc8,0xfd,0x3d,0x55,0xc8)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CXaml_CIDependencyProperty *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CXaml_CIDependencyProperty *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CXaml_CIDependencyProperty *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CXaml_CIDependencyProperty *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CXaml_CIDependencyProperty *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CXaml_CIDependencyProperty *This,
        TrustLevel *trustLevel);

    /*** IDependencyProperty methods ***/
    HRESULT (STDMETHODCALLTYPE *GetMetadata)(
        __x_ABI_CWindows_CUI_CXaml_CIDependencyProperty *This,
        __x_ABI_CWindows_CUI_CXaml_CInterop_CTypeName type,
        __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata **result);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyVtbl;

interface __x_ABI_CWindows_CUI_CXaml_CIDependencyProperty {
    CONST_VTBL __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyProperty_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyProperty_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyProperty_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyProperty_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyProperty_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyProperty_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IDependencyProperty methods ***/
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyProperty_GetMetadata(This,type,result) (This)->lpVtbl->GetMetadata(This,type,result)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIDependencyProperty_QueryInterface(__x_ABI_CWindows_CUI_CXaml_CIDependencyProperty* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CXaml_CIDependencyProperty_AddRef(__x_ABI_CWindows_CUI_CXaml_CIDependencyProperty* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CXaml_CIDependencyProperty_Release(__x_ABI_CWindows_CUI_CXaml_CIDependencyProperty* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIDependencyProperty_GetIids(__x_ABI_CWindows_CUI_CXaml_CIDependencyProperty* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIDependencyProperty_GetRuntimeClassName(__x_ABI_CWindows_CUI_CXaml_CIDependencyProperty* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIDependencyProperty_GetTrustLevel(__x_ABI_CWindows_CUI_CXaml_CIDependencyProperty* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IDependencyProperty methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIDependencyProperty_GetMetadata(__x_ABI_CWindows_CUI_CXaml_CIDependencyProperty* This,__x_ABI_CWindows_CUI_CXaml_CInterop_CTypeName type,__x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata **result) {
    return This->lpVtbl->GetMetadata(This,type,result);
}
#endif
#ifdef WIDL_using_Windows_UI_Xaml
#define IID_IDependencyProperty IID___x_ABI_CWindows_CUI_CXaml_CIDependencyProperty
#define IDependencyPropertyVtbl __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyVtbl
#define IDependencyProperty __x_ABI_CWindows_CUI_CXaml_CIDependencyProperty
#define IDependencyProperty_QueryInterface __x_ABI_CWindows_CUI_CXaml_CIDependencyProperty_QueryInterface
#define IDependencyProperty_AddRef __x_ABI_CWindows_CUI_CXaml_CIDependencyProperty_AddRef
#define IDependencyProperty_Release __x_ABI_CWindows_CUI_CXaml_CIDependencyProperty_Release
#define IDependencyProperty_GetIids __x_ABI_CWindows_CUI_CXaml_CIDependencyProperty_GetIids
#define IDependencyProperty_GetRuntimeClassName __x_ABI_CWindows_CUI_CXaml_CIDependencyProperty_GetRuntimeClassName
#define IDependencyProperty_GetTrustLevel __x_ABI_CWindows_CUI_CXaml_CIDependencyProperty_GetTrustLevel
#define IDependencyProperty_GetMetadata __x_ABI_CWindows_CUI_CXaml_CIDependencyProperty_GetMetadata
#endif /* WIDL_using_Windows_UI_Xaml */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CXaml_CIDependencyProperty_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IDependencyPropertyChangedEventArgs interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgs_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgs, 0x81212c2b, 0x24d0, 0x4957, 0xab,0xc3, 0x22,0x44,0x70,0xa9,0x3a,0x4e);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Xaml {
                MIDL_INTERFACE("81212c2b-24d0-4957-abc3-224470a93a4e")
                IDependencyPropertyChangedEventArgs : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_Property(
                        ABI::Windows::UI::Xaml::IDependencyProperty **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_OldValue(
                        IInspectable **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_NewValue(
                        IInspectable **value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgs, 0x81212c2b, 0x24d0, 0x4957, 0xab,0xc3, 0x22,0x44,0x70,0xa9,0x3a,0x4e)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgs *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgs *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgs *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgs *This,
        TrustLevel *trustLevel);

    /*** IDependencyPropertyChangedEventArgs methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Property)(
        __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgs *This,
        __x_ABI_CWindows_CUI_CXaml_CIDependencyProperty **value);

    HRESULT (STDMETHODCALLTYPE *get_OldValue)(
        __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgs *This,
        IInspectable **value);

    HRESULT (STDMETHODCALLTYPE *get_NewValue)(
        __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgs *This,
        IInspectable **value);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgsVtbl;

interface __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgs {
    CONST_VTBL __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgs_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgs_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgs_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IDependencyPropertyChangedEventArgs methods ***/
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgs_get_Property(This,value) (This)->lpVtbl->get_Property(This,value)
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgs_get_OldValue(This,value) (This)->lpVtbl->get_OldValue(This,value)
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgs_get_NewValue(This,value) (This)->lpVtbl->get_NewValue(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgs_QueryInterface(__x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgs_AddRef(__x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgs_Release(__x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgs_GetIids(__x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgs* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgs_GetRuntimeClassName(__x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgs* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgs_GetTrustLevel(__x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgs* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IDependencyPropertyChangedEventArgs methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgs_get_Property(__x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgs* This,__x_ABI_CWindows_CUI_CXaml_CIDependencyProperty **value) {
    return This->lpVtbl->get_Property(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgs_get_OldValue(__x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgs* This,IInspectable **value) {
    return This->lpVtbl->get_OldValue(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgs_get_NewValue(__x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgs* This,IInspectable **value) {
    return This->lpVtbl->get_NewValue(This,value);
}
#endif
#ifdef WIDL_using_Windows_UI_Xaml
#define IID_IDependencyPropertyChangedEventArgs IID___x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgs
#define IDependencyPropertyChangedEventArgsVtbl __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgsVtbl
#define IDependencyPropertyChangedEventArgs __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgs
#define IDependencyPropertyChangedEventArgs_QueryInterface __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgs_QueryInterface
#define IDependencyPropertyChangedEventArgs_AddRef __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgs_AddRef
#define IDependencyPropertyChangedEventArgs_Release __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgs_Release
#define IDependencyPropertyChangedEventArgs_GetIids __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgs_GetIids
#define IDependencyPropertyChangedEventArgs_GetRuntimeClassName __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgs_GetRuntimeClassName
#define IDependencyPropertyChangedEventArgs_GetTrustLevel __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgs_GetTrustLevel
#define IDependencyPropertyChangedEventArgs_get_Property __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgs_get_Property
#define IDependencyPropertyChangedEventArgs_get_OldValue __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgs_get_OldValue
#define IDependencyPropertyChangedEventArgs_get_NewValue __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgs_get_NewValue
#endif /* WIDL_using_Windows_UI_Xaml */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyChangedEventArgs_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IDependencyPropertyStatics interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStatics_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStatics_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStatics, 0x49e5f28f, 0x8259, 0x4d5c, 0xaa,0xe0, 0x83,0xd5,0x6d,0xbb,0x68,0xd9);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Xaml {
                MIDL_INTERFACE("49e5f28f-8259-4d5c-aae0-83d56dbb68d9")
                IDependencyPropertyStatics : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_UnsetValue(
                        IInspectable **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE Register(
                        HSTRING name,
                        ABI::Windows::UI::Xaml::Interop::TypeName property_type,
                        ABI::Windows::UI::Xaml::Interop::TypeName owner_type,
                        ABI::Windows::UI::Xaml::IPropertyMetadata *type_metadata,
                        ABI::Windows::UI::Xaml::IDependencyProperty **result) = 0;

                    virtual HRESULT STDMETHODCALLTYPE RegisterAttached(
                        HSTRING name,
                        ABI::Windows::UI::Xaml::Interop::TypeName property_type,
                        ABI::Windows::UI::Xaml::Interop::TypeName owner_type,
                        ABI::Windows::UI::Xaml::IPropertyMetadata *default_metadata,
                        ABI::Windows::UI::Xaml::IDependencyProperty **result) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStatics, 0x49e5f28f, 0x8259, 0x4d5c, 0xaa,0xe0, 0x83,0xd5,0x6d,0xbb,0x68,0xd9)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStaticsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStatics *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStatics *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStatics *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStatics *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStatics *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStatics *This,
        TrustLevel *trustLevel);

    /*** IDependencyPropertyStatics methods ***/
    HRESULT (STDMETHODCALLTYPE *get_UnsetValue)(
        __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStatics *This,
        IInspectable **value);

    HRESULT (STDMETHODCALLTYPE *Register)(
        __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStatics *This,
        HSTRING name,
        __x_ABI_CWindows_CUI_CXaml_CInterop_CTypeName property_type,
        __x_ABI_CWindows_CUI_CXaml_CInterop_CTypeName owner_type,
        __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata *type_metadata,
        __x_ABI_CWindows_CUI_CXaml_CIDependencyProperty **result);

    HRESULT (STDMETHODCALLTYPE *RegisterAttached)(
        __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStatics *This,
        HSTRING name,
        __x_ABI_CWindows_CUI_CXaml_CInterop_CTypeName property_type,
        __x_ABI_CWindows_CUI_CXaml_CInterop_CTypeName owner_type,
        __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata *default_metadata,
        __x_ABI_CWindows_CUI_CXaml_CIDependencyProperty **result);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStaticsVtbl;

interface __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStatics {
    CONST_VTBL __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStaticsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStatics_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStatics_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStatics_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStatics_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStatics_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStatics_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IDependencyPropertyStatics methods ***/
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStatics_get_UnsetValue(This,value) (This)->lpVtbl->get_UnsetValue(This,value)
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStatics_Register(This,name,property_type,owner_type,type_metadata,result) (This)->lpVtbl->Register(This,name,property_type,owner_type,type_metadata,result)
#define __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStatics_RegisterAttached(This,name,property_type,owner_type,default_metadata,result) (This)->lpVtbl->RegisterAttached(This,name,property_type,owner_type,default_metadata,result)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStatics_QueryInterface(__x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStatics* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStatics_AddRef(__x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStatics* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStatics_Release(__x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStatics* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStatics_GetIids(__x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStatics* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStatics_GetRuntimeClassName(__x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStatics* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStatics_GetTrustLevel(__x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStatics* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IDependencyPropertyStatics methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStatics_get_UnsetValue(__x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStatics* This,IInspectable **value) {
    return This->lpVtbl->get_UnsetValue(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStatics_Register(__x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStatics* This,HSTRING name,__x_ABI_CWindows_CUI_CXaml_CInterop_CTypeName property_type,__x_ABI_CWindows_CUI_CXaml_CInterop_CTypeName owner_type,__x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata *type_metadata,__x_ABI_CWindows_CUI_CXaml_CIDependencyProperty **result) {
    return This->lpVtbl->Register(This,name,property_type,owner_type,type_metadata,result);
}
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStatics_RegisterAttached(__x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStatics* This,HSTRING name,__x_ABI_CWindows_CUI_CXaml_CInterop_CTypeName property_type,__x_ABI_CWindows_CUI_CXaml_CInterop_CTypeName owner_type,__x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata *default_metadata,__x_ABI_CWindows_CUI_CXaml_CIDependencyProperty **result) {
    return This->lpVtbl->RegisterAttached(This,name,property_type,owner_type,default_metadata,result);
}
#endif
#ifdef WIDL_using_Windows_UI_Xaml
#define IID_IDependencyPropertyStatics IID___x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStatics
#define IDependencyPropertyStaticsVtbl __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStaticsVtbl
#define IDependencyPropertyStatics __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStatics
#define IDependencyPropertyStatics_QueryInterface __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStatics_QueryInterface
#define IDependencyPropertyStatics_AddRef __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStatics_AddRef
#define IDependencyPropertyStatics_Release __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStatics_Release
#define IDependencyPropertyStatics_GetIids __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStatics_GetIids
#define IDependencyPropertyStatics_GetRuntimeClassName __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStatics_GetRuntimeClassName
#define IDependencyPropertyStatics_GetTrustLevel __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStatics_GetTrustLevel
#define IDependencyPropertyStatics_get_UnsetValue __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStatics_get_UnsetValue
#define IDependencyPropertyStatics_Register __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStatics_Register
#define IDependencyPropertyStatics_RegisterAttached __x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStatics_RegisterAttached
#endif /* WIDL_using_Windows_UI_Xaml */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CXaml_CIDependencyPropertyStatics_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IPropertyMetadata interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata, 0x814ef30d, 0x8d18, 0x448a, 0x86,0x44, 0xf2,0xcb,0x51,0xe7,0x03,0x80);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Xaml {
                MIDL_INTERFACE("814ef30d-8d18-448a-8644-f2cb51e70380")
                IPropertyMetadata : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_DefaultValue(
                        IInspectable **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_CreateDefaultValueCallback(
                        ABI::Windows::UI::Xaml::ICreateDefaultValueCallback **value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata, 0x814ef30d, 0x8d18, 0x448a, 0x86,0x44, 0xf2,0xcb,0x51,0xe7,0x03,0x80)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata *This,
        TrustLevel *trustLevel);

    /*** IPropertyMetadata methods ***/
    HRESULT (STDMETHODCALLTYPE *get_DefaultValue)(
        __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata *This,
        IInspectable **value);

    HRESULT (STDMETHODCALLTYPE *get_CreateDefaultValueCallback)(
        __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata *This,
        __x_ABI_CWindows_CUI_CXaml_CICreateDefaultValueCallback **value);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataVtbl;

interface __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata {
    CONST_VTBL __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IPropertyMetadata methods ***/
#define __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata_get_DefaultValue(This,value) (This)->lpVtbl->get_DefaultValue(This,value)
#define __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata_get_CreateDefaultValueCallback(This,value) (This)->lpVtbl->get_CreateDefaultValueCallback(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata_QueryInterface(__x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata_AddRef(__x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata_Release(__x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata_GetIids(__x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata_GetRuntimeClassName(__x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata_GetTrustLevel(__x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IPropertyMetadata methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata_get_DefaultValue(__x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata* This,IInspectable **value) {
    return This->lpVtbl->get_DefaultValue(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata_get_CreateDefaultValueCallback(__x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata* This,__x_ABI_CWindows_CUI_CXaml_CICreateDefaultValueCallback **value) {
    return This->lpVtbl->get_CreateDefaultValueCallback(This,value);
}
#endif
#ifdef WIDL_using_Windows_UI_Xaml
#define IID_IPropertyMetadata IID___x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata
#define IPropertyMetadataVtbl __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataVtbl
#define IPropertyMetadata __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata
#define IPropertyMetadata_QueryInterface __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata_QueryInterface
#define IPropertyMetadata_AddRef __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata_AddRef
#define IPropertyMetadata_Release __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata_Release
#define IPropertyMetadata_GetIids __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata_GetIids
#define IPropertyMetadata_GetRuntimeClassName __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata_GetRuntimeClassName
#define IPropertyMetadata_GetTrustLevel __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata_GetTrustLevel
#define IPropertyMetadata_get_DefaultValue __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata_get_DefaultValue
#define IPropertyMetadata_get_CreateDefaultValueCallback __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata_get_CreateDefaultValueCallback
#endif /* WIDL_using_Windows_UI_Xaml */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IPropertyMetadataFactory interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataFactory_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataFactory_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataFactory, 0xc1b81cc0, 0x57cd, 0x4f2f, 0xb0,0xa9, 0xe1,0x80,0x1b,0x28,0xf7,0x6b);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Xaml {
                MIDL_INTERFACE("c1b81cc0-57cd-4f2f-b0a9-e1801b28f76b")
                IPropertyMetadataFactory : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE CreateInstanceWithDefaultValue(
                        IInspectable *default_value,
                        IInspectable *base_interface,
                        IInspectable **inner_interface,
                        ABI::Windows::UI::Xaml::IPropertyMetadata **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE CreateInstanceWithDefaultValueAndCallback(
                        IInspectable *default_value,
                        ABI::Windows::UI::Xaml::IPropertyChangedCallback *property_changed_callback,
                        IInspectable *base_interface,
                        IInspectable **inner_interface,
                        ABI::Windows::UI::Xaml::IPropertyMetadata **value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataFactory, 0xc1b81cc0, 0x57cd, 0x4f2f, 0xb0,0xa9, 0xe1,0x80,0x1b,0x28,0xf7,0x6b)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataFactoryVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataFactory *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataFactory *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataFactory *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataFactory *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataFactory *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataFactory *This,
        TrustLevel *trustLevel);

    /*** IPropertyMetadataFactory methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateInstanceWithDefaultValue)(
        __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataFactory *This,
        IInspectable *default_value,
        IInspectable *base_interface,
        IInspectable **inner_interface,
        __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata **value);

    HRESULT (STDMETHODCALLTYPE *CreateInstanceWithDefaultValueAndCallback)(
        __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataFactory *This,
        IInspectable *default_value,
        __x_ABI_CWindows_CUI_CXaml_CIPropertyChangedCallback *property_changed_callback,
        IInspectable *base_interface,
        IInspectable **inner_interface,
        __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata **value);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataFactoryVtbl;

interface __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataFactory {
    CONST_VTBL __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataFactoryVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataFactory_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataFactory_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataFactory_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataFactory_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataFactory_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataFactory_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IPropertyMetadataFactory methods ***/
#define __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataFactory_CreateInstanceWithDefaultValue(This,default_value,base_interface,inner_interface,value) (This)->lpVtbl->CreateInstanceWithDefaultValue(This,default_value,base_interface,inner_interface,value)
#define __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataFactory_CreateInstanceWithDefaultValueAndCallback(This,default_value,property_changed_callback,base_interface,inner_interface,value) (This)->lpVtbl->CreateInstanceWithDefaultValueAndCallback(This,default_value,property_changed_callback,base_interface,inner_interface,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataFactory_QueryInterface(__x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataFactory* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataFactory_AddRef(__x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataFactory* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataFactory_Release(__x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataFactory* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataFactory_GetIids(__x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataFactory* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataFactory_GetRuntimeClassName(__x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataFactory* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataFactory_GetTrustLevel(__x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataFactory* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IPropertyMetadataFactory methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataFactory_CreateInstanceWithDefaultValue(__x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataFactory* This,IInspectable *default_value,IInspectable *base_interface,IInspectable **inner_interface,__x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata **value) {
    return This->lpVtbl->CreateInstanceWithDefaultValue(This,default_value,base_interface,inner_interface,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataFactory_CreateInstanceWithDefaultValueAndCallback(__x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataFactory* This,IInspectable *default_value,__x_ABI_CWindows_CUI_CXaml_CIPropertyChangedCallback *property_changed_callback,IInspectable *base_interface,IInspectable **inner_interface,__x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata **value) {
    return This->lpVtbl->CreateInstanceWithDefaultValueAndCallback(This,default_value,property_changed_callback,base_interface,inner_interface,value);
}
#endif
#ifdef WIDL_using_Windows_UI_Xaml
#define IID_IPropertyMetadataFactory IID___x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataFactory
#define IPropertyMetadataFactoryVtbl __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataFactoryVtbl
#define IPropertyMetadataFactory __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataFactory
#define IPropertyMetadataFactory_QueryInterface __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataFactory_QueryInterface
#define IPropertyMetadataFactory_AddRef __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataFactory_AddRef
#define IPropertyMetadataFactory_Release __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataFactory_Release
#define IPropertyMetadataFactory_GetIids __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataFactory_GetIids
#define IPropertyMetadataFactory_GetRuntimeClassName __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataFactory_GetRuntimeClassName
#define IPropertyMetadataFactory_GetTrustLevel __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataFactory_GetTrustLevel
#define IPropertyMetadataFactory_CreateInstanceWithDefaultValue __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataFactory_CreateInstanceWithDefaultValue
#define IPropertyMetadataFactory_CreateInstanceWithDefaultValueAndCallback __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataFactory_CreateInstanceWithDefaultValueAndCallback
#endif /* WIDL_using_Windows_UI_Xaml */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataFactory_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IPropertyMetadataStatics interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics, 0x3b01077a, 0x6e06, 0x45e9, 0x8b,0x5c, 0xaf,0x24,0x34,0x58,0xc0,0x62);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Xaml {
                MIDL_INTERFACE("3b01077a-6e06-45e9-8b5c-af243458c062")
                IPropertyMetadataStatics : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE CreateWithDefaultValue(
                        IInspectable *default_value,
                        ABI::Windows::UI::Xaml::IPropertyMetadata **result) = 0;

                    virtual HRESULT STDMETHODCALLTYPE CreateWithDefaultValueAndCallback(
                        IInspectable *default_value,
                        ABI::Windows::UI::Xaml::IPropertyChangedCallback *property_changed_callback,
                        ABI::Windows::UI::Xaml::IPropertyMetadata **result) = 0;

                    virtual HRESULT STDMETHODCALLTYPE CreateWithFactory(
                        ABI::Windows::UI::Xaml::ICreateDefaultValueCallback *create_default_value_callback,
                        ABI::Windows::UI::Xaml::IPropertyMetadata **result) = 0;

                    virtual HRESULT STDMETHODCALLTYPE CreateWithFactoryAndCallback(
                        ABI::Windows::UI::Xaml::ICreateDefaultValueCallback *create_default_value_callback,
                        ABI::Windows::UI::Xaml::IPropertyChangedCallback *property_changed_callback,
                        ABI::Windows::UI::Xaml::IPropertyMetadata **result) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics, 0x3b01077a, 0x6e06, 0x45e9, 0x8b,0x5c, 0xaf,0x24,0x34,0x58,0xc0,0x62)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStaticsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics *This,
        TrustLevel *trustLevel);

    /*** IPropertyMetadataStatics methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateWithDefaultValue)(
        __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics *This,
        IInspectable *default_value,
        __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata **result);

    HRESULT (STDMETHODCALLTYPE *CreateWithDefaultValueAndCallback)(
        __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics *This,
        IInspectable *default_value,
        __x_ABI_CWindows_CUI_CXaml_CIPropertyChangedCallback *property_changed_callback,
        __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata **result);

    HRESULT (STDMETHODCALLTYPE *CreateWithFactory)(
        __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics *This,
        __x_ABI_CWindows_CUI_CXaml_CICreateDefaultValueCallback *create_default_value_callback,
        __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata **result);

    HRESULT (STDMETHODCALLTYPE *CreateWithFactoryAndCallback)(
        __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics *This,
        __x_ABI_CWindows_CUI_CXaml_CICreateDefaultValueCallback *create_default_value_callback,
        __x_ABI_CWindows_CUI_CXaml_CIPropertyChangedCallback *property_changed_callback,
        __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata **result);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStaticsVtbl;

interface __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics {
    CONST_VTBL __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStaticsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IPropertyMetadataStatics methods ***/
#define __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics_CreateWithDefaultValue(This,default_value,result) (This)->lpVtbl->CreateWithDefaultValue(This,default_value,result)
#define __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics_CreateWithDefaultValueAndCallback(This,default_value,property_changed_callback,result) (This)->lpVtbl->CreateWithDefaultValueAndCallback(This,default_value,property_changed_callback,result)
#define __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics_CreateWithFactory(This,create_default_value_callback,result) (This)->lpVtbl->CreateWithFactory(This,create_default_value_callback,result)
#define __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics_CreateWithFactoryAndCallback(This,create_default_value_callback,property_changed_callback,result) (This)->lpVtbl->CreateWithFactoryAndCallback(This,create_default_value_callback,property_changed_callback,result)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics_QueryInterface(__x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics_AddRef(__x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics_Release(__x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics_GetIids(__x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics_GetRuntimeClassName(__x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics_GetTrustLevel(__x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IPropertyMetadataStatics methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics_CreateWithDefaultValue(__x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics* This,IInspectable *default_value,__x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata **result) {
    return This->lpVtbl->CreateWithDefaultValue(This,default_value,result);
}
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics_CreateWithDefaultValueAndCallback(__x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics* This,IInspectable *default_value,__x_ABI_CWindows_CUI_CXaml_CIPropertyChangedCallback *property_changed_callback,__x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata **result) {
    return This->lpVtbl->CreateWithDefaultValueAndCallback(This,default_value,property_changed_callback,result);
}
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics_CreateWithFactory(__x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics* This,__x_ABI_CWindows_CUI_CXaml_CICreateDefaultValueCallback *create_default_value_callback,__x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata **result) {
    return This->lpVtbl->CreateWithFactory(This,create_default_value_callback,result);
}
static inline HRESULT __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics_CreateWithFactoryAndCallback(__x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics* This,__x_ABI_CWindows_CUI_CXaml_CICreateDefaultValueCallback *create_default_value_callback,__x_ABI_CWindows_CUI_CXaml_CIPropertyChangedCallback *property_changed_callback,__x_ABI_CWindows_CUI_CXaml_CIPropertyMetadata **result) {
    return This->lpVtbl->CreateWithFactoryAndCallback(This,create_default_value_callback,property_changed_callback,result);
}
#endif
#ifdef WIDL_using_Windows_UI_Xaml
#define IID_IPropertyMetadataStatics IID___x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics
#define IPropertyMetadataStaticsVtbl __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStaticsVtbl
#define IPropertyMetadataStatics __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics
#define IPropertyMetadataStatics_QueryInterface __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics_QueryInterface
#define IPropertyMetadataStatics_AddRef __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics_AddRef
#define IPropertyMetadataStatics_Release __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics_Release
#define IPropertyMetadataStatics_GetIids __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics_GetIids
#define IPropertyMetadataStatics_GetRuntimeClassName __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics_GetRuntimeClassName
#define IPropertyMetadataStatics_GetTrustLevel __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics_GetTrustLevel
#define IPropertyMetadataStatics_CreateWithDefaultValue __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics_CreateWithDefaultValue
#define IPropertyMetadataStatics_CreateWithDefaultValueAndCallback __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics_CreateWithDefaultValueAndCallback
#define IPropertyMetadataStatics_CreateWithFactory __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics_CreateWithFactory
#define IPropertyMetadataStatics_CreateWithFactoryAndCallback __x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics_CreateWithFactoryAndCallback
#endif /* WIDL_using_Windows_UI_Xaml */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CXaml_CIPropertyMetadataStatics_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.UI.Xaml.DataTemplateKey
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_UI_Xaml_DataTemplateKey_DEFINED
#define RUNTIMECLASS_Windows_UI_Xaml_DataTemplateKey_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_UI_Xaml_DataTemplateKey[] = {'W','i','n','d','o','w','s','.','U','I','.','X','a','m','l','.','D','a','t','a','T','e','m','p','l','a','t','e','K','e','y',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_Xaml_DataTemplateKey[] = L"Windows.UI.Xaml.DataTemplateKey";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_Xaml_DataTemplateKey[] = {'W','i','n','d','o','w','s','.','U','I','.','X','a','m','l','.','D','a','t','a','T','e','m','p','l','a','t','e','K','e','y',0};
#endif
#endif /* RUNTIMECLASS_Windows_UI_Xaml_DataTemplateKey_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.UI.Xaml.DependencyObject
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_UI_Xaml_DependencyObject_DEFINED
#define RUNTIMECLASS_Windows_UI_Xaml_DependencyObject_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_UI_Xaml_DependencyObject[] = {'W','i','n','d','o','w','s','.','U','I','.','X','a','m','l','.','D','e','p','e','n','d','e','n','c','y','O','b','j','e','c','t',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_Xaml_DependencyObject[] = L"Windows.UI.Xaml.DependencyObject";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_Xaml_DependencyObject[] = {'W','i','n','d','o','w','s','.','U','I','.','X','a','m','l','.','D','e','p','e','n','d','e','n','c','y','O','b','j','e','c','t',0};
#endif
#endif /* RUNTIMECLASS_Windows_UI_Xaml_DependencyObject_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.UI.Xaml.DependencyProperty
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_UI_Xaml_DependencyProperty_DEFINED
#define RUNTIMECLASS_Windows_UI_Xaml_DependencyProperty_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_UI_Xaml_DependencyProperty[] = {'W','i','n','d','o','w','s','.','U','I','.','X','a','m','l','.','D','e','p','e','n','d','e','n','c','y','P','r','o','p','e','r','t','y',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_Xaml_DependencyProperty[] = L"Windows.UI.Xaml.DependencyProperty";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_Xaml_DependencyProperty[] = {'W','i','n','d','o','w','s','.','U','I','.','X','a','m','l','.','D','e','p','e','n','d','e','n','c','y','P','r','o','p','e','r','t','y',0};
#endif
#endif /* RUNTIMECLASS_Windows_UI_Xaml_DependencyProperty_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.UI.Xaml.DependencyPropertyChangedEventArgs
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_UI_Xaml_DependencyPropertyChangedEventArgs_DEFINED
#define RUNTIMECLASS_Windows_UI_Xaml_DependencyPropertyChangedEventArgs_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_UI_Xaml_DependencyPropertyChangedEventArgs[] = {'W','i','n','d','o','w','s','.','U','I','.','X','a','m','l','.','D','e','p','e','n','d','e','n','c','y','P','r','o','p','e','r','t','y','C','h','a','n','g','e','d','E','v','e','n','t','A','r','g','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_Xaml_DependencyPropertyChangedEventArgs[] = L"Windows.UI.Xaml.DependencyPropertyChangedEventArgs";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_Xaml_DependencyPropertyChangedEventArgs[] = {'W','i','n','d','o','w','s','.','U','I','.','X','a','m','l','.','D','e','p','e','n','d','e','n','c','y','P','r','o','p','e','r','t','y','C','h','a','n','g','e','d','E','v','e','n','t','A','r','g','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_UI_Xaml_DependencyPropertyChangedEventArgs_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.UI.Xaml.PropertyMetadata
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_UI_Xaml_PropertyMetadata_DEFINED
#define RUNTIMECLASS_Windows_UI_Xaml_PropertyMetadata_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_UI_Xaml_PropertyMetadata[] = {'W','i','n','d','o','w','s','.','U','I','.','X','a','m','l','.','P','r','o','p','e','r','t','y','M','e','t','a','d','a','t','a',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_Xaml_PropertyMetadata[] = L"Windows.UI.Xaml.PropertyMetadata";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_Xaml_PropertyMetadata[] = {'W','i','n','d','o','w','s','.','U','I','.','X','a','m','l','.','P','r','o','p','e','r','t','y','M','e','t','a','d','a','t','a',0};
#endif
#endif /* RUNTIMECLASS_Windows_UI_Xaml_PropertyMetadata_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER HSTRING_UserSize     (ULONG *, ULONG, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserMarshal  (ULONG *, unsigned char *, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserUnmarshal(ULONG *, unsigned char *, HSTRING *);
void            __RPC_USER HSTRING_UserFree     (ULONG *, HSTRING *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __windows_ui_xaml_h__ */
