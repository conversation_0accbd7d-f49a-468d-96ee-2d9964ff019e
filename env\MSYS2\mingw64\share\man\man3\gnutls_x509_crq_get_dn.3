.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crq_get_dn" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crq_get_dn \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crq_get_dn(gnutls_x509_crq_t " crq ", char * " buf ", size_t * " buf_size ");"
.SH ARGUMENTS
.IP "gnutls_x509_crq_t crq" 12
should contain a \fBgnutls_x509_crq_t\fP type
.IP "char * buf" 12
a pointer to a structure to hold the name (may be \fBNULL\fP)
.IP "size_t * buf_size" 12
initially holds the size of  \fIbuf\fP 
.SH "DESCRIPTION"
This function will copy the name of the Certificate request subject
to the provided buffer.  The name will be in the form
"C=xxxx,O=yyyy,CN=zzzz" as described in RFC 2253. The output string
 \fIbuf\fP will be ASCII or UTF\-8 encoded, depending on the certificate
data.

This function does not output a fully RFC4514 compliant string, if
that is required see \fBgnutls_x509_crq_get_dn3()\fP.
.SH "RETURNS"
\fBGNUTLS_E_SHORT_MEMORY_BUFFER\fP if the provided buffer is not
long enough, and in that case the * \fIbuf_size\fP will be updated with
the required size.  On success 0 is returned.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
