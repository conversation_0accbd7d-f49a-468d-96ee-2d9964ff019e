/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */
#ifdef __cplusplus
extern "C"{
#endif

#include <rpc.h>
#include <rpcndr.h>

#ifdef _MIDL_USE_GUIDDEF_
#ifndef INITGUID
#define INITGUID
#include <guiddef.h>
#undef INITGUID
#else
#include <guiddef.h>
#endif
#define MIDL_DEFINE_GUID(type,name,l,w1,w2,b1,b2,b3,b4,b5,b6,b7,b8) DEFINE_GUID(name,l,w1,w2,b1,b2,b3,b4,b5,b6,b7,b8)
#else
#ifndef __IID_DEFINED__
#define __IID_DEFINED__
  typedef struct _IID {
    unsigned long x;
    unsigned short s1;
    unsigned short s2;
    unsigned char c[8];
  } IID;
#endif

#ifndef CLSID_DEFINED
#define CLSID_DEFINED
  typedef IID CLSID;
#endif

#define MIDL_DEFINE_GUID(type,name,l,w1,w2,b1,b2,b3,b4,b5,b6,b7,b8) const type name = {l,w1,w2,{b1,b2,b3,b4,b5,b6,b7,b8}}
#endif

  MIDL_DEFINE_GUID(IID,LIBID_CDOEXM,0x25150F00,0x5734,0x11d2,0xA5,0x93,0x00,0xC0,0x4F,0x99,0x0D,0x8A);
  MIDL_DEFINE_GUID(IID,IID_IDistributionList,0x25150F3F,0x5734,0x11d2,0xA5,0x93,0x00,0xC0,0x4F,0x99,0x0D,0x8A);
  MIDL_DEFINE_GUID(IID,IID_IMailRecipient,0x25150F40,0x5734,0x11d2,0xA5,0x93,0x00,0xC0,0x4F,0x99,0x0D,0x8A);
  MIDL_DEFINE_GUID(IID,IID_IMailboxStore,0x25150F41,0x5734,0x11d2,0xA5,0x93,0x00,0xC0,0x4F,0x99,0x0D,0x8A);
  MIDL_DEFINE_GUID(CLSID,CLSID_MailGroup,0x25150F1F,0x5734,0x11d2,0xA5,0x93,0x00,0xC0,0x4F,0x99,0x0D,0x8A);
  MIDL_DEFINE_GUID(CLSID,CLSID_MailRecipient,0x25150F20,0x5734,0x11d2,0xA5,0x93,0x00,0xC0,0x4F,0x99,0x0D,0x8A);
  MIDL_DEFINE_GUID(CLSID,CLSID_Mailbox,0x25150F21,0x5734,0x11d2,0xA5,0x93,0x00,0xC0,0x4F,0x99,0x0D,0x8A);
  MIDL_DEFINE_GUID(CLSID,CLSID_FolderAdmin,0x25150F22,0x5734,0x11d2,0xA5,0x93,0x00,0xC0,0x4F,0x99,0x0D,0x8A);
  MIDL_DEFINE_GUID(CLSID,CLSID_ExchangeServer,0x25150F27,0x5734,0x11d2,0xA5,0x93,0x00,0xC0,0x4F,0x99,0x0D,0x8A);
  MIDL_DEFINE_GUID(CLSID,CLSID_FolderTree,0x25150F23,0x5734,0x11d2,0xA5,0x93,0x00,0xC0,0x4F,0x99,0x0D,0x8A);
  MIDL_DEFINE_GUID(CLSID,CLSID_PublicStoreDB,0x25150F24,0x5734,0x11d2,0xA5,0x93,0x00,0xC0,0x4F,0x99,0x0D,0x8A);
  MIDL_DEFINE_GUID(CLSID,CLSID_MailboxStoreDB,0x25150F25,0x5734,0x11d2,0xA5,0x93,0x00,0xC0,0x4F,0x99,0x0D,0x8A);
  MIDL_DEFINE_GUID(CLSID,CLSID_StorageGroup,0x25150F26,0x5734,0x11d2,0xA5,0x93,0x00,0xC0,0x4F,0x99,0x0D,0x8A);

#undef MIDL_DEFINE_GUID

#ifdef __cplusplus
}
#endif
