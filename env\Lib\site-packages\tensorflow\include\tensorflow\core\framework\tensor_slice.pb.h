// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/framework/tensor_slice.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2ftensor_5fslice_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2ftensor_5fslice_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fframework_2ftensor_5fslice_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fframework_2ftensor_5fslice_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fframework_2ftensor_5fslice_2eproto;
namespace tensorflow {
class TensorSliceProto;
struct TensorSliceProtoDefaultTypeInternal;
extern TensorSliceProtoDefaultTypeInternal _TensorSliceProto_default_instance_;
class TensorSliceProto_Extent;
struct TensorSliceProto_ExtentDefaultTypeInternal;
extern TensorSliceProto_ExtentDefaultTypeInternal _TensorSliceProto_Extent_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::TensorSliceProto* Arena::CreateMaybeMessage<::tensorflow::TensorSliceProto>(Arena*);
template<> ::tensorflow::TensorSliceProto_Extent* Arena::CreateMaybeMessage<::tensorflow::TensorSliceProto_Extent>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

// ===================================================================

class TensorSliceProto_Extent final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.TensorSliceProto.Extent) */ {
 public:
  inline TensorSliceProto_Extent() : TensorSliceProto_Extent(nullptr) {}
  ~TensorSliceProto_Extent() override;
  explicit PROTOBUF_CONSTEXPR TensorSliceProto_Extent(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TensorSliceProto_Extent(const TensorSliceProto_Extent& from);
  TensorSliceProto_Extent(TensorSliceProto_Extent&& from) noexcept
    : TensorSliceProto_Extent() {
    *this = ::std::move(from);
  }

  inline TensorSliceProto_Extent& operator=(const TensorSliceProto_Extent& from) {
    CopyFrom(from);
    return *this;
  }
  inline TensorSliceProto_Extent& operator=(TensorSliceProto_Extent&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TensorSliceProto_Extent& default_instance() {
    return *internal_default_instance();
  }
  enum HasLengthCase {
    kLength = 2,
    HAS_LENGTH_NOT_SET = 0,
  };

  static inline const TensorSliceProto_Extent* internal_default_instance() {
    return reinterpret_cast<const TensorSliceProto_Extent*>(
               &_TensorSliceProto_Extent_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(TensorSliceProto_Extent& a, TensorSliceProto_Extent& b) {
    a.Swap(&b);
  }
  inline void Swap(TensorSliceProto_Extent* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TensorSliceProto_Extent* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TensorSliceProto_Extent* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TensorSliceProto_Extent>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TensorSliceProto_Extent& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const TensorSliceProto_Extent& from) {
    TensorSliceProto_Extent::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TensorSliceProto_Extent* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.TensorSliceProto.Extent";
  }
  protected:
  explicit TensorSliceProto_Extent(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kStartFieldNumber = 1,
    kLengthFieldNumber = 2,
  };
  // int64 start = 1;
  void clear_start();
  int64_t start() const;
  void set_start(int64_t value);
  private:
  int64_t _internal_start() const;
  void _internal_set_start(int64_t value);
  public:

  // int64 length = 2;
  bool has_length() const;
  private:
  bool _internal_has_length() const;
  public:
  void clear_length();
  int64_t length() const;
  void set_length(int64_t value);
  private:
  int64_t _internal_length() const;
  void _internal_set_length(int64_t value);
  public:

  void clear_has_length();
  HasLengthCase has_length_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.TensorSliceProto.Extent)
 private:
  class _Internal;
  void set_has_length();

  inline bool has_has_length() const;
  inline void clear_has_has_length();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int64_t start_;
    union HasLengthUnion {
      constexpr HasLengthUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      int64_t length_;
    } has_length_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    uint32_t _oneof_case_[1];

  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2ftensor_5fslice_2eproto;
};
// -------------------------------------------------------------------

class TensorSliceProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.TensorSliceProto) */ {
 public:
  inline TensorSliceProto() : TensorSliceProto(nullptr) {}
  ~TensorSliceProto() override;
  explicit PROTOBUF_CONSTEXPR TensorSliceProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TensorSliceProto(const TensorSliceProto& from);
  TensorSliceProto(TensorSliceProto&& from) noexcept
    : TensorSliceProto() {
    *this = ::std::move(from);
  }

  inline TensorSliceProto& operator=(const TensorSliceProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline TensorSliceProto& operator=(TensorSliceProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TensorSliceProto& default_instance() {
    return *internal_default_instance();
  }
  static inline const TensorSliceProto* internal_default_instance() {
    return reinterpret_cast<const TensorSliceProto*>(
               &_TensorSliceProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(TensorSliceProto& a, TensorSliceProto& b) {
    a.Swap(&b);
  }
  inline void Swap(TensorSliceProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TensorSliceProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TensorSliceProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TensorSliceProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TensorSliceProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const TensorSliceProto& from) {
    TensorSliceProto::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TensorSliceProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.TensorSliceProto";
  }
  protected:
  explicit TensorSliceProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef TensorSliceProto_Extent Extent;

  // accessors -------------------------------------------------------

  enum : int {
    kExtentFieldNumber = 1,
  };
  // repeated .tensorflow.TensorSliceProto.Extent extent = 1;
  int extent_size() const;
  private:
  int _internal_extent_size() const;
  public:
  void clear_extent();
  ::tensorflow::TensorSliceProto_Extent* mutable_extent(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorSliceProto_Extent >*
      mutable_extent();
  private:
  const ::tensorflow::TensorSliceProto_Extent& _internal_extent(int index) const;
  ::tensorflow::TensorSliceProto_Extent* _internal_add_extent();
  public:
  const ::tensorflow::TensorSliceProto_Extent& extent(int index) const;
  ::tensorflow::TensorSliceProto_Extent* add_extent();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorSliceProto_Extent >&
      extent() const;

  // @@protoc_insertion_point(class_scope:tensorflow.TensorSliceProto)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorSliceProto_Extent > extent_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2ftensor_5fslice_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// TensorSliceProto_Extent

// int64 start = 1;
inline void TensorSliceProto_Extent::clear_start() {
  _impl_.start_ = int64_t{0};
}
inline int64_t TensorSliceProto_Extent::_internal_start() const {
  return _impl_.start_;
}
inline int64_t TensorSliceProto_Extent::start() const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorSliceProto.Extent.start)
  return _internal_start();
}
inline void TensorSliceProto_Extent::_internal_set_start(int64_t value) {
  
  _impl_.start_ = value;
}
inline void TensorSliceProto_Extent::set_start(int64_t value) {
  _internal_set_start(value);
  // @@protoc_insertion_point(field_set:tensorflow.TensorSliceProto.Extent.start)
}

// int64 length = 2;
inline bool TensorSliceProto_Extent::_internal_has_length() const {
  return has_length_case() == kLength;
}
inline bool TensorSliceProto_Extent::has_length() const {
  return _internal_has_length();
}
inline void TensorSliceProto_Extent::set_has_length() {
  _impl_._oneof_case_[0] = kLength;
}
inline void TensorSliceProto_Extent::clear_length() {
  if (_internal_has_length()) {
    _impl_.has_length_.length_ = int64_t{0};
    clear_has_has_length();
  }
}
inline int64_t TensorSliceProto_Extent::_internal_length() const {
  if (_internal_has_length()) {
    return _impl_.has_length_.length_;
  }
  return int64_t{0};
}
inline void TensorSliceProto_Extent::_internal_set_length(int64_t value) {
  if (!_internal_has_length()) {
    clear_has_length();
    set_has_length();
  }
  _impl_.has_length_.length_ = value;
}
inline int64_t TensorSliceProto_Extent::length() const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorSliceProto.Extent.length)
  return _internal_length();
}
inline void TensorSliceProto_Extent::set_length(int64_t value) {
  _internal_set_length(value);
  // @@protoc_insertion_point(field_set:tensorflow.TensorSliceProto.Extent.length)
}

inline bool TensorSliceProto_Extent::has_has_length() const {
  return has_length_case() != HAS_LENGTH_NOT_SET;
}
inline void TensorSliceProto_Extent::clear_has_has_length() {
  _impl_._oneof_case_[0] = HAS_LENGTH_NOT_SET;
}
inline TensorSliceProto_Extent::HasLengthCase TensorSliceProto_Extent::has_length_case() const {
  return TensorSliceProto_Extent::HasLengthCase(_impl_._oneof_case_[0]);
}
// -------------------------------------------------------------------

// TensorSliceProto

// repeated .tensorflow.TensorSliceProto.Extent extent = 1;
inline int TensorSliceProto::_internal_extent_size() const {
  return _impl_.extent_.size();
}
inline int TensorSliceProto::extent_size() const {
  return _internal_extent_size();
}
inline void TensorSliceProto::clear_extent() {
  _impl_.extent_.Clear();
}
inline ::tensorflow::TensorSliceProto_Extent* TensorSliceProto::mutable_extent(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.TensorSliceProto.extent)
  return _impl_.extent_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorSliceProto_Extent >*
TensorSliceProto::mutable_extent() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.TensorSliceProto.extent)
  return &_impl_.extent_;
}
inline const ::tensorflow::TensorSliceProto_Extent& TensorSliceProto::_internal_extent(int index) const {
  return _impl_.extent_.Get(index);
}
inline const ::tensorflow::TensorSliceProto_Extent& TensorSliceProto::extent(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorSliceProto.extent)
  return _internal_extent(index);
}
inline ::tensorflow::TensorSliceProto_Extent* TensorSliceProto::_internal_add_extent() {
  return _impl_.extent_.Add();
}
inline ::tensorflow::TensorSliceProto_Extent* TensorSliceProto::add_extent() {
  ::tensorflow::TensorSliceProto_Extent* _add = _internal_add_extent();
  // @@protoc_insertion_point(field_add:tensorflow.TensorSliceProto.extent)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorSliceProto_Extent >&
TensorSliceProto::extent() const {
  // @@protoc_insertion_point(field_list:tensorflow.TensorSliceProto.extent)
  return _impl_.extent_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2ftensor_5fslice_2eproto
