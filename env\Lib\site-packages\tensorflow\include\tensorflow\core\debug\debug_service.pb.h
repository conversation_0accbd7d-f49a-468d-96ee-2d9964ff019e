// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/debug/debug_service.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/tensor.pb.h"
#include "tensorflow/core/profiler/tfprof_log.pb.h"
#include "tensorflow/core/protobuf/debug.pb.h"
#include "tensorflow/core/util/event.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto;
namespace tensorflow {
class CallTraceback;
struct CallTracebackDefaultTypeInternal;
extern CallTracebackDefaultTypeInternal _CallTraceback_default_instance_;
class CallTraceback_OriginIdToStringEntry_DoNotUse;
struct CallTraceback_OriginIdToStringEntry_DoNotUseDefaultTypeInternal;
extern CallTraceback_OriginIdToStringEntry_DoNotUseDefaultTypeInternal _CallTraceback_OriginIdToStringEntry_DoNotUse_default_instance_;
class EventReply;
struct EventReplyDefaultTypeInternal;
extern EventReplyDefaultTypeInternal _EventReply_default_instance_;
class EventReply_DebugOpStateChange;
struct EventReply_DebugOpStateChangeDefaultTypeInternal;
extern EventReply_DebugOpStateChangeDefaultTypeInternal _EventReply_DebugOpStateChange_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::CallTraceback* Arena::CreateMaybeMessage<::tensorflow::CallTraceback>(Arena*);
template<> ::tensorflow::CallTraceback_OriginIdToStringEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::CallTraceback_OriginIdToStringEntry_DoNotUse>(Arena*);
template<> ::tensorflow::EventReply* Arena::CreateMaybeMessage<::tensorflow::EventReply>(Arena*);
template<> ::tensorflow::EventReply_DebugOpStateChange* Arena::CreateMaybeMessage<::tensorflow::EventReply_DebugOpStateChange>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

enum EventReply_DebugOpStateChange_State : int {
  EventReply_DebugOpStateChange_State_STATE_UNSPECIFIED = 0,
  EventReply_DebugOpStateChange_State_DISABLED = 1,
  EventReply_DebugOpStateChange_State_READ_ONLY = 2,
  EventReply_DebugOpStateChange_State_READ_WRITE = 3,
  EventReply_DebugOpStateChange_State_EventReply_DebugOpStateChange_State_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  EventReply_DebugOpStateChange_State_EventReply_DebugOpStateChange_State_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool EventReply_DebugOpStateChange_State_IsValid(int value);
constexpr EventReply_DebugOpStateChange_State EventReply_DebugOpStateChange_State_State_MIN = EventReply_DebugOpStateChange_State_STATE_UNSPECIFIED;
constexpr EventReply_DebugOpStateChange_State EventReply_DebugOpStateChange_State_State_MAX = EventReply_DebugOpStateChange_State_READ_WRITE;
constexpr int EventReply_DebugOpStateChange_State_State_ARRAYSIZE = EventReply_DebugOpStateChange_State_State_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* EventReply_DebugOpStateChange_State_descriptor();
template<typename T>
inline const std::string& EventReply_DebugOpStateChange_State_Name(T enum_t_value) {
  static_assert(::std::is_same<T, EventReply_DebugOpStateChange_State>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function EventReply_DebugOpStateChange_State_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    EventReply_DebugOpStateChange_State_descriptor(), enum_t_value);
}
inline bool EventReply_DebugOpStateChange_State_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, EventReply_DebugOpStateChange_State* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<EventReply_DebugOpStateChange_State>(
    EventReply_DebugOpStateChange_State_descriptor(), name, value);
}
enum CallTraceback_CallType : int {
  CallTraceback_CallType_UNSPECIFIED = 0,
  CallTraceback_CallType_GRAPH_EXECUTION = 1,
  CallTraceback_CallType_EAGER_EXECUTION = 2,
  CallTraceback_CallType_CallTraceback_CallType_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  CallTraceback_CallType_CallTraceback_CallType_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool CallTraceback_CallType_IsValid(int value);
constexpr CallTraceback_CallType CallTraceback_CallType_CallType_MIN = CallTraceback_CallType_UNSPECIFIED;
constexpr CallTraceback_CallType CallTraceback_CallType_CallType_MAX = CallTraceback_CallType_EAGER_EXECUTION;
constexpr int CallTraceback_CallType_CallType_ARRAYSIZE = CallTraceback_CallType_CallType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* CallTraceback_CallType_descriptor();
template<typename T>
inline const std::string& CallTraceback_CallType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, CallTraceback_CallType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function CallTraceback_CallType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    CallTraceback_CallType_descriptor(), enum_t_value);
}
inline bool CallTraceback_CallType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, CallTraceback_CallType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<CallTraceback_CallType>(
    CallTraceback_CallType_descriptor(), name, value);
}
// ===================================================================

class EventReply_DebugOpStateChange final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.EventReply.DebugOpStateChange) */ {
 public:
  inline EventReply_DebugOpStateChange() : EventReply_DebugOpStateChange(nullptr) {}
  ~EventReply_DebugOpStateChange() override;
  explicit PROTOBUF_CONSTEXPR EventReply_DebugOpStateChange(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  EventReply_DebugOpStateChange(const EventReply_DebugOpStateChange& from);
  EventReply_DebugOpStateChange(EventReply_DebugOpStateChange&& from) noexcept
    : EventReply_DebugOpStateChange() {
    *this = ::std::move(from);
  }

  inline EventReply_DebugOpStateChange& operator=(const EventReply_DebugOpStateChange& from) {
    CopyFrom(from);
    return *this;
  }
  inline EventReply_DebugOpStateChange& operator=(EventReply_DebugOpStateChange&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const EventReply_DebugOpStateChange& default_instance() {
    return *internal_default_instance();
  }
  static inline const EventReply_DebugOpStateChange* internal_default_instance() {
    return reinterpret_cast<const EventReply_DebugOpStateChange*>(
               &_EventReply_DebugOpStateChange_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(EventReply_DebugOpStateChange& a, EventReply_DebugOpStateChange& b) {
    a.Swap(&b);
  }
  inline void Swap(EventReply_DebugOpStateChange* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(EventReply_DebugOpStateChange* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  EventReply_DebugOpStateChange* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<EventReply_DebugOpStateChange>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const EventReply_DebugOpStateChange& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const EventReply_DebugOpStateChange& from) {
    EventReply_DebugOpStateChange::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(EventReply_DebugOpStateChange* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.EventReply.DebugOpStateChange";
  }
  protected:
  explicit EventReply_DebugOpStateChange(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef EventReply_DebugOpStateChange_State State;
  static constexpr State STATE_UNSPECIFIED =
    EventReply_DebugOpStateChange_State_STATE_UNSPECIFIED;
  static constexpr State DISABLED =
    EventReply_DebugOpStateChange_State_DISABLED;
  static constexpr State READ_ONLY =
    EventReply_DebugOpStateChange_State_READ_ONLY;
  static constexpr State READ_WRITE =
    EventReply_DebugOpStateChange_State_READ_WRITE;
  static inline bool State_IsValid(int value) {
    return EventReply_DebugOpStateChange_State_IsValid(value);
  }
  static constexpr State State_MIN =
    EventReply_DebugOpStateChange_State_State_MIN;
  static constexpr State State_MAX =
    EventReply_DebugOpStateChange_State_State_MAX;
  static constexpr int State_ARRAYSIZE =
    EventReply_DebugOpStateChange_State_State_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  State_descriptor() {
    return EventReply_DebugOpStateChange_State_descriptor();
  }
  template<typename T>
  static inline const std::string& State_Name(T enum_t_value) {
    static_assert(::std::is_same<T, State>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function State_Name.");
    return EventReply_DebugOpStateChange_State_Name(enum_t_value);
  }
  static inline bool State_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      State* value) {
    return EventReply_DebugOpStateChange_State_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kNodeNameFieldNumber = 2,
    kDebugOpFieldNumber = 4,
    kStateFieldNumber = 1,
    kOutputSlotFieldNumber = 3,
  };
  // string node_name = 2;
  void clear_node_name();
  const std::string& node_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_node_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_node_name();
  PROTOBUF_NODISCARD std::string* release_node_name();
  void set_allocated_node_name(std::string* node_name);
  private:
  const std::string& _internal_node_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_node_name(const std::string& value);
  std::string* _internal_mutable_node_name();
  public:

  // string debug_op = 4;
  void clear_debug_op();
  const std::string& debug_op() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_debug_op(ArgT0&& arg0, ArgT... args);
  std::string* mutable_debug_op();
  PROTOBUF_NODISCARD std::string* release_debug_op();
  void set_allocated_debug_op(std::string* debug_op);
  private:
  const std::string& _internal_debug_op() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_debug_op(const std::string& value);
  std::string* _internal_mutable_debug_op();
  public:

  // .tensorflow.EventReply.DebugOpStateChange.State state = 1;
  void clear_state();
  ::tensorflow::EventReply_DebugOpStateChange_State state() const;
  void set_state(::tensorflow::EventReply_DebugOpStateChange_State value);
  private:
  ::tensorflow::EventReply_DebugOpStateChange_State _internal_state() const;
  void _internal_set_state(::tensorflow::EventReply_DebugOpStateChange_State value);
  public:

  // int32 output_slot = 3;
  void clear_output_slot();
  int32_t output_slot() const;
  void set_output_slot(int32_t value);
  private:
  int32_t _internal_output_slot() const;
  void _internal_set_output_slot(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.EventReply.DebugOpStateChange)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr node_name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr debug_op_;
    int state_;
    int32_t output_slot_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto;
};
// -------------------------------------------------------------------

class EventReply final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.EventReply) */ {
 public:
  inline EventReply() : EventReply(nullptr) {}
  ~EventReply() override;
  explicit PROTOBUF_CONSTEXPR EventReply(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  EventReply(const EventReply& from);
  EventReply(EventReply&& from) noexcept
    : EventReply() {
    *this = ::std::move(from);
  }

  inline EventReply& operator=(const EventReply& from) {
    CopyFrom(from);
    return *this;
  }
  inline EventReply& operator=(EventReply&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const EventReply& default_instance() {
    return *internal_default_instance();
  }
  static inline const EventReply* internal_default_instance() {
    return reinterpret_cast<const EventReply*>(
               &_EventReply_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(EventReply& a, EventReply& b) {
    a.Swap(&b);
  }
  inline void Swap(EventReply* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(EventReply* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  EventReply* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<EventReply>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const EventReply& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const EventReply& from) {
    EventReply::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(EventReply* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.EventReply";
  }
  protected:
  explicit EventReply(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef EventReply_DebugOpStateChange DebugOpStateChange;

  // accessors -------------------------------------------------------

  enum : int {
    kDebugOpStateChangesFieldNumber = 1,
    kTensorFieldNumber = 2,
  };
  // repeated .tensorflow.EventReply.DebugOpStateChange debug_op_state_changes = 1;
  int debug_op_state_changes_size() const;
  private:
  int _internal_debug_op_state_changes_size() const;
  public:
  void clear_debug_op_state_changes();
  ::tensorflow::EventReply_DebugOpStateChange* mutable_debug_op_state_changes(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::EventReply_DebugOpStateChange >*
      mutable_debug_op_state_changes();
  private:
  const ::tensorflow::EventReply_DebugOpStateChange& _internal_debug_op_state_changes(int index) const;
  ::tensorflow::EventReply_DebugOpStateChange* _internal_add_debug_op_state_changes();
  public:
  const ::tensorflow::EventReply_DebugOpStateChange& debug_op_state_changes(int index) const;
  ::tensorflow::EventReply_DebugOpStateChange* add_debug_op_state_changes();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::EventReply_DebugOpStateChange >&
      debug_op_state_changes() const;

  // .tensorflow.TensorProto tensor = 2;
  bool has_tensor() const;
  private:
  bool _internal_has_tensor() const;
  public:
  void clear_tensor();
  const ::tensorflow::TensorProto& tensor() const;
  PROTOBUF_NODISCARD ::tensorflow::TensorProto* release_tensor();
  ::tensorflow::TensorProto* mutable_tensor();
  void set_allocated_tensor(::tensorflow::TensorProto* tensor);
  private:
  const ::tensorflow::TensorProto& _internal_tensor() const;
  ::tensorflow::TensorProto* _internal_mutable_tensor();
  public:
  void unsafe_arena_set_allocated_tensor(
      ::tensorflow::TensorProto* tensor);
  ::tensorflow::TensorProto* unsafe_arena_release_tensor();

  // @@protoc_insertion_point(class_scope:tensorflow.EventReply)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::EventReply_DebugOpStateChange > debug_op_state_changes_;
    ::tensorflow::TensorProto* tensor_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto;
};
// -------------------------------------------------------------------

class CallTraceback_OriginIdToStringEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<CallTraceback_OriginIdToStringEntry_DoNotUse, 
    int64_t, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<CallTraceback_OriginIdToStringEntry_DoNotUse, 
    int64_t, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> SuperType;
  CallTraceback_OriginIdToStringEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR CallTraceback_OriginIdToStringEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit CallTraceback_OriginIdToStringEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const CallTraceback_OriginIdToStringEntry_DoNotUse& other);
  static const CallTraceback_OriginIdToStringEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const CallTraceback_OriginIdToStringEntry_DoNotUse*>(&_CallTraceback_OriginIdToStringEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(void*) { return true; }
  static bool ValidateValue(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.CallTraceback.OriginIdToStringEntry.value");
 }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto;
};

// -------------------------------------------------------------------

class CallTraceback final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.CallTraceback) */ {
 public:
  inline CallTraceback() : CallTraceback(nullptr) {}
  ~CallTraceback() override;
  explicit PROTOBUF_CONSTEXPR CallTraceback(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CallTraceback(const CallTraceback& from);
  CallTraceback(CallTraceback&& from) noexcept
    : CallTraceback() {
    *this = ::std::move(from);
  }

  inline CallTraceback& operator=(const CallTraceback& from) {
    CopyFrom(from);
    return *this;
  }
  inline CallTraceback& operator=(CallTraceback&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CallTraceback& default_instance() {
    return *internal_default_instance();
  }
  static inline const CallTraceback* internal_default_instance() {
    return reinterpret_cast<const CallTraceback*>(
               &_CallTraceback_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(CallTraceback& a, CallTraceback& b) {
    a.Swap(&b);
  }
  inline void Swap(CallTraceback* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CallTraceback* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CallTraceback* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CallTraceback>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CallTraceback& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CallTraceback& from) {
    CallTraceback::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CallTraceback* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CallTraceback";
  }
  protected:
  explicit CallTraceback(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  typedef CallTraceback_CallType CallType;
  static constexpr CallType UNSPECIFIED =
    CallTraceback_CallType_UNSPECIFIED;
  static constexpr CallType GRAPH_EXECUTION =
    CallTraceback_CallType_GRAPH_EXECUTION;
  static constexpr CallType EAGER_EXECUTION =
    CallTraceback_CallType_EAGER_EXECUTION;
  static inline bool CallType_IsValid(int value) {
    return CallTraceback_CallType_IsValid(value);
  }
  static constexpr CallType CallType_MIN =
    CallTraceback_CallType_CallType_MIN;
  static constexpr CallType CallType_MAX =
    CallTraceback_CallType_CallType_MAX;
  static constexpr int CallType_ARRAYSIZE =
    CallTraceback_CallType_CallType_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  CallType_descriptor() {
    return CallTraceback_CallType_descriptor();
  }
  template<typename T>
  static inline const std::string& CallType_Name(T enum_t_value) {
    static_assert(::std::is_same<T, CallType>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function CallType_Name.");
    return CallTraceback_CallType_Name(enum_t_value);
  }
  static inline bool CallType_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      CallType* value) {
    return CallTraceback_CallType_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kOriginIdToStringFieldNumber = 4,
    kCallKeyFieldNumber = 2,
    kOriginStackFieldNumber = 3,
    kGraphTracebackFieldNumber = 5,
    kGraphVersionFieldNumber = 6,
    kCallTypeFieldNumber = 1,
  };
  // map<int64, string> origin_id_to_string = 4;
  int origin_id_to_string_size() const;
  private:
  int _internal_origin_id_to_string_size() const;
  public:
  void clear_origin_id_to_string();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< int64_t, std::string >&
      _internal_origin_id_to_string() const;
  ::PROTOBUF_NAMESPACE_ID::Map< int64_t, std::string >*
      _internal_mutable_origin_id_to_string();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< int64_t, std::string >&
      origin_id_to_string() const;
  ::PROTOBUF_NAMESPACE_ID::Map< int64_t, std::string >*
      mutable_origin_id_to_string();

  // string call_key = 2;
  void clear_call_key();
  const std::string& call_key() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_call_key(ArgT0&& arg0, ArgT... args);
  std::string* mutable_call_key();
  PROTOBUF_NODISCARD std::string* release_call_key();
  void set_allocated_call_key(std::string* call_key);
  private:
  const std::string& _internal_call_key() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_call_key(const std::string& value);
  std::string* _internal_mutable_call_key();
  public:

  // .tensorflow.tfprof.CodeDef origin_stack = 3;
  bool has_origin_stack() const;
  private:
  bool _internal_has_origin_stack() const;
  public:
  void clear_origin_stack();
  const ::tensorflow::tfprof::CodeDef& origin_stack() const;
  PROTOBUF_NODISCARD ::tensorflow::tfprof::CodeDef* release_origin_stack();
  ::tensorflow::tfprof::CodeDef* mutable_origin_stack();
  void set_allocated_origin_stack(::tensorflow::tfprof::CodeDef* origin_stack);
  private:
  const ::tensorflow::tfprof::CodeDef& _internal_origin_stack() const;
  ::tensorflow::tfprof::CodeDef* _internal_mutable_origin_stack();
  public:
  void unsafe_arena_set_allocated_origin_stack(
      ::tensorflow::tfprof::CodeDef* origin_stack);
  ::tensorflow::tfprof::CodeDef* unsafe_arena_release_origin_stack();

  // .tensorflow.tfprof.OpLogProto graph_traceback = 5;
  bool has_graph_traceback() const;
  private:
  bool _internal_has_graph_traceback() const;
  public:
  void clear_graph_traceback();
  const ::tensorflow::tfprof::OpLogProto& graph_traceback() const;
  PROTOBUF_NODISCARD ::tensorflow::tfprof::OpLogProto* release_graph_traceback();
  ::tensorflow::tfprof::OpLogProto* mutable_graph_traceback();
  void set_allocated_graph_traceback(::tensorflow::tfprof::OpLogProto* graph_traceback);
  private:
  const ::tensorflow::tfprof::OpLogProto& _internal_graph_traceback() const;
  ::tensorflow::tfprof::OpLogProto* _internal_mutable_graph_traceback();
  public:
  void unsafe_arena_set_allocated_graph_traceback(
      ::tensorflow::tfprof::OpLogProto* graph_traceback);
  ::tensorflow::tfprof::OpLogProto* unsafe_arena_release_graph_traceback();

  // int64 graph_version = 6;
  void clear_graph_version();
  int64_t graph_version() const;
  void set_graph_version(int64_t value);
  private:
  int64_t _internal_graph_version() const;
  void _internal_set_graph_version(int64_t value);
  public:

  // .tensorflow.CallTraceback.CallType call_type = 1;
  void clear_call_type();
  ::tensorflow::CallTraceback_CallType call_type() const;
  void set_call_type(::tensorflow::CallTraceback_CallType value);
  private:
  ::tensorflow::CallTraceback_CallType _internal_call_type() const;
  void _internal_set_call_type(::tensorflow::CallTraceback_CallType value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.CallTraceback)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        CallTraceback_OriginIdToStringEntry_DoNotUse,
        int64_t, std::string,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> origin_id_to_string_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr call_key_;
    ::tensorflow::tfprof::CodeDef* origin_stack_;
    ::tensorflow::tfprof::OpLogProto* graph_traceback_;
    int64_t graph_version_;
    int call_type_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// EventReply_DebugOpStateChange

// .tensorflow.EventReply.DebugOpStateChange.State state = 1;
inline void EventReply_DebugOpStateChange::clear_state() {
  _impl_.state_ = 0;
}
inline ::tensorflow::EventReply_DebugOpStateChange_State EventReply_DebugOpStateChange::_internal_state() const {
  return static_cast< ::tensorflow::EventReply_DebugOpStateChange_State >(_impl_.state_);
}
inline ::tensorflow::EventReply_DebugOpStateChange_State EventReply_DebugOpStateChange::state() const {
  // @@protoc_insertion_point(field_get:tensorflow.EventReply.DebugOpStateChange.state)
  return _internal_state();
}
inline void EventReply_DebugOpStateChange::_internal_set_state(::tensorflow::EventReply_DebugOpStateChange_State value) {
  
  _impl_.state_ = value;
}
inline void EventReply_DebugOpStateChange::set_state(::tensorflow::EventReply_DebugOpStateChange_State value) {
  _internal_set_state(value);
  // @@protoc_insertion_point(field_set:tensorflow.EventReply.DebugOpStateChange.state)
}

// string node_name = 2;
inline void EventReply_DebugOpStateChange::clear_node_name() {
  _impl_.node_name_.ClearToEmpty();
}
inline const std::string& EventReply_DebugOpStateChange::node_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.EventReply.DebugOpStateChange.node_name)
  return _internal_node_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void EventReply_DebugOpStateChange::set_node_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.node_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.EventReply.DebugOpStateChange.node_name)
}
inline std::string* EventReply_DebugOpStateChange::mutable_node_name() {
  std::string* _s = _internal_mutable_node_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.EventReply.DebugOpStateChange.node_name)
  return _s;
}
inline const std::string& EventReply_DebugOpStateChange::_internal_node_name() const {
  return _impl_.node_name_.Get();
}
inline void EventReply_DebugOpStateChange::_internal_set_node_name(const std::string& value) {
  
  _impl_.node_name_.Set(value, GetArenaForAllocation());
}
inline std::string* EventReply_DebugOpStateChange::_internal_mutable_node_name() {
  
  return _impl_.node_name_.Mutable(GetArenaForAllocation());
}
inline std::string* EventReply_DebugOpStateChange::release_node_name() {
  // @@protoc_insertion_point(field_release:tensorflow.EventReply.DebugOpStateChange.node_name)
  return _impl_.node_name_.Release();
}
inline void EventReply_DebugOpStateChange::set_allocated_node_name(std::string* node_name) {
  if (node_name != nullptr) {
    
  } else {
    
  }
  _impl_.node_name_.SetAllocated(node_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.node_name_.IsDefault()) {
    _impl_.node_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.EventReply.DebugOpStateChange.node_name)
}

// int32 output_slot = 3;
inline void EventReply_DebugOpStateChange::clear_output_slot() {
  _impl_.output_slot_ = 0;
}
inline int32_t EventReply_DebugOpStateChange::_internal_output_slot() const {
  return _impl_.output_slot_;
}
inline int32_t EventReply_DebugOpStateChange::output_slot() const {
  // @@protoc_insertion_point(field_get:tensorflow.EventReply.DebugOpStateChange.output_slot)
  return _internal_output_slot();
}
inline void EventReply_DebugOpStateChange::_internal_set_output_slot(int32_t value) {
  
  _impl_.output_slot_ = value;
}
inline void EventReply_DebugOpStateChange::set_output_slot(int32_t value) {
  _internal_set_output_slot(value);
  // @@protoc_insertion_point(field_set:tensorflow.EventReply.DebugOpStateChange.output_slot)
}

// string debug_op = 4;
inline void EventReply_DebugOpStateChange::clear_debug_op() {
  _impl_.debug_op_.ClearToEmpty();
}
inline const std::string& EventReply_DebugOpStateChange::debug_op() const {
  // @@protoc_insertion_point(field_get:tensorflow.EventReply.DebugOpStateChange.debug_op)
  return _internal_debug_op();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void EventReply_DebugOpStateChange::set_debug_op(ArgT0&& arg0, ArgT... args) {
 
 _impl_.debug_op_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.EventReply.DebugOpStateChange.debug_op)
}
inline std::string* EventReply_DebugOpStateChange::mutable_debug_op() {
  std::string* _s = _internal_mutable_debug_op();
  // @@protoc_insertion_point(field_mutable:tensorflow.EventReply.DebugOpStateChange.debug_op)
  return _s;
}
inline const std::string& EventReply_DebugOpStateChange::_internal_debug_op() const {
  return _impl_.debug_op_.Get();
}
inline void EventReply_DebugOpStateChange::_internal_set_debug_op(const std::string& value) {
  
  _impl_.debug_op_.Set(value, GetArenaForAllocation());
}
inline std::string* EventReply_DebugOpStateChange::_internal_mutable_debug_op() {
  
  return _impl_.debug_op_.Mutable(GetArenaForAllocation());
}
inline std::string* EventReply_DebugOpStateChange::release_debug_op() {
  // @@protoc_insertion_point(field_release:tensorflow.EventReply.DebugOpStateChange.debug_op)
  return _impl_.debug_op_.Release();
}
inline void EventReply_DebugOpStateChange::set_allocated_debug_op(std::string* debug_op) {
  if (debug_op != nullptr) {
    
  } else {
    
  }
  _impl_.debug_op_.SetAllocated(debug_op, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.debug_op_.IsDefault()) {
    _impl_.debug_op_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.EventReply.DebugOpStateChange.debug_op)
}

// -------------------------------------------------------------------

// EventReply

// repeated .tensorflow.EventReply.DebugOpStateChange debug_op_state_changes = 1;
inline int EventReply::_internal_debug_op_state_changes_size() const {
  return _impl_.debug_op_state_changes_.size();
}
inline int EventReply::debug_op_state_changes_size() const {
  return _internal_debug_op_state_changes_size();
}
inline void EventReply::clear_debug_op_state_changes() {
  _impl_.debug_op_state_changes_.Clear();
}
inline ::tensorflow::EventReply_DebugOpStateChange* EventReply::mutable_debug_op_state_changes(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.EventReply.debug_op_state_changes)
  return _impl_.debug_op_state_changes_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::EventReply_DebugOpStateChange >*
EventReply::mutable_debug_op_state_changes() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.EventReply.debug_op_state_changes)
  return &_impl_.debug_op_state_changes_;
}
inline const ::tensorflow::EventReply_DebugOpStateChange& EventReply::_internal_debug_op_state_changes(int index) const {
  return _impl_.debug_op_state_changes_.Get(index);
}
inline const ::tensorflow::EventReply_DebugOpStateChange& EventReply::debug_op_state_changes(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.EventReply.debug_op_state_changes)
  return _internal_debug_op_state_changes(index);
}
inline ::tensorflow::EventReply_DebugOpStateChange* EventReply::_internal_add_debug_op_state_changes() {
  return _impl_.debug_op_state_changes_.Add();
}
inline ::tensorflow::EventReply_DebugOpStateChange* EventReply::add_debug_op_state_changes() {
  ::tensorflow::EventReply_DebugOpStateChange* _add = _internal_add_debug_op_state_changes();
  // @@protoc_insertion_point(field_add:tensorflow.EventReply.debug_op_state_changes)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::EventReply_DebugOpStateChange >&
EventReply::debug_op_state_changes() const {
  // @@protoc_insertion_point(field_list:tensorflow.EventReply.debug_op_state_changes)
  return _impl_.debug_op_state_changes_;
}

// .tensorflow.TensorProto tensor = 2;
inline bool EventReply::_internal_has_tensor() const {
  return this != internal_default_instance() && _impl_.tensor_ != nullptr;
}
inline bool EventReply::has_tensor() const {
  return _internal_has_tensor();
}
inline const ::tensorflow::TensorProto& EventReply::_internal_tensor() const {
  const ::tensorflow::TensorProto* p = _impl_.tensor_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::TensorProto&>(
      ::tensorflow::_TensorProto_default_instance_);
}
inline const ::tensorflow::TensorProto& EventReply::tensor() const {
  // @@protoc_insertion_point(field_get:tensorflow.EventReply.tensor)
  return _internal_tensor();
}
inline void EventReply::unsafe_arena_set_allocated_tensor(
    ::tensorflow::TensorProto* tensor) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.tensor_);
  }
  _impl_.tensor_ = tensor;
  if (tensor) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.EventReply.tensor)
}
inline ::tensorflow::TensorProto* EventReply::release_tensor() {
  
  ::tensorflow::TensorProto* temp = _impl_.tensor_;
  _impl_.tensor_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::TensorProto* EventReply::unsafe_arena_release_tensor() {
  // @@protoc_insertion_point(field_release:tensorflow.EventReply.tensor)
  
  ::tensorflow::TensorProto* temp = _impl_.tensor_;
  _impl_.tensor_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorProto* EventReply::_internal_mutable_tensor() {
  
  if (_impl_.tensor_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorProto>(GetArenaForAllocation());
    _impl_.tensor_ = p;
  }
  return _impl_.tensor_;
}
inline ::tensorflow::TensorProto* EventReply::mutable_tensor() {
  ::tensorflow::TensorProto* _msg = _internal_mutable_tensor();
  // @@protoc_insertion_point(field_mutable:tensorflow.EventReply.tensor)
  return _msg;
}
inline void EventReply::set_allocated_tensor(::tensorflow::TensorProto* tensor) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.tensor_);
  }
  if (tensor) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(tensor));
    if (message_arena != submessage_arena) {
      tensor = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, tensor, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.tensor_ = tensor;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.EventReply.tensor)
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// CallTraceback

// .tensorflow.CallTraceback.CallType call_type = 1;
inline void CallTraceback::clear_call_type() {
  _impl_.call_type_ = 0;
}
inline ::tensorflow::CallTraceback_CallType CallTraceback::_internal_call_type() const {
  return static_cast< ::tensorflow::CallTraceback_CallType >(_impl_.call_type_);
}
inline ::tensorflow::CallTraceback_CallType CallTraceback::call_type() const {
  // @@protoc_insertion_point(field_get:tensorflow.CallTraceback.call_type)
  return _internal_call_type();
}
inline void CallTraceback::_internal_set_call_type(::tensorflow::CallTraceback_CallType value) {
  
  _impl_.call_type_ = value;
}
inline void CallTraceback::set_call_type(::tensorflow::CallTraceback_CallType value) {
  _internal_set_call_type(value);
  // @@protoc_insertion_point(field_set:tensorflow.CallTraceback.call_type)
}

// string call_key = 2;
inline void CallTraceback::clear_call_key() {
  _impl_.call_key_.ClearToEmpty();
}
inline const std::string& CallTraceback::call_key() const {
  // @@protoc_insertion_point(field_get:tensorflow.CallTraceback.call_key)
  return _internal_call_key();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CallTraceback::set_call_key(ArgT0&& arg0, ArgT... args) {
 
 _impl_.call_key_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.CallTraceback.call_key)
}
inline std::string* CallTraceback::mutable_call_key() {
  std::string* _s = _internal_mutable_call_key();
  // @@protoc_insertion_point(field_mutable:tensorflow.CallTraceback.call_key)
  return _s;
}
inline const std::string& CallTraceback::_internal_call_key() const {
  return _impl_.call_key_.Get();
}
inline void CallTraceback::_internal_set_call_key(const std::string& value) {
  
  _impl_.call_key_.Set(value, GetArenaForAllocation());
}
inline std::string* CallTraceback::_internal_mutable_call_key() {
  
  return _impl_.call_key_.Mutable(GetArenaForAllocation());
}
inline std::string* CallTraceback::release_call_key() {
  // @@protoc_insertion_point(field_release:tensorflow.CallTraceback.call_key)
  return _impl_.call_key_.Release();
}
inline void CallTraceback::set_allocated_call_key(std::string* call_key) {
  if (call_key != nullptr) {
    
  } else {
    
  }
  _impl_.call_key_.SetAllocated(call_key, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.call_key_.IsDefault()) {
    _impl_.call_key_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CallTraceback.call_key)
}

// .tensorflow.tfprof.CodeDef origin_stack = 3;
inline bool CallTraceback::_internal_has_origin_stack() const {
  return this != internal_default_instance() && _impl_.origin_stack_ != nullptr;
}
inline bool CallTraceback::has_origin_stack() const {
  return _internal_has_origin_stack();
}
inline const ::tensorflow::tfprof::CodeDef& CallTraceback::_internal_origin_stack() const {
  const ::tensorflow::tfprof::CodeDef* p = _impl_.origin_stack_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::tfprof::CodeDef&>(
      ::tensorflow::tfprof::_CodeDef_default_instance_);
}
inline const ::tensorflow::tfprof::CodeDef& CallTraceback::origin_stack() const {
  // @@protoc_insertion_point(field_get:tensorflow.CallTraceback.origin_stack)
  return _internal_origin_stack();
}
inline void CallTraceback::unsafe_arena_set_allocated_origin_stack(
    ::tensorflow::tfprof::CodeDef* origin_stack) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.origin_stack_);
  }
  _impl_.origin_stack_ = origin_stack;
  if (origin_stack) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CallTraceback.origin_stack)
}
inline ::tensorflow::tfprof::CodeDef* CallTraceback::release_origin_stack() {
  
  ::tensorflow::tfprof::CodeDef* temp = _impl_.origin_stack_;
  _impl_.origin_stack_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::tfprof::CodeDef* CallTraceback::unsafe_arena_release_origin_stack() {
  // @@protoc_insertion_point(field_release:tensorflow.CallTraceback.origin_stack)
  
  ::tensorflow::tfprof::CodeDef* temp = _impl_.origin_stack_;
  _impl_.origin_stack_ = nullptr;
  return temp;
}
inline ::tensorflow::tfprof::CodeDef* CallTraceback::_internal_mutable_origin_stack() {
  
  if (_impl_.origin_stack_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::tfprof::CodeDef>(GetArenaForAllocation());
    _impl_.origin_stack_ = p;
  }
  return _impl_.origin_stack_;
}
inline ::tensorflow::tfprof::CodeDef* CallTraceback::mutable_origin_stack() {
  ::tensorflow::tfprof::CodeDef* _msg = _internal_mutable_origin_stack();
  // @@protoc_insertion_point(field_mutable:tensorflow.CallTraceback.origin_stack)
  return _msg;
}
inline void CallTraceback::set_allocated_origin_stack(::tensorflow::tfprof::CodeDef* origin_stack) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.origin_stack_);
  }
  if (origin_stack) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(origin_stack));
    if (message_arena != submessage_arena) {
      origin_stack = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, origin_stack, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.origin_stack_ = origin_stack;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CallTraceback.origin_stack)
}

// map<int64, string> origin_id_to_string = 4;
inline int CallTraceback::_internal_origin_id_to_string_size() const {
  return _impl_.origin_id_to_string_.size();
}
inline int CallTraceback::origin_id_to_string_size() const {
  return _internal_origin_id_to_string_size();
}
inline void CallTraceback::clear_origin_id_to_string() {
  _impl_.origin_id_to_string_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< int64_t, std::string >&
CallTraceback::_internal_origin_id_to_string() const {
  return _impl_.origin_id_to_string_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< int64_t, std::string >&
CallTraceback::origin_id_to_string() const {
  // @@protoc_insertion_point(field_map:tensorflow.CallTraceback.origin_id_to_string)
  return _internal_origin_id_to_string();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< int64_t, std::string >*
CallTraceback::_internal_mutable_origin_id_to_string() {
  return _impl_.origin_id_to_string_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< int64_t, std::string >*
CallTraceback::mutable_origin_id_to_string() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.CallTraceback.origin_id_to_string)
  return _internal_mutable_origin_id_to_string();
}

// .tensorflow.tfprof.OpLogProto graph_traceback = 5;
inline bool CallTraceback::_internal_has_graph_traceback() const {
  return this != internal_default_instance() && _impl_.graph_traceback_ != nullptr;
}
inline bool CallTraceback::has_graph_traceback() const {
  return _internal_has_graph_traceback();
}
inline const ::tensorflow::tfprof::OpLogProto& CallTraceback::_internal_graph_traceback() const {
  const ::tensorflow::tfprof::OpLogProto* p = _impl_.graph_traceback_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::tfprof::OpLogProto&>(
      ::tensorflow::tfprof::_OpLogProto_default_instance_);
}
inline const ::tensorflow::tfprof::OpLogProto& CallTraceback::graph_traceback() const {
  // @@protoc_insertion_point(field_get:tensorflow.CallTraceback.graph_traceback)
  return _internal_graph_traceback();
}
inline void CallTraceback::unsafe_arena_set_allocated_graph_traceback(
    ::tensorflow::tfprof::OpLogProto* graph_traceback) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.graph_traceback_);
  }
  _impl_.graph_traceback_ = graph_traceback;
  if (graph_traceback) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CallTraceback.graph_traceback)
}
inline ::tensorflow::tfprof::OpLogProto* CallTraceback::release_graph_traceback() {
  
  ::tensorflow::tfprof::OpLogProto* temp = _impl_.graph_traceback_;
  _impl_.graph_traceback_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::tfprof::OpLogProto* CallTraceback::unsafe_arena_release_graph_traceback() {
  // @@protoc_insertion_point(field_release:tensorflow.CallTraceback.graph_traceback)
  
  ::tensorflow::tfprof::OpLogProto* temp = _impl_.graph_traceback_;
  _impl_.graph_traceback_ = nullptr;
  return temp;
}
inline ::tensorflow::tfprof::OpLogProto* CallTraceback::_internal_mutable_graph_traceback() {
  
  if (_impl_.graph_traceback_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::tfprof::OpLogProto>(GetArenaForAllocation());
    _impl_.graph_traceback_ = p;
  }
  return _impl_.graph_traceback_;
}
inline ::tensorflow::tfprof::OpLogProto* CallTraceback::mutable_graph_traceback() {
  ::tensorflow::tfprof::OpLogProto* _msg = _internal_mutable_graph_traceback();
  // @@protoc_insertion_point(field_mutable:tensorflow.CallTraceback.graph_traceback)
  return _msg;
}
inline void CallTraceback::set_allocated_graph_traceback(::tensorflow::tfprof::OpLogProto* graph_traceback) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.graph_traceback_);
  }
  if (graph_traceback) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(graph_traceback));
    if (message_arena != submessage_arena) {
      graph_traceback = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, graph_traceback, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.graph_traceback_ = graph_traceback;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CallTraceback.graph_traceback)
}

// int64 graph_version = 6;
inline void CallTraceback::clear_graph_version() {
  _impl_.graph_version_ = int64_t{0};
}
inline int64_t CallTraceback::_internal_graph_version() const {
  return _impl_.graph_version_;
}
inline int64_t CallTraceback::graph_version() const {
  // @@protoc_insertion_point(field_get:tensorflow.CallTraceback.graph_version)
  return _internal_graph_version();
}
inline void CallTraceback::_internal_set_graph_version(int64_t value) {
  
  _impl_.graph_version_ = value;
}
inline void CallTraceback::set_graph_version(int64_t value) {
  _internal_set_graph_version(value);
  // @@protoc_insertion_point(field_set:tensorflow.CallTraceback.graph_version)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::tensorflow::EventReply_DebugOpStateChange_State> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::EventReply_DebugOpStateChange_State>() {
  return ::tensorflow::EventReply_DebugOpStateChange_State_descriptor();
}
template <> struct is_proto_enum< ::tensorflow::CallTraceback_CallType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::CallTraceback_CallType>() {
  return ::tensorflow::CallTraceback_CallType_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fdebug_2fdebug_5fservice_2eproto
