/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */

#ifndef _INTSAFE_H_INCLUDED_
#define _INTSAFE_H_INCLUDED_

#include <winapifamily.h>

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)

#include <wtypesbase.h>
#include <specstrings.h>

#define INTSAFE_E_ARITHMETIC_OVERFLOW ((HRESULT)0x80070216)

#ifndef S_OK
#define S_OK ((HRESULT)0)
#endif

#ifdef __clang__
#if __has_builtin(__builtin_add_overflow)
#define __MINGW_INTSAFE_WORKS
#endif
#elif __GNUC__ >= 5
#define __MINGW_INTSAFE_WORKS
#endif

#ifdef __MINGW_INTSAFE_WORKS

#ifndef __MINGW_INTSAFE_API
#define __MINGW_INTSAFE_API FORCEINLINE
#endif

/** If CH<PERSON> is unsigned, use static inline for functions that operate
on chars.  This avoids the risk of linking to the wrong function when
different translation units with different types of chars are linked
together, and code using signed chars will not be affected. */
#ifndef __MINGW_INTSAFE_CHAR_API
#ifdef __CHAR_UNSIGNED__
#define __MINGW_INTSAFE_CHAR_API static inline
#else
#define __MINGW_INTSAFE_CHAR_API __MINGW_INTSAFE_API
#endif
#endif

#define __MINGW_INTSAFE_BODY(operation, x, y, overflow)	\
{ \
  if (__builtin_##operation##_overflow(x, y, result)) \
  { \
      *result = overflow; \
      return INTSAFE_E_ARITHMETIC_OVERFLOW; \
  } \
  return S_OK; \
}

#define __MINGW_INTSAFE_CONV_UCHAR(name, type_src, type_dest) \
    HRESULT name(type_src operand, type_dest * result) \
    __MINGW_INTSAFE_BODY(add, operand, 0, 0)

#define __MINGW_INTSAFE_CONV(name, type_src, type_dest) \
    HRESULT name(type_src operand, type_dest * result) \
    __MINGW_INTSAFE_BODY(add, operand, 0, ~0)

#define __MINGW_INTSAFE_MATH(name, type, operation) \
    HRESULT name(type x, type y, type * result) \
    __MINGW_INTSAFE_BODY(operation, x, y, ~0)

#ifdef __CHAR_UNSIGNED__
#define __MINGW_INTSAFE_CONV_CHAR __MINGW_INTSAFE_CONV_UCHAR
#else
#define __MINGW_INTSAFE_CONV_CHAR __MINGW_INTSAFE_CONV
#endif

__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(UInt8ToInt8, UINT8, INT8)
__MINGW_INTSAFE_CHAR_API __MINGW_INTSAFE_CONV_CHAR(UInt8ToChar, UINT8, CHAR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(ByteToInt8, BYTE, INT8)
__MINGW_INTSAFE_CHAR_API __MINGW_INTSAFE_CONV_CHAR(ByteToChar, BYTE, CHAR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV_UCHAR(Int8ToUChar, INT8, UCHAR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(Int8ToUInt8, INT8, UINT8)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(Int8ToUShort, INT8, USHORT)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(Int8ToUInt, INT8, UINT)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(Int8ToULong, INT8, ULONG)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(Int8ToUIntPtr, INT8, UINT_PTR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(Int8ToULongPtr, INT8, ULONG_PTR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(Int8ToULongLong, INT8, ULONGLONG)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV_UCHAR(UShortToUChar, USHORT, UCHAR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(UShortToUInt8, USHORT, UINT8)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(UShortToByte, USHORT, BYTE)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(UShortToInt8, USHORT, INT8)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(UShortToShort, USHORT, SHORT)
__MINGW_INTSAFE_CHAR_API __MINGW_INTSAFE_CONV_CHAR(UShortToChar, USHORT, CHAR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV_UCHAR(WordToUChar, WORD, UCHAR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(WordToByte, WORD, BYTE)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(WordToShort, WORD, SHORT)
__MINGW_INTSAFE_CHAR_API __MINGW_INTSAFE_CONV_CHAR(WordToChar, WORD, CHAR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV_UCHAR(ShortToUChar, SHORT, UCHAR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(ShortToUInt8, SHORT, UINT8)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(ShortToByte, SHORT, BYTE)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(ShortToInt8, SHORT, INT8)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(ShortToUShort, SHORT, USHORT)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(ShortToWord, SHORT, WORD)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(ShortToUInt, SHORT, UINT)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(ShortToULong, SHORT, ULONG)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(ShortToUIntPtr, SHORT, UINT_PTR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(ShortToULongPtr, SHORT, ULONG_PTR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(ShortToDWordPtr, SHORT, DWORD_PTR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(ShortToULongLong, SHORT, ULONGLONG)
__MINGW_INTSAFE_CHAR_API __MINGW_INTSAFE_CONV_CHAR(ShortToChar, SHORT, CHAR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV_UCHAR(UIntToUChar, UINT, UCHAR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(UIntToUInt8, UINT, UINT8)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(UIntToByte, UINT, BYTE)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(UIntToInt8, UINT, INT8)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(UIntToUShort, UINT, USHORT)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(UIntToWord, UINT, WORD)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(UIntToShort, UINT, SHORT)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(UIntToLong, UINT, LONG)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(UIntToInt, UINT, INT)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(UIntToIntPtr, UINT, INT_PTR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(UIntToPtrdiffT, UINT, ptrdiff_t)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(UIntToLongPtr, UINT, LONG_PTR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(UIntToSSIZET, UINT, SSIZE_T)
__MINGW_INTSAFE_CHAR_API __MINGW_INTSAFE_CONV_CHAR(UIntToChar, UINT, CHAR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV_UCHAR(ULongToUChar, ULONG, UCHAR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(ULongToUInt8, ULONG, UINT8)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(ULongToByte, ULONG, BYTE)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(ULongToInt8, ULONG, INT8)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(ULongToUShort, ULONG, USHORT)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(ULongToWord, ULONG, WORD)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(ULongToShort, ULONG, SHORT)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(ULongToUInt, ULONG, UINT)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(ULongToLong, ULONG, LONG)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(ULongToInt, ULONG, INT)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(ULongToUIntPtr, ULONG, UINT_PTR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(ULongToIntPtr, ULONG, INT_PTR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(ULongToPtrdiffT, ULONG, ptrdiff_t)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(ULongToLongPtr, ULONG, LONG_PTR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(ULongToSSIZET, ULONG, SSIZE_T)
__MINGW_INTSAFE_CHAR_API __MINGW_INTSAFE_CONV_CHAR(ULongToChar, ULONG, CHAR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV_UCHAR(DWordToUChar, DWORD, UCHAR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(DWordToByte, DWORD, BYTE)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(DWordToUShort, DWORD, USHORT)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(DWordToWord, DWORD, WORD)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(DWordToShort, DWORD, SHORT)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(DWordToUInt, DWORD, UINT)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(DWordToLong, DWORD, LONG)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(DWordToInt, DWORD, INT)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(DWordToUIntPtr, DWORD, UINT_PTR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(DWordToIntPtr, DWORD, INT_PTR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(DWordToPtrdiffT, DWORD, ptrdiff_t)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(DWordToLongPtr, DWORD, LONG_PTR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(DWordToSSIZET, DWORD, SSIZE_T)
__MINGW_INTSAFE_CHAR_API __MINGW_INTSAFE_CONV_CHAR(DWordToChar, DWORD, CHAR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV_UCHAR(LongToUChar, LONG, UCHAR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(LongToUInt8, LONG, UINT8)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(LongToByte, LONG, BYTE)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(LongToInt8, LONG, INT8)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(LongToUShort, LONG, USHORT)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(LongToWord, LONG, WORD)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(LongToShort, LONG, SHORT)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(LongToUInt, LONG, UINT)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(LongToULong, LONG, ULONG)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(LongToDWord, LONG, DWORD)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(LongToInt, LONG, INT)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(LongToUIntPtr, LONG, UINT_PTR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(LongToSizeT, LONG, size_t)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(LongToULongPtr, LONG, ULONG_PTR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(LongToDWordPtr, LONG, DWORD_PTR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(LongToIntPtr, LONG, INT_PTR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(LongToPtrdiffT, LONG, ptrdiff_t)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(LongToULongLong, LONG, ULONGLONG)
__MINGW_INTSAFE_CHAR_API __MINGW_INTSAFE_CONV_CHAR(LongToChar, LONG, CHAR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV_UCHAR(IntToUChar, INT, UCHAR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(IntToUInt8, INT, UINT8)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(IntToByte, INT, BYTE)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(IntToInt8, INT, INT8)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(IntToUShort, INT, USHORT)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(IntToWord, INT, WORD)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(IntToShort, INT, SHORT)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(IntToUInt, INT, UINT)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(IntToULong, INT, ULONG)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(IntToDWord, INT, DWORD)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(IntToUIntPtr, INT, UINT_PTR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(IntToSizeT, INT, size_t)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(IntToULongPtr, INT, ULONG_PTR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(IntToDWordPtr, INT, DWORD_PTR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(IntToULongLong, INT, ULONGLONG)
__MINGW_INTSAFE_CHAR_API __MINGW_INTSAFE_CONV_CHAR(IntToChar, INT, CHAR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV_UCHAR(UIntPtrToUChar, UINT_PTR, UCHAR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(UIntPtrToUInt8, UINT_PTR, UINT8)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(UIntPtrToInt8, UINT_PTR, INT8)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(UIntPtrToUInt16, UINT_PTR, UINT16)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(UIntPtrToUShort, UINT_PTR, USHORT)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(UIntPtrToInt16, UINT_PTR, INT16)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(UIntPtrToShort, UINT_PTR, SHORT)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(UIntPtrToUInt, UINT_PTR, UINT)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(UIntPtrToULong, UINT_PTR, ULONG)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(UIntPtrToDWord, UINT_PTR, DWORD)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(UIntPtrToLong, UINT_PTR, LONG)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(UIntPtrToInt, UINT_PTR, INT)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(UIntPtrToIntPtr, UINT_PTR, INT_PTR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(UIntPtrToLongPtr, UINT_PTR, LONG_PTR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(UIntPtrToSSIZET, UINT_PTR, SSIZE_T)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(UIntPtrToInt64, UINT_PTR, INT64)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(UIntPtrToLongLong, UINT_PTR, LONGLONG)
__MINGW_INTSAFE_CHAR_API __MINGW_INTSAFE_CONV_CHAR(UIntPtrToChar, UINT_PTR, CHAR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(SizeTToUInt, size_t, UINT)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(SizeTToULong, size_t, ULONG)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(SizeTToDWord, size_t, DWORD)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(SizeTToLong, size_t, LONG)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(SizeTToInt, size_t, INT)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(SizeTToIntPtr, size_t, INT_PTR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(SizeTToPtrdiffT, size_t, ptrdiff_t)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(SizeTToLongPtr, size_t, LONG_PTR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(SizeTToSSIZET, size_t, SSIZE_T)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(SizeTToInt64, size_t, INT64)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(SizeTToUInt32, size_t, UINT32)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV_UCHAR(ULongPtrToUChar, ULONG_PTR, UCHAR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(ULongPtrToUInt8, ULONG_PTR, UINT8)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(ULongPtrToInt8, ULONG_PTR, INT8)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(ULongPtrToUShort, ULONG_PTR, USHORT)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(ULongPtrToShort, ULONG_PTR, SHORT)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(ULongPtrToUInt, ULONG_PTR, UINT)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(ULongPtrToULong, ULONG_PTR, ULONG)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(ULongPtrToDWord, ULONG_PTR, DWORD)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(ULongPtrToLong, ULONG_PTR, LONG)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(ULongPtrToInt, ULONG_PTR, INT)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(ULongPtrToUIntPtr, ULONG_PTR, UINT_PTR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(ULongPtrToIntPtr, ULONG_PTR, INT_PTR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(ULongPtrToPtrdiffT, ULONG_PTR, ptrdiff_t)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(ULongPtrToLongPtr, ULONG_PTR, LONG_PTR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(ULongPtrToSSIZET, ULONG_PTR, SSIZE_T)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(ULongPtrToInt64, ULONG_PTR, INT64)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(ULongPtrToLongLong, ULONG_PTR, LONGLONG)
__MINGW_INTSAFE_CHAR_API __MINGW_INTSAFE_CONV_CHAR(ULongPtrToChar, ULONG_PTR, CHAR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(DWordPtrToUInt, DWORD_PTR, UINT)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(DWordPtrToULong, DWORD_PTR, ULONG)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(DWordPtrToDWord, DWORD_PTR, DWORD)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(DWordPtrToLong, DWORD_PTR, LONG)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(DWordPtrToInt, DWORD_PTR, INT)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(DWordPtrToUIntPtr, DWORD_PTR, UINT_PTR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(DWordPtrToIntPtr, DWORD_PTR, INT_PTR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(DWordPtrToPtrdiffT, DWORD_PTR, ptrdiff_t)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(DWordPtrToLongPtr, DWORD_PTR, LONG_PTR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(DWordPtrToSSIZET, DWORD_PTR, SSIZE_T)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(DWordPtrToInt64, DWORD_PTR, INT64)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV_UCHAR(IntPtrToUChar, INT_PTR, UCHAR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(IntPtrToUInt8, INT_PTR, UINT8)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(IntPtrToInt8, INT_PTR, INT8)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(IntPtrToUShort, INT_PTR, USHORT)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(IntPtrToShort, INT_PTR, SHORT)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(IntPtrToUInt, INT_PTR, UINT)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(IntPtrToULong, INT_PTR, ULONG)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(IntPtrToDWord, INT_PTR, DWORD)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(IntPtrToLong, INT_PTR, LONG)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(IntPtrToInt, INT_PTR, INT)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(IntPtrToUIntPtr, INT_PTR, UINT_PTR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(IntPtrToSizeT, INT_PTR, size_t)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(IntPtrToULongPtr, INT_PTR, ULONG_PTR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(IntPtrToDWordPtr, INT_PTR, DWORD_PTR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(IntPtrToLongPtr, INT_PTR, LONG_PTR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(IntPtrToULongLong, INT_PTR, ULONGLONG)
__MINGW_INTSAFE_CHAR_API __MINGW_INTSAFE_CONV_CHAR(IntPtrToChar, INT_PTR, CHAR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(PtrdiffTToUInt, ptrdiff_t, UINT)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(PtrdiffTToULong, ptrdiff_t, ULONG)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(PtrdiffTToDWord, ptrdiff_t, DWORD)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(PtrdiffTToLong, ptrdiff_t, LONG)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(PtrdiffTToInt, ptrdiff_t, INT)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(PtrdiffTToUIntPtr, ptrdiff_t, UINT_PTR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(PtrdiffTToSizeT, ptrdiff_t, size_t)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(PtrdiffTToULongPtr, ptrdiff_t, ULONG_PTR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(PtrdiffTToDWordPtr, ptrdiff_t, DWORD_PTR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV_UCHAR(LongPtrToUChar, LONG_PTR, UCHAR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(LongPtrToUInt8, LONG_PTR, UINT8)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(LongPtrToInt8, LONG_PTR, INT8)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(LongPtrToUShort, LONG_PTR, USHORT)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(LongPtrToShort, LONG_PTR, SHORT)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(LongPtrToUInt, LONG_PTR, UINT)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(LongPtrToULong, LONG_PTR, ULONG)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(LongPtrToDWord, LONG_PTR, DWORD)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(LongPtrToLong, LONG_PTR, LONG)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(LongPtrToInt, LONG_PTR, INT)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(LongPtrToUIntPtr, LONG_PTR, UINT_PTR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(LongPtrToSizeT, LONG_PTR, size_t)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(LongPtrToULongPtr, LONG_PTR, ULONG_PTR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(LongPtrToDWordPtr, LONG_PTR, DWORD_PTR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(LongPtrToIntPtr, LONG_PTR, INT_PTR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(LongPtrToULongLong, LONG_PTR, ULONGLONG)
__MINGW_INTSAFE_CHAR_API __MINGW_INTSAFE_CONV_CHAR(LongPtrToChar, LONG_PTR, CHAR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(SSIZETToUInt, SSIZE_T, UINT)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(SSIZETToULong, SSIZE_T, ULONG)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(SSIZETToDWord, SSIZE_T, DWORD)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(SSIZETToLong, SSIZE_T, LONG)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(SSIZETToInt, SSIZE_T, INT)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(SSIZETToUIntPtr, SSIZE_T, UINT_PTR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(SSIZETToSizeT, SSIZE_T, size_t)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(SSIZETToULongPtr, SSIZE_T, ULONG_PTR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(SSIZETToDWordPtr, SSIZE_T, DWORD_PTR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(SSIZETToIntPtr, SSIZE_T, INT_PTR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV_UCHAR(ULongLongToUChar, ULONGLONG, UCHAR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(ULongLongToUInt8, ULONGLONG, UINT8)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(ULongLongToInt8, ULONGLONG, INT8)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(ULongLongToUShort, ULONGLONG, USHORT)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(ULongLongToShort, ULONGLONG, SHORT)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(ULongLongToUInt, ULONGLONG, UINT)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(ULongLongToULong, ULONGLONG, ULONG)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(ULongLongToDWord, ULONGLONG, DWORD)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(ULongLongToLong, ULONGLONG, LONG)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(ULongLongToInt, ULONGLONG, INT)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(ULongLongToUIntPtr, ULONGLONG, UINT_PTR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(ULongLongToSizeT, ULONGLONG, size_t)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(ULongLongToULongPtr, ULONGLONG, ULONG_PTR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(ULongLongToDWordPtr, ULONGLONG, DWORD_PTR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(ULongLongToIntPtr, ULONGLONG, INT_PTR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(ULongLongToPtrdiffT, ULONGLONG, ptrdiff_t)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(ULongLongToLongPtr, ULONGLONG, LONG_PTR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(ULongLongToSSIZET, ULONGLONG, SSIZE_T)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(ULongLongToInt64, ULONGLONG, INT64)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(ULongLongToLongLong, ULONGLONG, LONGLONG)
__MINGW_INTSAFE_CHAR_API __MINGW_INTSAFE_CONV_CHAR(ULongLongToChar, ULONGLONG, CHAR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(Int64ToUInt, INT64, UINT)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(Int64ToULong, INT64, ULONG)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(Int64ToDWord, INT64, DWORD)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(Int64ToLong, INT64, LONG)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(Int64ToInt, INT64, INT)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(Int64ToUIntPtr, INT64, UINT_PTR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(Int64ToSizeT, INT64, size_t)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(Int64ToULongPtr, INT64, ULONG_PTR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(Int64ToDWordPtr, INT64, DWORD_PTR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(Int64ToIntPtr, INT64, INT_PTR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(Int64ToPtrdiffT, INT64, ptrdiff_t)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(Int64ToLongPtr, INT64, LONG_PTR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(Int64ToSSIZET, INT64, SSIZE_T)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(Int64ToULongLong, INT64, ULONGLONG)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV_UCHAR(LongLongToUChar, LONGLONG, UCHAR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(LongLongToUInt8, LONGLONG, UINT8)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(LongLongToInt8, LONGLONG, INT8)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(LongLongToUShort, LONGLONG, USHORT)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(LongLongToShort, LONGLONG, SHORT)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(LongLongToUInt, LONGLONG, UINT)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(LongLongToULong, LONGLONG, ULONG)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(LongLongToLong, LONGLONG, LONG)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(LongLongToInt, LONGLONG, INT)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(LongLongToIntPtr, LONGLONG, INT_PTR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(LongLongToLongPtr, LONGLONG, LONG_PTR)
__MINGW_INTSAFE_API __MINGW_INTSAFE_CONV(LongLongToULongLong, LONGLONG, ULONGLONG)
__MINGW_INTSAFE_CHAR_API __MINGW_INTSAFE_CONV_CHAR(LongLongToChar, LONGLONG, CHAR)

__MINGW_INTSAFE_API __MINGW_INTSAFE_MATH(UInt8Add, UINT8, add)
__MINGW_INTSAFE_API __MINGW_INTSAFE_MATH(Int8Add, INT8, add)
__MINGW_INTSAFE_API __MINGW_INTSAFE_MATH(UShortAdd, USHORT, add)
__MINGW_INTSAFE_API __MINGW_INTSAFE_MATH(WordAdd, WORD, add)
__MINGW_INTSAFE_API __MINGW_INTSAFE_MATH(ShortAdd, SHORT, add)
__MINGW_INTSAFE_API __MINGW_INTSAFE_MATH(UIntAdd, UINT, add)
__MINGW_INTSAFE_API __MINGW_INTSAFE_MATH(ULongAdd, ULONG, add)
__MINGW_INTSAFE_API __MINGW_INTSAFE_MATH(DWordAdd, DWORD, add)
__MINGW_INTSAFE_API __MINGW_INTSAFE_MATH(LongAdd, LONG, add)
__MINGW_INTSAFE_API __MINGW_INTSAFE_MATH(IntAdd, INT, add)
__MINGW_INTSAFE_API __MINGW_INTSAFE_MATH(UIntPtrAdd, UINT_PTR, add)
__MINGW_INTSAFE_API __MINGW_INTSAFE_MATH(SizeTAdd, size_t, add)
__MINGW_INTSAFE_API __MINGW_INTSAFE_MATH(ULongPtrAdd, ULONG_PTR, add)
__MINGW_INTSAFE_API __MINGW_INTSAFE_MATH(DWordPtrAdd, DWORD_PTR, add)
__MINGW_INTSAFE_API __MINGW_INTSAFE_MATH(IntPtrAdd, INT_PTR, add)
__MINGW_INTSAFE_API __MINGW_INTSAFE_MATH(PtrdiffTAdd, ptrdiff_t, add)
__MINGW_INTSAFE_API __MINGW_INTSAFE_MATH(LongPtrAdd, LONG_PTR, add)
__MINGW_INTSAFE_API __MINGW_INTSAFE_MATH(SIZETAdd, SIZE_T, add)
__MINGW_INTSAFE_API __MINGW_INTSAFE_MATH(SSIZETAdd, SSIZE_T, add)
__MINGW_INTSAFE_API __MINGW_INTSAFE_MATH(ULongLongAdd, ULONGLONG, add)
__MINGW_INTSAFE_API __MINGW_INTSAFE_MATH(LongLongAdd, LONGLONG, add)

__MINGW_INTSAFE_API __MINGW_INTSAFE_MATH(UInt8Sub, UINT8, sub)
__MINGW_INTSAFE_API __MINGW_INTSAFE_MATH(Int8Sub, INT8, sub)
__MINGW_INTSAFE_API __MINGW_INTSAFE_MATH(UShortSub, USHORT, sub)
__MINGW_INTSAFE_API __MINGW_INTSAFE_MATH(WordSub, WORD, sub)
__MINGW_INTSAFE_API __MINGW_INTSAFE_MATH(ShortSub, SHORT, sub)
__MINGW_INTSAFE_API __MINGW_INTSAFE_MATH(UIntSub, UINT, sub)
__MINGW_INTSAFE_API __MINGW_INTSAFE_MATH(ULongSub, ULONG, sub)
__MINGW_INTSAFE_API __MINGW_INTSAFE_MATH(DWordSub, DWORD, sub)
__MINGW_INTSAFE_API __MINGW_INTSAFE_MATH(LongSub, LONG, sub)
__MINGW_INTSAFE_API __MINGW_INTSAFE_MATH(IntSub, INT, sub)
__MINGW_INTSAFE_API __MINGW_INTSAFE_MATH(UIntPtrSub, UINT_PTR, sub)
__MINGW_INTSAFE_API __MINGW_INTSAFE_MATH(SizeTSub, size_t, sub)
__MINGW_INTSAFE_API __MINGW_INTSAFE_MATH(ULongPtrSub, ULONG_PTR, sub)
__MINGW_INTSAFE_API __MINGW_INTSAFE_MATH(DWordPtrSub, DWORD_PTR, sub)
__MINGW_INTSAFE_API __MINGW_INTSAFE_MATH(IntPtrSub, INT_PTR, sub)
__MINGW_INTSAFE_API __MINGW_INTSAFE_MATH(PtrdiffTSub, ptrdiff_t, sub)
__MINGW_INTSAFE_API __MINGW_INTSAFE_MATH(LongPtrSub, LONG_PTR, sub)
__MINGW_INTSAFE_API __MINGW_INTSAFE_MATH(SIZETSub, SIZE_T, sub)
__MINGW_INTSAFE_API __MINGW_INTSAFE_MATH(SSIZETSub, SSIZE_T, sub)
__MINGW_INTSAFE_API __MINGW_INTSAFE_MATH(ULongLongSub, ULONGLONG, sub)
__MINGW_INTSAFE_API __MINGW_INTSAFE_MATH(LongLongSub, LONGLONG, sub)

__MINGW_INTSAFE_API __MINGW_INTSAFE_MATH(UInt8Mult, UINT8, mul)
__MINGW_INTSAFE_API __MINGW_INTSAFE_MATH(Int8Mult, INT8, mul)
__MINGW_INTSAFE_API __MINGW_INTSAFE_MATH(UShortMult, USHORT, mul)
__MINGW_INTSAFE_API __MINGW_INTSAFE_MATH(WordMult, WORD, mul)
__MINGW_INTSAFE_API __MINGW_INTSAFE_MATH(ShortMult, SHORT, mul)
__MINGW_INTSAFE_API __MINGW_INTSAFE_MATH(UIntMult, UINT, mul)
__MINGW_INTSAFE_API __MINGW_INTSAFE_MATH(ULongMult, ULONG, mul)
__MINGW_INTSAFE_API __MINGW_INTSAFE_MATH(DWordMult, DWORD, mul)
__MINGW_INTSAFE_API __MINGW_INTSAFE_MATH(LongMult, LONG, mul)
__MINGW_INTSAFE_API __MINGW_INTSAFE_MATH(IntMult, INT, mul)
__MINGW_INTSAFE_API __MINGW_INTSAFE_MATH(UIntPtrMult, UINT_PTR, mul)
__MINGW_INTSAFE_API __MINGW_INTSAFE_MATH(SizeTMult, size_t, mul)
__MINGW_INTSAFE_API __MINGW_INTSAFE_MATH(ULongPtrMult, ULONG_PTR, mul)
__MINGW_INTSAFE_API __MINGW_INTSAFE_MATH(DWordPtrMult, DWORD_PTR, mul)
__MINGW_INTSAFE_API __MINGW_INTSAFE_MATH(IntPtrMult, INT_PTR, mul)
__MINGW_INTSAFE_API __MINGW_INTSAFE_MATH(PtrdiffTMult, ptrdiff_t, mul)
__MINGW_INTSAFE_API __MINGW_INTSAFE_MATH(LongPtrMult, LONG_PTR, mul)
__MINGW_INTSAFE_API __MINGW_INTSAFE_MATH(SIZETMult, SIZE_T, mul)
__MINGW_INTSAFE_API __MINGW_INTSAFE_MATH(SSIZETMult, SSIZE_T, mul)
__MINGW_INTSAFE_API __MINGW_INTSAFE_MATH(ULongLongMult, ULONGLONG, mul)
__MINGW_INTSAFE_API __MINGW_INTSAFE_MATH(LongLongMult, LONGLONG, mul)

#define Int8ToByte Int8ToUInt8
#define Int8ToUInt16 Int8ToUShort
#define Int8ToWord Int8ToUShort
#define Int8ToUInt32 Int8ToUInt
#define Int8ToDWord Int8ToULong
#define Int8ToDWordPtr Int8ToULongPtr
#define Int8ToDWordLong Int8ToULongLong
#define Int8ToULong64 Int8ToULongLong
#define Int8ToDWord64 Int8ToULongLong
#define Int8ToUInt64 Int8ToULongLong
#define Int8ToSizeT Int8ToUIntPtr
#define Int8ToSIZET Int8ToULongPtr
#define ShortToUInt16 ShortToUShort
#define ShortToUInt32 ShortToUInt
#define ShortToDWord ShortToULong
#define ShortToDWordLong ShortToULongLong
#define ShortToULong64 ShortToULongLong
#define ShortToDWord64 ShortToULongLong
#define ShortToUInt64 ShortToULongLong
#define ShortToSizeT ShortToUIntPtr
#define ShortToSIZET ShortToULongPtr
#define Int16ToChar ShortToChar
#define Int16ToInt8 ShortToInt8
#define Int16ToUChar ShortToUChar
#define Int16ToUInt8 ShortToUInt8
#define Int16ToByte ShortToUInt8
#define Int16ToUShort ShortToUShort
#define Int16ToUInt16 ShortToUShort
#define Int16ToWord ShortToUShort
#define Int16ToUInt ShortToUInt
#define Int16ToUInt32 ShortToUInt
#define Int16ToUIntPtr ShortToUIntPtr
#define Int16ToULong ShortToULong
#define Int16ToULongPtr ShortToULongPtr
#define Int16ToDWord ShortToULong
#define Int16ToDWordPtr ShortToULongPtr
#define Int16ToULongLong ShortToULongLong
#define Int16ToDWordLong ShortToULongLong
#define Int16ToULong64 ShortToULongLong
#define Int16ToDWord64 ShortToULongLong
#define Int16ToUInt64 ShortToULongLong
#define Int16ToSizeT ShortToUIntPtr
#define Int16ToSIZET ShortToULongPtr
#define UShortToInt16 UShortToShort
#define UInt16ToChar UShortToChar
#define UInt16ToInt8 UShortToInt8
#define UInt16ToUChar UShortToUChar
#define UInt16ToUInt8 UShortToUInt8
#define UInt16ToByte UShortToUInt8
#define UInt16ToShort UShortToShort
#define UInt16ToInt16 UShortToShort
#define WordToInt8 UShortToInt8
#define WordToUInt8 UShortToUInt8
#define WordToInt16 UShortToShort
#define IntToInt16 IntToShort
#define IntToUInt16 IntToUShort
#define IntToDWordLong IntToULongLong
#define IntToULong64 IntToULongLong
#define IntToDWord64 IntToULongLong
#define IntToUInt64 IntToULongLong
#define IntToSIZET IntToULongPtr
#define Int32ToChar IntToChar
#define Int32ToInt8 IntToInt8
#define Int32ToUChar IntToUChar
#define Int32ToByte IntToUInt8
#define Int32ToUInt8 IntToUInt8
#define Int32ToShort IntToShort
#define Int32ToInt16 IntToShort
#define Int32ToUShort IntToUShort
#define Int32ToUInt16 IntToUShort
#define Int32ToWord IntToUShort
#define Int32ToUInt IntToUInt
#define Int32ToUInt32 IntToUInt
#define Int32ToUIntPtr IntToUIntPtr
#define Int32ToULong IntToULong
#define Int32ToULongPtr IntToULongPtr
#define Int32ToDWord IntToULong
#define Int32ToDWordPtr IntToULongPtr
#define Int32ToULongLong IntToULongLong
#define Int32ToDWordLong IntToULongLong
#define Int32ToULong64 IntToULongLong
#define Int32ToDWord64 IntToULongLong
#define Int32ToUInt64 IntToULongLong
#define Int32ToSizeT IntToUIntPtr
#define Int32ToSIZET IntToULongPtr
#define IntPtrToByte IntPtrToUInt8
#define IntPtrToInt16 IntPtrToShort
#define IntPtrToUInt16 IntPtrToUShort
#define IntPtrToWord IntPtrToUShort
#define IntPtrToInt32 IntPtrToInt
#define IntPtrToUInt32 IntPtrToUInt
#define IntPtrToDWordLong IntPtrToULongLong
#define IntPtrToULong64 IntPtrToULongLong
#define IntPtrToDWord64 IntPtrToULongLong
#define IntPtrToUInt64 IntPtrToULongLong
#define IntPtrToSIZET IntPtrToULongPtr
#define UIntToInt16 UIntToShort
#define UIntToUInt16 UIntToUShort
#define UIntToInt32 UIntToInt
#define UInt32ToChar UIntToChar
#define UInt32ToInt8 UIntToInt8
#define UInt32ToUChar UIntToUChar
#define UInt32ToUInt8 UIntToUInt8
#define UInt32ToByte UInt32ToUInt8
#define UInt32ToShort UIntToShort
#define UInt32ToInt16 UIntToShort
#define UInt32ToUShort UIntToUShort
#define UInt32ToUInt16 UIntToUShort
#define UInt32ToWord UIntToUShort
#define UInt32ToInt UIntToInt
#define UInt32ToIntPtr UIntToIntPtr
#define UInt32ToInt32 UIntToInt
#define UInt32ToLong UIntToLong
#define UInt32ToLongPtr UIntToLongPtr
#define UInt32ToPtrdiffT UIntToPtrdiffT
#define UInt32ToSSIZET UIntToSSIZET
#define UIntPtrToByte UIntPtrToUInt8
#define UIntPtrToWord UIntPtrToUShort
#define UIntPtrToInt32 UIntPtrToInt
#define UIntPtrToUInt32 UIntPtrToUInt
#define UIntPtrToLong64 UIntPtrToLongLong
#define UIntPtrToPtrdiffT UIntPtrToIntPtr
#define LongToInt16 LongToShort
#define LongToUInt16 LongToUShort
#define LongToInt32 LongToInt
#define LongToUInt32 LongToUInt
#define LongToDWordLong LongToULongLong
#define LongToULong64 LongToULongLong
#define LongToDWord64 LongToULongLong
#define LongToUInt64 LongToULongLong
#define LongToSIZET LongToULongPtr
#define LongPtrToByte LongPtrToUInt8
#define LongPtrToInt16 LongPtrToShort
#define LongPtrToUInt16 LongPtrToUShort
#define LongPtrToWord LongPtrToUShort
#define LongPtrToInt32 LongPtrToInt
#define LongPtrToUInt32 LongPtrToUInt
#define LongPtrToDWordLong LongPtrToULongLong
#define LongPtrToULong64 LongPtrToULongLong
#define LongPtrToDWord64 LongPtrToULongLong
#define LongPtrToUInt64 LongPtrToULongLong
#define LongPtrToSIZET LongPtrToULongPtr
#define ULongToInt16 ULongToShort
#define ULongToUInt16 ULongToUShort
#define ULongToInt32 ULongToInt
#define ULongToUInt32 ULongToUInt
#define ULongPtrToByte ULongPtrToUInt8
#define ULongPtrToInt16 ULongPtrToShort
#define ULongPtrToUInt16 ULongPtrToUShort
#define ULongPtrToWord ULongPtrToUShort
#define ULongPtrToInt32 ULongPtrToInt
#define ULongPtrToUInt32 ULongPtrToUInt
#define ULongPtrToLong64 ULongPtrToLongLong
#define DWordToInt8 ULongToInt8
#define DWordToUInt8 ULongToUInt8
#define DWordToInt16 ULongToShort
#define DWordToUInt16 ULongToUShort
#define DWordToInt32 ULongToInt
#define DWordToUInt32 ULongToUInt
#define DWordPtrToInt8 ULongPtrToInt8
#define DWordPtrToUChar ULongPtrToUChar
#define DWordPtrToChar ULongPtrToChar
#define DWordPtrToUInt8 ULongPtrToUInt8
#define DWordPtrToByte ULongPtrToUInt8
#define DWordPtrToShort ULongPtrToShort
#define DWordPtrToInt16 ULongPtrToShort
#define DWordPtrToUShort ULongPtrToUShort
#define DWordPtrToUInt16 ULongPtrToUShort
#define DWordPtrToWord ULongPtrToUShort
#define DWordPtrToInt32 ULongPtrToInt
#define DWordPtrToUInt32 ULongPtrToUInt
#define DWordPtrToLongLong ULongPtrToLongLong
#define DWordPtrToLong64 ULongPtrToLongLong
#define LongLongToByte LongLongToUInt8
#define LongLongToInt16 LongLongToShort
#define LongLongToUInt16 LongLongToUShort
#define LongLongToWord LongLongToUShort
#define LongLongToInt32 LongLongToInt
#define LongLongToUInt32 LongLongToUInt
#define LongLongToUIntPtr Int64ToUIntPtr
#define LongLongToULongPtr Int64ToULongPtr
#define LongLongToDWord LongLongToULong
#define LongLongToDWordPtr LongLongToULongPtr
#define LongLongToDWordLong LongLongToULongLong
#define LongLongToULong64 LongLongToULongLong
#define LongLongToDWord64 LongLongToULongLong
#define LongLongToUInt64 LongLongToULongLong
#define LongLongToPtrdiffT LongLongToIntPtr
#define LongLongToSizeT LongLongToUIntPtr
#define LongLongToSSIZET LongLongToLongPtr
#define LongLongToSIZET LongLongToULongPtr
#define Long64ToChar LongLongToChar
#define Long64ToInt8 LongLongToInt8
#define Long64ToUChar LongLongToUChar
#define Long64ToUInt8 LongLongToUInt8
#define Long64ToByte LongLongToUInt8
#define Long64ToShort LongLongToShort
#define Long64ToInt16 LongLongToShort
#define Long64ToUShort LongLongToUShort
#define Long64ToUInt16 LongLongToUShort
#define Long64ToWord LongLongToUShort
#define Long64ToInt LongLongToInt
#define Long64ToInt32 LongLongToInt
#define Long64ToIntPtr LongLongToIntPtr
#define Long64ToUInt LongLongToUInt
#define Long64ToUInt32 LongLongToUInt
#define Long64ToUIntPtr LongLongToUIntPtr
#define Long64ToLong LongLongToLong
#define Long64ToLongPtr LongLongToLongPtr
#define Long64ToULong LongLongToULong
#define Long64ToULongPtr LongLongToULongPtr
#define Long64ToDWord LongLongToULong
#define Long64ToDWordPtr LongLongToULongPtr
#define Long64ToULongLong LongLongToULongLong
#define Long64ToPtrdiffT LongLongToIntPtr
#define Long64ToSizeT LongLongToUIntPtr
#define Long64ToSSIZET LongLongToLongPtr
#define Long64ToSIZET LongLongToULongPtr
#define Int64ToChar LongLongToChar
#define Int64ToInt8 LongLongToInt8
#define Int64ToUChar LongLongToUChar
#define Int64ToUInt8 LongLongToUInt8
#define Int64ToByte LongLongToUInt8
#define Int64ToShort LongLongToShort
#define Int64ToInt16 LongLongToShort
#define Int64ToUShort LongLongToUShort
#define Int64ToUInt16 LongLongToUShort
#define Int64ToWord LongLongToUShort
#define Int64ToInt32 LongLongToInt
#define Int64ToUInt32 LongLongToUInt
#define Int64ToDWordLong LongLongToULongLong
#define Int64ToULong64 LongLongToULongLong
#define Int64ToDWord64 LongLongToULongLong
#define Int64ToUInt64 LongLongToULongLong
#define Int64ToSIZET LongLongToULongPtr
#define ULongLongToByte ULongLongToUInt8
#define ULongLongToInt16 ULongLongToShort
#define ULongLongToUInt16 ULongLongToUShort
#define ULongLongToWord ULongLongToUShort
#define ULongLongToInt32 ULongLongToInt
#define ULongLongToUInt32 ULongLongToUInt
#define ULongLongToLong64 ULongLongToLongLong
#define ULongLongToSIZET ULongLongToULongPtr
#define DWordLongToChar ULongLongToChar
#define DWordLongToInt8 ULongLongToInt8
#define DWordLongToUChar ULongLongToUChar
#define DWordLongToUInt8 ULongLongToUInt8
#define DWordLongToByte ULongLongToUInt8
#define DWordLongToShort ULongLongToShort
#define DWordLongToInt16 ULongLongToShort
#define DWordLongToUShort ULongLongToUShort
#define DWordLongToUInt16 ULongLongToUShort
#define DWordLongToWord ULongLongToUShort
#define DWordLongToInt ULongLongToInt
#define DWordLongToInt32 ULongLongToInt
#define DWordLongToIntPtr ULongLongToIntPtr
#define DWordLongToUInt ULongLongToUInt
#define DWordLongToUInt32 ULongLongToUInt
#define DWordLongToUIntPtr ULongLongToUIntPtr
#define DWordLongToLong ULongLongToLong
#define DWordLongToLongPtr ULongLongToLongPtr
#define DWordLongToULong ULongLongToULong
#define DWordLongToULongPtr ULongLongToULongPtr
#define DWordLongToDWord ULongLongToULong
#define DWordLongToDWordPtr ULongLongToULongPtr
#define DWordLongToLongLong ULongLongToLongLong
#define DWordLongToLong64 ULongLongToLongLong
#define DWordLongToInt64 ULongLongToLongLong
#define DWordLongToPtrdiffT ULongLongToIntPtr
#define DWordLongToSizeT ULongLongToUIntPtr
#define DWordLongToSSIZET ULongLongToLongPtr
#define DWordLongToSIZET ULongLongToULongPtr
#define ULong64ToChar ULongLongToChar
#define ULong64ToInt8 ULongLongToInt8
#define ULong64ToUChar ULongLongToUChar
#define ULong64ToUInt8 ULongLongToUInt8
#define ULong64ToByte ULongLongToUInt8
#define ULong64ToShort ULongLongToShort
#define ULong64ToInt16 ULongLongToShort
#define ULong64ToUShort ULongLongToUShort
#define ULong64ToUInt16 ULongLongToUShort
#define ULong64ToWord ULongLongToUShort
#define ULong64ToInt ULongLongToInt
#define ULong64ToInt32 ULongLongToInt
#define ULong64ToIntPtr ULongLongToIntPtr
#define ULong64ToUInt ULongLongToUInt
#define ULong64ToUInt32 ULongLongToUInt
#define ULong64ToUIntPtr ULongLongToUIntPtr
#define ULong64ToLong ULongLongToLong
#define ULong64ToLongPtr ULongLongToLongPtr
#define ULong64ToULong ULongLongToULong
#define ULong64ToULongPtr ULongLongToULongPtr
#define ULong64ToDWord ULongLongToULong
#define ULong64ToDWordPtr ULongLongToULongPtr
#define ULong64ToLongLong ULongLongToLongLong
#define ULong64ToLong64 ULongLongToLongLong
#define ULong64ToInt64 ULongLongToLongLong
#define ULong64ToPtrdiffT ULongLongToIntPtr
#define ULong64ToSizeT ULongLongToUIntPtr
#define ULong64ToSSIZET ULongLongToLongPtr
#define ULong64ToSIZET ULongLongToULongPtr
#define DWord64ToChar ULongLongToChar
#define DWord64ToInt8 ULongLongToInt8
#define DWord64ToUChar ULongLongToUChar
#define DWord64ToUInt8 ULongLongToUInt8
#define DWord64ToByte ULongLongToUInt8
#define DWord64ToShort ULongLongToShort
#define DWord64ToInt16 ULongLongToShort
#define DWord64ToUShort ULongLongToUShort
#define DWord64ToUInt16 ULongLongToUShort
#define DWord64ToWord ULongLongToUShort
#define DWord64ToInt ULongLongToInt
#define DWord64ToInt32 ULongLongToInt
#define DWord64ToIntPtr ULongLongToIntPtr
#define DWord64ToUInt ULongLongToUInt
#define DWord64ToUInt32 ULongLongToUInt
#define DWord64ToUIntPtr ULongLongToUIntPtr
#define DWord64ToLong ULongLongToLong
#define DWord64ToLongPtr ULongLongToLongPtr
#define DWord64ToULong ULongLongToULong
#define DWord64ToULongPtr ULongLongToULongPtr
#define DWord64ToDWord ULongLongToULong
#define DWord64ToDWordPtr ULongLongToULongPtr
#define DWord64ToLongLong ULongLongToLongLong
#define DWord64ToLong64 ULongLongToLongLong
#define DWord64ToInt64 ULongLongToLongLong
#define DWord64ToPtrdiffT ULongLongToIntPtr
#define DWord64ToSizeT ULongLongToUIntPtr
#define DWord64ToSSIZET ULongLongToLongPtr
#define DWord64ToSIZET ULongLongToULongPtr
#define UInt64ToChar ULongLongToChar
#define UInt64ToInt8 ULongLongToInt8
#define UInt64ToUChar ULongLongToUChar
#define UInt64ToUInt8 ULongLongToUInt8
#define UInt64ToByte ULongLongToUInt8
#define UInt64ToShort ULongLongToShort
#define UInt64ToInt16 ULongLongToShort
#define UInt64ToUShort ULongLongToUShort
#define UInt64ToUInt16 ULongLongToUShort
#define UInt64ToWord ULongLongToUShort
#define UInt64ToInt ULongLongToInt
#define UInt64ToInt32 ULongLongToInt
#define UInt64ToIntPtr ULongLongToIntPtr
#define UInt64ToUInt ULongLongToUInt
#define UInt64ToUInt32 ULongLongToUInt
#define UInt64ToUIntPtr ULongLongToUIntPtr
#define UInt64ToLong ULongLongToLong
#define UInt64ToLongPtr ULongLongToLongPtr
#define UInt64ToULong ULongLongToULong
#define UInt64ToULongPtr ULongLongToULongPtr
#define UInt64ToDWord ULongLongToULong
#define UInt64ToDWordPtr ULongLongToULongPtr
#define UInt64ToLongLong ULongLongToLongLong
#define UInt64ToLong64 ULongLongToLongLong
#define UInt64ToInt64 ULongLongToLongLong
#define UInt64ToPtrdiffT ULongLongToIntPtr
#define UInt64ToSizeT ULongLongToUIntPtr
#define UInt64ToSSIZET ULongLongToLongPtr
#define UInt64ToSIZET ULongLongToULongPtr
#define PtrdiffTToChar IntPtrToChar
#define PtrdiffTToInt8 IntPtrToInt8
#define PtrdiffTToUChar IntPtrToUChar
#define PtrdiffTToUInt8 IntPtrToUInt8
#define PtrdiffTToByte IntPtrToUInt8
#define PtrdiffTToShort IntPtrToShort
#define PtrdiffTToInt16 IntPtrToShort
#define PtrdiffTToUShort IntPtrToUShort
#define PtrdiffTToUInt16 IntPtrToUShort
#define PtrdiffTToWord IntPtrToUShort
#define PtrdiffTToInt32 IntPtrToInt
#define PtrdiffTToUInt32 IntPtrToUInt
#define PtrdiffTToLongPtr IntPtrToLongPtr
#define PtrdiffTToULongLong IntPtrToULongLong
#define PtrdiffTToDWordLong IntPtrToULongLong
#define PtrdiffTToULong64 IntPtrToULongLong
#define PtrdiffTToDWord64 IntPtrToULongLong
#define PtrdiffTToUInt64 IntPtrToULongLong
#define PtrdiffTToSIZET IntPtrToULongPtr
#define SizeTToInt8 UIntPtrToInt8
#define SizeTToUChar UIntPtrToUChar
#define SizeTToChar UIntPtrToChar
#define SizeTToUInt8 UIntPtrToUInt8
#define SizeTToByte UIntPtrToUInt8
#define SizeTToShort UIntPtrToShort
#define SizeTToInt16 UIntPtrToShort
#define SizeTToUShort UIntPtrToUShort
#define SizeTToUInt16 UIntPtrToUShort
#define SizeTToWord UIntPtrToUShort
#define SizeTToInt32 UIntPtrToInt
#define SizeTToLongLong UIntPtrToLongLong
#define SizeTToLong64 UIntPtrToLongLong
#define SSIZETToInt8 LongPtrToInt8
#define SSIZETToUChar LongPtrToUChar
#define SSIZETToChar LongPtrToChar
#define SSIZETToUInt8 LongPtrToUInt8
#define SSIZETToByte LongPtrToUInt8
#define SSIZETToShort LongPtrToShort
#define SSIZETToInt16 LongPtrToShort
#define SSIZETToUShort LongPtrToUShort
#define SSIZETToUInt16 LongPtrToUShort
#define SSIZETToWord LongPtrToUShort
#define SSIZETToInt32 LongPtrToInt
#define SSIZETToUInt32 LongPtrToUInt
#define SSIZETToULongLong LongPtrToULongLong
#define SSIZETToDWordLong LongPtrToULongLong
#define SSIZETToULong64 LongPtrToULongLong
#define SSIZETToDWord64 LongPtrToULongLong
#define SSIZETToUInt64 LongPtrToULongLong
#define SSIZETToSIZET LongPtrToULongPtr
#define SIZETToInt8 ULongPtrToInt8
#define SIZETToUChar ULongPtrToUChar
#define SIZETToChar ULongPtrToChar
#define SIZETToUInt8 ULongPtrToUInt8
#define SIZETToByte ULongPtrToUInt8
#define SIZETToShort ULongPtrToShort
#define SIZETToInt16 ULongPtrToShort
#define SIZETToUShort ULongPtrToUShort
#define SIZETToUInt16 ULongPtrToUShort
#define SIZETToWord ULongPtrToUShort
#define SIZETToInt ULongPtrToInt
#define SIZETToInt32 ULongPtrToInt
#define SIZETToIntPtr ULongPtrToIntPtr
#define SIZETToUInt ULongPtrToUInt
#define SIZETToUInt32 ULongPtrToUInt
#define SIZETToUIntPtr ULongPtrToUIntPtr
#define SIZETToLong ULongPtrToLong
#define SIZETToLongPtr ULongPtrToLongPtr
#define SIZETToULong ULongPtrToULong
#define SIZETToDWord ULongPtrToULong
#define SIZETToLongLong ULongPtrToLongLong
#define SIZETToLong64 ULongPtrToLongLong
#define SIZETToInt64 ULongPtrToLongLong
#define SIZETToPtrdiffT ULongPtrToIntPtr
#define SIZETToSSIZET ULongPtrToLongPtr

#define UInt16Add UShortAdd
#define UInt32Add UIntAdd
#define DWordLongAdd ULongLongAdd
#define ULong64Add ULongLongAdd
#define DWord64Add ULongLongAdd
#define UInt64Add ULongLongAdd
#define UInt16Sub UShortSub
#define UInt32Sub UIntSub
#define DWordLongSub ULongLongSub
#define ULong64Sub ULongLongSub
#define DWord64Sub ULongLongSub
#define UInt64Sub ULongLongSub
#define UInt16Mult UShortMult
#define UInt32Mult UIntMult
#define DWordLongMult ULongLongMult
#define ULong64Mult ULongLongMult
#define DWord64Mult ULongLongMult
#define UInt64Mult ULongLongMult
#define Int16Add ShortAdd
#define Int32Add IntAdd
#define Long32Add IntAdd
#define Long64Add LongLongAdd
#define Int64Add LongLongAdd
#define Int16Sub ShortSub
#define Int32Sub IntSub
#define Long32Sub IntSub
#define Long64Sub LongLongSub
#define Int64Sub LongLongSub
#define Int16Mult ShortMult
#define Int32Mult IntMult
#define Long32Mult IntMult
#define Long64Mult LongLongMult
#define Int64Mult LongLongMult

#endif /* __MINGW_INTSAFE_WORKS */
#endif /* WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP) */
#endif /* _INTSAFE_H_INCLUDED_ */
