/*** Autogenerated by WIDL 10.8 from include/bits.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __bits_h__
#define __bits_h__

/* Forward declarations */

#ifndef __IBackgroundCopyFile_FWD_DEFINED__
#define __IBackgroundCopyFile_FWD_DEFINED__
typedef interface IBackgroundCopyFile IBackgroundCopyFile;
#ifdef __cplusplus
interface IBackgroundCopyFile;
#endif /* __cplusplus */
#endif

#ifndef __IEnumBackgroundCopyFiles_FWD_DEFINED__
#define __IEnumBackgroundCopyFiles_FWD_DEFINED__
typedef interface IEnumBackgroundCopyFiles IEnumBackgroundCopyFiles;
#ifdef __cplusplus
interface IEnumBackgroundCopyFiles;
#endif /* __cplusplus */
#endif

#ifndef __IBackgroundCopyError_FWD_DEFINED__
#define __IBackgroundCopyError_FWD_DEFINED__
typedef interface IBackgroundCopyError IBackgroundCopyError;
#ifdef __cplusplus
interface IBackgroundCopyError;
#endif /* __cplusplus */
#endif

#ifndef __IBackgroundCopyJob_FWD_DEFINED__
#define __IBackgroundCopyJob_FWD_DEFINED__
typedef interface IBackgroundCopyJob IBackgroundCopyJob;
#ifdef __cplusplus
interface IBackgroundCopyJob;
#endif /* __cplusplus */
#endif

#ifndef __IEnumBackgroundCopyJobs_FWD_DEFINED__
#define __IEnumBackgroundCopyJobs_FWD_DEFINED__
typedef interface IEnumBackgroundCopyJobs IEnumBackgroundCopyJobs;
#ifdef __cplusplus
interface IEnumBackgroundCopyJobs;
#endif /* __cplusplus */
#endif

#ifndef __IBackgroundCopyCallback_FWD_DEFINED__
#define __IBackgroundCopyCallback_FWD_DEFINED__
typedef interface IBackgroundCopyCallback IBackgroundCopyCallback;
#ifdef __cplusplus
interface IBackgroundCopyCallback;
#endif /* __cplusplus */
#endif

#ifndef __IBackgroundCopyManager_FWD_DEFINED__
#define __IBackgroundCopyManager_FWD_DEFINED__
typedef interface IBackgroundCopyManager IBackgroundCopyManager;
#ifdef __cplusplus
interface IBackgroundCopyManager;
#endif /* __cplusplus */
#endif

#ifndef __BackgroundCopyManager_FWD_DEFINED__
#define __BackgroundCopyManager_FWD_DEFINED__
#ifdef __cplusplus
typedef class BackgroundCopyManager BackgroundCopyManager;
#else
typedef struct BackgroundCopyManager BackgroundCopyManager;
#endif /* defined __cplusplus */
#endif /* defined __BackgroundCopyManager_FWD_DEFINED__ */

/* Headers for imported files */

#include <unknwn.h>

#ifdef __cplusplus
extern "C" {
#endif

#include "bitsmsg.h"
#define BG_SIZE_UNKNOWN (UINT64)(-1)
#define BG_NOTIFY_JOB_TRANSFERRED 0x0001
#define BG_NOTIFY_JOB_ERROR 0x0002
#define BG_NOTIFY_DISABLE 0x0004
#define BG_NOTIFY_JOB_MODIFICATION 0x0008
#define BG_NOTIFY_FILE_TRANSFERRED 0x0010
#ifdef WINE_NO_UNICODE_MACROS
#undef EnumJobs
#undef GetJob
#endif
/*****************************************************************************
 * IBackgroundCopyFile interface
 */
#ifndef __IBackgroundCopyFile_INTERFACE_DEFINED__
#define __IBackgroundCopyFile_INTERFACE_DEFINED__

typedef struct _BG_FILE_PROGRESS {
    UINT64 BytesTotal;
    UINT64 BytesTransferred;
    WINBOOL Completed;
} BG_FILE_PROGRESS;
DEFINE_GUID(IID_IBackgroundCopyFile, 0x01b7bd23, 0xfb88, 0x4a77, 0x84,0x90, 0x58,0x91,0xd3,0xe4,0x65,0x3a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("01b7bd23-fb88-4a77-8490-5891d3e4653a")
IBackgroundCopyFile : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetRemoteName(
        LPWSTR *pVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetLocalName(
        LPWSTR *pVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetProgress(
        BG_FILE_PROGRESS *pVal) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IBackgroundCopyFile, 0x01b7bd23, 0xfb88, 0x4a77, 0x84,0x90, 0x58,0x91,0xd3,0xe4,0x65,0x3a)
#endif
#else
typedef struct IBackgroundCopyFileVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IBackgroundCopyFile *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IBackgroundCopyFile *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IBackgroundCopyFile *This);

    /*** IBackgroundCopyFile methods ***/
    HRESULT (STDMETHODCALLTYPE *GetRemoteName)(
        IBackgroundCopyFile *This,
        LPWSTR *pVal);

    HRESULT (STDMETHODCALLTYPE *GetLocalName)(
        IBackgroundCopyFile *This,
        LPWSTR *pVal);

    HRESULT (STDMETHODCALLTYPE *GetProgress)(
        IBackgroundCopyFile *This,
        BG_FILE_PROGRESS *pVal);

    END_INTERFACE
} IBackgroundCopyFileVtbl;

interface IBackgroundCopyFile {
    CONST_VTBL IBackgroundCopyFileVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IBackgroundCopyFile_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IBackgroundCopyFile_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IBackgroundCopyFile_Release(This) (This)->lpVtbl->Release(This)
/*** IBackgroundCopyFile methods ***/
#define IBackgroundCopyFile_GetRemoteName(This,pVal) (This)->lpVtbl->GetRemoteName(This,pVal)
#define IBackgroundCopyFile_GetLocalName(This,pVal) (This)->lpVtbl->GetLocalName(This,pVal)
#define IBackgroundCopyFile_GetProgress(This,pVal) (This)->lpVtbl->GetProgress(This,pVal)
#else
/*** IUnknown methods ***/
static inline HRESULT IBackgroundCopyFile_QueryInterface(IBackgroundCopyFile* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IBackgroundCopyFile_AddRef(IBackgroundCopyFile* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IBackgroundCopyFile_Release(IBackgroundCopyFile* This) {
    return This->lpVtbl->Release(This);
}
/*** IBackgroundCopyFile methods ***/
static inline HRESULT IBackgroundCopyFile_GetRemoteName(IBackgroundCopyFile* This,LPWSTR *pVal) {
    return This->lpVtbl->GetRemoteName(This,pVal);
}
static inline HRESULT IBackgroundCopyFile_GetLocalName(IBackgroundCopyFile* This,LPWSTR *pVal) {
    return This->lpVtbl->GetLocalName(This,pVal);
}
static inline HRESULT IBackgroundCopyFile_GetProgress(IBackgroundCopyFile* This,BG_FILE_PROGRESS *pVal) {
    return This->lpVtbl->GetProgress(This,pVal);
}
#endif
#endif

#endif


#endif  /* __IBackgroundCopyFile_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IEnumBackgroundCopyFiles interface
 */
#ifndef __IEnumBackgroundCopyFiles_INTERFACE_DEFINED__
#define __IEnumBackgroundCopyFiles_INTERFACE_DEFINED__

DEFINE_GUID(IID_IEnumBackgroundCopyFiles, 0xca51e165, 0xc365, 0x424c, 0x8d,0x41, 0x24,0xaa,0xa4,0xff,0x3c,0x40);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("ca51e165-c365-424c-8d41-24aaa4ff3c40")
IEnumBackgroundCopyFiles : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Next(
        ULONG celt,
        IBackgroundCopyFile **rgelt,
        ULONG *pceltFetched) = 0;

    virtual HRESULT STDMETHODCALLTYPE Skip(
        ULONG celt) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reset(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IEnumBackgroundCopyFiles **ppenum) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCount(
        ULONG *puCount) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IEnumBackgroundCopyFiles, 0xca51e165, 0xc365, 0x424c, 0x8d,0x41, 0x24,0xaa,0xa4,0xff,0x3c,0x40)
#endif
#else
typedef struct IEnumBackgroundCopyFilesVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IEnumBackgroundCopyFiles *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IEnumBackgroundCopyFiles *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IEnumBackgroundCopyFiles *This);

    /*** IEnumBackgroundCopyFiles methods ***/
    HRESULT (STDMETHODCALLTYPE *Next)(
        IEnumBackgroundCopyFiles *This,
        ULONG celt,
        IBackgroundCopyFile **rgelt,
        ULONG *pceltFetched);

    HRESULT (STDMETHODCALLTYPE *Skip)(
        IEnumBackgroundCopyFiles *This,
        ULONG celt);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        IEnumBackgroundCopyFiles *This);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IEnumBackgroundCopyFiles *This,
        IEnumBackgroundCopyFiles **ppenum);

    HRESULT (STDMETHODCALLTYPE *GetCount)(
        IEnumBackgroundCopyFiles *This,
        ULONG *puCount);

    END_INTERFACE
} IEnumBackgroundCopyFilesVtbl;

interface IEnumBackgroundCopyFiles {
    CONST_VTBL IEnumBackgroundCopyFilesVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IEnumBackgroundCopyFiles_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IEnumBackgroundCopyFiles_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IEnumBackgroundCopyFiles_Release(This) (This)->lpVtbl->Release(This)
/*** IEnumBackgroundCopyFiles methods ***/
#define IEnumBackgroundCopyFiles_Next(This,celt,rgelt,pceltFetched) (This)->lpVtbl->Next(This,celt,rgelt,pceltFetched)
#define IEnumBackgroundCopyFiles_Skip(This,celt) (This)->lpVtbl->Skip(This,celt)
#define IEnumBackgroundCopyFiles_Reset(This) (This)->lpVtbl->Reset(This)
#define IEnumBackgroundCopyFiles_Clone(This,ppenum) (This)->lpVtbl->Clone(This,ppenum)
#define IEnumBackgroundCopyFiles_GetCount(This,puCount) (This)->lpVtbl->GetCount(This,puCount)
#else
/*** IUnknown methods ***/
static inline HRESULT IEnumBackgroundCopyFiles_QueryInterface(IEnumBackgroundCopyFiles* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IEnumBackgroundCopyFiles_AddRef(IEnumBackgroundCopyFiles* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IEnumBackgroundCopyFiles_Release(IEnumBackgroundCopyFiles* This) {
    return This->lpVtbl->Release(This);
}
/*** IEnumBackgroundCopyFiles methods ***/
static inline HRESULT IEnumBackgroundCopyFiles_Next(IEnumBackgroundCopyFiles* This,ULONG celt,IBackgroundCopyFile **rgelt,ULONG *pceltFetched) {
    return This->lpVtbl->Next(This,celt,rgelt,pceltFetched);
}
static inline HRESULT IEnumBackgroundCopyFiles_Skip(IEnumBackgroundCopyFiles* This,ULONG celt) {
    return This->lpVtbl->Skip(This,celt);
}
static inline HRESULT IEnumBackgroundCopyFiles_Reset(IEnumBackgroundCopyFiles* This) {
    return This->lpVtbl->Reset(This);
}
static inline HRESULT IEnumBackgroundCopyFiles_Clone(IEnumBackgroundCopyFiles* This,IEnumBackgroundCopyFiles **ppenum) {
    return This->lpVtbl->Clone(This,ppenum);
}
static inline HRESULT IEnumBackgroundCopyFiles_GetCount(IEnumBackgroundCopyFiles* This,ULONG *puCount) {
    return This->lpVtbl->GetCount(This,puCount);
}
#endif
#endif

#endif


#endif  /* __IEnumBackgroundCopyFiles_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IBackgroundCopyError interface
 */
#ifndef __IBackgroundCopyError_INTERFACE_DEFINED__
#define __IBackgroundCopyError_INTERFACE_DEFINED__

typedef enum __WIDL_bits_generated_name_0000000C {
    BG_ERROR_CONTEXT_NONE = 0,
    BG_ERROR_CONTEXT_UNKNOWN = 1,
    BG_ERROR_CONTEXT_GENERAL_QUEUE_MANAGER = 2,
    BG_ERROR_CONTEXT_QUEUE_MANAGER_NOTIFICATION = 3,
    BG_ERROR_CONTEXT_LOCAL_FILE = 4,
    BG_ERROR_CONTEXT_REMOTE_FILE = 5,
    BG_ERROR_CONTEXT_GENERAL_TRANSPORT = 6,
    BG_ERROR_CONTEXT_REMOTE_APPLICATION = 7
} BG_ERROR_CONTEXT;
DEFINE_GUID(IID_IBackgroundCopyError, 0x19c613a0, 0xfcb8, 0x4f28, 0x81,0xae, 0x89,0x7c,0x3d,0x07,0x8f,0x81);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("19c613a0-fcb8-4f28-81ae-897c3d078f81")
IBackgroundCopyError : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetError(
        BG_ERROR_CONTEXT *pContext,
        HRESULT *pCode) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFile(
        IBackgroundCopyFile **pVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetErrorDescription(
        DWORD LanguageId,
        LPWSTR *pErrorDescription) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetErrorContextDescription(
        DWORD LanguageId,
        LPWSTR *pContextDescription) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetProtocol(
        LPWSTR *pProtocol) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IBackgroundCopyError, 0x19c613a0, 0xfcb8, 0x4f28, 0x81,0xae, 0x89,0x7c,0x3d,0x07,0x8f,0x81)
#endif
#else
typedef struct IBackgroundCopyErrorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IBackgroundCopyError *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IBackgroundCopyError *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IBackgroundCopyError *This);

    /*** IBackgroundCopyError methods ***/
    HRESULT (STDMETHODCALLTYPE *GetError)(
        IBackgroundCopyError *This,
        BG_ERROR_CONTEXT *pContext,
        HRESULT *pCode);

    HRESULT (STDMETHODCALLTYPE *GetFile)(
        IBackgroundCopyError *This,
        IBackgroundCopyFile **pVal);

    HRESULT (STDMETHODCALLTYPE *GetErrorDescription)(
        IBackgroundCopyError *This,
        DWORD LanguageId,
        LPWSTR *pErrorDescription);

    HRESULT (STDMETHODCALLTYPE *GetErrorContextDescription)(
        IBackgroundCopyError *This,
        DWORD LanguageId,
        LPWSTR *pContextDescription);

    HRESULT (STDMETHODCALLTYPE *GetProtocol)(
        IBackgroundCopyError *This,
        LPWSTR *pProtocol);

    END_INTERFACE
} IBackgroundCopyErrorVtbl;

interface IBackgroundCopyError {
    CONST_VTBL IBackgroundCopyErrorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IBackgroundCopyError_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IBackgroundCopyError_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IBackgroundCopyError_Release(This) (This)->lpVtbl->Release(This)
/*** IBackgroundCopyError methods ***/
#define IBackgroundCopyError_GetError(This,pContext,pCode) (This)->lpVtbl->GetError(This,pContext,pCode)
#define IBackgroundCopyError_GetFile(This,pVal) (This)->lpVtbl->GetFile(This,pVal)
#define IBackgroundCopyError_GetErrorDescription(This,LanguageId,pErrorDescription) (This)->lpVtbl->GetErrorDescription(This,LanguageId,pErrorDescription)
#define IBackgroundCopyError_GetErrorContextDescription(This,LanguageId,pContextDescription) (This)->lpVtbl->GetErrorContextDescription(This,LanguageId,pContextDescription)
#define IBackgroundCopyError_GetProtocol(This,pProtocol) (This)->lpVtbl->GetProtocol(This,pProtocol)
#else
/*** IUnknown methods ***/
static inline HRESULT IBackgroundCopyError_QueryInterface(IBackgroundCopyError* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IBackgroundCopyError_AddRef(IBackgroundCopyError* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IBackgroundCopyError_Release(IBackgroundCopyError* This) {
    return This->lpVtbl->Release(This);
}
/*** IBackgroundCopyError methods ***/
static inline HRESULT IBackgroundCopyError_GetError(IBackgroundCopyError* This,BG_ERROR_CONTEXT *pContext,HRESULT *pCode) {
    return This->lpVtbl->GetError(This,pContext,pCode);
}
static inline HRESULT IBackgroundCopyError_GetFile(IBackgroundCopyError* This,IBackgroundCopyFile **pVal) {
    return This->lpVtbl->GetFile(This,pVal);
}
static inline HRESULT IBackgroundCopyError_GetErrorDescription(IBackgroundCopyError* This,DWORD LanguageId,LPWSTR *pErrorDescription) {
    return This->lpVtbl->GetErrorDescription(This,LanguageId,pErrorDescription);
}
static inline HRESULT IBackgroundCopyError_GetErrorContextDescription(IBackgroundCopyError* This,DWORD LanguageId,LPWSTR *pContextDescription) {
    return This->lpVtbl->GetErrorContextDescription(This,LanguageId,pContextDescription);
}
static inline HRESULT IBackgroundCopyError_GetProtocol(IBackgroundCopyError* This,LPWSTR *pProtocol) {
    return This->lpVtbl->GetProtocol(This,pProtocol);
}
#endif
#endif

#endif


#endif  /* __IBackgroundCopyError_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IBackgroundCopyJob interface
 */
#ifndef __IBackgroundCopyJob_INTERFACE_DEFINED__
#define __IBackgroundCopyJob_INTERFACE_DEFINED__

typedef struct _BG_FILE_INFO {
    LPWSTR RemoteName;
    LPWSTR LocalName;
} BG_FILE_INFO;
typedef struct _BG_JOB_PROGRESS {
    UINT64 BytesTotal;
    UINT64 BytesTransferred;
    ULONG FilesTotal;
    ULONG FilesTransferred;
} BG_JOB_PROGRESS;
typedef struct _BG_JOB_TIMES {
    FILETIME CreationTime;
    FILETIME ModificationTime;
    FILETIME TransferCompletionTime;
} BG_JOB_TIMES;
typedef enum __WIDL_bits_generated_name_0000000D {
    BG_JOB_PRIORITY_FOREGROUND = 0,
    BG_JOB_PRIORITY_HIGH = 1,
    BG_JOB_PRIORITY_NORMAL = 2,
    BG_JOB_PRIORITY_LOW = 3
} BG_JOB_PRIORITY;
typedef enum __WIDL_bits_generated_name_0000000E {
    BG_JOB_STATE_QUEUED = 0,
    BG_JOB_STATE_CONNECTING = 1,
    BG_JOB_STATE_TRANSFERRING = 2,
    BG_JOB_STATE_SUSPENDED = 3,
    BG_JOB_STATE_ERROR = 4,
    BG_JOB_STATE_TRANSIENT_ERROR = 5,
    BG_JOB_STATE_TRANSFERRED = 6,
    BG_JOB_STATE_ACKNOWLEDGED = 7,
    BG_JOB_STATE_CANCELLED = 8
} BG_JOB_STATE;
typedef enum __WIDL_bits_generated_name_0000000F {
    BG_JOB_TYPE_DOWNLOAD = 0,
    BG_JOB_TYPE_UPLOAD = 1,
    BG_JOB_TYPE_UPLOAD_REPLY = 2
} BG_JOB_TYPE;
typedef enum __WIDL_bits_generated_name_00000010 {
    BG_JOB_PROXY_USAGE_PRECONFIG = 0,
    BG_JOB_PROXY_USAGE_NO_PROXY = 1,
    BG_JOB_PROXY_USAGE_OVERRIDE = 2,
    BG_JOB_PROXY_USAGE_AUTODETECT = 3
} BG_JOB_PROXY_USAGE;
DEFINE_GUID(IID_IBackgroundCopyJob, 0x37668d37, 0x507e, 0x4160, 0x93,0x16, 0x26,0x30,0x6d,0x15,0x0b,0x12);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("37668d37-507e-4160-9316-26306d150b12")
IBackgroundCopyJob : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE AddFileSet(
        ULONG cFileCount,
        BG_FILE_INFO *pFileSet) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddFile(
        LPCWSTR RemoteUrl,
        LPCWSTR LocalName) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumFiles(
        IEnumBackgroundCopyFiles **pEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE Suspend(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Resume(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Cancel(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Complete(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetId(
        GUID *pVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetType(
        BG_JOB_TYPE *pVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetProgress(
        BG_JOB_PROGRESS *pVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTimes(
        BG_JOB_TIMES *pVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetState(
        BG_JOB_STATE *pVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetError(
        IBackgroundCopyError **ppError) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetOwner(
        LPWSTR *pVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDisplayName(
        LPCWSTR Val) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDisplayName(
        LPWSTR *pVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDescription(
        LPCWSTR Val) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDescription(
        LPWSTR *pVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetPriority(
        BG_JOB_PRIORITY Val) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPriority(
        BG_JOB_PRIORITY *pVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetNotifyFlags(
        ULONG Val) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetNotifyFlags(
        ULONG *pVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetNotifyInterface(
        IUnknown *Val) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetNotifyInterface(
        IUnknown **pVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetMinimumRetryDelay(
        ULONG Seconds) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMinimumRetryDelay(
        ULONG *Seconds) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetNoProgressTimeout(
        ULONG Seconds) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetNoProgressTimeout(
        ULONG *Seconds) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetErrorCount(
        ULONG *Errors) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetProxySettings(
        BG_JOB_PROXY_USAGE ProxyUsage,
        const WCHAR *ProxyList,
        const WCHAR *ProxyBypassList) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetProxySettings(
        BG_JOB_PROXY_USAGE *pProxyUsage,
        LPWSTR *pProxyList,
        LPWSTR *pProxyBypassList) = 0;

    virtual HRESULT STDMETHODCALLTYPE TakeOwnership(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IBackgroundCopyJob, 0x37668d37, 0x507e, 0x4160, 0x93,0x16, 0x26,0x30,0x6d,0x15,0x0b,0x12)
#endif
#else
typedef struct IBackgroundCopyJobVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IBackgroundCopyJob *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IBackgroundCopyJob *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IBackgroundCopyJob *This);

    /*** IBackgroundCopyJob methods ***/
    HRESULT (STDMETHODCALLTYPE *AddFileSet)(
        IBackgroundCopyJob *This,
        ULONG cFileCount,
        BG_FILE_INFO *pFileSet);

    HRESULT (STDMETHODCALLTYPE *AddFile)(
        IBackgroundCopyJob *This,
        LPCWSTR RemoteUrl,
        LPCWSTR LocalName);

    HRESULT (STDMETHODCALLTYPE *EnumFiles)(
        IBackgroundCopyJob *This,
        IEnumBackgroundCopyFiles **pEnum);

    HRESULT (STDMETHODCALLTYPE *Suspend)(
        IBackgroundCopyJob *This);

    HRESULT (STDMETHODCALLTYPE *Resume)(
        IBackgroundCopyJob *This);

    HRESULT (STDMETHODCALLTYPE *Cancel)(
        IBackgroundCopyJob *This);

    HRESULT (STDMETHODCALLTYPE *Complete)(
        IBackgroundCopyJob *This);

    HRESULT (STDMETHODCALLTYPE *GetId)(
        IBackgroundCopyJob *This,
        GUID *pVal);

    HRESULT (STDMETHODCALLTYPE *GetType)(
        IBackgroundCopyJob *This,
        BG_JOB_TYPE *pVal);

    HRESULT (STDMETHODCALLTYPE *GetProgress)(
        IBackgroundCopyJob *This,
        BG_JOB_PROGRESS *pVal);

    HRESULT (STDMETHODCALLTYPE *GetTimes)(
        IBackgroundCopyJob *This,
        BG_JOB_TIMES *pVal);

    HRESULT (STDMETHODCALLTYPE *GetState)(
        IBackgroundCopyJob *This,
        BG_JOB_STATE *pVal);

    HRESULT (STDMETHODCALLTYPE *GetError)(
        IBackgroundCopyJob *This,
        IBackgroundCopyError **ppError);

    HRESULT (STDMETHODCALLTYPE *GetOwner)(
        IBackgroundCopyJob *This,
        LPWSTR *pVal);

    HRESULT (STDMETHODCALLTYPE *SetDisplayName)(
        IBackgroundCopyJob *This,
        LPCWSTR Val);

    HRESULT (STDMETHODCALLTYPE *GetDisplayName)(
        IBackgroundCopyJob *This,
        LPWSTR *pVal);

    HRESULT (STDMETHODCALLTYPE *SetDescription)(
        IBackgroundCopyJob *This,
        LPCWSTR Val);

    HRESULT (STDMETHODCALLTYPE *GetDescription)(
        IBackgroundCopyJob *This,
        LPWSTR *pVal);

    HRESULT (STDMETHODCALLTYPE *SetPriority)(
        IBackgroundCopyJob *This,
        BG_JOB_PRIORITY Val);

    HRESULT (STDMETHODCALLTYPE *GetPriority)(
        IBackgroundCopyJob *This,
        BG_JOB_PRIORITY *pVal);

    HRESULT (STDMETHODCALLTYPE *SetNotifyFlags)(
        IBackgroundCopyJob *This,
        ULONG Val);

    HRESULT (STDMETHODCALLTYPE *GetNotifyFlags)(
        IBackgroundCopyJob *This,
        ULONG *pVal);

    HRESULT (STDMETHODCALLTYPE *SetNotifyInterface)(
        IBackgroundCopyJob *This,
        IUnknown *Val);

    HRESULT (STDMETHODCALLTYPE *GetNotifyInterface)(
        IBackgroundCopyJob *This,
        IUnknown **pVal);

    HRESULT (STDMETHODCALLTYPE *SetMinimumRetryDelay)(
        IBackgroundCopyJob *This,
        ULONG Seconds);

    HRESULT (STDMETHODCALLTYPE *GetMinimumRetryDelay)(
        IBackgroundCopyJob *This,
        ULONG *Seconds);

    HRESULT (STDMETHODCALLTYPE *SetNoProgressTimeout)(
        IBackgroundCopyJob *This,
        ULONG Seconds);

    HRESULT (STDMETHODCALLTYPE *GetNoProgressTimeout)(
        IBackgroundCopyJob *This,
        ULONG *Seconds);

    HRESULT (STDMETHODCALLTYPE *GetErrorCount)(
        IBackgroundCopyJob *This,
        ULONG *Errors);

    HRESULT (STDMETHODCALLTYPE *SetProxySettings)(
        IBackgroundCopyJob *This,
        BG_JOB_PROXY_USAGE ProxyUsage,
        const WCHAR *ProxyList,
        const WCHAR *ProxyBypassList);

    HRESULT (STDMETHODCALLTYPE *GetProxySettings)(
        IBackgroundCopyJob *This,
        BG_JOB_PROXY_USAGE *pProxyUsage,
        LPWSTR *pProxyList,
        LPWSTR *pProxyBypassList);

    HRESULT (STDMETHODCALLTYPE *TakeOwnership)(
        IBackgroundCopyJob *This);

    END_INTERFACE
} IBackgroundCopyJobVtbl;

interface IBackgroundCopyJob {
    CONST_VTBL IBackgroundCopyJobVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IBackgroundCopyJob_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IBackgroundCopyJob_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IBackgroundCopyJob_Release(This) (This)->lpVtbl->Release(This)
/*** IBackgroundCopyJob methods ***/
#define IBackgroundCopyJob_AddFileSet(This,cFileCount,pFileSet) (This)->lpVtbl->AddFileSet(This,cFileCount,pFileSet)
#define IBackgroundCopyJob_AddFile(This,RemoteUrl,LocalName) (This)->lpVtbl->AddFile(This,RemoteUrl,LocalName)
#define IBackgroundCopyJob_EnumFiles(This,pEnum) (This)->lpVtbl->EnumFiles(This,pEnum)
#define IBackgroundCopyJob_Suspend(This) (This)->lpVtbl->Suspend(This)
#define IBackgroundCopyJob_Resume(This) (This)->lpVtbl->Resume(This)
#define IBackgroundCopyJob_Cancel(This) (This)->lpVtbl->Cancel(This)
#define IBackgroundCopyJob_Complete(This) (This)->lpVtbl->Complete(This)
#define IBackgroundCopyJob_GetId(This,pVal) (This)->lpVtbl->GetId(This,pVal)
#define IBackgroundCopyJob_GetType(This,pVal) (This)->lpVtbl->GetType(This,pVal)
#define IBackgroundCopyJob_GetProgress(This,pVal) (This)->lpVtbl->GetProgress(This,pVal)
#define IBackgroundCopyJob_GetTimes(This,pVal) (This)->lpVtbl->GetTimes(This,pVal)
#define IBackgroundCopyJob_GetState(This,pVal) (This)->lpVtbl->GetState(This,pVal)
#define IBackgroundCopyJob_GetError(This,ppError) (This)->lpVtbl->GetError(This,ppError)
#define IBackgroundCopyJob_GetOwner(This,pVal) (This)->lpVtbl->GetOwner(This,pVal)
#define IBackgroundCopyJob_SetDisplayName(This,Val) (This)->lpVtbl->SetDisplayName(This,Val)
#define IBackgroundCopyJob_GetDisplayName(This,pVal) (This)->lpVtbl->GetDisplayName(This,pVal)
#define IBackgroundCopyJob_SetDescription(This,Val) (This)->lpVtbl->SetDescription(This,Val)
#define IBackgroundCopyJob_GetDescription(This,pVal) (This)->lpVtbl->GetDescription(This,pVal)
#define IBackgroundCopyJob_SetPriority(This,Val) (This)->lpVtbl->SetPriority(This,Val)
#define IBackgroundCopyJob_GetPriority(This,pVal) (This)->lpVtbl->GetPriority(This,pVal)
#define IBackgroundCopyJob_SetNotifyFlags(This,Val) (This)->lpVtbl->SetNotifyFlags(This,Val)
#define IBackgroundCopyJob_GetNotifyFlags(This,pVal) (This)->lpVtbl->GetNotifyFlags(This,pVal)
#define IBackgroundCopyJob_SetNotifyInterface(This,Val) (This)->lpVtbl->SetNotifyInterface(This,Val)
#define IBackgroundCopyJob_GetNotifyInterface(This,pVal) (This)->lpVtbl->GetNotifyInterface(This,pVal)
#define IBackgroundCopyJob_SetMinimumRetryDelay(This,Seconds) (This)->lpVtbl->SetMinimumRetryDelay(This,Seconds)
#define IBackgroundCopyJob_GetMinimumRetryDelay(This,Seconds) (This)->lpVtbl->GetMinimumRetryDelay(This,Seconds)
#define IBackgroundCopyJob_SetNoProgressTimeout(This,Seconds) (This)->lpVtbl->SetNoProgressTimeout(This,Seconds)
#define IBackgroundCopyJob_GetNoProgressTimeout(This,Seconds) (This)->lpVtbl->GetNoProgressTimeout(This,Seconds)
#define IBackgroundCopyJob_GetErrorCount(This,Errors) (This)->lpVtbl->GetErrorCount(This,Errors)
#define IBackgroundCopyJob_SetProxySettings(This,ProxyUsage,ProxyList,ProxyBypassList) (This)->lpVtbl->SetProxySettings(This,ProxyUsage,ProxyList,ProxyBypassList)
#define IBackgroundCopyJob_GetProxySettings(This,pProxyUsage,pProxyList,pProxyBypassList) (This)->lpVtbl->GetProxySettings(This,pProxyUsage,pProxyList,pProxyBypassList)
#define IBackgroundCopyJob_TakeOwnership(This) (This)->lpVtbl->TakeOwnership(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IBackgroundCopyJob_QueryInterface(IBackgroundCopyJob* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IBackgroundCopyJob_AddRef(IBackgroundCopyJob* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IBackgroundCopyJob_Release(IBackgroundCopyJob* This) {
    return This->lpVtbl->Release(This);
}
/*** IBackgroundCopyJob methods ***/
static inline HRESULT IBackgroundCopyJob_AddFileSet(IBackgroundCopyJob* This,ULONG cFileCount,BG_FILE_INFO *pFileSet) {
    return This->lpVtbl->AddFileSet(This,cFileCount,pFileSet);
}
static inline HRESULT IBackgroundCopyJob_AddFile(IBackgroundCopyJob* This,LPCWSTR RemoteUrl,LPCWSTR LocalName) {
    return This->lpVtbl->AddFile(This,RemoteUrl,LocalName);
}
static inline HRESULT IBackgroundCopyJob_EnumFiles(IBackgroundCopyJob* This,IEnumBackgroundCopyFiles **pEnum) {
    return This->lpVtbl->EnumFiles(This,pEnum);
}
static inline HRESULT IBackgroundCopyJob_Suspend(IBackgroundCopyJob* This) {
    return This->lpVtbl->Suspend(This);
}
static inline HRESULT IBackgroundCopyJob_Resume(IBackgroundCopyJob* This) {
    return This->lpVtbl->Resume(This);
}
static inline HRESULT IBackgroundCopyJob_Cancel(IBackgroundCopyJob* This) {
    return This->lpVtbl->Cancel(This);
}
static inline HRESULT IBackgroundCopyJob_Complete(IBackgroundCopyJob* This) {
    return This->lpVtbl->Complete(This);
}
static inline HRESULT IBackgroundCopyJob_GetId(IBackgroundCopyJob* This,GUID *pVal) {
    return This->lpVtbl->GetId(This,pVal);
}
static inline HRESULT IBackgroundCopyJob_GetType(IBackgroundCopyJob* This,BG_JOB_TYPE *pVal) {
    return This->lpVtbl->GetType(This,pVal);
}
static inline HRESULT IBackgroundCopyJob_GetProgress(IBackgroundCopyJob* This,BG_JOB_PROGRESS *pVal) {
    return This->lpVtbl->GetProgress(This,pVal);
}
static inline HRESULT IBackgroundCopyJob_GetTimes(IBackgroundCopyJob* This,BG_JOB_TIMES *pVal) {
    return This->lpVtbl->GetTimes(This,pVal);
}
static inline HRESULT IBackgroundCopyJob_GetState(IBackgroundCopyJob* This,BG_JOB_STATE *pVal) {
    return This->lpVtbl->GetState(This,pVal);
}
static inline HRESULT IBackgroundCopyJob_GetError(IBackgroundCopyJob* This,IBackgroundCopyError **ppError) {
    return This->lpVtbl->GetError(This,ppError);
}
static inline HRESULT IBackgroundCopyJob_GetOwner(IBackgroundCopyJob* This,LPWSTR *pVal) {
    return This->lpVtbl->GetOwner(This,pVal);
}
static inline HRESULT IBackgroundCopyJob_SetDisplayName(IBackgroundCopyJob* This,LPCWSTR Val) {
    return This->lpVtbl->SetDisplayName(This,Val);
}
static inline HRESULT IBackgroundCopyJob_GetDisplayName(IBackgroundCopyJob* This,LPWSTR *pVal) {
    return This->lpVtbl->GetDisplayName(This,pVal);
}
static inline HRESULT IBackgroundCopyJob_SetDescription(IBackgroundCopyJob* This,LPCWSTR Val) {
    return This->lpVtbl->SetDescription(This,Val);
}
static inline HRESULT IBackgroundCopyJob_GetDescription(IBackgroundCopyJob* This,LPWSTR *pVal) {
    return This->lpVtbl->GetDescription(This,pVal);
}
static inline HRESULT IBackgroundCopyJob_SetPriority(IBackgroundCopyJob* This,BG_JOB_PRIORITY Val) {
    return This->lpVtbl->SetPriority(This,Val);
}
static inline HRESULT IBackgroundCopyJob_GetPriority(IBackgroundCopyJob* This,BG_JOB_PRIORITY *pVal) {
    return This->lpVtbl->GetPriority(This,pVal);
}
static inline HRESULT IBackgroundCopyJob_SetNotifyFlags(IBackgroundCopyJob* This,ULONG Val) {
    return This->lpVtbl->SetNotifyFlags(This,Val);
}
static inline HRESULT IBackgroundCopyJob_GetNotifyFlags(IBackgroundCopyJob* This,ULONG *pVal) {
    return This->lpVtbl->GetNotifyFlags(This,pVal);
}
static inline HRESULT IBackgroundCopyJob_SetNotifyInterface(IBackgroundCopyJob* This,IUnknown *Val) {
    return This->lpVtbl->SetNotifyInterface(This,Val);
}
static inline HRESULT IBackgroundCopyJob_GetNotifyInterface(IBackgroundCopyJob* This,IUnknown **pVal) {
    return This->lpVtbl->GetNotifyInterface(This,pVal);
}
static inline HRESULT IBackgroundCopyJob_SetMinimumRetryDelay(IBackgroundCopyJob* This,ULONG Seconds) {
    return This->lpVtbl->SetMinimumRetryDelay(This,Seconds);
}
static inline HRESULT IBackgroundCopyJob_GetMinimumRetryDelay(IBackgroundCopyJob* This,ULONG *Seconds) {
    return This->lpVtbl->GetMinimumRetryDelay(This,Seconds);
}
static inline HRESULT IBackgroundCopyJob_SetNoProgressTimeout(IBackgroundCopyJob* This,ULONG Seconds) {
    return This->lpVtbl->SetNoProgressTimeout(This,Seconds);
}
static inline HRESULT IBackgroundCopyJob_GetNoProgressTimeout(IBackgroundCopyJob* This,ULONG *Seconds) {
    return This->lpVtbl->GetNoProgressTimeout(This,Seconds);
}
static inline HRESULT IBackgroundCopyJob_GetErrorCount(IBackgroundCopyJob* This,ULONG *Errors) {
    return This->lpVtbl->GetErrorCount(This,Errors);
}
static inline HRESULT IBackgroundCopyJob_SetProxySettings(IBackgroundCopyJob* This,BG_JOB_PROXY_USAGE ProxyUsage,const WCHAR *ProxyList,const WCHAR *ProxyBypassList) {
    return This->lpVtbl->SetProxySettings(This,ProxyUsage,ProxyList,ProxyBypassList);
}
static inline HRESULT IBackgroundCopyJob_GetProxySettings(IBackgroundCopyJob* This,BG_JOB_PROXY_USAGE *pProxyUsage,LPWSTR *pProxyList,LPWSTR *pProxyBypassList) {
    return This->lpVtbl->GetProxySettings(This,pProxyUsage,pProxyList,pProxyBypassList);
}
static inline HRESULT IBackgroundCopyJob_TakeOwnership(IBackgroundCopyJob* This) {
    return This->lpVtbl->TakeOwnership(This);
}
#endif
#endif

#endif


#endif  /* __IBackgroundCopyJob_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IEnumBackgroundCopyJobs interface
 */
#ifndef __IEnumBackgroundCopyJobs_INTERFACE_DEFINED__
#define __IEnumBackgroundCopyJobs_INTERFACE_DEFINED__

DEFINE_GUID(IID_IEnumBackgroundCopyJobs, 0x1af4f612, 0x3b71, 0x466f, 0x8f,0x58, 0x7b,0x6f,0x73,0xac,0x57,0xad);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("1af4f612-3b71-466f-8f58-7b6f73ac57ad")
IEnumBackgroundCopyJobs : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Next(
        ULONG celt,
        IBackgroundCopyJob **rgelt,
        ULONG *pceltFetched) = 0;

    virtual HRESULT STDMETHODCALLTYPE Skip(
        ULONG celt) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reset(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IEnumBackgroundCopyJobs **ppenum) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCount(
        ULONG *puCount) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IEnumBackgroundCopyJobs, 0x1af4f612, 0x3b71, 0x466f, 0x8f,0x58, 0x7b,0x6f,0x73,0xac,0x57,0xad)
#endif
#else
typedef struct IEnumBackgroundCopyJobsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IEnumBackgroundCopyJobs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IEnumBackgroundCopyJobs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IEnumBackgroundCopyJobs *This);

    /*** IEnumBackgroundCopyJobs methods ***/
    HRESULT (STDMETHODCALLTYPE *Next)(
        IEnumBackgroundCopyJobs *This,
        ULONG celt,
        IBackgroundCopyJob **rgelt,
        ULONG *pceltFetched);

    HRESULT (STDMETHODCALLTYPE *Skip)(
        IEnumBackgroundCopyJobs *This,
        ULONG celt);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        IEnumBackgroundCopyJobs *This);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IEnumBackgroundCopyJobs *This,
        IEnumBackgroundCopyJobs **ppenum);

    HRESULT (STDMETHODCALLTYPE *GetCount)(
        IEnumBackgroundCopyJobs *This,
        ULONG *puCount);

    END_INTERFACE
} IEnumBackgroundCopyJobsVtbl;

interface IEnumBackgroundCopyJobs {
    CONST_VTBL IEnumBackgroundCopyJobsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IEnumBackgroundCopyJobs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IEnumBackgroundCopyJobs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IEnumBackgroundCopyJobs_Release(This) (This)->lpVtbl->Release(This)
/*** IEnumBackgroundCopyJobs methods ***/
#define IEnumBackgroundCopyJobs_Next(This,celt,rgelt,pceltFetched) (This)->lpVtbl->Next(This,celt,rgelt,pceltFetched)
#define IEnumBackgroundCopyJobs_Skip(This,celt) (This)->lpVtbl->Skip(This,celt)
#define IEnumBackgroundCopyJobs_Reset(This) (This)->lpVtbl->Reset(This)
#define IEnumBackgroundCopyJobs_Clone(This,ppenum) (This)->lpVtbl->Clone(This,ppenum)
#define IEnumBackgroundCopyJobs_GetCount(This,puCount) (This)->lpVtbl->GetCount(This,puCount)
#else
/*** IUnknown methods ***/
static inline HRESULT IEnumBackgroundCopyJobs_QueryInterface(IEnumBackgroundCopyJobs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IEnumBackgroundCopyJobs_AddRef(IEnumBackgroundCopyJobs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IEnumBackgroundCopyJobs_Release(IEnumBackgroundCopyJobs* This) {
    return This->lpVtbl->Release(This);
}
/*** IEnumBackgroundCopyJobs methods ***/
static inline HRESULT IEnumBackgroundCopyJobs_Next(IEnumBackgroundCopyJobs* This,ULONG celt,IBackgroundCopyJob **rgelt,ULONG *pceltFetched) {
    return This->lpVtbl->Next(This,celt,rgelt,pceltFetched);
}
static inline HRESULT IEnumBackgroundCopyJobs_Skip(IEnumBackgroundCopyJobs* This,ULONG celt) {
    return This->lpVtbl->Skip(This,celt);
}
static inline HRESULT IEnumBackgroundCopyJobs_Reset(IEnumBackgroundCopyJobs* This) {
    return This->lpVtbl->Reset(This);
}
static inline HRESULT IEnumBackgroundCopyJobs_Clone(IEnumBackgroundCopyJobs* This,IEnumBackgroundCopyJobs **ppenum) {
    return This->lpVtbl->Clone(This,ppenum);
}
static inline HRESULT IEnumBackgroundCopyJobs_GetCount(IEnumBackgroundCopyJobs* This,ULONG *puCount) {
    return This->lpVtbl->GetCount(This,puCount);
}
#endif
#endif

#endif


#endif  /* __IEnumBackgroundCopyJobs_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IBackgroundCopyCallback interface
 */
#ifndef __IBackgroundCopyCallback_INTERFACE_DEFINED__
#define __IBackgroundCopyCallback_INTERFACE_DEFINED__

DEFINE_GUID(IID_IBackgroundCopyCallback, 0x97ea99c7, 0x0186, 0x4ad4, 0x8d,0xf9, 0xc5,0xb4,0xe0,0xed,0x6b,0x22);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("97ea99c7-0186-4ad4-8df9-c5b4e0ed6b22")
IBackgroundCopyCallback : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE JobTransferred(
        IBackgroundCopyJob *pJob) = 0;

    virtual HRESULT STDMETHODCALLTYPE JobError(
        IBackgroundCopyJob *pJob,
        IBackgroundCopyError *pError) = 0;

    virtual HRESULT STDMETHODCALLTYPE JobModification(
        IBackgroundCopyJob *pJob,
        DWORD dwReserved) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IBackgroundCopyCallback, 0x97ea99c7, 0x0186, 0x4ad4, 0x8d,0xf9, 0xc5,0xb4,0xe0,0xed,0x6b,0x22)
#endif
#else
typedef struct IBackgroundCopyCallbackVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IBackgroundCopyCallback *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IBackgroundCopyCallback *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IBackgroundCopyCallback *This);

    /*** IBackgroundCopyCallback methods ***/
    HRESULT (STDMETHODCALLTYPE *JobTransferred)(
        IBackgroundCopyCallback *This,
        IBackgroundCopyJob *pJob);

    HRESULT (STDMETHODCALLTYPE *JobError)(
        IBackgroundCopyCallback *This,
        IBackgroundCopyJob *pJob,
        IBackgroundCopyError *pError);

    HRESULT (STDMETHODCALLTYPE *JobModification)(
        IBackgroundCopyCallback *This,
        IBackgroundCopyJob *pJob,
        DWORD dwReserved);

    END_INTERFACE
} IBackgroundCopyCallbackVtbl;

interface IBackgroundCopyCallback {
    CONST_VTBL IBackgroundCopyCallbackVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IBackgroundCopyCallback_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IBackgroundCopyCallback_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IBackgroundCopyCallback_Release(This) (This)->lpVtbl->Release(This)
/*** IBackgroundCopyCallback methods ***/
#define IBackgroundCopyCallback_JobTransferred(This,pJob) (This)->lpVtbl->JobTransferred(This,pJob)
#define IBackgroundCopyCallback_JobError(This,pJob,pError) (This)->lpVtbl->JobError(This,pJob,pError)
#define IBackgroundCopyCallback_JobModification(This,pJob,dwReserved) (This)->lpVtbl->JobModification(This,pJob,dwReserved)
#else
/*** IUnknown methods ***/
static inline HRESULT IBackgroundCopyCallback_QueryInterface(IBackgroundCopyCallback* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IBackgroundCopyCallback_AddRef(IBackgroundCopyCallback* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IBackgroundCopyCallback_Release(IBackgroundCopyCallback* This) {
    return This->lpVtbl->Release(This);
}
/*** IBackgroundCopyCallback methods ***/
static inline HRESULT IBackgroundCopyCallback_JobTransferred(IBackgroundCopyCallback* This,IBackgroundCopyJob *pJob) {
    return This->lpVtbl->JobTransferred(This,pJob);
}
static inline HRESULT IBackgroundCopyCallback_JobError(IBackgroundCopyCallback* This,IBackgroundCopyJob *pJob,IBackgroundCopyError *pError) {
    return This->lpVtbl->JobError(This,pJob,pError);
}
static inline HRESULT IBackgroundCopyCallback_JobModification(IBackgroundCopyCallback* This,IBackgroundCopyJob *pJob,DWORD dwReserved) {
    return This->lpVtbl->JobModification(This,pJob,dwReserved);
}
#endif
#endif

#endif


#endif  /* __IBackgroundCopyCallback_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IBackgroundCopyManager interface
 */
#ifndef __IBackgroundCopyManager_INTERFACE_DEFINED__
#define __IBackgroundCopyManager_INTERFACE_DEFINED__

#define BG_JOB_ENUM_ALL_USERS 0x0001
DEFINE_GUID(IID_IBackgroundCopyManager, 0x5ce34c0d, 0x0dc9, 0x4c1f, 0x89,0x7c, 0xda,0xa1,0xb7,0x8c,0xee,0x7c);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("5ce34c0d-0dc9-4c1f-897c-daa1b78cee7c")
IBackgroundCopyManager : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE CreateJob(
        LPCWSTR DisplayName,
        BG_JOB_TYPE Type,
        GUID *pJobId,
        IBackgroundCopyJob **ppJob) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetJob(
        REFGUID jobID,
        IBackgroundCopyJob **ppJob) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumJobs(
        DWORD dwFlags,
        IEnumBackgroundCopyJobs **ppEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetErrorDescription(
        HRESULT hResult,
        DWORD LanguageId,
        LPWSTR *pErrorDescription) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IBackgroundCopyManager, 0x5ce34c0d, 0x0dc9, 0x4c1f, 0x89,0x7c, 0xda,0xa1,0xb7,0x8c,0xee,0x7c)
#endif
#else
typedef struct IBackgroundCopyManagerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IBackgroundCopyManager *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IBackgroundCopyManager *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IBackgroundCopyManager *This);

    /*** IBackgroundCopyManager methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateJob)(
        IBackgroundCopyManager *This,
        LPCWSTR DisplayName,
        BG_JOB_TYPE Type,
        GUID *pJobId,
        IBackgroundCopyJob **ppJob);

    HRESULT (STDMETHODCALLTYPE *GetJob)(
        IBackgroundCopyManager *This,
        REFGUID jobID,
        IBackgroundCopyJob **ppJob);

    HRESULT (STDMETHODCALLTYPE *EnumJobs)(
        IBackgroundCopyManager *This,
        DWORD dwFlags,
        IEnumBackgroundCopyJobs **ppEnum);

    HRESULT (STDMETHODCALLTYPE *GetErrorDescription)(
        IBackgroundCopyManager *This,
        HRESULT hResult,
        DWORD LanguageId,
        LPWSTR *pErrorDescription);

    END_INTERFACE
} IBackgroundCopyManagerVtbl;

interface IBackgroundCopyManager {
    CONST_VTBL IBackgroundCopyManagerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IBackgroundCopyManager_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IBackgroundCopyManager_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IBackgroundCopyManager_Release(This) (This)->lpVtbl->Release(This)
/*** IBackgroundCopyManager methods ***/
#define IBackgroundCopyManager_CreateJob(This,DisplayName,Type,pJobId,ppJob) (This)->lpVtbl->CreateJob(This,DisplayName,Type,pJobId,ppJob)
#define IBackgroundCopyManager_GetJob(This,jobID,ppJob) (This)->lpVtbl->GetJob(This,jobID,ppJob)
#define IBackgroundCopyManager_EnumJobs(This,dwFlags,ppEnum) (This)->lpVtbl->EnumJobs(This,dwFlags,ppEnum)
#define IBackgroundCopyManager_GetErrorDescription(This,hResult,LanguageId,pErrorDescription) (This)->lpVtbl->GetErrorDescription(This,hResult,LanguageId,pErrorDescription)
#else
/*** IUnknown methods ***/
static inline HRESULT IBackgroundCopyManager_QueryInterface(IBackgroundCopyManager* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IBackgroundCopyManager_AddRef(IBackgroundCopyManager* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IBackgroundCopyManager_Release(IBackgroundCopyManager* This) {
    return This->lpVtbl->Release(This);
}
/*** IBackgroundCopyManager methods ***/
static inline HRESULT IBackgroundCopyManager_CreateJob(IBackgroundCopyManager* This,LPCWSTR DisplayName,BG_JOB_TYPE Type,GUID *pJobId,IBackgroundCopyJob **ppJob) {
    return This->lpVtbl->CreateJob(This,DisplayName,Type,pJobId,ppJob);
}
static inline HRESULT IBackgroundCopyManager_GetJob(IBackgroundCopyManager* This,REFGUID jobID,IBackgroundCopyJob **ppJob) {
    return This->lpVtbl->GetJob(This,jobID,ppJob);
}
static inline HRESULT IBackgroundCopyManager_EnumJobs(IBackgroundCopyManager* This,DWORD dwFlags,IEnumBackgroundCopyJobs **ppEnum) {
    return This->lpVtbl->EnumJobs(This,dwFlags,ppEnum);
}
static inline HRESULT IBackgroundCopyManager_GetErrorDescription(IBackgroundCopyManager* This,HRESULT hResult,DWORD LanguageId,LPWSTR *pErrorDescription) {
    return This->lpVtbl->GetErrorDescription(This,hResult,LanguageId,pErrorDescription);
}
#endif
#endif

#endif


#endif  /* __IBackgroundCopyManager_INTERFACE_DEFINED__ */

#ifndef __BackgroundCopyManager_LIBRARY_DEFINED__
#define __BackgroundCopyManager_LIBRARY_DEFINED__

DEFINE_GUID(LIBID_BackgroundCopyManager, 0x1deeb74f, 0x7915, 0x4560, 0xb5,0x58, 0x91,0x8c,0x83,0xf1,0x76,0xa6);

/*****************************************************************************
 * BackgroundCopyManager coclass
 */

DEFINE_GUID(CLSID_BackgroundCopyManager, 0x4991d34b, 0x80a1, 0x4291, 0x83,0xb6, 0x33,0x28,0x36,0x6b,0x90,0x97);

#ifdef __cplusplus
class DECLSPEC_UUID("4991d34b-80a1-4291-83b6-3328366b9097") BackgroundCopyManager;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(BackgroundCopyManager, 0x4991d34b, 0x80a1, 0x4291, 0x83,0xb6, 0x33,0x28,0x36,0x6b,0x90,0x97)
#endif
#endif

#ifndef __IBackgroundCopyCallback_FWD_DEFINED__
#define __IBackgroundCopyCallback_FWD_DEFINED__
typedef interface IBackgroundCopyCallback IBackgroundCopyCallback;
#ifdef __cplusplus
interface IBackgroundCopyCallback;
#endif /* __cplusplus */
#endif

#endif /* __BackgroundCopyManager_LIBRARY_DEFINED__ */
#include "bits1_5.h"
/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __bits_h__ */
