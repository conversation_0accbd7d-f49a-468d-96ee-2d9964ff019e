# Copyright 2023 The TensorFlow Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# ==============================================================================

from typing import ClassVar

MATCH: MatchType
MATCH_DISPATCHABLE: MatchType
NO_MATCH: MatchType

class MatchType:
    __members__: ClassVar[dict] = ...  # read-only
    MATCH: ClassVar[MatchType] = ...
    MATCH_DISPATCHABLE: ClassVar[MatchType] = ...
    NO_MATCH: ClassVar[MatchType] = ...
    __entries: ClassVar[dict] = ...
    def __init__(self, value: int) -> None: ...
    def __eq__(self, other: object) -> bool: ...
    def __hash__(self) -> int: ...
    def __index__(self) -> int: ...
    def __int__(self) -> int: ...
    def __ne__(self, other: object) -> bool: ...
    @property
    def name(self) -> str: ...
    @property
    def value(self) -> int: ...

class PySignatureChecker:
    def __init__(self, arg0: list[tuple[int, PyTypeChecker]]) -> None: ...
    def CheckCanonicalizedArgs(self, arg0: tuple) -> bool: ...

class PyTypeChecker:
    def __init__(self, *args, **kwargs) -> None: ...
    def Check(self, arg0: object) -> MatchType: ...
    def cache_size(self) -> int: ...
    def cost(self) -> int: ...

class PythonAPIDispatcher:
    def __init__(self, arg0: str, arg1: list[str], arg2: object) -> None: ...
    def Dispatch(self, arg0: object, arg1: object) -> object: ...
    def Register(self, arg0: PySignatureChecker, arg1: object) -> None: ...
    def Unregister(self, arg0: object) -> None: ...

def MakeInstanceChecker(*args) -> PyTypeChecker: ...
def MakeListChecker(arg0: PyTypeChecker) -> PyTypeChecker: ...
def MakeUnionChecker(arg0: list[PyTypeChecker]) -> PyTypeChecker: ...
def register_dispatchable_type(arg0: object) -> object: ...
