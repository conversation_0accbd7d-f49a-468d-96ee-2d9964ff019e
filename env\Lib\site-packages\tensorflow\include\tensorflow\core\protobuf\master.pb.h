// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/master.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_bases.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/device_attributes.pb.h"
#include "tensorflow/core/framework/graph.pb.h"
#include "tensorflow/core/framework/tensor.pb.h"
#include "tensorflow/core/protobuf/config.pb.h"
#include "tensorflow/core/protobuf/error_codes.pb.h"
#include "tensorflow/core/protobuf/named_tensor.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto;
namespace tensorflow {
class CloseSessionRequest;
struct CloseSessionRequestDefaultTypeInternal;
extern CloseSessionRequestDefaultTypeInternal _CloseSessionRequest_default_instance_;
class CloseSessionResponse;
struct CloseSessionResponseDefaultTypeInternal;
extern CloseSessionResponseDefaultTypeInternal _CloseSessionResponse_default_instance_;
class CreateSessionRequest;
struct CreateSessionRequestDefaultTypeInternal;
extern CreateSessionRequestDefaultTypeInternal _CreateSessionRequest_default_instance_;
class CreateSessionResponse;
struct CreateSessionResponseDefaultTypeInternal;
extern CreateSessionResponseDefaultTypeInternal _CreateSessionResponse_default_instance_;
class ExtendSessionRequest;
struct ExtendSessionRequestDefaultTypeInternal;
extern ExtendSessionRequestDefaultTypeInternal _ExtendSessionRequest_default_instance_;
class ExtendSessionResponse;
struct ExtendSessionResponseDefaultTypeInternal;
extern ExtendSessionResponseDefaultTypeInternal _ExtendSessionResponse_default_instance_;
class ListDevicesRequest;
struct ListDevicesRequestDefaultTypeInternal;
extern ListDevicesRequestDefaultTypeInternal _ListDevicesRequest_default_instance_;
class ListDevicesResponse;
struct ListDevicesResponseDefaultTypeInternal;
extern ListDevicesResponseDefaultTypeInternal _ListDevicesResponse_default_instance_;
class MakeCallableRequest;
struct MakeCallableRequestDefaultTypeInternal;
extern MakeCallableRequestDefaultTypeInternal _MakeCallableRequest_default_instance_;
class MakeCallableResponse;
struct MakeCallableResponseDefaultTypeInternal;
extern MakeCallableResponseDefaultTypeInternal _MakeCallableResponse_default_instance_;
class PartialRunSetupRequest;
struct PartialRunSetupRequestDefaultTypeInternal;
extern PartialRunSetupRequestDefaultTypeInternal _PartialRunSetupRequest_default_instance_;
class PartialRunSetupResponse;
struct PartialRunSetupResponseDefaultTypeInternal;
extern PartialRunSetupResponseDefaultTypeInternal _PartialRunSetupResponse_default_instance_;
class ReleaseCallableRequest;
struct ReleaseCallableRequestDefaultTypeInternal;
extern ReleaseCallableRequestDefaultTypeInternal _ReleaseCallableRequest_default_instance_;
class ReleaseCallableResponse;
struct ReleaseCallableResponseDefaultTypeInternal;
extern ReleaseCallableResponseDefaultTypeInternal _ReleaseCallableResponse_default_instance_;
class ResetRequest;
struct ResetRequestDefaultTypeInternal;
extern ResetRequestDefaultTypeInternal _ResetRequest_default_instance_;
class ResetResponse;
struct ResetResponseDefaultTypeInternal;
extern ResetResponseDefaultTypeInternal _ResetResponse_default_instance_;
class RunCallableRequest;
struct RunCallableRequestDefaultTypeInternal;
extern RunCallableRequestDefaultTypeInternal _RunCallableRequest_default_instance_;
class RunCallableResponse;
struct RunCallableResponseDefaultTypeInternal;
extern RunCallableResponseDefaultTypeInternal _RunCallableResponse_default_instance_;
class RunStepRequest;
struct RunStepRequestDefaultTypeInternal;
extern RunStepRequestDefaultTypeInternal _RunStepRequest_default_instance_;
class RunStepResponse;
struct RunStepResponseDefaultTypeInternal;
extern RunStepResponseDefaultTypeInternal _RunStepResponse_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::CloseSessionRequest* Arena::CreateMaybeMessage<::tensorflow::CloseSessionRequest>(Arena*);
template<> ::tensorflow::CloseSessionResponse* Arena::CreateMaybeMessage<::tensorflow::CloseSessionResponse>(Arena*);
template<> ::tensorflow::CreateSessionRequest* Arena::CreateMaybeMessage<::tensorflow::CreateSessionRequest>(Arena*);
template<> ::tensorflow::CreateSessionResponse* Arena::CreateMaybeMessage<::tensorflow::CreateSessionResponse>(Arena*);
template<> ::tensorflow::ExtendSessionRequest* Arena::CreateMaybeMessage<::tensorflow::ExtendSessionRequest>(Arena*);
template<> ::tensorflow::ExtendSessionResponse* Arena::CreateMaybeMessage<::tensorflow::ExtendSessionResponse>(Arena*);
template<> ::tensorflow::ListDevicesRequest* Arena::CreateMaybeMessage<::tensorflow::ListDevicesRequest>(Arena*);
template<> ::tensorflow::ListDevicesResponse* Arena::CreateMaybeMessage<::tensorflow::ListDevicesResponse>(Arena*);
template<> ::tensorflow::MakeCallableRequest* Arena::CreateMaybeMessage<::tensorflow::MakeCallableRequest>(Arena*);
template<> ::tensorflow::MakeCallableResponse* Arena::CreateMaybeMessage<::tensorflow::MakeCallableResponse>(Arena*);
template<> ::tensorflow::PartialRunSetupRequest* Arena::CreateMaybeMessage<::tensorflow::PartialRunSetupRequest>(Arena*);
template<> ::tensorflow::PartialRunSetupResponse* Arena::CreateMaybeMessage<::tensorflow::PartialRunSetupResponse>(Arena*);
template<> ::tensorflow::ReleaseCallableRequest* Arena::CreateMaybeMessage<::tensorflow::ReleaseCallableRequest>(Arena*);
template<> ::tensorflow::ReleaseCallableResponse* Arena::CreateMaybeMessage<::tensorflow::ReleaseCallableResponse>(Arena*);
template<> ::tensorflow::ResetRequest* Arena::CreateMaybeMessage<::tensorflow::ResetRequest>(Arena*);
template<> ::tensorflow::ResetResponse* Arena::CreateMaybeMessage<::tensorflow::ResetResponse>(Arena*);
template<> ::tensorflow::RunCallableRequest* Arena::CreateMaybeMessage<::tensorflow::RunCallableRequest>(Arena*);
template<> ::tensorflow::RunCallableResponse* Arena::CreateMaybeMessage<::tensorflow::RunCallableResponse>(Arena*);
template<> ::tensorflow::RunStepRequest* Arena::CreateMaybeMessage<::tensorflow::RunStepRequest>(Arena*);
template<> ::tensorflow::RunStepResponse* Arena::CreateMaybeMessage<::tensorflow::RunStepResponse>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

// ===================================================================

class CreateSessionRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.CreateSessionRequest) */ {
 public:
  inline CreateSessionRequest() : CreateSessionRequest(nullptr) {}
  ~CreateSessionRequest() override;
  explicit PROTOBUF_CONSTEXPR CreateSessionRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CreateSessionRequest(const CreateSessionRequest& from);
  CreateSessionRequest(CreateSessionRequest&& from) noexcept
    : CreateSessionRequest() {
    *this = ::std::move(from);
  }

  inline CreateSessionRequest& operator=(const CreateSessionRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline CreateSessionRequest& operator=(CreateSessionRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CreateSessionRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const CreateSessionRequest* internal_default_instance() {
    return reinterpret_cast<const CreateSessionRequest*>(
               &_CreateSessionRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(CreateSessionRequest& a, CreateSessionRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(CreateSessionRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CreateSessionRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CreateSessionRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CreateSessionRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CreateSessionRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CreateSessionRequest& from) {
    CreateSessionRequest::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CreateSessionRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CreateSessionRequest";
  }
  protected:
  explicit CreateSessionRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTargetFieldNumber = 3,
    kGraphDefFieldNumber = 1,
    kConfigFieldNumber = 2,
  };
  // string target = 3;
  void clear_target();
  const std::string& target() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_target(ArgT0&& arg0, ArgT... args);
  std::string* mutable_target();
  PROTOBUF_NODISCARD std::string* release_target();
  void set_allocated_target(std::string* target);
  private:
  const std::string& _internal_target() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_target(const std::string& value);
  std::string* _internal_mutable_target();
  public:

  // .tensorflow.GraphDef graph_def = 1;
  bool has_graph_def() const;
  private:
  bool _internal_has_graph_def() const;
  public:
  void clear_graph_def();
  const ::tensorflow::GraphDef& graph_def() const;
  PROTOBUF_NODISCARD ::tensorflow::GraphDef* release_graph_def();
  ::tensorflow::GraphDef* mutable_graph_def();
  void set_allocated_graph_def(::tensorflow::GraphDef* graph_def);
  private:
  const ::tensorflow::GraphDef& _internal_graph_def() const;
  ::tensorflow::GraphDef* _internal_mutable_graph_def();
  public:
  void unsafe_arena_set_allocated_graph_def(
      ::tensorflow::GraphDef* graph_def);
  ::tensorflow::GraphDef* unsafe_arena_release_graph_def();

  // .tensorflow.ConfigProto config = 2;
  bool has_config() const;
  private:
  bool _internal_has_config() const;
  public:
  void clear_config();
  const ::tensorflow::ConfigProto& config() const;
  PROTOBUF_NODISCARD ::tensorflow::ConfigProto* release_config();
  ::tensorflow::ConfigProto* mutable_config();
  void set_allocated_config(::tensorflow::ConfigProto* config);
  private:
  const ::tensorflow::ConfigProto& _internal_config() const;
  ::tensorflow::ConfigProto* _internal_mutable_config();
  public:
  void unsafe_arena_set_allocated_config(
      ::tensorflow::ConfigProto* config);
  ::tensorflow::ConfigProto* unsafe_arena_release_config();

  // @@protoc_insertion_point(class_scope:tensorflow.CreateSessionRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr target_;
    ::tensorflow::GraphDef* graph_def_;
    ::tensorflow::ConfigProto* config_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto;
};
// -------------------------------------------------------------------

class CreateSessionResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.CreateSessionResponse) */ {
 public:
  inline CreateSessionResponse() : CreateSessionResponse(nullptr) {}
  ~CreateSessionResponse() override;
  explicit PROTOBUF_CONSTEXPR CreateSessionResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CreateSessionResponse(const CreateSessionResponse& from);
  CreateSessionResponse(CreateSessionResponse&& from) noexcept
    : CreateSessionResponse() {
    *this = ::std::move(from);
  }

  inline CreateSessionResponse& operator=(const CreateSessionResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline CreateSessionResponse& operator=(CreateSessionResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CreateSessionResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const CreateSessionResponse* internal_default_instance() {
    return reinterpret_cast<const CreateSessionResponse*>(
               &_CreateSessionResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(CreateSessionResponse& a, CreateSessionResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(CreateSessionResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CreateSessionResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CreateSessionResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CreateSessionResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CreateSessionResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CreateSessionResponse& from) {
    CreateSessionResponse::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CreateSessionResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CreateSessionResponse";
  }
  protected:
  explicit CreateSessionResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSessionHandleFieldNumber = 1,
    kGraphVersionFieldNumber = 2,
  };
  // string session_handle = 1;
  void clear_session_handle();
  const std::string& session_handle() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_session_handle(ArgT0&& arg0, ArgT... args);
  std::string* mutable_session_handle();
  PROTOBUF_NODISCARD std::string* release_session_handle();
  void set_allocated_session_handle(std::string* session_handle);
  private:
  const std::string& _internal_session_handle() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_session_handle(const std::string& value);
  std::string* _internal_mutable_session_handle();
  public:

  // int64 graph_version = 2;
  void clear_graph_version();
  int64_t graph_version() const;
  void set_graph_version(int64_t value);
  private:
  int64_t _internal_graph_version() const;
  void _internal_set_graph_version(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.CreateSessionResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr session_handle_;
    int64_t graph_version_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto;
};
// -------------------------------------------------------------------

class ExtendSessionRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.ExtendSessionRequest) */ {
 public:
  inline ExtendSessionRequest() : ExtendSessionRequest(nullptr) {}
  ~ExtendSessionRequest() override;
  explicit PROTOBUF_CONSTEXPR ExtendSessionRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ExtendSessionRequest(const ExtendSessionRequest& from);
  ExtendSessionRequest(ExtendSessionRequest&& from) noexcept
    : ExtendSessionRequest() {
    *this = ::std::move(from);
  }

  inline ExtendSessionRequest& operator=(const ExtendSessionRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline ExtendSessionRequest& operator=(ExtendSessionRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ExtendSessionRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const ExtendSessionRequest* internal_default_instance() {
    return reinterpret_cast<const ExtendSessionRequest*>(
               &_ExtendSessionRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(ExtendSessionRequest& a, ExtendSessionRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(ExtendSessionRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ExtendSessionRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ExtendSessionRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ExtendSessionRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ExtendSessionRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ExtendSessionRequest& from) {
    ExtendSessionRequest::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ExtendSessionRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ExtendSessionRequest";
  }
  protected:
  explicit ExtendSessionRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSessionHandleFieldNumber = 1,
    kGraphDefFieldNumber = 2,
    kCurrentGraphVersionFieldNumber = 3,
  };
  // string session_handle = 1;
  void clear_session_handle();
  const std::string& session_handle() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_session_handle(ArgT0&& arg0, ArgT... args);
  std::string* mutable_session_handle();
  PROTOBUF_NODISCARD std::string* release_session_handle();
  void set_allocated_session_handle(std::string* session_handle);
  private:
  const std::string& _internal_session_handle() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_session_handle(const std::string& value);
  std::string* _internal_mutable_session_handle();
  public:

  // .tensorflow.GraphDef graph_def = 2;
  bool has_graph_def() const;
  private:
  bool _internal_has_graph_def() const;
  public:
  void clear_graph_def();
  const ::tensorflow::GraphDef& graph_def() const;
  PROTOBUF_NODISCARD ::tensorflow::GraphDef* release_graph_def();
  ::tensorflow::GraphDef* mutable_graph_def();
  void set_allocated_graph_def(::tensorflow::GraphDef* graph_def);
  private:
  const ::tensorflow::GraphDef& _internal_graph_def() const;
  ::tensorflow::GraphDef* _internal_mutable_graph_def();
  public:
  void unsafe_arena_set_allocated_graph_def(
      ::tensorflow::GraphDef* graph_def);
  ::tensorflow::GraphDef* unsafe_arena_release_graph_def();

  // int64 current_graph_version = 3;
  void clear_current_graph_version();
  int64_t current_graph_version() const;
  void set_current_graph_version(int64_t value);
  private:
  int64_t _internal_current_graph_version() const;
  void _internal_set_current_graph_version(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.ExtendSessionRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr session_handle_;
    ::tensorflow::GraphDef* graph_def_;
    int64_t current_graph_version_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto;
};
// -------------------------------------------------------------------

class ExtendSessionResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.ExtendSessionResponse) */ {
 public:
  inline ExtendSessionResponse() : ExtendSessionResponse(nullptr) {}
  ~ExtendSessionResponse() override;
  explicit PROTOBUF_CONSTEXPR ExtendSessionResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ExtendSessionResponse(const ExtendSessionResponse& from);
  ExtendSessionResponse(ExtendSessionResponse&& from) noexcept
    : ExtendSessionResponse() {
    *this = ::std::move(from);
  }

  inline ExtendSessionResponse& operator=(const ExtendSessionResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline ExtendSessionResponse& operator=(ExtendSessionResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ExtendSessionResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const ExtendSessionResponse* internal_default_instance() {
    return reinterpret_cast<const ExtendSessionResponse*>(
               &_ExtendSessionResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(ExtendSessionResponse& a, ExtendSessionResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(ExtendSessionResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ExtendSessionResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ExtendSessionResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ExtendSessionResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ExtendSessionResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ExtendSessionResponse& from) {
    ExtendSessionResponse::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ExtendSessionResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ExtendSessionResponse";
  }
  protected:
  explicit ExtendSessionResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNewGraphVersionFieldNumber = 4,
  };
  // int64 new_graph_version = 4;
  void clear_new_graph_version();
  int64_t new_graph_version() const;
  void set_new_graph_version(int64_t value);
  private:
  int64_t _internal_new_graph_version() const;
  void _internal_set_new_graph_version(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.ExtendSessionResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int64_t new_graph_version_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto;
};
// -------------------------------------------------------------------

class RunStepRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.RunStepRequest) */ {
 public:
  inline RunStepRequest() : RunStepRequest(nullptr) {}
  ~RunStepRequest() override;
  explicit PROTOBUF_CONSTEXPR RunStepRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RunStepRequest(const RunStepRequest& from);
  RunStepRequest(RunStepRequest&& from) noexcept
    : RunStepRequest() {
    *this = ::std::move(from);
  }

  inline RunStepRequest& operator=(const RunStepRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline RunStepRequest& operator=(RunStepRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RunStepRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const RunStepRequest* internal_default_instance() {
    return reinterpret_cast<const RunStepRequest*>(
               &_RunStepRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(RunStepRequest& a, RunStepRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(RunStepRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RunStepRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RunStepRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RunStepRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RunStepRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const RunStepRequest& from) {
    RunStepRequest::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RunStepRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.RunStepRequest";
  }
  protected:
  explicit RunStepRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFeedFieldNumber = 2,
    kFetchFieldNumber = 3,
    kTargetFieldNumber = 4,
    kSessionHandleFieldNumber = 1,
    kPartialRunHandleFieldNumber = 6,
    kOptionsFieldNumber = 5,
    kRequestIdFieldNumber = 8,
    kStoreErrorsInResponseBodyFieldNumber = 7,
  };
  // repeated .tensorflow.NamedTensorProto feed = 2;
  int feed_size() const;
  private:
  int _internal_feed_size() const;
  public:
  void clear_feed();
  ::tensorflow::NamedTensorProto* mutable_feed(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NamedTensorProto >*
      mutable_feed();
  private:
  const ::tensorflow::NamedTensorProto& _internal_feed(int index) const;
  ::tensorflow::NamedTensorProto* _internal_add_feed();
  public:
  const ::tensorflow::NamedTensorProto& feed(int index) const;
  ::tensorflow::NamedTensorProto* add_feed();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NamedTensorProto >&
      feed() const;

  // repeated string fetch = 3;
  int fetch_size() const;
  private:
  int _internal_fetch_size() const;
  public:
  void clear_fetch();
  const std::string& fetch(int index) const;
  std::string* mutable_fetch(int index);
  void set_fetch(int index, const std::string& value);
  void set_fetch(int index, std::string&& value);
  void set_fetch(int index, const char* value);
  void set_fetch(int index, const char* value, size_t size);
  std::string* add_fetch();
  void add_fetch(const std::string& value);
  void add_fetch(std::string&& value);
  void add_fetch(const char* value);
  void add_fetch(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& fetch() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_fetch();
  private:
  const std::string& _internal_fetch(int index) const;
  std::string* _internal_add_fetch();
  public:

  // repeated string target = 4;
  int target_size() const;
  private:
  int _internal_target_size() const;
  public:
  void clear_target();
  const std::string& target(int index) const;
  std::string* mutable_target(int index);
  void set_target(int index, const std::string& value);
  void set_target(int index, std::string&& value);
  void set_target(int index, const char* value);
  void set_target(int index, const char* value, size_t size);
  std::string* add_target();
  void add_target(const std::string& value);
  void add_target(std::string&& value);
  void add_target(const char* value);
  void add_target(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& target() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_target();
  private:
  const std::string& _internal_target(int index) const;
  std::string* _internal_add_target();
  public:

  // string session_handle = 1;
  void clear_session_handle();
  const std::string& session_handle() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_session_handle(ArgT0&& arg0, ArgT... args);
  std::string* mutable_session_handle();
  PROTOBUF_NODISCARD std::string* release_session_handle();
  void set_allocated_session_handle(std::string* session_handle);
  private:
  const std::string& _internal_session_handle() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_session_handle(const std::string& value);
  std::string* _internal_mutable_session_handle();
  public:

  // string partial_run_handle = 6;
  void clear_partial_run_handle();
  const std::string& partial_run_handle() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_partial_run_handle(ArgT0&& arg0, ArgT... args);
  std::string* mutable_partial_run_handle();
  PROTOBUF_NODISCARD std::string* release_partial_run_handle();
  void set_allocated_partial_run_handle(std::string* partial_run_handle);
  private:
  const std::string& _internal_partial_run_handle() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_partial_run_handle(const std::string& value);
  std::string* _internal_mutable_partial_run_handle();
  public:

  // .tensorflow.RunOptions options = 5;
  bool has_options() const;
  private:
  bool _internal_has_options() const;
  public:
  void clear_options();
  const ::tensorflow::RunOptions& options() const;
  PROTOBUF_NODISCARD ::tensorflow::RunOptions* release_options();
  ::tensorflow::RunOptions* mutable_options();
  void set_allocated_options(::tensorflow::RunOptions* options);
  private:
  const ::tensorflow::RunOptions& _internal_options() const;
  ::tensorflow::RunOptions* _internal_mutable_options();
  public:
  void unsafe_arena_set_allocated_options(
      ::tensorflow::RunOptions* options);
  ::tensorflow::RunOptions* unsafe_arena_release_options();

  // int64 request_id = 8;
  void clear_request_id();
  int64_t request_id() const;
  void set_request_id(int64_t value);
  private:
  int64_t _internal_request_id() const;
  void _internal_set_request_id(int64_t value);
  public:

  // bool store_errors_in_response_body = 7;
  void clear_store_errors_in_response_body();
  bool store_errors_in_response_body() const;
  void set_store_errors_in_response_body(bool value);
  private:
  bool _internal_store_errors_in_response_body() const;
  void _internal_set_store_errors_in_response_body(bool value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.RunStepRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NamedTensorProto > feed_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> fetch_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> target_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr session_handle_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr partial_run_handle_;
    ::tensorflow::RunOptions* options_;
    int64_t request_id_;
    bool store_errors_in_response_body_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto;
};
// -------------------------------------------------------------------

class RunStepResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.RunStepResponse) */ {
 public:
  inline RunStepResponse() : RunStepResponse(nullptr) {}
  ~RunStepResponse() override;
  explicit PROTOBUF_CONSTEXPR RunStepResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RunStepResponse(const RunStepResponse& from);
  RunStepResponse(RunStepResponse&& from) noexcept
    : RunStepResponse() {
    *this = ::std::move(from);
  }

  inline RunStepResponse& operator=(const RunStepResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline RunStepResponse& operator=(RunStepResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RunStepResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const RunStepResponse* internal_default_instance() {
    return reinterpret_cast<const RunStepResponse*>(
               &_RunStepResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(RunStepResponse& a, RunStepResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(RunStepResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RunStepResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RunStepResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RunStepResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RunStepResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const RunStepResponse& from) {
    RunStepResponse::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RunStepResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.RunStepResponse";
  }
  protected:
  explicit RunStepResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTensorFieldNumber = 1,
    kStatusErrorMessageFieldNumber = 4,
    kMetadataFieldNumber = 2,
    kStatusCodeFieldNumber = 3,
  };
  // repeated .tensorflow.NamedTensorProto tensor = 1;
  int tensor_size() const;
  private:
  int _internal_tensor_size() const;
  public:
  void clear_tensor();
  ::tensorflow::NamedTensorProto* mutable_tensor(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NamedTensorProto >*
      mutable_tensor();
  private:
  const ::tensorflow::NamedTensorProto& _internal_tensor(int index) const;
  ::tensorflow::NamedTensorProto* _internal_add_tensor();
  public:
  const ::tensorflow::NamedTensorProto& tensor(int index) const;
  ::tensorflow::NamedTensorProto* add_tensor();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NamedTensorProto >&
      tensor() const;

  // string status_error_message = 4;
  void clear_status_error_message();
  const std::string& status_error_message() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_status_error_message(ArgT0&& arg0, ArgT... args);
  std::string* mutable_status_error_message();
  PROTOBUF_NODISCARD std::string* release_status_error_message();
  void set_allocated_status_error_message(std::string* status_error_message);
  private:
  const std::string& _internal_status_error_message() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_status_error_message(const std::string& value);
  std::string* _internal_mutable_status_error_message();
  public:

  // .tensorflow.RunMetadata metadata = 2;
  bool has_metadata() const;
  private:
  bool _internal_has_metadata() const;
  public:
  void clear_metadata();
  const ::tensorflow::RunMetadata& metadata() const;
  PROTOBUF_NODISCARD ::tensorflow::RunMetadata* release_metadata();
  ::tensorflow::RunMetadata* mutable_metadata();
  void set_allocated_metadata(::tensorflow::RunMetadata* metadata);
  private:
  const ::tensorflow::RunMetadata& _internal_metadata() const;
  ::tensorflow::RunMetadata* _internal_mutable_metadata();
  public:
  void unsafe_arena_set_allocated_metadata(
      ::tensorflow::RunMetadata* metadata);
  ::tensorflow::RunMetadata* unsafe_arena_release_metadata();

  // .tensorflow.error.Code status_code = 3;
  void clear_status_code();
  ::tensorflow::error::Code status_code() const;
  void set_status_code(::tensorflow::error::Code value);
  private:
  ::tensorflow::error::Code _internal_status_code() const;
  void _internal_set_status_code(::tensorflow::error::Code value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.RunStepResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NamedTensorProto > tensor_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr status_error_message_;
    ::tensorflow::RunMetadata* metadata_;
    int status_code_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto;
};
// -------------------------------------------------------------------

class PartialRunSetupRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.PartialRunSetupRequest) */ {
 public:
  inline PartialRunSetupRequest() : PartialRunSetupRequest(nullptr) {}
  ~PartialRunSetupRequest() override;
  explicit PROTOBUF_CONSTEXPR PartialRunSetupRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  PartialRunSetupRequest(const PartialRunSetupRequest& from);
  PartialRunSetupRequest(PartialRunSetupRequest&& from) noexcept
    : PartialRunSetupRequest() {
    *this = ::std::move(from);
  }

  inline PartialRunSetupRequest& operator=(const PartialRunSetupRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline PartialRunSetupRequest& operator=(PartialRunSetupRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const PartialRunSetupRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const PartialRunSetupRequest* internal_default_instance() {
    return reinterpret_cast<const PartialRunSetupRequest*>(
               &_PartialRunSetupRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(PartialRunSetupRequest& a, PartialRunSetupRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(PartialRunSetupRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PartialRunSetupRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  PartialRunSetupRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<PartialRunSetupRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const PartialRunSetupRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const PartialRunSetupRequest& from) {
    PartialRunSetupRequest::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PartialRunSetupRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.PartialRunSetupRequest";
  }
  protected:
  explicit PartialRunSetupRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFeedFieldNumber = 2,
    kFetchFieldNumber = 3,
    kTargetFieldNumber = 4,
    kSessionHandleFieldNumber = 1,
    kRequestIdFieldNumber = 5,
  };
  // repeated string feed = 2;
  int feed_size() const;
  private:
  int _internal_feed_size() const;
  public:
  void clear_feed();
  const std::string& feed(int index) const;
  std::string* mutable_feed(int index);
  void set_feed(int index, const std::string& value);
  void set_feed(int index, std::string&& value);
  void set_feed(int index, const char* value);
  void set_feed(int index, const char* value, size_t size);
  std::string* add_feed();
  void add_feed(const std::string& value);
  void add_feed(std::string&& value);
  void add_feed(const char* value);
  void add_feed(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& feed() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_feed();
  private:
  const std::string& _internal_feed(int index) const;
  std::string* _internal_add_feed();
  public:

  // repeated string fetch = 3;
  int fetch_size() const;
  private:
  int _internal_fetch_size() const;
  public:
  void clear_fetch();
  const std::string& fetch(int index) const;
  std::string* mutable_fetch(int index);
  void set_fetch(int index, const std::string& value);
  void set_fetch(int index, std::string&& value);
  void set_fetch(int index, const char* value);
  void set_fetch(int index, const char* value, size_t size);
  std::string* add_fetch();
  void add_fetch(const std::string& value);
  void add_fetch(std::string&& value);
  void add_fetch(const char* value);
  void add_fetch(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& fetch() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_fetch();
  private:
  const std::string& _internal_fetch(int index) const;
  std::string* _internal_add_fetch();
  public:

  // repeated string target = 4;
  int target_size() const;
  private:
  int _internal_target_size() const;
  public:
  void clear_target();
  const std::string& target(int index) const;
  std::string* mutable_target(int index);
  void set_target(int index, const std::string& value);
  void set_target(int index, std::string&& value);
  void set_target(int index, const char* value);
  void set_target(int index, const char* value, size_t size);
  std::string* add_target();
  void add_target(const std::string& value);
  void add_target(std::string&& value);
  void add_target(const char* value);
  void add_target(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& target() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_target();
  private:
  const std::string& _internal_target(int index) const;
  std::string* _internal_add_target();
  public:

  // string session_handle = 1;
  void clear_session_handle();
  const std::string& session_handle() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_session_handle(ArgT0&& arg0, ArgT... args);
  std::string* mutable_session_handle();
  PROTOBUF_NODISCARD std::string* release_session_handle();
  void set_allocated_session_handle(std::string* session_handle);
  private:
  const std::string& _internal_session_handle() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_session_handle(const std::string& value);
  std::string* _internal_mutable_session_handle();
  public:

  // int64 request_id = 5;
  void clear_request_id();
  int64_t request_id() const;
  void set_request_id(int64_t value);
  private:
  int64_t _internal_request_id() const;
  void _internal_set_request_id(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.PartialRunSetupRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> feed_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> fetch_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> target_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr session_handle_;
    int64_t request_id_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto;
};
// -------------------------------------------------------------------

class PartialRunSetupResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.PartialRunSetupResponse) */ {
 public:
  inline PartialRunSetupResponse() : PartialRunSetupResponse(nullptr) {}
  ~PartialRunSetupResponse() override;
  explicit PROTOBUF_CONSTEXPR PartialRunSetupResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  PartialRunSetupResponse(const PartialRunSetupResponse& from);
  PartialRunSetupResponse(PartialRunSetupResponse&& from) noexcept
    : PartialRunSetupResponse() {
    *this = ::std::move(from);
  }

  inline PartialRunSetupResponse& operator=(const PartialRunSetupResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline PartialRunSetupResponse& operator=(PartialRunSetupResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const PartialRunSetupResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const PartialRunSetupResponse* internal_default_instance() {
    return reinterpret_cast<const PartialRunSetupResponse*>(
               &_PartialRunSetupResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(PartialRunSetupResponse& a, PartialRunSetupResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(PartialRunSetupResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PartialRunSetupResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  PartialRunSetupResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<PartialRunSetupResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const PartialRunSetupResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const PartialRunSetupResponse& from) {
    PartialRunSetupResponse::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PartialRunSetupResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.PartialRunSetupResponse";
  }
  protected:
  explicit PartialRunSetupResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPartialRunHandleFieldNumber = 1,
  };
  // string partial_run_handle = 1;
  void clear_partial_run_handle();
  const std::string& partial_run_handle() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_partial_run_handle(ArgT0&& arg0, ArgT... args);
  std::string* mutable_partial_run_handle();
  PROTOBUF_NODISCARD std::string* release_partial_run_handle();
  void set_allocated_partial_run_handle(std::string* partial_run_handle);
  private:
  const std::string& _internal_partial_run_handle() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_partial_run_handle(const std::string& value);
  std::string* _internal_mutable_partial_run_handle();
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.PartialRunSetupResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr partial_run_handle_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto;
};
// -------------------------------------------------------------------

class CloseSessionRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.CloseSessionRequest) */ {
 public:
  inline CloseSessionRequest() : CloseSessionRequest(nullptr) {}
  ~CloseSessionRequest() override;
  explicit PROTOBUF_CONSTEXPR CloseSessionRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CloseSessionRequest(const CloseSessionRequest& from);
  CloseSessionRequest(CloseSessionRequest&& from) noexcept
    : CloseSessionRequest() {
    *this = ::std::move(from);
  }

  inline CloseSessionRequest& operator=(const CloseSessionRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline CloseSessionRequest& operator=(CloseSessionRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CloseSessionRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const CloseSessionRequest* internal_default_instance() {
    return reinterpret_cast<const CloseSessionRequest*>(
               &_CloseSessionRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(CloseSessionRequest& a, CloseSessionRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(CloseSessionRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CloseSessionRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CloseSessionRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CloseSessionRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CloseSessionRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CloseSessionRequest& from) {
    CloseSessionRequest::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CloseSessionRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CloseSessionRequest";
  }
  protected:
  explicit CloseSessionRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSessionHandleFieldNumber = 1,
  };
  // string session_handle = 1;
  void clear_session_handle();
  const std::string& session_handle() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_session_handle(ArgT0&& arg0, ArgT... args);
  std::string* mutable_session_handle();
  PROTOBUF_NODISCARD std::string* release_session_handle();
  void set_allocated_session_handle(std::string* session_handle);
  private:
  const std::string& _internal_session_handle() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_session_handle(const std::string& value);
  std::string* _internal_mutable_session_handle();
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.CloseSessionRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr session_handle_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto;
};
// -------------------------------------------------------------------

class CloseSessionResponse final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:tensorflow.CloseSessionResponse) */ {
 public:
  inline CloseSessionResponse() : CloseSessionResponse(nullptr) {}
  explicit PROTOBUF_CONSTEXPR CloseSessionResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CloseSessionResponse(const CloseSessionResponse& from);
  CloseSessionResponse(CloseSessionResponse&& from) noexcept
    : CloseSessionResponse() {
    *this = ::std::move(from);
  }

  inline CloseSessionResponse& operator=(const CloseSessionResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline CloseSessionResponse& operator=(CloseSessionResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CloseSessionResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const CloseSessionResponse* internal_default_instance() {
    return reinterpret_cast<const CloseSessionResponse*>(
               &_CloseSessionResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(CloseSessionResponse& a, CloseSessionResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(CloseSessionResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CloseSessionResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CloseSessionResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CloseSessionResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const CloseSessionResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(*this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const CloseSessionResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(*this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CloseSessionResponse";
  }
  protected:
  explicit CloseSessionResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.CloseSessionResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
  };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto;
};
// -------------------------------------------------------------------

class ResetRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.ResetRequest) */ {
 public:
  inline ResetRequest() : ResetRequest(nullptr) {}
  ~ResetRequest() override;
  explicit PROTOBUF_CONSTEXPR ResetRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ResetRequest(const ResetRequest& from);
  ResetRequest(ResetRequest&& from) noexcept
    : ResetRequest() {
    *this = ::std::move(from);
  }

  inline ResetRequest& operator=(const ResetRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline ResetRequest& operator=(ResetRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ResetRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const ResetRequest* internal_default_instance() {
    return reinterpret_cast<const ResetRequest*>(
               &_ResetRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  friend void swap(ResetRequest& a, ResetRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(ResetRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ResetRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ResetRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ResetRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ResetRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ResetRequest& from) {
    ResetRequest::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ResetRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ResetRequest";
  }
  protected:
  explicit ResetRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kContainerFieldNumber = 1,
    kDeviceFiltersFieldNumber = 2,
  };
  // repeated string container = 1;
  int container_size() const;
  private:
  int _internal_container_size() const;
  public:
  void clear_container();
  const std::string& container(int index) const;
  std::string* mutable_container(int index);
  void set_container(int index, const std::string& value);
  void set_container(int index, std::string&& value);
  void set_container(int index, const char* value);
  void set_container(int index, const char* value, size_t size);
  std::string* add_container();
  void add_container(const std::string& value);
  void add_container(std::string&& value);
  void add_container(const char* value);
  void add_container(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& container() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_container();
  private:
  const std::string& _internal_container(int index) const;
  std::string* _internal_add_container();
  public:

  // repeated string device_filters = 2;
  int device_filters_size() const;
  private:
  int _internal_device_filters_size() const;
  public:
  void clear_device_filters();
  const std::string& device_filters(int index) const;
  std::string* mutable_device_filters(int index);
  void set_device_filters(int index, const std::string& value);
  void set_device_filters(int index, std::string&& value);
  void set_device_filters(int index, const char* value);
  void set_device_filters(int index, const char* value, size_t size);
  std::string* add_device_filters();
  void add_device_filters(const std::string& value);
  void add_device_filters(std::string&& value);
  void add_device_filters(const char* value);
  void add_device_filters(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& device_filters() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_device_filters();
  private:
  const std::string& _internal_device_filters(int index) const;
  std::string* _internal_add_device_filters();
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.ResetRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> container_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> device_filters_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto;
};
// -------------------------------------------------------------------

class ResetResponse final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:tensorflow.ResetResponse) */ {
 public:
  inline ResetResponse() : ResetResponse(nullptr) {}
  explicit PROTOBUF_CONSTEXPR ResetResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ResetResponse(const ResetResponse& from);
  ResetResponse(ResetResponse&& from) noexcept
    : ResetResponse() {
    *this = ::std::move(from);
  }

  inline ResetResponse& operator=(const ResetResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline ResetResponse& operator=(ResetResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ResetResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const ResetResponse* internal_default_instance() {
    return reinterpret_cast<const ResetResponse*>(
               &_ResetResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  friend void swap(ResetResponse& a, ResetResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(ResetResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ResetResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ResetResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ResetResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const ResetResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(*this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const ResetResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(*this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ResetResponse";
  }
  protected:
  explicit ResetResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.ResetResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
  };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto;
};
// -------------------------------------------------------------------

class ListDevicesRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.ListDevicesRequest) */ {
 public:
  inline ListDevicesRequest() : ListDevicesRequest(nullptr) {}
  ~ListDevicesRequest() override;
  explicit PROTOBUF_CONSTEXPR ListDevicesRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ListDevicesRequest(const ListDevicesRequest& from);
  ListDevicesRequest(ListDevicesRequest&& from) noexcept
    : ListDevicesRequest() {
    *this = ::std::move(from);
  }

  inline ListDevicesRequest& operator=(const ListDevicesRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline ListDevicesRequest& operator=(ListDevicesRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ListDevicesRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const ListDevicesRequest* internal_default_instance() {
    return reinterpret_cast<const ListDevicesRequest*>(
               &_ListDevicesRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    12;

  friend void swap(ListDevicesRequest& a, ListDevicesRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(ListDevicesRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ListDevicesRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ListDevicesRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ListDevicesRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ListDevicesRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ListDevicesRequest& from) {
    ListDevicesRequest::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ListDevicesRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ListDevicesRequest";
  }
  protected:
  explicit ListDevicesRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSessionHandleFieldNumber = 1,
  };
  // string session_handle = 1;
  void clear_session_handle();
  const std::string& session_handle() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_session_handle(ArgT0&& arg0, ArgT... args);
  std::string* mutable_session_handle();
  PROTOBUF_NODISCARD std::string* release_session_handle();
  void set_allocated_session_handle(std::string* session_handle);
  private:
  const std::string& _internal_session_handle() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_session_handle(const std::string& value);
  std::string* _internal_mutable_session_handle();
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.ListDevicesRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr session_handle_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto;
};
// -------------------------------------------------------------------

class ListDevicesResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.ListDevicesResponse) */ {
 public:
  inline ListDevicesResponse() : ListDevicesResponse(nullptr) {}
  ~ListDevicesResponse() override;
  explicit PROTOBUF_CONSTEXPR ListDevicesResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ListDevicesResponse(const ListDevicesResponse& from);
  ListDevicesResponse(ListDevicesResponse&& from) noexcept
    : ListDevicesResponse() {
    *this = ::std::move(from);
  }

  inline ListDevicesResponse& operator=(const ListDevicesResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline ListDevicesResponse& operator=(ListDevicesResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ListDevicesResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const ListDevicesResponse* internal_default_instance() {
    return reinterpret_cast<const ListDevicesResponse*>(
               &_ListDevicesResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    13;

  friend void swap(ListDevicesResponse& a, ListDevicesResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(ListDevicesResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ListDevicesResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ListDevicesResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ListDevicesResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ListDevicesResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ListDevicesResponse& from) {
    ListDevicesResponse::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ListDevicesResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ListDevicesResponse";
  }
  protected:
  explicit ListDevicesResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLocalDeviceFieldNumber = 1,
    kRemoteDeviceFieldNumber = 2,
  };
  // repeated .tensorflow.DeviceAttributes local_device = 1;
  int local_device_size() const;
  private:
  int _internal_local_device_size() const;
  public:
  void clear_local_device();
  ::tensorflow::DeviceAttributes* mutable_local_device(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >*
      mutable_local_device();
  private:
  const ::tensorflow::DeviceAttributes& _internal_local_device(int index) const;
  ::tensorflow::DeviceAttributes* _internal_add_local_device();
  public:
  const ::tensorflow::DeviceAttributes& local_device(int index) const;
  ::tensorflow::DeviceAttributes* add_local_device();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >&
      local_device() const;

  // repeated .tensorflow.DeviceAttributes remote_device = 2;
  int remote_device_size() const;
  private:
  int _internal_remote_device_size() const;
  public:
  void clear_remote_device();
  ::tensorflow::DeviceAttributes* mutable_remote_device(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >*
      mutable_remote_device();
  private:
  const ::tensorflow::DeviceAttributes& _internal_remote_device(int index) const;
  ::tensorflow::DeviceAttributes* _internal_add_remote_device();
  public:
  const ::tensorflow::DeviceAttributes& remote_device(int index) const;
  ::tensorflow::DeviceAttributes* add_remote_device();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >&
      remote_device() const;

  // @@protoc_insertion_point(class_scope:tensorflow.ListDevicesResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes > local_device_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes > remote_device_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto;
};
// -------------------------------------------------------------------

class MakeCallableRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.MakeCallableRequest) */ {
 public:
  inline MakeCallableRequest() : MakeCallableRequest(nullptr) {}
  ~MakeCallableRequest() override;
  explicit PROTOBUF_CONSTEXPR MakeCallableRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MakeCallableRequest(const MakeCallableRequest& from);
  MakeCallableRequest(MakeCallableRequest&& from) noexcept
    : MakeCallableRequest() {
    *this = ::std::move(from);
  }

  inline MakeCallableRequest& operator=(const MakeCallableRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline MakeCallableRequest& operator=(MakeCallableRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MakeCallableRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const MakeCallableRequest* internal_default_instance() {
    return reinterpret_cast<const MakeCallableRequest*>(
               &_MakeCallableRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    14;

  friend void swap(MakeCallableRequest& a, MakeCallableRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(MakeCallableRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MakeCallableRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MakeCallableRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MakeCallableRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MakeCallableRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const MakeCallableRequest& from) {
    MakeCallableRequest::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MakeCallableRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.MakeCallableRequest";
  }
  protected:
  explicit MakeCallableRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSessionHandleFieldNumber = 1,
    kOptionsFieldNumber = 2,
    kRequestIdFieldNumber = 3,
  };
  // string session_handle = 1;
  void clear_session_handle();
  const std::string& session_handle() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_session_handle(ArgT0&& arg0, ArgT... args);
  std::string* mutable_session_handle();
  PROTOBUF_NODISCARD std::string* release_session_handle();
  void set_allocated_session_handle(std::string* session_handle);
  private:
  const std::string& _internal_session_handle() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_session_handle(const std::string& value);
  std::string* _internal_mutable_session_handle();
  public:

  // .tensorflow.CallableOptions options = 2;
  bool has_options() const;
  private:
  bool _internal_has_options() const;
  public:
  void clear_options();
  const ::tensorflow::CallableOptions& options() const;
  PROTOBUF_NODISCARD ::tensorflow::CallableOptions* release_options();
  ::tensorflow::CallableOptions* mutable_options();
  void set_allocated_options(::tensorflow::CallableOptions* options);
  private:
  const ::tensorflow::CallableOptions& _internal_options() const;
  ::tensorflow::CallableOptions* _internal_mutable_options();
  public:
  void unsafe_arena_set_allocated_options(
      ::tensorflow::CallableOptions* options);
  ::tensorflow::CallableOptions* unsafe_arena_release_options();

  // int64 request_id = 3;
  void clear_request_id();
  int64_t request_id() const;
  void set_request_id(int64_t value);
  private:
  int64_t _internal_request_id() const;
  void _internal_set_request_id(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.MakeCallableRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr session_handle_;
    ::tensorflow::CallableOptions* options_;
    int64_t request_id_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto;
};
// -------------------------------------------------------------------

class MakeCallableResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.MakeCallableResponse) */ {
 public:
  inline MakeCallableResponse() : MakeCallableResponse(nullptr) {}
  ~MakeCallableResponse() override;
  explicit PROTOBUF_CONSTEXPR MakeCallableResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MakeCallableResponse(const MakeCallableResponse& from);
  MakeCallableResponse(MakeCallableResponse&& from) noexcept
    : MakeCallableResponse() {
    *this = ::std::move(from);
  }

  inline MakeCallableResponse& operator=(const MakeCallableResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline MakeCallableResponse& operator=(MakeCallableResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MakeCallableResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const MakeCallableResponse* internal_default_instance() {
    return reinterpret_cast<const MakeCallableResponse*>(
               &_MakeCallableResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    15;

  friend void swap(MakeCallableResponse& a, MakeCallableResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(MakeCallableResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MakeCallableResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MakeCallableResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MakeCallableResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MakeCallableResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const MakeCallableResponse& from) {
    MakeCallableResponse::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MakeCallableResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.MakeCallableResponse";
  }
  protected:
  explicit MakeCallableResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kHandleFieldNumber = 1,
  };
  // int64 handle = 1;
  void clear_handle();
  int64_t handle() const;
  void set_handle(int64_t value);
  private:
  int64_t _internal_handle() const;
  void _internal_set_handle(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.MakeCallableResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int64_t handle_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto;
};
// -------------------------------------------------------------------

class RunCallableRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.RunCallableRequest) */ {
 public:
  inline RunCallableRequest() : RunCallableRequest(nullptr) {}
  ~RunCallableRequest() override;
  explicit PROTOBUF_CONSTEXPR RunCallableRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RunCallableRequest(const RunCallableRequest& from);
  RunCallableRequest(RunCallableRequest&& from) noexcept
    : RunCallableRequest() {
    *this = ::std::move(from);
  }

  inline RunCallableRequest& operator=(const RunCallableRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline RunCallableRequest& operator=(RunCallableRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RunCallableRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const RunCallableRequest* internal_default_instance() {
    return reinterpret_cast<const RunCallableRequest*>(
               &_RunCallableRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    16;

  friend void swap(RunCallableRequest& a, RunCallableRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(RunCallableRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RunCallableRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RunCallableRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RunCallableRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RunCallableRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const RunCallableRequest& from) {
    RunCallableRequest::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RunCallableRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.RunCallableRequest";
  }
  protected:
  explicit RunCallableRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFeedFieldNumber = 3,
    kSessionHandleFieldNumber = 1,
    kHandleFieldNumber = 2,
    kRequestIdFieldNumber = 4,
  };
  // repeated .tensorflow.TensorProto feed = 3;
  int feed_size() const;
  private:
  int _internal_feed_size() const;
  public:
  void clear_feed();
  ::tensorflow::TensorProto* mutable_feed(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >*
      mutable_feed();
  private:
  const ::tensorflow::TensorProto& _internal_feed(int index) const;
  ::tensorflow::TensorProto* _internal_add_feed();
  public:
  const ::tensorflow::TensorProto& feed(int index) const;
  ::tensorflow::TensorProto* add_feed();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >&
      feed() const;

  // string session_handle = 1;
  void clear_session_handle();
  const std::string& session_handle() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_session_handle(ArgT0&& arg0, ArgT... args);
  std::string* mutable_session_handle();
  PROTOBUF_NODISCARD std::string* release_session_handle();
  void set_allocated_session_handle(std::string* session_handle);
  private:
  const std::string& _internal_session_handle() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_session_handle(const std::string& value);
  std::string* _internal_mutable_session_handle();
  public:

  // int64 handle = 2;
  void clear_handle();
  int64_t handle() const;
  void set_handle(int64_t value);
  private:
  int64_t _internal_handle() const;
  void _internal_set_handle(int64_t value);
  public:

  // int64 request_id = 4;
  void clear_request_id();
  int64_t request_id() const;
  void set_request_id(int64_t value);
  private:
  int64_t _internal_request_id() const;
  void _internal_set_request_id(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.RunCallableRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto > feed_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr session_handle_;
    int64_t handle_;
    int64_t request_id_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto;
};
// -------------------------------------------------------------------

class RunCallableResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.RunCallableResponse) */ {
 public:
  inline RunCallableResponse() : RunCallableResponse(nullptr) {}
  ~RunCallableResponse() override;
  explicit PROTOBUF_CONSTEXPR RunCallableResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RunCallableResponse(const RunCallableResponse& from);
  RunCallableResponse(RunCallableResponse&& from) noexcept
    : RunCallableResponse() {
    *this = ::std::move(from);
  }

  inline RunCallableResponse& operator=(const RunCallableResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline RunCallableResponse& operator=(RunCallableResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RunCallableResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const RunCallableResponse* internal_default_instance() {
    return reinterpret_cast<const RunCallableResponse*>(
               &_RunCallableResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    17;

  friend void swap(RunCallableResponse& a, RunCallableResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(RunCallableResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RunCallableResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RunCallableResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RunCallableResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RunCallableResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const RunCallableResponse& from) {
    RunCallableResponse::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RunCallableResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.RunCallableResponse";
  }
  protected:
  explicit RunCallableResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFetchFieldNumber = 1,
    kMetadataFieldNumber = 2,
  };
  // repeated .tensorflow.TensorProto fetch = 1;
  int fetch_size() const;
  private:
  int _internal_fetch_size() const;
  public:
  void clear_fetch();
  ::tensorflow::TensorProto* mutable_fetch(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >*
      mutable_fetch();
  private:
  const ::tensorflow::TensorProto& _internal_fetch(int index) const;
  ::tensorflow::TensorProto* _internal_add_fetch();
  public:
  const ::tensorflow::TensorProto& fetch(int index) const;
  ::tensorflow::TensorProto* add_fetch();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >&
      fetch() const;

  // .tensorflow.RunMetadata metadata = 2;
  bool has_metadata() const;
  private:
  bool _internal_has_metadata() const;
  public:
  void clear_metadata();
  const ::tensorflow::RunMetadata& metadata() const;
  PROTOBUF_NODISCARD ::tensorflow::RunMetadata* release_metadata();
  ::tensorflow::RunMetadata* mutable_metadata();
  void set_allocated_metadata(::tensorflow::RunMetadata* metadata);
  private:
  const ::tensorflow::RunMetadata& _internal_metadata() const;
  ::tensorflow::RunMetadata* _internal_mutable_metadata();
  public:
  void unsafe_arena_set_allocated_metadata(
      ::tensorflow::RunMetadata* metadata);
  ::tensorflow::RunMetadata* unsafe_arena_release_metadata();

  // @@protoc_insertion_point(class_scope:tensorflow.RunCallableResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto > fetch_;
    ::tensorflow::RunMetadata* metadata_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto;
};
// -------------------------------------------------------------------

class ReleaseCallableRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.ReleaseCallableRequest) */ {
 public:
  inline ReleaseCallableRequest() : ReleaseCallableRequest(nullptr) {}
  ~ReleaseCallableRequest() override;
  explicit PROTOBUF_CONSTEXPR ReleaseCallableRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ReleaseCallableRequest(const ReleaseCallableRequest& from);
  ReleaseCallableRequest(ReleaseCallableRequest&& from) noexcept
    : ReleaseCallableRequest() {
    *this = ::std::move(from);
  }

  inline ReleaseCallableRequest& operator=(const ReleaseCallableRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline ReleaseCallableRequest& operator=(ReleaseCallableRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ReleaseCallableRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const ReleaseCallableRequest* internal_default_instance() {
    return reinterpret_cast<const ReleaseCallableRequest*>(
               &_ReleaseCallableRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    18;

  friend void swap(ReleaseCallableRequest& a, ReleaseCallableRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(ReleaseCallableRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ReleaseCallableRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ReleaseCallableRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ReleaseCallableRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ReleaseCallableRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ReleaseCallableRequest& from) {
    ReleaseCallableRequest::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ReleaseCallableRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ReleaseCallableRequest";
  }
  protected:
  explicit ReleaseCallableRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSessionHandleFieldNumber = 1,
    kHandleFieldNumber = 2,
  };
  // string session_handle = 1;
  void clear_session_handle();
  const std::string& session_handle() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_session_handle(ArgT0&& arg0, ArgT... args);
  std::string* mutable_session_handle();
  PROTOBUF_NODISCARD std::string* release_session_handle();
  void set_allocated_session_handle(std::string* session_handle);
  private:
  const std::string& _internal_session_handle() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_session_handle(const std::string& value);
  std::string* _internal_mutable_session_handle();
  public:

  // int64 handle = 2;
  void clear_handle();
  int64_t handle() const;
  void set_handle(int64_t value);
  private:
  int64_t _internal_handle() const;
  void _internal_set_handle(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.ReleaseCallableRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr session_handle_;
    int64_t handle_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto;
};
// -------------------------------------------------------------------

class ReleaseCallableResponse final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:tensorflow.ReleaseCallableResponse) */ {
 public:
  inline ReleaseCallableResponse() : ReleaseCallableResponse(nullptr) {}
  explicit PROTOBUF_CONSTEXPR ReleaseCallableResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ReleaseCallableResponse(const ReleaseCallableResponse& from);
  ReleaseCallableResponse(ReleaseCallableResponse&& from) noexcept
    : ReleaseCallableResponse() {
    *this = ::std::move(from);
  }

  inline ReleaseCallableResponse& operator=(const ReleaseCallableResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline ReleaseCallableResponse& operator=(ReleaseCallableResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ReleaseCallableResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const ReleaseCallableResponse* internal_default_instance() {
    return reinterpret_cast<const ReleaseCallableResponse*>(
               &_ReleaseCallableResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    19;

  friend void swap(ReleaseCallableResponse& a, ReleaseCallableResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(ReleaseCallableResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ReleaseCallableResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ReleaseCallableResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ReleaseCallableResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const ReleaseCallableResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(*this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const ReleaseCallableResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(*this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ReleaseCallableResponse";
  }
  protected:
  explicit ReleaseCallableResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.ReleaseCallableResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
  };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// CreateSessionRequest

// .tensorflow.GraphDef graph_def = 1;
inline bool CreateSessionRequest::_internal_has_graph_def() const {
  return this != internal_default_instance() && _impl_.graph_def_ != nullptr;
}
inline bool CreateSessionRequest::has_graph_def() const {
  return _internal_has_graph_def();
}
inline const ::tensorflow::GraphDef& CreateSessionRequest::_internal_graph_def() const {
  const ::tensorflow::GraphDef* p = _impl_.graph_def_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::GraphDef&>(
      ::tensorflow::_GraphDef_default_instance_);
}
inline const ::tensorflow::GraphDef& CreateSessionRequest::graph_def() const {
  // @@protoc_insertion_point(field_get:tensorflow.CreateSessionRequest.graph_def)
  return _internal_graph_def();
}
inline void CreateSessionRequest::unsafe_arena_set_allocated_graph_def(
    ::tensorflow::GraphDef* graph_def) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.graph_def_);
  }
  _impl_.graph_def_ = graph_def;
  if (graph_def) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CreateSessionRequest.graph_def)
}
inline ::tensorflow::GraphDef* CreateSessionRequest::release_graph_def() {
  
  ::tensorflow::GraphDef* temp = _impl_.graph_def_;
  _impl_.graph_def_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::GraphDef* CreateSessionRequest::unsafe_arena_release_graph_def() {
  // @@protoc_insertion_point(field_release:tensorflow.CreateSessionRequest.graph_def)
  
  ::tensorflow::GraphDef* temp = _impl_.graph_def_;
  _impl_.graph_def_ = nullptr;
  return temp;
}
inline ::tensorflow::GraphDef* CreateSessionRequest::_internal_mutable_graph_def() {
  
  if (_impl_.graph_def_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::GraphDef>(GetArenaForAllocation());
    _impl_.graph_def_ = p;
  }
  return _impl_.graph_def_;
}
inline ::tensorflow::GraphDef* CreateSessionRequest::mutable_graph_def() {
  ::tensorflow::GraphDef* _msg = _internal_mutable_graph_def();
  // @@protoc_insertion_point(field_mutable:tensorflow.CreateSessionRequest.graph_def)
  return _msg;
}
inline void CreateSessionRequest::set_allocated_graph_def(::tensorflow::GraphDef* graph_def) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.graph_def_);
  }
  if (graph_def) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(graph_def));
    if (message_arena != submessage_arena) {
      graph_def = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, graph_def, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.graph_def_ = graph_def;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CreateSessionRequest.graph_def)
}

// .tensorflow.ConfigProto config = 2;
inline bool CreateSessionRequest::_internal_has_config() const {
  return this != internal_default_instance() && _impl_.config_ != nullptr;
}
inline bool CreateSessionRequest::has_config() const {
  return _internal_has_config();
}
inline const ::tensorflow::ConfigProto& CreateSessionRequest::_internal_config() const {
  const ::tensorflow::ConfigProto* p = _impl_.config_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::ConfigProto&>(
      ::tensorflow::_ConfigProto_default_instance_);
}
inline const ::tensorflow::ConfigProto& CreateSessionRequest::config() const {
  // @@protoc_insertion_point(field_get:tensorflow.CreateSessionRequest.config)
  return _internal_config();
}
inline void CreateSessionRequest::unsafe_arena_set_allocated_config(
    ::tensorflow::ConfigProto* config) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.config_);
  }
  _impl_.config_ = config;
  if (config) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CreateSessionRequest.config)
}
inline ::tensorflow::ConfigProto* CreateSessionRequest::release_config() {
  
  ::tensorflow::ConfigProto* temp = _impl_.config_;
  _impl_.config_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::ConfigProto* CreateSessionRequest::unsafe_arena_release_config() {
  // @@protoc_insertion_point(field_release:tensorflow.CreateSessionRequest.config)
  
  ::tensorflow::ConfigProto* temp = _impl_.config_;
  _impl_.config_ = nullptr;
  return temp;
}
inline ::tensorflow::ConfigProto* CreateSessionRequest::_internal_mutable_config() {
  
  if (_impl_.config_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::ConfigProto>(GetArenaForAllocation());
    _impl_.config_ = p;
  }
  return _impl_.config_;
}
inline ::tensorflow::ConfigProto* CreateSessionRequest::mutable_config() {
  ::tensorflow::ConfigProto* _msg = _internal_mutable_config();
  // @@protoc_insertion_point(field_mutable:tensorflow.CreateSessionRequest.config)
  return _msg;
}
inline void CreateSessionRequest::set_allocated_config(::tensorflow::ConfigProto* config) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.config_);
  }
  if (config) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(config));
    if (message_arena != submessage_arena) {
      config = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, config, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.config_ = config;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CreateSessionRequest.config)
}

// string target = 3;
inline void CreateSessionRequest::clear_target() {
  _impl_.target_.ClearToEmpty();
}
inline const std::string& CreateSessionRequest::target() const {
  // @@protoc_insertion_point(field_get:tensorflow.CreateSessionRequest.target)
  return _internal_target();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CreateSessionRequest::set_target(ArgT0&& arg0, ArgT... args) {
 
 _impl_.target_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.CreateSessionRequest.target)
}
inline std::string* CreateSessionRequest::mutable_target() {
  std::string* _s = _internal_mutable_target();
  // @@protoc_insertion_point(field_mutable:tensorflow.CreateSessionRequest.target)
  return _s;
}
inline const std::string& CreateSessionRequest::_internal_target() const {
  return _impl_.target_.Get();
}
inline void CreateSessionRequest::_internal_set_target(const std::string& value) {
  
  _impl_.target_.Set(value, GetArenaForAllocation());
}
inline std::string* CreateSessionRequest::_internal_mutable_target() {
  
  return _impl_.target_.Mutable(GetArenaForAllocation());
}
inline std::string* CreateSessionRequest::release_target() {
  // @@protoc_insertion_point(field_release:tensorflow.CreateSessionRequest.target)
  return _impl_.target_.Release();
}
inline void CreateSessionRequest::set_allocated_target(std::string* target) {
  if (target != nullptr) {
    
  } else {
    
  }
  _impl_.target_.SetAllocated(target, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.target_.IsDefault()) {
    _impl_.target_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CreateSessionRequest.target)
}

// -------------------------------------------------------------------

// CreateSessionResponse

// string session_handle = 1;
inline void CreateSessionResponse::clear_session_handle() {
  _impl_.session_handle_.ClearToEmpty();
}
inline const std::string& CreateSessionResponse::session_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.CreateSessionResponse.session_handle)
  return _internal_session_handle();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CreateSessionResponse::set_session_handle(ArgT0&& arg0, ArgT... args) {
 
 _impl_.session_handle_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.CreateSessionResponse.session_handle)
}
inline std::string* CreateSessionResponse::mutable_session_handle() {
  std::string* _s = _internal_mutable_session_handle();
  // @@protoc_insertion_point(field_mutable:tensorflow.CreateSessionResponse.session_handle)
  return _s;
}
inline const std::string& CreateSessionResponse::_internal_session_handle() const {
  return _impl_.session_handle_.Get();
}
inline void CreateSessionResponse::_internal_set_session_handle(const std::string& value) {
  
  _impl_.session_handle_.Set(value, GetArenaForAllocation());
}
inline std::string* CreateSessionResponse::_internal_mutable_session_handle() {
  
  return _impl_.session_handle_.Mutable(GetArenaForAllocation());
}
inline std::string* CreateSessionResponse::release_session_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.CreateSessionResponse.session_handle)
  return _impl_.session_handle_.Release();
}
inline void CreateSessionResponse::set_allocated_session_handle(std::string* session_handle) {
  if (session_handle != nullptr) {
    
  } else {
    
  }
  _impl_.session_handle_.SetAllocated(session_handle, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.session_handle_.IsDefault()) {
    _impl_.session_handle_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CreateSessionResponse.session_handle)
}

// int64 graph_version = 2;
inline void CreateSessionResponse::clear_graph_version() {
  _impl_.graph_version_ = int64_t{0};
}
inline int64_t CreateSessionResponse::_internal_graph_version() const {
  return _impl_.graph_version_;
}
inline int64_t CreateSessionResponse::graph_version() const {
  // @@protoc_insertion_point(field_get:tensorflow.CreateSessionResponse.graph_version)
  return _internal_graph_version();
}
inline void CreateSessionResponse::_internal_set_graph_version(int64_t value) {
  
  _impl_.graph_version_ = value;
}
inline void CreateSessionResponse::set_graph_version(int64_t value) {
  _internal_set_graph_version(value);
  // @@protoc_insertion_point(field_set:tensorflow.CreateSessionResponse.graph_version)
}

// -------------------------------------------------------------------

// ExtendSessionRequest

// string session_handle = 1;
inline void ExtendSessionRequest::clear_session_handle() {
  _impl_.session_handle_.ClearToEmpty();
}
inline const std::string& ExtendSessionRequest::session_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.ExtendSessionRequest.session_handle)
  return _internal_session_handle();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ExtendSessionRequest::set_session_handle(ArgT0&& arg0, ArgT... args) {
 
 _impl_.session_handle_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.ExtendSessionRequest.session_handle)
}
inline std::string* ExtendSessionRequest::mutable_session_handle() {
  std::string* _s = _internal_mutable_session_handle();
  // @@protoc_insertion_point(field_mutable:tensorflow.ExtendSessionRequest.session_handle)
  return _s;
}
inline const std::string& ExtendSessionRequest::_internal_session_handle() const {
  return _impl_.session_handle_.Get();
}
inline void ExtendSessionRequest::_internal_set_session_handle(const std::string& value) {
  
  _impl_.session_handle_.Set(value, GetArenaForAllocation());
}
inline std::string* ExtendSessionRequest::_internal_mutable_session_handle() {
  
  return _impl_.session_handle_.Mutable(GetArenaForAllocation());
}
inline std::string* ExtendSessionRequest::release_session_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.ExtendSessionRequest.session_handle)
  return _impl_.session_handle_.Release();
}
inline void ExtendSessionRequest::set_allocated_session_handle(std::string* session_handle) {
  if (session_handle != nullptr) {
    
  } else {
    
  }
  _impl_.session_handle_.SetAllocated(session_handle, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.session_handle_.IsDefault()) {
    _impl_.session_handle_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ExtendSessionRequest.session_handle)
}

// .tensorflow.GraphDef graph_def = 2;
inline bool ExtendSessionRequest::_internal_has_graph_def() const {
  return this != internal_default_instance() && _impl_.graph_def_ != nullptr;
}
inline bool ExtendSessionRequest::has_graph_def() const {
  return _internal_has_graph_def();
}
inline const ::tensorflow::GraphDef& ExtendSessionRequest::_internal_graph_def() const {
  const ::tensorflow::GraphDef* p = _impl_.graph_def_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::GraphDef&>(
      ::tensorflow::_GraphDef_default_instance_);
}
inline const ::tensorflow::GraphDef& ExtendSessionRequest::graph_def() const {
  // @@protoc_insertion_point(field_get:tensorflow.ExtendSessionRequest.graph_def)
  return _internal_graph_def();
}
inline void ExtendSessionRequest::unsafe_arena_set_allocated_graph_def(
    ::tensorflow::GraphDef* graph_def) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.graph_def_);
  }
  _impl_.graph_def_ = graph_def;
  if (graph_def) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ExtendSessionRequest.graph_def)
}
inline ::tensorflow::GraphDef* ExtendSessionRequest::release_graph_def() {
  
  ::tensorflow::GraphDef* temp = _impl_.graph_def_;
  _impl_.graph_def_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::GraphDef* ExtendSessionRequest::unsafe_arena_release_graph_def() {
  // @@protoc_insertion_point(field_release:tensorflow.ExtendSessionRequest.graph_def)
  
  ::tensorflow::GraphDef* temp = _impl_.graph_def_;
  _impl_.graph_def_ = nullptr;
  return temp;
}
inline ::tensorflow::GraphDef* ExtendSessionRequest::_internal_mutable_graph_def() {
  
  if (_impl_.graph_def_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::GraphDef>(GetArenaForAllocation());
    _impl_.graph_def_ = p;
  }
  return _impl_.graph_def_;
}
inline ::tensorflow::GraphDef* ExtendSessionRequest::mutable_graph_def() {
  ::tensorflow::GraphDef* _msg = _internal_mutable_graph_def();
  // @@protoc_insertion_point(field_mutable:tensorflow.ExtendSessionRequest.graph_def)
  return _msg;
}
inline void ExtendSessionRequest::set_allocated_graph_def(::tensorflow::GraphDef* graph_def) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.graph_def_);
  }
  if (graph_def) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(graph_def));
    if (message_arena != submessage_arena) {
      graph_def = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, graph_def, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.graph_def_ = graph_def;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ExtendSessionRequest.graph_def)
}

// int64 current_graph_version = 3;
inline void ExtendSessionRequest::clear_current_graph_version() {
  _impl_.current_graph_version_ = int64_t{0};
}
inline int64_t ExtendSessionRequest::_internal_current_graph_version() const {
  return _impl_.current_graph_version_;
}
inline int64_t ExtendSessionRequest::current_graph_version() const {
  // @@protoc_insertion_point(field_get:tensorflow.ExtendSessionRequest.current_graph_version)
  return _internal_current_graph_version();
}
inline void ExtendSessionRequest::_internal_set_current_graph_version(int64_t value) {
  
  _impl_.current_graph_version_ = value;
}
inline void ExtendSessionRequest::set_current_graph_version(int64_t value) {
  _internal_set_current_graph_version(value);
  // @@protoc_insertion_point(field_set:tensorflow.ExtendSessionRequest.current_graph_version)
}

// -------------------------------------------------------------------

// ExtendSessionResponse

// int64 new_graph_version = 4;
inline void ExtendSessionResponse::clear_new_graph_version() {
  _impl_.new_graph_version_ = int64_t{0};
}
inline int64_t ExtendSessionResponse::_internal_new_graph_version() const {
  return _impl_.new_graph_version_;
}
inline int64_t ExtendSessionResponse::new_graph_version() const {
  // @@protoc_insertion_point(field_get:tensorflow.ExtendSessionResponse.new_graph_version)
  return _internal_new_graph_version();
}
inline void ExtendSessionResponse::_internal_set_new_graph_version(int64_t value) {
  
  _impl_.new_graph_version_ = value;
}
inline void ExtendSessionResponse::set_new_graph_version(int64_t value) {
  _internal_set_new_graph_version(value);
  // @@protoc_insertion_point(field_set:tensorflow.ExtendSessionResponse.new_graph_version)
}

// -------------------------------------------------------------------

// RunStepRequest

// string session_handle = 1;
inline void RunStepRequest::clear_session_handle() {
  _impl_.session_handle_.ClearToEmpty();
}
inline const std::string& RunStepRequest::session_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunStepRequest.session_handle)
  return _internal_session_handle();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RunStepRequest::set_session_handle(ArgT0&& arg0, ArgT... args) {
 
 _impl_.session_handle_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.RunStepRequest.session_handle)
}
inline std::string* RunStepRequest::mutable_session_handle() {
  std::string* _s = _internal_mutable_session_handle();
  // @@protoc_insertion_point(field_mutable:tensorflow.RunStepRequest.session_handle)
  return _s;
}
inline const std::string& RunStepRequest::_internal_session_handle() const {
  return _impl_.session_handle_.Get();
}
inline void RunStepRequest::_internal_set_session_handle(const std::string& value) {
  
  _impl_.session_handle_.Set(value, GetArenaForAllocation());
}
inline std::string* RunStepRequest::_internal_mutable_session_handle() {
  
  return _impl_.session_handle_.Mutable(GetArenaForAllocation());
}
inline std::string* RunStepRequest::release_session_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.RunStepRequest.session_handle)
  return _impl_.session_handle_.Release();
}
inline void RunStepRequest::set_allocated_session_handle(std::string* session_handle) {
  if (session_handle != nullptr) {
    
  } else {
    
  }
  _impl_.session_handle_.SetAllocated(session_handle, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.session_handle_.IsDefault()) {
    _impl_.session_handle_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RunStepRequest.session_handle)
}

// repeated .tensorflow.NamedTensorProto feed = 2;
inline int RunStepRequest::_internal_feed_size() const {
  return _impl_.feed_.size();
}
inline int RunStepRequest::feed_size() const {
  return _internal_feed_size();
}
inline ::tensorflow::NamedTensorProto* RunStepRequest::mutable_feed(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.RunStepRequest.feed)
  return _impl_.feed_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NamedTensorProto >*
RunStepRequest::mutable_feed() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.RunStepRequest.feed)
  return &_impl_.feed_;
}
inline const ::tensorflow::NamedTensorProto& RunStepRequest::_internal_feed(int index) const {
  return _impl_.feed_.Get(index);
}
inline const ::tensorflow::NamedTensorProto& RunStepRequest::feed(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.RunStepRequest.feed)
  return _internal_feed(index);
}
inline ::tensorflow::NamedTensorProto* RunStepRequest::_internal_add_feed() {
  return _impl_.feed_.Add();
}
inline ::tensorflow::NamedTensorProto* RunStepRequest::add_feed() {
  ::tensorflow::NamedTensorProto* _add = _internal_add_feed();
  // @@protoc_insertion_point(field_add:tensorflow.RunStepRequest.feed)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NamedTensorProto >&
RunStepRequest::feed() const {
  // @@protoc_insertion_point(field_list:tensorflow.RunStepRequest.feed)
  return _impl_.feed_;
}

// repeated string fetch = 3;
inline int RunStepRequest::_internal_fetch_size() const {
  return _impl_.fetch_.size();
}
inline int RunStepRequest::fetch_size() const {
  return _internal_fetch_size();
}
inline void RunStepRequest::clear_fetch() {
  _impl_.fetch_.Clear();
}
inline std::string* RunStepRequest::add_fetch() {
  std::string* _s = _internal_add_fetch();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.RunStepRequest.fetch)
  return _s;
}
inline const std::string& RunStepRequest::_internal_fetch(int index) const {
  return _impl_.fetch_.Get(index);
}
inline const std::string& RunStepRequest::fetch(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.RunStepRequest.fetch)
  return _internal_fetch(index);
}
inline std::string* RunStepRequest::mutable_fetch(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.RunStepRequest.fetch)
  return _impl_.fetch_.Mutable(index);
}
inline void RunStepRequest::set_fetch(int index, const std::string& value) {
  _impl_.fetch_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.RunStepRequest.fetch)
}
inline void RunStepRequest::set_fetch(int index, std::string&& value) {
  _impl_.fetch_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.RunStepRequest.fetch)
}
inline void RunStepRequest::set_fetch(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.fetch_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.RunStepRequest.fetch)
}
inline void RunStepRequest::set_fetch(int index, const char* value, size_t size) {
  _impl_.fetch_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.RunStepRequest.fetch)
}
inline std::string* RunStepRequest::_internal_add_fetch() {
  return _impl_.fetch_.Add();
}
inline void RunStepRequest::add_fetch(const std::string& value) {
  _impl_.fetch_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.RunStepRequest.fetch)
}
inline void RunStepRequest::add_fetch(std::string&& value) {
  _impl_.fetch_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.RunStepRequest.fetch)
}
inline void RunStepRequest::add_fetch(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.fetch_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.RunStepRequest.fetch)
}
inline void RunStepRequest::add_fetch(const char* value, size_t size) {
  _impl_.fetch_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.RunStepRequest.fetch)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
RunStepRequest::fetch() const {
  // @@protoc_insertion_point(field_list:tensorflow.RunStepRequest.fetch)
  return _impl_.fetch_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
RunStepRequest::mutable_fetch() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.RunStepRequest.fetch)
  return &_impl_.fetch_;
}

// repeated string target = 4;
inline int RunStepRequest::_internal_target_size() const {
  return _impl_.target_.size();
}
inline int RunStepRequest::target_size() const {
  return _internal_target_size();
}
inline void RunStepRequest::clear_target() {
  _impl_.target_.Clear();
}
inline std::string* RunStepRequest::add_target() {
  std::string* _s = _internal_add_target();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.RunStepRequest.target)
  return _s;
}
inline const std::string& RunStepRequest::_internal_target(int index) const {
  return _impl_.target_.Get(index);
}
inline const std::string& RunStepRequest::target(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.RunStepRequest.target)
  return _internal_target(index);
}
inline std::string* RunStepRequest::mutable_target(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.RunStepRequest.target)
  return _impl_.target_.Mutable(index);
}
inline void RunStepRequest::set_target(int index, const std::string& value) {
  _impl_.target_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.RunStepRequest.target)
}
inline void RunStepRequest::set_target(int index, std::string&& value) {
  _impl_.target_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.RunStepRequest.target)
}
inline void RunStepRequest::set_target(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.target_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.RunStepRequest.target)
}
inline void RunStepRequest::set_target(int index, const char* value, size_t size) {
  _impl_.target_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.RunStepRequest.target)
}
inline std::string* RunStepRequest::_internal_add_target() {
  return _impl_.target_.Add();
}
inline void RunStepRequest::add_target(const std::string& value) {
  _impl_.target_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.RunStepRequest.target)
}
inline void RunStepRequest::add_target(std::string&& value) {
  _impl_.target_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.RunStepRequest.target)
}
inline void RunStepRequest::add_target(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.target_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.RunStepRequest.target)
}
inline void RunStepRequest::add_target(const char* value, size_t size) {
  _impl_.target_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.RunStepRequest.target)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
RunStepRequest::target() const {
  // @@protoc_insertion_point(field_list:tensorflow.RunStepRequest.target)
  return _impl_.target_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
RunStepRequest::mutable_target() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.RunStepRequest.target)
  return &_impl_.target_;
}

// .tensorflow.RunOptions options = 5;
inline bool RunStepRequest::_internal_has_options() const {
  return this != internal_default_instance() && _impl_.options_ != nullptr;
}
inline bool RunStepRequest::has_options() const {
  return _internal_has_options();
}
inline const ::tensorflow::RunOptions& RunStepRequest::_internal_options() const {
  const ::tensorflow::RunOptions* p = _impl_.options_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::RunOptions&>(
      ::tensorflow::_RunOptions_default_instance_);
}
inline const ::tensorflow::RunOptions& RunStepRequest::options() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunStepRequest.options)
  return _internal_options();
}
inline void RunStepRequest::unsafe_arena_set_allocated_options(
    ::tensorflow::RunOptions* options) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.options_);
  }
  _impl_.options_ = options;
  if (options) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RunStepRequest.options)
}
inline ::tensorflow::RunOptions* RunStepRequest::release_options() {
  
  ::tensorflow::RunOptions* temp = _impl_.options_;
  _impl_.options_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::RunOptions* RunStepRequest::unsafe_arena_release_options() {
  // @@protoc_insertion_point(field_release:tensorflow.RunStepRequest.options)
  
  ::tensorflow::RunOptions* temp = _impl_.options_;
  _impl_.options_ = nullptr;
  return temp;
}
inline ::tensorflow::RunOptions* RunStepRequest::_internal_mutable_options() {
  
  if (_impl_.options_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::RunOptions>(GetArenaForAllocation());
    _impl_.options_ = p;
  }
  return _impl_.options_;
}
inline ::tensorflow::RunOptions* RunStepRequest::mutable_options() {
  ::tensorflow::RunOptions* _msg = _internal_mutable_options();
  // @@protoc_insertion_point(field_mutable:tensorflow.RunStepRequest.options)
  return _msg;
}
inline void RunStepRequest::set_allocated_options(::tensorflow::RunOptions* options) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.options_);
  }
  if (options) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(options));
    if (message_arena != submessage_arena) {
      options = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, options, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.options_ = options;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RunStepRequest.options)
}

// string partial_run_handle = 6;
inline void RunStepRequest::clear_partial_run_handle() {
  _impl_.partial_run_handle_.ClearToEmpty();
}
inline const std::string& RunStepRequest::partial_run_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunStepRequest.partial_run_handle)
  return _internal_partial_run_handle();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RunStepRequest::set_partial_run_handle(ArgT0&& arg0, ArgT... args) {
 
 _impl_.partial_run_handle_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.RunStepRequest.partial_run_handle)
}
inline std::string* RunStepRequest::mutable_partial_run_handle() {
  std::string* _s = _internal_mutable_partial_run_handle();
  // @@protoc_insertion_point(field_mutable:tensorflow.RunStepRequest.partial_run_handle)
  return _s;
}
inline const std::string& RunStepRequest::_internal_partial_run_handle() const {
  return _impl_.partial_run_handle_.Get();
}
inline void RunStepRequest::_internal_set_partial_run_handle(const std::string& value) {
  
  _impl_.partial_run_handle_.Set(value, GetArenaForAllocation());
}
inline std::string* RunStepRequest::_internal_mutable_partial_run_handle() {
  
  return _impl_.partial_run_handle_.Mutable(GetArenaForAllocation());
}
inline std::string* RunStepRequest::release_partial_run_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.RunStepRequest.partial_run_handle)
  return _impl_.partial_run_handle_.Release();
}
inline void RunStepRequest::set_allocated_partial_run_handle(std::string* partial_run_handle) {
  if (partial_run_handle != nullptr) {
    
  } else {
    
  }
  _impl_.partial_run_handle_.SetAllocated(partial_run_handle, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.partial_run_handle_.IsDefault()) {
    _impl_.partial_run_handle_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RunStepRequest.partial_run_handle)
}

// bool store_errors_in_response_body = 7;
inline void RunStepRequest::clear_store_errors_in_response_body() {
  _impl_.store_errors_in_response_body_ = false;
}
inline bool RunStepRequest::_internal_store_errors_in_response_body() const {
  return _impl_.store_errors_in_response_body_;
}
inline bool RunStepRequest::store_errors_in_response_body() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunStepRequest.store_errors_in_response_body)
  return _internal_store_errors_in_response_body();
}
inline void RunStepRequest::_internal_set_store_errors_in_response_body(bool value) {
  
  _impl_.store_errors_in_response_body_ = value;
}
inline void RunStepRequest::set_store_errors_in_response_body(bool value) {
  _internal_set_store_errors_in_response_body(value);
  // @@protoc_insertion_point(field_set:tensorflow.RunStepRequest.store_errors_in_response_body)
}

// int64 request_id = 8;
inline void RunStepRequest::clear_request_id() {
  _impl_.request_id_ = int64_t{0};
}
inline int64_t RunStepRequest::_internal_request_id() const {
  return _impl_.request_id_;
}
inline int64_t RunStepRequest::request_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunStepRequest.request_id)
  return _internal_request_id();
}
inline void RunStepRequest::_internal_set_request_id(int64_t value) {
  
  _impl_.request_id_ = value;
}
inline void RunStepRequest::set_request_id(int64_t value) {
  _internal_set_request_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.RunStepRequest.request_id)
}

// -------------------------------------------------------------------

// RunStepResponse

// repeated .tensorflow.NamedTensorProto tensor = 1;
inline int RunStepResponse::_internal_tensor_size() const {
  return _impl_.tensor_.size();
}
inline int RunStepResponse::tensor_size() const {
  return _internal_tensor_size();
}
inline ::tensorflow::NamedTensorProto* RunStepResponse::mutable_tensor(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.RunStepResponse.tensor)
  return _impl_.tensor_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NamedTensorProto >*
RunStepResponse::mutable_tensor() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.RunStepResponse.tensor)
  return &_impl_.tensor_;
}
inline const ::tensorflow::NamedTensorProto& RunStepResponse::_internal_tensor(int index) const {
  return _impl_.tensor_.Get(index);
}
inline const ::tensorflow::NamedTensorProto& RunStepResponse::tensor(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.RunStepResponse.tensor)
  return _internal_tensor(index);
}
inline ::tensorflow::NamedTensorProto* RunStepResponse::_internal_add_tensor() {
  return _impl_.tensor_.Add();
}
inline ::tensorflow::NamedTensorProto* RunStepResponse::add_tensor() {
  ::tensorflow::NamedTensorProto* _add = _internal_add_tensor();
  // @@protoc_insertion_point(field_add:tensorflow.RunStepResponse.tensor)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NamedTensorProto >&
RunStepResponse::tensor() const {
  // @@protoc_insertion_point(field_list:tensorflow.RunStepResponse.tensor)
  return _impl_.tensor_;
}

// .tensorflow.RunMetadata metadata = 2;
inline bool RunStepResponse::_internal_has_metadata() const {
  return this != internal_default_instance() && _impl_.metadata_ != nullptr;
}
inline bool RunStepResponse::has_metadata() const {
  return _internal_has_metadata();
}
inline const ::tensorflow::RunMetadata& RunStepResponse::_internal_metadata() const {
  const ::tensorflow::RunMetadata* p = _impl_.metadata_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::RunMetadata&>(
      ::tensorflow::_RunMetadata_default_instance_);
}
inline const ::tensorflow::RunMetadata& RunStepResponse::metadata() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunStepResponse.metadata)
  return _internal_metadata();
}
inline void RunStepResponse::unsafe_arena_set_allocated_metadata(
    ::tensorflow::RunMetadata* metadata) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.metadata_);
  }
  _impl_.metadata_ = metadata;
  if (metadata) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RunStepResponse.metadata)
}
inline ::tensorflow::RunMetadata* RunStepResponse::release_metadata() {
  
  ::tensorflow::RunMetadata* temp = _impl_.metadata_;
  _impl_.metadata_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::RunMetadata* RunStepResponse::unsafe_arena_release_metadata() {
  // @@protoc_insertion_point(field_release:tensorflow.RunStepResponse.metadata)
  
  ::tensorflow::RunMetadata* temp = _impl_.metadata_;
  _impl_.metadata_ = nullptr;
  return temp;
}
inline ::tensorflow::RunMetadata* RunStepResponse::_internal_mutable_metadata() {
  
  if (_impl_.metadata_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::RunMetadata>(GetArenaForAllocation());
    _impl_.metadata_ = p;
  }
  return _impl_.metadata_;
}
inline ::tensorflow::RunMetadata* RunStepResponse::mutable_metadata() {
  ::tensorflow::RunMetadata* _msg = _internal_mutable_metadata();
  // @@protoc_insertion_point(field_mutable:tensorflow.RunStepResponse.metadata)
  return _msg;
}
inline void RunStepResponse::set_allocated_metadata(::tensorflow::RunMetadata* metadata) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.metadata_);
  }
  if (metadata) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(metadata));
    if (message_arena != submessage_arena) {
      metadata = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, metadata, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.metadata_ = metadata;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RunStepResponse.metadata)
}

// .tensorflow.error.Code status_code = 3;
inline void RunStepResponse::clear_status_code() {
  _impl_.status_code_ = 0;
}
inline ::tensorflow::error::Code RunStepResponse::_internal_status_code() const {
  return static_cast< ::tensorflow::error::Code >(_impl_.status_code_);
}
inline ::tensorflow::error::Code RunStepResponse::status_code() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunStepResponse.status_code)
  return _internal_status_code();
}
inline void RunStepResponse::_internal_set_status_code(::tensorflow::error::Code value) {
  
  _impl_.status_code_ = value;
}
inline void RunStepResponse::set_status_code(::tensorflow::error::Code value) {
  _internal_set_status_code(value);
  // @@protoc_insertion_point(field_set:tensorflow.RunStepResponse.status_code)
}

// string status_error_message = 4;
inline void RunStepResponse::clear_status_error_message() {
  _impl_.status_error_message_.ClearToEmpty();
}
inline const std::string& RunStepResponse::status_error_message() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunStepResponse.status_error_message)
  return _internal_status_error_message();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RunStepResponse::set_status_error_message(ArgT0&& arg0, ArgT... args) {
 
 _impl_.status_error_message_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.RunStepResponse.status_error_message)
}
inline std::string* RunStepResponse::mutable_status_error_message() {
  std::string* _s = _internal_mutable_status_error_message();
  // @@protoc_insertion_point(field_mutable:tensorflow.RunStepResponse.status_error_message)
  return _s;
}
inline const std::string& RunStepResponse::_internal_status_error_message() const {
  return _impl_.status_error_message_.Get();
}
inline void RunStepResponse::_internal_set_status_error_message(const std::string& value) {
  
  _impl_.status_error_message_.Set(value, GetArenaForAllocation());
}
inline std::string* RunStepResponse::_internal_mutable_status_error_message() {
  
  return _impl_.status_error_message_.Mutable(GetArenaForAllocation());
}
inline std::string* RunStepResponse::release_status_error_message() {
  // @@protoc_insertion_point(field_release:tensorflow.RunStepResponse.status_error_message)
  return _impl_.status_error_message_.Release();
}
inline void RunStepResponse::set_allocated_status_error_message(std::string* status_error_message) {
  if (status_error_message != nullptr) {
    
  } else {
    
  }
  _impl_.status_error_message_.SetAllocated(status_error_message, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.status_error_message_.IsDefault()) {
    _impl_.status_error_message_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RunStepResponse.status_error_message)
}

// -------------------------------------------------------------------

// PartialRunSetupRequest

// string session_handle = 1;
inline void PartialRunSetupRequest::clear_session_handle() {
  _impl_.session_handle_.ClearToEmpty();
}
inline const std::string& PartialRunSetupRequest::session_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.PartialRunSetupRequest.session_handle)
  return _internal_session_handle();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void PartialRunSetupRequest::set_session_handle(ArgT0&& arg0, ArgT... args) {
 
 _impl_.session_handle_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.PartialRunSetupRequest.session_handle)
}
inline std::string* PartialRunSetupRequest::mutable_session_handle() {
  std::string* _s = _internal_mutable_session_handle();
  // @@protoc_insertion_point(field_mutable:tensorflow.PartialRunSetupRequest.session_handle)
  return _s;
}
inline const std::string& PartialRunSetupRequest::_internal_session_handle() const {
  return _impl_.session_handle_.Get();
}
inline void PartialRunSetupRequest::_internal_set_session_handle(const std::string& value) {
  
  _impl_.session_handle_.Set(value, GetArenaForAllocation());
}
inline std::string* PartialRunSetupRequest::_internal_mutable_session_handle() {
  
  return _impl_.session_handle_.Mutable(GetArenaForAllocation());
}
inline std::string* PartialRunSetupRequest::release_session_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.PartialRunSetupRequest.session_handle)
  return _impl_.session_handle_.Release();
}
inline void PartialRunSetupRequest::set_allocated_session_handle(std::string* session_handle) {
  if (session_handle != nullptr) {
    
  } else {
    
  }
  _impl_.session_handle_.SetAllocated(session_handle, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.session_handle_.IsDefault()) {
    _impl_.session_handle_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.PartialRunSetupRequest.session_handle)
}

// repeated string feed = 2;
inline int PartialRunSetupRequest::_internal_feed_size() const {
  return _impl_.feed_.size();
}
inline int PartialRunSetupRequest::feed_size() const {
  return _internal_feed_size();
}
inline void PartialRunSetupRequest::clear_feed() {
  _impl_.feed_.Clear();
}
inline std::string* PartialRunSetupRequest::add_feed() {
  std::string* _s = _internal_add_feed();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.PartialRunSetupRequest.feed)
  return _s;
}
inline const std::string& PartialRunSetupRequest::_internal_feed(int index) const {
  return _impl_.feed_.Get(index);
}
inline const std::string& PartialRunSetupRequest::feed(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.PartialRunSetupRequest.feed)
  return _internal_feed(index);
}
inline std::string* PartialRunSetupRequest::mutable_feed(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.PartialRunSetupRequest.feed)
  return _impl_.feed_.Mutable(index);
}
inline void PartialRunSetupRequest::set_feed(int index, const std::string& value) {
  _impl_.feed_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.PartialRunSetupRequest.feed)
}
inline void PartialRunSetupRequest::set_feed(int index, std::string&& value) {
  _impl_.feed_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.PartialRunSetupRequest.feed)
}
inline void PartialRunSetupRequest::set_feed(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.feed_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.PartialRunSetupRequest.feed)
}
inline void PartialRunSetupRequest::set_feed(int index, const char* value, size_t size) {
  _impl_.feed_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.PartialRunSetupRequest.feed)
}
inline std::string* PartialRunSetupRequest::_internal_add_feed() {
  return _impl_.feed_.Add();
}
inline void PartialRunSetupRequest::add_feed(const std::string& value) {
  _impl_.feed_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.PartialRunSetupRequest.feed)
}
inline void PartialRunSetupRequest::add_feed(std::string&& value) {
  _impl_.feed_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.PartialRunSetupRequest.feed)
}
inline void PartialRunSetupRequest::add_feed(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.feed_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.PartialRunSetupRequest.feed)
}
inline void PartialRunSetupRequest::add_feed(const char* value, size_t size) {
  _impl_.feed_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.PartialRunSetupRequest.feed)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
PartialRunSetupRequest::feed() const {
  // @@protoc_insertion_point(field_list:tensorflow.PartialRunSetupRequest.feed)
  return _impl_.feed_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
PartialRunSetupRequest::mutable_feed() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.PartialRunSetupRequest.feed)
  return &_impl_.feed_;
}

// repeated string fetch = 3;
inline int PartialRunSetupRequest::_internal_fetch_size() const {
  return _impl_.fetch_.size();
}
inline int PartialRunSetupRequest::fetch_size() const {
  return _internal_fetch_size();
}
inline void PartialRunSetupRequest::clear_fetch() {
  _impl_.fetch_.Clear();
}
inline std::string* PartialRunSetupRequest::add_fetch() {
  std::string* _s = _internal_add_fetch();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.PartialRunSetupRequest.fetch)
  return _s;
}
inline const std::string& PartialRunSetupRequest::_internal_fetch(int index) const {
  return _impl_.fetch_.Get(index);
}
inline const std::string& PartialRunSetupRequest::fetch(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.PartialRunSetupRequest.fetch)
  return _internal_fetch(index);
}
inline std::string* PartialRunSetupRequest::mutable_fetch(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.PartialRunSetupRequest.fetch)
  return _impl_.fetch_.Mutable(index);
}
inline void PartialRunSetupRequest::set_fetch(int index, const std::string& value) {
  _impl_.fetch_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.PartialRunSetupRequest.fetch)
}
inline void PartialRunSetupRequest::set_fetch(int index, std::string&& value) {
  _impl_.fetch_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.PartialRunSetupRequest.fetch)
}
inline void PartialRunSetupRequest::set_fetch(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.fetch_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.PartialRunSetupRequest.fetch)
}
inline void PartialRunSetupRequest::set_fetch(int index, const char* value, size_t size) {
  _impl_.fetch_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.PartialRunSetupRequest.fetch)
}
inline std::string* PartialRunSetupRequest::_internal_add_fetch() {
  return _impl_.fetch_.Add();
}
inline void PartialRunSetupRequest::add_fetch(const std::string& value) {
  _impl_.fetch_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.PartialRunSetupRequest.fetch)
}
inline void PartialRunSetupRequest::add_fetch(std::string&& value) {
  _impl_.fetch_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.PartialRunSetupRequest.fetch)
}
inline void PartialRunSetupRequest::add_fetch(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.fetch_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.PartialRunSetupRequest.fetch)
}
inline void PartialRunSetupRequest::add_fetch(const char* value, size_t size) {
  _impl_.fetch_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.PartialRunSetupRequest.fetch)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
PartialRunSetupRequest::fetch() const {
  // @@protoc_insertion_point(field_list:tensorflow.PartialRunSetupRequest.fetch)
  return _impl_.fetch_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
PartialRunSetupRequest::mutable_fetch() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.PartialRunSetupRequest.fetch)
  return &_impl_.fetch_;
}

// repeated string target = 4;
inline int PartialRunSetupRequest::_internal_target_size() const {
  return _impl_.target_.size();
}
inline int PartialRunSetupRequest::target_size() const {
  return _internal_target_size();
}
inline void PartialRunSetupRequest::clear_target() {
  _impl_.target_.Clear();
}
inline std::string* PartialRunSetupRequest::add_target() {
  std::string* _s = _internal_add_target();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.PartialRunSetupRequest.target)
  return _s;
}
inline const std::string& PartialRunSetupRequest::_internal_target(int index) const {
  return _impl_.target_.Get(index);
}
inline const std::string& PartialRunSetupRequest::target(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.PartialRunSetupRequest.target)
  return _internal_target(index);
}
inline std::string* PartialRunSetupRequest::mutable_target(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.PartialRunSetupRequest.target)
  return _impl_.target_.Mutable(index);
}
inline void PartialRunSetupRequest::set_target(int index, const std::string& value) {
  _impl_.target_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.PartialRunSetupRequest.target)
}
inline void PartialRunSetupRequest::set_target(int index, std::string&& value) {
  _impl_.target_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.PartialRunSetupRequest.target)
}
inline void PartialRunSetupRequest::set_target(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.target_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.PartialRunSetupRequest.target)
}
inline void PartialRunSetupRequest::set_target(int index, const char* value, size_t size) {
  _impl_.target_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.PartialRunSetupRequest.target)
}
inline std::string* PartialRunSetupRequest::_internal_add_target() {
  return _impl_.target_.Add();
}
inline void PartialRunSetupRequest::add_target(const std::string& value) {
  _impl_.target_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.PartialRunSetupRequest.target)
}
inline void PartialRunSetupRequest::add_target(std::string&& value) {
  _impl_.target_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.PartialRunSetupRequest.target)
}
inline void PartialRunSetupRequest::add_target(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.target_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.PartialRunSetupRequest.target)
}
inline void PartialRunSetupRequest::add_target(const char* value, size_t size) {
  _impl_.target_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.PartialRunSetupRequest.target)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
PartialRunSetupRequest::target() const {
  // @@protoc_insertion_point(field_list:tensorflow.PartialRunSetupRequest.target)
  return _impl_.target_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
PartialRunSetupRequest::mutable_target() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.PartialRunSetupRequest.target)
  return &_impl_.target_;
}

// int64 request_id = 5;
inline void PartialRunSetupRequest::clear_request_id() {
  _impl_.request_id_ = int64_t{0};
}
inline int64_t PartialRunSetupRequest::_internal_request_id() const {
  return _impl_.request_id_;
}
inline int64_t PartialRunSetupRequest::request_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.PartialRunSetupRequest.request_id)
  return _internal_request_id();
}
inline void PartialRunSetupRequest::_internal_set_request_id(int64_t value) {
  
  _impl_.request_id_ = value;
}
inline void PartialRunSetupRequest::set_request_id(int64_t value) {
  _internal_set_request_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.PartialRunSetupRequest.request_id)
}

// -------------------------------------------------------------------

// PartialRunSetupResponse

// string partial_run_handle = 1;
inline void PartialRunSetupResponse::clear_partial_run_handle() {
  _impl_.partial_run_handle_.ClearToEmpty();
}
inline const std::string& PartialRunSetupResponse::partial_run_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.PartialRunSetupResponse.partial_run_handle)
  return _internal_partial_run_handle();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void PartialRunSetupResponse::set_partial_run_handle(ArgT0&& arg0, ArgT... args) {
 
 _impl_.partial_run_handle_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.PartialRunSetupResponse.partial_run_handle)
}
inline std::string* PartialRunSetupResponse::mutable_partial_run_handle() {
  std::string* _s = _internal_mutable_partial_run_handle();
  // @@protoc_insertion_point(field_mutable:tensorflow.PartialRunSetupResponse.partial_run_handle)
  return _s;
}
inline const std::string& PartialRunSetupResponse::_internal_partial_run_handle() const {
  return _impl_.partial_run_handle_.Get();
}
inline void PartialRunSetupResponse::_internal_set_partial_run_handle(const std::string& value) {
  
  _impl_.partial_run_handle_.Set(value, GetArenaForAllocation());
}
inline std::string* PartialRunSetupResponse::_internal_mutable_partial_run_handle() {
  
  return _impl_.partial_run_handle_.Mutable(GetArenaForAllocation());
}
inline std::string* PartialRunSetupResponse::release_partial_run_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.PartialRunSetupResponse.partial_run_handle)
  return _impl_.partial_run_handle_.Release();
}
inline void PartialRunSetupResponse::set_allocated_partial_run_handle(std::string* partial_run_handle) {
  if (partial_run_handle != nullptr) {
    
  } else {
    
  }
  _impl_.partial_run_handle_.SetAllocated(partial_run_handle, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.partial_run_handle_.IsDefault()) {
    _impl_.partial_run_handle_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.PartialRunSetupResponse.partial_run_handle)
}

// -------------------------------------------------------------------

// CloseSessionRequest

// string session_handle = 1;
inline void CloseSessionRequest::clear_session_handle() {
  _impl_.session_handle_.ClearToEmpty();
}
inline const std::string& CloseSessionRequest::session_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.CloseSessionRequest.session_handle)
  return _internal_session_handle();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CloseSessionRequest::set_session_handle(ArgT0&& arg0, ArgT... args) {
 
 _impl_.session_handle_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.CloseSessionRequest.session_handle)
}
inline std::string* CloseSessionRequest::mutable_session_handle() {
  std::string* _s = _internal_mutable_session_handle();
  // @@protoc_insertion_point(field_mutable:tensorflow.CloseSessionRequest.session_handle)
  return _s;
}
inline const std::string& CloseSessionRequest::_internal_session_handle() const {
  return _impl_.session_handle_.Get();
}
inline void CloseSessionRequest::_internal_set_session_handle(const std::string& value) {
  
  _impl_.session_handle_.Set(value, GetArenaForAllocation());
}
inline std::string* CloseSessionRequest::_internal_mutable_session_handle() {
  
  return _impl_.session_handle_.Mutable(GetArenaForAllocation());
}
inline std::string* CloseSessionRequest::release_session_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.CloseSessionRequest.session_handle)
  return _impl_.session_handle_.Release();
}
inline void CloseSessionRequest::set_allocated_session_handle(std::string* session_handle) {
  if (session_handle != nullptr) {
    
  } else {
    
  }
  _impl_.session_handle_.SetAllocated(session_handle, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.session_handle_.IsDefault()) {
    _impl_.session_handle_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CloseSessionRequest.session_handle)
}

// -------------------------------------------------------------------

// CloseSessionResponse

// -------------------------------------------------------------------

// ResetRequest

// repeated string container = 1;
inline int ResetRequest::_internal_container_size() const {
  return _impl_.container_.size();
}
inline int ResetRequest::container_size() const {
  return _internal_container_size();
}
inline void ResetRequest::clear_container() {
  _impl_.container_.Clear();
}
inline std::string* ResetRequest::add_container() {
  std::string* _s = _internal_add_container();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.ResetRequest.container)
  return _s;
}
inline const std::string& ResetRequest::_internal_container(int index) const {
  return _impl_.container_.Get(index);
}
inline const std::string& ResetRequest::container(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.ResetRequest.container)
  return _internal_container(index);
}
inline std::string* ResetRequest::mutable_container(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.ResetRequest.container)
  return _impl_.container_.Mutable(index);
}
inline void ResetRequest::set_container(int index, const std::string& value) {
  _impl_.container_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.ResetRequest.container)
}
inline void ResetRequest::set_container(int index, std::string&& value) {
  _impl_.container_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.ResetRequest.container)
}
inline void ResetRequest::set_container(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.container_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.ResetRequest.container)
}
inline void ResetRequest::set_container(int index, const char* value, size_t size) {
  _impl_.container_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.ResetRequest.container)
}
inline std::string* ResetRequest::_internal_add_container() {
  return _impl_.container_.Add();
}
inline void ResetRequest::add_container(const std::string& value) {
  _impl_.container_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.ResetRequest.container)
}
inline void ResetRequest::add_container(std::string&& value) {
  _impl_.container_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.ResetRequest.container)
}
inline void ResetRequest::add_container(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.container_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.ResetRequest.container)
}
inline void ResetRequest::add_container(const char* value, size_t size) {
  _impl_.container_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.ResetRequest.container)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
ResetRequest::container() const {
  // @@protoc_insertion_point(field_list:tensorflow.ResetRequest.container)
  return _impl_.container_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
ResetRequest::mutable_container() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.ResetRequest.container)
  return &_impl_.container_;
}

// repeated string device_filters = 2;
inline int ResetRequest::_internal_device_filters_size() const {
  return _impl_.device_filters_.size();
}
inline int ResetRequest::device_filters_size() const {
  return _internal_device_filters_size();
}
inline void ResetRequest::clear_device_filters() {
  _impl_.device_filters_.Clear();
}
inline std::string* ResetRequest::add_device_filters() {
  std::string* _s = _internal_add_device_filters();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.ResetRequest.device_filters)
  return _s;
}
inline const std::string& ResetRequest::_internal_device_filters(int index) const {
  return _impl_.device_filters_.Get(index);
}
inline const std::string& ResetRequest::device_filters(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.ResetRequest.device_filters)
  return _internal_device_filters(index);
}
inline std::string* ResetRequest::mutable_device_filters(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.ResetRequest.device_filters)
  return _impl_.device_filters_.Mutable(index);
}
inline void ResetRequest::set_device_filters(int index, const std::string& value) {
  _impl_.device_filters_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.ResetRequest.device_filters)
}
inline void ResetRequest::set_device_filters(int index, std::string&& value) {
  _impl_.device_filters_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.ResetRequest.device_filters)
}
inline void ResetRequest::set_device_filters(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.device_filters_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.ResetRequest.device_filters)
}
inline void ResetRequest::set_device_filters(int index, const char* value, size_t size) {
  _impl_.device_filters_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.ResetRequest.device_filters)
}
inline std::string* ResetRequest::_internal_add_device_filters() {
  return _impl_.device_filters_.Add();
}
inline void ResetRequest::add_device_filters(const std::string& value) {
  _impl_.device_filters_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.ResetRequest.device_filters)
}
inline void ResetRequest::add_device_filters(std::string&& value) {
  _impl_.device_filters_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.ResetRequest.device_filters)
}
inline void ResetRequest::add_device_filters(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.device_filters_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.ResetRequest.device_filters)
}
inline void ResetRequest::add_device_filters(const char* value, size_t size) {
  _impl_.device_filters_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.ResetRequest.device_filters)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
ResetRequest::device_filters() const {
  // @@protoc_insertion_point(field_list:tensorflow.ResetRequest.device_filters)
  return _impl_.device_filters_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
ResetRequest::mutable_device_filters() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.ResetRequest.device_filters)
  return &_impl_.device_filters_;
}

// -------------------------------------------------------------------

// ResetResponse

// -------------------------------------------------------------------

// ListDevicesRequest

// string session_handle = 1;
inline void ListDevicesRequest::clear_session_handle() {
  _impl_.session_handle_.ClearToEmpty();
}
inline const std::string& ListDevicesRequest::session_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.ListDevicesRequest.session_handle)
  return _internal_session_handle();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ListDevicesRequest::set_session_handle(ArgT0&& arg0, ArgT... args) {
 
 _impl_.session_handle_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.ListDevicesRequest.session_handle)
}
inline std::string* ListDevicesRequest::mutable_session_handle() {
  std::string* _s = _internal_mutable_session_handle();
  // @@protoc_insertion_point(field_mutable:tensorflow.ListDevicesRequest.session_handle)
  return _s;
}
inline const std::string& ListDevicesRequest::_internal_session_handle() const {
  return _impl_.session_handle_.Get();
}
inline void ListDevicesRequest::_internal_set_session_handle(const std::string& value) {
  
  _impl_.session_handle_.Set(value, GetArenaForAllocation());
}
inline std::string* ListDevicesRequest::_internal_mutable_session_handle() {
  
  return _impl_.session_handle_.Mutable(GetArenaForAllocation());
}
inline std::string* ListDevicesRequest::release_session_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.ListDevicesRequest.session_handle)
  return _impl_.session_handle_.Release();
}
inline void ListDevicesRequest::set_allocated_session_handle(std::string* session_handle) {
  if (session_handle != nullptr) {
    
  } else {
    
  }
  _impl_.session_handle_.SetAllocated(session_handle, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.session_handle_.IsDefault()) {
    _impl_.session_handle_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ListDevicesRequest.session_handle)
}

// -------------------------------------------------------------------

// ListDevicesResponse

// repeated .tensorflow.DeviceAttributes local_device = 1;
inline int ListDevicesResponse::_internal_local_device_size() const {
  return _impl_.local_device_.size();
}
inline int ListDevicesResponse::local_device_size() const {
  return _internal_local_device_size();
}
inline ::tensorflow::DeviceAttributes* ListDevicesResponse::mutable_local_device(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.ListDevicesResponse.local_device)
  return _impl_.local_device_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >*
ListDevicesResponse::mutable_local_device() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.ListDevicesResponse.local_device)
  return &_impl_.local_device_;
}
inline const ::tensorflow::DeviceAttributes& ListDevicesResponse::_internal_local_device(int index) const {
  return _impl_.local_device_.Get(index);
}
inline const ::tensorflow::DeviceAttributes& ListDevicesResponse::local_device(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.ListDevicesResponse.local_device)
  return _internal_local_device(index);
}
inline ::tensorflow::DeviceAttributes* ListDevicesResponse::_internal_add_local_device() {
  return _impl_.local_device_.Add();
}
inline ::tensorflow::DeviceAttributes* ListDevicesResponse::add_local_device() {
  ::tensorflow::DeviceAttributes* _add = _internal_add_local_device();
  // @@protoc_insertion_point(field_add:tensorflow.ListDevicesResponse.local_device)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >&
ListDevicesResponse::local_device() const {
  // @@protoc_insertion_point(field_list:tensorflow.ListDevicesResponse.local_device)
  return _impl_.local_device_;
}

// repeated .tensorflow.DeviceAttributes remote_device = 2;
inline int ListDevicesResponse::_internal_remote_device_size() const {
  return _impl_.remote_device_.size();
}
inline int ListDevicesResponse::remote_device_size() const {
  return _internal_remote_device_size();
}
inline ::tensorflow::DeviceAttributes* ListDevicesResponse::mutable_remote_device(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.ListDevicesResponse.remote_device)
  return _impl_.remote_device_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >*
ListDevicesResponse::mutable_remote_device() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.ListDevicesResponse.remote_device)
  return &_impl_.remote_device_;
}
inline const ::tensorflow::DeviceAttributes& ListDevicesResponse::_internal_remote_device(int index) const {
  return _impl_.remote_device_.Get(index);
}
inline const ::tensorflow::DeviceAttributes& ListDevicesResponse::remote_device(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.ListDevicesResponse.remote_device)
  return _internal_remote_device(index);
}
inline ::tensorflow::DeviceAttributes* ListDevicesResponse::_internal_add_remote_device() {
  return _impl_.remote_device_.Add();
}
inline ::tensorflow::DeviceAttributes* ListDevicesResponse::add_remote_device() {
  ::tensorflow::DeviceAttributes* _add = _internal_add_remote_device();
  // @@protoc_insertion_point(field_add:tensorflow.ListDevicesResponse.remote_device)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >&
ListDevicesResponse::remote_device() const {
  // @@protoc_insertion_point(field_list:tensorflow.ListDevicesResponse.remote_device)
  return _impl_.remote_device_;
}

// -------------------------------------------------------------------

// MakeCallableRequest

// string session_handle = 1;
inline void MakeCallableRequest::clear_session_handle() {
  _impl_.session_handle_.ClearToEmpty();
}
inline const std::string& MakeCallableRequest::session_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.MakeCallableRequest.session_handle)
  return _internal_session_handle();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void MakeCallableRequest::set_session_handle(ArgT0&& arg0, ArgT... args) {
 
 _impl_.session_handle_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.MakeCallableRequest.session_handle)
}
inline std::string* MakeCallableRequest::mutable_session_handle() {
  std::string* _s = _internal_mutable_session_handle();
  // @@protoc_insertion_point(field_mutable:tensorflow.MakeCallableRequest.session_handle)
  return _s;
}
inline const std::string& MakeCallableRequest::_internal_session_handle() const {
  return _impl_.session_handle_.Get();
}
inline void MakeCallableRequest::_internal_set_session_handle(const std::string& value) {
  
  _impl_.session_handle_.Set(value, GetArenaForAllocation());
}
inline std::string* MakeCallableRequest::_internal_mutable_session_handle() {
  
  return _impl_.session_handle_.Mutable(GetArenaForAllocation());
}
inline std::string* MakeCallableRequest::release_session_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.MakeCallableRequest.session_handle)
  return _impl_.session_handle_.Release();
}
inline void MakeCallableRequest::set_allocated_session_handle(std::string* session_handle) {
  if (session_handle != nullptr) {
    
  } else {
    
  }
  _impl_.session_handle_.SetAllocated(session_handle, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.session_handle_.IsDefault()) {
    _impl_.session_handle_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MakeCallableRequest.session_handle)
}

// .tensorflow.CallableOptions options = 2;
inline bool MakeCallableRequest::_internal_has_options() const {
  return this != internal_default_instance() && _impl_.options_ != nullptr;
}
inline bool MakeCallableRequest::has_options() const {
  return _internal_has_options();
}
inline const ::tensorflow::CallableOptions& MakeCallableRequest::_internal_options() const {
  const ::tensorflow::CallableOptions* p = _impl_.options_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::CallableOptions&>(
      ::tensorflow::_CallableOptions_default_instance_);
}
inline const ::tensorflow::CallableOptions& MakeCallableRequest::options() const {
  // @@protoc_insertion_point(field_get:tensorflow.MakeCallableRequest.options)
  return _internal_options();
}
inline void MakeCallableRequest::unsafe_arena_set_allocated_options(
    ::tensorflow::CallableOptions* options) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.options_);
  }
  _impl_.options_ = options;
  if (options) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.MakeCallableRequest.options)
}
inline ::tensorflow::CallableOptions* MakeCallableRequest::release_options() {
  
  ::tensorflow::CallableOptions* temp = _impl_.options_;
  _impl_.options_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::CallableOptions* MakeCallableRequest::unsafe_arena_release_options() {
  // @@protoc_insertion_point(field_release:tensorflow.MakeCallableRequest.options)
  
  ::tensorflow::CallableOptions* temp = _impl_.options_;
  _impl_.options_ = nullptr;
  return temp;
}
inline ::tensorflow::CallableOptions* MakeCallableRequest::_internal_mutable_options() {
  
  if (_impl_.options_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::CallableOptions>(GetArenaForAllocation());
    _impl_.options_ = p;
  }
  return _impl_.options_;
}
inline ::tensorflow::CallableOptions* MakeCallableRequest::mutable_options() {
  ::tensorflow::CallableOptions* _msg = _internal_mutable_options();
  // @@protoc_insertion_point(field_mutable:tensorflow.MakeCallableRequest.options)
  return _msg;
}
inline void MakeCallableRequest::set_allocated_options(::tensorflow::CallableOptions* options) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.options_);
  }
  if (options) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(options));
    if (message_arena != submessage_arena) {
      options = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, options, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.options_ = options;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MakeCallableRequest.options)
}

// int64 request_id = 3;
inline void MakeCallableRequest::clear_request_id() {
  _impl_.request_id_ = int64_t{0};
}
inline int64_t MakeCallableRequest::_internal_request_id() const {
  return _impl_.request_id_;
}
inline int64_t MakeCallableRequest::request_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.MakeCallableRequest.request_id)
  return _internal_request_id();
}
inline void MakeCallableRequest::_internal_set_request_id(int64_t value) {
  
  _impl_.request_id_ = value;
}
inline void MakeCallableRequest::set_request_id(int64_t value) {
  _internal_set_request_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.MakeCallableRequest.request_id)
}

// -------------------------------------------------------------------

// MakeCallableResponse

// int64 handle = 1;
inline void MakeCallableResponse::clear_handle() {
  _impl_.handle_ = int64_t{0};
}
inline int64_t MakeCallableResponse::_internal_handle() const {
  return _impl_.handle_;
}
inline int64_t MakeCallableResponse::handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.MakeCallableResponse.handle)
  return _internal_handle();
}
inline void MakeCallableResponse::_internal_set_handle(int64_t value) {
  
  _impl_.handle_ = value;
}
inline void MakeCallableResponse::set_handle(int64_t value) {
  _internal_set_handle(value);
  // @@protoc_insertion_point(field_set:tensorflow.MakeCallableResponse.handle)
}

// -------------------------------------------------------------------

// RunCallableRequest

// string session_handle = 1;
inline void RunCallableRequest::clear_session_handle() {
  _impl_.session_handle_.ClearToEmpty();
}
inline const std::string& RunCallableRequest::session_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunCallableRequest.session_handle)
  return _internal_session_handle();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RunCallableRequest::set_session_handle(ArgT0&& arg0, ArgT... args) {
 
 _impl_.session_handle_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.RunCallableRequest.session_handle)
}
inline std::string* RunCallableRequest::mutable_session_handle() {
  std::string* _s = _internal_mutable_session_handle();
  // @@protoc_insertion_point(field_mutable:tensorflow.RunCallableRequest.session_handle)
  return _s;
}
inline const std::string& RunCallableRequest::_internal_session_handle() const {
  return _impl_.session_handle_.Get();
}
inline void RunCallableRequest::_internal_set_session_handle(const std::string& value) {
  
  _impl_.session_handle_.Set(value, GetArenaForAllocation());
}
inline std::string* RunCallableRequest::_internal_mutable_session_handle() {
  
  return _impl_.session_handle_.Mutable(GetArenaForAllocation());
}
inline std::string* RunCallableRequest::release_session_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.RunCallableRequest.session_handle)
  return _impl_.session_handle_.Release();
}
inline void RunCallableRequest::set_allocated_session_handle(std::string* session_handle) {
  if (session_handle != nullptr) {
    
  } else {
    
  }
  _impl_.session_handle_.SetAllocated(session_handle, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.session_handle_.IsDefault()) {
    _impl_.session_handle_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RunCallableRequest.session_handle)
}

// int64 handle = 2;
inline void RunCallableRequest::clear_handle() {
  _impl_.handle_ = int64_t{0};
}
inline int64_t RunCallableRequest::_internal_handle() const {
  return _impl_.handle_;
}
inline int64_t RunCallableRequest::handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunCallableRequest.handle)
  return _internal_handle();
}
inline void RunCallableRequest::_internal_set_handle(int64_t value) {
  
  _impl_.handle_ = value;
}
inline void RunCallableRequest::set_handle(int64_t value) {
  _internal_set_handle(value);
  // @@protoc_insertion_point(field_set:tensorflow.RunCallableRequest.handle)
}

// repeated .tensorflow.TensorProto feed = 3;
inline int RunCallableRequest::_internal_feed_size() const {
  return _impl_.feed_.size();
}
inline int RunCallableRequest::feed_size() const {
  return _internal_feed_size();
}
inline ::tensorflow::TensorProto* RunCallableRequest::mutable_feed(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.RunCallableRequest.feed)
  return _impl_.feed_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >*
RunCallableRequest::mutable_feed() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.RunCallableRequest.feed)
  return &_impl_.feed_;
}
inline const ::tensorflow::TensorProto& RunCallableRequest::_internal_feed(int index) const {
  return _impl_.feed_.Get(index);
}
inline const ::tensorflow::TensorProto& RunCallableRequest::feed(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.RunCallableRequest.feed)
  return _internal_feed(index);
}
inline ::tensorflow::TensorProto* RunCallableRequest::_internal_add_feed() {
  return _impl_.feed_.Add();
}
inline ::tensorflow::TensorProto* RunCallableRequest::add_feed() {
  ::tensorflow::TensorProto* _add = _internal_add_feed();
  // @@protoc_insertion_point(field_add:tensorflow.RunCallableRequest.feed)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >&
RunCallableRequest::feed() const {
  // @@protoc_insertion_point(field_list:tensorflow.RunCallableRequest.feed)
  return _impl_.feed_;
}

// int64 request_id = 4;
inline void RunCallableRequest::clear_request_id() {
  _impl_.request_id_ = int64_t{0};
}
inline int64_t RunCallableRequest::_internal_request_id() const {
  return _impl_.request_id_;
}
inline int64_t RunCallableRequest::request_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunCallableRequest.request_id)
  return _internal_request_id();
}
inline void RunCallableRequest::_internal_set_request_id(int64_t value) {
  
  _impl_.request_id_ = value;
}
inline void RunCallableRequest::set_request_id(int64_t value) {
  _internal_set_request_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.RunCallableRequest.request_id)
}

// -------------------------------------------------------------------

// RunCallableResponse

// repeated .tensorflow.TensorProto fetch = 1;
inline int RunCallableResponse::_internal_fetch_size() const {
  return _impl_.fetch_.size();
}
inline int RunCallableResponse::fetch_size() const {
  return _internal_fetch_size();
}
inline ::tensorflow::TensorProto* RunCallableResponse::mutable_fetch(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.RunCallableResponse.fetch)
  return _impl_.fetch_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >*
RunCallableResponse::mutable_fetch() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.RunCallableResponse.fetch)
  return &_impl_.fetch_;
}
inline const ::tensorflow::TensorProto& RunCallableResponse::_internal_fetch(int index) const {
  return _impl_.fetch_.Get(index);
}
inline const ::tensorflow::TensorProto& RunCallableResponse::fetch(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.RunCallableResponse.fetch)
  return _internal_fetch(index);
}
inline ::tensorflow::TensorProto* RunCallableResponse::_internal_add_fetch() {
  return _impl_.fetch_.Add();
}
inline ::tensorflow::TensorProto* RunCallableResponse::add_fetch() {
  ::tensorflow::TensorProto* _add = _internal_add_fetch();
  // @@protoc_insertion_point(field_add:tensorflow.RunCallableResponse.fetch)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >&
RunCallableResponse::fetch() const {
  // @@protoc_insertion_point(field_list:tensorflow.RunCallableResponse.fetch)
  return _impl_.fetch_;
}

// .tensorflow.RunMetadata metadata = 2;
inline bool RunCallableResponse::_internal_has_metadata() const {
  return this != internal_default_instance() && _impl_.metadata_ != nullptr;
}
inline bool RunCallableResponse::has_metadata() const {
  return _internal_has_metadata();
}
inline const ::tensorflow::RunMetadata& RunCallableResponse::_internal_metadata() const {
  const ::tensorflow::RunMetadata* p = _impl_.metadata_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::RunMetadata&>(
      ::tensorflow::_RunMetadata_default_instance_);
}
inline const ::tensorflow::RunMetadata& RunCallableResponse::metadata() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunCallableResponse.metadata)
  return _internal_metadata();
}
inline void RunCallableResponse::unsafe_arena_set_allocated_metadata(
    ::tensorflow::RunMetadata* metadata) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.metadata_);
  }
  _impl_.metadata_ = metadata;
  if (metadata) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RunCallableResponse.metadata)
}
inline ::tensorflow::RunMetadata* RunCallableResponse::release_metadata() {
  
  ::tensorflow::RunMetadata* temp = _impl_.metadata_;
  _impl_.metadata_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::RunMetadata* RunCallableResponse::unsafe_arena_release_metadata() {
  // @@protoc_insertion_point(field_release:tensorflow.RunCallableResponse.metadata)
  
  ::tensorflow::RunMetadata* temp = _impl_.metadata_;
  _impl_.metadata_ = nullptr;
  return temp;
}
inline ::tensorflow::RunMetadata* RunCallableResponse::_internal_mutable_metadata() {
  
  if (_impl_.metadata_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::RunMetadata>(GetArenaForAllocation());
    _impl_.metadata_ = p;
  }
  return _impl_.metadata_;
}
inline ::tensorflow::RunMetadata* RunCallableResponse::mutable_metadata() {
  ::tensorflow::RunMetadata* _msg = _internal_mutable_metadata();
  // @@protoc_insertion_point(field_mutable:tensorflow.RunCallableResponse.metadata)
  return _msg;
}
inline void RunCallableResponse::set_allocated_metadata(::tensorflow::RunMetadata* metadata) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.metadata_);
  }
  if (metadata) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(metadata));
    if (message_arena != submessage_arena) {
      metadata = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, metadata, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.metadata_ = metadata;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RunCallableResponse.metadata)
}

// -------------------------------------------------------------------

// ReleaseCallableRequest

// string session_handle = 1;
inline void ReleaseCallableRequest::clear_session_handle() {
  _impl_.session_handle_.ClearToEmpty();
}
inline const std::string& ReleaseCallableRequest::session_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.ReleaseCallableRequest.session_handle)
  return _internal_session_handle();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ReleaseCallableRequest::set_session_handle(ArgT0&& arg0, ArgT... args) {
 
 _impl_.session_handle_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.ReleaseCallableRequest.session_handle)
}
inline std::string* ReleaseCallableRequest::mutable_session_handle() {
  std::string* _s = _internal_mutable_session_handle();
  // @@protoc_insertion_point(field_mutable:tensorflow.ReleaseCallableRequest.session_handle)
  return _s;
}
inline const std::string& ReleaseCallableRequest::_internal_session_handle() const {
  return _impl_.session_handle_.Get();
}
inline void ReleaseCallableRequest::_internal_set_session_handle(const std::string& value) {
  
  _impl_.session_handle_.Set(value, GetArenaForAllocation());
}
inline std::string* ReleaseCallableRequest::_internal_mutable_session_handle() {
  
  return _impl_.session_handle_.Mutable(GetArenaForAllocation());
}
inline std::string* ReleaseCallableRequest::release_session_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.ReleaseCallableRequest.session_handle)
  return _impl_.session_handle_.Release();
}
inline void ReleaseCallableRequest::set_allocated_session_handle(std::string* session_handle) {
  if (session_handle != nullptr) {
    
  } else {
    
  }
  _impl_.session_handle_.SetAllocated(session_handle, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.session_handle_.IsDefault()) {
    _impl_.session_handle_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ReleaseCallableRequest.session_handle)
}

// int64 handle = 2;
inline void ReleaseCallableRequest::clear_handle() {
  _impl_.handle_ = int64_t{0};
}
inline int64_t ReleaseCallableRequest::_internal_handle() const {
  return _impl_.handle_;
}
inline int64_t ReleaseCallableRequest::handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.ReleaseCallableRequest.handle)
  return _internal_handle();
}
inline void ReleaseCallableRequest::_internal_set_handle(int64_t value) {
  
  _impl_.handle_ = value;
}
inline void ReleaseCallableRequest::set_handle(int64_t value) {
  _internal_set_handle(value);
  // @@protoc_insertion_point(field_set:tensorflow.ReleaseCallableRequest.handle)
}

// -------------------------------------------------------------------

// ReleaseCallableResponse

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fmaster_2eproto
