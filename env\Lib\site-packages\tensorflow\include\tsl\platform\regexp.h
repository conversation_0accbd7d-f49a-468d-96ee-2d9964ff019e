/* Copyright 2015 The TensorFlow Authors. All Rights Reserved.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
==============================================================================*/

#ifndef TENSORFLOW_TSL_PLATFORM_REGEXP_H_
#define TENSORFLOW_TSL_PLATFORM_REGEXP_H_

#include "tsl/platform/platform.h"

#if TSL_IS_IN_OSS
#include "re2/re2.h"  // IWYU pragma: export
#else
#include "third_party/re2/re2.h"  // IWYU pragma: export
#endif                            // TSL_IS_IN_OSS

#endif  // TENSORFLOW_TSL_PLATFORM_REGEXP_H_
