/**
 * Core Audio audio policy definitions
 *
 * (c) 2013 Extended by mingw-w64 team
 * base on version Copyright 2009 <PERSON><PERSON><PERSON>
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 *
 */

cpp_quote("#include <winapifamily.h>")
cpp_quote("")

import "oaidl.idl";
import "ocidl.idl";
import "propidl.idl";
import "audiosessiontypes.h";
import "audioclient.idl";

cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)")
typedef enum AudioSessionDisconnectReason {
  DisconnectReasonDeviceRemoval,
  DisconnectReasonServerShutdown,
  DisconnectReasonFormatChanged,
  DisconnectReasonSessionLogoff,
  DisconnectReasonSessionDisconnected,
  DisconnectReasonExclusiveModeOverride
} AudioSessionDisconnectReason;

cpp_quote("")
[object, local, pointer_default (unique), uuid (24918acc-64b3-37c1-8ca9-74a66e9957a8)]
interface IAudioSessionEvents : IUnknown {
  HRESULT OnDisplayNameChanged ([in, string]LPCWSTR NewDisplayName,[in] LPCGUID EventContext);
  HRESULT OnIconPathChanged ([in, string]LPCWSTR NewIconPath,[in] LPCGUID EventContext);
  HRESULT OnSimpleVolumeChanged ([in] float NewVolume,[in] BOOL NewMute,[in] LPCGUID EventContext);
  HRESULT OnChannelVolumeChanged ([in] DWORD ChannelCount,[in, size_is (ChannelCount)]float NewChannelVolumeArray[*],[in]DWORD ChangedChannel,[in] LPCGUID EventContext);
  HRESULT OnGroupingParamChanged ([in] LPCGUID NewGroupingParam,[in] LPCGUID EventContext);
  HRESULT OnStateChanged ([in] AudioSessionState NewState);
  HRESULT OnSessionDisconnected ([in] AudioSessionDisconnectReason DisconnectReason);
};

cpp_quote("")
[object, local, uuid (F4B1A599-7266-4319-A8CA-E70ACB11E8CD), helpstring ("AudioSession Control Interface"), pointer_default (unique)]
interface IAudioSessionControl : IUnknown {
  HRESULT GetState ([out] AudioSessionState *pRetVal);
  HRESULT GetDisplayName ([out, string] LPWSTR *pRetVal);
  HRESULT SetDisplayName ([in, string] LPCWSTR Value,[in, unique] LPCGUID EventContext);
  HRESULT GetIconPath ([out, string] LPWSTR *pRetVal);
  HRESULT SetIconPath ([in, string] LPCWSTR Value,[in, unique] LPCGUID EventContext);
  HRESULT GetGroupingParam ([out] GUID *pRetVal);
  HRESULT SetGroupingParam ([in] LPCGUID Override,[in, unique] LPCGUID EventContext);
  HRESULT RegisterAudioSessionNotification ([in]IAudioSessionEvents *NewNotifications);
  HRESULT UnregisterAudioSessionNotification ([in]IAudioSessionEvents *NewNotifications);
}
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)")
[object, local, uuid (bfb7ff88-7239-4fc9-8fa2-07c950be9c6d), helpstring ("AudioSession Control Extended Interface"), pointer_default (unique)]
interface IAudioSessionControl2 : IAudioSessionControl {
  HRESULT GetSessionIdentifier ([out, string] LPWSTR *pRetVal);
  HRESULT GetSessionInstanceIdentifier ([out, string] LPWSTR *pRetVal);
  HRESULT GetProcessId ([out] DWORD *pRetVal);
  HRESULT IsSystemSoundsSession ();
  HRESULT SetDuckingPreference ([in] BOOL optOut);
}

cpp_quote("")
[object, local, uuid (BFA971F1-4d5e-40bb-935e-967039bfbee4), helpstring ("Audio Session Manager Interface"), pointer_default (unique)]
interface IAudioSessionManager : IUnknown {
  HRESULT GetAudioSessionControl ([in]LPCGUID AudioSessionGuid,[in] DWORD StreamFlags,[out] IAudioSessionControl **SessionControl);
  HRESULT GetSimpleAudioVolume ([in]LPCGUID AudioSessionGuid,[in] DWORD StreamFlags,[out] ISimpleAudioVolume **AudioVolume);
}

cpp_quote("")
[object, local, uuid (C3B284D4-6d39-4359-B3CF-B56DDB3BB39C), helpstring ("Audio Session Notification Interface"), pointer_default (unique)]
interface IAudioVolumeDuckNotification : IUnknown {
  HRESULT OnVolumeDuckNotification ([in] LPCWSTR sessionID,[in] UINT32 countCommunicationSessions);
  HRESULT OnVolumeUnduckNotification ([in] LPCWSTR sessionID);
}

cpp_quote("")
[object, local, uuid (641dd20b-4d41-49cc-ABA3-174b9477bb08), helpstring ("Audio Session Notification Interface"), pointer_default (unique)]
interface IAudioSessionNotification : IUnknown {
  HRESULT OnSessionCreated ([in] IAudioSessionControl *NewSession);
}

cpp_quote("")
[object, local, uuid (E2F5BB11-0570-40ca-ACDD-3aa01277dee8), helpstring ("Audio Session Enumerator Interface"), pointer_default (unique)]
interface IAudioSessionEnumerator : IUnknown {
  HRESULT GetCount ([out] int *SessionCount);
  HRESULT GetSession ([in] int SessionCount,[out] IAudioSessionControl **Session);
}

cpp_quote("")
[object, local, uuid (77aa99a0-1bd6-484f-8bc7-2c654c9a9b6f), helpstring ("Audio Session Manager Extended Interface"), pointer_default (unique)]
interface IAudioSessionManager2 : IAudioSessionManager {
  HRESULT GetSessionEnumerator ([out, retval]IAudioSessionEnumerator **SessionEnum);
  HRESULT RegisterSessionNotification (IAudioSessionNotification *SessionNotification);
  HRESULT UnregisterSessionNotification (IAudioSessionNotification *SessionNotification);
  HRESULT RegisterDuckNotification ([in, string] LPCWSTR sessionID,[in]IAudioVolumeDuckNotification *duckNotification);
  HRESULT UnregisterDuckNotification ([in]IAudioVolumeDuckNotification *duckNotification);
}
cpp_quote("#endif")
