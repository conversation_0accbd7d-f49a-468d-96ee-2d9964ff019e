.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crt_sign" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crt_sign \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crt_sign(gnutls_x509_crt_t " crt ", gnutls_x509_crt_t " issuer ", gnutls_x509_privkey_t " issuer_key ");"
.SH ARGUMENTS
.IP "gnutls_x509_crt_t crt" 12
a certificate of type \fBgnutls_x509_crt_t\fP
.IP "gnutls_x509_crt_t issuer" 12
is the certificate of the certificate issuer
.IP "gnutls_x509_privkey_t issuer_key" 12
holds the issuer's private key
.SH "DESCRIPTION"
This function is the same a \fBgnutls_x509_crt_sign2()\fP with no flags,
and an appropriate hash algorithm. The hash algorithm used may
vary between versions of GnuTLS, and it is tied to the security
level of the issuer's public key.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
