.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crt_set_version" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crt_set_version \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crt_set_version(gnutls_x509_crt_t " crt ", unsigned int " version ");"
.SH ARGUMENTS
.IP "gnutls_x509_crt_t crt" 12
a certificate of type \fBgnutls_x509_crt_t\fP
.IP "unsigned int version" 12
holds the version number. For X.509v1 certificates must be 1.
.SH "DESCRIPTION"
This function will set the version of the certificate.  This must
be one for X.509 version 1, and so on.  Plain certificates without
extensions must have version set to one.

To create well\-formed certificates, you must specify version 3 if
you use any certificate extensions.  Extensions are created by
functions such as \fBgnutls_x509_crt_set_subject_alt_name()\fP
or \fBgnutls_x509_crt_set_key_usage()\fP.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
