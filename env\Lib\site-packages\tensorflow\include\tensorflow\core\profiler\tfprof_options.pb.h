// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/profiler/tfprof_options.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto;
namespace tensorflow {
namespace tfprof {
class AdvisorOptionsProto;
struct AdvisorOptionsProtoDefaultTypeInternal;
extern AdvisorOptionsProtoDefaultTypeInternal _AdvisorOptionsProto_default_instance_;
class AdvisorOptionsProto_CheckerOption;
struct AdvisorOptionsProto_CheckerOptionDefaultTypeInternal;
extern AdvisorOptionsProto_CheckerOptionDefaultTypeInternal _AdvisorOptionsProto_CheckerOption_default_instance_;
class AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse;
struct AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUseDefaultTypeInternal;
extern AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUseDefaultTypeInternal _AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse_default_instance_;
class AdvisorOptionsProto_CheckersEntry_DoNotUse;
struct AdvisorOptionsProto_CheckersEntry_DoNotUseDefaultTypeInternal;
extern AdvisorOptionsProto_CheckersEntry_DoNotUseDefaultTypeInternal _AdvisorOptionsProto_CheckersEntry_DoNotUse_default_instance_;
class OptionsProto;
struct OptionsProtoDefaultTypeInternal;
extern OptionsProtoDefaultTypeInternal _OptionsProto_default_instance_;
}  // namespace tfprof
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::tfprof::AdvisorOptionsProto* Arena::CreateMaybeMessage<::tensorflow::tfprof::AdvisorOptionsProto>(Arena*);
template<> ::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption* Arena::CreateMaybeMessage<::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption>(Arena*);
template<> ::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse>(Arena*);
template<> ::tensorflow::tfprof::AdvisorOptionsProto_CheckersEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::tfprof::AdvisorOptionsProto_CheckersEntry_DoNotUse>(Arena*);
template<> ::tensorflow::tfprof::OptionsProto* Arena::CreateMaybeMessage<::tensorflow::tfprof::OptionsProto>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {
namespace tfprof {

// ===================================================================

class OptionsProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.OptionsProto) */ {
 public:
  inline OptionsProto() : OptionsProto(nullptr) {}
  ~OptionsProto() override;
  explicit PROTOBUF_CONSTEXPR OptionsProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  OptionsProto(const OptionsProto& from);
  OptionsProto(OptionsProto&& from) noexcept
    : OptionsProto() {
    *this = ::std::move(from);
  }

  inline OptionsProto& operator=(const OptionsProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline OptionsProto& operator=(OptionsProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const OptionsProto& default_instance() {
    return *internal_default_instance();
  }
  static inline const OptionsProto* internal_default_instance() {
    return reinterpret_cast<const OptionsProto*>(
               &_OptionsProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(OptionsProto& a, OptionsProto& b) {
    a.Swap(&b);
  }
  inline void Swap(OptionsProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(OptionsProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  OptionsProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<OptionsProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const OptionsProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const OptionsProto& from) {
    OptionsProto::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OptionsProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tfprof.OptionsProto";
  }
  protected:
  explicit OptionsProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kAccountTypeRegexesFieldNumber = 8,
    kStartNameRegexesFieldNumber = 9,
    kTrimNameRegexesFieldNumber = 10,
    kShowNameRegexesFieldNumber = 11,
    kHideNameRegexesFieldNumber = 12,
    kSelectFieldNumber = 14,
    kOrderByFieldNumber = 7,
    kOutputFieldNumber = 15,
    kDumpToFileFieldNumber = 16,
    kMaxDepthFieldNumber = 1,
    kMinBytesFieldNumber = 2,
    kMinMicrosFieldNumber = 3,
    kMinParamsFieldNumber = 4,
    kMinFloatOpsFieldNumber = 5,
    kMinOccurrenceFieldNumber = 17,
    kStepFieldNumber = 18,
    kMinPeakBytesFieldNumber = 19,
    kMinResidualBytesFieldNumber = 20,
    kMinOutputBytesFieldNumber = 21,
    kMinAcceleratorMicrosFieldNumber = 22,
    kMinCpuMicrosFieldNumber = 23,
    kAccountDisplayedOpOnlyFieldNumber = 13,
  };
  // repeated string account_type_regexes = 8;
  int account_type_regexes_size() const;
  private:
  int _internal_account_type_regexes_size() const;
  public:
  void clear_account_type_regexes();
  const std::string& account_type_regexes(int index) const;
  std::string* mutable_account_type_regexes(int index);
  void set_account_type_regexes(int index, const std::string& value);
  void set_account_type_regexes(int index, std::string&& value);
  void set_account_type_regexes(int index, const char* value);
  void set_account_type_regexes(int index, const char* value, size_t size);
  std::string* add_account_type_regexes();
  void add_account_type_regexes(const std::string& value);
  void add_account_type_regexes(std::string&& value);
  void add_account_type_regexes(const char* value);
  void add_account_type_regexes(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& account_type_regexes() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_account_type_regexes();
  private:
  const std::string& _internal_account_type_regexes(int index) const;
  std::string* _internal_add_account_type_regexes();
  public:

  // repeated string start_name_regexes = 9;
  int start_name_regexes_size() const;
  private:
  int _internal_start_name_regexes_size() const;
  public:
  void clear_start_name_regexes();
  const std::string& start_name_regexes(int index) const;
  std::string* mutable_start_name_regexes(int index);
  void set_start_name_regexes(int index, const std::string& value);
  void set_start_name_regexes(int index, std::string&& value);
  void set_start_name_regexes(int index, const char* value);
  void set_start_name_regexes(int index, const char* value, size_t size);
  std::string* add_start_name_regexes();
  void add_start_name_regexes(const std::string& value);
  void add_start_name_regexes(std::string&& value);
  void add_start_name_regexes(const char* value);
  void add_start_name_regexes(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& start_name_regexes() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_start_name_regexes();
  private:
  const std::string& _internal_start_name_regexes(int index) const;
  std::string* _internal_add_start_name_regexes();
  public:

  // repeated string trim_name_regexes = 10;
  int trim_name_regexes_size() const;
  private:
  int _internal_trim_name_regexes_size() const;
  public:
  void clear_trim_name_regexes();
  const std::string& trim_name_regexes(int index) const;
  std::string* mutable_trim_name_regexes(int index);
  void set_trim_name_regexes(int index, const std::string& value);
  void set_trim_name_regexes(int index, std::string&& value);
  void set_trim_name_regexes(int index, const char* value);
  void set_trim_name_regexes(int index, const char* value, size_t size);
  std::string* add_trim_name_regexes();
  void add_trim_name_regexes(const std::string& value);
  void add_trim_name_regexes(std::string&& value);
  void add_trim_name_regexes(const char* value);
  void add_trim_name_regexes(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& trim_name_regexes() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_trim_name_regexes();
  private:
  const std::string& _internal_trim_name_regexes(int index) const;
  std::string* _internal_add_trim_name_regexes();
  public:

  // repeated string show_name_regexes = 11;
  int show_name_regexes_size() const;
  private:
  int _internal_show_name_regexes_size() const;
  public:
  void clear_show_name_regexes();
  const std::string& show_name_regexes(int index) const;
  std::string* mutable_show_name_regexes(int index);
  void set_show_name_regexes(int index, const std::string& value);
  void set_show_name_regexes(int index, std::string&& value);
  void set_show_name_regexes(int index, const char* value);
  void set_show_name_regexes(int index, const char* value, size_t size);
  std::string* add_show_name_regexes();
  void add_show_name_regexes(const std::string& value);
  void add_show_name_regexes(std::string&& value);
  void add_show_name_regexes(const char* value);
  void add_show_name_regexes(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& show_name_regexes() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_show_name_regexes();
  private:
  const std::string& _internal_show_name_regexes(int index) const;
  std::string* _internal_add_show_name_regexes();
  public:

  // repeated string hide_name_regexes = 12;
  int hide_name_regexes_size() const;
  private:
  int _internal_hide_name_regexes_size() const;
  public:
  void clear_hide_name_regexes();
  const std::string& hide_name_regexes(int index) const;
  std::string* mutable_hide_name_regexes(int index);
  void set_hide_name_regexes(int index, const std::string& value);
  void set_hide_name_regexes(int index, std::string&& value);
  void set_hide_name_regexes(int index, const char* value);
  void set_hide_name_regexes(int index, const char* value, size_t size);
  std::string* add_hide_name_regexes();
  void add_hide_name_regexes(const std::string& value);
  void add_hide_name_regexes(std::string&& value);
  void add_hide_name_regexes(const char* value);
  void add_hide_name_regexes(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& hide_name_regexes() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_hide_name_regexes();
  private:
  const std::string& _internal_hide_name_regexes(int index) const;
  std::string* _internal_add_hide_name_regexes();
  public:

  // repeated string select = 14;
  int select_size() const;
  private:
  int _internal_select_size() const;
  public:
  void clear_select();
  const std::string& select(int index) const;
  std::string* mutable_select(int index);
  void set_select(int index, const std::string& value);
  void set_select(int index, std::string&& value);
  void set_select(int index, const char* value);
  void set_select(int index, const char* value, size_t size);
  std::string* add_select();
  void add_select(const std::string& value);
  void add_select(std::string&& value);
  void add_select(const char* value);
  void add_select(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& select() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_select();
  private:
  const std::string& _internal_select(int index) const;
  std::string* _internal_add_select();
  public:

  // string order_by = 7;
  void clear_order_by();
  const std::string& order_by() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_order_by(ArgT0&& arg0, ArgT... args);
  std::string* mutable_order_by();
  PROTOBUF_NODISCARD std::string* release_order_by();
  void set_allocated_order_by(std::string* order_by);
  private:
  const std::string& _internal_order_by() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_order_by(const std::string& value);
  std::string* _internal_mutable_order_by();
  public:

  // string output = 15;
  void clear_output();
  const std::string& output() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_output(ArgT0&& arg0, ArgT... args);
  std::string* mutable_output();
  PROTOBUF_NODISCARD std::string* release_output();
  void set_allocated_output(std::string* output);
  private:
  const std::string& _internal_output() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_output(const std::string& value);
  std::string* _internal_mutable_output();
  public:

  // string dump_to_file = 16;
  void clear_dump_to_file();
  const std::string& dump_to_file() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_dump_to_file(ArgT0&& arg0, ArgT... args);
  std::string* mutable_dump_to_file();
  PROTOBUF_NODISCARD std::string* release_dump_to_file();
  void set_allocated_dump_to_file(std::string* dump_to_file);
  private:
  const std::string& _internal_dump_to_file() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_dump_to_file(const std::string& value);
  std::string* _internal_mutable_dump_to_file();
  public:

  // int64 max_depth = 1;
  void clear_max_depth();
  int64_t max_depth() const;
  void set_max_depth(int64_t value);
  private:
  int64_t _internal_max_depth() const;
  void _internal_set_max_depth(int64_t value);
  public:

  // int64 min_bytes = 2;
  void clear_min_bytes();
  int64_t min_bytes() const;
  void set_min_bytes(int64_t value);
  private:
  int64_t _internal_min_bytes() const;
  void _internal_set_min_bytes(int64_t value);
  public:

  // int64 min_micros = 3;
  void clear_min_micros();
  int64_t min_micros() const;
  void set_min_micros(int64_t value);
  private:
  int64_t _internal_min_micros() const;
  void _internal_set_min_micros(int64_t value);
  public:

  // int64 min_params = 4;
  void clear_min_params();
  int64_t min_params() const;
  void set_min_params(int64_t value);
  private:
  int64_t _internal_min_params() const;
  void _internal_set_min_params(int64_t value);
  public:

  // int64 min_float_ops = 5;
  void clear_min_float_ops();
  int64_t min_float_ops() const;
  void set_min_float_ops(int64_t value);
  private:
  int64_t _internal_min_float_ops() const;
  void _internal_set_min_float_ops(int64_t value);
  public:

  // int64 min_occurrence = 17;
  void clear_min_occurrence();
  int64_t min_occurrence() const;
  void set_min_occurrence(int64_t value);
  private:
  int64_t _internal_min_occurrence() const;
  void _internal_set_min_occurrence(int64_t value);
  public:

  // int64 step = 18;
  void clear_step();
  int64_t step() const;
  void set_step(int64_t value);
  private:
  int64_t _internal_step() const;
  void _internal_set_step(int64_t value);
  public:

  // int64 min_peak_bytes = 19;
  void clear_min_peak_bytes();
  int64_t min_peak_bytes() const;
  void set_min_peak_bytes(int64_t value);
  private:
  int64_t _internal_min_peak_bytes() const;
  void _internal_set_min_peak_bytes(int64_t value);
  public:

  // int64 min_residual_bytes = 20;
  void clear_min_residual_bytes();
  int64_t min_residual_bytes() const;
  void set_min_residual_bytes(int64_t value);
  private:
  int64_t _internal_min_residual_bytes() const;
  void _internal_set_min_residual_bytes(int64_t value);
  public:

  // int64 min_output_bytes = 21;
  void clear_min_output_bytes();
  int64_t min_output_bytes() const;
  void set_min_output_bytes(int64_t value);
  private:
  int64_t _internal_min_output_bytes() const;
  void _internal_set_min_output_bytes(int64_t value);
  public:

  // int64 min_accelerator_micros = 22;
  void clear_min_accelerator_micros();
  int64_t min_accelerator_micros() const;
  void set_min_accelerator_micros(int64_t value);
  private:
  int64_t _internal_min_accelerator_micros() const;
  void _internal_set_min_accelerator_micros(int64_t value);
  public:

  // int64 min_cpu_micros = 23;
  void clear_min_cpu_micros();
  int64_t min_cpu_micros() const;
  void set_min_cpu_micros(int64_t value);
  private:
  int64_t _internal_min_cpu_micros() const;
  void _internal_set_min_cpu_micros(int64_t value);
  public:

  // bool account_displayed_op_only = 13;
  void clear_account_displayed_op_only();
  bool account_displayed_op_only() const;
  void set_account_displayed_op_only(bool value);
  private:
  bool _internal_account_displayed_op_only() const;
  void _internal_set_account_displayed_op_only(bool value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.OptionsProto)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> account_type_regexes_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> start_name_regexes_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> trim_name_regexes_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> show_name_regexes_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> hide_name_regexes_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> select_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr order_by_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr output_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr dump_to_file_;
    int64_t max_depth_;
    int64_t min_bytes_;
    int64_t min_micros_;
    int64_t min_params_;
    int64_t min_float_ops_;
    int64_t min_occurrence_;
    int64_t step_;
    int64_t min_peak_bytes_;
    int64_t min_residual_bytes_;
    int64_t min_output_bytes_;
    int64_t min_accelerator_micros_;
    int64_t min_cpu_micros_;
    bool account_displayed_op_only_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto;
};
// -------------------------------------------------------------------

class AdvisorOptionsProto_CheckersEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<AdvisorOptionsProto_CheckersEntry_DoNotUse, 
    std::string, ::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<AdvisorOptionsProto_CheckersEntry_DoNotUse, 
    std::string, ::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> SuperType;
  AdvisorOptionsProto_CheckersEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR AdvisorOptionsProto_CheckersEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit AdvisorOptionsProto_CheckersEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const AdvisorOptionsProto_CheckersEntry_DoNotUse& other);
  static const AdvisorOptionsProto_CheckersEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const AdvisorOptionsProto_CheckersEntry_DoNotUse*>(&_AdvisorOptionsProto_CheckersEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.tfprof.AdvisorOptionsProto.CheckersEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto;
};

// -------------------------------------------------------------------

class AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> SuperType;
  AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse& other);
  static const AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse*>(&_AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.tfprof.AdvisorOptionsProto.CheckerOption.OptionsEntry.key");
 }
  static bool ValidateValue(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.tfprof.AdvisorOptionsProto.CheckerOption.OptionsEntry.value");
 }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto;
};

// -------------------------------------------------------------------

class AdvisorOptionsProto_CheckerOption final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.AdvisorOptionsProto.CheckerOption) */ {
 public:
  inline AdvisorOptionsProto_CheckerOption() : AdvisorOptionsProto_CheckerOption(nullptr) {}
  ~AdvisorOptionsProto_CheckerOption() override;
  explicit PROTOBUF_CONSTEXPR AdvisorOptionsProto_CheckerOption(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  AdvisorOptionsProto_CheckerOption(const AdvisorOptionsProto_CheckerOption& from);
  AdvisorOptionsProto_CheckerOption(AdvisorOptionsProto_CheckerOption&& from) noexcept
    : AdvisorOptionsProto_CheckerOption() {
    *this = ::std::move(from);
  }

  inline AdvisorOptionsProto_CheckerOption& operator=(const AdvisorOptionsProto_CheckerOption& from) {
    CopyFrom(from);
    return *this;
  }
  inline AdvisorOptionsProto_CheckerOption& operator=(AdvisorOptionsProto_CheckerOption&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const AdvisorOptionsProto_CheckerOption& default_instance() {
    return *internal_default_instance();
  }
  static inline const AdvisorOptionsProto_CheckerOption* internal_default_instance() {
    return reinterpret_cast<const AdvisorOptionsProto_CheckerOption*>(
               &_AdvisorOptionsProto_CheckerOption_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(AdvisorOptionsProto_CheckerOption& a, AdvisorOptionsProto_CheckerOption& b) {
    a.Swap(&b);
  }
  inline void Swap(AdvisorOptionsProto_CheckerOption* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AdvisorOptionsProto_CheckerOption* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  AdvisorOptionsProto_CheckerOption* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<AdvisorOptionsProto_CheckerOption>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const AdvisorOptionsProto_CheckerOption& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const AdvisorOptionsProto_CheckerOption& from) {
    AdvisorOptionsProto_CheckerOption::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AdvisorOptionsProto_CheckerOption* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tfprof.AdvisorOptionsProto.CheckerOption";
  }
  protected:
  explicit AdvisorOptionsProto_CheckerOption(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kOptionsFieldNumber = 1,
  };
  // map<string, string> options = 1;
  int options_size() const;
  private:
  int _internal_options_size() const;
  public:
  void clear_options();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      _internal_options() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      _internal_mutable_options();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      options() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      mutable_options();

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.AdvisorOptionsProto.CheckerOption)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        AdvisorOptionsProto_CheckerOption_OptionsEntry_DoNotUse,
        std::string, std::string,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> options_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto;
};
// -------------------------------------------------------------------

class AdvisorOptionsProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.AdvisorOptionsProto) */ {
 public:
  inline AdvisorOptionsProto() : AdvisorOptionsProto(nullptr) {}
  ~AdvisorOptionsProto() override;
  explicit PROTOBUF_CONSTEXPR AdvisorOptionsProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  AdvisorOptionsProto(const AdvisorOptionsProto& from);
  AdvisorOptionsProto(AdvisorOptionsProto&& from) noexcept
    : AdvisorOptionsProto() {
    *this = ::std::move(from);
  }

  inline AdvisorOptionsProto& operator=(const AdvisorOptionsProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline AdvisorOptionsProto& operator=(AdvisorOptionsProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const AdvisorOptionsProto& default_instance() {
    return *internal_default_instance();
  }
  static inline const AdvisorOptionsProto* internal_default_instance() {
    return reinterpret_cast<const AdvisorOptionsProto*>(
               &_AdvisorOptionsProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(AdvisorOptionsProto& a, AdvisorOptionsProto& b) {
    a.Swap(&b);
  }
  inline void Swap(AdvisorOptionsProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AdvisorOptionsProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  AdvisorOptionsProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<AdvisorOptionsProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const AdvisorOptionsProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const AdvisorOptionsProto& from) {
    AdvisorOptionsProto::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AdvisorOptionsProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tfprof.AdvisorOptionsProto";
  }
  protected:
  explicit AdvisorOptionsProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef AdvisorOptionsProto_CheckerOption CheckerOption;

  // accessors -------------------------------------------------------

  enum : int {
    kCheckersFieldNumber = 1,
  };
  // map<string, .tensorflow.tfprof.AdvisorOptionsProto.CheckerOption> checkers = 1;
  int checkers_size() const;
  private:
  int _internal_checkers_size() const;
  public:
  void clear_checkers();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption >&
      _internal_checkers() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption >*
      _internal_mutable_checkers();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption >&
      checkers() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption >*
      mutable_checkers();

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.AdvisorOptionsProto)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        AdvisorOptionsProto_CheckersEntry_DoNotUse,
        std::string, ::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> checkers_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// OptionsProto

// int64 max_depth = 1;
inline void OptionsProto::clear_max_depth() {
  _impl_.max_depth_ = int64_t{0};
}
inline int64_t OptionsProto::_internal_max_depth() const {
  return _impl_.max_depth_;
}
inline int64_t OptionsProto::max_depth() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.max_depth)
  return _internal_max_depth();
}
inline void OptionsProto::_internal_set_max_depth(int64_t value) {
  
  _impl_.max_depth_ = value;
}
inline void OptionsProto::set_max_depth(int64_t value) {
  _internal_set_max_depth(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.max_depth)
}

// int64 min_bytes = 2;
inline void OptionsProto::clear_min_bytes() {
  _impl_.min_bytes_ = int64_t{0};
}
inline int64_t OptionsProto::_internal_min_bytes() const {
  return _impl_.min_bytes_;
}
inline int64_t OptionsProto::min_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.min_bytes)
  return _internal_min_bytes();
}
inline void OptionsProto::_internal_set_min_bytes(int64_t value) {
  
  _impl_.min_bytes_ = value;
}
inline void OptionsProto::set_min_bytes(int64_t value) {
  _internal_set_min_bytes(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.min_bytes)
}

// int64 min_peak_bytes = 19;
inline void OptionsProto::clear_min_peak_bytes() {
  _impl_.min_peak_bytes_ = int64_t{0};
}
inline int64_t OptionsProto::_internal_min_peak_bytes() const {
  return _impl_.min_peak_bytes_;
}
inline int64_t OptionsProto::min_peak_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.min_peak_bytes)
  return _internal_min_peak_bytes();
}
inline void OptionsProto::_internal_set_min_peak_bytes(int64_t value) {
  
  _impl_.min_peak_bytes_ = value;
}
inline void OptionsProto::set_min_peak_bytes(int64_t value) {
  _internal_set_min_peak_bytes(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.min_peak_bytes)
}

// int64 min_residual_bytes = 20;
inline void OptionsProto::clear_min_residual_bytes() {
  _impl_.min_residual_bytes_ = int64_t{0};
}
inline int64_t OptionsProto::_internal_min_residual_bytes() const {
  return _impl_.min_residual_bytes_;
}
inline int64_t OptionsProto::min_residual_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.min_residual_bytes)
  return _internal_min_residual_bytes();
}
inline void OptionsProto::_internal_set_min_residual_bytes(int64_t value) {
  
  _impl_.min_residual_bytes_ = value;
}
inline void OptionsProto::set_min_residual_bytes(int64_t value) {
  _internal_set_min_residual_bytes(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.min_residual_bytes)
}

// int64 min_output_bytes = 21;
inline void OptionsProto::clear_min_output_bytes() {
  _impl_.min_output_bytes_ = int64_t{0};
}
inline int64_t OptionsProto::_internal_min_output_bytes() const {
  return _impl_.min_output_bytes_;
}
inline int64_t OptionsProto::min_output_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.min_output_bytes)
  return _internal_min_output_bytes();
}
inline void OptionsProto::_internal_set_min_output_bytes(int64_t value) {
  
  _impl_.min_output_bytes_ = value;
}
inline void OptionsProto::set_min_output_bytes(int64_t value) {
  _internal_set_min_output_bytes(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.min_output_bytes)
}

// int64 min_micros = 3;
inline void OptionsProto::clear_min_micros() {
  _impl_.min_micros_ = int64_t{0};
}
inline int64_t OptionsProto::_internal_min_micros() const {
  return _impl_.min_micros_;
}
inline int64_t OptionsProto::min_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.min_micros)
  return _internal_min_micros();
}
inline void OptionsProto::_internal_set_min_micros(int64_t value) {
  
  _impl_.min_micros_ = value;
}
inline void OptionsProto::set_min_micros(int64_t value) {
  _internal_set_min_micros(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.min_micros)
}

// int64 min_accelerator_micros = 22;
inline void OptionsProto::clear_min_accelerator_micros() {
  _impl_.min_accelerator_micros_ = int64_t{0};
}
inline int64_t OptionsProto::_internal_min_accelerator_micros() const {
  return _impl_.min_accelerator_micros_;
}
inline int64_t OptionsProto::min_accelerator_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.min_accelerator_micros)
  return _internal_min_accelerator_micros();
}
inline void OptionsProto::_internal_set_min_accelerator_micros(int64_t value) {
  
  _impl_.min_accelerator_micros_ = value;
}
inline void OptionsProto::set_min_accelerator_micros(int64_t value) {
  _internal_set_min_accelerator_micros(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.min_accelerator_micros)
}

// int64 min_cpu_micros = 23;
inline void OptionsProto::clear_min_cpu_micros() {
  _impl_.min_cpu_micros_ = int64_t{0};
}
inline int64_t OptionsProto::_internal_min_cpu_micros() const {
  return _impl_.min_cpu_micros_;
}
inline int64_t OptionsProto::min_cpu_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.min_cpu_micros)
  return _internal_min_cpu_micros();
}
inline void OptionsProto::_internal_set_min_cpu_micros(int64_t value) {
  
  _impl_.min_cpu_micros_ = value;
}
inline void OptionsProto::set_min_cpu_micros(int64_t value) {
  _internal_set_min_cpu_micros(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.min_cpu_micros)
}

// int64 min_params = 4;
inline void OptionsProto::clear_min_params() {
  _impl_.min_params_ = int64_t{0};
}
inline int64_t OptionsProto::_internal_min_params() const {
  return _impl_.min_params_;
}
inline int64_t OptionsProto::min_params() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.min_params)
  return _internal_min_params();
}
inline void OptionsProto::_internal_set_min_params(int64_t value) {
  
  _impl_.min_params_ = value;
}
inline void OptionsProto::set_min_params(int64_t value) {
  _internal_set_min_params(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.min_params)
}

// int64 min_float_ops = 5;
inline void OptionsProto::clear_min_float_ops() {
  _impl_.min_float_ops_ = int64_t{0};
}
inline int64_t OptionsProto::_internal_min_float_ops() const {
  return _impl_.min_float_ops_;
}
inline int64_t OptionsProto::min_float_ops() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.min_float_ops)
  return _internal_min_float_ops();
}
inline void OptionsProto::_internal_set_min_float_ops(int64_t value) {
  
  _impl_.min_float_ops_ = value;
}
inline void OptionsProto::set_min_float_ops(int64_t value) {
  _internal_set_min_float_ops(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.min_float_ops)
}

// int64 min_occurrence = 17;
inline void OptionsProto::clear_min_occurrence() {
  _impl_.min_occurrence_ = int64_t{0};
}
inline int64_t OptionsProto::_internal_min_occurrence() const {
  return _impl_.min_occurrence_;
}
inline int64_t OptionsProto::min_occurrence() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.min_occurrence)
  return _internal_min_occurrence();
}
inline void OptionsProto::_internal_set_min_occurrence(int64_t value) {
  
  _impl_.min_occurrence_ = value;
}
inline void OptionsProto::set_min_occurrence(int64_t value) {
  _internal_set_min_occurrence(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.min_occurrence)
}

// int64 step = 18;
inline void OptionsProto::clear_step() {
  _impl_.step_ = int64_t{0};
}
inline int64_t OptionsProto::_internal_step() const {
  return _impl_.step_;
}
inline int64_t OptionsProto::step() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.step)
  return _internal_step();
}
inline void OptionsProto::_internal_set_step(int64_t value) {
  
  _impl_.step_ = value;
}
inline void OptionsProto::set_step(int64_t value) {
  _internal_set_step(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.step)
}

// string order_by = 7;
inline void OptionsProto::clear_order_by() {
  _impl_.order_by_.ClearToEmpty();
}
inline const std::string& OptionsProto::order_by() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.order_by)
  return _internal_order_by();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void OptionsProto::set_order_by(ArgT0&& arg0, ArgT... args) {
 
 _impl_.order_by_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.order_by)
}
inline std::string* OptionsProto::mutable_order_by() {
  std::string* _s = _internal_mutable_order_by();
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.OptionsProto.order_by)
  return _s;
}
inline const std::string& OptionsProto::_internal_order_by() const {
  return _impl_.order_by_.Get();
}
inline void OptionsProto::_internal_set_order_by(const std::string& value) {
  
  _impl_.order_by_.Set(value, GetArenaForAllocation());
}
inline std::string* OptionsProto::_internal_mutable_order_by() {
  
  return _impl_.order_by_.Mutable(GetArenaForAllocation());
}
inline std::string* OptionsProto::release_order_by() {
  // @@protoc_insertion_point(field_release:tensorflow.tfprof.OptionsProto.order_by)
  return _impl_.order_by_.Release();
}
inline void OptionsProto::set_allocated_order_by(std::string* order_by) {
  if (order_by != nullptr) {
    
  } else {
    
  }
  _impl_.order_by_.SetAllocated(order_by, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.order_by_.IsDefault()) {
    _impl_.order_by_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tfprof.OptionsProto.order_by)
}

// repeated string account_type_regexes = 8;
inline int OptionsProto::_internal_account_type_regexes_size() const {
  return _impl_.account_type_regexes_.size();
}
inline int OptionsProto::account_type_regexes_size() const {
  return _internal_account_type_regexes_size();
}
inline void OptionsProto::clear_account_type_regexes() {
  _impl_.account_type_regexes_.Clear();
}
inline std::string* OptionsProto::add_account_type_regexes() {
  std::string* _s = _internal_add_account_type_regexes();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.tfprof.OptionsProto.account_type_regexes)
  return _s;
}
inline const std::string& OptionsProto::_internal_account_type_regexes(int index) const {
  return _impl_.account_type_regexes_.Get(index);
}
inline const std::string& OptionsProto::account_type_regexes(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.account_type_regexes)
  return _internal_account_type_regexes(index);
}
inline std::string* OptionsProto::mutable_account_type_regexes(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.OptionsProto.account_type_regexes)
  return _impl_.account_type_regexes_.Mutable(index);
}
inline void OptionsProto::set_account_type_regexes(int index, const std::string& value) {
  _impl_.account_type_regexes_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.account_type_regexes)
}
inline void OptionsProto::set_account_type_regexes(int index, std::string&& value) {
  _impl_.account_type_regexes_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.account_type_regexes)
}
inline void OptionsProto::set_account_type_regexes(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.account_type_regexes_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.tfprof.OptionsProto.account_type_regexes)
}
inline void OptionsProto::set_account_type_regexes(int index, const char* value, size_t size) {
  _impl_.account_type_regexes_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tfprof.OptionsProto.account_type_regexes)
}
inline std::string* OptionsProto::_internal_add_account_type_regexes() {
  return _impl_.account_type_regexes_.Add();
}
inline void OptionsProto::add_account_type_regexes(const std::string& value) {
  _impl_.account_type_regexes_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.OptionsProto.account_type_regexes)
}
inline void OptionsProto::add_account_type_regexes(std::string&& value) {
  _impl_.account_type_regexes_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.OptionsProto.account_type_regexes)
}
inline void OptionsProto::add_account_type_regexes(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.account_type_regexes_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.tfprof.OptionsProto.account_type_regexes)
}
inline void OptionsProto::add_account_type_regexes(const char* value, size_t size) {
  _impl_.account_type_regexes_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.tfprof.OptionsProto.account_type_regexes)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
OptionsProto::account_type_regexes() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.OptionsProto.account_type_regexes)
  return _impl_.account_type_regexes_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
OptionsProto::mutable_account_type_regexes() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.OptionsProto.account_type_regexes)
  return &_impl_.account_type_regexes_;
}

// repeated string start_name_regexes = 9;
inline int OptionsProto::_internal_start_name_regexes_size() const {
  return _impl_.start_name_regexes_.size();
}
inline int OptionsProto::start_name_regexes_size() const {
  return _internal_start_name_regexes_size();
}
inline void OptionsProto::clear_start_name_regexes() {
  _impl_.start_name_regexes_.Clear();
}
inline std::string* OptionsProto::add_start_name_regexes() {
  std::string* _s = _internal_add_start_name_regexes();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.tfprof.OptionsProto.start_name_regexes)
  return _s;
}
inline const std::string& OptionsProto::_internal_start_name_regexes(int index) const {
  return _impl_.start_name_regexes_.Get(index);
}
inline const std::string& OptionsProto::start_name_regexes(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.start_name_regexes)
  return _internal_start_name_regexes(index);
}
inline std::string* OptionsProto::mutable_start_name_regexes(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.OptionsProto.start_name_regexes)
  return _impl_.start_name_regexes_.Mutable(index);
}
inline void OptionsProto::set_start_name_regexes(int index, const std::string& value) {
  _impl_.start_name_regexes_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.start_name_regexes)
}
inline void OptionsProto::set_start_name_regexes(int index, std::string&& value) {
  _impl_.start_name_regexes_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.start_name_regexes)
}
inline void OptionsProto::set_start_name_regexes(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.start_name_regexes_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.tfprof.OptionsProto.start_name_regexes)
}
inline void OptionsProto::set_start_name_regexes(int index, const char* value, size_t size) {
  _impl_.start_name_regexes_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tfprof.OptionsProto.start_name_regexes)
}
inline std::string* OptionsProto::_internal_add_start_name_regexes() {
  return _impl_.start_name_regexes_.Add();
}
inline void OptionsProto::add_start_name_regexes(const std::string& value) {
  _impl_.start_name_regexes_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.OptionsProto.start_name_regexes)
}
inline void OptionsProto::add_start_name_regexes(std::string&& value) {
  _impl_.start_name_regexes_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.OptionsProto.start_name_regexes)
}
inline void OptionsProto::add_start_name_regexes(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.start_name_regexes_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.tfprof.OptionsProto.start_name_regexes)
}
inline void OptionsProto::add_start_name_regexes(const char* value, size_t size) {
  _impl_.start_name_regexes_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.tfprof.OptionsProto.start_name_regexes)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
OptionsProto::start_name_regexes() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.OptionsProto.start_name_regexes)
  return _impl_.start_name_regexes_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
OptionsProto::mutable_start_name_regexes() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.OptionsProto.start_name_regexes)
  return &_impl_.start_name_regexes_;
}

// repeated string trim_name_regexes = 10;
inline int OptionsProto::_internal_trim_name_regexes_size() const {
  return _impl_.trim_name_regexes_.size();
}
inline int OptionsProto::trim_name_regexes_size() const {
  return _internal_trim_name_regexes_size();
}
inline void OptionsProto::clear_trim_name_regexes() {
  _impl_.trim_name_regexes_.Clear();
}
inline std::string* OptionsProto::add_trim_name_regexes() {
  std::string* _s = _internal_add_trim_name_regexes();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.tfprof.OptionsProto.trim_name_regexes)
  return _s;
}
inline const std::string& OptionsProto::_internal_trim_name_regexes(int index) const {
  return _impl_.trim_name_regexes_.Get(index);
}
inline const std::string& OptionsProto::trim_name_regexes(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.trim_name_regexes)
  return _internal_trim_name_regexes(index);
}
inline std::string* OptionsProto::mutable_trim_name_regexes(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.OptionsProto.trim_name_regexes)
  return _impl_.trim_name_regexes_.Mutable(index);
}
inline void OptionsProto::set_trim_name_regexes(int index, const std::string& value) {
  _impl_.trim_name_regexes_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.trim_name_regexes)
}
inline void OptionsProto::set_trim_name_regexes(int index, std::string&& value) {
  _impl_.trim_name_regexes_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.trim_name_regexes)
}
inline void OptionsProto::set_trim_name_regexes(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.trim_name_regexes_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.tfprof.OptionsProto.trim_name_regexes)
}
inline void OptionsProto::set_trim_name_regexes(int index, const char* value, size_t size) {
  _impl_.trim_name_regexes_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tfprof.OptionsProto.trim_name_regexes)
}
inline std::string* OptionsProto::_internal_add_trim_name_regexes() {
  return _impl_.trim_name_regexes_.Add();
}
inline void OptionsProto::add_trim_name_regexes(const std::string& value) {
  _impl_.trim_name_regexes_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.OptionsProto.trim_name_regexes)
}
inline void OptionsProto::add_trim_name_regexes(std::string&& value) {
  _impl_.trim_name_regexes_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.OptionsProto.trim_name_regexes)
}
inline void OptionsProto::add_trim_name_regexes(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.trim_name_regexes_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.tfprof.OptionsProto.trim_name_regexes)
}
inline void OptionsProto::add_trim_name_regexes(const char* value, size_t size) {
  _impl_.trim_name_regexes_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.tfprof.OptionsProto.trim_name_regexes)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
OptionsProto::trim_name_regexes() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.OptionsProto.trim_name_regexes)
  return _impl_.trim_name_regexes_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
OptionsProto::mutable_trim_name_regexes() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.OptionsProto.trim_name_regexes)
  return &_impl_.trim_name_regexes_;
}

// repeated string show_name_regexes = 11;
inline int OptionsProto::_internal_show_name_regexes_size() const {
  return _impl_.show_name_regexes_.size();
}
inline int OptionsProto::show_name_regexes_size() const {
  return _internal_show_name_regexes_size();
}
inline void OptionsProto::clear_show_name_regexes() {
  _impl_.show_name_regexes_.Clear();
}
inline std::string* OptionsProto::add_show_name_regexes() {
  std::string* _s = _internal_add_show_name_regexes();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.tfprof.OptionsProto.show_name_regexes)
  return _s;
}
inline const std::string& OptionsProto::_internal_show_name_regexes(int index) const {
  return _impl_.show_name_regexes_.Get(index);
}
inline const std::string& OptionsProto::show_name_regexes(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.show_name_regexes)
  return _internal_show_name_regexes(index);
}
inline std::string* OptionsProto::mutable_show_name_regexes(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.OptionsProto.show_name_regexes)
  return _impl_.show_name_regexes_.Mutable(index);
}
inline void OptionsProto::set_show_name_regexes(int index, const std::string& value) {
  _impl_.show_name_regexes_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.show_name_regexes)
}
inline void OptionsProto::set_show_name_regexes(int index, std::string&& value) {
  _impl_.show_name_regexes_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.show_name_regexes)
}
inline void OptionsProto::set_show_name_regexes(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.show_name_regexes_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.tfprof.OptionsProto.show_name_regexes)
}
inline void OptionsProto::set_show_name_regexes(int index, const char* value, size_t size) {
  _impl_.show_name_regexes_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tfprof.OptionsProto.show_name_regexes)
}
inline std::string* OptionsProto::_internal_add_show_name_regexes() {
  return _impl_.show_name_regexes_.Add();
}
inline void OptionsProto::add_show_name_regexes(const std::string& value) {
  _impl_.show_name_regexes_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.OptionsProto.show_name_regexes)
}
inline void OptionsProto::add_show_name_regexes(std::string&& value) {
  _impl_.show_name_regexes_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.OptionsProto.show_name_regexes)
}
inline void OptionsProto::add_show_name_regexes(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.show_name_regexes_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.tfprof.OptionsProto.show_name_regexes)
}
inline void OptionsProto::add_show_name_regexes(const char* value, size_t size) {
  _impl_.show_name_regexes_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.tfprof.OptionsProto.show_name_regexes)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
OptionsProto::show_name_regexes() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.OptionsProto.show_name_regexes)
  return _impl_.show_name_regexes_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
OptionsProto::mutable_show_name_regexes() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.OptionsProto.show_name_regexes)
  return &_impl_.show_name_regexes_;
}

// repeated string hide_name_regexes = 12;
inline int OptionsProto::_internal_hide_name_regexes_size() const {
  return _impl_.hide_name_regexes_.size();
}
inline int OptionsProto::hide_name_regexes_size() const {
  return _internal_hide_name_regexes_size();
}
inline void OptionsProto::clear_hide_name_regexes() {
  _impl_.hide_name_regexes_.Clear();
}
inline std::string* OptionsProto::add_hide_name_regexes() {
  std::string* _s = _internal_add_hide_name_regexes();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.tfprof.OptionsProto.hide_name_regexes)
  return _s;
}
inline const std::string& OptionsProto::_internal_hide_name_regexes(int index) const {
  return _impl_.hide_name_regexes_.Get(index);
}
inline const std::string& OptionsProto::hide_name_regexes(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.hide_name_regexes)
  return _internal_hide_name_regexes(index);
}
inline std::string* OptionsProto::mutable_hide_name_regexes(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.OptionsProto.hide_name_regexes)
  return _impl_.hide_name_regexes_.Mutable(index);
}
inline void OptionsProto::set_hide_name_regexes(int index, const std::string& value) {
  _impl_.hide_name_regexes_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.hide_name_regexes)
}
inline void OptionsProto::set_hide_name_regexes(int index, std::string&& value) {
  _impl_.hide_name_regexes_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.hide_name_regexes)
}
inline void OptionsProto::set_hide_name_regexes(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.hide_name_regexes_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.tfprof.OptionsProto.hide_name_regexes)
}
inline void OptionsProto::set_hide_name_regexes(int index, const char* value, size_t size) {
  _impl_.hide_name_regexes_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tfprof.OptionsProto.hide_name_regexes)
}
inline std::string* OptionsProto::_internal_add_hide_name_regexes() {
  return _impl_.hide_name_regexes_.Add();
}
inline void OptionsProto::add_hide_name_regexes(const std::string& value) {
  _impl_.hide_name_regexes_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.OptionsProto.hide_name_regexes)
}
inline void OptionsProto::add_hide_name_regexes(std::string&& value) {
  _impl_.hide_name_regexes_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.OptionsProto.hide_name_regexes)
}
inline void OptionsProto::add_hide_name_regexes(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.hide_name_regexes_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.tfprof.OptionsProto.hide_name_regexes)
}
inline void OptionsProto::add_hide_name_regexes(const char* value, size_t size) {
  _impl_.hide_name_regexes_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.tfprof.OptionsProto.hide_name_regexes)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
OptionsProto::hide_name_regexes() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.OptionsProto.hide_name_regexes)
  return _impl_.hide_name_regexes_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
OptionsProto::mutable_hide_name_regexes() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.OptionsProto.hide_name_regexes)
  return &_impl_.hide_name_regexes_;
}

// bool account_displayed_op_only = 13;
inline void OptionsProto::clear_account_displayed_op_only() {
  _impl_.account_displayed_op_only_ = false;
}
inline bool OptionsProto::_internal_account_displayed_op_only() const {
  return _impl_.account_displayed_op_only_;
}
inline bool OptionsProto::account_displayed_op_only() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.account_displayed_op_only)
  return _internal_account_displayed_op_only();
}
inline void OptionsProto::_internal_set_account_displayed_op_only(bool value) {
  
  _impl_.account_displayed_op_only_ = value;
}
inline void OptionsProto::set_account_displayed_op_only(bool value) {
  _internal_set_account_displayed_op_only(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.account_displayed_op_only)
}

// repeated string select = 14;
inline int OptionsProto::_internal_select_size() const {
  return _impl_.select_.size();
}
inline int OptionsProto::select_size() const {
  return _internal_select_size();
}
inline void OptionsProto::clear_select() {
  _impl_.select_.Clear();
}
inline std::string* OptionsProto::add_select() {
  std::string* _s = _internal_add_select();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.tfprof.OptionsProto.select)
  return _s;
}
inline const std::string& OptionsProto::_internal_select(int index) const {
  return _impl_.select_.Get(index);
}
inline const std::string& OptionsProto::select(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.select)
  return _internal_select(index);
}
inline std::string* OptionsProto::mutable_select(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.OptionsProto.select)
  return _impl_.select_.Mutable(index);
}
inline void OptionsProto::set_select(int index, const std::string& value) {
  _impl_.select_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.select)
}
inline void OptionsProto::set_select(int index, std::string&& value) {
  _impl_.select_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.select)
}
inline void OptionsProto::set_select(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.select_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.tfprof.OptionsProto.select)
}
inline void OptionsProto::set_select(int index, const char* value, size_t size) {
  _impl_.select_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tfprof.OptionsProto.select)
}
inline std::string* OptionsProto::_internal_add_select() {
  return _impl_.select_.Add();
}
inline void OptionsProto::add_select(const std::string& value) {
  _impl_.select_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.OptionsProto.select)
}
inline void OptionsProto::add_select(std::string&& value) {
  _impl_.select_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.OptionsProto.select)
}
inline void OptionsProto::add_select(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.select_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.tfprof.OptionsProto.select)
}
inline void OptionsProto::add_select(const char* value, size_t size) {
  _impl_.select_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.tfprof.OptionsProto.select)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
OptionsProto::select() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.OptionsProto.select)
  return _impl_.select_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
OptionsProto::mutable_select() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.OptionsProto.select)
  return &_impl_.select_;
}

// string output = 15;
inline void OptionsProto::clear_output() {
  _impl_.output_.ClearToEmpty();
}
inline const std::string& OptionsProto::output() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.output)
  return _internal_output();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void OptionsProto::set_output(ArgT0&& arg0, ArgT... args) {
 
 _impl_.output_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.output)
}
inline std::string* OptionsProto::mutable_output() {
  std::string* _s = _internal_mutable_output();
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.OptionsProto.output)
  return _s;
}
inline const std::string& OptionsProto::_internal_output() const {
  return _impl_.output_.Get();
}
inline void OptionsProto::_internal_set_output(const std::string& value) {
  
  _impl_.output_.Set(value, GetArenaForAllocation());
}
inline std::string* OptionsProto::_internal_mutable_output() {
  
  return _impl_.output_.Mutable(GetArenaForAllocation());
}
inline std::string* OptionsProto::release_output() {
  // @@protoc_insertion_point(field_release:tensorflow.tfprof.OptionsProto.output)
  return _impl_.output_.Release();
}
inline void OptionsProto::set_allocated_output(std::string* output) {
  if (output != nullptr) {
    
  } else {
    
  }
  _impl_.output_.SetAllocated(output, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.output_.IsDefault()) {
    _impl_.output_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tfprof.OptionsProto.output)
}

// string dump_to_file = 16;
inline void OptionsProto::clear_dump_to_file() {
  _impl_.dump_to_file_.ClearToEmpty();
}
inline const std::string& OptionsProto::dump_to_file() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OptionsProto.dump_to_file)
  return _internal_dump_to_file();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void OptionsProto::set_dump_to_file(ArgT0&& arg0, ArgT... args) {
 
 _impl_.dump_to_file_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OptionsProto.dump_to_file)
}
inline std::string* OptionsProto::mutable_dump_to_file() {
  std::string* _s = _internal_mutable_dump_to_file();
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.OptionsProto.dump_to_file)
  return _s;
}
inline const std::string& OptionsProto::_internal_dump_to_file() const {
  return _impl_.dump_to_file_.Get();
}
inline void OptionsProto::_internal_set_dump_to_file(const std::string& value) {
  
  _impl_.dump_to_file_.Set(value, GetArenaForAllocation());
}
inline std::string* OptionsProto::_internal_mutable_dump_to_file() {
  
  return _impl_.dump_to_file_.Mutable(GetArenaForAllocation());
}
inline std::string* OptionsProto::release_dump_to_file() {
  // @@protoc_insertion_point(field_release:tensorflow.tfprof.OptionsProto.dump_to_file)
  return _impl_.dump_to_file_.Release();
}
inline void OptionsProto::set_allocated_dump_to_file(std::string* dump_to_file) {
  if (dump_to_file != nullptr) {
    
  } else {
    
  }
  _impl_.dump_to_file_.SetAllocated(dump_to_file, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.dump_to_file_.IsDefault()) {
    _impl_.dump_to_file_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tfprof.OptionsProto.dump_to_file)
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// AdvisorOptionsProto_CheckerOption

// map<string, string> options = 1;
inline int AdvisorOptionsProto_CheckerOption::_internal_options_size() const {
  return _impl_.options_.size();
}
inline int AdvisorOptionsProto_CheckerOption::options_size() const {
  return _internal_options_size();
}
inline void AdvisorOptionsProto_CheckerOption::clear_options() {
  _impl_.options_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
AdvisorOptionsProto_CheckerOption::_internal_options() const {
  return _impl_.options_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
AdvisorOptionsProto_CheckerOption::options() const {
  // @@protoc_insertion_point(field_map:tensorflow.tfprof.AdvisorOptionsProto.CheckerOption.options)
  return _internal_options();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
AdvisorOptionsProto_CheckerOption::_internal_mutable_options() {
  return _impl_.options_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
AdvisorOptionsProto_CheckerOption::mutable_options() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.tfprof.AdvisorOptionsProto.CheckerOption.options)
  return _internal_mutable_options();
}

// -------------------------------------------------------------------

// AdvisorOptionsProto

// map<string, .tensorflow.tfprof.AdvisorOptionsProto.CheckerOption> checkers = 1;
inline int AdvisorOptionsProto::_internal_checkers_size() const {
  return _impl_.checkers_.size();
}
inline int AdvisorOptionsProto::checkers_size() const {
  return _internal_checkers_size();
}
inline void AdvisorOptionsProto::clear_checkers() {
  _impl_.checkers_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption >&
AdvisorOptionsProto::_internal_checkers() const {
  return _impl_.checkers_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption >&
AdvisorOptionsProto::checkers() const {
  // @@protoc_insertion_point(field_map:tensorflow.tfprof.AdvisorOptionsProto.checkers)
  return _internal_checkers();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption >*
AdvisorOptionsProto::_internal_mutable_checkers() {
  return _impl_.checkers_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::tfprof::AdvisorOptionsProto_CheckerOption >*
AdvisorOptionsProto::mutable_checkers() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.tfprof.AdvisorOptionsProto.checkers)
  return _internal_mutable_checkers();
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tfprof
}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprofiler_2ftfprof_5foptions_2eproto
