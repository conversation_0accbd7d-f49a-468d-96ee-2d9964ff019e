.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_trust_list_add_system_trust" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_trust_list_add_system_trust \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_trust_list_add_system_trust(gnutls_x509_trust_list_t " list ", unsigned int " tl_flags ", unsigned int " tl_vflags ");"
.SH ARGUMENTS
.IP "gnutls_x509_trust_list_t list" 12
The structure of the list
.IP "unsigned int tl_flags" 12
GNUTLS_TL_*
.IP "unsigned int tl_vflags" 12
gnutls_certificate_verify_flags if flags specifies GNUTLS_TL_VERIFY_CRL
.SH "DESCRIPTION"
This function adds the system's default trusted certificate
authorities to the trusted list. Note that on unsupported systems
this function returns \fBGNUTLS_E_UNIMPLEMENTED_FEATURE\fP.

This function implies the flag \fBGNUTLS_TL_NO_DUPLICATES\fP.
.SH "RETURNS"
The number of added elements or a negative error code on error.
.SH "SINCE"
3.1
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
