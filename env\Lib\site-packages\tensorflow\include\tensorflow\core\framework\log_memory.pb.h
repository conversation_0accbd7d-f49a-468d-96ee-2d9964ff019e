// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/framework/log_memory.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/tensor_description.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto;
namespace tensorflow {
class MemoryLogRawAllocation;
struct MemoryLogRawAllocationDefaultTypeInternal;
extern MemoryLogRawAllocationDefaultTypeInternal _MemoryLogRawAllocation_default_instance_;
class MemoryLogRawDeallocation;
struct MemoryLogRawDeallocationDefaultTypeInternal;
extern MemoryLogRawDeallocationDefaultTypeInternal _MemoryLogRawDeallocation_default_instance_;
class MemoryLogStep;
struct MemoryLogStepDefaultTypeInternal;
extern MemoryLogStepDefaultTypeInternal _MemoryLogStep_default_instance_;
class MemoryLogTensorAllocation;
struct MemoryLogTensorAllocationDefaultTypeInternal;
extern MemoryLogTensorAllocationDefaultTypeInternal _MemoryLogTensorAllocation_default_instance_;
class MemoryLogTensorDeallocation;
struct MemoryLogTensorDeallocationDefaultTypeInternal;
extern MemoryLogTensorDeallocationDefaultTypeInternal _MemoryLogTensorDeallocation_default_instance_;
class MemoryLogTensorOutput;
struct MemoryLogTensorOutputDefaultTypeInternal;
extern MemoryLogTensorOutputDefaultTypeInternal _MemoryLogTensorOutput_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::MemoryLogRawAllocation* Arena::CreateMaybeMessage<::tensorflow::MemoryLogRawAllocation>(Arena*);
template<> ::tensorflow::MemoryLogRawDeallocation* Arena::CreateMaybeMessage<::tensorflow::MemoryLogRawDeallocation>(Arena*);
template<> ::tensorflow::MemoryLogStep* Arena::CreateMaybeMessage<::tensorflow::MemoryLogStep>(Arena*);
template<> ::tensorflow::MemoryLogTensorAllocation* Arena::CreateMaybeMessage<::tensorflow::MemoryLogTensorAllocation>(Arena*);
template<> ::tensorflow::MemoryLogTensorDeallocation* Arena::CreateMaybeMessage<::tensorflow::MemoryLogTensorDeallocation>(Arena*);
template<> ::tensorflow::MemoryLogTensorOutput* Arena::CreateMaybeMessage<::tensorflow::MemoryLogTensorOutput>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

// ===================================================================

class MemoryLogStep final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.MemoryLogStep) */ {
 public:
  inline MemoryLogStep() : MemoryLogStep(nullptr) {}
  ~MemoryLogStep() override;
  explicit PROTOBUF_CONSTEXPR MemoryLogStep(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MemoryLogStep(const MemoryLogStep& from);
  MemoryLogStep(MemoryLogStep&& from) noexcept
    : MemoryLogStep() {
    *this = ::std::move(from);
  }

  inline MemoryLogStep& operator=(const MemoryLogStep& from) {
    CopyFrom(from);
    return *this;
  }
  inline MemoryLogStep& operator=(MemoryLogStep&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MemoryLogStep& default_instance() {
    return *internal_default_instance();
  }
  static inline const MemoryLogStep* internal_default_instance() {
    return reinterpret_cast<const MemoryLogStep*>(
               &_MemoryLogStep_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(MemoryLogStep& a, MemoryLogStep& b) {
    a.Swap(&b);
  }
  inline void Swap(MemoryLogStep* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MemoryLogStep* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MemoryLogStep* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MemoryLogStep>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MemoryLogStep& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const MemoryLogStep& from) {
    MemoryLogStep::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MemoryLogStep* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.MemoryLogStep";
  }
  protected:
  explicit MemoryLogStep(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kHandleFieldNumber = 2,
    kStepIdFieldNumber = 1,
  };
  // string handle = 2;
  void clear_handle();
  const std::string& handle() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_handle(ArgT0&& arg0, ArgT... args);
  std::string* mutable_handle();
  PROTOBUF_NODISCARD std::string* release_handle();
  void set_allocated_handle(std::string* handle);
  private:
  const std::string& _internal_handle() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_handle(const std::string& value);
  std::string* _internal_mutable_handle();
  public:

  // int64 step_id = 1;
  void clear_step_id();
  int64_t step_id() const;
  void set_step_id(int64_t value);
  private:
  int64_t _internal_step_id() const;
  void _internal_set_step_id(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.MemoryLogStep)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr handle_;
    int64_t step_id_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto;
};
// -------------------------------------------------------------------

class MemoryLogTensorAllocation final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.MemoryLogTensorAllocation) */ {
 public:
  inline MemoryLogTensorAllocation() : MemoryLogTensorAllocation(nullptr) {}
  ~MemoryLogTensorAllocation() override;
  explicit PROTOBUF_CONSTEXPR MemoryLogTensorAllocation(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MemoryLogTensorAllocation(const MemoryLogTensorAllocation& from);
  MemoryLogTensorAllocation(MemoryLogTensorAllocation&& from) noexcept
    : MemoryLogTensorAllocation() {
    *this = ::std::move(from);
  }

  inline MemoryLogTensorAllocation& operator=(const MemoryLogTensorAllocation& from) {
    CopyFrom(from);
    return *this;
  }
  inline MemoryLogTensorAllocation& operator=(MemoryLogTensorAllocation&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MemoryLogTensorAllocation& default_instance() {
    return *internal_default_instance();
  }
  static inline const MemoryLogTensorAllocation* internal_default_instance() {
    return reinterpret_cast<const MemoryLogTensorAllocation*>(
               &_MemoryLogTensorAllocation_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(MemoryLogTensorAllocation& a, MemoryLogTensorAllocation& b) {
    a.Swap(&b);
  }
  inline void Swap(MemoryLogTensorAllocation* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MemoryLogTensorAllocation* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MemoryLogTensorAllocation* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MemoryLogTensorAllocation>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MemoryLogTensorAllocation& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const MemoryLogTensorAllocation& from) {
    MemoryLogTensorAllocation::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MemoryLogTensorAllocation* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.MemoryLogTensorAllocation";
  }
  protected:
  explicit MemoryLogTensorAllocation(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kKernelNameFieldNumber = 2,
    kTensorFieldNumber = 3,
    kStepIdFieldNumber = 1,
  };
  // string kernel_name = 2;
  void clear_kernel_name();
  const std::string& kernel_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_kernel_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_kernel_name();
  PROTOBUF_NODISCARD std::string* release_kernel_name();
  void set_allocated_kernel_name(std::string* kernel_name);
  private:
  const std::string& _internal_kernel_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_kernel_name(const std::string& value);
  std::string* _internal_mutable_kernel_name();
  public:

  // .tensorflow.TensorDescription tensor = 3;
  bool has_tensor() const;
  private:
  bool _internal_has_tensor() const;
  public:
  void clear_tensor();
  const ::tensorflow::TensorDescription& tensor() const;
  PROTOBUF_NODISCARD ::tensorflow::TensorDescription* release_tensor();
  ::tensorflow::TensorDescription* mutable_tensor();
  void set_allocated_tensor(::tensorflow::TensorDescription* tensor);
  private:
  const ::tensorflow::TensorDescription& _internal_tensor() const;
  ::tensorflow::TensorDescription* _internal_mutable_tensor();
  public:
  void unsafe_arena_set_allocated_tensor(
      ::tensorflow::TensorDescription* tensor);
  ::tensorflow::TensorDescription* unsafe_arena_release_tensor();

  // int64 step_id = 1;
  void clear_step_id();
  int64_t step_id() const;
  void set_step_id(int64_t value);
  private:
  int64_t _internal_step_id() const;
  void _internal_set_step_id(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.MemoryLogTensorAllocation)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr kernel_name_;
    ::tensorflow::TensorDescription* tensor_;
    int64_t step_id_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto;
};
// -------------------------------------------------------------------

class MemoryLogTensorDeallocation final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.MemoryLogTensorDeallocation) */ {
 public:
  inline MemoryLogTensorDeallocation() : MemoryLogTensorDeallocation(nullptr) {}
  ~MemoryLogTensorDeallocation() override;
  explicit PROTOBUF_CONSTEXPR MemoryLogTensorDeallocation(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MemoryLogTensorDeallocation(const MemoryLogTensorDeallocation& from);
  MemoryLogTensorDeallocation(MemoryLogTensorDeallocation&& from) noexcept
    : MemoryLogTensorDeallocation() {
    *this = ::std::move(from);
  }

  inline MemoryLogTensorDeallocation& operator=(const MemoryLogTensorDeallocation& from) {
    CopyFrom(from);
    return *this;
  }
  inline MemoryLogTensorDeallocation& operator=(MemoryLogTensorDeallocation&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MemoryLogTensorDeallocation& default_instance() {
    return *internal_default_instance();
  }
  static inline const MemoryLogTensorDeallocation* internal_default_instance() {
    return reinterpret_cast<const MemoryLogTensorDeallocation*>(
               &_MemoryLogTensorDeallocation_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(MemoryLogTensorDeallocation& a, MemoryLogTensorDeallocation& b) {
    a.Swap(&b);
  }
  inline void Swap(MemoryLogTensorDeallocation* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MemoryLogTensorDeallocation* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MemoryLogTensorDeallocation* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MemoryLogTensorDeallocation>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MemoryLogTensorDeallocation& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const MemoryLogTensorDeallocation& from) {
    MemoryLogTensorDeallocation::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MemoryLogTensorDeallocation* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.MemoryLogTensorDeallocation";
  }
  protected:
  explicit MemoryLogTensorDeallocation(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kAllocatorNameFieldNumber = 2,
    kAllocationIdFieldNumber = 1,
  };
  // string allocator_name = 2;
  void clear_allocator_name();
  const std::string& allocator_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_allocator_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_allocator_name();
  PROTOBUF_NODISCARD std::string* release_allocator_name();
  void set_allocated_allocator_name(std::string* allocator_name);
  private:
  const std::string& _internal_allocator_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_allocator_name(const std::string& value);
  std::string* _internal_mutable_allocator_name();
  public:

  // int64 allocation_id = 1;
  void clear_allocation_id();
  int64_t allocation_id() const;
  void set_allocation_id(int64_t value);
  private:
  int64_t _internal_allocation_id() const;
  void _internal_set_allocation_id(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.MemoryLogTensorDeallocation)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr allocator_name_;
    int64_t allocation_id_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto;
};
// -------------------------------------------------------------------

class MemoryLogTensorOutput final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.MemoryLogTensorOutput) */ {
 public:
  inline MemoryLogTensorOutput() : MemoryLogTensorOutput(nullptr) {}
  ~MemoryLogTensorOutput() override;
  explicit PROTOBUF_CONSTEXPR MemoryLogTensorOutput(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MemoryLogTensorOutput(const MemoryLogTensorOutput& from);
  MemoryLogTensorOutput(MemoryLogTensorOutput&& from) noexcept
    : MemoryLogTensorOutput() {
    *this = ::std::move(from);
  }

  inline MemoryLogTensorOutput& operator=(const MemoryLogTensorOutput& from) {
    CopyFrom(from);
    return *this;
  }
  inline MemoryLogTensorOutput& operator=(MemoryLogTensorOutput&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MemoryLogTensorOutput& default_instance() {
    return *internal_default_instance();
  }
  static inline const MemoryLogTensorOutput* internal_default_instance() {
    return reinterpret_cast<const MemoryLogTensorOutput*>(
               &_MemoryLogTensorOutput_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(MemoryLogTensorOutput& a, MemoryLogTensorOutput& b) {
    a.Swap(&b);
  }
  inline void Swap(MemoryLogTensorOutput* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MemoryLogTensorOutput* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MemoryLogTensorOutput* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MemoryLogTensorOutput>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MemoryLogTensorOutput& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const MemoryLogTensorOutput& from) {
    MemoryLogTensorOutput::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MemoryLogTensorOutput* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.MemoryLogTensorOutput";
  }
  protected:
  explicit MemoryLogTensorOutput(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kKernelNameFieldNumber = 2,
    kTensorFieldNumber = 4,
    kStepIdFieldNumber = 1,
    kIndexFieldNumber = 3,
  };
  // string kernel_name = 2;
  void clear_kernel_name();
  const std::string& kernel_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_kernel_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_kernel_name();
  PROTOBUF_NODISCARD std::string* release_kernel_name();
  void set_allocated_kernel_name(std::string* kernel_name);
  private:
  const std::string& _internal_kernel_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_kernel_name(const std::string& value);
  std::string* _internal_mutable_kernel_name();
  public:

  // .tensorflow.TensorDescription tensor = 4;
  bool has_tensor() const;
  private:
  bool _internal_has_tensor() const;
  public:
  void clear_tensor();
  const ::tensorflow::TensorDescription& tensor() const;
  PROTOBUF_NODISCARD ::tensorflow::TensorDescription* release_tensor();
  ::tensorflow::TensorDescription* mutable_tensor();
  void set_allocated_tensor(::tensorflow::TensorDescription* tensor);
  private:
  const ::tensorflow::TensorDescription& _internal_tensor() const;
  ::tensorflow::TensorDescription* _internal_mutable_tensor();
  public:
  void unsafe_arena_set_allocated_tensor(
      ::tensorflow::TensorDescription* tensor);
  ::tensorflow::TensorDescription* unsafe_arena_release_tensor();

  // int64 step_id = 1;
  void clear_step_id();
  int64_t step_id() const;
  void set_step_id(int64_t value);
  private:
  int64_t _internal_step_id() const;
  void _internal_set_step_id(int64_t value);
  public:

  // int32 index = 3;
  void clear_index();
  int32_t index() const;
  void set_index(int32_t value);
  private:
  int32_t _internal_index() const;
  void _internal_set_index(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.MemoryLogTensorOutput)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr kernel_name_;
    ::tensorflow::TensorDescription* tensor_;
    int64_t step_id_;
    int32_t index_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto;
};
// -------------------------------------------------------------------

class MemoryLogRawAllocation final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.MemoryLogRawAllocation) */ {
 public:
  inline MemoryLogRawAllocation() : MemoryLogRawAllocation(nullptr) {}
  ~MemoryLogRawAllocation() override;
  explicit PROTOBUF_CONSTEXPR MemoryLogRawAllocation(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MemoryLogRawAllocation(const MemoryLogRawAllocation& from);
  MemoryLogRawAllocation(MemoryLogRawAllocation&& from) noexcept
    : MemoryLogRawAllocation() {
    *this = ::std::move(from);
  }

  inline MemoryLogRawAllocation& operator=(const MemoryLogRawAllocation& from) {
    CopyFrom(from);
    return *this;
  }
  inline MemoryLogRawAllocation& operator=(MemoryLogRawAllocation&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MemoryLogRawAllocation& default_instance() {
    return *internal_default_instance();
  }
  static inline const MemoryLogRawAllocation* internal_default_instance() {
    return reinterpret_cast<const MemoryLogRawAllocation*>(
               &_MemoryLogRawAllocation_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(MemoryLogRawAllocation& a, MemoryLogRawAllocation& b) {
    a.Swap(&b);
  }
  inline void Swap(MemoryLogRawAllocation* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MemoryLogRawAllocation* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MemoryLogRawAllocation* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MemoryLogRawAllocation>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MemoryLogRawAllocation& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const MemoryLogRawAllocation& from) {
    MemoryLogRawAllocation::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MemoryLogRawAllocation* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.MemoryLogRawAllocation";
  }
  protected:
  explicit MemoryLogRawAllocation(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kOperationFieldNumber = 2,
    kAllocatorNameFieldNumber = 6,
    kStepIdFieldNumber = 1,
    kNumBytesFieldNumber = 3,
    kPtrFieldNumber = 4,
    kAllocationIdFieldNumber = 5,
  };
  // string operation = 2;
  void clear_operation();
  const std::string& operation() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_operation(ArgT0&& arg0, ArgT... args);
  std::string* mutable_operation();
  PROTOBUF_NODISCARD std::string* release_operation();
  void set_allocated_operation(std::string* operation);
  private:
  const std::string& _internal_operation() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_operation(const std::string& value);
  std::string* _internal_mutable_operation();
  public:

  // string allocator_name = 6;
  void clear_allocator_name();
  const std::string& allocator_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_allocator_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_allocator_name();
  PROTOBUF_NODISCARD std::string* release_allocator_name();
  void set_allocated_allocator_name(std::string* allocator_name);
  private:
  const std::string& _internal_allocator_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_allocator_name(const std::string& value);
  std::string* _internal_mutable_allocator_name();
  public:

  // int64 step_id = 1;
  void clear_step_id();
  int64_t step_id() const;
  void set_step_id(int64_t value);
  private:
  int64_t _internal_step_id() const;
  void _internal_set_step_id(int64_t value);
  public:

  // int64 num_bytes = 3;
  void clear_num_bytes();
  int64_t num_bytes() const;
  void set_num_bytes(int64_t value);
  private:
  int64_t _internal_num_bytes() const;
  void _internal_set_num_bytes(int64_t value);
  public:

  // uint64 ptr = 4;
  void clear_ptr();
  uint64_t ptr() const;
  void set_ptr(uint64_t value);
  private:
  uint64_t _internal_ptr() const;
  void _internal_set_ptr(uint64_t value);
  public:

  // int64 allocation_id = 5;
  void clear_allocation_id();
  int64_t allocation_id() const;
  void set_allocation_id(int64_t value);
  private:
  int64_t _internal_allocation_id() const;
  void _internal_set_allocation_id(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.MemoryLogRawAllocation)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr operation_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr allocator_name_;
    int64_t step_id_;
    int64_t num_bytes_;
    uint64_t ptr_;
    int64_t allocation_id_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto;
};
// -------------------------------------------------------------------

class MemoryLogRawDeallocation final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.MemoryLogRawDeallocation) */ {
 public:
  inline MemoryLogRawDeallocation() : MemoryLogRawDeallocation(nullptr) {}
  ~MemoryLogRawDeallocation() override;
  explicit PROTOBUF_CONSTEXPR MemoryLogRawDeallocation(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MemoryLogRawDeallocation(const MemoryLogRawDeallocation& from);
  MemoryLogRawDeallocation(MemoryLogRawDeallocation&& from) noexcept
    : MemoryLogRawDeallocation() {
    *this = ::std::move(from);
  }

  inline MemoryLogRawDeallocation& operator=(const MemoryLogRawDeallocation& from) {
    CopyFrom(from);
    return *this;
  }
  inline MemoryLogRawDeallocation& operator=(MemoryLogRawDeallocation&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MemoryLogRawDeallocation& default_instance() {
    return *internal_default_instance();
  }
  static inline const MemoryLogRawDeallocation* internal_default_instance() {
    return reinterpret_cast<const MemoryLogRawDeallocation*>(
               &_MemoryLogRawDeallocation_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(MemoryLogRawDeallocation& a, MemoryLogRawDeallocation& b) {
    a.Swap(&b);
  }
  inline void Swap(MemoryLogRawDeallocation* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MemoryLogRawDeallocation* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MemoryLogRawDeallocation* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MemoryLogRawDeallocation>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MemoryLogRawDeallocation& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const MemoryLogRawDeallocation& from) {
    MemoryLogRawDeallocation::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MemoryLogRawDeallocation* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.MemoryLogRawDeallocation";
  }
  protected:
  explicit MemoryLogRawDeallocation(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kOperationFieldNumber = 2,
    kAllocatorNameFieldNumber = 4,
    kStepIdFieldNumber = 1,
    kAllocationIdFieldNumber = 3,
    kDeferredFieldNumber = 5,
  };
  // string operation = 2;
  void clear_operation();
  const std::string& operation() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_operation(ArgT0&& arg0, ArgT... args);
  std::string* mutable_operation();
  PROTOBUF_NODISCARD std::string* release_operation();
  void set_allocated_operation(std::string* operation);
  private:
  const std::string& _internal_operation() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_operation(const std::string& value);
  std::string* _internal_mutable_operation();
  public:

  // string allocator_name = 4;
  void clear_allocator_name();
  const std::string& allocator_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_allocator_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_allocator_name();
  PROTOBUF_NODISCARD std::string* release_allocator_name();
  void set_allocated_allocator_name(std::string* allocator_name);
  private:
  const std::string& _internal_allocator_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_allocator_name(const std::string& value);
  std::string* _internal_mutable_allocator_name();
  public:

  // int64 step_id = 1;
  void clear_step_id();
  int64_t step_id() const;
  void set_step_id(int64_t value);
  private:
  int64_t _internal_step_id() const;
  void _internal_set_step_id(int64_t value);
  public:

  // int64 allocation_id = 3;
  void clear_allocation_id();
  int64_t allocation_id() const;
  void set_allocation_id(int64_t value);
  private:
  int64_t _internal_allocation_id() const;
  void _internal_set_allocation_id(int64_t value);
  public:

  // bool deferred = 5;
  void clear_deferred();
  bool deferred() const;
  void set_deferred(bool value);
  private:
  bool _internal_deferred() const;
  void _internal_set_deferred(bool value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.MemoryLogRawDeallocation)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr operation_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr allocator_name_;
    int64_t step_id_;
    int64_t allocation_id_;
    bool deferred_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// MemoryLogStep

// int64 step_id = 1;
inline void MemoryLogStep::clear_step_id() {
  _impl_.step_id_ = int64_t{0};
}
inline int64_t MemoryLogStep::_internal_step_id() const {
  return _impl_.step_id_;
}
inline int64_t MemoryLogStep::step_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryLogStep.step_id)
  return _internal_step_id();
}
inline void MemoryLogStep::_internal_set_step_id(int64_t value) {
  
  _impl_.step_id_ = value;
}
inline void MemoryLogStep::set_step_id(int64_t value) {
  _internal_set_step_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.MemoryLogStep.step_id)
}

// string handle = 2;
inline void MemoryLogStep::clear_handle() {
  _impl_.handle_.ClearToEmpty();
}
inline const std::string& MemoryLogStep::handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryLogStep.handle)
  return _internal_handle();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void MemoryLogStep::set_handle(ArgT0&& arg0, ArgT... args) {
 
 _impl_.handle_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.MemoryLogStep.handle)
}
inline std::string* MemoryLogStep::mutable_handle() {
  std::string* _s = _internal_mutable_handle();
  // @@protoc_insertion_point(field_mutable:tensorflow.MemoryLogStep.handle)
  return _s;
}
inline const std::string& MemoryLogStep::_internal_handle() const {
  return _impl_.handle_.Get();
}
inline void MemoryLogStep::_internal_set_handle(const std::string& value) {
  
  _impl_.handle_.Set(value, GetArenaForAllocation());
}
inline std::string* MemoryLogStep::_internal_mutable_handle() {
  
  return _impl_.handle_.Mutable(GetArenaForAllocation());
}
inline std::string* MemoryLogStep::release_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.MemoryLogStep.handle)
  return _impl_.handle_.Release();
}
inline void MemoryLogStep::set_allocated_handle(std::string* handle) {
  if (handle != nullptr) {
    
  } else {
    
  }
  _impl_.handle_.SetAllocated(handle, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.handle_.IsDefault()) {
    _impl_.handle_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MemoryLogStep.handle)
}

// -------------------------------------------------------------------

// MemoryLogTensorAllocation

// int64 step_id = 1;
inline void MemoryLogTensorAllocation::clear_step_id() {
  _impl_.step_id_ = int64_t{0};
}
inline int64_t MemoryLogTensorAllocation::_internal_step_id() const {
  return _impl_.step_id_;
}
inline int64_t MemoryLogTensorAllocation::step_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryLogTensorAllocation.step_id)
  return _internal_step_id();
}
inline void MemoryLogTensorAllocation::_internal_set_step_id(int64_t value) {
  
  _impl_.step_id_ = value;
}
inline void MemoryLogTensorAllocation::set_step_id(int64_t value) {
  _internal_set_step_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.MemoryLogTensorAllocation.step_id)
}

// string kernel_name = 2;
inline void MemoryLogTensorAllocation::clear_kernel_name() {
  _impl_.kernel_name_.ClearToEmpty();
}
inline const std::string& MemoryLogTensorAllocation::kernel_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryLogTensorAllocation.kernel_name)
  return _internal_kernel_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void MemoryLogTensorAllocation::set_kernel_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.kernel_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.MemoryLogTensorAllocation.kernel_name)
}
inline std::string* MemoryLogTensorAllocation::mutable_kernel_name() {
  std::string* _s = _internal_mutable_kernel_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.MemoryLogTensorAllocation.kernel_name)
  return _s;
}
inline const std::string& MemoryLogTensorAllocation::_internal_kernel_name() const {
  return _impl_.kernel_name_.Get();
}
inline void MemoryLogTensorAllocation::_internal_set_kernel_name(const std::string& value) {
  
  _impl_.kernel_name_.Set(value, GetArenaForAllocation());
}
inline std::string* MemoryLogTensorAllocation::_internal_mutable_kernel_name() {
  
  return _impl_.kernel_name_.Mutable(GetArenaForAllocation());
}
inline std::string* MemoryLogTensorAllocation::release_kernel_name() {
  // @@protoc_insertion_point(field_release:tensorflow.MemoryLogTensorAllocation.kernel_name)
  return _impl_.kernel_name_.Release();
}
inline void MemoryLogTensorAllocation::set_allocated_kernel_name(std::string* kernel_name) {
  if (kernel_name != nullptr) {
    
  } else {
    
  }
  _impl_.kernel_name_.SetAllocated(kernel_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.kernel_name_.IsDefault()) {
    _impl_.kernel_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MemoryLogTensorAllocation.kernel_name)
}

// .tensorflow.TensorDescription tensor = 3;
inline bool MemoryLogTensorAllocation::_internal_has_tensor() const {
  return this != internal_default_instance() && _impl_.tensor_ != nullptr;
}
inline bool MemoryLogTensorAllocation::has_tensor() const {
  return _internal_has_tensor();
}
inline const ::tensorflow::TensorDescription& MemoryLogTensorAllocation::_internal_tensor() const {
  const ::tensorflow::TensorDescription* p = _impl_.tensor_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::TensorDescription&>(
      ::tensorflow::_TensorDescription_default_instance_);
}
inline const ::tensorflow::TensorDescription& MemoryLogTensorAllocation::tensor() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryLogTensorAllocation.tensor)
  return _internal_tensor();
}
inline void MemoryLogTensorAllocation::unsafe_arena_set_allocated_tensor(
    ::tensorflow::TensorDescription* tensor) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.tensor_);
  }
  _impl_.tensor_ = tensor;
  if (tensor) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.MemoryLogTensorAllocation.tensor)
}
inline ::tensorflow::TensorDescription* MemoryLogTensorAllocation::release_tensor() {
  
  ::tensorflow::TensorDescription* temp = _impl_.tensor_;
  _impl_.tensor_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::TensorDescription* MemoryLogTensorAllocation::unsafe_arena_release_tensor() {
  // @@protoc_insertion_point(field_release:tensorflow.MemoryLogTensorAllocation.tensor)
  
  ::tensorflow::TensorDescription* temp = _impl_.tensor_;
  _impl_.tensor_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorDescription* MemoryLogTensorAllocation::_internal_mutable_tensor() {
  
  if (_impl_.tensor_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorDescription>(GetArenaForAllocation());
    _impl_.tensor_ = p;
  }
  return _impl_.tensor_;
}
inline ::tensorflow::TensorDescription* MemoryLogTensorAllocation::mutable_tensor() {
  ::tensorflow::TensorDescription* _msg = _internal_mutable_tensor();
  // @@protoc_insertion_point(field_mutable:tensorflow.MemoryLogTensorAllocation.tensor)
  return _msg;
}
inline void MemoryLogTensorAllocation::set_allocated_tensor(::tensorflow::TensorDescription* tensor) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.tensor_);
  }
  if (tensor) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(tensor));
    if (message_arena != submessage_arena) {
      tensor = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, tensor, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.tensor_ = tensor;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MemoryLogTensorAllocation.tensor)
}

// -------------------------------------------------------------------

// MemoryLogTensorDeallocation

// int64 allocation_id = 1;
inline void MemoryLogTensorDeallocation::clear_allocation_id() {
  _impl_.allocation_id_ = int64_t{0};
}
inline int64_t MemoryLogTensorDeallocation::_internal_allocation_id() const {
  return _impl_.allocation_id_;
}
inline int64_t MemoryLogTensorDeallocation::allocation_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryLogTensorDeallocation.allocation_id)
  return _internal_allocation_id();
}
inline void MemoryLogTensorDeallocation::_internal_set_allocation_id(int64_t value) {
  
  _impl_.allocation_id_ = value;
}
inline void MemoryLogTensorDeallocation::set_allocation_id(int64_t value) {
  _internal_set_allocation_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.MemoryLogTensorDeallocation.allocation_id)
}

// string allocator_name = 2;
inline void MemoryLogTensorDeallocation::clear_allocator_name() {
  _impl_.allocator_name_.ClearToEmpty();
}
inline const std::string& MemoryLogTensorDeallocation::allocator_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryLogTensorDeallocation.allocator_name)
  return _internal_allocator_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void MemoryLogTensorDeallocation::set_allocator_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.allocator_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.MemoryLogTensorDeallocation.allocator_name)
}
inline std::string* MemoryLogTensorDeallocation::mutable_allocator_name() {
  std::string* _s = _internal_mutable_allocator_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.MemoryLogTensorDeallocation.allocator_name)
  return _s;
}
inline const std::string& MemoryLogTensorDeallocation::_internal_allocator_name() const {
  return _impl_.allocator_name_.Get();
}
inline void MemoryLogTensorDeallocation::_internal_set_allocator_name(const std::string& value) {
  
  _impl_.allocator_name_.Set(value, GetArenaForAllocation());
}
inline std::string* MemoryLogTensorDeallocation::_internal_mutable_allocator_name() {
  
  return _impl_.allocator_name_.Mutable(GetArenaForAllocation());
}
inline std::string* MemoryLogTensorDeallocation::release_allocator_name() {
  // @@protoc_insertion_point(field_release:tensorflow.MemoryLogTensorDeallocation.allocator_name)
  return _impl_.allocator_name_.Release();
}
inline void MemoryLogTensorDeallocation::set_allocated_allocator_name(std::string* allocator_name) {
  if (allocator_name != nullptr) {
    
  } else {
    
  }
  _impl_.allocator_name_.SetAllocated(allocator_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.allocator_name_.IsDefault()) {
    _impl_.allocator_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MemoryLogTensorDeallocation.allocator_name)
}

// -------------------------------------------------------------------

// MemoryLogTensorOutput

// int64 step_id = 1;
inline void MemoryLogTensorOutput::clear_step_id() {
  _impl_.step_id_ = int64_t{0};
}
inline int64_t MemoryLogTensorOutput::_internal_step_id() const {
  return _impl_.step_id_;
}
inline int64_t MemoryLogTensorOutput::step_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryLogTensorOutput.step_id)
  return _internal_step_id();
}
inline void MemoryLogTensorOutput::_internal_set_step_id(int64_t value) {
  
  _impl_.step_id_ = value;
}
inline void MemoryLogTensorOutput::set_step_id(int64_t value) {
  _internal_set_step_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.MemoryLogTensorOutput.step_id)
}

// string kernel_name = 2;
inline void MemoryLogTensorOutput::clear_kernel_name() {
  _impl_.kernel_name_.ClearToEmpty();
}
inline const std::string& MemoryLogTensorOutput::kernel_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryLogTensorOutput.kernel_name)
  return _internal_kernel_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void MemoryLogTensorOutput::set_kernel_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.kernel_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.MemoryLogTensorOutput.kernel_name)
}
inline std::string* MemoryLogTensorOutput::mutable_kernel_name() {
  std::string* _s = _internal_mutable_kernel_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.MemoryLogTensorOutput.kernel_name)
  return _s;
}
inline const std::string& MemoryLogTensorOutput::_internal_kernel_name() const {
  return _impl_.kernel_name_.Get();
}
inline void MemoryLogTensorOutput::_internal_set_kernel_name(const std::string& value) {
  
  _impl_.kernel_name_.Set(value, GetArenaForAllocation());
}
inline std::string* MemoryLogTensorOutput::_internal_mutable_kernel_name() {
  
  return _impl_.kernel_name_.Mutable(GetArenaForAllocation());
}
inline std::string* MemoryLogTensorOutput::release_kernel_name() {
  // @@protoc_insertion_point(field_release:tensorflow.MemoryLogTensorOutput.kernel_name)
  return _impl_.kernel_name_.Release();
}
inline void MemoryLogTensorOutput::set_allocated_kernel_name(std::string* kernel_name) {
  if (kernel_name != nullptr) {
    
  } else {
    
  }
  _impl_.kernel_name_.SetAllocated(kernel_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.kernel_name_.IsDefault()) {
    _impl_.kernel_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MemoryLogTensorOutput.kernel_name)
}

// int32 index = 3;
inline void MemoryLogTensorOutput::clear_index() {
  _impl_.index_ = 0;
}
inline int32_t MemoryLogTensorOutput::_internal_index() const {
  return _impl_.index_;
}
inline int32_t MemoryLogTensorOutput::index() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryLogTensorOutput.index)
  return _internal_index();
}
inline void MemoryLogTensorOutput::_internal_set_index(int32_t value) {
  
  _impl_.index_ = value;
}
inline void MemoryLogTensorOutput::set_index(int32_t value) {
  _internal_set_index(value);
  // @@protoc_insertion_point(field_set:tensorflow.MemoryLogTensorOutput.index)
}

// .tensorflow.TensorDescription tensor = 4;
inline bool MemoryLogTensorOutput::_internal_has_tensor() const {
  return this != internal_default_instance() && _impl_.tensor_ != nullptr;
}
inline bool MemoryLogTensorOutput::has_tensor() const {
  return _internal_has_tensor();
}
inline const ::tensorflow::TensorDescription& MemoryLogTensorOutput::_internal_tensor() const {
  const ::tensorflow::TensorDescription* p = _impl_.tensor_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::TensorDescription&>(
      ::tensorflow::_TensorDescription_default_instance_);
}
inline const ::tensorflow::TensorDescription& MemoryLogTensorOutput::tensor() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryLogTensorOutput.tensor)
  return _internal_tensor();
}
inline void MemoryLogTensorOutput::unsafe_arena_set_allocated_tensor(
    ::tensorflow::TensorDescription* tensor) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.tensor_);
  }
  _impl_.tensor_ = tensor;
  if (tensor) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.MemoryLogTensorOutput.tensor)
}
inline ::tensorflow::TensorDescription* MemoryLogTensorOutput::release_tensor() {
  
  ::tensorflow::TensorDescription* temp = _impl_.tensor_;
  _impl_.tensor_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::TensorDescription* MemoryLogTensorOutput::unsafe_arena_release_tensor() {
  // @@protoc_insertion_point(field_release:tensorflow.MemoryLogTensorOutput.tensor)
  
  ::tensorflow::TensorDescription* temp = _impl_.tensor_;
  _impl_.tensor_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorDescription* MemoryLogTensorOutput::_internal_mutable_tensor() {
  
  if (_impl_.tensor_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorDescription>(GetArenaForAllocation());
    _impl_.tensor_ = p;
  }
  return _impl_.tensor_;
}
inline ::tensorflow::TensorDescription* MemoryLogTensorOutput::mutable_tensor() {
  ::tensorflow::TensorDescription* _msg = _internal_mutable_tensor();
  // @@protoc_insertion_point(field_mutable:tensorflow.MemoryLogTensorOutput.tensor)
  return _msg;
}
inline void MemoryLogTensorOutput::set_allocated_tensor(::tensorflow::TensorDescription* tensor) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.tensor_);
  }
  if (tensor) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(tensor));
    if (message_arena != submessage_arena) {
      tensor = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, tensor, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.tensor_ = tensor;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MemoryLogTensorOutput.tensor)
}

// -------------------------------------------------------------------

// MemoryLogRawAllocation

// int64 step_id = 1;
inline void MemoryLogRawAllocation::clear_step_id() {
  _impl_.step_id_ = int64_t{0};
}
inline int64_t MemoryLogRawAllocation::_internal_step_id() const {
  return _impl_.step_id_;
}
inline int64_t MemoryLogRawAllocation::step_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryLogRawAllocation.step_id)
  return _internal_step_id();
}
inline void MemoryLogRawAllocation::_internal_set_step_id(int64_t value) {
  
  _impl_.step_id_ = value;
}
inline void MemoryLogRawAllocation::set_step_id(int64_t value) {
  _internal_set_step_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.MemoryLogRawAllocation.step_id)
}

// string operation = 2;
inline void MemoryLogRawAllocation::clear_operation() {
  _impl_.operation_.ClearToEmpty();
}
inline const std::string& MemoryLogRawAllocation::operation() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryLogRawAllocation.operation)
  return _internal_operation();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void MemoryLogRawAllocation::set_operation(ArgT0&& arg0, ArgT... args) {
 
 _impl_.operation_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.MemoryLogRawAllocation.operation)
}
inline std::string* MemoryLogRawAllocation::mutable_operation() {
  std::string* _s = _internal_mutable_operation();
  // @@protoc_insertion_point(field_mutable:tensorflow.MemoryLogRawAllocation.operation)
  return _s;
}
inline const std::string& MemoryLogRawAllocation::_internal_operation() const {
  return _impl_.operation_.Get();
}
inline void MemoryLogRawAllocation::_internal_set_operation(const std::string& value) {
  
  _impl_.operation_.Set(value, GetArenaForAllocation());
}
inline std::string* MemoryLogRawAllocation::_internal_mutable_operation() {
  
  return _impl_.operation_.Mutable(GetArenaForAllocation());
}
inline std::string* MemoryLogRawAllocation::release_operation() {
  // @@protoc_insertion_point(field_release:tensorflow.MemoryLogRawAllocation.operation)
  return _impl_.operation_.Release();
}
inline void MemoryLogRawAllocation::set_allocated_operation(std::string* operation) {
  if (operation != nullptr) {
    
  } else {
    
  }
  _impl_.operation_.SetAllocated(operation, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.operation_.IsDefault()) {
    _impl_.operation_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MemoryLogRawAllocation.operation)
}

// int64 num_bytes = 3;
inline void MemoryLogRawAllocation::clear_num_bytes() {
  _impl_.num_bytes_ = int64_t{0};
}
inline int64_t MemoryLogRawAllocation::_internal_num_bytes() const {
  return _impl_.num_bytes_;
}
inline int64_t MemoryLogRawAllocation::num_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryLogRawAllocation.num_bytes)
  return _internal_num_bytes();
}
inline void MemoryLogRawAllocation::_internal_set_num_bytes(int64_t value) {
  
  _impl_.num_bytes_ = value;
}
inline void MemoryLogRawAllocation::set_num_bytes(int64_t value) {
  _internal_set_num_bytes(value);
  // @@protoc_insertion_point(field_set:tensorflow.MemoryLogRawAllocation.num_bytes)
}

// uint64 ptr = 4;
inline void MemoryLogRawAllocation::clear_ptr() {
  _impl_.ptr_ = uint64_t{0u};
}
inline uint64_t MemoryLogRawAllocation::_internal_ptr() const {
  return _impl_.ptr_;
}
inline uint64_t MemoryLogRawAllocation::ptr() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryLogRawAllocation.ptr)
  return _internal_ptr();
}
inline void MemoryLogRawAllocation::_internal_set_ptr(uint64_t value) {
  
  _impl_.ptr_ = value;
}
inline void MemoryLogRawAllocation::set_ptr(uint64_t value) {
  _internal_set_ptr(value);
  // @@protoc_insertion_point(field_set:tensorflow.MemoryLogRawAllocation.ptr)
}

// int64 allocation_id = 5;
inline void MemoryLogRawAllocation::clear_allocation_id() {
  _impl_.allocation_id_ = int64_t{0};
}
inline int64_t MemoryLogRawAllocation::_internal_allocation_id() const {
  return _impl_.allocation_id_;
}
inline int64_t MemoryLogRawAllocation::allocation_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryLogRawAllocation.allocation_id)
  return _internal_allocation_id();
}
inline void MemoryLogRawAllocation::_internal_set_allocation_id(int64_t value) {
  
  _impl_.allocation_id_ = value;
}
inline void MemoryLogRawAllocation::set_allocation_id(int64_t value) {
  _internal_set_allocation_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.MemoryLogRawAllocation.allocation_id)
}

// string allocator_name = 6;
inline void MemoryLogRawAllocation::clear_allocator_name() {
  _impl_.allocator_name_.ClearToEmpty();
}
inline const std::string& MemoryLogRawAllocation::allocator_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryLogRawAllocation.allocator_name)
  return _internal_allocator_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void MemoryLogRawAllocation::set_allocator_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.allocator_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.MemoryLogRawAllocation.allocator_name)
}
inline std::string* MemoryLogRawAllocation::mutable_allocator_name() {
  std::string* _s = _internal_mutable_allocator_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.MemoryLogRawAllocation.allocator_name)
  return _s;
}
inline const std::string& MemoryLogRawAllocation::_internal_allocator_name() const {
  return _impl_.allocator_name_.Get();
}
inline void MemoryLogRawAllocation::_internal_set_allocator_name(const std::string& value) {
  
  _impl_.allocator_name_.Set(value, GetArenaForAllocation());
}
inline std::string* MemoryLogRawAllocation::_internal_mutable_allocator_name() {
  
  return _impl_.allocator_name_.Mutable(GetArenaForAllocation());
}
inline std::string* MemoryLogRawAllocation::release_allocator_name() {
  // @@protoc_insertion_point(field_release:tensorflow.MemoryLogRawAllocation.allocator_name)
  return _impl_.allocator_name_.Release();
}
inline void MemoryLogRawAllocation::set_allocated_allocator_name(std::string* allocator_name) {
  if (allocator_name != nullptr) {
    
  } else {
    
  }
  _impl_.allocator_name_.SetAllocated(allocator_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.allocator_name_.IsDefault()) {
    _impl_.allocator_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MemoryLogRawAllocation.allocator_name)
}

// -------------------------------------------------------------------

// MemoryLogRawDeallocation

// int64 step_id = 1;
inline void MemoryLogRawDeallocation::clear_step_id() {
  _impl_.step_id_ = int64_t{0};
}
inline int64_t MemoryLogRawDeallocation::_internal_step_id() const {
  return _impl_.step_id_;
}
inline int64_t MemoryLogRawDeallocation::step_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryLogRawDeallocation.step_id)
  return _internal_step_id();
}
inline void MemoryLogRawDeallocation::_internal_set_step_id(int64_t value) {
  
  _impl_.step_id_ = value;
}
inline void MemoryLogRawDeallocation::set_step_id(int64_t value) {
  _internal_set_step_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.MemoryLogRawDeallocation.step_id)
}

// string operation = 2;
inline void MemoryLogRawDeallocation::clear_operation() {
  _impl_.operation_.ClearToEmpty();
}
inline const std::string& MemoryLogRawDeallocation::operation() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryLogRawDeallocation.operation)
  return _internal_operation();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void MemoryLogRawDeallocation::set_operation(ArgT0&& arg0, ArgT... args) {
 
 _impl_.operation_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.MemoryLogRawDeallocation.operation)
}
inline std::string* MemoryLogRawDeallocation::mutable_operation() {
  std::string* _s = _internal_mutable_operation();
  // @@protoc_insertion_point(field_mutable:tensorflow.MemoryLogRawDeallocation.operation)
  return _s;
}
inline const std::string& MemoryLogRawDeallocation::_internal_operation() const {
  return _impl_.operation_.Get();
}
inline void MemoryLogRawDeallocation::_internal_set_operation(const std::string& value) {
  
  _impl_.operation_.Set(value, GetArenaForAllocation());
}
inline std::string* MemoryLogRawDeallocation::_internal_mutable_operation() {
  
  return _impl_.operation_.Mutable(GetArenaForAllocation());
}
inline std::string* MemoryLogRawDeallocation::release_operation() {
  // @@protoc_insertion_point(field_release:tensorflow.MemoryLogRawDeallocation.operation)
  return _impl_.operation_.Release();
}
inline void MemoryLogRawDeallocation::set_allocated_operation(std::string* operation) {
  if (operation != nullptr) {
    
  } else {
    
  }
  _impl_.operation_.SetAllocated(operation, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.operation_.IsDefault()) {
    _impl_.operation_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MemoryLogRawDeallocation.operation)
}

// int64 allocation_id = 3;
inline void MemoryLogRawDeallocation::clear_allocation_id() {
  _impl_.allocation_id_ = int64_t{0};
}
inline int64_t MemoryLogRawDeallocation::_internal_allocation_id() const {
  return _impl_.allocation_id_;
}
inline int64_t MemoryLogRawDeallocation::allocation_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryLogRawDeallocation.allocation_id)
  return _internal_allocation_id();
}
inline void MemoryLogRawDeallocation::_internal_set_allocation_id(int64_t value) {
  
  _impl_.allocation_id_ = value;
}
inline void MemoryLogRawDeallocation::set_allocation_id(int64_t value) {
  _internal_set_allocation_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.MemoryLogRawDeallocation.allocation_id)
}

// string allocator_name = 4;
inline void MemoryLogRawDeallocation::clear_allocator_name() {
  _impl_.allocator_name_.ClearToEmpty();
}
inline const std::string& MemoryLogRawDeallocation::allocator_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryLogRawDeallocation.allocator_name)
  return _internal_allocator_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void MemoryLogRawDeallocation::set_allocator_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.allocator_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.MemoryLogRawDeallocation.allocator_name)
}
inline std::string* MemoryLogRawDeallocation::mutable_allocator_name() {
  std::string* _s = _internal_mutable_allocator_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.MemoryLogRawDeallocation.allocator_name)
  return _s;
}
inline const std::string& MemoryLogRawDeallocation::_internal_allocator_name() const {
  return _impl_.allocator_name_.Get();
}
inline void MemoryLogRawDeallocation::_internal_set_allocator_name(const std::string& value) {
  
  _impl_.allocator_name_.Set(value, GetArenaForAllocation());
}
inline std::string* MemoryLogRawDeallocation::_internal_mutable_allocator_name() {
  
  return _impl_.allocator_name_.Mutable(GetArenaForAllocation());
}
inline std::string* MemoryLogRawDeallocation::release_allocator_name() {
  // @@protoc_insertion_point(field_release:tensorflow.MemoryLogRawDeallocation.allocator_name)
  return _impl_.allocator_name_.Release();
}
inline void MemoryLogRawDeallocation::set_allocated_allocator_name(std::string* allocator_name) {
  if (allocator_name != nullptr) {
    
  } else {
    
  }
  _impl_.allocator_name_.SetAllocated(allocator_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.allocator_name_.IsDefault()) {
    _impl_.allocator_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MemoryLogRawDeallocation.allocator_name)
}

// bool deferred = 5;
inline void MemoryLogRawDeallocation::clear_deferred() {
  _impl_.deferred_ = false;
}
inline bool MemoryLogRawDeallocation::_internal_deferred() const {
  return _impl_.deferred_;
}
inline bool MemoryLogRawDeallocation::deferred() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryLogRawDeallocation.deferred)
  return _internal_deferred();
}
inline void MemoryLogRawDeallocation::_internal_set_deferred(bool value) {
  
  _impl_.deferred_ = value;
}
inline void MemoryLogRawDeallocation::set_deferred(bool value) {
  _internal_set_deferred(value);
  // @@protoc_insertion_point(field_set:tensorflow.MemoryLogRawDeallocation.deferred)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2flog_5fmemory_2eproto
