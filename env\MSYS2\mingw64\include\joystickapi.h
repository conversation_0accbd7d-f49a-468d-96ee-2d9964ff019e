/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */

#ifndef _JOYSTICKAPI_H_
#define _JOYSTICKAPI_H_

#include <apiset.h>
#include <apisetcconv.h>

#include <mmsyscom.h>

#ifdef __cplusplus
extern "C" {
#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)

#ifndef MMNOJOY

#define JOYERR_NOERROR (0)
#define JOYERR_PARMS (JOYERR_BASE+5)
#define JOYERR_NOCANDO (JOYERR_BASE+6)
#define JOYERR_UNPLUGGED (JOYERR_BASE+7)

#define JOY_BUTTON1 0x0001
#define JOY_BUTTON2 0x0002
#define JOY_BUTTON3 0x0004
#define JOY_BUTTON4 0x0008
#define JOY_BUTTON1CHG 0x0100
#define JOY_BUTTON2CHG 0x0200
#define JOY_BUTTON3CHG 0x0400
#define JOY_BUTTON4CHG 0x0800

#define JOY_BUTTON5 __MSABI_LONG(0x00000010)
#define JOY_BUTTON6 __MSABI_LONG(0x00000020)
#define JOY_BUTTON7 __MSABI_LONG(0x00000040)
#define JOY_BUTTON8 __MSABI_LONG(0x00000080)
#define JOY_BUTTON9 __MSABI_LONG(0x00000100)
#define JOY_BUTTON10 __MSABI_LONG(0x00000200)
#define JOY_BUTTON11 __MSABI_LONG(0x00000400)
#define JOY_BUTTON12 __MSABI_LONG(0x00000800)
#define JOY_BUTTON13 __MSABI_LONG(0x00001000)
#define JOY_BUTTON14 __MSABI_LONG(0x00002000)
#define JOY_BUTTON15 __MSABI_LONG(0x00004000)
#define JOY_BUTTON16 __MSABI_LONG(0x00008000)
#define JOY_BUTTON17 __MSABI_LONG(0x00010000)
#define JOY_BUTTON18 __MSABI_LONG(0x00020000)
#define JOY_BUTTON19 __MSABI_LONG(0x00040000)
#define JOY_BUTTON20 __MSABI_LONG(0x00080000)
#define JOY_BUTTON21 __MSABI_LONG(0x00100000)
#define JOY_BUTTON22 __MSABI_LONG(0x00200000)
#define JOY_BUTTON23 __MSABI_LONG(0x00400000)
#define JOY_BUTTON24 __MSABI_LONG(0x00800000)
#define JOY_BUTTON25 __MSABI_LONG(0x01000000)
#define JOY_BUTTON26 __MSABI_LONG(0x02000000)
#define JOY_BUTTON27 __MSABI_LONG(0x04000000)
#define JOY_BUTTON28 __MSABI_LONG(0x08000000)
#define JOY_BUTTON29 __MSABI_LONG(0x10000000)
#define JOY_BUTTON30 __MSABI_LONG(0x20000000)
#define JOY_BUTTON31 __MSABI_LONG(0x40000000)
#define JOY_BUTTON32 __MSABI_LONG(0x80000000)

#define JOY_POVCENTERED ((WORD)-1)
#define JOY_POVFORWARD 0
#define JOY_POVRIGHT 9000
#define JOY_POVBACKWARD 18000
#define JOY_POVLEFT 27000

#define JOY_RETURNX __MSABI_LONG(0x00000001)
#define JOY_RETURNY __MSABI_LONG(0x00000002)
#define JOY_RETURNZ __MSABI_LONG(0x00000004)
#define JOY_RETURNR __MSABI_LONG(0x00000008)
#define JOY_RETURNU __MSABI_LONG(0x00000010)
#define JOY_RETURNV __MSABI_LONG(0x00000020)
#define JOY_RETURNPOV __MSABI_LONG(0x00000040)
#define JOY_RETURNBUTTONS __MSABI_LONG(0x00000080)
#define JOY_RETURNRAWDATA __MSABI_LONG(0x00000100)
#define JOY_RETURNPOVCTS __MSABI_LONG(0x00000200)
#define JOY_RETURNCENTERED __MSABI_LONG(0x00000400)
#define JOY_USEDEADZONE __MSABI_LONG(0x00000800)
#define JOY_RETURNALL (JOY_RETURNX | JOY_RETURNY | JOY_RETURNZ | JOY_RETURNR | JOY_RETURNU | JOY_RETURNV | JOY_RETURNPOV | JOY_RETURNBUTTONS)
#define JOY_CAL_READALWAYS __MSABI_LONG(0x00010000)
#define JOY_CAL_READXYONLY __MSABI_LONG(0x00020000)
#define JOY_CAL_READ3 __MSABI_LONG(0x00040000)
#define JOY_CAL_READ4 __MSABI_LONG(0x00080000)
#define JOY_CAL_READXONLY __MSABI_LONG(0x00100000)
#define JOY_CAL_READYONLY __MSABI_LONG(0x00200000)
#define JOY_CAL_READ5 __MSABI_LONG(0x00400000)
#define JOY_CAL_READ6 __MSABI_LONG(0x00800000)
#define JOY_CAL_READZONLY __MSABI_LONG(0x01000000)
#define JOY_CAL_READRONLY __MSABI_LONG(0x02000000)
#define JOY_CAL_READUONLY __MSABI_LONG(0x04000000)
#define JOY_CAL_READVONLY __MSABI_LONG(0x08000000)

#define JOYSTICKID1 0
#define JOYSTICKID2 1

#define JOYCAPS_HASZ 0x0001
#define JOYCAPS_HASR 0x0002
#define JOYCAPS_HASU 0x0004
#define JOYCAPS_HASV 0x0008
#define JOYCAPS_HASPOV 0x0010
#define JOYCAPS_POV4DIR 0x0020
#define JOYCAPS_POVCTS 0x0040

typedef struct tagJOYCAPSA {
  WORD wMid;
  WORD wPid;
  CHAR szPname[MAXPNAMELEN];
  UINT wXmin;
  UINT wXmax;
  UINT wYmin;
  UINT wYmax;
  UINT wZmin;
  UINT wZmax;
  UINT wNumButtons;
  UINT wPeriodMin;
  UINT wPeriodMax;
  UINT wRmin;
  UINT wRmax;
  UINT wUmin;
  UINT wUmax;
  UINT wVmin;
  UINT wVmax;
  UINT wCaps;
  UINT wMaxAxes;
  UINT wNumAxes;
  UINT wMaxButtons;
  CHAR szRegKey[MAXPNAMELEN];
  CHAR szOEMVxD[MAX_JOYSTICKOEMVXDNAME];
} JOYCAPSA, *PJOYCAPSA, *NPJOYCAPSA, *LPJOYCAPSA;

typedef struct tagJOYCAPSW {
  WORD wMid;
  WORD wPid;
  WCHAR szPname[MAXPNAMELEN];
  UINT wXmin;
  UINT wXmax;
  UINT wYmin;
  UINT wYmax;
  UINT wZmin;
  UINT wZmax;
  UINT wNumButtons;
  UINT wPeriodMin;
  UINT wPeriodMax;
  UINT wRmin;
  UINT wRmax;
  UINT wUmin;
  UINT wUmax;
  UINT wVmin;
  UINT wVmax;
  UINT wCaps;
  UINT wMaxAxes;
  UINT wNumAxes;
  UINT wMaxButtons;
  WCHAR szRegKey[MAXPNAMELEN];
  WCHAR szOEMVxD[MAX_JOYSTICKOEMVXDNAME];
} JOYCAPSW, *PJOYCAPSW, *NPJOYCAPSW, *LPJOYCAPSW;

__MINGW_TYPEDEF_AW(JOYCAPS)
__MINGW_TYPEDEF_AW(PJOYCAPS)
__MINGW_TYPEDEF_AW(NPJOYCAPS)
__MINGW_TYPEDEF_AW(LPJOYCAPS)

typedef struct tagJOYCAPS2A {
  WORD wMid;
  WORD wPid;
  CHAR szPname[MAXPNAMELEN];
  UINT wXmin;
  UINT wXmax;
  UINT wYmin;
  UINT wYmax;
  UINT wZmin;
  UINT wZmax;
  UINT wNumButtons;
  UINT wPeriodMin;
  UINT wPeriodMax;
  UINT wRmin;
  UINT wRmax;
  UINT wUmin;
  UINT wUmax;
  UINT wVmin;
  UINT wVmax;
  UINT wCaps;
  UINT wMaxAxes;
  UINT wNumAxes;
  UINT wMaxButtons;
  CHAR szRegKey[MAXPNAMELEN];
  CHAR szOEMVxD[MAX_JOYSTICKOEMVXDNAME];
  GUID ManufacturerGuid;
  GUID ProductGuid;
  GUID NameGuid;
} JOYCAPS2A, *PJOYCAPS2A, *NPJOYCAPS2A, *LPJOYCAPS2A;

typedef struct tagJOYCAPS2W {
  WORD wMid;
  WORD wPid;
  WCHAR szPname[MAXPNAMELEN];
  UINT wXmin;
  UINT wXmax;
  UINT wYmin;
  UINT wYmax;
  UINT wZmin;
  UINT wZmax;
  UINT wNumButtons;
  UINT wPeriodMin;
  UINT wPeriodMax;
  UINT wRmin;
  UINT wRmax;
  UINT wUmin;
  UINT wUmax;
  UINT wVmin;
  UINT wVmax;
  UINT wCaps;
  UINT wMaxAxes;
  UINT wNumAxes;
  UINT wMaxButtons;
  WCHAR szRegKey[MAXPNAMELEN];
  WCHAR szOEMVxD[MAX_JOYSTICKOEMVXDNAME];
  GUID ManufacturerGuid;
  GUID ProductGuid;
  GUID NameGuid;
} JOYCAPS2W, *PJOYCAPS2W, *NPJOYCAPS2W, *LPJOYCAPS2W;

__MINGW_TYPEDEF_AW(JOYCAPS2)
__MINGW_TYPEDEF_AW(PJOYCAPS2)
__MINGW_TYPEDEF_AW(NPJOYCAPS2)
__MINGW_TYPEDEF_AW(LPJOYCAPS2)

typedef struct joyinfo_tag {
  UINT wXpos;
  UINT wYpos;
  UINT wZpos;
  UINT wButtons;
} JOYINFO, *PJOYINFO, *NPJOYINFO, *LPJOYINFO;

typedef struct joyinfoex_tag {
  DWORD dwSize;
  DWORD dwFlags;
  DWORD dwXpos;
  DWORD dwYpos;
  DWORD dwZpos;
  DWORD dwRpos;
  DWORD dwUpos;
  DWORD dwVpos;
  DWORD dwButtons;
  DWORD dwButtonNumber;
  DWORD dwPOV;
  DWORD dwReserved1;
  DWORD dwReserved2;
} JOYINFOEX, *PJOYINFOEX, *NPJOYINFOEX, *LPJOYINFOEX;

WINMMAPI MMRESULT WINAPI joyGetPosEx(UINT uJoyID, LPJOYINFOEX pji);

WINMMAPI UINT WINAPI joyGetNumDevs(void);

WINMMAPI MMRESULT WINAPI joyGetDevCapsA(UINT_PTR uJoyID, LPJOYCAPSA pjc, UINT cbjc);
WINMMAPI MMRESULT WINAPI joyGetDevCapsW(UINT_PTR uJoyID, LPJOYCAPSW pjc, UINT cbjc);
#define joyGetDevCaps __MINGW_NAME_AW(joyGetDevCaps)

WINMMAPI MMRESULT WINAPI joyGetPos(UINT uJoyID, LPJOYINFO pji);
WINMMAPI MMRESULT WINAPI joyGetThreshold(UINT uJoyID, LPUINT puThreshold);
WINMMAPI MMRESULT WINAPI joyReleaseCapture(UINT uJoyID);
WINMMAPI MMRESULT WINAPI joySetCapture(HWND hwnd, UINT uJoyID, UINT uPeriod, WINBOOL fChanged);
WINMMAPI MMRESULT WINAPI joySetThreshold(UINT uJoyID, UINT uThreshold);

WINMMAPI MMRESULT WINAPI joyConfigChanged(DWORD dwFlags);

#endif  /* ifndef MMNOJOY */

#endif /* WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP) */

#ifdef __cplusplus
}
#endif

#endif /* _JOYSTICKAPI_H_ */
