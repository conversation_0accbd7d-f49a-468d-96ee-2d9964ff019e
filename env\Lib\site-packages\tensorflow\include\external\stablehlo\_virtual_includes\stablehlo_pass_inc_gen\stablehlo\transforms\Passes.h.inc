/* Autogenerated by mlir-tblgen; don't manually edit */

#ifdef GEN_PASS_DECL
// Generate declarations for all passes.
#define GEN_PASS_DECL_CHLOLEGALIZETOSTABLEHLOPASS
#define GEN_PASS_DECL_SHAPELEGALIZETOSTABLEHLOPASS
#define GEN_PASS_DECL_STABLEHLOAGGRESSIVEFOLDERPASS
#define GEN_PASS_DECL_STABLEHLOAGGRESSIVESIMPLIFICATIONPASS
#define GEN_PASS_DECL_STABLEHLOCANONICALIZEDYNAMISMPASS
#define GEN_PASS_DECL_STABLEHLOCOMPATIBILITYEXPANDERPASS
#define GEN_PASS_DECL_STABLEHLOCOMPLEXMATHEXPANDERPASS
#define GEN_PASS_DECL_STABLEHLOCONVERTTOSIGNLESSPASS
#define GEN_PASS_DECL_STABLEHLOLEGALIZECOMPOSITETOCALLPASS
#define GEN_PASS_DECL_STABLEHLOLEGALIZEDEPRECATEDOPSPASS
#define GEN_PA<PERSON>_DECL_STABLEHLOLEGALIZEQDQTOQUANTIZEDOPPASS
#define GEN_PASS_DECL_STABLEHLOLEGALIZEQUANTTOMATHPASS
#define GEN_PASS_DECL_STABLEHLOLEGALIZEQUANTIZEDOPTOQDQPASS
#define GEN_PASS_DECL_STABLEHLOLEGALIZETOVHLOPASS
#define GEN_PASS_DECL_STABLEHLOREFINEARGUMENTSPASS
#define GEN_PASS_DECL_STABLEHLOREFINESHAPESPASS
#define GEN_PASS_DECL_VHLOLEGALIZETOSTABLEHLOPASS
#define GEN_PASS_DECL_VHLOTOVERSIONPASS
#undef GEN_PASS_DECL
#endif // GEN_PASS_DECL

//===----------------------------------------------------------------------===//
// ChloLegalizeToStablehloPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CHLOLEGALIZETOSTABLEHLOPASS
std::unique_ptr<::mlir::Pass> createChloLegalizeToStablehloPass();
#undef GEN_PASS_DECL_CHLOLEGALIZETOSTABLEHLOPASS
#endif // GEN_PASS_DECL_CHLOLEGALIZETOSTABLEHLOPASS
#ifdef GEN_PASS_DEF_CHLOLEGALIZETOSTABLEHLOPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createChloLegalizeToStablehloPass();
} // namespace impl
namespace impl {

template <typename DerivedT>
class ChloLegalizeToStablehloPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = ChloLegalizeToStablehloPassBase;

  ChloLegalizeToStablehloPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  ChloLegalizeToStablehloPassBase(const ChloLegalizeToStablehloPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  ChloLegalizeToStablehloPassBase& operator=(const ChloLegalizeToStablehloPassBase &) = delete;
  ChloLegalizeToStablehloPassBase(ChloLegalizeToStablehloPassBase &&) = delete;
  ChloLegalizeToStablehloPassBase& operator=(ChloLegalizeToStablehloPassBase &&) = delete;
  ~ChloLegalizeToStablehloPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("chlo-legalize-to-stablehlo");
  }
  ::llvm::StringRef getArgument() const override { return "chlo-legalize-to-stablehlo"; }

  ::llvm::StringRef getDescription() const override { return "Legalizes from CHLO ops flow to StableHLO and Shape ops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ChloLegalizeToStablehloPass");
  }
  ::llvm::StringRef getName() const override { return "ChloLegalizeToStablehloPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::shape::ShapeDialect>();
    registry.insert<mlir::stablehlo::StablehloDialect>();
    registry.insert<mlir::tensor::TensorDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ChloLegalizeToStablehloPassBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createChloLegalizeToStablehloPass() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createChloLegalizeToStablehloPass() {
  return impl::createChloLegalizeToStablehloPass();
}
#undef GEN_PASS_DEF_CHLOLEGALIZETOSTABLEHLOPASS
#endif // GEN_PASS_DEF_CHLOLEGALIZETOSTABLEHLOPASS

//===----------------------------------------------------------------------===//
// ShapeLegalizeToStablehloPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_SHAPELEGALIZETOSTABLEHLOPASS
std::unique_ptr<::mlir::Pass> createShapeLegalizeToStablehloPass();
#undef GEN_PASS_DECL_SHAPELEGALIZETOSTABLEHLOPASS
#endif // GEN_PASS_DECL_SHAPELEGALIZETOSTABLEHLOPASS
#ifdef GEN_PASS_DEF_SHAPELEGALIZETOSTABLEHLOPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createShapeLegalizeToStablehloPass();
} // namespace impl
namespace impl {

template <typename DerivedT>
class ShapeLegalizeToStablehloPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = ShapeLegalizeToStablehloPassBase;

  ShapeLegalizeToStablehloPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  ShapeLegalizeToStablehloPassBase(const ShapeLegalizeToStablehloPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  ShapeLegalizeToStablehloPassBase& operator=(const ShapeLegalizeToStablehloPassBase &) = delete;
  ShapeLegalizeToStablehloPassBase(ShapeLegalizeToStablehloPassBase &&) = delete;
  ShapeLegalizeToStablehloPassBase& operator=(ShapeLegalizeToStablehloPassBase &&) = delete;
  ~ShapeLegalizeToStablehloPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("shape-legalize-to-stablehlo");
  }
  ::llvm::StringRef getArgument() const override { return "shape-legalize-to-stablehlo"; }

  ::llvm::StringRef getDescription() const override { return "Legalize shape-related ops to StableHLO."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ShapeLegalizeToStablehloPass");
  }
  ::llvm::StringRef getName() const override { return "ShapeLegalizeToStablehloPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::stablehlo::StablehloDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ShapeLegalizeToStablehloPassBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createShapeLegalizeToStablehloPass() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createShapeLegalizeToStablehloPass() {
  return impl::createShapeLegalizeToStablehloPass();
}
#undef GEN_PASS_DEF_SHAPELEGALIZETOSTABLEHLOPASS
#endif // GEN_PASS_DEF_SHAPELEGALIZETOSTABLEHLOPASS

//===----------------------------------------------------------------------===//
// StablehloAggressiveFolderPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_STABLEHLOAGGRESSIVEFOLDERPASS
struct StablehloAggressiveFolderPassOptions {
  bool foldFloat = true;
};
std::unique_ptr<::mlir::Pass> createStablehloAggressiveFolderPass();
std::unique_ptr<::mlir::Pass> createStablehloAggressiveFolderPass(StablehloAggressiveFolderPassOptions options);
#undef GEN_PASS_DECL_STABLEHLOAGGRESSIVEFOLDERPASS
#endif // GEN_PASS_DECL_STABLEHLOAGGRESSIVEFOLDERPASS
#ifdef GEN_PASS_DEF_STABLEHLOAGGRESSIVEFOLDERPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createStablehloAggressiveFolderPass();
} // namespace impl

namespace impl {
  std::unique_ptr<::mlir::Pass> createStablehloAggressiveFolderPass(StablehloAggressiveFolderPassOptions options);
} // namespace impl
namespace impl {

template <typename DerivedT>
class StablehloAggressiveFolderPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = StablehloAggressiveFolderPassBase;

  StablehloAggressiveFolderPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  StablehloAggressiveFolderPassBase(const StablehloAggressiveFolderPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  StablehloAggressiveFolderPassBase& operator=(const StablehloAggressiveFolderPassBase &) = delete;
  StablehloAggressiveFolderPassBase(StablehloAggressiveFolderPassBase &&) = delete;
  StablehloAggressiveFolderPassBase& operator=(StablehloAggressiveFolderPassBase &&) = delete;
  ~StablehloAggressiveFolderPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("stablehlo-aggressive-folder");
  }
  ::llvm::StringRef getArgument() const override { return "stablehlo-aggressive-folder"; }

  ::llvm::StringRef getDescription() const override { return "Folds StableHLO operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("StablehloAggressiveFolderPass");
  }
  ::llvm::StringRef getName() const override { return "StablehloAggressiveFolderPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::stablehlo::StablehloDialect>();
    registry.insert<mlir::tensor::TensorDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(StablehloAggressiveFolderPassBase<DerivedT>)

  StablehloAggressiveFolderPassBase(StablehloAggressiveFolderPassOptions options) : StablehloAggressiveFolderPassBase() {
    foldFloat = std::move(options.foldFloat);
  }
protected:
  ::mlir::Pass::Option<bool> foldFloat{*this, "fold-float", ::llvm::cl::desc("Allow for potentially lossy computations using float type."), ::llvm::cl::init(true)};
private:

  friend std::unique_ptr<::mlir::Pass> createStablehloAggressiveFolderPass() {
    return std::make_unique<DerivedT>();
  }

  friend std::unique_ptr<::mlir::Pass> createStablehloAggressiveFolderPass(StablehloAggressiveFolderPassOptions options) {
    return std::make_unique<DerivedT>(std::move(options));
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createStablehloAggressiveFolderPass() {
  return impl::createStablehloAggressiveFolderPass();
}

std::unique_ptr<::mlir::Pass> createStablehloAggressiveFolderPass(StablehloAggressiveFolderPassOptions options) {
  return impl::createStablehloAggressiveFolderPass(std::move(options));
}
#undef GEN_PASS_DEF_STABLEHLOAGGRESSIVEFOLDERPASS
#endif // GEN_PASS_DEF_STABLEHLOAGGRESSIVEFOLDERPASS

//===----------------------------------------------------------------------===//
// StablehloAggressiveSimplificationPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_STABLEHLOAGGRESSIVESIMPLIFICATIONPASS
std::unique_ptr<::mlir::Pass> createStablehloAggressiveSimplificationPass();
#undef GEN_PASS_DECL_STABLEHLOAGGRESSIVESIMPLIFICATIONPASS
#endif // GEN_PASS_DECL_STABLEHLOAGGRESSIVESIMPLIFICATIONPASS
#ifdef GEN_PASS_DEF_STABLEHLOAGGRESSIVESIMPLIFICATIONPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createStablehloAggressiveSimplificationPass();
} // namespace impl
namespace impl {

template <typename DerivedT>
class StablehloAggressiveSimplificationPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = StablehloAggressiveSimplificationPassBase;

  StablehloAggressiveSimplificationPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  StablehloAggressiveSimplificationPassBase(const StablehloAggressiveSimplificationPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  StablehloAggressiveSimplificationPassBase& operator=(const StablehloAggressiveSimplificationPassBase &) = delete;
  StablehloAggressiveSimplificationPassBase(StablehloAggressiveSimplificationPassBase &&) = delete;
  StablehloAggressiveSimplificationPassBase& operator=(StablehloAggressiveSimplificationPassBase &&) = delete;
  ~StablehloAggressiveSimplificationPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("stablehlo-aggressive-simplification");
  }
  ::llvm::StringRef getArgument() const override { return "stablehlo-aggressive-simplification"; }

  ::llvm::StringRef getDescription() const override { return "Canonicalizes StableHLO operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("StablehloAggressiveSimplificationPass");
  }
  ::llvm::StringRef getName() const override { return "StablehloAggressiveSimplificationPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::stablehlo::StablehloDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(StablehloAggressiveSimplificationPassBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createStablehloAggressiveSimplificationPass() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createStablehloAggressiveSimplificationPass() {
  return impl::createStablehloAggressiveSimplificationPass();
}
#undef GEN_PASS_DEF_STABLEHLOAGGRESSIVESIMPLIFICATIONPASS
#endif // GEN_PASS_DEF_STABLEHLOAGGRESSIVESIMPLIFICATIONPASS

//===----------------------------------------------------------------------===//
// StablehloCanonicalizeDynamismPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_STABLEHLOCANONICALIZEDYNAMISMPASS
std::unique_ptr<::mlir::Pass> createStablehloCanonicalizeDynamismPass();
#undef GEN_PASS_DECL_STABLEHLOCANONICALIZEDYNAMISMPASS
#endif // GEN_PASS_DECL_STABLEHLOCANONICALIZEDYNAMISMPASS
#ifdef GEN_PASS_DEF_STABLEHLOCANONICALIZEDYNAMISMPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createStablehloCanonicalizeDynamismPass();
} // namespace impl
namespace impl {

template <typename DerivedT>
class StablehloCanonicalizeDynamismPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = StablehloCanonicalizeDynamismPassBase;

  StablehloCanonicalizeDynamismPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  StablehloCanonicalizeDynamismPassBase(const StablehloCanonicalizeDynamismPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  StablehloCanonicalizeDynamismPassBase& operator=(const StablehloCanonicalizeDynamismPassBase &) = delete;
  StablehloCanonicalizeDynamismPassBase(StablehloCanonicalizeDynamismPassBase &&) = delete;
  StablehloCanonicalizeDynamismPassBase& operator=(StablehloCanonicalizeDynamismPassBase &&) = delete;
  ~StablehloCanonicalizeDynamismPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("stablehlo-canonicalize-dynamism");
  }
  ::llvm::StringRef getArgument() const override { return "stablehlo-canonicalize-dynamism"; }

  ::llvm::StringRef getDescription() const override { return "Canonicalizes dynamic StableHLO ops into static ops."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("StablehloCanonicalizeDynamismPass");
  }
  ::llvm::StringRef getName() const override { return "StablehloCanonicalizeDynamismPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(StablehloCanonicalizeDynamismPassBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createStablehloCanonicalizeDynamismPass() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createStablehloCanonicalizeDynamismPass() {
  return impl::createStablehloCanonicalizeDynamismPass();
}
#undef GEN_PASS_DEF_STABLEHLOCANONICALIZEDYNAMISMPASS
#endif // GEN_PASS_DEF_STABLEHLOCANONICALIZEDYNAMISMPASS

//===----------------------------------------------------------------------===//
// StablehloCompatibilityExpanderPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_STABLEHLOCOMPATIBILITYEXPANDERPASS
struct StablehloCompatibilityExpanderPassOptions {
  std::string targetVersionOption;
};
std::unique_ptr<::mlir::Pass> createStablehloCompatibilityExpanderPass();
std::unique_ptr<::mlir::Pass> createStablehloCompatibilityExpanderPass(StablehloCompatibilityExpanderPassOptions options);
#undef GEN_PASS_DECL_STABLEHLOCOMPATIBILITYEXPANDERPASS
#endif // GEN_PASS_DECL_STABLEHLOCOMPATIBILITYEXPANDERPASS
#ifdef GEN_PASS_DEF_STABLEHLOCOMPATIBILITYEXPANDERPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createStablehloCompatibilityExpanderPass();
} // namespace impl

namespace impl {
  std::unique_ptr<::mlir::Pass> createStablehloCompatibilityExpanderPass(StablehloCompatibilityExpanderPassOptions options);
} // namespace impl
namespace impl {

template <typename DerivedT>
class StablehloCompatibilityExpanderPassBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = StablehloCompatibilityExpanderPassBase;

  StablehloCompatibilityExpanderPassBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  StablehloCompatibilityExpanderPassBase(const StablehloCompatibilityExpanderPassBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}
  StablehloCompatibilityExpanderPassBase& operator=(const StablehloCompatibilityExpanderPassBase &) = delete;
  StablehloCompatibilityExpanderPassBase(StablehloCompatibilityExpanderPassBase &&) = delete;
  StablehloCompatibilityExpanderPassBase& operator=(StablehloCompatibilityExpanderPassBase &&) = delete;
  ~StablehloCompatibilityExpanderPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("stablehlo-compatibility-expander");
  }
  ::llvm::StringRef getArgument() const override { return "stablehlo-compatibility-expander"; }

  ::llvm::StringRef getDescription() const override { return "Compatibility expander for StableHLO operations."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("StablehloCompatibilityExpanderPass");
  }
  ::llvm::StringRef getName() const override { return "StablehloCompatibilityExpanderPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::stablehlo::StablehloDialect>();
    registry.insert<mlir::chlo::ChloDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(StablehloCompatibilityExpanderPassBase<DerivedT>)

  StablehloCompatibilityExpanderPassBase(StablehloCompatibilityExpanderPassOptions options) : StablehloCompatibilityExpanderPassBase() {
    targetVersionOption = std::move(options.targetVersionOption);
  }
protected:
  ::mlir::Pass::Option<std::string> targetVersionOption{*this, "target", ::llvm::cl::desc("The target version. Must be a version of the form #.#.#.")};
private:

  friend std::unique_ptr<::mlir::Pass> createStablehloCompatibilityExpanderPass() {
    return std::make_unique<DerivedT>();
  }

  friend std::unique_ptr<::mlir::Pass> createStablehloCompatibilityExpanderPass(StablehloCompatibilityExpanderPassOptions options) {
    return std::make_unique<DerivedT>(std::move(options));
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createStablehloCompatibilityExpanderPass() {
  return impl::createStablehloCompatibilityExpanderPass();
}

std::unique_ptr<::mlir::Pass> createStablehloCompatibilityExpanderPass(StablehloCompatibilityExpanderPassOptions options) {
  return impl::createStablehloCompatibilityExpanderPass(std::move(options));
}
#undef GEN_PASS_DEF_STABLEHLOCOMPATIBILITYEXPANDERPASS
#endif // GEN_PASS_DEF_STABLEHLOCOMPATIBILITYEXPANDERPASS

//===----------------------------------------------------------------------===//
// StablehloComplexMathExpanderPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_STABLEHLOCOMPLEXMATHEXPANDERPASS
std::unique_ptr<::mlir::Pass> createStablehloComplexMathExpanderPass();
#undef GEN_PASS_DECL_STABLEHLOCOMPLEXMATHEXPANDERPASS
#endif // GEN_PASS_DECL_STABLEHLOCOMPLEXMATHEXPANDERPASS
#ifdef GEN_PASS_DEF_STABLEHLOCOMPLEXMATHEXPANDERPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createStablehloComplexMathExpanderPass();
} // namespace impl
namespace impl {

template <typename DerivedT>
class StablehloComplexMathExpanderPassBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = StablehloComplexMathExpanderPassBase;

  StablehloComplexMathExpanderPassBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  StablehloComplexMathExpanderPassBase(const StablehloComplexMathExpanderPassBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}
  StablehloComplexMathExpanderPassBase& operator=(const StablehloComplexMathExpanderPassBase &) = delete;
  StablehloComplexMathExpanderPassBase(StablehloComplexMathExpanderPassBase &&) = delete;
  StablehloComplexMathExpanderPassBase& operator=(StablehloComplexMathExpanderPassBase &&) = delete;
  ~StablehloComplexMathExpanderPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("stablehlo-complex-math-expander");
  }
  ::llvm::StringRef getArgument() const override { return "stablehlo-complex-math-expander"; }

  ::llvm::StringRef getDescription() const override { return "Expander for StableHLO complex math operations."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("StablehloComplexMathExpanderPass");
  }
  ::llvm::StringRef getName() const override { return "StablehloComplexMathExpanderPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::stablehlo::StablehloDialect>();
    registry.insert<mlir::chlo::ChloDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(StablehloComplexMathExpanderPassBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createStablehloComplexMathExpanderPass() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createStablehloComplexMathExpanderPass() {
  return impl::createStablehloComplexMathExpanderPass();
}
#undef GEN_PASS_DEF_STABLEHLOCOMPLEXMATHEXPANDERPASS
#endif // GEN_PASS_DEF_STABLEHLOCOMPLEXMATHEXPANDERPASS

//===----------------------------------------------------------------------===//
// StablehloConvertToSignlessPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_STABLEHLOCONVERTTOSIGNLESSPASS
std::unique_ptr<::mlir::Pass> createStablehloConvertToSignlessPass();
#undef GEN_PASS_DECL_STABLEHLOCONVERTTOSIGNLESSPASS
#endif // GEN_PASS_DECL_STABLEHLOCONVERTTOSIGNLESSPASS
#ifdef GEN_PASS_DEF_STABLEHLOCONVERTTOSIGNLESSPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createStablehloConvertToSignlessPass();
} // namespace impl
namespace impl {

template <typename DerivedT>
class StablehloConvertToSignlessPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = StablehloConvertToSignlessPassBase;

  StablehloConvertToSignlessPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  StablehloConvertToSignlessPassBase(const StablehloConvertToSignlessPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  StablehloConvertToSignlessPassBase& operator=(const StablehloConvertToSignlessPassBase &) = delete;
  StablehloConvertToSignlessPassBase(StablehloConvertToSignlessPassBase &&) = delete;
  StablehloConvertToSignlessPassBase& operator=(StablehloConvertToSignlessPassBase &&) = delete;
  ~StablehloConvertToSignlessPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("stablehlo-convert-to-signless");
  }
  ::llvm::StringRef getArgument() const override { return "stablehlo-convert-to-signless"; }

  ::llvm::StringRef getDescription() const override { return "Pass to transform the IR to be on signless integers."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("StablehloConvertToSignlessPass");
  }
  ::llvm::StringRef getName() const override { return "StablehloConvertToSignlessPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(StablehloConvertToSignlessPassBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createStablehloConvertToSignlessPass() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createStablehloConvertToSignlessPass() {
  return impl::createStablehloConvertToSignlessPass();
}
#undef GEN_PASS_DEF_STABLEHLOCONVERTTOSIGNLESSPASS
#endif // GEN_PASS_DEF_STABLEHLOCONVERTTOSIGNLESSPASS

//===----------------------------------------------------------------------===//
// StablehloLegalizeCompositeToCallPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_STABLEHLOLEGALIZECOMPOSITETOCALLPASS
struct StablehloLegalizeCompositeToCallPassOptions {
  ::llvm::SmallVector<std::string> exceptListOption;
};
std::unique_ptr<::mlir::Pass> createStablehloLegalizeCompositeToCallPass();
std::unique_ptr<::mlir::Pass> createStablehloLegalizeCompositeToCallPass(StablehloLegalizeCompositeToCallPassOptions options);
#undef GEN_PASS_DECL_STABLEHLOLEGALIZECOMPOSITETOCALLPASS
#endif // GEN_PASS_DECL_STABLEHLOLEGALIZECOMPOSITETOCALLPASS
#ifdef GEN_PASS_DEF_STABLEHLOLEGALIZECOMPOSITETOCALLPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createStablehloLegalizeCompositeToCallPass();
} // namespace impl

namespace impl {
  std::unique_ptr<::mlir::Pass> createStablehloLegalizeCompositeToCallPass(StablehloLegalizeCompositeToCallPassOptions options);
} // namespace impl
namespace impl {

template <typename DerivedT>
class StablehloLegalizeCompositeToCallPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = StablehloLegalizeCompositeToCallPassBase;

  StablehloLegalizeCompositeToCallPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  StablehloLegalizeCompositeToCallPassBase(const StablehloLegalizeCompositeToCallPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  StablehloLegalizeCompositeToCallPassBase& operator=(const StablehloLegalizeCompositeToCallPassBase &) = delete;
  StablehloLegalizeCompositeToCallPassBase(StablehloLegalizeCompositeToCallPassBase &&) = delete;
  StablehloLegalizeCompositeToCallPassBase& operator=(StablehloLegalizeCompositeToCallPassBase &&) = delete;
  ~StablehloLegalizeCompositeToCallPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("stablehlo-legalize-composite-to-call");
  }
  ::llvm::StringRef getArgument() const override { return "stablehlo-legalize-composite-to-call"; }

  ::llvm::StringRef getDescription() const override { return "Replaces composite ops with a call to their decomposition."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("StablehloLegalizeCompositeToCallPass");
  }
  ::llvm::StringRef getName() const override { return "StablehloLegalizeCompositeToCallPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::func::FuncDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(StablehloLegalizeCompositeToCallPassBase<DerivedT>)

  StablehloLegalizeCompositeToCallPassBase(StablehloLegalizeCompositeToCallPassOptions options) : StablehloLegalizeCompositeToCallPassBase() {
    exceptListOption = std::move(options.exceptListOption);
  }
protected:
  ::mlir::Pass::ListOption<std::string> exceptListOption{*this, "except", ::llvm::cl::desc("Names of composites that should not be replaced with calls.")};
private:

  friend std::unique_ptr<::mlir::Pass> createStablehloLegalizeCompositeToCallPass() {
    return std::make_unique<DerivedT>();
  }

  friend std::unique_ptr<::mlir::Pass> createStablehloLegalizeCompositeToCallPass(StablehloLegalizeCompositeToCallPassOptions options) {
    return std::make_unique<DerivedT>(std::move(options));
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createStablehloLegalizeCompositeToCallPass() {
  return impl::createStablehloLegalizeCompositeToCallPass();
}

std::unique_ptr<::mlir::Pass> createStablehloLegalizeCompositeToCallPass(StablehloLegalizeCompositeToCallPassOptions options) {
  return impl::createStablehloLegalizeCompositeToCallPass(std::move(options));
}
#undef GEN_PASS_DEF_STABLEHLOLEGALIZECOMPOSITETOCALLPASS
#endif // GEN_PASS_DEF_STABLEHLOLEGALIZECOMPOSITETOCALLPASS

//===----------------------------------------------------------------------===//
// StablehloLegalizeDeprecatedOpsPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_STABLEHLOLEGALIZEDEPRECATEDOPSPASS
struct StablehloLegalizeDeprecatedOpsPassOptions {
  bool failOnUnusedOps = true;
};
std::unique_ptr<::mlir::Pass> createStablehloLegalizeDeprecatedOpsPass();
std::unique_ptr<::mlir::Pass> createStablehloLegalizeDeprecatedOpsPass(StablehloLegalizeDeprecatedOpsPassOptions options);
#undef GEN_PASS_DECL_STABLEHLOLEGALIZEDEPRECATEDOPSPASS
#endif // GEN_PASS_DECL_STABLEHLOLEGALIZEDEPRECATEDOPSPASS
#ifdef GEN_PASS_DEF_STABLEHLOLEGALIZEDEPRECATEDOPSPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createStablehloLegalizeDeprecatedOpsPass();
} // namespace impl

namespace impl {
  std::unique_ptr<::mlir::Pass> createStablehloLegalizeDeprecatedOpsPass(StablehloLegalizeDeprecatedOpsPassOptions options);
} // namespace impl
namespace impl {

template <typename DerivedT>
class StablehloLegalizeDeprecatedOpsPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = StablehloLegalizeDeprecatedOpsPassBase;

  StablehloLegalizeDeprecatedOpsPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  StablehloLegalizeDeprecatedOpsPassBase(const StablehloLegalizeDeprecatedOpsPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  StablehloLegalizeDeprecatedOpsPassBase& operator=(const StablehloLegalizeDeprecatedOpsPassBase &) = delete;
  StablehloLegalizeDeprecatedOpsPassBase(StablehloLegalizeDeprecatedOpsPassBase &&) = delete;
  StablehloLegalizeDeprecatedOpsPassBase& operator=(StablehloLegalizeDeprecatedOpsPassBase &&) = delete;
  ~StablehloLegalizeDeprecatedOpsPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("stablehlo-legalize-deprecated-ops");
  }
  ::llvm::StringRef getArgument() const override { return "stablehlo-legalize-deprecated-ops"; }

  ::llvm::StringRef getDescription() const override { return "Legalize deprecated ops to well-supported ops."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("StablehloLegalizeDeprecatedOpsPass");
  }
  ::llvm::StringRef getName() const override { return "StablehloLegalizeDeprecatedOpsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::stablehlo::StablehloDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(StablehloLegalizeDeprecatedOpsPassBase<DerivedT>)

  StablehloLegalizeDeprecatedOpsPassBase(StablehloLegalizeDeprecatedOpsPassOptions options) : StablehloLegalizeDeprecatedOpsPassBase() {
    failOnUnusedOps = std::move(options.failOnUnusedOps);
  }
protected:
  ::mlir::Pass::Option<bool> failOnUnusedOps{*this, "fail-on-unused", ::llvm::cl::desc("Fail on (mostly) unused ops that are deprecated without any fallback."), ::llvm::cl::init(true)};
private:

  friend std::unique_ptr<::mlir::Pass> createStablehloLegalizeDeprecatedOpsPass() {
    return std::make_unique<DerivedT>();
  }

  friend std::unique_ptr<::mlir::Pass> createStablehloLegalizeDeprecatedOpsPass(StablehloLegalizeDeprecatedOpsPassOptions options) {
    return std::make_unique<DerivedT>(std::move(options));
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createStablehloLegalizeDeprecatedOpsPass() {
  return impl::createStablehloLegalizeDeprecatedOpsPass();
}

std::unique_ptr<::mlir::Pass> createStablehloLegalizeDeprecatedOpsPass(StablehloLegalizeDeprecatedOpsPassOptions options) {
  return impl::createStablehloLegalizeDeprecatedOpsPass(std::move(options));
}
#undef GEN_PASS_DEF_STABLEHLOLEGALIZEDEPRECATEDOPSPASS
#endif // GEN_PASS_DEF_STABLEHLOLEGALIZEDEPRECATEDOPSPASS

//===----------------------------------------------------------------------===//
// StablehloLegalizeQDQToQuantizedOpPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_STABLEHLOLEGALIZEQDQTOQUANTIZEDOPPASS
std::unique_ptr<::mlir::Pass> createStablehloLegalizeQDQToQuantizedOpPass();
#undef GEN_PASS_DECL_STABLEHLOLEGALIZEQDQTOQUANTIZEDOPPASS
#endif // GEN_PASS_DECL_STABLEHLOLEGALIZEQDQTOQUANTIZEDOPPASS
#ifdef GEN_PASS_DEF_STABLEHLOLEGALIZEQDQTOQUANTIZEDOPPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createStablehloLegalizeQDQToQuantizedOpPass();
} // namespace impl
namespace impl {

template <typename DerivedT>
class StablehloLegalizeQDQToQuantizedOpPassBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = StablehloLegalizeQDQToQuantizedOpPassBase;

  StablehloLegalizeQDQToQuantizedOpPassBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  StablehloLegalizeQDQToQuantizedOpPassBase(const StablehloLegalizeQDQToQuantizedOpPassBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}
  StablehloLegalizeQDQToQuantizedOpPassBase& operator=(const StablehloLegalizeQDQToQuantizedOpPassBase &) = delete;
  StablehloLegalizeQDQToQuantizedOpPassBase(StablehloLegalizeQDQToQuantizedOpPassBase &&) = delete;
  StablehloLegalizeQDQToQuantizedOpPassBase& operator=(StablehloLegalizeQDQToQuantizedOpPassBase &&) = delete;
  ~StablehloLegalizeQDQToQuantizedOpPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("stablehlo-legalize-qdq-to-quantized-op");
  }
  ::llvm::StringRef getArgument() const override { return "stablehlo-legalize-qdq-to-quantized-op"; }

  ::llvm::StringRef getDescription() const override { return "Fuse (de-quantize, floating-point operation and quantize) pattern into StableHLO quantized operation"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("StablehloLegalizeQDQToQuantizedOpPass");
  }
  ::llvm::StringRef getName() const override { return "StablehloLegalizeQDQToQuantizedOpPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::stablehlo::StablehloDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(StablehloLegalizeQDQToQuantizedOpPassBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createStablehloLegalizeQDQToQuantizedOpPass() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createStablehloLegalizeQDQToQuantizedOpPass() {
  return impl::createStablehloLegalizeQDQToQuantizedOpPass();
}
#undef GEN_PASS_DEF_STABLEHLOLEGALIZEQDQTOQUANTIZEDOPPASS
#endif // GEN_PASS_DEF_STABLEHLOLEGALIZEQDQTOQUANTIZEDOPPASS

//===----------------------------------------------------------------------===//
// StablehloLegalizeQuantToMathPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_STABLEHLOLEGALIZEQUANTTOMATHPASS
std::unique_ptr<::mlir::Pass> createStablehloLegalizeQuantToMathPass();
#undef GEN_PASS_DECL_STABLEHLOLEGALIZEQUANTTOMATHPASS
#endif // GEN_PASS_DECL_STABLEHLOLEGALIZEQUANTTOMATHPASS
#ifdef GEN_PASS_DEF_STABLEHLOLEGALIZEQUANTTOMATHPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createStablehloLegalizeQuantToMathPass();
} // namespace impl
namespace impl {

template <typename DerivedT>
class StablehloLegalizeQuantToMathPassBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = StablehloLegalizeQuantToMathPassBase;

  StablehloLegalizeQuantToMathPassBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  StablehloLegalizeQuantToMathPassBase(const StablehloLegalizeQuantToMathPassBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}
  StablehloLegalizeQuantToMathPassBase& operator=(const StablehloLegalizeQuantToMathPassBase &) = delete;
  StablehloLegalizeQuantToMathPassBase(StablehloLegalizeQuantToMathPassBase &&) = delete;
  StablehloLegalizeQuantToMathPassBase& operator=(StablehloLegalizeQuantToMathPassBase &&) = delete;
  ~StablehloLegalizeQuantToMathPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("stablehlo-legalize-quant-to-math");
  }
  ::llvm::StringRef getArgument() const override { return "stablehlo-legalize-quant-to-math"; }

  ::llvm::StringRef getDescription() const override { return "Convert from StableHLO quantized ops to StableHLO primitive math ops."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("StablehloLegalizeQuantToMathPass");
  }
  ::llvm::StringRef getName() const override { return "StablehloLegalizeQuantToMathPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::chlo::ChloDialect>();
    registry.insert<mlir::stablehlo::StablehloDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(StablehloLegalizeQuantToMathPassBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createStablehloLegalizeQuantToMathPass() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createStablehloLegalizeQuantToMathPass() {
  return impl::createStablehloLegalizeQuantToMathPass();
}
#undef GEN_PASS_DEF_STABLEHLOLEGALIZEQUANTTOMATHPASS
#endif // GEN_PASS_DEF_STABLEHLOLEGALIZEQUANTTOMATHPASS

//===----------------------------------------------------------------------===//
// StablehloLegalizeQuantizedOpToQDQPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_STABLEHLOLEGALIZEQUANTIZEDOPTOQDQPASS
std::unique_ptr<::mlir::Pass> createStablehloLegalizeQuantizedOpToQDQPass();
#undef GEN_PASS_DECL_STABLEHLOLEGALIZEQUANTIZEDOPTOQDQPASS
#endif // GEN_PASS_DECL_STABLEHLOLEGALIZEQUANTIZEDOPTOQDQPASS
#ifdef GEN_PASS_DEF_STABLEHLOLEGALIZEQUANTIZEDOPTOQDQPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createStablehloLegalizeQuantizedOpToQDQPass();
} // namespace impl
namespace impl {

template <typename DerivedT>
class StablehloLegalizeQuantizedOpToQDQPassBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = StablehloLegalizeQuantizedOpToQDQPassBase;

  StablehloLegalizeQuantizedOpToQDQPassBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  StablehloLegalizeQuantizedOpToQDQPassBase(const StablehloLegalizeQuantizedOpToQDQPassBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}
  StablehloLegalizeQuantizedOpToQDQPassBase& operator=(const StablehloLegalizeQuantizedOpToQDQPassBase &) = delete;
  StablehloLegalizeQuantizedOpToQDQPassBase(StablehloLegalizeQuantizedOpToQDQPassBase &&) = delete;
  StablehloLegalizeQuantizedOpToQDQPassBase& operator=(StablehloLegalizeQuantizedOpToQDQPassBase &&) = delete;
  ~StablehloLegalizeQuantizedOpToQDQPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("stablehlo-legalize-quantized-op-to-qdq");
  }
  ::llvm::StringRef getArgument() const override { return "stablehlo-legalize-quantized-op-to-qdq"; }

  ::llvm::StringRef getDescription() const override { return "Decompose quantized StableHLO operation to (de-quantize, floating-point operation and quantize) pattern."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("StablehloLegalizeQuantizedOpToQDQPass");
  }
  ::llvm::StringRef getName() const override { return "StablehloLegalizeQuantizedOpToQDQPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::stablehlo::StablehloDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(StablehloLegalizeQuantizedOpToQDQPassBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createStablehloLegalizeQuantizedOpToQDQPass() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createStablehloLegalizeQuantizedOpToQDQPass() {
  return impl::createStablehloLegalizeQuantizedOpToQDQPass();
}
#undef GEN_PASS_DEF_STABLEHLOLEGALIZEQUANTIZEDOPTOQDQPASS
#endif // GEN_PASS_DEF_STABLEHLOLEGALIZEQUANTIZEDOPTOQDQPASS

//===----------------------------------------------------------------------===//
// StablehloLegalizeToVhloPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_STABLEHLOLEGALIZETOVHLOPASS
std::unique_ptr<::mlir::Pass> createStablehloLegalizeToVhloPass();
#undef GEN_PASS_DECL_STABLEHLOLEGALIZETOVHLOPASS
#endif // GEN_PASS_DECL_STABLEHLOLEGALIZETOVHLOPASS
#ifdef GEN_PASS_DEF_STABLEHLOLEGALIZETOVHLOPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createStablehloLegalizeToVhloPass();
} // namespace impl
namespace impl {

template <typename DerivedT>
class StablehloLegalizeToVhloPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = StablehloLegalizeToVhloPassBase;

  StablehloLegalizeToVhloPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  StablehloLegalizeToVhloPassBase(const StablehloLegalizeToVhloPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  StablehloLegalizeToVhloPassBase& operator=(const StablehloLegalizeToVhloPassBase &) = delete;
  StablehloLegalizeToVhloPassBase(StablehloLegalizeToVhloPassBase &&) = delete;
  StablehloLegalizeToVhloPassBase& operator=(StablehloLegalizeToVhloPassBase &&) = delete;
  ~StablehloLegalizeToVhloPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("stablehlo-legalize-to-vhlo");
  }
  ::llvm::StringRef getArgument() const override { return "stablehlo-legalize-to-vhlo"; }

  ::llvm::StringRef getDescription() const override { return "Legalize StableHLO to VHLO."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("StablehloLegalizeToVhloPass");
  }
  ::llvm::StringRef getName() const override { return "StablehloLegalizeToVhloPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::vhlo::VhloDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(StablehloLegalizeToVhloPassBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createStablehloLegalizeToVhloPass() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createStablehloLegalizeToVhloPass() {
  return impl::createStablehloLegalizeToVhloPass();
}
#undef GEN_PASS_DEF_STABLEHLOLEGALIZETOVHLOPASS
#endif // GEN_PASS_DEF_STABLEHLOLEGALIZETOVHLOPASS

//===----------------------------------------------------------------------===//
// StablehloRefineArgumentsPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_STABLEHLOREFINEARGUMENTSPASS
struct StablehloRefineArgumentsPassOptions {
  ::llvm::SmallVector<std::string> refinedTypesOption;
};
std::unique_ptr<::mlir::Pass> createStablehloRefineArgumentsPass();
std::unique_ptr<::mlir::Pass> createStablehloRefineArgumentsPass(StablehloRefineArgumentsPassOptions options);
#undef GEN_PASS_DECL_STABLEHLOREFINEARGUMENTSPASS
#endif // GEN_PASS_DECL_STABLEHLOREFINEARGUMENTSPASS
#ifdef GEN_PASS_DEF_STABLEHLOREFINEARGUMENTSPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createStablehloRefineArgumentsPass();
} // namespace impl

namespace impl {
  std::unique_ptr<::mlir::Pass> createStablehloRefineArgumentsPass(StablehloRefineArgumentsPassOptions options);
} // namespace impl
namespace impl {

template <typename DerivedT>
class StablehloRefineArgumentsPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = StablehloRefineArgumentsPassBase;

  StablehloRefineArgumentsPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  StablehloRefineArgumentsPassBase(const StablehloRefineArgumentsPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  StablehloRefineArgumentsPassBase& operator=(const StablehloRefineArgumentsPassBase &) = delete;
  StablehloRefineArgumentsPassBase(StablehloRefineArgumentsPassBase &&) = delete;
  StablehloRefineArgumentsPassBase& operator=(StablehloRefineArgumentsPassBase &&) = delete;
  ~StablehloRefineArgumentsPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("stablehlo-refine-arguments");
  }
  ::llvm::StringRef getArgument() const override { return "stablehlo-refine-arguments"; }

  ::llvm::StringRef getDescription() const override { return "Refines the argument shapes of the main function."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("StablehloRefineArgumentsPass");
  }
  ::llvm::StringRef getName() const override { return "StablehloRefineArgumentsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::stablehlo::StablehloDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(StablehloRefineArgumentsPassBase<DerivedT>)

  StablehloRefineArgumentsPassBase(StablehloRefineArgumentsPassOptions options) : StablehloRefineArgumentsPassBase() {
    refinedTypesOption = std::move(options.refinedTypesOption);
  }
protected:
  ::mlir::Pass::ListOption<std::string> refinedTypesOption{*this, "types", ::llvm::cl::desc("The new types to be used for the main function's arguments, specified as an MLIR TypeRange 'tensor<1x2xf32>, ...'")};
private:

  friend std::unique_ptr<::mlir::Pass> createStablehloRefineArgumentsPass() {
    return std::make_unique<DerivedT>();
  }

  friend std::unique_ptr<::mlir::Pass> createStablehloRefineArgumentsPass(StablehloRefineArgumentsPassOptions options) {
    return std::make_unique<DerivedT>(std::move(options));
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createStablehloRefineArgumentsPass() {
  return impl::createStablehloRefineArgumentsPass();
}

std::unique_ptr<::mlir::Pass> createStablehloRefineArgumentsPass(StablehloRefineArgumentsPassOptions options) {
  return impl::createStablehloRefineArgumentsPass(std::move(options));
}
#undef GEN_PASS_DEF_STABLEHLOREFINEARGUMENTSPASS
#endif // GEN_PASS_DEF_STABLEHLOREFINEARGUMENTSPASS

//===----------------------------------------------------------------------===//
// StablehloRefineShapesPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_STABLEHLOREFINESHAPESPASS
std::unique_ptr<::mlir::Pass> createStablehloRefineShapesPass();
#undef GEN_PASS_DECL_STABLEHLOREFINESHAPESPASS
#endif // GEN_PASS_DECL_STABLEHLOREFINESHAPESPASS
#ifdef GEN_PASS_DEF_STABLEHLOREFINESHAPESPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createStablehloRefineShapesPass();
} // namespace impl
namespace impl {

template <typename DerivedT>
class StablehloRefineShapesPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = StablehloRefineShapesPassBase;

  StablehloRefineShapesPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  StablehloRefineShapesPassBase(const StablehloRefineShapesPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  StablehloRefineShapesPassBase& operator=(const StablehloRefineShapesPassBase &) = delete;
  StablehloRefineShapesPassBase(StablehloRefineShapesPassBase &&) = delete;
  StablehloRefineShapesPassBase& operator=(StablehloRefineShapesPassBase &&) = delete;
  ~StablehloRefineShapesPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("stablehlo-refine-shapes");
  }
  ::llvm::StringRef getArgument() const override { return "stablehlo-refine-shapes"; }

  ::llvm::StringRef getDescription() const override { return "Refines shapes across a StableHLO program."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("StablehloRefineShapesPass");
  }
  ::llvm::StringRef getName() const override { return "StablehloRefineShapesPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(StablehloRefineShapesPassBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createStablehloRefineShapesPass() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createStablehloRefineShapesPass() {
  return impl::createStablehloRefineShapesPass();
}
#undef GEN_PASS_DEF_STABLEHLOREFINESHAPESPASS
#endif // GEN_PASS_DEF_STABLEHLOREFINESHAPESPASS

//===----------------------------------------------------------------------===//
// VhloLegalizeToStablehloPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_VHLOLEGALIZETOSTABLEHLOPASS
std::unique_ptr<::mlir::Pass> createVhloLegalizeToStablehloPass();
#undef GEN_PASS_DECL_VHLOLEGALIZETOSTABLEHLOPASS
#endif // GEN_PASS_DECL_VHLOLEGALIZETOSTABLEHLOPASS
#ifdef GEN_PASS_DEF_VHLOLEGALIZETOSTABLEHLOPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createVhloLegalizeToStablehloPass();
} // namespace impl
namespace impl {

template <typename DerivedT>
class VhloLegalizeToStablehloPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = VhloLegalizeToStablehloPassBase;

  VhloLegalizeToStablehloPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  VhloLegalizeToStablehloPassBase(const VhloLegalizeToStablehloPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  VhloLegalizeToStablehloPassBase& operator=(const VhloLegalizeToStablehloPassBase &) = delete;
  VhloLegalizeToStablehloPassBase(VhloLegalizeToStablehloPassBase &&) = delete;
  VhloLegalizeToStablehloPassBase& operator=(VhloLegalizeToStablehloPassBase &&) = delete;
  ~VhloLegalizeToStablehloPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("vhlo-legalize-to-stablehlo");
  }
  ::llvm::StringRef getArgument() const override { return "vhlo-legalize-to-stablehlo"; }

  ::llvm::StringRef getDescription() const override { return "Legalize VHLO to StableHLO."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("VhloLegalizeToStablehloPass");
  }
  ::llvm::StringRef getName() const override { return "VhloLegalizeToStablehloPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::func::FuncDialect>();
    registry.insert<mlir::quant::QuantDialect>();
    registry.insert<mlir::shape::ShapeDialect>();
    registry.insert<mlir::stablehlo::StablehloDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(VhloLegalizeToStablehloPassBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createVhloLegalizeToStablehloPass() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createVhloLegalizeToStablehloPass() {
  return impl::createVhloLegalizeToStablehloPass();
}
#undef GEN_PASS_DEF_VHLOLEGALIZETOSTABLEHLOPASS
#endif // GEN_PASS_DEF_VHLOLEGALIZETOSTABLEHLOPASS

//===----------------------------------------------------------------------===//
// VhloToVersionPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_VHLOTOVERSIONPASS
struct VhloToVersionPassOptions {
  std::string targetVersionOption;
};
std::unique_ptr<::mlir::Pass> createVhloToVersionPass();
std::unique_ptr<::mlir::Pass> createVhloToVersionPass(VhloToVersionPassOptions options);
#undef GEN_PASS_DECL_VHLOTOVERSIONPASS
#endif // GEN_PASS_DECL_VHLOTOVERSIONPASS
#ifdef GEN_PASS_DEF_VHLOTOVERSIONPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createVhloToVersionPass();
} // namespace impl

namespace impl {
  std::unique_ptr<::mlir::Pass> createVhloToVersionPass(VhloToVersionPassOptions options);
} // namespace impl
namespace impl {

template <typename DerivedT>
class VhloToVersionPassBase : public ::mlir::OperationPass<> {
public:
  using Base = VhloToVersionPassBase;

  VhloToVersionPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  VhloToVersionPassBase(const VhloToVersionPassBase &other) : ::mlir::OperationPass<>(other) {}
  VhloToVersionPassBase& operator=(const VhloToVersionPassBase &) = delete;
  VhloToVersionPassBase(VhloToVersionPassBase &&) = delete;
  VhloToVersionPassBase& operator=(VhloToVersionPassBase &&) = delete;
  ~VhloToVersionPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("vhlo-to-version");
  }
  ::llvm::StringRef getArgument() const override { return "vhlo-to-version"; }

  ::llvm::StringRef getDescription() const override { return "Convert between versions of VHLO."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("VhloToVersionPass");
  }
  ::llvm::StringRef getName() const override { return "VhloToVersionPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::vhlo::VhloDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(VhloToVersionPassBase<DerivedT>)

  VhloToVersionPassBase(VhloToVersionPassOptions options) : VhloToVersionPassBase() {
    targetVersionOption = std::move(options.targetVersionOption);
  }
protected:
  ::mlir::Pass::Option<std::string> targetVersionOption{*this, "target", ::llvm::cl::desc("The target version. Must be a version of the form #.#.# .")};
private:

  friend std::unique_ptr<::mlir::Pass> createVhloToVersionPass() {
    return std::make_unique<DerivedT>();
  }

  friend std::unique_ptr<::mlir::Pass> createVhloToVersionPass(VhloToVersionPassOptions options) {
    return std::make_unique<DerivedT>(std::move(options));
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createVhloToVersionPass() {
  return impl::createVhloToVersionPass();
}

std::unique_ptr<::mlir::Pass> createVhloToVersionPass(VhloToVersionPassOptions options) {
  return impl::createVhloToVersionPass(std::move(options));
}
#undef GEN_PASS_DEF_VHLOTOVERSIONPASS
#endif // GEN_PASS_DEF_VHLOTOVERSIONPASS
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// ChloLegalizeToStablehloPass Registration
//===----------------------------------------------------------------------===//

inline void registerChloLegalizeToStablehloPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createChloLegalizeToStablehloPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerChloLegalizeToStablehloPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createChloLegalizeToStablehloPass();
  });
}

//===----------------------------------------------------------------------===//
// ShapeLegalizeToStablehloPass Registration
//===----------------------------------------------------------------------===//

inline void registerShapeLegalizeToStablehloPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createShapeLegalizeToStablehloPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerShapeLegalizeToStablehloPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createShapeLegalizeToStablehloPass();
  });
}

//===----------------------------------------------------------------------===//
// StablehloAggressiveFolderPass Registration
//===----------------------------------------------------------------------===//

inline void registerStablehloAggressiveFolderPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createStablehloAggressiveFolderPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerStablehloAggressiveFolderPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createStablehloAggressiveFolderPass();
  });
}

//===----------------------------------------------------------------------===//
// StablehloAggressiveSimplificationPass Registration
//===----------------------------------------------------------------------===//

inline void registerStablehloAggressiveSimplificationPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createStablehloAggressiveSimplificationPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerStablehloAggressiveSimplificationPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createStablehloAggressiveSimplificationPass();
  });
}

//===----------------------------------------------------------------------===//
// StablehloCanonicalizeDynamismPass Registration
//===----------------------------------------------------------------------===//

inline void registerStablehloCanonicalizeDynamismPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createStablehloCanonicalizeDynamismPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerStablehloCanonicalizeDynamismPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createStablehloCanonicalizeDynamismPass();
  });
}

//===----------------------------------------------------------------------===//
// StablehloCompatibilityExpanderPass Registration
//===----------------------------------------------------------------------===//

inline void registerStablehloCompatibilityExpanderPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createStablehloCompatibilityExpanderPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerStablehloCompatibilityExpanderPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createStablehloCompatibilityExpanderPass();
  });
}

//===----------------------------------------------------------------------===//
// StablehloComplexMathExpanderPass Registration
//===----------------------------------------------------------------------===//

inline void registerStablehloComplexMathExpanderPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createStablehloComplexMathExpanderPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerStablehloComplexMathExpanderPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createStablehloComplexMathExpanderPass();
  });
}

//===----------------------------------------------------------------------===//
// StablehloConvertToSignlessPass Registration
//===----------------------------------------------------------------------===//

inline void registerStablehloConvertToSignlessPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createStablehloConvertToSignlessPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerStablehloConvertToSignlessPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createStablehloConvertToSignlessPass();
  });
}

//===----------------------------------------------------------------------===//
// StablehloLegalizeCompositeToCallPass Registration
//===----------------------------------------------------------------------===//

inline void registerStablehloLegalizeCompositeToCallPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createStablehloLegalizeCompositeToCallPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerStablehloLegalizeCompositeToCallPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createStablehloLegalizeCompositeToCallPass();
  });
}

//===----------------------------------------------------------------------===//
// StablehloLegalizeDeprecatedOpsPass Registration
//===----------------------------------------------------------------------===//

inline void registerStablehloLegalizeDeprecatedOpsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createStablehloLegalizeDeprecatedOpsPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerStablehloLegalizeDeprecatedOpsPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createStablehloLegalizeDeprecatedOpsPass();
  });
}

//===----------------------------------------------------------------------===//
// StablehloLegalizeQDQToQuantizedOpPass Registration
//===----------------------------------------------------------------------===//

inline void registerStablehloLegalizeQDQToQuantizedOpPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createStablehloLegalizeQDQToQuantizedOpPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerStablehloLegalizeQDQToQuantizedOpPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createStablehloLegalizeQDQToQuantizedOpPass();
  });
}

//===----------------------------------------------------------------------===//
// StablehloLegalizeQuantToMathPass Registration
//===----------------------------------------------------------------------===//

inline void registerStablehloLegalizeQuantToMathPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createStablehloLegalizeQuantToMathPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerStablehloLegalizeQuantToMathPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createStablehloLegalizeQuantToMathPass();
  });
}

//===----------------------------------------------------------------------===//
// StablehloLegalizeQuantizedOpToQDQPass Registration
//===----------------------------------------------------------------------===//

inline void registerStablehloLegalizeQuantizedOpToQDQPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createStablehloLegalizeQuantizedOpToQDQPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerStablehloLegalizeQuantizedOpToQDQPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createStablehloLegalizeQuantizedOpToQDQPass();
  });
}

//===----------------------------------------------------------------------===//
// StablehloLegalizeToVhloPass Registration
//===----------------------------------------------------------------------===//

inline void registerStablehloLegalizeToVhloPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createStablehloLegalizeToVhloPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerStablehloLegalizeToVhloPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createStablehloLegalizeToVhloPass();
  });
}

//===----------------------------------------------------------------------===//
// StablehloRefineArgumentsPass Registration
//===----------------------------------------------------------------------===//

inline void registerStablehloRefineArgumentsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createStablehloRefineArgumentsPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerStablehloRefineArgumentsPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createStablehloRefineArgumentsPass();
  });
}

//===----------------------------------------------------------------------===//
// StablehloRefineShapesPass Registration
//===----------------------------------------------------------------------===//

inline void registerStablehloRefineShapesPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createStablehloRefineShapesPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerStablehloRefineShapesPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createStablehloRefineShapesPass();
  });
}

//===----------------------------------------------------------------------===//
// VhloLegalizeToStablehloPass Registration
//===----------------------------------------------------------------------===//

inline void registerVhloLegalizeToStablehloPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createVhloLegalizeToStablehloPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerVhloLegalizeToStablehloPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createVhloLegalizeToStablehloPass();
  });
}

//===----------------------------------------------------------------------===//
// VhloToVersionPass Registration
//===----------------------------------------------------------------------===//

inline void registerVhloToVersionPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createVhloToVersionPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerVhloToVersionPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createVhloToVersionPass();
  });
}

//===----------------------------------------------------------------------===//
//  Registration
//===----------------------------------------------------------------------===//

inline void registerPasses() {
  registerChloLegalizeToStablehloPass();
  registerShapeLegalizeToStablehloPass();
  registerStablehloAggressiveFolderPass();
  registerStablehloAggressiveSimplificationPass();
  registerStablehloCanonicalizeDynamismPass();
  registerStablehloCompatibilityExpanderPass();
  registerStablehloComplexMathExpanderPass();
  registerStablehloConvertToSignlessPass();
  registerStablehloLegalizeCompositeToCallPass();
  registerStablehloLegalizeDeprecatedOpsPass();
  registerStablehloLegalizeQDQToQuantizedOpPass();
  registerStablehloLegalizeQuantToMathPass();
  registerStablehloLegalizeQuantizedOpToQDQPass();
  registerStablehloLegalizeToVhloPass();
  registerStablehloRefineArgumentsPass();
  registerStablehloRefineShapesPass();
  registerVhloLegalizeToStablehloPass();
  registerVhloToVersionPass();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
// Deprecated. Please use the new per-pass macros.
#ifdef GEN_PASS_CLASSES

template <typename DerivedT>
class ChloLegalizeToStablehloPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = ChloLegalizeToStablehloPassBase;

  ChloLegalizeToStablehloPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  ChloLegalizeToStablehloPassBase(const ChloLegalizeToStablehloPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  ChloLegalizeToStablehloPassBase& operator=(const ChloLegalizeToStablehloPassBase &) = delete;
  ChloLegalizeToStablehloPassBase(ChloLegalizeToStablehloPassBase &&) = delete;
  ChloLegalizeToStablehloPassBase& operator=(ChloLegalizeToStablehloPassBase &&) = delete;
  ~ChloLegalizeToStablehloPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("chlo-legalize-to-stablehlo");
  }
  ::llvm::StringRef getArgument() const override { return "chlo-legalize-to-stablehlo"; }

  ::llvm::StringRef getDescription() const override { return "Legalizes from CHLO ops flow to StableHLO and Shape ops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ChloLegalizeToStablehloPass");
  }
  ::llvm::StringRef getName() const override { return "ChloLegalizeToStablehloPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::shape::ShapeDialect>();
    registry.insert<mlir::stablehlo::StablehloDialect>();
    registry.insert<mlir::tensor::TensorDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ChloLegalizeToStablehloPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ShapeLegalizeToStablehloPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = ShapeLegalizeToStablehloPassBase;

  ShapeLegalizeToStablehloPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  ShapeLegalizeToStablehloPassBase(const ShapeLegalizeToStablehloPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  ShapeLegalizeToStablehloPassBase& operator=(const ShapeLegalizeToStablehloPassBase &) = delete;
  ShapeLegalizeToStablehloPassBase(ShapeLegalizeToStablehloPassBase &&) = delete;
  ShapeLegalizeToStablehloPassBase& operator=(ShapeLegalizeToStablehloPassBase &&) = delete;
  ~ShapeLegalizeToStablehloPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("shape-legalize-to-stablehlo");
  }
  ::llvm::StringRef getArgument() const override { return "shape-legalize-to-stablehlo"; }

  ::llvm::StringRef getDescription() const override { return "Legalize shape-related ops to StableHLO."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ShapeLegalizeToStablehloPass");
  }
  ::llvm::StringRef getName() const override { return "ShapeLegalizeToStablehloPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::stablehlo::StablehloDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ShapeLegalizeToStablehloPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class StablehloAggressiveFolderPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = StablehloAggressiveFolderPassBase;

  StablehloAggressiveFolderPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  StablehloAggressiveFolderPassBase(const StablehloAggressiveFolderPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  StablehloAggressiveFolderPassBase& operator=(const StablehloAggressiveFolderPassBase &) = delete;
  StablehloAggressiveFolderPassBase(StablehloAggressiveFolderPassBase &&) = delete;
  StablehloAggressiveFolderPassBase& operator=(StablehloAggressiveFolderPassBase &&) = delete;
  ~StablehloAggressiveFolderPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("stablehlo-aggressive-folder");
  }
  ::llvm::StringRef getArgument() const override { return "stablehlo-aggressive-folder"; }

  ::llvm::StringRef getDescription() const override { return "Folds StableHLO operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("StablehloAggressiveFolderPass");
  }
  ::llvm::StringRef getName() const override { return "StablehloAggressiveFolderPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::stablehlo::StablehloDialect>();
    registry.insert<mlir::tensor::TensorDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(StablehloAggressiveFolderPassBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> foldFloat{*this, "fold-float", ::llvm::cl::desc("Allow for potentially lossy computations using float type."), ::llvm::cl::init(true)};
};

template <typename DerivedT>
class StablehloAggressiveSimplificationPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = StablehloAggressiveSimplificationPassBase;

  StablehloAggressiveSimplificationPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  StablehloAggressiveSimplificationPassBase(const StablehloAggressiveSimplificationPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  StablehloAggressiveSimplificationPassBase& operator=(const StablehloAggressiveSimplificationPassBase &) = delete;
  StablehloAggressiveSimplificationPassBase(StablehloAggressiveSimplificationPassBase &&) = delete;
  StablehloAggressiveSimplificationPassBase& operator=(StablehloAggressiveSimplificationPassBase &&) = delete;
  ~StablehloAggressiveSimplificationPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("stablehlo-aggressive-simplification");
  }
  ::llvm::StringRef getArgument() const override { return "stablehlo-aggressive-simplification"; }

  ::llvm::StringRef getDescription() const override { return "Canonicalizes StableHLO operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("StablehloAggressiveSimplificationPass");
  }
  ::llvm::StringRef getName() const override { return "StablehloAggressiveSimplificationPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::stablehlo::StablehloDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(StablehloAggressiveSimplificationPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class StablehloCanonicalizeDynamismPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = StablehloCanonicalizeDynamismPassBase;

  StablehloCanonicalizeDynamismPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  StablehloCanonicalizeDynamismPassBase(const StablehloCanonicalizeDynamismPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  StablehloCanonicalizeDynamismPassBase& operator=(const StablehloCanonicalizeDynamismPassBase &) = delete;
  StablehloCanonicalizeDynamismPassBase(StablehloCanonicalizeDynamismPassBase &&) = delete;
  StablehloCanonicalizeDynamismPassBase& operator=(StablehloCanonicalizeDynamismPassBase &&) = delete;
  ~StablehloCanonicalizeDynamismPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("stablehlo-canonicalize-dynamism");
  }
  ::llvm::StringRef getArgument() const override { return "stablehlo-canonicalize-dynamism"; }

  ::llvm::StringRef getDescription() const override { return "Canonicalizes dynamic StableHLO ops into static ops."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("StablehloCanonicalizeDynamismPass");
  }
  ::llvm::StringRef getName() const override { return "StablehloCanonicalizeDynamismPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(StablehloCanonicalizeDynamismPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class StablehloCompatibilityExpanderPassBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = StablehloCompatibilityExpanderPassBase;

  StablehloCompatibilityExpanderPassBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  StablehloCompatibilityExpanderPassBase(const StablehloCompatibilityExpanderPassBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}
  StablehloCompatibilityExpanderPassBase& operator=(const StablehloCompatibilityExpanderPassBase &) = delete;
  StablehloCompatibilityExpanderPassBase(StablehloCompatibilityExpanderPassBase &&) = delete;
  StablehloCompatibilityExpanderPassBase& operator=(StablehloCompatibilityExpanderPassBase &&) = delete;
  ~StablehloCompatibilityExpanderPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("stablehlo-compatibility-expander");
  }
  ::llvm::StringRef getArgument() const override { return "stablehlo-compatibility-expander"; }

  ::llvm::StringRef getDescription() const override { return "Compatibility expander for StableHLO operations."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("StablehloCompatibilityExpanderPass");
  }
  ::llvm::StringRef getName() const override { return "StablehloCompatibilityExpanderPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::stablehlo::StablehloDialect>();
    registry.insert<mlir::chlo::ChloDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(StablehloCompatibilityExpanderPassBase<DerivedT>)

protected:
  ::mlir::Pass::Option<std::string> targetVersionOption{*this, "target", ::llvm::cl::desc("The target version. Must be a version of the form #.#.#.")};
};

template <typename DerivedT>
class StablehloComplexMathExpanderPassBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = StablehloComplexMathExpanderPassBase;

  StablehloComplexMathExpanderPassBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  StablehloComplexMathExpanderPassBase(const StablehloComplexMathExpanderPassBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}
  StablehloComplexMathExpanderPassBase& operator=(const StablehloComplexMathExpanderPassBase &) = delete;
  StablehloComplexMathExpanderPassBase(StablehloComplexMathExpanderPassBase &&) = delete;
  StablehloComplexMathExpanderPassBase& operator=(StablehloComplexMathExpanderPassBase &&) = delete;
  ~StablehloComplexMathExpanderPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("stablehlo-complex-math-expander");
  }
  ::llvm::StringRef getArgument() const override { return "stablehlo-complex-math-expander"; }

  ::llvm::StringRef getDescription() const override { return "Expander for StableHLO complex math operations."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("StablehloComplexMathExpanderPass");
  }
  ::llvm::StringRef getName() const override { return "StablehloComplexMathExpanderPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::stablehlo::StablehloDialect>();
    registry.insert<mlir::chlo::ChloDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(StablehloComplexMathExpanderPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class StablehloConvertToSignlessPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = StablehloConvertToSignlessPassBase;

  StablehloConvertToSignlessPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  StablehloConvertToSignlessPassBase(const StablehloConvertToSignlessPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  StablehloConvertToSignlessPassBase& operator=(const StablehloConvertToSignlessPassBase &) = delete;
  StablehloConvertToSignlessPassBase(StablehloConvertToSignlessPassBase &&) = delete;
  StablehloConvertToSignlessPassBase& operator=(StablehloConvertToSignlessPassBase &&) = delete;
  ~StablehloConvertToSignlessPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("stablehlo-convert-to-signless");
  }
  ::llvm::StringRef getArgument() const override { return "stablehlo-convert-to-signless"; }

  ::llvm::StringRef getDescription() const override { return "Pass to transform the IR to be on signless integers."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("StablehloConvertToSignlessPass");
  }
  ::llvm::StringRef getName() const override { return "StablehloConvertToSignlessPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(StablehloConvertToSignlessPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class StablehloLegalizeCompositeToCallPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = StablehloLegalizeCompositeToCallPassBase;

  StablehloLegalizeCompositeToCallPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  StablehloLegalizeCompositeToCallPassBase(const StablehloLegalizeCompositeToCallPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  StablehloLegalizeCompositeToCallPassBase& operator=(const StablehloLegalizeCompositeToCallPassBase &) = delete;
  StablehloLegalizeCompositeToCallPassBase(StablehloLegalizeCompositeToCallPassBase &&) = delete;
  StablehloLegalizeCompositeToCallPassBase& operator=(StablehloLegalizeCompositeToCallPassBase &&) = delete;
  ~StablehloLegalizeCompositeToCallPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("stablehlo-legalize-composite-to-call");
  }
  ::llvm::StringRef getArgument() const override { return "stablehlo-legalize-composite-to-call"; }

  ::llvm::StringRef getDescription() const override { return "Replaces composite ops with a call to their decomposition."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("StablehloLegalizeCompositeToCallPass");
  }
  ::llvm::StringRef getName() const override { return "StablehloLegalizeCompositeToCallPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::func::FuncDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(StablehloLegalizeCompositeToCallPassBase<DerivedT>)

protected:
  ::mlir::Pass::ListOption<std::string> exceptListOption{*this, "except", ::llvm::cl::desc("Names of composites that should not be replaced with calls.")};
};

template <typename DerivedT>
class StablehloLegalizeDeprecatedOpsPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = StablehloLegalizeDeprecatedOpsPassBase;

  StablehloLegalizeDeprecatedOpsPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  StablehloLegalizeDeprecatedOpsPassBase(const StablehloLegalizeDeprecatedOpsPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  StablehloLegalizeDeprecatedOpsPassBase& operator=(const StablehloLegalizeDeprecatedOpsPassBase &) = delete;
  StablehloLegalizeDeprecatedOpsPassBase(StablehloLegalizeDeprecatedOpsPassBase &&) = delete;
  StablehloLegalizeDeprecatedOpsPassBase& operator=(StablehloLegalizeDeprecatedOpsPassBase &&) = delete;
  ~StablehloLegalizeDeprecatedOpsPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("stablehlo-legalize-deprecated-ops");
  }
  ::llvm::StringRef getArgument() const override { return "stablehlo-legalize-deprecated-ops"; }

  ::llvm::StringRef getDescription() const override { return "Legalize deprecated ops to well-supported ops."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("StablehloLegalizeDeprecatedOpsPass");
  }
  ::llvm::StringRef getName() const override { return "StablehloLegalizeDeprecatedOpsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::stablehlo::StablehloDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(StablehloLegalizeDeprecatedOpsPassBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> failOnUnusedOps{*this, "fail-on-unused", ::llvm::cl::desc("Fail on (mostly) unused ops that are deprecated without any fallback."), ::llvm::cl::init(true)};
};

template <typename DerivedT>
class StablehloLegalizeQDQToQuantizedOpPassBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = StablehloLegalizeQDQToQuantizedOpPassBase;

  StablehloLegalizeQDQToQuantizedOpPassBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  StablehloLegalizeQDQToQuantizedOpPassBase(const StablehloLegalizeQDQToQuantizedOpPassBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}
  StablehloLegalizeQDQToQuantizedOpPassBase& operator=(const StablehloLegalizeQDQToQuantizedOpPassBase &) = delete;
  StablehloLegalizeQDQToQuantizedOpPassBase(StablehloLegalizeQDQToQuantizedOpPassBase &&) = delete;
  StablehloLegalizeQDQToQuantizedOpPassBase& operator=(StablehloLegalizeQDQToQuantizedOpPassBase &&) = delete;
  ~StablehloLegalizeQDQToQuantizedOpPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("stablehlo-legalize-qdq-to-quantized-op");
  }
  ::llvm::StringRef getArgument() const override { return "stablehlo-legalize-qdq-to-quantized-op"; }

  ::llvm::StringRef getDescription() const override { return "Fuse (de-quantize, floating-point operation and quantize) pattern into StableHLO quantized operation"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("StablehloLegalizeQDQToQuantizedOpPass");
  }
  ::llvm::StringRef getName() const override { return "StablehloLegalizeQDQToQuantizedOpPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::stablehlo::StablehloDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(StablehloLegalizeQDQToQuantizedOpPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class StablehloLegalizeQuantToMathPassBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = StablehloLegalizeQuantToMathPassBase;

  StablehloLegalizeQuantToMathPassBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  StablehloLegalizeQuantToMathPassBase(const StablehloLegalizeQuantToMathPassBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}
  StablehloLegalizeQuantToMathPassBase& operator=(const StablehloLegalizeQuantToMathPassBase &) = delete;
  StablehloLegalizeQuantToMathPassBase(StablehloLegalizeQuantToMathPassBase &&) = delete;
  StablehloLegalizeQuantToMathPassBase& operator=(StablehloLegalizeQuantToMathPassBase &&) = delete;
  ~StablehloLegalizeQuantToMathPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("stablehlo-legalize-quant-to-math");
  }
  ::llvm::StringRef getArgument() const override { return "stablehlo-legalize-quant-to-math"; }

  ::llvm::StringRef getDescription() const override { return "Convert from StableHLO quantized ops to StableHLO primitive math ops."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("StablehloLegalizeQuantToMathPass");
  }
  ::llvm::StringRef getName() const override { return "StablehloLegalizeQuantToMathPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::chlo::ChloDialect>();
    registry.insert<mlir::stablehlo::StablehloDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(StablehloLegalizeQuantToMathPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class StablehloLegalizeQuantizedOpToQDQPassBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = StablehloLegalizeQuantizedOpToQDQPassBase;

  StablehloLegalizeQuantizedOpToQDQPassBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  StablehloLegalizeQuantizedOpToQDQPassBase(const StablehloLegalizeQuantizedOpToQDQPassBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}
  StablehloLegalizeQuantizedOpToQDQPassBase& operator=(const StablehloLegalizeQuantizedOpToQDQPassBase &) = delete;
  StablehloLegalizeQuantizedOpToQDQPassBase(StablehloLegalizeQuantizedOpToQDQPassBase &&) = delete;
  StablehloLegalizeQuantizedOpToQDQPassBase& operator=(StablehloLegalizeQuantizedOpToQDQPassBase &&) = delete;
  ~StablehloLegalizeQuantizedOpToQDQPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("stablehlo-legalize-quantized-op-to-qdq");
  }
  ::llvm::StringRef getArgument() const override { return "stablehlo-legalize-quantized-op-to-qdq"; }

  ::llvm::StringRef getDescription() const override { return "Decompose quantized StableHLO operation to (de-quantize, floating-point operation and quantize) pattern."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("StablehloLegalizeQuantizedOpToQDQPass");
  }
  ::llvm::StringRef getName() const override { return "StablehloLegalizeQuantizedOpToQDQPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::stablehlo::StablehloDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(StablehloLegalizeQuantizedOpToQDQPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class StablehloLegalizeToVhloPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = StablehloLegalizeToVhloPassBase;

  StablehloLegalizeToVhloPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  StablehloLegalizeToVhloPassBase(const StablehloLegalizeToVhloPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  StablehloLegalizeToVhloPassBase& operator=(const StablehloLegalizeToVhloPassBase &) = delete;
  StablehloLegalizeToVhloPassBase(StablehloLegalizeToVhloPassBase &&) = delete;
  StablehloLegalizeToVhloPassBase& operator=(StablehloLegalizeToVhloPassBase &&) = delete;
  ~StablehloLegalizeToVhloPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("stablehlo-legalize-to-vhlo");
  }
  ::llvm::StringRef getArgument() const override { return "stablehlo-legalize-to-vhlo"; }

  ::llvm::StringRef getDescription() const override { return "Legalize StableHLO to VHLO."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("StablehloLegalizeToVhloPass");
  }
  ::llvm::StringRef getName() const override { return "StablehloLegalizeToVhloPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::vhlo::VhloDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(StablehloLegalizeToVhloPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class StablehloRefineArgumentsPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = StablehloRefineArgumentsPassBase;

  StablehloRefineArgumentsPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  StablehloRefineArgumentsPassBase(const StablehloRefineArgumentsPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  StablehloRefineArgumentsPassBase& operator=(const StablehloRefineArgumentsPassBase &) = delete;
  StablehloRefineArgumentsPassBase(StablehloRefineArgumentsPassBase &&) = delete;
  StablehloRefineArgumentsPassBase& operator=(StablehloRefineArgumentsPassBase &&) = delete;
  ~StablehloRefineArgumentsPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("stablehlo-refine-arguments");
  }
  ::llvm::StringRef getArgument() const override { return "stablehlo-refine-arguments"; }

  ::llvm::StringRef getDescription() const override { return "Refines the argument shapes of the main function."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("StablehloRefineArgumentsPass");
  }
  ::llvm::StringRef getName() const override { return "StablehloRefineArgumentsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::stablehlo::StablehloDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(StablehloRefineArgumentsPassBase<DerivedT>)

protected:
  ::mlir::Pass::ListOption<std::string> refinedTypesOption{*this, "types", ::llvm::cl::desc("The new types to be used for the main function's arguments, specified as an MLIR TypeRange 'tensor<1x2xf32>, ...'")};
};

template <typename DerivedT>
class StablehloRefineShapesPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = StablehloRefineShapesPassBase;

  StablehloRefineShapesPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  StablehloRefineShapesPassBase(const StablehloRefineShapesPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  StablehloRefineShapesPassBase& operator=(const StablehloRefineShapesPassBase &) = delete;
  StablehloRefineShapesPassBase(StablehloRefineShapesPassBase &&) = delete;
  StablehloRefineShapesPassBase& operator=(StablehloRefineShapesPassBase &&) = delete;
  ~StablehloRefineShapesPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("stablehlo-refine-shapes");
  }
  ::llvm::StringRef getArgument() const override { return "stablehlo-refine-shapes"; }

  ::llvm::StringRef getDescription() const override { return "Refines shapes across a StableHLO program."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("StablehloRefineShapesPass");
  }
  ::llvm::StringRef getName() const override { return "StablehloRefineShapesPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(StablehloRefineShapesPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class VhloLegalizeToStablehloPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = VhloLegalizeToStablehloPassBase;

  VhloLegalizeToStablehloPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  VhloLegalizeToStablehloPassBase(const VhloLegalizeToStablehloPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  VhloLegalizeToStablehloPassBase& operator=(const VhloLegalizeToStablehloPassBase &) = delete;
  VhloLegalizeToStablehloPassBase(VhloLegalizeToStablehloPassBase &&) = delete;
  VhloLegalizeToStablehloPassBase& operator=(VhloLegalizeToStablehloPassBase &&) = delete;
  ~VhloLegalizeToStablehloPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("vhlo-legalize-to-stablehlo");
  }
  ::llvm::StringRef getArgument() const override { return "vhlo-legalize-to-stablehlo"; }

  ::llvm::StringRef getDescription() const override { return "Legalize VHLO to StableHLO."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("VhloLegalizeToStablehloPass");
  }
  ::llvm::StringRef getName() const override { return "VhloLegalizeToStablehloPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::func::FuncDialect>();
    registry.insert<mlir::quant::QuantDialect>();
    registry.insert<mlir::shape::ShapeDialect>();
    registry.insert<mlir::stablehlo::StablehloDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(VhloLegalizeToStablehloPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class VhloToVersionPassBase : public ::mlir::OperationPass<> {
public:
  using Base = VhloToVersionPassBase;

  VhloToVersionPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  VhloToVersionPassBase(const VhloToVersionPassBase &other) : ::mlir::OperationPass<>(other) {}
  VhloToVersionPassBase& operator=(const VhloToVersionPassBase &) = delete;
  VhloToVersionPassBase(VhloToVersionPassBase &&) = delete;
  VhloToVersionPassBase& operator=(VhloToVersionPassBase &&) = delete;
  ~VhloToVersionPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("vhlo-to-version");
  }
  ::llvm::StringRef getArgument() const override { return "vhlo-to-version"; }

  ::llvm::StringRef getDescription() const override { return "Convert between versions of VHLO."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("VhloToVersionPass");
  }
  ::llvm::StringRef getName() const override { return "VhloToVersionPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::vhlo::VhloDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(VhloToVersionPassBase<DerivedT>)

protected:
  ::mlir::Pass::Option<std::string> targetVersionOption{*this, "target", ::llvm::cl::desc("The target version. Must be a version of the form #.#.# .")};
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
