// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/trackable_object_graph.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include <google/protobuf/wrappers.pb.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto;
namespace tensorflow {
class RegisteredSaver;
struct RegisteredSaverDefaultTypeInternal;
extern RegisteredSaverDefaultTypeInternal _RegisteredSaver_default_instance_;
class TrackableObjectGraph;
struct TrackableObjectGraphDefaultTypeInternal;
extern TrackableObjectGraphDefaultTypeInternal _TrackableObjectGraph_default_instance_;
class TrackableObjectGraph_TrackableObject;
struct TrackableObjectGraph_TrackableObjectDefaultTypeInternal;
extern TrackableObjectGraph_TrackableObjectDefaultTypeInternal _TrackableObjectGraph_TrackableObject_default_instance_;
class TrackableObjectGraph_TrackableObject_ObjectReference;
struct TrackableObjectGraph_TrackableObject_ObjectReferenceDefaultTypeInternal;
extern TrackableObjectGraph_TrackableObject_ObjectReferenceDefaultTypeInternal _TrackableObjectGraph_TrackableObject_ObjectReference_default_instance_;
class TrackableObjectGraph_TrackableObject_SerializedTensor;
struct TrackableObjectGraph_TrackableObject_SerializedTensorDefaultTypeInternal;
extern TrackableObjectGraph_TrackableObject_SerializedTensorDefaultTypeInternal _TrackableObjectGraph_TrackableObject_SerializedTensor_default_instance_;
class TrackableObjectGraph_TrackableObject_SlotVariableReference;
struct TrackableObjectGraph_TrackableObject_SlotVariableReferenceDefaultTypeInternal;
extern TrackableObjectGraph_TrackableObject_SlotVariableReferenceDefaultTypeInternal _TrackableObjectGraph_TrackableObject_SlotVariableReference_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::RegisteredSaver* Arena::CreateMaybeMessage<::tensorflow::RegisteredSaver>(Arena*);
template<> ::tensorflow::TrackableObjectGraph* Arena::CreateMaybeMessage<::tensorflow::TrackableObjectGraph>(Arena*);
template<> ::tensorflow::TrackableObjectGraph_TrackableObject* Arena::CreateMaybeMessage<::tensorflow::TrackableObjectGraph_TrackableObject>(Arena*);
template<> ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference* Arena::CreateMaybeMessage<::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference>(Arena*);
template<> ::tensorflow::TrackableObjectGraph_TrackableObject_SerializedTensor* Arena::CreateMaybeMessage<::tensorflow::TrackableObjectGraph_TrackableObject_SerializedTensor>(Arena*);
template<> ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference* Arena::CreateMaybeMessage<::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

// ===================================================================

class TrackableObjectGraph_TrackableObject_ObjectReference final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference) */ {
 public:
  inline TrackableObjectGraph_TrackableObject_ObjectReference() : TrackableObjectGraph_TrackableObject_ObjectReference(nullptr) {}
  ~TrackableObjectGraph_TrackableObject_ObjectReference() override;
  explicit PROTOBUF_CONSTEXPR TrackableObjectGraph_TrackableObject_ObjectReference(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TrackableObjectGraph_TrackableObject_ObjectReference(const TrackableObjectGraph_TrackableObject_ObjectReference& from);
  TrackableObjectGraph_TrackableObject_ObjectReference(TrackableObjectGraph_TrackableObject_ObjectReference&& from) noexcept
    : TrackableObjectGraph_TrackableObject_ObjectReference() {
    *this = ::std::move(from);
  }

  inline TrackableObjectGraph_TrackableObject_ObjectReference& operator=(const TrackableObjectGraph_TrackableObject_ObjectReference& from) {
    CopyFrom(from);
    return *this;
  }
  inline TrackableObjectGraph_TrackableObject_ObjectReference& operator=(TrackableObjectGraph_TrackableObject_ObjectReference&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TrackableObjectGraph_TrackableObject_ObjectReference& default_instance() {
    return *internal_default_instance();
  }
  static inline const TrackableObjectGraph_TrackableObject_ObjectReference* internal_default_instance() {
    return reinterpret_cast<const TrackableObjectGraph_TrackableObject_ObjectReference*>(
               &_TrackableObjectGraph_TrackableObject_ObjectReference_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(TrackableObjectGraph_TrackableObject_ObjectReference& a, TrackableObjectGraph_TrackableObject_ObjectReference& b) {
    a.Swap(&b);
  }
  inline void Swap(TrackableObjectGraph_TrackableObject_ObjectReference* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TrackableObjectGraph_TrackableObject_ObjectReference* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TrackableObjectGraph_TrackableObject_ObjectReference* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TrackableObjectGraph_TrackableObject_ObjectReference>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TrackableObjectGraph_TrackableObject_ObjectReference& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const TrackableObjectGraph_TrackableObject_ObjectReference& from) {
    TrackableObjectGraph_TrackableObject_ObjectReference::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TrackableObjectGraph_TrackableObject_ObjectReference* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference";
  }
  protected:
  explicit TrackableObjectGraph_TrackableObject_ObjectReference(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLocalNameFieldNumber = 2,
    kNodeIdFieldNumber = 1,
  };
  // string local_name = 2;
  void clear_local_name();
  const std::string& local_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_local_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_local_name();
  PROTOBUF_NODISCARD std::string* release_local_name();
  void set_allocated_local_name(std::string* local_name);
  private:
  const std::string& _internal_local_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_local_name(const std::string& value);
  std::string* _internal_mutable_local_name();
  public:

  // int32 node_id = 1;
  void clear_node_id();
  int32_t node_id() const;
  void set_node_id(int32_t value);
  private:
  int32_t _internal_node_id() const;
  void _internal_set_node_id(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr local_name_;
    int32_t node_id_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto;
};
// -------------------------------------------------------------------

class TrackableObjectGraph_TrackableObject_SerializedTensor final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor) */ {
 public:
  inline TrackableObjectGraph_TrackableObject_SerializedTensor() : TrackableObjectGraph_TrackableObject_SerializedTensor(nullptr) {}
  ~TrackableObjectGraph_TrackableObject_SerializedTensor() override;
  explicit PROTOBUF_CONSTEXPR TrackableObjectGraph_TrackableObject_SerializedTensor(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TrackableObjectGraph_TrackableObject_SerializedTensor(const TrackableObjectGraph_TrackableObject_SerializedTensor& from);
  TrackableObjectGraph_TrackableObject_SerializedTensor(TrackableObjectGraph_TrackableObject_SerializedTensor&& from) noexcept
    : TrackableObjectGraph_TrackableObject_SerializedTensor() {
    *this = ::std::move(from);
  }

  inline TrackableObjectGraph_TrackableObject_SerializedTensor& operator=(const TrackableObjectGraph_TrackableObject_SerializedTensor& from) {
    CopyFrom(from);
    return *this;
  }
  inline TrackableObjectGraph_TrackableObject_SerializedTensor& operator=(TrackableObjectGraph_TrackableObject_SerializedTensor&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TrackableObjectGraph_TrackableObject_SerializedTensor& default_instance() {
    return *internal_default_instance();
  }
  static inline const TrackableObjectGraph_TrackableObject_SerializedTensor* internal_default_instance() {
    return reinterpret_cast<const TrackableObjectGraph_TrackableObject_SerializedTensor*>(
               &_TrackableObjectGraph_TrackableObject_SerializedTensor_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(TrackableObjectGraph_TrackableObject_SerializedTensor& a, TrackableObjectGraph_TrackableObject_SerializedTensor& b) {
    a.Swap(&b);
  }
  inline void Swap(TrackableObjectGraph_TrackableObject_SerializedTensor* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TrackableObjectGraph_TrackableObject_SerializedTensor* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TrackableObjectGraph_TrackableObject_SerializedTensor* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TrackableObjectGraph_TrackableObject_SerializedTensor>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TrackableObjectGraph_TrackableObject_SerializedTensor& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const TrackableObjectGraph_TrackableObject_SerializedTensor& from) {
    TrackableObjectGraph_TrackableObject_SerializedTensor::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TrackableObjectGraph_TrackableObject_SerializedTensor* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor";
  }
  protected:
  explicit TrackableObjectGraph_TrackableObject_SerializedTensor(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
    kFullNameFieldNumber = 2,
    kCheckpointKeyFieldNumber = 3,
  };
  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // string full_name = 2;
  void clear_full_name();
  const std::string& full_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_full_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_full_name();
  PROTOBUF_NODISCARD std::string* release_full_name();
  void set_allocated_full_name(std::string* full_name);
  private:
  const std::string& _internal_full_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_full_name(const std::string& value);
  std::string* _internal_mutable_full_name();
  public:

  // string checkpoint_key = 3;
  void clear_checkpoint_key();
  const std::string& checkpoint_key() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_checkpoint_key(ArgT0&& arg0, ArgT... args);
  std::string* mutable_checkpoint_key();
  PROTOBUF_NODISCARD std::string* release_checkpoint_key();
  void set_allocated_checkpoint_key(std::string* checkpoint_key);
  private:
  const std::string& _internal_checkpoint_key() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_checkpoint_key(const std::string& value);
  std::string* _internal_mutable_checkpoint_key();
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr full_name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr checkpoint_key_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto;
};
// -------------------------------------------------------------------

class TrackableObjectGraph_TrackableObject_SlotVariableReference final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference) */ {
 public:
  inline TrackableObjectGraph_TrackableObject_SlotVariableReference() : TrackableObjectGraph_TrackableObject_SlotVariableReference(nullptr) {}
  ~TrackableObjectGraph_TrackableObject_SlotVariableReference() override;
  explicit PROTOBUF_CONSTEXPR TrackableObjectGraph_TrackableObject_SlotVariableReference(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TrackableObjectGraph_TrackableObject_SlotVariableReference(const TrackableObjectGraph_TrackableObject_SlotVariableReference& from);
  TrackableObjectGraph_TrackableObject_SlotVariableReference(TrackableObjectGraph_TrackableObject_SlotVariableReference&& from) noexcept
    : TrackableObjectGraph_TrackableObject_SlotVariableReference() {
    *this = ::std::move(from);
  }

  inline TrackableObjectGraph_TrackableObject_SlotVariableReference& operator=(const TrackableObjectGraph_TrackableObject_SlotVariableReference& from) {
    CopyFrom(from);
    return *this;
  }
  inline TrackableObjectGraph_TrackableObject_SlotVariableReference& operator=(TrackableObjectGraph_TrackableObject_SlotVariableReference&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TrackableObjectGraph_TrackableObject_SlotVariableReference& default_instance() {
    return *internal_default_instance();
  }
  static inline const TrackableObjectGraph_TrackableObject_SlotVariableReference* internal_default_instance() {
    return reinterpret_cast<const TrackableObjectGraph_TrackableObject_SlotVariableReference*>(
               &_TrackableObjectGraph_TrackableObject_SlotVariableReference_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(TrackableObjectGraph_TrackableObject_SlotVariableReference& a, TrackableObjectGraph_TrackableObject_SlotVariableReference& b) {
    a.Swap(&b);
  }
  inline void Swap(TrackableObjectGraph_TrackableObject_SlotVariableReference* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TrackableObjectGraph_TrackableObject_SlotVariableReference* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TrackableObjectGraph_TrackableObject_SlotVariableReference* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TrackableObjectGraph_TrackableObject_SlotVariableReference>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TrackableObjectGraph_TrackableObject_SlotVariableReference& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const TrackableObjectGraph_TrackableObject_SlotVariableReference& from) {
    TrackableObjectGraph_TrackableObject_SlotVariableReference::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TrackableObjectGraph_TrackableObject_SlotVariableReference* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference";
  }
  protected:
  explicit TrackableObjectGraph_TrackableObject_SlotVariableReference(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSlotNameFieldNumber = 2,
    kOriginalVariableNodeIdFieldNumber = 1,
    kSlotVariableNodeIdFieldNumber = 3,
  };
  // string slot_name = 2;
  void clear_slot_name();
  const std::string& slot_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_slot_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_slot_name();
  PROTOBUF_NODISCARD std::string* release_slot_name();
  void set_allocated_slot_name(std::string* slot_name);
  private:
  const std::string& _internal_slot_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_slot_name(const std::string& value);
  std::string* _internal_mutable_slot_name();
  public:

  // int32 original_variable_node_id = 1;
  void clear_original_variable_node_id();
  int32_t original_variable_node_id() const;
  void set_original_variable_node_id(int32_t value);
  private:
  int32_t _internal_original_variable_node_id() const;
  void _internal_set_original_variable_node_id(int32_t value);
  public:

  // int32 slot_variable_node_id = 3;
  void clear_slot_variable_node_id();
  int32_t slot_variable_node_id() const;
  void set_slot_variable_node_id(int32_t value);
  private:
  int32_t _internal_slot_variable_node_id() const;
  void _internal_set_slot_variable_node_id(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr slot_name_;
    int32_t original_variable_node_id_;
    int32_t slot_variable_node_id_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto;
};
// -------------------------------------------------------------------

class TrackableObjectGraph_TrackableObject final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.TrackableObjectGraph.TrackableObject) */ {
 public:
  inline TrackableObjectGraph_TrackableObject() : TrackableObjectGraph_TrackableObject(nullptr) {}
  ~TrackableObjectGraph_TrackableObject() override;
  explicit PROTOBUF_CONSTEXPR TrackableObjectGraph_TrackableObject(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TrackableObjectGraph_TrackableObject(const TrackableObjectGraph_TrackableObject& from);
  TrackableObjectGraph_TrackableObject(TrackableObjectGraph_TrackableObject&& from) noexcept
    : TrackableObjectGraph_TrackableObject() {
    *this = ::std::move(from);
  }

  inline TrackableObjectGraph_TrackableObject& operator=(const TrackableObjectGraph_TrackableObject& from) {
    CopyFrom(from);
    return *this;
  }
  inline TrackableObjectGraph_TrackableObject& operator=(TrackableObjectGraph_TrackableObject&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TrackableObjectGraph_TrackableObject& default_instance() {
    return *internal_default_instance();
  }
  static inline const TrackableObjectGraph_TrackableObject* internal_default_instance() {
    return reinterpret_cast<const TrackableObjectGraph_TrackableObject*>(
               &_TrackableObjectGraph_TrackableObject_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(TrackableObjectGraph_TrackableObject& a, TrackableObjectGraph_TrackableObject& b) {
    a.Swap(&b);
  }
  inline void Swap(TrackableObjectGraph_TrackableObject* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TrackableObjectGraph_TrackableObject* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TrackableObjectGraph_TrackableObject* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TrackableObjectGraph_TrackableObject>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TrackableObjectGraph_TrackableObject& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const TrackableObjectGraph_TrackableObject& from) {
    TrackableObjectGraph_TrackableObject::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TrackableObjectGraph_TrackableObject* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.TrackableObjectGraph.TrackableObject";
  }
  protected:
  explicit TrackableObjectGraph_TrackableObject(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef TrackableObjectGraph_TrackableObject_ObjectReference ObjectReference;
  typedef TrackableObjectGraph_TrackableObject_SerializedTensor SerializedTensor;
  typedef TrackableObjectGraph_TrackableObject_SlotVariableReference SlotVariableReference;

  // accessors -------------------------------------------------------

  enum : int {
    kChildrenFieldNumber = 1,
    kAttributesFieldNumber = 2,
    kSlotVariablesFieldNumber = 3,
    kRegisteredSaverFieldNumber = 4,
    kHasCheckpointValuesFieldNumber = 5,
  };
  // repeated .tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference children = 1;
  int children_size() const;
  private:
  int _internal_children_size() const;
  public:
  void clear_children();
  ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference* mutable_children(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference >*
      mutable_children();
  private:
  const ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference& _internal_children(int index) const;
  ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference* _internal_add_children();
  public:
  const ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference& children(int index) const;
  ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference* add_children();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference >&
      children() const;

  // repeated .tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor attributes = 2;
  int attributes_size() const;
  private:
  int _internal_attributes_size() const;
  public:
  void clear_attributes();
  ::tensorflow::TrackableObjectGraph_TrackableObject_SerializedTensor* mutable_attributes(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_SerializedTensor >*
      mutable_attributes();
  private:
  const ::tensorflow::TrackableObjectGraph_TrackableObject_SerializedTensor& _internal_attributes(int index) const;
  ::tensorflow::TrackableObjectGraph_TrackableObject_SerializedTensor* _internal_add_attributes();
  public:
  const ::tensorflow::TrackableObjectGraph_TrackableObject_SerializedTensor& attributes(int index) const;
  ::tensorflow::TrackableObjectGraph_TrackableObject_SerializedTensor* add_attributes();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_SerializedTensor >&
      attributes() const;

  // repeated .tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference slot_variables = 3;
  int slot_variables_size() const;
  private:
  int _internal_slot_variables_size() const;
  public:
  void clear_slot_variables();
  ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference* mutable_slot_variables(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference >*
      mutable_slot_variables();
  private:
  const ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference& _internal_slot_variables(int index) const;
  ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference* _internal_add_slot_variables();
  public:
  const ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference& slot_variables(int index) const;
  ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference* add_slot_variables();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference >&
      slot_variables() const;

  // .tensorflow.RegisteredSaver registered_saver = 4;
  bool has_registered_saver() const;
  private:
  bool _internal_has_registered_saver() const;
  public:
  void clear_registered_saver();
  const ::tensorflow::RegisteredSaver& registered_saver() const;
  PROTOBUF_NODISCARD ::tensorflow::RegisteredSaver* release_registered_saver();
  ::tensorflow::RegisteredSaver* mutable_registered_saver();
  void set_allocated_registered_saver(::tensorflow::RegisteredSaver* registered_saver);
  private:
  const ::tensorflow::RegisteredSaver& _internal_registered_saver() const;
  ::tensorflow::RegisteredSaver* _internal_mutable_registered_saver();
  public:
  void unsafe_arena_set_allocated_registered_saver(
      ::tensorflow::RegisteredSaver* registered_saver);
  ::tensorflow::RegisteredSaver* unsafe_arena_release_registered_saver();

  // .google.protobuf.BoolValue has_checkpoint_values = 5;
  bool has_has_checkpoint_values() const;
  private:
  bool _internal_has_has_checkpoint_values() const;
  public:
  void clear_has_checkpoint_values();
  const ::PROTOBUF_NAMESPACE_ID::BoolValue& has_checkpoint_values() const;
  PROTOBUF_NODISCARD ::PROTOBUF_NAMESPACE_ID::BoolValue* release_has_checkpoint_values();
  ::PROTOBUF_NAMESPACE_ID::BoolValue* mutable_has_checkpoint_values();
  void set_allocated_has_checkpoint_values(::PROTOBUF_NAMESPACE_ID::BoolValue* has_checkpoint_values);
  private:
  const ::PROTOBUF_NAMESPACE_ID::BoolValue& _internal_has_checkpoint_values() const;
  ::PROTOBUF_NAMESPACE_ID::BoolValue* _internal_mutable_has_checkpoint_values();
  public:
  void unsafe_arena_set_allocated_has_checkpoint_values(
      ::PROTOBUF_NAMESPACE_ID::BoolValue* has_checkpoint_values);
  ::PROTOBUF_NAMESPACE_ID::BoolValue* unsafe_arena_release_has_checkpoint_values();

  // @@protoc_insertion_point(class_scope:tensorflow.TrackableObjectGraph.TrackableObject)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference > children_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_SerializedTensor > attributes_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference > slot_variables_;
    ::tensorflow::RegisteredSaver* registered_saver_;
    ::PROTOBUF_NAMESPACE_ID::BoolValue* has_checkpoint_values_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto;
};
// -------------------------------------------------------------------

class TrackableObjectGraph final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.TrackableObjectGraph) */ {
 public:
  inline TrackableObjectGraph() : TrackableObjectGraph(nullptr) {}
  ~TrackableObjectGraph() override;
  explicit PROTOBUF_CONSTEXPR TrackableObjectGraph(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TrackableObjectGraph(const TrackableObjectGraph& from);
  TrackableObjectGraph(TrackableObjectGraph&& from) noexcept
    : TrackableObjectGraph() {
    *this = ::std::move(from);
  }

  inline TrackableObjectGraph& operator=(const TrackableObjectGraph& from) {
    CopyFrom(from);
    return *this;
  }
  inline TrackableObjectGraph& operator=(TrackableObjectGraph&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TrackableObjectGraph& default_instance() {
    return *internal_default_instance();
  }
  static inline const TrackableObjectGraph* internal_default_instance() {
    return reinterpret_cast<const TrackableObjectGraph*>(
               &_TrackableObjectGraph_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(TrackableObjectGraph& a, TrackableObjectGraph& b) {
    a.Swap(&b);
  }
  inline void Swap(TrackableObjectGraph* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TrackableObjectGraph* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TrackableObjectGraph* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TrackableObjectGraph>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TrackableObjectGraph& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const TrackableObjectGraph& from) {
    TrackableObjectGraph::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TrackableObjectGraph* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.TrackableObjectGraph";
  }
  protected:
  explicit TrackableObjectGraph(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef TrackableObjectGraph_TrackableObject TrackableObject;

  // accessors -------------------------------------------------------

  enum : int {
    kNodesFieldNumber = 1,
  };
  // repeated .tensorflow.TrackableObjectGraph.TrackableObject nodes = 1;
  int nodes_size() const;
  private:
  int _internal_nodes_size() const;
  public:
  void clear_nodes();
  ::tensorflow::TrackableObjectGraph_TrackableObject* mutable_nodes(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject >*
      mutable_nodes();
  private:
  const ::tensorflow::TrackableObjectGraph_TrackableObject& _internal_nodes(int index) const;
  ::tensorflow::TrackableObjectGraph_TrackableObject* _internal_add_nodes();
  public:
  const ::tensorflow::TrackableObjectGraph_TrackableObject& nodes(int index) const;
  ::tensorflow::TrackableObjectGraph_TrackableObject* add_nodes();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject >&
      nodes() const;

  // @@protoc_insertion_point(class_scope:tensorflow.TrackableObjectGraph)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject > nodes_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto;
};
// -------------------------------------------------------------------

class RegisteredSaver final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.RegisteredSaver) */ {
 public:
  inline RegisteredSaver() : RegisteredSaver(nullptr) {}
  ~RegisteredSaver() override;
  explicit PROTOBUF_CONSTEXPR RegisteredSaver(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RegisteredSaver(const RegisteredSaver& from);
  RegisteredSaver(RegisteredSaver&& from) noexcept
    : RegisteredSaver() {
    *this = ::std::move(from);
  }

  inline RegisteredSaver& operator=(const RegisteredSaver& from) {
    CopyFrom(from);
    return *this;
  }
  inline RegisteredSaver& operator=(RegisteredSaver&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RegisteredSaver& default_instance() {
    return *internal_default_instance();
  }
  static inline const RegisteredSaver* internal_default_instance() {
    return reinterpret_cast<const RegisteredSaver*>(
               &_RegisteredSaver_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(RegisteredSaver& a, RegisteredSaver& b) {
    a.Swap(&b);
  }
  inline void Swap(RegisteredSaver* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RegisteredSaver* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RegisteredSaver* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RegisteredSaver>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RegisteredSaver& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const RegisteredSaver& from) {
    RegisteredSaver::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RegisteredSaver* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.RegisteredSaver";
  }
  protected:
  explicit RegisteredSaver(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
    kObjectNameFieldNumber = 2,
  };
  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // string object_name = 2;
  void clear_object_name();
  const std::string& object_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_object_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_object_name();
  PROTOBUF_NODISCARD std::string* release_object_name();
  void set_allocated_object_name(std::string* object_name);
  private:
  const std::string& _internal_object_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_object_name(const std::string& value);
  std::string* _internal_mutable_object_name();
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.RegisteredSaver)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr object_name_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// TrackableObjectGraph_TrackableObject_ObjectReference

// int32 node_id = 1;
inline void TrackableObjectGraph_TrackableObject_ObjectReference::clear_node_id() {
  _impl_.node_id_ = 0;
}
inline int32_t TrackableObjectGraph_TrackableObject_ObjectReference::_internal_node_id() const {
  return _impl_.node_id_;
}
inline int32_t TrackableObjectGraph_TrackableObject_ObjectReference::node_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference.node_id)
  return _internal_node_id();
}
inline void TrackableObjectGraph_TrackableObject_ObjectReference::_internal_set_node_id(int32_t value) {
  
  _impl_.node_id_ = value;
}
inline void TrackableObjectGraph_TrackableObject_ObjectReference::set_node_id(int32_t value) {
  _internal_set_node_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference.node_id)
}

// string local_name = 2;
inline void TrackableObjectGraph_TrackableObject_ObjectReference::clear_local_name() {
  _impl_.local_name_.ClearToEmpty();
}
inline const std::string& TrackableObjectGraph_TrackableObject_ObjectReference::local_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference.local_name)
  return _internal_local_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void TrackableObjectGraph_TrackableObject_ObjectReference::set_local_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.local_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference.local_name)
}
inline std::string* TrackableObjectGraph_TrackableObject_ObjectReference::mutable_local_name() {
  std::string* _s = _internal_mutable_local_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference.local_name)
  return _s;
}
inline const std::string& TrackableObjectGraph_TrackableObject_ObjectReference::_internal_local_name() const {
  return _impl_.local_name_.Get();
}
inline void TrackableObjectGraph_TrackableObject_ObjectReference::_internal_set_local_name(const std::string& value) {
  
  _impl_.local_name_.Set(value, GetArenaForAllocation());
}
inline std::string* TrackableObjectGraph_TrackableObject_ObjectReference::_internal_mutable_local_name() {
  
  return _impl_.local_name_.Mutable(GetArenaForAllocation());
}
inline std::string* TrackableObjectGraph_TrackableObject_ObjectReference::release_local_name() {
  // @@protoc_insertion_point(field_release:tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference.local_name)
  return _impl_.local_name_.Release();
}
inline void TrackableObjectGraph_TrackableObject_ObjectReference::set_allocated_local_name(std::string* local_name) {
  if (local_name != nullptr) {
    
  } else {
    
  }
  _impl_.local_name_.SetAllocated(local_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.local_name_.IsDefault()) {
    _impl_.local_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference.local_name)
}

// -------------------------------------------------------------------

// TrackableObjectGraph_TrackableObject_SerializedTensor

// string name = 1;
inline void TrackableObjectGraph_TrackableObject_SerializedTensor::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& TrackableObjectGraph_TrackableObject_SerializedTensor::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void TrackableObjectGraph_TrackableObject_SerializedTensor::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.name)
}
inline std::string* TrackableObjectGraph_TrackableObject_SerializedTensor::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.name)
  return _s;
}
inline const std::string& TrackableObjectGraph_TrackableObject_SerializedTensor::_internal_name() const {
  return _impl_.name_.Get();
}
inline void TrackableObjectGraph_TrackableObject_SerializedTensor::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* TrackableObjectGraph_TrackableObject_SerializedTensor::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* TrackableObjectGraph_TrackableObject_SerializedTensor::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.name)
  return _impl_.name_.Release();
}
inline void TrackableObjectGraph_TrackableObject_SerializedTensor::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.name)
}

// string full_name = 2;
inline void TrackableObjectGraph_TrackableObject_SerializedTensor::clear_full_name() {
  _impl_.full_name_.ClearToEmpty();
}
inline const std::string& TrackableObjectGraph_TrackableObject_SerializedTensor::full_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.full_name)
  return _internal_full_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void TrackableObjectGraph_TrackableObject_SerializedTensor::set_full_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.full_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.full_name)
}
inline std::string* TrackableObjectGraph_TrackableObject_SerializedTensor::mutable_full_name() {
  std::string* _s = _internal_mutable_full_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.full_name)
  return _s;
}
inline const std::string& TrackableObjectGraph_TrackableObject_SerializedTensor::_internal_full_name() const {
  return _impl_.full_name_.Get();
}
inline void TrackableObjectGraph_TrackableObject_SerializedTensor::_internal_set_full_name(const std::string& value) {
  
  _impl_.full_name_.Set(value, GetArenaForAllocation());
}
inline std::string* TrackableObjectGraph_TrackableObject_SerializedTensor::_internal_mutable_full_name() {
  
  return _impl_.full_name_.Mutable(GetArenaForAllocation());
}
inline std::string* TrackableObjectGraph_TrackableObject_SerializedTensor::release_full_name() {
  // @@protoc_insertion_point(field_release:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.full_name)
  return _impl_.full_name_.Release();
}
inline void TrackableObjectGraph_TrackableObject_SerializedTensor::set_allocated_full_name(std::string* full_name) {
  if (full_name != nullptr) {
    
  } else {
    
  }
  _impl_.full_name_.SetAllocated(full_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.full_name_.IsDefault()) {
    _impl_.full_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.full_name)
}

// string checkpoint_key = 3;
inline void TrackableObjectGraph_TrackableObject_SerializedTensor::clear_checkpoint_key() {
  _impl_.checkpoint_key_.ClearToEmpty();
}
inline const std::string& TrackableObjectGraph_TrackableObject_SerializedTensor::checkpoint_key() const {
  // @@protoc_insertion_point(field_get:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.checkpoint_key)
  return _internal_checkpoint_key();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void TrackableObjectGraph_TrackableObject_SerializedTensor::set_checkpoint_key(ArgT0&& arg0, ArgT... args) {
 
 _impl_.checkpoint_key_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.checkpoint_key)
}
inline std::string* TrackableObjectGraph_TrackableObject_SerializedTensor::mutable_checkpoint_key() {
  std::string* _s = _internal_mutable_checkpoint_key();
  // @@protoc_insertion_point(field_mutable:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.checkpoint_key)
  return _s;
}
inline const std::string& TrackableObjectGraph_TrackableObject_SerializedTensor::_internal_checkpoint_key() const {
  return _impl_.checkpoint_key_.Get();
}
inline void TrackableObjectGraph_TrackableObject_SerializedTensor::_internal_set_checkpoint_key(const std::string& value) {
  
  _impl_.checkpoint_key_.Set(value, GetArenaForAllocation());
}
inline std::string* TrackableObjectGraph_TrackableObject_SerializedTensor::_internal_mutable_checkpoint_key() {
  
  return _impl_.checkpoint_key_.Mutable(GetArenaForAllocation());
}
inline std::string* TrackableObjectGraph_TrackableObject_SerializedTensor::release_checkpoint_key() {
  // @@protoc_insertion_point(field_release:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.checkpoint_key)
  return _impl_.checkpoint_key_.Release();
}
inline void TrackableObjectGraph_TrackableObject_SerializedTensor::set_allocated_checkpoint_key(std::string* checkpoint_key) {
  if (checkpoint_key != nullptr) {
    
  } else {
    
  }
  _impl_.checkpoint_key_.SetAllocated(checkpoint_key, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.checkpoint_key_.IsDefault()) {
    _impl_.checkpoint_key_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor.checkpoint_key)
}

// -------------------------------------------------------------------

// TrackableObjectGraph_TrackableObject_SlotVariableReference

// int32 original_variable_node_id = 1;
inline void TrackableObjectGraph_TrackableObject_SlotVariableReference::clear_original_variable_node_id() {
  _impl_.original_variable_node_id_ = 0;
}
inline int32_t TrackableObjectGraph_TrackableObject_SlotVariableReference::_internal_original_variable_node_id() const {
  return _impl_.original_variable_node_id_;
}
inline int32_t TrackableObjectGraph_TrackableObject_SlotVariableReference::original_variable_node_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference.original_variable_node_id)
  return _internal_original_variable_node_id();
}
inline void TrackableObjectGraph_TrackableObject_SlotVariableReference::_internal_set_original_variable_node_id(int32_t value) {
  
  _impl_.original_variable_node_id_ = value;
}
inline void TrackableObjectGraph_TrackableObject_SlotVariableReference::set_original_variable_node_id(int32_t value) {
  _internal_set_original_variable_node_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference.original_variable_node_id)
}

// string slot_name = 2;
inline void TrackableObjectGraph_TrackableObject_SlotVariableReference::clear_slot_name() {
  _impl_.slot_name_.ClearToEmpty();
}
inline const std::string& TrackableObjectGraph_TrackableObject_SlotVariableReference::slot_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference.slot_name)
  return _internal_slot_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void TrackableObjectGraph_TrackableObject_SlotVariableReference::set_slot_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.slot_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference.slot_name)
}
inline std::string* TrackableObjectGraph_TrackableObject_SlotVariableReference::mutable_slot_name() {
  std::string* _s = _internal_mutable_slot_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference.slot_name)
  return _s;
}
inline const std::string& TrackableObjectGraph_TrackableObject_SlotVariableReference::_internal_slot_name() const {
  return _impl_.slot_name_.Get();
}
inline void TrackableObjectGraph_TrackableObject_SlotVariableReference::_internal_set_slot_name(const std::string& value) {
  
  _impl_.slot_name_.Set(value, GetArenaForAllocation());
}
inline std::string* TrackableObjectGraph_TrackableObject_SlotVariableReference::_internal_mutable_slot_name() {
  
  return _impl_.slot_name_.Mutable(GetArenaForAllocation());
}
inline std::string* TrackableObjectGraph_TrackableObject_SlotVariableReference::release_slot_name() {
  // @@protoc_insertion_point(field_release:tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference.slot_name)
  return _impl_.slot_name_.Release();
}
inline void TrackableObjectGraph_TrackableObject_SlotVariableReference::set_allocated_slot_name(std::string* slot_name) {
  if (slot_name != nullptr) {
    
  } else {
    
  }
  _impl_.slot_name_.SetAllocated(slot_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.slot_name_.IsDefault()) {
    _impl_.slot_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference.slot_name)
}

// int32 slot_variable_node_id = 3;
inline void TrackableObjectGraph_TrackableObject_SlotVariableReference::clear_slot_variable_node_id() {
  _impl_.slot_variable_node_id_ = 0;
}
inline int32_t TrackableObjectGraph_TrackableObject_SlotVariableReference::_internal_slot_variable_node_id() const {
  return _impl_.slot_variable_node_id_;
}
inline int32_t TrackableObjectGraph_TrackableObject_SlotVariableReference::slot_variable_node_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference.slot_variable_node_id)
  return _internal_slot_variable_node_id();
}
inline void TrackableObjectGraph_TrackableObject_SlotVariableReference::_internal_set_slot_variable_node_id(int32_t value) {
  
  _impl_.slot_variable_node_id_ = value;
}
inline void TrackableObjectGraph_TrackableObject_SlotVariableReference::set_slot_variable_node_id(int32_t value) {
  _internal_set_slot_variable_node_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference.slot_variable_node_id)
}

// -------------------------------------------------------------------

// TrackableObjectGraph_TrackableObject

// repeated .tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference children = 1;
inline int TrackableObjectGraph_TrackableObject::_internal_children_size() const {
  return _impl_.children_.size();
}
inline int TrackableObjectGraph_TrackableObject::children_size() const {
  return _internal_children_size();
}
inline void TrackableObjectGraph_TrackableObject::clear_children() {
  _impl_.children_.Clear();
}
inline ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference* TrackableObjectGraph_TrackableObject::mutable_children(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.TrackableObjectGraph.TrackableObject.children)
  return _impl_.children_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference >*
TrackableObjectGraph_TrackableObject::mutable_children() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.TrackableObjectGraph.TrackableObject.children)
  return &_impl_.children_;
}
inline const ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference& TrackableObjectGraph_TrackableObject::_internal_children(int index) const {
  return _impl_.children_.Get(index);
}
inline const ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference& TrackableObjectGraph_TrackableObject::children(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.TrackableObjectGraph.TrackableObject.children)
  return _internal_children(index);
}
inline ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference* TrackableObjectGraph_TrackableObject::_internal_add_children() {
  return _impl_.children_.Add();
}
inline ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference* TrackableObjectGraph_TrackableObject::add_children() {
  ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference* _add = _internal_add_children();
  // @@protoc_insertion_point(field_add:tensorflow.TrackableObjectGraph.TrackableObject.children)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference >&
TrackableObjectGraph_TrackableObject::children() const {
  // @@protoc_insertion_point(field_list:tensorflow.TrackableObjectGraph.TrackableObject.children)
  return _impl_.children_;
}

// repeated .tensorflow.TrackableObjectGraph.TrackableObject.SerializedTensor attributes = 2;
inline int TrackableObjectGraph_TrackableObject::_internal_attributes_size() const {
  return _impl_.attributes_.size();
}
inline int TrackableObjectGraph_TrackableObject::attributes_size() const {
  return _internal_attributes_size();
}
inline void TrackableObjectGraph_TrackableObject::clear_attributes() {
  _impl_.attributes_.Clear();
}
inline ::tensorflow::TrackableObjectGraph_TrackableObject_SerializedTensor* TrackableObjectGraph_TrackableObject::mutable_attributes(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.TrackableObjectGraph.TrackableObject.attributes)
  return _impl_.attributes_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_SerializedTensor >*
TrackableObjectGraph_TrackableObject::mutable_attributes() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.TrackableObjectGraph.TrackableObject.attributes)
  return &_impl_.attributes_;
}
inline const ::tensorflow::TrackableObjectGraph_TrackableObject_SerializedTensor& TrackableObjectGraph_TrackableObject::_internal_attributes(int index) const {
  return _impl_.attributes_.Get(index);
}
inline const ::tensorflow::TrackableObjectGraph_TrackableObject_SerializedTensor& TrackableObjectGraph_TrackableObject::attributes(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.TrackableObjectGraph.TrackableObject.attributes)
  return _internal_attributes(index);
}
inline ::tensorflow::TrackableObjectGraph_TrackableObject_SerializedTensor* TrackableObjectGraph_TrackableObject::_internal_add_attributes() {
  return _impl_.attributes_.Add();
}
inline ::tensorflow::TrackableObjectGraph_TrackableObject_SerializedTensor* TrackableObjectGraph_TrackableObject::add_attributes() {
  ::tensorflow::TrackableObjectGraph_TrackableObject_SerializedTensor* _add = _internal_add_attributes();
  // @@protoc_insertion_point(field_add:tensorflow.TrackableObjectGraph.TrackableObject.attributes)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_SerializedTensor >&
TrackableObjectGraph_TrackableObject::attributes() const {
  // @@protoc_insertion_point(field_list:tensorflow.TrackableObjectGraph.TrackableObject.attributes)
  return _impl_.attributes_;
}

// repeated .tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference slot_variables = 3;
inline int TrackableObjectGraph_TrackableObject::_internal_slot_variables_size() const {
  return _impl_.slot_variables_.size();
}
inline int TrackableObjectGraph_TrackableObject::slot_variables_size() const {
  return _internal_slot_variables_size();
}
inline void TrackableObjectGraph_TrackableObject::clear_slot_variables() {
  _impl_.slot_variables_.Clear();
}
inline ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference* TrackableObjectGraph_TrackableObject::mutable_slot_variables(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.TrackableObjectGraph.TrackableObject.slot_variables)
  return _impl_.slot_variables_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference >*
TrackableObjectGraph_TrackableObject::mutable_slot_variables() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.TrackableObjectGraph.TrackableObject.slot_variables)
  return &_impl_.slot_variables_;
}
inline const ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference& TrackableObjectGraph_TrackableObject::_internal_slot_variables(int index) const {
  return _impl_.slot_variables_.Get(index);
}
inline const ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference& TrackableObjectGraph_TrackableObject::slot_variables(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.TrackableObjectGraph.TrackableObject.slot_variables)
  return _internal_slot_variables(index);
}
inline ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference* TrackableObjectGraph_TrackableObject::_internal_add_slot_variables() {
  return _impl_.slot_variables_.Add();
}
inline ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference* TrackableObjectGraph_TrackableObject::add_slot_variables() {
  ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference* _add = _internal_add_slot_variables();
  // @@protoc_insertion_point(field_add:tensorflow.TrackableObjectGraph.TrackableObject.slot_variables)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference >&
TrackableObjectGraph_TrackableObject::slot_variables() const {
  // @@protoc_insertion_point(field_list:tensorflow.TrackableObjectGraph.TrackableObject.slot_variables)
  return _impl_.slot_variables_;
}

// .tensorflow.RegisteredSaver registered_saver = 4;
inline bool TrackableObjectGraph_TrackableObject::_internal_has_registered_saver() const {
  return this != internal_default_instance() && _impl_.registered_saver_ != nullptr;
}
inline bool TrackableObjectGraph_TrackableObject::has_registered_saver() const {
  return _internal_has_registered_saver();
}
inline void TrackableObjectGraph_TrackableObject::clear_registered_saver() {
  if (GetArenaForAllocation() == nullptr && _impl_.registered_saver_ != nullptr) {
    delete _impl_.registered_saver_;
  }
  _impl_.registered_saver_ = nullptr;
}
inline const ::tensorflow::RegisteredSaver& TrackableObjectGraph_TrackableObject::_internal_registered_saver() const {
  const ::tensorflow::RegisteredSaver* p = _impl_.registered_saver_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::RegisteredSaver&>(
      ::tensorflow::_RegisteredSaver_default_instance_);
}
inline const ::tensorflow::RegisteredSaver& TrackableObjectGraph_TrackableObject::registered_saver() const {
  // @@protoc_insertion_point(field_get:tensorflow.TrackableObjectGraph.TrackableObject.registered_saver)
  return _internal_registered_saver();
}
inline void TrackableObjectGraph_TrackableObject::unsafe_arena_set_allocated_registered_saver(
    ::tensorflow::RegisteredSaver* registered_saver) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.registered_saver_);
  }
  _impl_.registered_saver_ = registered_saver;
  if (registered_saver) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.TrackableObjectGraph.TrackableObject.registered_saver)
}
inline ::tensorflow::RegisteredSaver* TrackableObjectGraph_TrackableObject::release_registered_saver() {
  
  ::tensorflow::RegisteredSaver* temp = _impl_.registered_saver_;
  _impl_.registered_saver_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::RegisteredSaver* TrackableObjectGraph_TrackableObject::unsafe_arena_release_registered_saver() {
  // @@protoc_insertion_point(field_release:tensorflow.TrackableObjectGraph.TrackableObject.registered_saver)
  
  ::tensorflow::RegisteredSaver* temp = _impl_.registered_saver_;
  _impl_.registered_saver_ = nullptr;
  return temp;
}
inline ::tensorflow::RegisteredSaver* TrackableObjectGraph_TrackableObject::_internal_mutable_registered_saver() {
  
  if (_impl_.registered_saver_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::RegisteredSaver>(GetArenaForAllocation());
    _impl_.registered_saver_ = p;
  }
  return _impl_.registered_saver_;
}
inline ::tensorflow::RegisteredSaver* TrackableObjectGraph_TrackableObject::mutable_registered_saver() {
  ::tensorflow::RegisteredSaver* _msg = _internal_mutable_registered_saver();
  // @@protoc_insertion_point(field_mutable:tensorflow.TrackableObjectGraph.TrackableObject.registered_saver)
  return _msg;
}
inline void TrackableObjectGraph_TrackableObject::set_allocated_registered_saver(::tensorflow::RegisteredSaver* registered_saver) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.registered_saver_;
  }
  if (registered_saver) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(registered_saver);
    if (message_arena != submessage_arena) {
      registered_saver = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, registered_saver, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.registered_saver_ = registered_saver;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TrackableObjectGraph.TrackableObject.registered_saver)
}

// .google.protobuf.BoolValue has_checkpoint_values = 5;
inline bool TrackableObjectGraph_TrackableObject::_internal_has_has_checkpoint_values() const {
  return this != internal_default_instance() && _impl_.has_checkpoint_values_ != nullptr;
}
inline bool TrackableObjectGraph_TrackableObject::has_has_checkpoint_values() const {
  return _internal_has_has_checkpoint_values();
}
inline const ::PROTOBUF_NAMESPACE_ID::BoolValue& TrackableObjectGraph_TrackableObject::_internal_has_checkpoint_values() const {
  const ::PROTOBUF_NAMESPACE_ID::BoolValue* p = _impl_.has_checkpoint_values_;
  return p != nullptr ? *p : reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::BoolValue&>(
      ::PROTOBUF_NAMESPACE_ID::_BoolValue_default_instance_);
}
inline const ::PROTOBUF_NAMESPACE_ID::BoolValue& TrackableObjectGraph_TrackableObject::has_checkpoint_values() const {
  // @@protoc_insertion_point(field_get:tensorflow.TrackableObjectGraph.TrackableObject.has_checkpoint_values)
  return _internal_has_checkpoint_values();
}
inline void TrackableObjectGraph_TrackableObject::unsafe_arena_set_allocated_has_checkpoint_values(
    ::PROTOBUF_NAMESPACE_ID::BoolValue* has_checkpoint_values) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.has_checkpoint_values_);
  }
  _impl_.has_checkpoint_values_ = has_checkpoint_values;
  if (has_checkpoint_values) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.TrackableObjectGraph.TrackableObject.has_checkpoint_values)
}
inline ::PROTOBUF_NAMESPACE_ID::BoolValue* TrackableObjectGraph_TrackableObject::release_has_checkpoint_values() {
  
  ::PROTOBUF_NAMESPACE_ID::BoolValue* temp = _impl_.has_checkpoint_values_;
  _impl_.has_checkpoint_values_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::BoolValue* TrackableObjectGraph_TrackableObject::unsafe_arena_release_has_checkpoint_values() {
  // @@protoc_insertion_point(field_release:tensorflow.TrackableObjectGraph.TrackableObject.has_checkpoint_values)
  
  ::PROTOBUF_NAMESPACE_ID::BoolValue* temp = _impl_.has_checkpoint_values_;
  _impl_.has_checkpoint_values_ = nullptr;
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::BoolValue* TrackableObjectGraph_TrackableObject::_internal_mutable_has_checkpoint_values() {
  
  if (_impl_.has_checkpoint_values_ == nullptr) {
    auto* p = CreateMaybeMessage<::PROTOBUF_NAMESPACE_ID::BoolValue>(GetArenaForAllocation());
    _impl_.has_checkpoint_values_ = p;
  }
  return _impl_.has_checkpoint_values_;
}
inline ::PROTOBUF_NAMESPACE_ID::BoolValue* TrackableObjectGraph_TrackableObject::mutable_has_checkpoint_values() {
  ::PROTOBUF_NAMESPACE_ID::BoolValue* _msg = _internal_mutable_has_checkpoint_values();
  // @@protoc_insertion_point(field_mutable:tensorflow.TrackableObjectGraph.TrackableObject.has_checkpoint_values)
  return _msg;
}
inline void TrackableObjectGraph_TrackableObject::set_allocated_has_checkpoint_values(::PROTOBUF_NAMESPACE_ID::BoolValue* has_checkpoint_values) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.has_checkpoint_values_);
  }
  if (has_checkpoint_values) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(has_checkpoint_values));
    if (message_arena != submessage_arena) {
      has_checkpoint_values = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, has_checkpoint_values, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.has_checkpoint_values_ = has_checkpoint_values;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TrackableObjectGraph.TrackableObject.has_checkpoint_values)
}

// -------------------------------------------------------------------

// TrackableObjectGraph

// repeated .tensorflow.TrackableObjectGraph.TrackableObject nodes = 1;
inline int TrackableObjectGraph::_internal_nodes_size() const {
  return _impl_.nodes_.size();
}
inline int TrackableObjectGraph::nodes_size() const {
  return _internal_nodes_size();
}
inline void TrackableObjectGraph::clear_nodes() {
  _impl_.nodes_.Clear();
}
inline ::tensorflow::TrackableObjectGraph_TrackableObject* TrackableObjectGraph::mutable_nodes(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.TrackableObjectGraph.nodes)
  return _impl_.nodes_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject >*
TrackableObjectGraph::mutable_nodes() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.TrackableObjectGraph.nodes)
  return &_impl_.nodes_;
}
inline const ::tensorflow::TrackableObjectGraph_TrackableObject& TrackableObjectGraph::_internal_nodes(int index) const {
  return _impl_.nodes_.Get(index);
}
inline const ::tensorflow::TrackableObjectGraph_TrackableObject& TrackableObjectGraph::nodes(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.TrackableObjectGraph.nodes)
  return _internal_nodes(index);
}
inline ::tensorflow::TrackableObjectGraph_TrackableObject* TrackableObjectGraph::_internal_add_nodes() {
  return _impl_.nodes_.Add();
}
inline ::tensorflow::TrackableObjectGraph_TrackableObject* TrackableObjectGraph::add_nodes() {
  ::tensorflow::TrackableObjectGraph_TrackableObject* _add = _internal_add_nodes();
  // @@protoc_insertion_point(field_add:tensorflow.TrackableObjectGraph.nodes)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject >&
TrackableObjectGraph::nodes() const {
  // @@protoc_insertion_point(field_list:tensorflow.TrackableObjectGraph.nodes)
  return _impl_.nodes_;
}

// -------------------------------------------------------------------

// RegisteredSaver

// string name = 1;
inline void RegisteredSaver::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& RegisteredSaver::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.RegisteredSaver.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RegisteredSaver::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.RegisteredSaver.name)
}
inline std::string* RegisteredSaver::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.RegisteredSaver.name)
  return _s;
}
inline const std::string& RegisteredSaver::_internal_name() const {
  return _impl_.name_.Get();
}
inline void RegisteredSaver::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* RegisteredSaver::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* RegisteredSaver::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.RegisteredSaver.name)
  return _impl_.name_.Release();
}
inline void RegisteredSaver::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RegisteredSaver.name)
}

// string object_name = 2;
inline void RegisteredSaver::clear_object_name() {
  _impl_.object_name_.ClearToEmpty();
}
inline const std::string& RegisteredSaver::object_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.RegisteredSaver.object_name)
  return _internal_object_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RegisteredSaver::set_object_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.object_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.RegisteredSaver.object_name)
}
inline std::string* RegisteredSaver::mutable_object_name() {
  std::string* _s = _internal_mutable_object_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.RegisteredSaver.object_name)
  return _s;
}
inline const std::string& RegisteredSaver::_internal_object_name() const {
  return _impl_.object_name_.Get();
}
inline void RegisteredSaver::_internal_set_object_name(const std::string& value) {
  
  _impl_.object_name_.Set(value, GetArenaForAllocation());
}
inline std::string* RegisteredSaver::_internal_mutable_object_name() {
  
  return _impl_.object_name_.Mutable(GetArenaForAllocation());
}
inline std::string* RegisteredSaver::release_object_name() {
  // @@protoc_insertion_point(field_release:tensorflow.RegisteredSaver.object_name)
  return _impl_.object_name_.Release();
}
inline void RegisteredSaver::set_allocated_object_name(std::string* object_name) {
  if (object_name != nullptr) {
    
  } else {
    
  }
  _impl_.object_name_.SetAllocated(object_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.object_name_.IsDefault()) {
    _impl_.object_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RegisteredSaver.object_name)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2ftrackable_5fobject_5fgraph_2eproto
