.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_dn_import" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_dn_import \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_dn_import(gnutls_x509_dn_t " dn ", const gnutls_datum_t * " data ");"
.SH ARGUMENTS
.IP "gnutls_x509_dn_t dn" 12
the structure that will hold the imported DN
.IP "const gnutls_datum_t * data" 12
should contain a DER encoded RDN sequence
.SH "DESCRIPTION"
This function parses an RDN sequence and stores the result to a
\fBgnutls_x509_dn_t\fP type. The data must have been initialized
with \fBgnutls_x509_dn_init()\fP. You may use \fBgnutls_x509_dn_get_rdn_ava()\fP to
decode the DN.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "SINCE"
2.4.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
