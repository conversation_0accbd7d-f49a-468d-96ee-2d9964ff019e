/* Autogenerated by mlir-tblgen; don't manually edit */

#ifdef GEN_PASS_DECL
// Generate declarations for all passes.
#define GEN_PASS_DECL_INFERENCEMETRICSPASS
#undef GEN_PASS_DECL
#endif // GEN_PASS_DECL

//===----------------------------------------------------------------------===//
// InferenceMetricsPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_INFERENCEMETRICSPASS
#undef GEN_PASS_DECL_INFERENCEMETRICSPASS
#endif // GEN_PASS_DECL_INFERENCEMETRICSPASS
#ifdef GEN_PASS_DEF_INFERENCEMETRICSPASS
namespace impl {

template <typename DerivedT>
class InferenceMetricsPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = InferenceMetricsPassBase;

  InferenceMetricsPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  InferenceMetricsPassBase(const InferenceMetricsPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  InferenceMetricsPassBase& operator=(const InferenceMetricsPassBase &) = delete;
  InferenceMetricsPassBase(InferenceMetricsPassBase &&) = delete;
  InferenceMetricsPassBase& operator=(InferenceMetricsPassBase &&) = delete;
  ~InferenceMetricsPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf2xla-inference-metrics-pass");
  }
  ::llvm::StringRef getArgument() const override { return "tf2xla-inference-metrics-pass"; }

  ::llvm::StringRef getDescription() const override { return "A pass to go over a Module and collect various metrics"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("InferenceMetricsPass");
  }
  ::llvm::StringRef getName() const override { return "InferenceMetricsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(InferenceMetricsPassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_INFERENCEMETRICSPASS
#endif // GEN_PASS_DEF_INFERENCEMETRICSPASS
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// InferenceMetricsPass Registration
//===----------------------------------------------------------------------===//

inline void registerInferenceMetricsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return internal::CreateInferenceMetricsPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerInferenceMetricsPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return internal::CreateInferenceMetricsPass();
  });
}

//===----------------------------------------------------------------------===//
// TF2XLA Registration
//===----------------------------------------------------------------------===//

inline void registerTF2XLAPasses() {
  registerInferenceMetricsPass();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
// Deprecated. Please use the new per-pass macros.
#ifdef GEN_PASS_CLASSES

template <typename DerivedT>
class InferenceMetricsPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = InferenceMetricsPassBase;

  InferenceMetricsPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  InferenceMetricsPassBase(const InferenceMetricsPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  InferenceMetricsPassBase& operator=(const InferenceMetricsPassBase &) = delete;
  InferenceMetricsPassBase(InferenceMetricsPassBase &&) = delete;
  InferenceMetricsPassBase& operator=(InferenceMetricsPassBase &&) = delete;
  ~InferenceMetricsPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf2xla-inference-metrics-pass");
  }
  ::llvm::StringRef getArgument() const override { return "tf2xla-inference-metrics-pass"; }

  ::llvm::StringRef getDescription() const override { return "A pass to go over a Module and collect various metrics"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("InferenceMetricsPass");
  }
  ::llvm::StringRef getName() const override { return "InferenceMetricsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(InferenceMetricsPassBase<DerivedT>)

protected:
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
