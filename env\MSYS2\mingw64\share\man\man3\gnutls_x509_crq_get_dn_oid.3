.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crq_get_dn_oid" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crq_get_dn_oid \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crq_get_dn_oid(gnutls_x509_crq_t " crq ", unsigned " indx ", void * " oid ", size_t * " sizeof_oid ");"
.SH ARGUMENTS
.IP "gnutls_x509_crq_t crq" 12
should contain a gnutls_x509_crq_t type
.IP "unsigned indx" 12
Specifies which DN OID to get. Use (0) to get the first one.
.IP "void * oid" 12
a pointer to a structure to hold the name (may be \fBNULL\fP)
.IP "size_t * sizeof_oid" 12
initially holds the size of  \fIoid\fP 
.SH "DESCRIPTION"
This function will extract the requested OID of the name of the
certificate request subject, specified by the given index.
.SH "RETURNS"
\fBGNUTLS_E_SHORT_MEMORY_BUFFER\fP if the provided buffer is
not long enough, and in that case the * \fIsizeof_oid\fP will be
updated with the required size.  On success 0 is returned.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
