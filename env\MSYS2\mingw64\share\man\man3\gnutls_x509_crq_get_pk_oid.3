.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crq_get_pk_oid" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crq_get_pk_oid \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crq_get_pk_oid(gnutls_x509_crq_t " crq ", char * " oid ", size_t * " oid_size ");"
.SH ARGUMENTS
.IP "gnutls_x509_crq_t crq" 12
should contain a \fBgnutls_x509_crq_t\fP type
.IP "char * oid" 12
a pointer to a buffer to hold the OID (may be null)
.IP "size_t * oid_size" 12
initially holds the size of  \fIoid\fP 
.SH "DESCRIPTION"
This function will return the OID of the public key algorithm
on that certificate request. This function
is useful in the case \fBgnutls_x509_crq_get_pk_algorithm()\fP
returned \fBGNUTLS_PK_UNKNOWN\fP.
.SH "RETURNS"
zero or a negative error code on error.
.SH "SINCE"
3.5.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
