/*
 * Copyright 2021 R<PERSON><PERSON> for CodeWeavers
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

#ifdef __WIDL__
#pragma winrt ns_prefix
#endif

#ifndef DO_NO_IMPORTS
import "inspectable.idl";
import "asyncinfo.idl";
import "eventtoken.idl";
import "windowscontracts.idl";
import "windows.foundation.idl";
import "windows.system.idl";
#endif

namespace Windows {
    namespace Globalization {
        typedef enum DayOfWeek DayOfWeek;
        typedef enum LanguageLayoutDirection LanguageLayoutDirection;
        interface IApplicationLanguagesStatics;
        interface IApplicationLanguagesStatics2;
        interface ICalendar;
        interface ICalendarFactory;
        interface ICalendarFactory2;
        interface ILanguage;
        interface ILanguage2;
        interface ILanguage3;
        interface ILanguageExtensionSubtags;
        interface ILanguageFactory;
        interface ILanguageStatics;
        interface ILanguageStatics2;
        interface ILanguageStatics3;
        interface IGeographicRegion;
        interface IGeographicRegionFactory;
        interface IGeographicRegionStatics;
        interface ITimeZoneOnCalendar;
        runtimeclass ApplicationLanguages;
        runtimeclass Calendar;
        runtimeclass Language;
        runtimeclass GeographicRegion;
    }
}

namespace Windows {
    namespace Globalization {
        declare{
            interface Windows.Foundation.Collections.IIterable<Windows.Globalization.Language*>;
            interface Windows.Foundation.Collections.IIterator<Windows.Globalization.Language*>;
            interface Windows.Foundation.Collections.IVectorView<Windows.Globalization.Language*>;
            interface Windows.Foundation.Collections.IVector<Windows.Globalization.Language*>;
        }
    }
}


namespace Windows {
    namespace Globalization {
        [contract(Windows.Foundation.UniversalApiContract, 1.0)]
        enum DayOfWeek
        {
            Sunday    = 0,
            Monday    = 1,
            Tuesday   = 2,
            Wednesday = 3,
            Thursday  = 4,
            Friday    = 5,
            Saturday  = 6
        };

        [contract(Windows.Foundation.UniversalApiContract, 6.0)]
        enum LanguageLayoutDirection
        {
            Ltr = 0,
            Rtl = 1,
            TtbLtr = 2,
            TtbRtl = 3,
        };

        [
            contract(Windows.Foundation.UniversalApiContract, 1.0),
            exclusiveto(Windows.Globalization.Calendar),
            uuid(ca30221d-86d9-40fb-a26b-d44eb7cf08ea)
        ]
        interface ICalendar : IInspectable
        {
            HRESULT Clone([out, retval] Windows.Globalization.Calendar **value);
            HRESULT SetToMin();
            HRESULT SetToMax();
            [propget] HRESULT Languages([out, retval] Windows.Foundation.Collections.IVectorView<HSTRING> **value);
            [propget] HRESULT NumeralSystem([out, retval] HSTRING *value);
            [propput] HRESULT NumeralSystem([in] HSTRING value);
            HRESULT GetCalendarSystem([out, retval] HSTRING *value);
            HRESULT ChangeCalendarSystem([in] HSTRING value);
            HRESULT GetClock([out, retval] HSTRING *value);
            HRESULT ChangeClock([in] HSTRING value);
            HRESULT GetDateTime([out, retval] Windows.Foundation.DateTime *result);
            HRESULT SetDateTime([in] Windows.Foundation.DateTime value);
            HRESULT SetToNow();
            [propget] HRESULT FirstEra([out, retval] INT32 *value);
            [propget] HRESULT LastEra([out, retval] INT32 *value);
            [propget] HRESULT NumberOfEras([out, retval] INT32 *value);
            [propget] HRESULT Era([out, retval] INT32 *value);
            [propput] HRESULT Era([in] INT32 value);
            HRESULT AddEras([in] INT32 eras);
            [overload("EraAsString")] HRESULT EraAsFullString([out, retval] HSTRING *result);
            [overload("EraAsString")] HRESULT EraAsString([in] INT32 ideal_length, [out, retval] HSTRING *result);
            [propget] HRESULT FirstYearInThisEra([out, retval] INT32 *value);
            [propget] HRESULT LastYearInThisEra([out, retval] INT32 *value);
            [propget] HRESULT NumberOfYearsInThisEra([out, retval] INT32 *value);
            [propget] HRESULT Year([out, retval] INT32 *value);
            [propput] HRESULT Year([in] INT32 value);
            HRESULT AddYears([in] INT32 years);
            HRESULT YearAsString([out, retval] HSTRING *result);
            HRESULT YearAsTruncatedString([in] INT32 remaining_digits, [out, retval] HSTRING *result);
            HRESULT YearAsPaddedString([in] INT32 min_digits, [out, retval] HSTRING *result);
            [propget] HRESULT FirstMonthInThisYear([out, retval] INT32 *value);
            [propget] HRESULT LastMonthInThisYear([out, retval] INT32 *value);
            [propget] HRESULT NumberOfMonthsInThisYear([out, retval] INT32 *value);
            [propget] HRESULT Month([out, retval] INT32 *value);
            [propput] HRESULT Month([in] INT32 value);
            HRESULT AddMonths([in] INT32 months);
            [overload("MonthAsString")] HRESULT MonthAsFullString([out, retval] HSTRING *result);
            [overload("MonthAsString")] HRESULT MonthAsString([in] INT32 ideal_length, [out, retval] HSTRING *result);
            [overload("MonthAsSoloString")] HRESULT MonthAsFullSoloString([out, retval] HSTRING *result);
            [overload("MonthAsSoloString")] HRESULT MonthAsSoloString([in] INT32 ideal_length, [out, retval] HSTRING *result);
            HRESULT MonthAsNumericString([out, retval] HSTRING *result);
            HRESULT MonthAsPaddedNumericString([in] INT32 min_digits, [out, retval] HSTRING *result);
            HRESULT AddWeeks([in] INT32 weeks);
            [propget] HRESULT FirstDayInThisMonth([out, retval] INT32 *value);
            [propget] HRESULT LastDayInThisMonth([out, retval] INT32 *value);
            [propget] HRESULT NumberOfDaysInThisMonth([out, retval] INT32 *value);
            [propget] HRESULT Day([out, retval] INT32 *value);
            [propput] HRESULT Day([in] INT32 value);
            HRESULT AddDays([in] INT32 days);
            HRESULT DayAsString([out, retval] HSTRING *result);
            HRESULT DayAsPaddedString([in] INT32 min_digits, [out, retval] HSTRING *result);
            [propget] HRESULT DayOfWeek([out, retval] Windows.Globalization.DayOfWeek *value);
            [overload("DayOfWeekAsString")] HRESULT DayOfWeekAsFullString([out, retval] HSTRING *result);
            [overload("DayOfWeekAsString")] HRESULT DayOfWeekAsString([in] INT32 ideal_length, [out, retval] HSTRING *result);
            [overload("DayOfWeekAsSoloString")] HRESULT DayOfWeekAsFullSoloString([out, retval] HSTRING *result);
            [overload("DayOfWeekAsSoloString")] HRESULT DayOfWeekAsSoloString([in] INT32 ideal_length, [out, retval] HSTRING *result);
            [propget] HRESULT FirstPeriodInThisDay([out, retval] INT32 *value);
            [propget] HRESULT LastPeriodInThisDay([out, retval] INT32 *value);
            [propget] HRESULT NumberOfPeriodsInThisDay([out, retval] INT32 *value);
            [propget] HRESULT Period([out, retval] INT32 *value);
            [propput] HRESULT Period([in] INT32 value);
            HRESULT AddPeriods([in] INT32 periods);
            [overload("PeriodAsString")] HRESULT PeriodAsFullString([out, retval] HSTRING *result);
            [overload("PeriodAsString")] HRESULT PeriodAsString([in] INT32 ideal_length, [out, retval] HSTRING *result);
            [propget] HRESULT FirstHourInThisPeriod([out, retval] INT32 *value);
            [propget] HRESULT LastHourInThisPeriod([out, retval] INT32 *value);
            [propget] HRESULT NumberOfHoursInThisPeriod([out, retval] INT32 *value);
            [propget] HRESULT Hour([out, retval] INT32 *value);
            [propput] HRESULT Hour([in] INT32 value);
            HRESULT AddHours([in] INT32 hours);
            HRESULT HourAsString([out, retval] HSTRING *result);
            HRESULT HourAsPaddedString([in] INT32 min_digits, [out, retval] HSTRING *result);
            [propget] HRESULT Minute([out, retval] INT32 *value);
            [propput] HRESULT Minute([in] INT32 value);
            HRESULT AddMinutes([in] INT32 minutes);
            HRESULT MinuteAsString([out, retval] HSTRING *result);
            HRESULT MinuteAsPaddedString([in] INT32 min_digits, [out, retval] HSTRING *result);
            [propget] HRESULT Second([out, retval] INT32 *value);
            [propput] HRESULT Second([in] INT32 value);
            HRESULT AddSeconds([in] INT32 seconds);
            HRESULT SecondAsString([out, retval] HSTRING *result);
            HRESULT SecondAsPaddedString([in] INT32 min_digits, [out, retval] HSTRING *result);
            [propget] HRESULT Nanosecond([out, retval] INT32 *value);
            [propput] HRESULT Nanosecond([in] INT32 value);
            HRESULT AddNanoseconds([in] INT32 nanoseconds);
            HRESULT NanosecondAsString([out, retval] HSTRING *result);
            HRESULT NanosecondAsPaddedString([in] INT32 min_digits, [out, retval] HSTRING *result);
            HRESULT Compare([in] Windows.Globalization.Calendar *other, [out, retval] INT32 *result);
            HRESULT CompareDateTime([in] Windows.Foundation.DateTime other, [out, retval] INT32 *result);
            HRESULT CopyTo([in] Windows.Globalization.Calendar *other);
            [propget] HRESULT FirstMinuteInThisHour([out, retval] INT32 *value);
            [propget] HRESULT LastMinuteInThisHour([out, retval] INT32 *value);
            [propget] HRESULT NumberOfMinutesInThisHour([out, retval] INT32 *value);
            [propget] HRESULT FirstSecondInThisMinute([out, retval] INT32 *value);
            [propget] HRESULT LastSecondInThisMinute([out, retval] INT32 *value);
            [propget] HRESULT NumberOfSecondsInThisMinute([out, retval] INT32 *value);
            [propget] HRESULT ResolvedLanguage([out, retval] HSTRING *value);
            [propget] HRESULT IsDaylightSavingTime([out, retval] boolean *value);
        }

        [
            contract(Windows.Foundation.UniversalApiContract, 1.0),
            exclusiveto(Windows.Globalization.Calendar),
            uuid(83f58412-e56b-4c75-a66e-0f63d57758a6)
        ]
        interface ICalendarFactory : IInspectable
        {
            HRESULT CreateCalendarDefaultCalendarAndClock(
                [in] Windows.Foundation.Collections.IIterable<HSTRING> *languages,
                [out, retval] Windows.Globalization.Calendar **result);
            HRESULT CreateCalendar(
                [in] Windows.Foundation.Collections.IIterable<HSTRING> *languages,
                [in] HSTRING calendar,
                [in] HSTRING clock,
                [out, retval] Windows.Globalization.Calendar **result);
        }

        [
            contract(Windows.Foundation.UniversalApiContract, 1.0),
            exclusiveto(Windows.Globalization.Calendar),
            uuid(b44b378c-ca7e-4590-9e72-ea2bec1a5115)
        ]
        interface ICalendarFactory2 : IInspectable
        {
            HRESULT CreateCalendarWithTimeZone(
                [in] Windows.Foundation.Collections.IIterable<HSTRING> *languages,
                [in] HSTRING calendar,
                [in] HSTRING clock,
                [in] HSTRING time_zone_id,
                [out, retval] Windows.Globalization.Calendar **result);
        }

        [
            contract(Windows.Foundation.UniversalApiContract, 1.0),
            exclusiveto(Windows.Globalization.ApplicationLanguages),
            uuid(75b40847-0a4c-4a92-9565-fd63c95f7aed)
        ]
        interface IApplicationLanguagesStatics : IInspectable
        {
            [propget] HRESULT PrimaryLanguageOverride([out, retval] HSTRING *value);
            [propput] HRESULT PrimaryLanguageOverride([in] HSTRING value);
            [propget] HRESULT Languages([out, retval] Windows.Foundation.Collections.IVectorView<HSTRING> **value);
            [propget] HRESULT ManifestLanguages([out, retval] Windows.Foundation.Collections.IVectorView<HSTRING> **value);
        }

        [
            contract(Windows.Foundation.UniversalApiContract, 6.0),
            exclusiveto(Windows.Globalization.ApplicationLanguages),
            uuid(1df0de4f-072b-4d7b-8f06-cb2db40f2bb5)
        ]
        interface IApplicationLanguagesStatics2 : IInspectable
        {
            HRESULT GetLanguagesForUser([in] Windows.System.User *user, [out, retval] Windows.Foundation.Collections.IVectorView<HSTRING> **value);
        }

        [
            contract(Windows.Foundation.UniversalApiContract, 1.0),
            exclusiveto(Windows.Globalization.Language),
            uuid(ea79a752-f7c2-4265-b1bd-c4dec4e4f080)
        ]
        interface ILanguage : IInspectable
        {
            [propget] HRESULT LanguageTag([out, retval] HSTRING *value);
            [propget] HRESULT DisplayName([out, retval] HSTRING *value);
            [propget] HRESULT NativeName([out, retval] HSTRING *value);
            [propget] HRESULT Script([out, retval] HSTRING *value);
        }

        [
            contract(Windows.Foundation.UniversalApiContract, 6.0),
            exclusiveto(Windows.Globalization.Language),
            uuid(6a47e5b5-d94d-4886-a404-a5a5b9d5b494)
        ]
        interface ILanguage2 : IInspectable
        {
            [propget] HRESULT LayoutDirection([out, retval] Windows.Globalization.LanguageLayoutDirection *value);
        }

        [
            contract(Windows.Foundation.UniversalApiContract, 10.0),
            exclusiveto(Windows.Globalization.Language),
            uuid(c6af3d10-641a-5ba4-bb43-5e12aed75954)
        ]
        interface ILanguage3 : IInspectable
        {
            [propget] HRESULT AbbreviatedName([out, retval] HSTRING *value);
        }

        [
            contract(Windows.Foundation.UniversalApiContract, 1.0),
            exclusiveto(Windows.Globalization.Language),
            uuid(7d7daf45-368d-4364-852b-dec927037b85)
        ]
        interface ILanguageExtensionSubtags : IInspectable
        {
            HRESULT GetExtensionSubtags([in] HSTRING tag, [out, retval] Windows.Foundation.Collections.IVectorView<HSTRING> **value);
        }

        [
            contract(Windows.Foundation.UniversalApiContract, 1.0),
            exclusiveto(Windows.Globalization.Language),
            uuid(9b0252ac-0c27-44f8-b792-9793fb66c63e)
        ]
        interface ILanguageFactory : IInspectable
        {
            HRESULT CreateLanguage([in] HSTRING tag, [out, retval] Windows.Globalization.Language **value);
        }

        [
            contract(Windows.Foundation.UniversalApiContract, 1.0),
            exclusiveto(Windows.Globalization.Language),
            uuid(b23cd557-0865-46d4-89b8-d59be8990f0d)
        ]
        interface ILanguageStatics : IInspectable
        {
            HRESULT IsWellFormed([in] HSTRING tag, [out, retval] BOOLEAN *result);
            [propget] HRESULT CurrentInputMethodLanguageTag([out, retval] HSTRING *value);
        }

        [
            contract(Windows.Foundation.UniversalApiContract, 1.0),
            exclusiveto(Windows.Globalization.Language),
            uuid(30199f6e-914b-4b2a-9d6e-e3b0e27dbe4f)
        ]
        interface ILanguageStatics2 : IInspectable
        {
            HRESULT TrySetInputMethodLanguageTag([in] HSTRING tag, [out, retval] BOOLEAN *result);
        }

        [
            contract(Windows.Foundation.UniversalApiContract, 10.0),
            exclusiveto(Windows.Globalization.Language),
            uuid(d15ecb5a-71de-5752-9542-fac5b4f27261)
        ]
        interface ILanguageStatics3 : IInspectable
        {
            HRESULT GetMuiCompatibleLanguageListFromLanguageTags([in] Windows.Foundation.Collections.IIterable<HSTRING> *tags, [out, retval] Windows.Foundation.Collections.IVector<HSTRING> **result);
        }

        [
            contract(Windows.Foundation.UniversalApiContract, 1.0),
            exclusiveto(Windows.Globalization.GeographicRegion),
            uuid(01e9a621-4a64-4ed9-954f-9edeb07bd903)
        ]
        interface IGeographicRegion : IInspectable
        {
            [propget] HRESULT Code([out, retval] HSTRING *value);
            [propget] HRESULT CodeTwoLetter([out, retval] HSTRING *value);
            [propget] HRESULT CodeThreeLetter([out, retval] HSTRING *value);
            [propget] HRESULT CodeThreeDigit([out, retval] HSTRING *value);
            [propget] HRESULT DisplayName([out, retval] HSTRING *value);
            [propget] HRESULT NativeName([out, retval] HSTRING *value);
            [propget] HRESULT CurrenciesInUse([out, retval] Windows.Foundation.Collections.IVectorView<HSTRING> **value);
        }

        [
            contract(Windows.Foundation.UniversalApiContract, 1.0),
            exclusiveto(Windows.Globalization.GeographicRegion),
            uuid(53425270-77b4-426b-859f-81e19d512546)
        ]
        interface IGeographicRegionFactory : IInspectable
        {
            HRESULT CreateGeographicRegion([in] HSTRING region_code, [out, retval] Windows.Globalization.GeographicRegion **result);
        }

        [
            contract(Windows.Foundation.UniversalApiContract, 1.0),
            exclusiveto(Windows.Globalization.GeographicRegion),
            uuid(29e28974-7ad9-4ef4-8799-b3b44fadec08)
        ]
        interface IGeographicRegionStatics : IInspectable
        {
            HRESULT IsSupported([in] HSTRING region_code, [out, retval] boolean *result);
        }

        [
            contract(Windows.Foundation.UniversalApiContract, 1.0),
            exclusiveto(Windows.Globalization.Calendar),
            uuid(bb3c25e5-46cf-4317-a3f5-02621ad54478)
        ]
        interface ITimeZoneOnCalendar : IInspectable
        {
            HRESULT GetTimeZone([out, retval] HSTRING *value);
            HRESULT ChangeTimeZone([in] HSTRING time_zone_id);
            [overload("TimeZoneAsString")] HRESULT TimeZoneAsFullString([out, retval] HSTRING *result);
            [overload("TimeZoneAsString")] HRESULT TimeZoneAsString([in] INT32 ideal_length, [out, retval] HSTRING *result);
        }

        [
            activatable(Windows.Foundation.UniversalApiContract, 1.0),
            activatable(Windows.Globalization.ICalendarFactory, Windows.Foundation.UniversalApiContract, 1.0),
            activatable(Windows.Globalization.ICalendarFactory2, Windows.Foundation.UniversalApiContract, 1.0),
            contract(Windows.Foundation.UniversalApiContract, 1.0),
            marshaling_behavior(agile),
            threading(both)
        ]
        runtimeclass Calendar
        {
            [contract(Windows.Foundation.UniversalApiContract, 1.0)] [default] interface Windows.Globalization.ICalendar;
            [contract(Windows.Foundation.UniversalApiContract, 1.0)] interface Windows.Globalization.ITimeZoneOnCalendar;
        }

        [
            contract(Windows.Foundation.UniversalApiContract, 1.0),
            marshaling_behavior(agile),
            static(Windows.Globalization.IApplicationLanguagesStatics, Windows.Foundation.UniversalApiContract, 1.0),
            static(Windows.Globalization.IApplicationLanguagesStatics2, Windows.Foundation.UniversalApiContract, 6.0),
            threading(both)
        ]
        runtimeclass ApplicationLanguages
        {
        }

        [
            activatable(Windows.Globalization.ILanguageFactory, Windows.Foundation.UniversalApiContract, 1.0),
            contract(Windows.Foundation.UniversalApiContract, 1.0),
            marshaling_behavior(agile),
            static(Windows.Globalization.ILanguageStatics, Windows.Foundation.UniversalApiContract, 1.0),
            static(Windows.Globalization.ILanguageStatics2, Windows.Foundation.UniversalApiContract, 1.0),
            static(Windows.Globalization.ILanguageStatics3, Windows.Foundation.UniversalApiContract, 10.0),
            threading(both)
        ]
        runtimeclass Language
        {
            [contract(Windows.Foundation.UniversalApiContract, 1.0), default] interface Windows.Globalization.ILanguage;
            [contract(Windows.Foundation.UniversalApiContract, 1.0)] interface Windows.Globalization.ILanguageExtensionSubtags;
            [contract(Windows.Foundation.UniversalApiContract, 6.0)] interface Windows.Globalization.ILanguage2;
            [contract(Windows.Foundation.UniversalApiContract, 10.0)] interface Windows.Globalization.ILanguage3;
        }

        [
            activatable(Windows.Foundation.UniversalApiContract, 1.0),
            activatable(Windows.Globalization.IGeographicRegionFactory, Windows.Foundation.UniversalApiContract, 1.0),
            contract(Windows.Foundation.UniversalApiContract, 1.0),
            marshaling_behavior(agile),
            static(Windows.Globalization.IGeographicRegionStatics, Windows.Foundation.UniversalApiContract, 1.0),
            threading(both)
        ]
        runtimeclass GeographicRegion
        {
            [contract(Windows.Foundation.UniversalApiContract, 1.0), default] interface Windows.Globalization.IGeographicRegion;
        }
    }
}
