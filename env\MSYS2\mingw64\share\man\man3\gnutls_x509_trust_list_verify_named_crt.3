.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_trust_list_verify_named_crt" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_trust_list_verify_named_crt \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_trust_list_verify_named_crt(gnutls_x509_trust_list_t " list ", gnutls_x509_crt_t " cert ", const void * " name ", size_t " name_size ", unsigned int " flags ", unsigned int * " voutput ", gnutls_verify_output_function " func ");"
.SH ARGUMENTS
.IP "gnutls_x509_trust_list_t list" 12
The list
.IP "gnutls_x509_crt_t cert" 12
is the certificate to be verified
.IP "const void * name" 12
is the certificate's name
.IP "size_t name_size" 12
is the certificate's name size
.IP "unsigned int flags" 12
Flags that may be used to change the verification algorithm. Use OR of the gnutls_certificate_verify_flags enumerations.
.IP "unsigned int * voutput" 12
will hold the certificate verification output.
.IP "gnutls_verify_output_function func" 12
If non\-null will be called on each chain element verification with the output.
.SH "DESCRIPTION"
This function will try to find a certificate that is associated with the provided
name \-\-see \fBgnutls_x509_trust_list_add_named_crt()\fP. If a match is found the
certificate is considered valid. In addition to that this function will also 
check CRLs. The  \fIvoutput\fP parameter will hold an OR'ed sequence of 
\fBgnutls_certificate_status_t\fP flags.

Additionally a certificate verification profile can be specified
from the ones in \fBgnutls_certificate_verification_profiles_t\fP by
ORing the result of \fBGNUTLS_PROFILE_TO_VFLAGS()\fP to the verification
flags.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "SINCE"
3.0.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
