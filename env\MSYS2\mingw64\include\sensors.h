/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */

#ifndef _SENSORS_H_
#define _SENSORS_H_

#include "propkeydef.h"

DEFINE_GUID(GUID_DEVINTERFACE_SENSOR, 0xba1bb692, 0x9b7a, 0x4833, 0x9a, 0x1e, 0x52, 0x5e, 0xd1, 0x34, 0xe7, 0xe2);
DEFINE_GUID(SENSOR_EVENT_STATE_CHANGED, 0xbfd96016, 0x6bd7, 0x4560, 0xad, 0x34, 0xf2, 0xf6, 0x60, 0x7e, 0x8f, 0x81);
DEFINE_GUID(SENSOR_EVENT_DATA_UPDATED, 0x2ed0f2a4, 0x0087, 0x41d3, 0x87, 0xdb, 0x67, 0x73, 0x37, 0x0b, 0x3c, 0x88);
DEFINE_GUID(SENSOR_EVENT_PROPERTY_CHANGED, 0x2358f099, 0x84c9, 0x4d3d, 0x90, 0xdf, 0xc2, 0x42, 0x1e, 0x2b, 0x20, 0x45);
DEFINE_GUID(SENSOR_EVENT_ACCELEROMETER_SHAKE, 0x825f5a94, 0x0f48, 0x4396, 0x9c, 0xa0, 0x6e, 0xcb, 0x5c, 0x99, 0xd9, 0x15);

DEFINE_GUID(SENSOR_EVENT_PARAMETER_COMMON_GUID, 0x64346e30, 0x8728, 0x4b34, 0xbd, 0xf6, 0x4f, 0x52, 0x44, 0x2c, 0x5c, 0x28);
DEFINE_PROPERTYKEY(SENSOR_EVENT_PARAMETER_EVENT_ID, 0x64346e30, 0x8728, 0x4b34, 0xbd, 0xf6, 0x4f, 0x52, 0x44, 0x2c, 0x5c, 0x28, 2);
DEFINE_PROPERTYKEY(SENSOR_EVENT_PARAMETER_STATE, 0x64346e30, 0x8728, 0x4b34, 0xbd, 0xf6, 0x4f, 0x52, 0x44, 0x2c, 0x5c, 0x28, 3);

DEFINE_GUID(SENSOR_ERROR_PARAMETER_COMMON_GUID, 0x77112bcd, 0xfce1, 0x4f43, 0xb8, 0xb8, 0xa8, 0x82, 0x56, 0xad, 0xb4, 0xb3);

DEFINE_GUID(SENSOR_CATEGORY_ALL,                              0xc317c286, 0xc468, 0x4288, 0x99, 0x75, 0xd4, 0xc4, 0x58, 0x7c, 0x44, 0x2c);
DEFINE_GUID(SENSOR_CATEGORY_BIOMETRIC,                        0xca19690f, 0xa2c7, 0x477d, 0xa9, 0x9e, 0x99, 0xec, 0x6e, 0x2b, 0x56, 0x48);
DEFINE_GUID(SENSOR_CATEGORY_ELECTRICAL,                       0xfb73fcd8, 0xfc4a, 0x483c, 0xac, 0x58, 0x27, 0xb6, 0x91, 0xc6, 0xbe, 0xff);
DEFINE_GUID(SENSOR_CATEGORY_ENVIRONMENTAL,                    0x323439aa, 0x7f66, 0x492b, 0xba, 0x0c, 0x73, 0xe9, 0xaa, 0x0a, 0x65, 0xd5);
DEFINE_GUID(SENSOR_CATEGORY_LIGHT,                            0x17a665c0, 0x9063, 0x4216, 0xb2, 0x02, 0x5c, 0x7a, 0x25, 0x5e, 0x18, 0xce);
DEFINE_GUID(SENSOR_CATEGORY_LOCATION,                         0xbfa794e4, 0xf964, 0x4fdb, 0x90, 0xf6, 0x51, 0x05, 0x6b, 0xfe, 0x4b, 0x44);
DEFINE_GUID(SENSOR_CATEGORY_MECHANICAL,                       0x8d131d68, 0x8ef7, 0x4656, 0x80, 0xb5, 0xcc, 0xcb, 0xd9, 0x37, 0x91, 0xc5);
DEFINE_GUID(SENSOR_CATEGORY_MOTION,                           0xcd09daf1, 0x3b2e, 0x4c3d, 0xb5, 0x98, 0xb5, 0xe5, 0xff, 0x93, 0xfd, 0x46);
DEFINE_GUID(SENSOR_CATEGORY_ORIENTATION,                      0x9e6c04b6, 0x96fe, 0x4954, 0xb7, 0x26, 0x68, 0x68, 0x2a, 0x47, 0x3f, 0x69);
DEFINE_GUID(SENSOR_CATEGORY_SCANNER,                          0xb000e77e, 0xf5b5, 0x420f, 0x81, 0x5d, 0x02, 0x70, 0xa7, 0x26, 0xf2, 0x70);
DEFINE_GUID(SENSOR_CATEGORY_OTHER,                            0x2c90e7a9, 0xf4c9, 0x4fa2, 0xaf, 0x37, 0x56, 0xd4, 0x71, 0xfe, 0x5a, 0x3d);
DEFINE_GUID(SENSOR_CATEGORY_UNSUPPORTED,                      0x2beae7fa, 0x19b0, 0x48c5, 0xa1, 0xf6, 0xb5, 0x48, 0x0d, 0xc2, 0x06, 0xb0);

DEFINE_GUID(SENSOR_PROPERTY_COMMON_GUID,                      0x7f8383ec, 0xd3ec, 0x495c, 0xa8, 0xcf, 0xb8, 0xbb, 0xe8, 0x5c, 0x29, 0x20);
DEFINE_PROPERTYKEY(SENSOR_PROPERTY_TYPE,                      0x7f8383ec, 0xd3ec, 0x495c, 0xa8, 0xcf, 0xb8, 0xbb, 0xe8, 0x5c, 0x29, 0x20, 2);
DEFINE_PROPERTYKEY(SENSOR_PROPERTY_STATE,                     0x7f8383ec, 0xd3ec, 0x495c, 0xa8, 0xcf, 0xb8, 0xbb, 0xe8, 0x5c, 0x29, 0x20, 3);
DEFINE_PROPERTYKEY(SENSOR_PROPERTY_PERSISTENT_UNIQUE_ID,      0x7f8383ec, 0xd3ec, 0x495c, 0xa8, 0xcf, 0xb8, 0xbb, 0xe8, 0x5c, 0x29, 0x20, 5);
DEFINE_PROPERTYKEY(SENSOR_PROPERTY_MANUFACTURER,              0x7f8383ec, 0xd3ec, 0x495c, 0xa8, 0xcf, 0xb8, 0xbb, 0xe8, 0x5c, 0x29, 0x20, 6);
DEFINE_PROPERTYKEY(SENSOR_PROPERTY_MODEL,                     0x7f8383ec, 0xd3ec, 0x495c, 0xa8, 0xcf, 0xb8, 0xbb, 0xe8, 0x5c, 0x29, 0x20, 7);
DEFINE_PROPERTYKEY(SENSOR_PROPERTY_SERIAL_NUMBER,             0x7f8383ec, 0xd3ec, 0x495c, 0xa8, 0xcf, 0xb8, 0xbb, 0xe8, 0x5c, 0x29, 0x20, 8);
DEFINE_PROPERTYKEY(SENSOR_PROPERTY_FRIENDLY_NAME,             0x7f8383ec, 0xd3ec, 0x495c, 0xa8, 0xcf, 0xb8, 0xbb, 0xe8, 0x5c, 0x29, 0x20, 9);
DEFINE_PROPERTYKEY(SENSOR_PROPERTY_DESCRIPTION,               0x7f8383ec, 0xd3ec, 0x495c, 0xa8, 0xcf, 0xb8, 0xbb, 0xe8, 0x5c, 0x29, 0x20, 10);
DEFINE_PROPERTYKEY(SENSOR_PROPERTY_CONNECTION_TYPE,           0x7f8383ec, 0xd3ec, 0x495c, 0xa8, 0xcf, 0xb8, 0xbb, 0xe8, 0x5c, 0x29, 0x20, 11);
DEFINE_PROPERTYKEY(SENSOR_PROPERTY_MIN_REPORT_INTERVAL,       0x7f8383ec, 0xd3ec, 0x495c, 0xa8, 0xcf, 0xb8, 0xbb, 0xe8, 0x5c, 0x29, 0x20, 12);
DEFINE_PROPERTYKEY(SENSOR_PROPERTY_CURRENT_REPORT_INTERVAL,   0x7f8383ec, 0xd3ec, 0x495c, 0xa8, 0xcf, 0xb8, 0xbb, 0xe8, 0x5c, 0x29, 0x20, 13);
DEFINE_PROPERTYKEY(SENSOR_PROPERTY_CHANGE_SENSITIVITY,        0x7f8383ec, 0xd3ec, 0x495c, 0xa8, 0xcf, 0xb8, 0xbb, 0xe8, 0x5c, 0x29, 0x20, 14);
DEFINE_PROPERTYKEY(SENSOR_PROPERTY_DEVICE_PATH,               0x7f8383ec, 0xd3ec, 0x495c, 0xa8, 0xcf, 0xb8, 0xbb, 0xe8, 0x5c, 0x29, 0x20, 15);
DEFINE_PROPERTYKEY(SENSOR_PROPERTY_LIGHT_RESPONSE_CURVE,      0x7f8383ec, 0xd3ec, 0x495c, 0xa8, 0xcf, 0xb8, 0xbb, 0xe8, 0x5c, 0x29, 0x20, 16);
DEFINE_PROPERTYKEY(SENSOR_PROPERTY_ACCURACY,                  0x7f8383ec, 0xd3ec, 0x495c, 0xa8, 0xcf, 0xb8, 0xbb, 0xe8, 0x5c, 0x29, 0x20, 17);
DEFINE_PROPERTYKEY(SENSOR_PROPERTY_RESOLUTION,                0x7f8383ec, 0xd3ec, 0x495c, 0xa8, 0xcf, 0xb8, 0xbb, 0xe8, 0x5c, 0x29, 0x20, 18);
DEFINE_PROPERTYKEY(SENSOR_PROPERTY_LOCATION_DESIRED_ACCURACY, 0x7f8383ec, 0xd3ec, 0x495c, 0xa8, 0xcf, 0xb8, 0xbb, 0xe8, 0x5c, 0x29, 0x20, 19);
DEFINE_PROPERTYKEY(SENSOR_PROPERTY_RANGE_MINIMUM,             0x7f8383ec, 0xd3ec, 0x495c, 0xa8, 0xcf, 0xb8, 0xbb, 0xe8, 0x5c, 0x29, 0x20, 20);
DEFINE_PROPERTYKEY(SENSOR_PROPERTY_RANGE_MAXIMUM,             0x7f8383ec, 0xd3ec, 0x495c, 0xa8, 0xcf, 0xb8, 0xbb, 0xe8, 0x5c, 0x29, 0x20, 21);
DEFINE_PROPERTYKEY(SENSOR_PROPERTY_HID_USAGE,                 0x7f8383ec, 0xd3ec, 0x495c, 0xa8, 0xcf, 0xb8, 0xbb, 0xe8, 0x5c, 0x29, 0x20, 22);
DEFINE_PROPERTYKEY(SENSOR_PROPERTY_RADIO_STATE,               0x7f8383ec, 0xd3ec, 0x495c, 0xa8, 0xcf, 0xb8, 0xbb, 0xe8, 0x5c, 0x29, 0x20, 23);
DEFINE_PROPERTYKEY(SENSOR_PROPERTY_RADIO_STATE_PREVIOUS,      0x7f8383ec, 0xd3ec, 0x495c, 0xa8, 0xcf, 0xb8, 0xbb, 0xe8, 0x5c, 0x29, 0x20, 24);

DEFINE_GUID(SENSOR_DATA_TYPE_COMMON_GUID,                     0xdb5e0cf2, 0xcf1f, 0x4c18, 0xb4, 0x6c, 0xd8, 0x60, 0x11, 0xd6, 0x21, 0x50);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_TIMESTAMP,                0xdb5e0cf2, 0xcf1f, 0x4c18, 0xb4, 0x6c, 0xd8, 0x60, 0x11, 0xd6, 0x21, 0x50, 2);

DEFINE_GUID(SENSOR_TYPE_HUMAN_PRESENCE,                       0xc138c12b, 0xad52, 0x451c, 0x93, 0x75, 0x87, 0xf5, 0x18, 0xff, 0x10, 0xc6);
DEFINE_GUID(SENSOR_TYPE_HUMAN_PROXIMITY,                      0x5220dae9, 0x3179, 0x4430, 0x9f, 0x90, 0x06, 0x26, 0x6d, 0x2a, 0x34, 0xde);
DEFINE_GUID(SENSOR_TYPE_TOUCH,                                0x17db3018, 0x06c4, 0x4f7d, 0x81, 0xaf, 0x92, 0x74, 0xb7, 0x59, 0x9c, 0x27);

DEFINE_GUID(SENSOR_DATA_TYPE_BIOMETRIC_GUID,                  0x2299288a, 0x6d9e, 0x4b0b, 0xb7, 0xec, 0x35, 0x28, 0xf8, 0x9e, 0x40, 0xaf);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_HUMAN_PRESENCE,           0x2299288a, 0x6d9e, 0x4b0b, 0xb7, 0xec, 0x35, 0x28, 0xf8, 0x9e, 0x40, 0xaf, 2);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_HUMAN_PROXIMITY_METERS,   0x2299288a, 0x6d9e, 0x4b0b, 0xb7, 0xec, 0x35, 0x28, 0xf8, 0x9e, 0x40, 0xaf, 3);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_TOUCH_STATE,              0x2299288a, 0x6d9e, 0x4b0b, 0xb7, 0xec, 0x35, 0x28, 0xf8, 0x9e, 0x40, 0xaf, 4);

DEFINE_GUID(SENSOR_TYPE_CAPACITANCE,                          0xca2ffb1c, 0x2317, 0x49c0, 0xa0, 0xb4, 0xb6, 0x3c, 0xe6, 0x34, 0x61, 0xa0);
DEFINE_GUID(SENSOR_TYPE_CURRENT,                              0x5adc9fce, 0x15a0, 0x4bbe, 0xa1, 0xad, 0x2d, 0x38, 0xa9, 0xae, 0x83, 0x1c);
DEFINE_GUID(SENSOR_TYPE_ELECTRICAL_POWER,                     0x212f10f5, 0x14ab, 0x4376, 0x9a, 0x43, 0xa7, 0x79, 0x40, 0x98, 0xc2, 0xfe);
DEFINE_GUID(SENSOR_TYPE_FREQUENCY,                            0x8cd2cbb6, 0x73e6, 0x4640, 0xa7, 0x09, 0x72, 0xae, 0x8f, 0xb6, 0x0d, 0x7f);
DEFINE_GUID(SENSOR_TYPE_INDUCTANCE,                           0xdc1d933f, 0xc435, 0x4c7d, 0xa2, 0xfe, 0x60, 0x71, 0x92, 0xa5, 0x24, 0xd3);
DEFINE_GUID(SENSOR_TYPE_POTENTIOMETER,                        0x2b3681a9, 0xcadc, 0x45aa, 0xa6, 0xff, 0x54, 0x95, 0x7c, 0x8b, 0xb4, 0x40);
DEFINE_GUID(SENSOR_TYPE_RESISTANCE,                           0x9993d2c8, 0xc157, 0x4a52, 0xa7, 0xb5, 0x19, 0x5c, 0x76, 0x03, 0x72, 0x31);
DEFINE_GUID(SENSOR_TYPE_VOLTAGE,                              0xc5484637, 0x4fb7, 0x4953, 0x98, 0xb8, 0xa5, 0x6d, 0x8a, 0xa1, 0xfb, 0x1e);

DEFINE_GUID(SENSOR_DATA_TYPE_ELECTRICAL_GUID,                    0xbbb246d1, 0xe242, 0x4780, 0xa2, 0xd3, 0xcd, 0xed, 0x84, 0xf3, 0x58, 0x42);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_VOLTAGE_VOLTS,               0xbbb246d1, 0xe242, 0x4780, 0xa2, 0xd3, 0xcd, 0xed, 0x84, 0xf3, 0x58, 0x42, 2);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_CURRENT_AMPS,                0xbbb246d1, 0xe242, 0x4780, 0xa2, 0xd3, 0xcd, 0xed, 0x84, 0xf3, 0x58, 0x42, 3);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_CAPACITANCE_FARAD,           0xbbb246d1, 0xe242, 0x4780, 0xa2, 0xd3, 0xcd, 0xed, 0x84, 0xf3, 0x58, 0x42, 4);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_RESISTANCE_OHMS,             0xbbb246d1, 0xe242, 0x4780, 0xa2, 0xd3, 0xcd, 0xed, 0x84, 0xf3, 0x58, 0x42, 5);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_INDUCTANCE_HENRY,            0xbbb246d1, 0xe242, 0x4780, 0xa2, 0xd3, 0xcd, 0xed, 0x84, 0xf3, 0x58, 0x42, 6);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_ELECTRICAL_POWER_WATTS,      0xbbb246d1, 0xe242, 0x4780, 0xa2, 0xd3, 0xcd, 0xed, 0x84, 0xf3, 0x58, 0x42, 7);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_ELECTRICAL_PERCENT_OF_RANGE, 0xbbb246d1, 0xe242, 0x4780, 0xa2, 0xd3, 0xcd, 0xed, 0x84, 0xf3, 0x58, 0x42, 8);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_ELECTRICAL_FREQUENCY_HERTZ,  0xbbb246d1, 0xe242, 0x4780, 0xa2, 0xd3, 0xcd, 0xed, 0x84, 0xf3, 0x58, 0x42, 9);

DEFINE_GUID(SENSOR_TYPE_ENVIRONMENTAL_ATMOSPHERIC_PRESSURE,   0x0e903829, 0xff8a, 0x4a93, 0x97, 0xdf, 0x3d, 0xcb, 0xde, 0x40, 0x22, 0x88);
DEFINE_GUID(SENSOR_TYPE_ENVIRONMENTAL_HUMIDITY,               0x5c72bf67, 0xbd7e, 0x4257, 0x99, 0x0b, 0x98, 0xa3, 0xba, 0x3b, 0x40, 0x0a);
DEFINE_GUID(SENSOR_TYPE_ENVIRONMENTAL_TEMPERATURE,            0x04fd0ec4, 0xd5da, 0x45fa, 0x95, 0xa9, 0x5d, 0xb3, 0x8e, 0xe1, 0x93, 0x06);
DEFINE_GUID(SENSOR_TYPE_ENVIRONMENTAL_WIND_DIRECTION,         0x9ef57a35, 0x9306, 0x434d, 0xaf, 0x09, 0x37, 0xfa, 0x5a, 0x9c, 0x00, 0xbd);
DEFINE_GUID(SENSOR_TYPE_ENVIRONMENTAL_WIND_SPEED,             0xdd50607b, 0xa45f, 0x42cd, 0x8e, 0xfd, 0xec, 0x61, 0x76, 0x1c, 0x42, 0x26);

DEFINE_GUID(SENSOR_DATA_TYPE_ENVIRONMENTAL_GUID,                          0x8b0aa2f1, 0x2d57, 0x42ee, 0x8c, 0xc0, 0x4d, 0x27, 0x62, 0x2b, 0x46, 0xc4);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_TEMPERATURE_CELSIUS,                  0x8b0aa2f1, 0x2d57, 0x42ee, 0x8c, 0xc0, 0x4d, 0x27, 0x62, 0x2b, 0x46, 0xc4, 2);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_RELATIVE_HUMIDITY_PERCENT,            0x8b0aa2f1, 0x2d57, 0x42ee, 0x8c, 0xc0, 0x4d, 0x27, 0x62, 0x2b, 0x46, 0xc4, 3);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_ATMOSPHERIC_PRESSURE_BAR,             0x8b0aa2f1, 0x2d57, 0x42ee, 0x8c, 0xc0, 0x4d, 0x27, 0x62, 0x2b, 0x46, 0xc4, 4);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_WIND_DIRECTION_DEGREES_ANTICLOCKWISE, 0x8b0aa2f1, 0x2d57, 0x42ee, 0x8c, 0xc0, 0x4d, 0x27, 0x62, 0x2b, 0x46, 0xc4, 5);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_WIND_SPEED_METERS_PER_SECOND,         0x8b0aa2f1, 0x2d57, 0x42ee, 0x8c, 0xc0, 0x4d, 0x27, 0x62, 0x2b, 0x46, 0xc4, 6);

DEFINE_GUID(SENSOR_TYPE_AMBIENT_LIGHT,                        0x97f115c8, 0x599a, 0x4153, 0x88, 0x94, 0xd2, 0xd1, 0x28, 0x99, 0x91, 0x8a);

DEFINE_GUID(SENSOR_DATA_TYPE_LIGHT_GUID,                      0xe4c77ce2, 0xdcb7, 0x46e9, 0x84, 0x39, 0x4f, 0xec, 0x54, 0x88, 0x33, 0xa6);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_LIGHT_LEVEL_LUX,          0xe4c77ce2, 0xdcb7, 0x46e9, 0x84, 0x39, 0x4f, 0xec, 0x54, 0x88, 0x33, 0xa6, 2);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_LIGHT_TEMPERATURE_KELVIN, 0xe4c77ce2, 0xdcb7, 0x46e9, 0x84, 0x39, 0x4f, 0xec, 0x54, 0x88, 0x33, 0xa6, 3);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_LIGHT_CHROMACITY,         0xe4c77ce2, 0xdcb7, 0x46e9, 0x84, 0x39, 0x4f, 0xec, 0x54, 0x88, 0x33, 0xa6, 4);


DEFINE_GUID(SENSOR_TYPE_LOCATION_BROADCAST,	              0xd26988cf, 0x5162, 0x4039, 0xbb, 0x17, 0x4c, 0x58, 0xb6, 0x98, 0xe4, 0x4a);
DEFINE_GUID(SENSOR_TYPE_LOCATION_DEAD_RECKONING,              0x1a37d538, 0xf28b, 0x42da, 0x9f, 0xce, 0xa9, 0xd0, 0xa2, 0xa6, 0xd8, 0x29);
DEFINE_GUID(SENSOR_TYPE_LOCATION_GPS,                         0xed4ca589, 0x327a, 0x4ff9, 0xa5, 0x60, 0x91, 0xda, 0x4b, 0x48, 0x27, 0x5e);
DEFINE_GUID(SENSOR_TYPE_LOCATION_LOOKUP,                      0x3b2eae4a, 0x72ce, 0x436d, 0x96, 0xd2, 0x3c, 0x5b, 0x85, 0x70, 0xe9, 0x87);
DEFINE_GUID(SENSOR_TYPE_LOCATION_OTHER,                       0x9b2d0566, 0x0368, 0x4f71, 0xb8, 0x8d, 0x53, 0x3f, 0x13, 0x20, 0x31, 0xde);
DEFINE_GUID(SENSOR_TYPE_LOCATION_STATIC,                      0x095f8184, 0x0fa9, 0x4445, 0x8e, 0x6e, 0xb7, 0x0f, 0x32, 0x0b, 0x6b, 0x4c);
DEFINE_GUID(SENSOR_TYPE_LOCATION_TRIANGULATION,               0x691c341a, 0x5406, 0x4fe1, 0x94, 0x2f, 0x22, 0x46, 0xcb, 0xeb, 0x39, 0xe0);

DEFINE_GUID(SENSOR_DATA_TYPE_LOCATION_GUID,                            0x055c74d8, 0xca6f, 0x47d6, 0x95, 0xc6, 0x1e, 0xd3, 0x63, 0x7a, 0x0f, 0xf4);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_LATITUDE_DEGREES,                  0x055c74d8, 0xca6f, 0x47d6, 0x95, 0xc6, 0x1e, 0xd3, 0x63, 0x7a, 0x0f, 0xf4, 2);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_LONGITUDE_DEGREES,                 0x055c74d8, 0xca6f, 0x47d6, 0x95, 0xc6, 0x1e, 0xd3, 0x63, 0x7a, 0x0f, 0xf4, 3);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_ALTITUDE_SEALEVEL_METERS,          0x055c74d8, 0xca6f, 0x47d6, 0x95, 0xc6, 0x1e, 0xd3, 0x63, 0x7a, 0x0f, 0xf4, 4);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_ALTITUDE_ELLIPSOID_METERS,         0x055c74d8, 0xca6f, 0x47d6, 0x95, 0xc6, 0x1e, 0xd3, 0x63, 0x7a, 0x0f, 0xf4, 5);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_SPEED_KNOTS,                       0x055c74d8, 0xca6f, 0x47d6, 0x95, 0xc6, 0x1e, 0xd3, 0x63, 0x7a, 0x0f, 0xf4, 6);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_TRUE_HEADING_DEGREES,              0x055c74d8, 0xca6f, 0x47d6, 0x95, 0xc6, 0x1e, 0xd3, 0x63, 0x7a, 0x0f, 0xf4, 7);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_MAGNETIC_HEADING_DEGREES,          0x055c74d8, 0xca6f, 0x47d6, 0x95, 0xc6, 0x1e, 0xd3, 0x63, 0x7a, 0x0f, 0xf4, 8);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_MAGNETIC_VARIATION,                0x055c74d8, 0xca6f, 0x47d6, 0x95, 0xc6, 0x1e, 0xd3, 0x63, 0x7a, 0x0f, 0xf4, 9);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_FIX_QUALITY,                       0x055c74d8, 0xca6f, 0x47d6, 0x95, 0xc6, 0x1e, 0xd3, 0x63, 0x7a, 0x0f, 0xf4, 10);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_FIX_TYPE,                          0x055c74d8, 0xca6f, 0x47d6, 0x95, 0xc6, 0x1e, 0xd3, 0x63, 0x7a, 0x0f, 0xf4, 11);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_POSITION_DILUTION_OF_PRECISION,    0x055c74d8, 0xca6f, 0x47d6, 0x95, 0xc6, 0x1e, 0xd3, 0x63, 0x7a, 0x0f, 0xf4, 12);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_HORIZONAL_DILUTION_OF_PRECISION,   0x055c74d8, 0xca6f, 0x47d6, 0x95, 0xc6, 0x1e, 0xd3, 0x63, 0x7a, 0x0f, 0xf4, 13);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_VERTICAL_DILUTION_OF_PRECISION,    0x055c74d8, 0xca6f, 0x47d6, 0x95, 0xc6, 0x1e, 0xd3, 0x63, 0x7a, 0x0f, 0xf4, 14);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_SATELLITES_USED_COUNT,             0x055c74d8, 0xca6f, 0x47d6, 0x95, 0xc6, 0x1e, 0xd3, 0x63, 0x7a, 0x0f, 0xf4, 15);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_SATELLITES_USED_PRNS,              0x055c74d8, 0xca6f, 0x47d6, 0x95, 0xc6, 0x1e, 0xd3, 0x63, 0x7a, 0x0f, 0xf4, 16);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_SATELLITES_IN_VIEW,                0x055c74d8, 0xca6f, 0x47d6, 0x95, 0xc6, 0x1e, 0xd3, 0x63, 0x7a, 0x0f, 0xf4, 17);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_SATELLITES_IN_VIEW_PRNS,           0x055c74d8, 0xca6f, 0x47d6, 0x95, 0xc6, 0x1e, 0xd3, 0x63, 0x7a, 0x0f, 0xf4, 18);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_SATELLITES_IN_VIEW_ELEVATION,      0x055c74d8, 0xca6f, 0x47d6, 0x95, 0xc6, 0x1e, 0xd3, 0x63, 0x7a, 0x0f, 0xf4, 19);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_SATELLITES_IN_VIEW_AZIMUTH,        0x055c74d8, 0xca6f, 0x47d6, 0x95, 0xc6, 0x1e, 0xd3, 0x63, 0x7a, 0x0f, 0xf4, 20);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_SATELLITES_IN_VIEW_STN_RATIO,      0x055c74d8, 0xca6f, 0x47d6, 0x95, 0xc6, 0x1e, 0xd3, 0x63, 0x7a, 0x0f, 0xf4, 21);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_ERROR_RADIUS_METERS,               0x055c74d8, 0xca6f, 0x47d6, 0x95, 0xc6, 0x1e, 0xd3, 0x63, 0x7a, 0x0f, 0xf4, 22);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_ADDRESS1,                          0x055c74d8, 0xca6f, 0x47d6, 0x95, 0xc6, 0x1e, 0xd3, 0x63, 0x7a, 0x0f, 0xf4, 23);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_ADDRESS2,                          0x055c74d8, 0xca6f, 0x47d6, 0x95, 0xc6, 0x1e, 0xd3, 0x63, 0x7a, 0x0f, 0xf4, 24);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_CITY,                              0x055c74d8, 0xca6f, 0x47d6, 0x95, 0xc6, 0x1e, 0xd3, 0x63, 0x7a, 0x0f, 0xf4, 25);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_STATE_PROVINCE,                    0x055c74d8, 0xca6f, 0x47d6, 0x95, 0xc6, 0x1e, 0xd3, 0x63, 0x7a, 0x0f, 0xf4, 26);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_POSTALCODE,                        0x055c74d8, 0xca6f, 0x47d6, 0x95, 0xc6, 0x1e, 0xd3, 0x63, 0x7a, 0x0f, 0xf4, 27);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_COUNTRY_REGION,                    0x055c74d8, 0xca6f, 0x47d6, 0x95, 0xc6, 0x1e, 0xd3, 0x63, 0x7a, 0x0f, 0xf4, 28);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_ALTITUDE_ELLIPSOID_ERROR_METERS,   0x055c74d8, 0xca6f, 0x47d6, 0x95, 0xc6, 0x1e, 0xd3, 0x63, 0x7a, 0x0f, 0xf4, 29);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_ALTITUDE_SEALEVEL_ERROR_METERS,    0x055c74d8, 0xca6f, 0x47d6, 0x95, 0xc6, 0x1e, 0xd3, 0x63, 0x7a, 0x0f, 0xf4, 30);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_GPS_SELECTION_MODE,                0x055c74d8, 0xca6f, 0x47d6, 0x95, 0xc6, 0x1e, 0xd3, 0x63, 0x7a, 0x0f, 0xf4, 31);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_GPS_OPERATION_MODE,                0x055c74d8, 0xca6f, 0x47d6, 0x95, 0xc6, 0x1e, 0xd3, 0x63, 0x7a, 0x0f, 0xf4, 32);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_GPS_STATUS,                        0x055c74d8, 0xca6f, 0x47d6, 0x95, 0xc6, 0x1e, 0xd3, 0x63, 0x7a, 0x0f, 0xf4, 33);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_GEOIDAL_SEPARATION,                0x055c74d8, 0xca6f, 0x47d6, 0x95, 0xc6, 0x1e, 0xd3, 0x63, 0x7a, 0x0f, 0xf4, 34);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_DGPS_DATA_AGE,                     0x055c74d8, 0xca6f, 0x47d6, 0x95, 0xc6, 0x1e, 0xd3, 0x63, 0x7a, 0x0f, 0xf4, 35);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_ALTITUDE_ANTENNA_SEALEVEL_METERS,  0x055c74d8, 0xca6f, 0x47d6, 0x95, 0xc6, 0x1e, 0xd3, 0x63, 0x7a, 0x0f, 0xf4, 36);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_DIFFERENTIAL_REFERENCE_STATION_ID, 0x055c74d8, 0xca6f, 0x47d6, 0x95, 0xc6, 0x1e, 0xd3, 0x63, 0x7a, 0x0f, 0xf4, 37);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_NMEA_SENTENCE,                     0x055c74d8, 0xca6f, 0x47d6, 0x95, 0xc6, 0x1e, 0xd3, 0x63, 0x7a, 0x0f, 0xf4, 38);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_SATELLITES_IN_VIEW_ID,             0x055c74d8, 0xca6f, 0x47d6, 0x95, 0xc6, 0x1e, 0xd3, 0x63, 0x7a, 0x0f, 0xf4, 39);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_LOCATION_SOURCE,                   0x055c74d8, 0xca6f, 0x47d6, 0x95, 0xc6, 0x1e, 0xd3, 0x63, 0x7a, 0x0f, 0xf4, 40);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_SATELLITES_USED_PRNS_AND_CONSTELLATIONS, 0x055c74d8, 0xca6f, 0x47d6, 0x95, 0xc6, 0x1e, 0xd3, 0x63, 0x7a, 0x0f, 0xf4, 41);

DEFINE_GUID(SENSOR_TYPE_BOOLEAN_SWITCH,                       0x9c7e371f, 0x1041, 0x460b, 0x8d, 0x5c, 0x71, 0xe4, 0x75, 0x2e, 0x35, 0x0c);
DEFINE_GUID(SENSOR_TYPE_BOOLEAN_SWITCH_ARRAY,                 0x545c8ba5, 0xb143, 0x4545, 0x86, 0x8f, 0xca, 0x7f, 0xd9, 0x86, 0xb4, 0xf6);
DEFINE_GUID(SENSOR_TYPE_FORCE,                                0xc2ab2b02, 0x1a1c, 0x4778, 0xa8, 0x1b, 0x95, 0x4a, 0x17, 0x88, 0xcc, 0x75);
DEFINE_GUID(SENSOR_TYPE_MULTIVALUE_SWITCH,                    0xb3ee4d76, 0x37a4, 0x4402, 0xb2, 0x5e, 0x99, 0xc6, 0x0a, 0x77, 0x5f, 0xa1);
DEFINE_GUID(SENSOR_TYPE_PRESSURE,                             0x26d31f34, 0x6352, 0x41cf, 0xb7, 0x93, 0xea, 0x07, 0x13, 0xd5, 0x3d, 0x77);
DEFINE_GUID(SENSOR_TYPE_SCALE,                                0xc06dd92c, 0x7feb, 0x438e, 0x9b, 0xf6, 0x82, 0x20, 0x7f, 0xff, 0x5b, 0xb8);
DEFINE_GUID(SENSOR_TYPE_STRAIN,                               0xc6d1ec0e, 0x6803, 0x4361, 0xad, 0x3d, 0x85, 0xbc, 0xc5, 0x8c, 0x6d, 0x29);

DEFINE_GUID(SENSOR_DATA_TYPE_GUID_MECHANICAL_GUID,               0x38564a7c, 0xf2f2, 0x49bb, 0x9b, 0x2b, 0xba, 0x60, 0xf6, 0x6a, 0x58, 0xdf);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_BOOLEAN_SWITCH_STATE,        0x38564a7c, 0xf2f2, 0x49bb, 0x9b, 0x2b, 0xba, 0x60, 0xf6, 0x6a, 0x58, 0xdf, 2);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_MULTIVALUE_SWITCH_STATE,     0x38564a7c, 0xf2f2, 0x49bb, 0x9b, 0x2b, 0xba, 0x60, 0xf6, 0x6a, 0x58, 0xdf, 3);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_FORCE_NEWTONS,               0x38564a7c, 0xf2f2, 0x49bb, 0x9b, 0x2b, 0xba, 0x60, 0xf6, 0x6a, 0x58, 0xdf, 4);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_ABSOLUTE_PRESSURE_PASCAL,    0x38564a7c, 0xf2f2, 0x49bb, 0x9b, 0x2b, 0xba, 0x60, 0xf6, 0x6a, 0x58, 0xdf, 5);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_GAUGE_PRESSURE_PASCAL,       0x38564a7c, 0xf2f2, 0x49bb, 0x9b, 0x2b, 0xba, 0x60, 0xf6, 0x6a, 0x58, 0xdf, 6);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_STRAIN,                      0x38564a7c, 0xf2f2, 0x49bb, 0x9b, 0x2b, 0xba, 0x60, 0xf6, 0x6a, 0x58, 0xdf, 7);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_WEIGHT_KILOGRAMS,            0x38564a7c, 0xf2f2, 0x49bb, 0x9b, 0x2b, 0xba, 0x60, 0xf6, 0x6a, 0x58, 0xdf, 8);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_BOOLEAN_SWITCH_ARRAY_STATES, 0x38564a7c, 0xf2f2, 0x49bb, 0x9b, 0x2b, 0xba, 0x60, 0xf6, 0x6a, 0x58, 0xdf, 10);

DEFINE_GUID(SENSOR_TYPE_ACCELEROMETER_1D, 0xc04d2387, 0x7340, 0x4cc2, 0x99, 0x1e, 0x3b, 0x18, 0xcb, 0x8e, 0xf2, 0xf4);
DEFINE_GUID(SENSOR_TYPE_ACCELEROMETER_2D, 0xb2c517a8, 0xf6b5, 0x4ba6, 0xa4, 0x23, 0x5D, 0xf5, 0x60, 0xb4, 0xcc, 0x07);
DEFINE_GUID(SENSOR_TYPE_ACCELEROMETER_3D, 0xc2fb0f5f, 0xe2d2, 0x4c78, 0xbc, 0xd0, 0x35, 0x2a, 0x95, 0x82, 0x81, 0x9d);
DEFINE_GUID(SENSOR_TYPE_MOTION_DETECTOR,  0x5c7c1a12, 0x30a5, 0x43b9, 0xa4, 0xb2, 0xcf, 0x09, 0xec, 0x5b, 0x7b, 0xe8);
DEFINE_GUID(SENSOR_TYPE_GYROMETER_1D,     0xfa088734, 0xf552, 0x4584, 0x83, 0x24, 0xed, 0xfa, 0xf6, 0x49, 0x65, 0x2c);
DEFINE_GUID(SENSOR_TYPE_GYROMETER_2D,     0x31ef4f83, 0x919b, 0x48bF, 0x8d, 0xe0, 0x5d, 0x7a, 0x9d, 0x24, 0x05, 0x56);
DEFINE_GUID(SENSOR_TYPE_GYROMETER_3D,     0x09485F5a, 0x759e, 0x42c2, 0xbd, 0x4b, 0xa3, 0x49, 0xb7, 0x5c, 0x86, 0x43);
DEFINE_GUID(SENSOR_TYPE_SPEEDOMETER,      0x6bd73c1f, 0x0bb4, 0x4310, 0x81, 0xb2, 0xdf, 0xc1, 0x8a, 0x52, 0xbf, 0x94);

DEFINE_GUID(SENSOR_DATA_TYPE_MOTION_GUID,                                              0x3f8a69a2, 0x7c5, 0x4e48, 0xa9, 0x65, 0xcd, 0x79, 0x7a, 0xab, 0x56, 0xd5);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_ACCELERATION_X_G,                                  0x3f8a69a2, 0x7c5, 0x4e48, 0xa9, 0x65, 0xcd, 0x79, 0x7a, 0xab, 0x56, 0xd5, 2);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_ACCELERATION_Y_G,                                  0x3f8a69a2, 0x7c5, 0x4e48, 0xa9, 0x65, 0xcd, 0x79, 0x7a, 0xab, 0x56, 0xd5, 3);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_ACCELERATION_Z_G,                                  0x3f8a69a2, 0x7c5, 0x4e48, 0xa9, 0x65, 0xcd, 0x79, 0x7a, 0xab, 0x56, 0xd5, 4);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_ANGULAR_ACCELERATION_X_DEGREES_PER_SECOND_SQUARED, 0x3f8a69a2, 0x7c5, 0x4e48, 0xa9, 0x65, 0xcd, 0x79, 0x7a, 0xab, 0x56, 0xd5, 5);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_ANGULAR_ACCELERATION_Y_DEGREES_PER_SECOND_SQUARED, 0x3f8a69a2, 0x7c5, 0x4e48, 0xa9, 0x65, 0xcd, 0x79, 0x7a, 0xab, 0x56, 0xd5, 6);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_ANGULAR_ACCELERATION_Z_DEGREES_PER_SECOND_SQUARED, 0x3f8a69a2, 0x7c5, 0x4e48, 0xa9, 0x65, 0xcd, 0x79, 0x7a, 0xab, 0x56, 0xd5, 7);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_SPEED_METERS_PER_SECOND,                           0x3f8a69a2, 0x7c5, 0x4e48, 0xa9, 0x65, 0xcd, 0x79, 0x7a, 0xab, 0x56, 0xd5, 8);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_MOTION_STATE,                                      0x3f8a69a2, 0x7c5, 0x4e48, 0xa9, 0x65, 0xcd, 0x79, 0x7a, 0xab, 0x56, 0xd5, 9);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_ANGULAR_VELOCITY_X_DEGREES_PER_SECOND,             0x3f8a69a2, 0x7c5, 0x4e48, 0xa9, 0x65, 0xcd, 0x79, 0x7a, 0xab, 0x56, 0xd5, 10);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_ANGULAR_VELOCITY_Y_DEGREES_PER_SECOND,             0x3f8a69a2, 0x7c5, 0x4e48, 0xa9, 0x65, 0xcd, 0x79, 0x7a, 0xab, 0x56, 0xd5, 11);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_ANGULAR_VELOCITY_Z_DEGREES_PER_SECOND,             0x3f8a69a2, 0x7c5, 0x4e48, 0xa9, 0x65, 0xcd, 0x79, 0x7a, 0xab, 0x56, 0xd5, 12);

DEFINE_GUID(SENSOR_TYPE_AGGREGATED_DEVICE_ORIENTATION,        0xcdb5d8f7, 0x3cfd, 0x41c8, 0x85, 0x42, 0xcc, 0xe6, 0x22, 0xcf, 0x5d, 0x6e);
DEFINE_GUID(SENSOR_TYPE_AGGREGATED_QUADRANT_ORIENTATION,      0x9f81f1af, 0xc4ab, 0x4307, 0x99, 0x04, 0xc8, 0x28, 0xbf, 0xb9, 0x08, 0x29);
DEFINE_GUID(SENSOR_TYPE_AGGREGATED_SIMPLE_DEVICE_ORIENTATION, 0x86a19291, 0x0482, 0x402c, 0xbf, 0x4c, 0xad, 0xda, 0xc5, 0x2b, 0x1c, 0x39);
DEFINE_GUID(SENSOR_TYPE_COMPASS_1D,                           0xa415f6c5, 0xcb50, 0x49d0, 0x8e, 0x62, 0xa8, 0x27, 0x0b, 0xd7, 0xa2, 0x6c);
DEFINE_GUID(SENSOR_TYPE_COMPASS_2D,                           0x15655cc0, 0x997a, 0x4d30, 0x84, 0xdb, 0x57, 0xca, 0xba, 0x36, 0x48, 0xbb);
DEFINE_GUID(SENSOR_TYPE_COMPASS_3D,                           0x76b5ce0d, 0x17dd, 0x414d, 0x93, 0xa1, 0xe1, 0x27, 0xf4, 0x0b, 0xdf, 0x6e);
DEFINE_GUID(SENSOR_TYPE_DISTANCE_1D,                          0x5f14ab2f, 0x1407, 0x4306, 0xa9, 0x3f, 0xb1, 0xdb, 0xab, 0xe4, 0xf9, 0xc0);
DEFINE_GUID(SENSOR_TYPE_DISTANCE_2D,                          0x5cf9a46c, 0xa9a2, 0x4e55, 0xb6, 0xa1, 0xa0, 0x4a, 0xaf, 0xa9, 0x5a, 0x92);
DEFINE_GUID(SENSOR_TYPE_DISTANCE_3D,                          0xa20cae31, 0x0e25, 0x4772, 0x9f, 0xe5, 0x96, 0x60, 0x8a, 0x13, 0x54, 0xb2);
DEFINE_GUID(SENSOR_TYPE_INCLINOMETER_1D,                      0xb96f98c5, 0x7a75, 0x4ba7, 0x94, 0xe9, 0xac, 0x86, 0x8c, 0x96, 0x6d, 0xd8);
DEFINE_GUID(SENSOR_TYPE_INCLINOMETER_2D,                      0xab140f6d, 0x83eb, 0x4264, 0xb7, 0x0b, 0xb1, 0x6a, 0x5b, 0x25, 0x6a, 0x01);
DEFINE_GUID(SENSOR_TYPE_INCLINOMETER_3D,                      0xb84919fb, 0xea85, 0x4976, 0x84, 0x44, 0x6f, 0x6f, 0x5c, 0x6d, 0x31, 0xdb);

DEFINE_GUID(SENSOR_DATA_TYPE_ORIENTATION_GUID,                                           0x1637d8a2, 0x4248, 0x4275, 0x86, 0x5d, 0x55, 0x8d, 0xe8, 0x4a, 0xed, 0xfd);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_TILT_X_DEGREES,                                      0x1637d8a2, 0x4248, 0x4275, 0x86, 0x5d, 0x55, 0x8d, 0xe8, 0x4a, 0xed, 0xfd, 2);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_TILT_Y_DEGREES,                                      0x1637d8a2, 0x4248, 0x4275, 0x86, 0x5d, 0x55, 0x8d, 0xe8, 0x4a, 0xed, 0xfd, 3);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_TILT_Z_DEGREES,                                      0x1637d8a2, 0x4248, 0x4275, 0x86, 0x5d, 0x55, 0x8d, 0xe8, 0x4a, 0xed, 0xfd, 4);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_MAGNETIC_HEADING_X_DEGREES,                          0x1637d8a2, 0x4248, 0x4275, 0x86, 0x5d, 0x55, 0x8d, 0xe8, 0x4a, 0xed, 0xfd, 5);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_MAGNETIC_HEADING_Y_DEGREES,                          0x1637d8a2, 0x4248, 0x4275, 0x86, 0x5d, 0x55, 0x8d, 0xe8, 0x4a, 0xed, 0xfd, 6);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_MAGNETIC_HEADING_Z_DEGREES,                          0x1637d8a2, 0x4248, 0x4275, 0x86, 0x5d, 0x55, 0x8d, 0xe8, 0x4a, 0xed, 0xfd, 7);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_DISTANCE_X_METERS,                                   0x1637d8a2, 0x4248, 0x4275, 0x86, 0x5d, 0x55, 0x8d, 0xe8, 0x4a, 0xed, 0xfd, 8);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_DISTANCE_Y_METERS,                                   0x1637d8a2, 0x4248, 0x4275, 0x86, 0x5d, 0x55, 0x8d, 0xe8, 0x4a, 0xed, 0xfd, 9);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_DISTANCE_Z_METERS,                                   0x1637d8a2, 0x4248, 0x4275, 0x86, 0x5d, 0x55, 0x8d, 0xe8, 0x4a, 0xed, 0xfd, 10);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_MAGNETIC_HEADING_COMPENSATED_MAGNETIC_NORTH_DEGREES, 0x1637d8a2, 0x4248, 0x4275, 0x86, 0x5d, 0x55, 0x8d, 0xe8, 0x4a, 0xed, 0xfd, 11);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_MAGNETIC_HEADING_COMPENSATED_TRUE_NORTH_DEGREES,     0x1637d8a2, 0x4248, 0x4275, 0x86, 0x5d, 0x55, 0x8d, 0xe8, 0x4a, 0xed, 0xfd, 12);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_MAGNETIC_HEADING_MAGNETIC_NORTH_DEGREES,             0x1637d8a2, 0x4248, 0x4275, 0x86, 0x5d, 0x55, 0x8d, 0xe8, 0x4a, 0xed, 0xfd, 13);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_MAGNETIC_HEADING_TRUE_NORTH_DEGREES,                 0x1637d8a2, 0x4248, 0x4275, 0x86, 0x5d, 0x55, 0x8d, 0xe8, 0x4a, 0xed, 0xfd, 14);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_QUADRANT_ANGLE_DEGREES,                              0x1637d8a2, 0x4248, 0x4275, 0x86, 0x5d, 0x55, 0x8d, 0xe8, 0x4a, 0xed, 0xfd, 15);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_ROTATION_MATRIX,                                     0x1637d8a2, 0x4248, 0x4275, 0x86, 0x5d, 0x55, 0x8d, 0xe8, 0x4a, 0xed, 0xfd, 16);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_QUATERNION,                                          0x1637d8a2, 0x4248, 0x4275, 0x86, 0x5d, 0x55, 0x8d, 0xe8, 0x4a, 0xed, 0xfd, 17);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_SIMPLE_DEVICE_ORIENTATION,                           0x1637d8a2, 0x4248, 0x4275, 0x86, 0x5d, 0x55, 0x8d, 0xe8, 0x4a, 0xed, 0xfd, 18);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_MAGNETIC_FIELD_STRENGTH_X_MILLIGAUSS,                0x1637d8a2, 0x4248, 0x4275, 0x86, 0x5d, 0x55, 0x8d, 0xe8, 0x4a, 0xed, 0xfd, 19);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_MAGNETIC_FIELD_STRENGTH_Y_MILLIGAUSS,                0x1637d8a2, 0x4248, 0x4275, 0x86, 0x5d, 0x55, 0x8d, 0xe8, 0x4a, 0xed, 0xfd, 20);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_MAGNETIC_FIELD_STRENGTH_Z_MILLIGAUSS,                0x1637d8a2, 0x4248, 0x4275, 0x86, 0x5d, 0x55, 0x8d, 0xe8, 0x4a, 0xed, 0xfd, 21);

DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_MAGNETOMETER_ACCURACY, 0x1637d8a2, 0x4248, 0x4275, 0x86, 0x5d, 0x55, 0x8d, 0xe8, 0x4a, 0xed, 0xfd, 22);

DEFINE_GUID(SENSOR_TYPE_CUSTOM,                               0xe83af229, 0x8640, 0x4d18, 0xa2, 0x13, 0xe2, 0x26, 0x75, 0xeb, 0xb2, 0xc3);

DEFINE_GUID(SENSOR_DATA_TYPE_CUSTOM_GUID,                     0xb14c764f, 0x07cf, 0x41e8, 0x9d, 0x82, 0xeb, 0xe3, 0xd0, 0x77, 0x6a, 0x6f);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_CUSTOM_USAGE,             0xb14c764f, 0x07cf, 0x41e8, 0x9d, 0x82, 0xeb, 0xe3, 0xd0, 0x77, 0x6a, 0x6f, 5);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_CUSTOM_BOOLEAN_ARRAY,     0xb14c764f, 0x07cf, 0x41e8, 0x9d, 0x82, 0xeb, 0xe3, 0xd0, 0x77, 0x6a, 0x6f, 6);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_CUSTOM_VALUE1,            0xb14c764f, 0x07cf, 0x41e8, 0x9d, 0x82, 0xeb, 0xe3, 0xd0, 0x77, 0x6a, 0x6f, 7);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_CUSTOM_VALUE2,            0xb14c764f, 0x07cf, 0x41e8, 0x9d, 0x82, 0xeb, 0xe3, 0xd0, 0x77, 0x6a, 0x6f, 8);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_CUSTOM_VALUE3,            0xb14c764f, 0x07cf, 0x41e8, 0x9d, 0x82, 0xeb, 0xe3, 0xd0, 0x77, 0x6a, 0x6f, 9);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_CUSTOM_VALUE4,            0xb14c764f, 0x07cf, 0x41e8, 0x9d, 0x82, 0xeb, 0xe3, 0xd0, 0x77, 0x6a, 0x6f, 10);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_CUSTOM_VALUE5,            0xb14c764f, 0x07cf, 0x41e8, 0x9d, 0x82, 0xeb, 0xe3, 0xd0, 0x77, 0x6a, 0x6f, 11);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_CUSTOM_VALUE6,            0xb14c764f, 0x07cf, 0x41e8, 0x9d, 0x82, 0xeb, 0xe3, 0xd0, 0x77, 0x6a, 0x6f, 12);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_CUSTOM_VALUE7,            0xb14c764f, 0x07cf, 0x41e8, 0x9d, 0x82, 0xeb, 0xe3, 0xd0, 0x77, 0x6a, 0x6f, 13);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_CUSTOM_VALUE8,            0xb14c764f, 0x07cf, 0x41e8, 0x9d, 0x82, 0xeb, 0xe3, 0xd0, 0x77, 0x6a, 0x6f, 14);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_CUSTOM_VALUE9,            0xb14c764f, 0x07cf, 0x41e8, 0x9d, 0x82, 0xeb, 0xe3, 0xd0, 0x77, 0x6a, 0x6f, 15);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_CUSTOM_VALUE10,           0xb14c764f, 0x07cf, 0x41e8, 0x9d, 0x82, 0xeb, 0xe3, 0xd0, 0x77, 0x6a, 0x6f, 16);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_CUSTOM_VALUE11,           0xb14c764f, 0x07cf, 0x41e8, 0x9d, 0x82, 0xeb, 0xe3, 0xd0, 0x77, 0x6a, 0x6f, 17);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_CUSTOM_VALUE12,           0xb14c764f, 0x07cf, 0x41e8, 0x9d, 0x82, 0xeb, 0xe3, 0xd0, 0x77, 0x6a, 0x6f, 18);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_CUSTOM_VALUE13,           0xb14c764f, 0x07cf, 0x41e8, 0x9d, 0x82, 0xeb, 0xe3, 0xd0, 0x77, 0x6a, 0x6f, 19);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_CUSTOM_VALUE14,           0xb14c764f, 0x07cf, 0x41e8, 0x9d, 0x82, 0xeb, 0xe3, 0xd0, 0x77, 0x6a, 0x6f, 20);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_CUSTOM_VALUE15,           0xb14c764f, 0x07cf, 0x41e8, 0x9d, 0x82, 0xeb, 0xe3, 0xd0, 0x77, 0x6a, 0x6f, 21);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_CUSTOM_VALUE16,           0xb14c764f, 0x07cf, 0x41e8, 0x9d, 0x82, 0xeb, 0xe3, 0xd0, 0x77, 0x6a, 0x6f, 22);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_CUSTOM_VALUE17,           0xb14c764f, 0x07cf, 0x41e8, 0x9d, 0x82, 0xeb, 0xe3, 0xd0, 0x77, 0x6a, 0x6f, 23);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_CUSTOM_VALUE18,           0xb14c764f, 0x07cf, 0x41e8, 0x9d, 0x82, 0xeb, 0xe3, 0xd0, 0x77, 0x6a, 0x6f, 24);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_CUSTOM_VALUE19,           0xb14c764f, 0x07cf, 0x41e8, 0x9d, 0x82, 0xeb, 0xe3, 0xd0, 0x77, 0x6a, 0x6f, 25);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_CUSTOM_VALUE20,           0xb14c764f, 0x07cf, 0x41e8, 0x9d, 0x82, 0xeb, 0xe3, 0xd0, 0x77, 0x6a, 0x6f, 26);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_CUSTOM_VALUE21,           0xb14c764f, 0x07cf, 0x41e8, 0x9d, 0x82, 0xeb, 0xe3, 0xd0, 0x77, 0x6a, 0x6f, 27);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_CUSTOM_VALUE22,           0xb14c764f, 0x07cf, 0x41e8, 0x9d, 0x82, 0xeb, 0xe3, 0xd0, 0x77, 0x6a, 0x6f, 28);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_CUSTOM_VALUE23,           0xb14c764f, 0x07cf, 0x41e8, 0x9d, 0x82, 0xeb, 0xe3, 0xd0, 0x77, 0x6a, 0x6f, 29);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_CUSTOM_VALUE24,           0xb14c764f, 0x07cf, 0x41e8, 0x9d, 0x82, 0xeb, 0xe3, 0xd0, 0x77, 0x6a, 0x6f, 30);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_CUSTOM_VALUE25,           0xb14c764f, 0x07cf, 0x41e8, 0x9d, 0x82, 0xeb, 0xe3, 0xd0, 0x77, 0x6a, 0x6f, 31);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_CUSTOM_VALUE26,           0xb14c764f, 0x07cf, 0x41e8, 0x9d, 0x82, 0xeb, 0xe3, 0xd0, 0x77, 0x6a, 0x6f, 32);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_CUSTOM_VALUE27,           0xb14c764f, 0x07cf, 0x41e8, 0x9d, 0x82, 0xeb, 0xe3, 0xd0, 0x77, 0x6a, 0x6f, 33);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_CUSTOM_VALUE28,           0xb14c764f, 0x07cf, 0x41e8, 0x9d, 0x82, 0xeb, 0xe3, 0xd0, 0x77, 0x6a, 0x6f, 34);

DEFINE_GUID(SENSOR_TYPE_BARCODE_SCANNER,                      0x990b3d8f, 0x85bb, 0x45ff, 0x91, 0x4d, 0x99, 0x8c, 0x04, 0xf3, 0x72, 0xdf);
DEFINE_GUID(SENSOR_TYPE_RFID_SCANNER,                         0x44328ef5, 0x02dd, 0x4e8d, 0xad, 0x5d, 0x92, 0x49, 0x83, 0x2b, 0x2e, 0xca);

DEFINE_GUID(SENSOR_DATA_TYPE_SCANNER_GUID,                    0xd7a59a3c, 0x3421, 0x44ab, 0x8d, 0x3a, 0x9d, 0xe8, 0xab, 0x6c, 0x4c, 0xae);
DEFINE_PROPERTYKEY(SENSOR_DATA_TYPE_RFID_TAG_40_BIT,          0xd7a59a3c, 0x3421, 0x44ab, 0x8d, 0x3a, 0x9d, 0xe8, 0xab, 0x6c, 0x4c, 0xae, 2);

DEFINE_GUID(SENSOR_TYPE_UNKNOWN,                              0x10ba83e3, 0xef4f, 0x41ed, 0x98, 0x85, 0xa8, 0x7d, 0x64, 0x35, 0xa8, 0xe1);

DEFINE_GUID(SENSOR_PROPERTY_TEST_GUID, 0xe1e962f4, 0x6e65, 0x45f7, 0x9c, 0x36, 0xd4, 0x87, 0xb7, 0xb1, 0xbd, 0x34);
DEFINE_PROPERTYKEY(SENSOR_PROPERTY_CLEAR_ASSISTANCE_DATA, 0xe1e962f4, 0x6e65, 0x45f7, 0x9c, 0x36, 0xd4, 0x87, 0xb7, 0xb1, 0xbd, 0x34, 2);
DEFINE_PROPERTYKEY(SENSOR_PROPERTY_TURN_ON_OFF_NMEA, 0xe1e962f4, 0x6e65, 0x45f7, 0x9c, 0x36, 0xd4, 0x87, 0xb7, 0xb1, 0xbd, 0x34, 3);

#define GNSS_CLEAR_ALL_ASSISTANCE_DATA 0x00000001

#endif
