/*** Autogenerated by WIDL 10.8 from include/msctf.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __msctf_h__
#define __msctf_h__

/* Forward declarations */

#ifndef __ITfFunctionProvider_FWD_DEFINED__
#define __ITfFunctionProvider_FWD_DEFINED__
typedef interface ITfFunctionProvider ITfFunctionProvider;
#ifdef __cplusplus
interface ITfFunctionProvider;
#endif /* __cplusplus */
#endif

#ifndef __IEnumTfFunctionProviders_FWD_DEFINED__
#define __IEnumTfFunctionProviders_FWD_DEFINED__
typedef interface IEnumTfFunctionProviders IEnumTfFunctionProviders;
#ifdef __cplusplus
interface IEnumTfFunctionProviders;
#endif /* __cplusplus */
#endif

#ifndef __ITfThreadMgr_FWD_DEFINED__
#define __ITfThreadMgr_FWD_DEFINED__
typedef interface ITfThreadMgr ITfThreadMgr;
#ifdef __cplusplus
interface ITfThreadMgr;
#endif /* __cplusplus */
#endif

#ifndef __ITfThreadMgrEx_FWD_DEFINED__
#define __ITfThreadMgrEx_FWD_DEFINED__
typedef interface ITfThreadMgrEx ITfThreadMgrEx;
#ifdef __cplusplus
interface ITfThreadMgrEx;
#endif /* __cplusplus */
#endif

#ifndef __ITfCompositionView_FWD_DEFINED__
#define __ITfCompositionView_FWD_DEFINED__
typedef interface ITfCompositionView ITfCompositionView;
#ifdef __cplusplus
interface ITfCompositionView;
#endif /* __cplusplus */
#endif

#ifndef __ITfDocumentMgr_FWD_DEFINED__
#define __ITfDocumentMgr_FWD_DEFINED__
typedef interface ITfDocumentMgr ITfDocumentMgr;
#ifdef __cplusplus
interface ITfDocumentMgr;
#endif /* __cplusplus */
#endif

#ifndef __ITfContextView_FWD_DEFINED__
#define __ITfContextView_FWD_DEFINED__
typedef interface ITfContextView ITfContextView;
#ifdef __cplusplus
interface ITfContextView;
#endif /* __cplusplus */
#endif

#ifndef __IEnumTfContextViews_FWD_DEFINED__
#define __IEnumTfContextViews_FWD_DEFINED__
typedef interface IEnumTfContextViews IEnumTfContextViews;
#ifdef __cplusplus
interface IEnumTfContextViews;
#endif /* __cplusplus */
#endif

#ifndef __IEnumTfProperties_FWD_DEFINED__
#define __IEnumTfProperties_FWD_DEFINED__
typedef interface IEnumTfProperties IEnumTfProperties;
#ifdef __cplusplus
interface IEnumTfProperties;
#endif /* __cplusplus */
#endif

#ifndef __IEnumTfPropertyValue_FWD_DEFINED__
#define __IEnumTfPropertyValue_FWD_DEFINED__
typedef interface IEnumTfPropertyValue IEnumTfPropertyValue;
#ifdef __cplusplus
interface IEnumTfPropertyValue;
#endif /* __cplusplus */
#endif

#ifndef __ITfRangeBackup_FWD_DEFINED__
#define __ITfRangeBackup_FWD_DEFINED__
typedef interface ITfRangeBackup ITfRangeBackup;
#ifdef __cplusplus
interface ITfRangeBackup;
#endif /* __cplusplus */
#endif

#ifndef __ITextStoreACPServices_FWD_DEFINED__
#define __ITextStoreACPServices_FWD_DEFINED__
typedef interface ITextStoreACPServices ITextStoreACPServices;
#ifdef __cplusplus
interface ITextStoreACPServices;
#endif /* __cplusplus */
#endif

#ifndef __ITfContext_FWD_DEFINED__
#define __ITfContext_FWD_DEFINED__
typedef interface ITfContext ITfContext;
#ifdef __cplusplus
interface ITfContext;
#endif /* __cplusplus */
#endif

#ifndef __ITfSource_FWD_DEFINED__
#define __ITfSource_FWD_DEFINED__
typedef interface ITfSource ITfSource;
#ifdef __cplusplus
interface ITfSource;
#endif /* __cplusplus */
#endif

#ifndef __ITfInputProcessorProfiles_FWD_DEFINED__
#define __ITfInputProcessorProfiles_FWD_DEFINED__
typedef interface ITfInputProcessorProfiles ITfInputProcessorProfiles;
#ifdef __cplusplus
interface ITfInputProcessorProfiles;
#endif /* __cplusplus */
#endif

#ifndef __IEnumTfInputProcessorProfiles_FWD_DEFINED__
#define __IEnumTfInputProcessorProfiles_FWD_DEFINED__
typedef interface IEnumTfInputProcessorProfiles IEnumTfInputProcessorProfiles;
#ifdef __cplusplus
interface IEnumTfInputProcessorProfiles;
#endif /* __cplusplus */
#endif

#ifndef __ITfInputProcessorProfileMgr_FWD_DEFINED__
#define __ITfInputProcessorProfileMgr_FWD_DEFINED__
typedef interface ITfInputProcessorProfileMgr ITfInputProcessorProfileMgr;
#ifdef __cplusplus
interface ITfInputProcessorProfileMgr;
#endif /* __cplusplus */
#endif

#ifndef __ITfDisplayAttributeInfo_FWD_DEFINED__
#define __ITfDisplayAttributeInfo_FWD_DEFINED__
typedef interface ITfDisplayAttributeInfo ITfDisplayAttributeInfo;
#ifdef __cplusplus
interface ITfDisplayAttributeInfo;
#endif /* __cplusplus */
#endif

#ifndef __IEnumTfDisplayAttributeInfo_FWD_DEFINED__
#define __IEnumTfDisplayAttributeInfo_FWD_DEFINED__
typedef interface IEnumTfDisplayAttributeInfo IEnumTfDisplayAttributeInfo;
#ifdef __cplusplus
interface IEnumTfDisplayAttributeInfo;
#endif /* __cplusplus */
#endif

#ifndef __ITfDisplayAttributeMgr_FWD_DEFINED__
#define __ITfDisplayAttributeMgr_FWD_DEFINED__
typedef interface ITfDisplayAttributeMgr ITfDisplayAttributeMgr;
#ifdef __cplusplus
interface ITfDisplayAttributeMgr;
#endif /* __cplusplus */
#endif

#ifndef __ITfCategoryMgr_FWD_DEFINED__
#define __ITfCategoryMgr_FWD_DEFINED__
typedef interface ITfCategoryMgr ITfCategoryMgr;
#ifdef __cplusplus
interface ITfCategoryMgr;
#endif /* __cplusplus */
#endif

#ifndef __IEnumTfRanges_FWD_DEFINED__
#define __IEnumTfRanges_FWD_DEFINED__
typedef interface IEnumTfRanges IEnumTfRanges;
#ifdef __cplusplus
interface IEnumTfRanges;
#endif /* __cplusplus */
#endif

#ifndef __ITfEditRecord_FWD_DEFINED__
#define __ITfEditRecord_FWD_DEFINED__
typedef interface ITfEditRecord ITfEditRecord;
#ifdef __cplusplus
interface ITfEditRecord;
#endif /* __cplusplus */
#endif

#ifndef __ITfTextEditSink_FWD_DEFINED__
#define __ITfTextEditSink_FWD_DEFINED__
typedef interface ITfTextEditSink ITfTextEditSink;
#ifdef __cplusplus
interface ITfTextEditSink;
#endif /* __cplusplus */
#endif

#ifndef __ITfContextOwnerCompositionSink_FWD_DEFINED__
#define __ITfContextOwnerCompositionSink_FWD_DEFINED__
typedef interface ITfContextOwnerCompositionSink ITfContextOwnerCompositionSink;
#ifdef __cplusplus
interface ITfContextOwnerCompositionSink;
#endif /* __cplusplus */
#endif

#ifndef __ITfActiveLanguageProfileNotifySink_FWD_DEFINED__
#define __ITfActiveLanguageProfileNotifySink_FWD_DEFINED__
typedef interface ITfActiveLanguageProfileNotifySink ITfActiveLanguageProfileNotifySink;
#ifdef __cplusplus
interface ITfActiveLanguageProfileNotifySink;
#endif /* __cplusplus */
#endif

#ifndef __IEnumTfLanguageProfiles_FWD_DEFINED__
#define __IEnumTfLanguageProfiles_FWD_DEFINED__
typedef interface IEnumTfLanguageProfiles IEnumTfLanguageProfiles;
#ifdef __cplusplus
interface IEnumTfLanguageProfiles;
#endif /* __cplusplus */
#endif

#ifndef __ITfTextInputProcessor_FWD_DEFINED__
#define __ITfTextInputProcessor_FWD_DEFINED__
typedef interface ITfTextInputProcessor ITfTextInputProcessor;
#ifdef __cplusplus
interface ITfTextInputProcessor;
#endif /* __cplusplus */
#endif

#ifndef __ITfThreadMgrEventSink_FWD_DEFINED__
#define __ITfThreadMgrEventSink_FWD_DEFINED__
typedef interface ITfThreadMgrEventSink ITfThreadMgrEventSink;
#ifdef __cplusplus
interface ITfThreadMgrEventSink;
#endif /* __cplusplus */
#endif

#ifndef __ITfKeystrokeMgr_FWD_DEFINED__
#define __ITfKeystrokeMgr_FWD_DEFINED__
typedef interface ITfKeystrokeMgr ITfKeystrokeMgr;
#ifdef __cplusplus
interface ITfKeystrokeMgr;
#endif /* __cplusplus */
#endif

#ifndef __ITfKeyEventSink_FWD_DEFINED__
#define __ITfKeyEventSink_FWD_DEFINED__
typedef interface ITfKeyEventSink ITfKeyEventSink;
#ifdef __cplusplus
interface ITfKeyEventSink;
#endif /* __cplusplus */
#endif

#ifndef __ITfKeyTraceEventSink_FWD_DEFINED__
#define __ITfKeyTraceEventSink_FWD_DEFINED__
typedef interface ITfKeyTraceEventSink ITfKeyTraceEventSink;
#ifdef __cplusplus
interface ITfKeyTraceEventSink;
#endif /* __cplusplus */
#endif

#ifndef __ITfUIElementSink_FWD_DEFINED__
#define __ITfUIElementSink_FWD_DEFINED__
typedef interface ITfUIElementSink ITfUIElementSink;
#ifdef __cplusplus
interface ITfUIElementSink;
#endif /* __cplusplus */
#endif

#ifndef __ITfMessagePump_FWD_DEFINED__
#define __ITfMessagePump_FWD_DEFINED__
typedef interface ITfMessagePump ITfMessagePump;
#ifdef __cplusplus
interface ITfMessagePump;
#endif /* __cplusplus */
#endif

#ifndef __ITfClientId_FWD_DEFINED__
#define __ITfClientId_FWD_DEFINED__
typedef interface ITfClientId ITfClientId;
#ifdef __cplusplus
interface ITfClientId;
#endif /* __cplusplus */
#endif

#ifndef __ITfLanguageProfileNotifySink_FWD_DEFINED__
#define __ITfLanguageProfileNotifySink_FWD_DEFINED__
typedef interface ITfLanguageProfileNotifySink ITfLanguageProfileNotifySink;
#ifdef __cplusplus
interface ITfLanguageProfileNotifySink;
#endif /* __cplusplus */
#endif

#ifndef __ITfEditSession_FWD_DEFINED__
#define __ITfEditSession_FWD_DEFINED__
typedef interface ITfEditSession ITfEditSession;
#ifdef __cplusplus
interface ITfEditSession;
#endif /* __cplusplus */
#endif

#ifndef __ITfRange_FWD_DEFINED__
#define __ITfRange_FWD_DEFINED__
typedef interface ITfRange ITfRange;
#ifdef __cplusplus
interface ITfRange;
#endif /* __cplusplus */
#endif

#ifndef __ITfRangeACP_FWD_DEFINED__
#define __ITfRangeACP_FWD_DEFINED__
typedef interface ITfRangeACP ITfRangeACP;
#ifdef __cplusplus
interface ITfRangeACP;
#endif /* __cplusplus */
#endif

#ifndef __ITfInsertAtSelection_FWD_DEFINED__
#define __ITfInsertAtSelection_FWD_DEFINED__
typedef interface ITfInsertAtSelection ITfInsertAtSelection;
#ifdef __cplusplus
interface ITfInsertAtSelection;
#endif /* __cplusplus */
#endif

#ifndef __ITfPropertyStore_FWD_DEFINED__
#define __ITfPropertyStore_FWD_DEFINED__
typedef interface ITfPropertyStore ITfPropertyStore;
#ifdef __cplusplus
interface ITfPropertyStore;
#endif /* __cplusplus */
#endif

#ifndef __IEnumITfCompositionView_FWD_DEFINED__
#define __IEnumITfCompositionView_FWD_DEFINED__
typedef interface IEnumITfCompositionView IEnumITfCompositionView;
#ifdef __cplusplus
interface IEnumITfCompositionView;
#endif /* __cplusplus */
#endif

#ifndef __ITfComposition_FWD_DEFINED__
#define __ITfComposition_FWD_DEFINED__
typedef interface ITfComposition ITfComposition;
#ifdef __cplusplus
interface ITfComposition;
#endif /* __cplusplus */
#endif

#ifndef __ITfCompositionSink_FWD_DEFINED__
#define __ITfCompositionSink_FWD_DEFINED__
typedef interface ITfCompositionSink ITfCompositionSink;
#ifdef __cplusplus
interface ITfCompositionSink;
#endif /* __cplusplus */
#endif

#ifndef __ITfContextComposition_FWD_DEFINED__
#define __ITfContextComposition_FWD_DEFINED__
typedef interface ITfContextComposition ITfContextComposition;
#ifdef __cplusplus
interface ITfContextComposition;
#endif /* __cplusplus */
#endif

#ifndef __ITfContextOwnerCompositionServices_FWD_DEFINED__
#define __ITfContextOwnerCompositionServices_FWD_DEFINED__
typedef interface ITfContextOwnerCompositionServices ITfContextOwnerCompositionServices;
#ifdef __cplusplus
interface ITfContextOwnerCompositionServices;
#endif /* __cplusplus */
#endif

#ifndef __ITfPersistentPropertyLoaderACP_FWD_DEFINED__
#define __ITfPersistentPropertyLoaderACP_FWD_DEFINED__
typedef interface ITfPersistentPropertyLoaderACP ITfPersistentPropertyLoaderACP;
#ifdef __cplusplus
interface ITfPersistentPropertyLoaderACP;
#endif /* __cplusplus */
#endif

#ifndef __ITfContextOwnerServices_FWD_DEFINED__
#define __ITfContextOwnerServices_FWD_DEFINED__
typedef interface ITfContextOwnerServices ITfContextOwnerServices;
#ifdef __cplusplus
interface ITfContextOwnerServices;
#endif /* __cplusplus */
#endif

#ifndef __ITfReadOnlyProperty_FWD_DEFINED__
#define __ITfReadOnlyProperty_FWD_DEFINED__
typedef interface ITfReadOnlyProperty ITfReadOnlyProperty;
#ifdef __cplusplus
interface ITfReadOnlyProperty;
#endif /* __cplusplus */
#endif

#ifndef __ITfProperty_FWD_DEFINED__
#define __ITfProperty_FWD_DEFINED__
typedef interface ITfProperty ITfProperty;
#ifdef __cplusplus
interface ITfProperty;
#endif /* __cplusplus */
#endif

#ifndef __ITfCompartment_FWD_DEFINED__
#define __ITfCompartment_FWD_DEFINED__
typedef interface ITfCompartment ITfCompartment;
#ifdef __cplusplus
interface ITfCompartment;
#endif /* __cplusplus */
#endif

#ifndef __ITfCompartmentMgr_FWD_DEFINED__
#define __ITfCompartmentMgr_FWD_DEFINED__
typedef interface ITfCompartmentMgr ITfCompartmentMgr;
#ifdef __cplusplus
interface ITfCompartmentMgr;
#endif /* __cplusplus */
#endif

#ifndef __ITfCompartmentEventSink_FWD_DEFINED__
#define __ITfCompartmentEventSink_FWD_DEFINED__
typedef interface ITfCompartmentEventSink ITfCompartmentEventSink;
#ifdef __cplusplus
interface ITfCompartmentEventSink;
#endif /* __cplusplus */
#endif

#ifndef __IEnumTfContexts_FWD_DEFINED__
#define __IEnumTfContexts_FWD_DEFINED__
typedef interface IEnumTfContexts IEnumTfContexts;
#ifdef __cplusplus
interface IEnumTfContexts;
#endif /* __cplusplus */
#endif

#ifndef __IEnumTfDocumentMgrs_FWD_DEFINED__
#define __IEnumTfDocumentMgrs_FWD_DEFINED__
typedef interface IEnumTfDocumentMgrs IEnumTfDocumentMgrs;
#ifdef __cplusplus
interface IEnumTfDocumentMgrs;
#endif /* __cplusplus */
#endif

#ifndef __ITfUIElement_FWD_DEFINED__
#define __ITfUIElement_FWD_DEFINED__
typedef interface ITfUIElement ITfUIElement;
#ifdef __cplusplus
interface ITfUIElement;
#endif /* __cplusplus */
#endif

#ifndef __IEnumTfUIElements_FWD_DEFINED__
#define __IEnumTfUIElements_FWD_DEFINED__
typedef interface IEnumTfUIElements IEnumTfUIElements;
#ifdef __cplusplus
interface IEnumTfUIElements;
#endif /* __cplusplus */
#endif

#ifndef __ITfUIElementMgr_FWD_DEFINED__
#define __ITfUIElementMgr_FWD_DEFINED__
typedef interface ITfUIElementMgr ITfUIElementMgr;
#ifdef __cplusplus
interface ITfUIElementMgr;
#endif /* __cplusplus */
#endif

#ifndef __ITfSourceSingle_FWD_DEFINED__
#define __ITfSourceSingle_FWD_DEFINED__
typedef interface ITfSourceSingle ITfSourceSingle;
#ifdef __cplusplus
interface ITfSourceSingle;
#endif /* __cplusplus */
#endif

#ifndef __ITfThreadFocusSink_FWD_DEFINED__
#define __ITfThreadFocusSink_FWD_DEFINED__
typedef interface ITfThreadFocusSink ITfThreadFocusSink;
#ifdef __cplusplus
interface ITfThreadFocusSink;
#endif /* __cplusplus */
#endif

#ifndef __ITfInputProcessorProfileActivationSink_FWD_DEFINED__
#define __ITfInputProcessorProfileActivationSink_FWD_DEFINED__
typedef interface ITfInputProcessorProfileActivationSink ITfInputProcessorProfileActivationSink;
#ifdef __cplusplus
interface ITfInputProcessorProfileActivationSink;
#endif /* __cplusplus */
#endif

#ifndef __ITfMouseSink_FWD_DEFINED__
#define __ITfMouseSink_FWD_DEFINED__
typedef interface ITfMouseSink ITfMouseSink;
#ifdef __cplusplus
interface ITfMouseSink;
#endif /* __cplusplus */
#endif

#ifndef __ITfMouseTracker_FWD_DEFINED__
#define __ITfMouseTracker_FWD_DEFINED__
typedef interface ITfMouseTracker ITfMouseTracker;
#ifdef __cplusplus
interface ITfMouseTracker;
#endif /* __cplusplus */
#endif

#ifndef __ITfMouseTrackerACP_FWD_DEFINED__
#define __ITfMouseTrackerACP_FWD_DEFINED__
typedef interface ITfMouseTrackerACP ITfMouseTrackerACP;
#ifdef __cplusplus
interface ITfMouseTrackerACP;
#endif /* __cplusplus */
#endif

#ifndef __ITfTransitoryExtensionSink_FWD_DEFINED__
#define __ITfTransitoryExtensionSink_FWD_DEFINED__
typedef interface ITfTransitoryExtensionSink ITfTransitoryExtensionSink;
#ifdef __cplusplus
interface ITfTransitoryExtensionSink;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <oaidl.h>
#include <comcat.h>
#include <textstor.h>
#include <ctfutb.h>

#ifdef __cplusplus
extern "C" {
#endif

#define TF_E_LOCKED                MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x0500)
#define TF_E_STACKFULL             MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x0501)
#define TF_E_NOTOWNEDRANGE         MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x0502)
#define TF_E_NOPROVIDER            MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x0503)
#define TF_E_DISCONNECTED          MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x0504)
#define TF_E_INVALIDVIEW           MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x0505)
#define TF_E_ALREADY_EXISTS        MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x0506)
#define TF_E_RANGE_NOT_COVERED     MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x0507)
#define TF_E_COMPOSITION_REJECTED  MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x0508)
#define TF_E_EMPTYCONTEXT          MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x0509)
#define TF_E_INVALIDPOS            MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x0200)
#define TF_E_NOLOCK                MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x0201)
#define TF_E_NOOBJECT              MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x0202)
#define TF_E_NOSERVICE             MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x0203)
#define TF_E_NOINTERFACE           MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x0204)
#define TF_E_NOSELECTION           MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x0205)
#define TF_E_NOLAYOUT              MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x0206)
#define TF_E_INVALIDPOINT          MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x0207)
#define TF_E_SYNCHRONOUS           MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x0208)
#define TF_E_READONLY              MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x0209)
#define TF_E_FORMAT                MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x020a)
#define TF_S_ASYNC                 MAKE_HRESULT(SEVERITY_SUCCESS, FACILITY_ITF, 0x0300)
HRESULT WINAPI TF_CreateThreadMgr(ITfThreadMgr **pptim);
HRESULT WINAPI TF_GetThreadMgr(ITfThreadMgr **pptim);
HRESULT WINAPI TF_CreateInputProcessorProfiles(ITfInputProcessorProfiles **ppipr);
HRESULT WINAPI TF_CreateLangBarMgr(ITfLangBarMgr **pppbm);
HRESULT WINAPI TF_CreateLangBarItemMgr(ITfLangBarItemMgr **pplbim);
HRESULT WINAPI TF_InvalidAssemblyListCacheIfExist(void);
EXTERN_C const GUID GUID_PROP_TEXTOWNER;
DEFINE_GUID(GUID_PROP_ATTRIBUTE,0x34b45670,0x7526,0x11d2,0xa1,0x47,0x00,0x10,0x5a,0x27,0x99,0xb5);
EXTERN_C const GUID GUID_PROP_LANGID;
EXTERN_C const GUID GUID_PROP_READING;
EXTERN_C const GUID GUID_PROP_COMPOSING;
EXTERN_C const CLSID CLSID_TF_ThreadMgr;
EXTERN_C const CLSID CLSID_TF_InputProcessorProfiles;
EXTERN_C const CLSID CLSID_TF_LangBarMgr;
EXTERN_C const CLSID CLSID_TF_CategoryMgr;
EXTERN_C const CLSID CLSID_TF_DisplayAttributeMgr;
EXTERN_C const GUID GUID_COMPARTMENT_KEYBOARD_DISABLED;
EXTERN_C const GUID GUID_COMPARTMENT_KEYBOARD_OPENCLOSE;
EXTERN_C const GUID GUID_COMPARTMENT_HANDWRITING_OPENCLOSE;
EXTERN_C const GUID GUID_COMPARTMENT_SPEECH_DISABLED;
EXTERN_C const GUID GUID_COMPARTMENT_SPEECH_OPENCLOSE;
EXTERN_C const GUID GUID_COMPARTMENT_SPEECH_GLOBALSTATE;
EXTERN_C const GUID GUID_COMPARTMENT_PERSISTMENUENABLED;
EXTERN_C const GUID GUID_COMPARTMENT_EMPTYCONTEXT;
EXTERN_C const GUID GUID_COMPARTMENT_TIPUISTATUS;
EXTERN_C const GUID GUID_TFCAT_TIP_KEYBOARD;
EXTERN_C const GUID GUID_TFCAT_TIP_SPEECH;
EXTERN_C const GUID GUID_TFCAT_TIP_HANDWRITING;
EXTERN_C const GUID GUID_TFCAT_DISPLAYATTRIBUTEPROVIDER;
typedef DWORD TfEditCookie;
typedef DWORD TfClientId;
typedef DWORD TfGuidAtom;
#define TF_MOD_ALT                   0x0001
#define TF_MOD_CONTROL               0x0002
#define TF_MOD_SHIFT                 0x0004
#define TF_MOD_RALT                  0x0008
#define TF_MOD_RCONTROL              0x0010
#define TF_MOD_RSHIFT                0x0020
#define TF_MOD_LALT                  0x0040
#define TF_MOD_LCONTROL              0x0080
#define TF_MOD_LSHIFT                0x0100
#define TF_MOD_ON_KEYUP              0x0200
#define TF_MOD_IGNORE_ALL_MODIFIER   0x0400
#define TF_PROFILETYPE_INPUTPROCESSOR  0x0001
#define TF_PROFILETYPE_KEYBOARDLAYOUT  0x0002
#define TF_IPSINK_FLAG_ACTIVE 0x0001
#define TF_TMAE_NOACTIVATETIP            0x00000001
#define TF_TMAE_SECUREMODE               0x00000002
#define TF_TMAE_UIELEMENTENABLEDONLY     0x00000004
#define TF_TMAE_COMLESS                  0x00000008
#define TF_TMAE_WOW16                    0x00000010
#define TF_TMAE_NOACTIVATEKEYBOARDLAYOUT 0x00000020
#define TF_TMAE_CONSOLE                  0x00000040
#define TF_TMF_NOACTIVATETIP             TF_TMAE_NOACTIVATETIP
#define TF_TMF_SECUREMODE                TF_TMAE_SECUREMODE
#define TF_TMF_UIELEMENTENABLEDONLY      TF_TMAE_UIELEMENTENABLEDONLY
#define TF_TMF_COMLESS                   TF_TMAE_COMLESS
#define TF_TMF_WOW16                     TF_TMAE_WOW16
#define TF_TMF_CONSOLE                   TF_TMAE_CONSOLE
#define TF_TMF_IMMERSIVEMODE             0x40000000
#define TF_TMF_ACTIVATED                 0x80000000
#ifndef __ITfDocumentMgr_FWD_DEFINED__
#define __ITfDocumentMgr_FWD_DEFINED__
typedef interface ITfDocumentMgr ITfDocumentMgr;
#ifdef __cplusplus
interface ITfDocumentMgr;
#endif /* __cplusplus */
#endif

#ifndef __ITfContext_FWD_DEFINED__
#define __ITfContext_FWD_DEFINED__
typedef interface ITfContext ITfContext;
#ifdef __cplusplus
interface ITfContext;
#endif /* __cplusplus */
#endif

#ifndef __IEnumTfDocumentMgrs_FWD_DEFINED__
#define __IEnumTfDocumentMgrs_FWD_DEFINED__
typedef interface IEnumTfDocumentMgrs IEnumTfDocumentMgrs;
#ifdef __cplusplus
interface IEnumTfDocumentMgrs;
#endif /* __cplusplus */
#endif

#ifndef __IEnumTfContexts_FWD_DEFINED__
#define __IEnumTfContexts_FWD_DEFINED__
typedef interface IEnumTfContexts IEnumTfContexts;
#ifdef __cplusplus
interface IEnumTfContexts;
#endif /* __cplusplus */
#endif

#ifndef __ITfCompartmentMgr_FWD_DEFINED__
#define __ITfCompartmentMgr_FWD_DEFINED__
typedef interface ITfCompartmentMgr ITfCompartmentMgr;
#ifdef __cplusplus
interface ITfCompartmentMgr;
#endif /* __cplusplus */
#endif

#ifndef __ITfEditSession_FWD_DEFINED__
#define __ITfEditSession_FWD_DEFINED__
typedef interface ITfEditSession ITfEditSession;
#ifdef __cplusplus
interface ITfEditSession;
#endif /* __cplusplus */
#endif

#ifndef __ITfRange_FWD_DEFINED__
#define __ITfRange_FWD_DEFINED__
typedef interface ITfRange ITfRange;
#ifdef __cplusplus
interface ITfRange;
#endif /* __cplusplus */
#endif

#ifndef __ITfProperty_FWD_DEFINED__
#define __ITfProperty_FWD_DEFINED__
typedef interface ITfProperty ITfProperty;
#ifdef __cplusplus
interface ITfProperty;
#endif /* __cplusplus */
#endif

#ifndef __ITfReadOnlyProperty_FWD_DEFINED__
#define __ITfReadOnlyProperty_FWD_DEFINED__
typedef interface ITfReadOnlyProperty ITfReadOnlyProperty;
#ifdef __cplusplus
interface ITfReadOnlyProperty;
#endif /* __cplusplus */
#endif

#ifndef __IEnumTfLanguageProfiles_FWD_DEFINED__
#define __IEnumTfLanguageProfiles_FWD_DEFINED__
typedef interface IEnumTfLanguageProfiles IEnumTfLanguageProfiles;
#ifdef __cplusplus
interface IEnumTfLanguageProfiles;
#endif /* __cplusplus */
#endif

#ifndef __ITfCompositionView_FWD_DEFINED__
#define __ITfCompositionView_FWD_DEFINED__
typedef interface ITfCompositionView ITfCompositionView;
#ifdef __cplusplus
interface ITfCompositionView;
#endif /* __cplusplus */
#endif

#ifndef __ITfKeyEventSink_FWD_DEFINED__
#define __ITfKeyEventSink_FWD_DEFINED__
typedef interface ITfKeyEventSink ITfKeyEventSink;
#ifdef __cplusplus
interface ITfKeyEventSink;
#endif /* __cplusplus */
#endif

#ifndef __ITfPersistentPropertyLoaderACP_FWD_DEFINED__
#define __ITfPersistentPropertyLoaderACP_FWD_DEFINED__
typedef interface ITfPersistentPropertyLoaderACP ITfPersistentPropertyLoaderACP;
#ifdef __cplusplus
interface ITfPersistentPropertyLoaderACP;
#endif /* __cplusplus */
#endif

#ifndef __ITfRangeACP_FWD_DEFINED__
#define __ITfRangeACP_FWD_DEFINED__
typedef interface ITfRangeACP ITfRangeACP;
#ifdef __cplusplus
interface ITfRangeACP;
#endif /* __cplusplus */
#endif

#if 0
typedef UINT_PTR HKL;
#endif
typedef struct TF_PERSISTENT_PROPERTY_HEADER_ACP {
    GUID guidType;
    LONG ichStart;
    LONG cch;
    ULONG cb;
    DWORD dwPrivate;
    CLSID clsidTIP;
} TF_PERSISTENT_PROPERTY_HEADER_ACP;
typedef struct TF_LANGUAGEPROFILE {
    CLSID clsid;
    LANGID langid;
    GUID catid;
    WINBOOL fActive;
    GUID guidProfile;
} TF_LANGUAGEPROFILE;
typedef struct TF_PRESERVEDKEY {
    UINT uVKey;
    UINT uModifiers;
} TF_PRESERVEDKEY;
typedef enum __WIDL_msctf_generated_name_00000012 {
    TF_ANCHOR_START = 0,
    TF_ANCHOR_END = 1
} TfAnchor;
typedef struct TF_PROPERTYVAL {
    GUID guidId;
    VARIANT varValue;
} TF_PROPERTYVAL;
/*****************************************************************************
 * ITfFunctionProvider interface
 */
#ifndef __ITfFunctionProvider_INTERFACE_DEFINED__
#define __ITfFunctionProvider_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITfFunctionProvider, 0x101d6610, 0x0990, 0x11d3, 0x8d,0xf0, 0x00,0x10,0x5a,0x27,0x99,0xb5);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("101d6610-0990-11d3-8df0-00105a2799b5")
ITfFunctionProvider : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetType(
        GUID *guid) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDescription(
        BSTR *desc) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFunction(
        REFGUID guid,
        REFIID riid,
        IUnknown **func) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITfFunctionProvider, 0x101d6610, 0x0990, 0x11d3, 0x8d,0xf0, 0x00,0x10,0x5a,0x27,0x99,0xb5)
#endif
#else
typedef struct ITfFunctionProviderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITfFunctionProvider *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITfFunctionProvider *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITfFunctionProvider *This);

    /*** ITfFunctionProvider methods ***/
    HRESULT (STDMETHODCALLTYPE *GetType)(
        ITfFunctionProvider *This,
        GUID *guid);

    HRESULT (STDMETHODCALLTYPE *GetDescription)(
        ITfFunctionProvider *This,
        BSTR *desc);

    HRESULT (STDMETHODCALLTYPE *GetFunction)(
        ITfFunctionProvider *This,
        REFGUID guid,
        REFIID riid,
        IUnknown **func);

    END_INTERFACE
} ITfFunctionProviderVtbl;

interface ITfFunctionProvider {
    CONST_VTBL ITfFunctionProviderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITfFunctionProvider_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITfFunctionProvider_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITfFunctionProvider_Release(This) (This)->lpVtbl->Release(This)
/*** ITfFunctionProvider methods ***/
#define ITfFunctionProvider_GetType(This,guid) (This)->lpVtbl->GetType(This,guid)
#define ITfFunctionProvider_GetDescription(This,desc) (This)->lpVtbl->GetDescription(This,desc)
#define ITfFunctionProvider_GetFunction(This,guid,riid,func) (This)->lpVtbl->GetFunction(This,guid,riid,func)
#else
/*** IUnknown methods ***/
static inline HRESULT ITfFunctionProvider_QueryInterface(ITfFunctionProvider* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITfFunctionProvider_AddRef(ITfFunctionProvider* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITfFunctionProvider_Release(ITfFunctionProvider* This) {
    return This->lpVtbl->Release(This);
}
/*** ITfFunctionProvider methods ***/
static inline HRESULT ITfFunctionProvider_GetType(ITfFunctionProvider* This,GUID *guid) {
    return This->lpVtbl->GetType(This,guid);
}
static inline HRESULT ITfFunctionProvider_GetDescription(ITfFunctionProvider* This,BSTR *desc) {
    return This->lpVtbl->GetDescription(This,desc);
}
static inline HRESULT ITfFunctionProvider_GetFunction(ITfFunctionProvider* This,REFGUID guid,REFIID riid,IUnknown **func) {
    return This->lpVtbl->GetFunction(This,guid,riid,func);
}
#endif
#endif

#endif


#endif  /* __ITfFunctionProvider_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IEnumTfFunctionProviders interface
 */
#ifndef __IEnumTfFunctionProviders_INTERFACE_DEFINED__
#define __IEnumTfFunctionProviders_INTERFACE_DEFINED__

DEFINE_GUID(IID_IEnumTfFunctionProviders, 0xe4b24db0, 0x0990, 0x11d3, 0x8d,0xf0, 0x00,0x10,0x5a,0x27,0x99,0xb5);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("e4b24db0-0990-11d3-8df0-00105a2799b5")
IEnumTfFunctionProviders : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Clone(
        IEnumTfFunctionProviders **ret) = 0;

    virtual HRESULT STDMETHODCALLTYPE Next(
        ULONG count,
        ITfFunctionProvider **prov,
        ULONG *fetched) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reset(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Skip(
        ULONG count) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IEnumTfFunctionProviders, 0xe4b24db0, 0x0990, 0x11d3, 0x8d,0xf0, 0x00,0x10,0x5a,0x27,0x99,0xb5)
#endif
#else
typedef struct IEnumTfFunctionProvidersVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IEnumTfFunctionProviders *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IEnumTfFunctionProviders *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IEnumTfFunctionProviders *This);

    /*** IEnumTfFunctionProviders methods ***/
    HRESULT (STDMETHODCALLTYPE *Clone)(
        IEnumTfFunctionProviders *This,
        IEnumTfFunctionProviders **ret);

    HRESULT (STDMETHODCALLTYPE *Next)(
        IEnumTfFunctionProviders *This,
        ULONG count,
        ITfFunctionProvider **prov,
        ULONG *fetched);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        IEnumTfFunctionProviders *This);

    HRESULT (STDMETHODCALLTYPE *Skip)(
        IEnumTfFunctionProviders *This,
        ULONG count);

    END_INTERFACE
} IEnumTfFunctionProvidersVtbl;

interface IEnumTfFunctionProviders {
    CONST_VTBL IEnumTfFunctionProvidersVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IEnumTfFunctionProviders_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IEnumTfFunctionProviders_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IEnumTfFunctionProviders_Release(This) (This)->lpVtbl->Release(This)
/*** IEnumTfFunctionProviders methods ***/
#define IEnumTfFunctionProviders_Clone(This,ret) (This)->lpVtbl->Clone(This,ret)
#define IEnumTfFunctionProviders_Next(This,count,prov,fetched) (This)->lpVtbl->Next(This,count,prov,fetched)
#define IEnumTfFunctionProviders_Reset(This) (This)->lpVtbl->Reset(This)
#define IEnumTfFunctionProviders_Skip(This,count) (This)->lpVtbl->Skip(This,count)
#else
/*** IUnknown methods ***/
static inline HRESULT IEnumTfFunctionProviders_QueryInterface(IEnumTfFunctionProviders* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IEnumTfFunctionProviders_AddRef(IEnumTfFunctionProviders* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IEnumTfFunctionProviders_Release(IEnumTfFunctionProviders* This) {
    return This->lpVtbl->Release(This);
}
/*** IEnumTfFunctionProviders methods ***/
static inline HRESULT IEnumTfFunctionProviders_Clone(IEnumTfFunctionProviders* This,IEnumTfFunctionProviders **ret) {
    return This->lpVtbl->Clone(This,ret);
}
static inline HRESULT IEnumTfFunctionProviders_Next(IEnumTfFunctionProviders* This,ULONG count,ITfFunctionProvider **prov,ULONG *fetched) {
    return This->lpVtbl->Next(This,count,prov,fetched);
}
static inline HRESULT IEnumTfFunctionProviders_Reset(IEnumTfFunctionProviders* This) {
    return This->lpVtbl->Reset(This);
}
static inline HRESULT IEnumTfFunctionProviders_Skip(IEnumTfFunctionProviders* This,ULONG count) {
    return This->lpVtbl->Skip(This,count);
}
#endif
#endif

#endif


#endif  /* __IEnumTfFunctionProviders_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITfThreadMgr interface
 */
#ifndef __ITfThreadMgr_INTERFACE_DEFINED__
#define __ITfThreadMgr_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITfThreadMgr, 0xaa80e801, 0x2021, 0x11d2, 0x93,0xe0, 0x00,0x60,0xb0,0x67,0xb8,0x6e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("aa80e801-2021-11d2-93e0-0060b067b86e")
ITfThreadMgr : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Activate(
        TfClientId *ptid) = 0;

    virtual HRESULT STDMETHODCALLTYPE Deactivate(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateDocumentMgr(
        ITfDocumentMgr **ppdim) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumDocumentMgrs(
        IEnumTfDocumentMgrs **ppEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFocus(
        ITfDocumentMgr **ppdimFocus) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetFocus(
        ITfDocumentMgr *pdimFocus) = 0;

    virtual HRESULT STDMETHODCALLTYPE AssociateFocus(
        HWND hwnd,
        ITfDocumentMgr *pdimNew,
        ITfDocumentMgr **ppdimPrev) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsThreadFocus(
        WINBOOL *pfThreadFocus) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFunctionProvider(
        REFCLSID clsid,
        ITfFunctionProvider **ppFuncProv) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumFunctionProviders(
        IEnumTfFunctionProviders **ppEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetGlobalCompartment(
        ITfCompartmentMgr **ppCompMgr) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITfThreadMgr, 0xaa80e801, 0x2021, 0x11d2, 0x93,0xe0, 0x00,0x60,0xb0,0x67,0xb8,0x6e)
#endif
#else
typedef struct ITfThreadMgrVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITfThreadMgr *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITfThreadMgr *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITfThreadMgr *This);

    /*** ITfThreadMgr methods ***/
    HRESULT (STDMETHODCALLTYPE *Activate)(
        ITfThreadMgr *This,
        TfClientId *ptid);

    HRESULT (STDMETHODCALLTYPE *Deactivate)(
        ITfThreadMgr *This);

    HRESULT (STDMETHODCALLTYPE *CreateDocumentMgr)(
        ITfThreadMgr *This,
        ITfDocumentMgr **ppdim);

    HRESULT (STDMETHODCALLTYPE *EnumDocumentMgrs)(
        ITfThreadMgr *This,
        IEnumTfDocumentMgrs **ppEnum);

    HRESULT (STDMETHODCALLTYPE *GetFocus)(
        ITfThreadMgr *This,
        ITfDocumentMgr **ppdimFocus);

    HRESULT (STDMETHODCALLTYPE *SetFocus)(
        ITfThreadMgr *This,
        ITfDocumentMgr *pdimFocus);

    HRESULT (STDMETHODCALLTYPE *AssociateFocus)(
        ITfThreadMgr *This,
        HWND hwnd,
        ITfDocumentMgr *pdimNew,
        ITfDocumentMgr **ppdimPrev);

    HRESULT (STDMETHODCALLTYPE *IsThreadFocus)(
        ITfThreadMgr *This,
        WINBOOL *pfThreadFocus);

    HRESULT (STDMETHODCALLTYPE *GetFunctionProvider)(
        ITfThreadMgr *This,
        REFCLSID clsid,
        ITfFunctionProvider **ppFuncProv);

    HRESULT (STDMETHODCALLTYPE *EnumFunctionProviders)(
        ITfThreadMgr *This,
        IEnumTfFunctionProviders **ppEnum);

    HRESULT (STDMETHODCALLTYPE *GetGlobalCompartment)(
        ITfThreadMgr *This,
        ITfCompartmentMgr **ppCompMgr);

    END_INTERFACE
} ITfThreadMgrVtbl;

interface ITfThreadMgr {
    CONST_VTBL ITfThreadMgrVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITfThreadMgr_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITfThreadMgr_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITfThreadMgr_Release(This) (This)->lpVtbl->Release(This)
/*** ITfThreadMgr methods ***/
#define ITfThreadMgr_Activate(This,ptid) (This)->lpVtbl->Activate(This,ptid)
#define ITfThreadMgr_Deactivate(This) (This)->lpVtbl->Deactivate(This)
#define ITfThreadMgr_CreateDocumentMgr(This,ppdim) (This)->lpVtbl->CreateDocumentMgr(This,ppdim)
#define ITfThreadMgr_EnumDocumentMgrs(This,ppEnum) (This)->lpVtbl->EnumDocumentMgrs(This,ppEnum)
#define ITfThreadMgr_GetFocus(This,ppdimFocus) (This)->lpVtbl->GetFocus(This,ppdimFocus)
#define ITfThreadMgr_SetFocus(This,pdimFocus) (This)->lpVtbl->SetFocus(This,pdimFocus)
#define ITfThreadMgr_AssociateFocus(This,hwnd,pdimNew,ppdimPrev) (This)->lpVtbl->AssociateFocus(This,hwnd,pdimNew,ppdimPrev)
#define ITfThreadMgr_IsThreadFocus(This,pfThreadFocus) (This)->lpVtbl->IsThreadFocus(This,pfThreadFocus)
#define ITfThreadMgr_GetFunctionProvider(This,clsid,ppFuncProv) (This)->lpVtbl->GetFunctionProvider(This,clsid,ppFuncProv)
#define ITfThreadMgr_EnumFunctionProviders(This,ppEnum) (This)->lpVtbl->EnumFunctionProviders(This,ppEnum)
#define ITfThreadMgr_GetGlobalCompartment(This,ppCompMgr) (This)->lpVtbl->GetGlobalCompartment(This,ppCompMgr)
#else
/*** IUnknown methods ***/
static inline HRESULT ITfThreadMgr_QueryInterface(ITfThreadMgr* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITfThreadMgr_AddRef(ITfThreadMgr* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITfThreadMgr_Release(ITfThreadMgr* This) {
    return This->lpVtbl->Release(This);
}
/*** ITfThreadMgr methods ***/
static inline HRESULT ITfThreadMgr_Activate(ITfThreadMgr* This,TfClientId *ptid) {
    return This->lpVtbl->Activate(This,ptid);
}
static inline HRESULT ITfThreadMgr_Deactivate(ITfThreadMgr* This) {
    return This->lpVtbl->Deactivate(This);
}
static inline HRESULT ITfThreadMgr_CreateDocumentMgr(ITfThreadMgr* This,ITfDocumentMgr **ppdim) {
    return This->lpVtbl->CreateDocumentMgr(This,ppdim);
}
static inline HRESULT ITfThreadMgr_EnumDocumentMgrs(ITfThreadMgr* This,IEnumTfDocumentMgrs **ppEnum) {
    return This->lpVtbl->EnumDocumentMgrs(This,ppEnum);
}
static inline HRESULT ITfThreadMgr_GetFocus(ITfThreadMgr* This,ITfDocumentMgr **ppdimFocus) {
    return This->lpVtbl->GetFocus(This,ppdimFocus);
}
static inline HRESULT ITfThreadMgr_SetFocus(ITfThreadMgr* This,ITfDocumentMgr *pdimFocus) {
    return This->lpVtbl->SetFocus(This,pdimFocus);
}
static inline HRESULT ITfThreadMgr_AssociateFocus(ITfThreadMgr* This,HWND hwnd,ITfDocumentMgr *pdimNew,ITfDocumentMgr **ppdimPrev) {
    return This->lpVtbl->AssociateFocus(This,hwnd,pdimNew,ppdimPrev);
}
static inline HRESULT ITfThreadMgr_IsThreadFocus(ITfThreadMgr* This,WINBOOL *pfThreadFocus) {
    return This->lpVtbl->IsThreadFocus(This,pfThreadFocus);
}
static inline HRESULT ITfThreadMgr_GetFunctionProvider(ITfThreadMgr* This,REFCLSID clsid,ITfFunctionProvider **ppFuncProv) {
    return This->lpVtbl->GetFunctionProvider(This,clsid,ppFuncProv);
}
static inline HRESULT ITfThreadMgr_EnumFunctionProviders(ITfThreadMgr* This,IEnumTfFunctionProviders **ppEnum) {
    return This->lpVtbl->EnumFunctionProviders(This,ppEnum);
}
static inline HRESULT ITfThreadMgr_GetGlobalCompartment(ITfThreadMgr* This,ITfCompartmentMgr **ppCompMgr) {
    return This->lpVtbl->GetGlobalCompartment(This,ppCompMgr);
}
#endif
#endif

#endif


#endif  /* __ITfThreadMgr_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITfThreadMgrEx interface
 */
#ifndef __ITfThreadMgrEx_INTERFACE_DEFINED__
#define __ITfThreadMgrEx_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITfThreadMgrEx, 0x3e90ade3, 0x7594, 0x4cb0, 0xbb,0x58, 0x69,0x62,0x8f,0x5f,0x45,0x8c);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("3e90ade3-7594-4cb0-bb58-69628f5f458c")
ITfThreadMgrEx : public ITfThreadMgr
{
    virtual HRESULT STDMETHODCALLTYPE ActivateEx(
        TfClientId *id,
        DWORD flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetActiveFlags(
        DWORD *flags) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITfThreadMgrEx, 0x3e90ade3, 0x7594, 0x4cb0, 0xbb,0x58, 0x69,0x62,0x8f,0x5f,0x45,0x8c)
#endif
#else
typedef struct ITfThreadMgrExVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITfThreadMgrEx *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITfThreadMgrEx *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITfThreadMgrEx *This);

    /*** ITfThreadMgr methods ***/
    HRESULT (STDMETHODCALLTYPE *Activate)(
        ITfThreadMgrEx *This,
        TfClientId *ptid);

    HRESULT (STDMETHODCALLTYPE *Deactivate)(
        ITfThreadMgrEx *This);

    HRESULT (STDMETHODCALLTYPE *CreateDocumentMgr)(
        ITfThreadMgrEx *This,
        ITfDocumentMgr **ppdim);

    HRESULT (STDMETHODCALLTYPE *EnumDocumentMgrs)(
        ITfThreadMgrEx *This,
        IEnumTfDocumentMgrs **ppEnum);

    HRESULT (STDMETHODCALLTYPE *GetFocus)(
        ITfThreadMgrEx *This,
        ITfDocumentMgr **ppdimFocus);

    HRESULT (STDMETHODCALLTYPE *SetFocus)(
        ITfThreadMgrEx *This,
        ITfDocumentMgr *pdimFocus);

    HRESULT (STDMETHODCALLTYPE *AssociateFocus)(
        ITfThreadMgrEx *This,
        HWND hwnd,
        ITfDocumentMgr *pdimNew,
        ITfDocumentMgr **ppdimPrev);

    HRESULT (STDMETHODCALLTYPE *IsThreadFocus)(
        ITfThreadMgrEx *This,
        WINBOOL *pfThreadFocus);

    HRESULT (STDMETHODCALLTYPE *GetFunctionProvider)(
        ITfThreadMgrEx *This,
        REFCLSID clsid,
        ITfFunctionProvider **ppFuncProv);

    HRESULT (STDMETHODCALLTYPE *EnumFunctionProviders)(
        ITfThreadMgrEx *This,
        IEnumTfFunctionProviders **ppEnum);

    HRESULT (STDMETHODCALLTYPE *GetGlobalCompartment)(
        ITfThreadMgrEx *This,
        ITfCompartmentMgr **ppCompMgr);

    /*** ITfThreadMgrEx methods ***/
    HRESULT (STDMETHODCALLTYPE *ActivateEx)(
        ITfThreadMgrEx *This,
        TfClientId *id,
        DWORD flags);

    HRESULT (STDMETHODCALLTYPE *GetActiveFlags)(
        ITfThreadMgrEx *This,
        DWORD *flags);

    END_INTERFACE
} ITfThreadMgrExVtbl;

interface ITfThreadMgrEx {
    CONST_VTBL ITfThreadMgrExVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITfThreadMgrEx_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITfThreadMgrEx_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITfThreadMgrEx_Release(This) (This)->lpVtbl->Release(This)
/*** ITfThreadMgr methods ***/
#define ITfThreadMgrEx_Activate(This,ptid) (This)->lpVtbl->Activate(This,ptid)
#define ITfThreadMgrEx_Deactivate(This) (This)->lpVtbl->Deactivate(This)
#define ITfThreadMgrEx_CreateDocumentMgr(This,ppdim) (This)->lpVtbl->CreateDocumentMgr(This,ppdim)
#define ITfThreadMgrEx_EnumDocumentMgrs(This,ppEnum) (This)->lpVtbl->EnumDocumentMgrs(This,ppEnum)
#define ITfThreadMgrEx_GetFocus(This,ppdimFocus) (This)->lpVtbl->GetFocus(This,ppdimFocus)
#define ITfThreadMgrEx_SetFocus(This,pdimFocus) (This)->lpVtbl->SetFocus(This,pdimFocus)
#define ITfThreadMgrEx_AssociateFocus(This,hwnd,pdimNew,ppdimPrev) (This)->lpVtbl->AssociateFocus(This,hwnd,pdimNew,ppdimPrev)
#define ITfThreadMgrEx_IsThreadFocus(This,pfThreadFocus) (This)->lpVtbl->IsThreadFocus(This,pfThreadFocus)
#define ITfThreadMgrEx_GetFunctionProvider(This,clsid,ppFuncProv) (This)->lpVtbl->GetFunctionProvider(This,clsid,ppFuncProv)
#define ITfThreadMgrEx_EnumFunctionProviders(This,ppEnum) (This)->lpVtbl->EnumFunctionProviders(This,ppEnum)
#define ITfThreadMgrEx_GetGlobalCompartment(This,ppCompMgr) (This)->lpVtbl->GetGlobalCompartment(This,ppCompMgr)
/*** ITfThreadMgrEx methods ***/
#define ITfThreadMgrEx_ActivateEx(This,id,flags) (This)->lpVtbl->ActivateEx(This,id,flags)
#define ITfThreadMgrEx_GetActiveFlags(This,flags) (This)->lpVtbl->GetActiveFlags(This,flags)
#else
/*** IUnknown methods ***/
static inline HRESULT ITfThreadMgrEx_QueryInterface(ITfThreadMgrEx* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITfThreadMgrEx_AddRef(ITfThreadMgrEx* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITfThreadMgrEx_Release(ITfThreadMgrEx* This) {
    return This->lpVtbl->Release(This);
}
/*** ITfThreadMgr methods ***/
static inline HRESULT ITfThreadMgrEx_Activate(ITfThreadMgrEx* This,TfClientId *ptid) {
    return This->lpVtbl->Activate(This,ptid);
}
static inline HRESULT ITfThreadMgrEx_Deactivate(ITfThreadMgrEx* This) {
    return This->lpVtbl->Deactivate(This);
}
static inline HRESULT ITfThreadMgrEx_CreateDocumentMgr(ITfThreadMgrEx* This,ITfDocumentMgr **ppdim) {
    return This->lpVtbl->CreateDocumentMgr(This,ppdim);
}
static inline HRESULT ITfThreadMgrEx_EnumDocumentMgrs(ITfThreadMgrEx* This,IEnumTfDocumentMgrs **ppEnum) {
    return This->lpVtbl->EnumDocumentMgrs(This,ppEnum);
}
static inline HRESULT ITfThreadMgrEx_GetFocus(ITfThreadMgrEx* This,ITfDocumentMgr **ppdimFocus) {
    return This->lpVtbl->GetFocus(This,ppdimFocus);
}
static inline HRESULT ITfThreadMgrEx_SetFocus(ITfThreadMgrEx* This,ITfDocumentMgr *pdimFocus) {
    return This->lpVtbl->SetFocus(This,pdimFocus);
}
static inline HRESULT ITfThreadMgrEx_AssociateFocus(ITfThreadMgrEx* This,HWND hwnd,ITfDocumentMgr *pdimNew,ITfDocumentMgr **ppdimPrev) {
    return This->lpVtbl->AssociateFocus(This,hwnd,pdimNew,ppdimPrev);
}
static inline HRESULT ITfThreadMgrEx_IsThreadFocus(ITfThreadMgrEx* This,WINBOOL *pfThreadFocus) {
    return This->lpVtbl->IsThreadFocus(This,pfThreadFocus);
}
static inline HRESULT ITfThreadMgrEx_GetFunctionProvider(ITfThreadMgrEx* This,REFCLSID clsid,ITfFunctionProvider **ppFuncProv) {
    return This->lpVtbl->GetFunctionProvider(This,clsid,ppFuncProv);
}
static inline HRESULT ITfThreadMgrEx_EnumFunctionProviders(ITfThreadMgrEx* This,IEnumTfFunctionProviders **ppEnum) {
    return This->lpVtbl->EnumFunctionProviders(This,ppEnum);
}
static inline HRESULT ITfThreadMgrEx_GetGlobalCompartment(ITfThreadMgrEx* This,ITfCompartmentMgr **ppCompMgr) {
    return This->lpVtbl->GetGlobalCompartment(This,ppCompMgr);
}
/*** ITfThreadMgrEx methods ***/
static inline HRESULT ITfThreadMgrEx_ActivateEx(ITfThreadMgrEx* This,TfClientId *id,DWORD flags) {
    return This->lpVtbl->ActivateEx(This,id,flags);
}
static inline HRESULT ITfThreadMgrEx_GetActiveFlags(ITfThreadMgrEx* This,DWORD *flags) {
    return This->lpVtbl->GetActiveFlags(This,flags);
}
#endif
#endif

#endif


#endif  /* __ITfThreadMgrEx_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITfCompositionView interface
 */
#ifndef __ITfCompositionView_INTERFACE_DEFINED__
#define __ITfCompositionView_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITfCompositionView, 0xd7540241, 0xf9a1, 0x4364, 0xbe,0xfc, 0xdb,0xcd,0x2c,0x43,0x95,0xb7);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d7540241-f9a1-4364-befc-dbcd2c4395b7")
ITfCompositionView : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetOwnerClsid(
        CLSID *pclsid) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRange(
        ITfRange **ppRange) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITfCompositionView, 0xd7540241, 0xf9a1, 0x4364, 0xbe,0xfc, 0xdb,0xcd,0x2c,0x43,0x95,0xb7)
#endif
#else
typedef struct ITfCompositionViewVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITfCompositionView *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITfCompositionView *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITfCompositionView *This);

    /*** ITfCompositionView methods ***/
    HRESULT (STDMETHODCALLTYPE *GetOwnerClsid)(
        ITfCompositionView *This,
        CLSID *pclsid);

    HRESULT (STDMETHODCALLTYPE *GetRange)(
        ITfCompositionView *This,
        ITfRange **ppRange);

    END_INTERFACE
} ITfCompositionViewVtbl;

interface ITfCompositionView {
    CONST_VTBL ITfCompositionViewVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITfCompositionView_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITfCompositionView_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITfCompositionView_Release(This) (This)->lpVtbl->Release(This)
/*** ITfCompositionView methods ***/
#define ITfCompositionView_GetOwnerClsid(This,pclsid) (This)->lpVtbl->GetOwnerClsid(This,pclsid)
#define ITfCompositionView_GetRange(This,ppRange) (This)->lpVtbl->GetRange(This,ppRange)
#else
/*** IUnknown methods ***/
static inline HRESULT ITfCompositionView_QueryInterface(ITfCompositionView* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITfCompositionView_AddRef(ITfCompositionView* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITfCompositionView_Release(ITfCompositionView* This) {
    return This->lpVtbl->Release(This);
}
/*** ITfCompositionView methods ***/
static inline HRESULT ITfCompositionView_GetOwnerClsid(ITfCompositionView* This,CLSID *pclsid) {
    return This->lpVtbl->GetOwnerClsid(This,pclsid);
}
static inline HRESULT ITfCompositionView_GetRange(ITfCompositionView* This,ITfRange **ppRange) {
    return This->lpVtbl->GetRange(This,ppRange);
}
#endif
#endif

#endif


#endif  /* __ITfCompositionView_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITfDocumentMgr interface
 */
#ifndef __ITfDocumentMgr_INTERFACE_DEFINED__
#define __ITfDocumentMgr_INTERFACE_DEFINED__

#define TF_POPF_ALL (0x1)

DEFINE_GUID(IID_ITfDocumentMgr, 0xaa80e7f4, 0x2021, 0x11d2, 0x93,0xe0, 0x00,0x60,0xb0,0x67,0xb8,0x6e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("aa80e7f4-2021-11d2-93e0-0060b067b86e")
ITfDocumentMgr : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE CreateContext(
        TfClientId tidOwner,
        DWORD dwFlags,
        IUnknown *punk,
        ITfContext **ppic,
        TfEditCookie *pecTextStore) = 0;

    virtual HRESULT STDMETHODCALLTYPE Push(
        ITfContext *pic) = 0;

    virtual HRESULT STDMETHODCALLTYPE Pop(
        DWORD dwFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTop(
        ITfContext **ppic) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetBase(
        ITfContext **ppic) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumContexts(
        IEnumTfContexts **ppEnum) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITfDocumentMgr, 0xaa80e7f4, 0x2021, 0x11d2, 0x93,0xe0, 0x00,0x60,0xb0,0x67,0xb8,0x6e)
#endif
#else
typedef struct ITfDocumentMgrVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITfDocumentMgr *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITfDocumentMgr *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITfDocumentMgr *This);

    /*** ITfDocumentMgr methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateContext)(
        ITfDocumentMgr *This,
        TfClientId tidOwner,
        DWORD dwFlags,
        IUnknown *punk,
        ITfContext **ppic,
        TfEditCookie *pecTextStore);

    HRESULT (STDMETHODCALLTYPE *Push)(
        ITfDocumentMgr *This,
        ITfContext *pic);

    HRESULT (STDMETHODCALLTYPE *Pop)(
        ITfDocumentMgr *This,
        DWORD dwFlags);

    HRESULT (STDMETHODCALLTYPE *GetTop)(
        ITfDocumentMgr *This,
        ITfContext **ppic);

    HRESULT (STDMETHODCALLTYPE *GetBase)(
        ITfDocumentMgr *This,
        ITfContext **ppic);

    HRESULT (STDMETHODCALLTYPE *EnumContexts)(
        ITfDocumentMgr *This,
        IEnumTfContexts **ppEnum);

    END_INTERFACE
} ITfDocumentMgrVtbl;

interface ITfDocumentMgr {
    CONST_VTBL ITfDocumentMgrVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITfDocumentMgr_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITfDocumentMgr_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITfDocumentMgr_Release(This) (This)->lpVtbl->Release(This)
/*** ITfDocumentMgr methods ***/
#define ITfDocumentMgr_CreateContext(This,tidOwner,dwFlags,punk,ppic,pecTextStore) (This)->lpVtbl->CreateContext(This,tidOwner,dwFlags,punk,ppic,pecTextStore)
#define ITfDocumentMgr_Push(This,pic) (This)->lpVtbl->Push(This,pic)
#define ITfDocumentMgr_Pop(This,dwFlags) (This)->lpVtbl->Pop(This,dwFlags)
#define ITfDocumentMgr_GetTop(This,ppic) (This)->lpVtbl->GetTop(This,ppic)
#define ITfDocumentMgr_GetBase(This,ppic) (This)->lpVtbl->GetBase(This,ppic)
#define ITfDocumentMgr_EnumContexts(This,ppEnum) (This)->lpVtbl->EnumContexts(This,ppEnum)
#else
/*** IUnknown methods ***/
static inline HRESULT ITfDocumentMgr_QueryInterface(ITfDocumentMgr* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITfDocumentMgr_AddRef(ITfDocumentMgr* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITfDocumentMgr_Release(ITfDocumentMgr* This) {
    return This->lpVtbl->Release(This);
}
/*** ITfDocumentMgr methods ***/
static inline HRESULT ITfDocumentMgr_CreateContext(ITfDocumentMgr* This,TfClientId tidOwner,DWORD dwFlags,IUnknown *punk,ITfContext **ppic,TfEditCookie *pecTextStore) {
    return This->lpVtbl->CreateContext(This,tidOwner,dwFlags,punk,ppic,pecTextStore);
}
static inline HRESULT ITfDocumentMgr_Push(ITfDocumentMgr* This,ITfContext *pic) {
    return This->lpVtbl->Push(This,pic);
}
static inline HRESULT ITfDocumentMgr_Pop(ITfDocumentMgr* This,DWORD dwFlags) {
    return This->lpVtbl->Pop(This,dwFlags);
}
static inline HRESULT ITfDocumentMgr_GetTop(ITfDocumentMgr* This,ITfContext **ppic) {
    return This->lpVtbl->GetTop(This,ppic);
}
static inline HRESULT ITfDocumentMgr_GetBase(ITfDocumentMgr* This,ITfContext **ppic) {
    return This->lpVtbl->GetBase(This,ppic);
}
static inline HRESULT ITfDocumentMgr_EnumContexts(ITfDocumentMgr* This,IEnumTfContexts **ppEnum) {
    return This->lpVtbl->EnumContexts(This,ppEnum);
}
#endif
#endif

#endif


#endif  /* __ITfDocumentMgr_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITfContextView interface
 */
#ifndef __ITfContextView_INTERFACE_DEFINED__
#define __ITfContextView_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITfContextView, 0x2433bf8e, 0x0f9b, 0x435c, 0xba,0x2c, 0x18,0x06,0x11,0x97,0x8c,0x30);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("2433bf8e-0f9b-435c-ba2c-180611978c30")
ITfContextView : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetRangeFromPoint(
        TfEditCookie cookie,
        const POINT *pt,
        DWORD flags,
        ITfRange **range) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTextExt(
        TfEditCookie cookie,
        ITfRange *range,
        RECT *rect,
        WINBOOL *clipped) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetScreenExt(
        RECT *rect) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetWnd(
        HWND *hwnd) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITfContextView, 0x2433bf8e, 0x0f9b, 0x435c, 0xba,0x2c, 0x18,0x06,0x11,0x97,0x8c,0x30)
#endif
#else
typedef struct ITfContextViewVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITfContextView *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITfContextView *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITfContextView *This);

    /*** ITfContextView methods ***/
    HRESULT (STDMETHODCALLTYPE *GetRangeFromPoint)(
        ITfContextView *This,
        TfEditCookie cookie,
        const POINT *pt,
        DWORD flags,
        ITfRange **range);

    HRESULT (STDMETHODCALLTYPE *GetTextExt)(
        ITfContextView *This,
        TfEditCookie cookie,
        ITfRange *range,
        RECT *rect,
        WINBOOL *clipped);

    HRESULT (STDMETHODCALLTYPE *GetScreenExt)(
        ITfContextView *This,
        RECT *rect);

    HRESULT (STDMETHODCALLTYPE *GetWnd)(
        ITfContextView *This,
        HWND *hwnd);

    END_INTERFACE
} ITfContextViewVtbl;

interface ITfContextView {
    CONST_VTBL ITfContextViewVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITfContextView_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITfContextView_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITfContextView_Release(This) (This)->lpVtbl->Release(This)
/*** ITfContextView methods ***/
#define ITfContextView_GetRangeFromPoint(This,cookie,pt,flags,range) (This)->lpVtbl->GetRangeFromPoint(This,cookie,pt,flags,range)
#define ITfContextView_GetTextExt(This,cookie,range,rect,clipped) (This)->lpVtbl->GetTextExt(This,cookie,range,rect,clipped)
#define ITfContextView_GetScreenExt(This,rect) (This)->lpVtbl->GetScreenExt(This,rect)
#define ITfContextView_GetWnd(This,hwnd) (This)->lpVtbl->GetWnd(This,hwnd)
#else
/*** IUnknown methods ***/
static inline HRESULT ITfContextView_QueryInterface(ITfContextView* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITfContextView_AddRef(ITfContextView* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITfContextView_Release(ITfContextView* This) {
    return This->lpVtbl->Release(This);
}
/*** ITfContextView methods ***/
static inline HRESULT ITfContextView_GetRangeFromPoint(ITfContextView* This,TfEditCookie cookie,const POINT *pt,DWORD flags,ITfRange **range) {
    return This->lpVtbl->GetRangeFromPoint(This,cookie,pt,flags,range);
}
static inline HRESULT ITfContextView_GetTextExt(ITfContextView* This,TfEditCookie cookie,ITfRange *range,RECT *rect,WINBOOL *clipped) {
    return This->lpVtbl->GetTextExt(This,cookie,range,rect,clipped);
}
static inline HRESULT ITfContextView_GetScreenExt(ITfContextView* This,RECT *rect) {
    return This->lpVtbl->GetScreenExt(This,rect);
}
static inline HRESULT ITfContextView_GetWnd(ITfContextView* This,HWND *hwnd) {
    return This->lpVtbl->GetWnd(This,hwnd);
}
#endif
#endif

#endif


#endif  /* __ITfContextView_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IEnumTfContextViews interface
 */
#ifndef __IEnumTfContextViews_INTERFACE_DEFINED__
#define __IEnumTfContextViews_INTERFACE_DEFINED__

DEFINE_GUID(IID_IEnumTfContextViews, 0xf0c0f8dd, 0xcf38, 0x44e1, 0xbb,0x0f, 0x68,0xcf,0x0d,0x55,0x1c,0x78);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("f0c0f8dd-cf38-44e1-bb0f-68cf0d551c78")
IEnumTfContextViews : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Clone(
        IEnumTfContextViews **ret) = 0;

    virtual HRESULT STDMETHODCALLTYPE Next(
        ULONG count,
        ITfContextView **views,
        ULONG *fetched) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reset(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Skip(
        ULONG count) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IEnumTfContextViews, 0xf0c0f8dd, 0xcf38, 0x44e1, 0xbb,0x0f, 0x68,0xcf,0x0d,0x55,0x1c,0x78)
#endif
#else
typedef struct IEnumTfContextViewsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IEnumTfContextViews *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IEnumTfContextViews *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IEnumTfContextViews *This);

    /*** IEnumTfContextViews methods ***/
    HRESULT (STDMETHODCALLTYPE *Clone)(
        IEnumTfContextViews *This,
        IEnumTfContextViews **ret);

    HRESULT (STDMETHODCALLTYPE *Next)(
        IEnumTfContextViews *This,
        ULONG count,
        ITfContextView **views,
        ULONG *fetched);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        IEnumTfContextViews *This);

    HRESULT (STDMETHODCALLTYPE *Skip)(
        IEnumTfContextViews *This,
        ULONG count);

    END_INTERFACE
} IEnumTfContextViewsVtbl;

interface IEnumTfContextViews {
    CONST_VTBL IEnumTfContextViewsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IEnumTfContextViews_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IEnumTfContextViews_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IEnumTfContextViews_Release(This) (This)->lpVtbl->Release(This)
/*** IEnumTfContextViews methods ***/
#define IEnumTfContextViews_Clone(This,ret) (This)->lpVtbl->Clone(This,ret)
#define IEnumTfContextViews_Next(This,count,views,fetched) (This)->lpVtbl->Next(This,count,views,fetched)
#define IEnumTfContextViews_Reset(This) (This)->lpVtbl->Reset(This)
#define IEnumTfContextViews_Skip(This,count) (This)->lpVtbl->Skip(This,count)
#else
/*** IUnknown methods ***/
static inline HRESULT IEnumTfContextViews_QueryInterface(IEnumTfContextViews* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IEnumTfContextViews_AddRef(IEnumTfContextViews* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IEnumTfContextViews_Release(IEnumTfContextViews* This) {
    return This->lpVtbl->Release(This);
}
/*** IEnumTfContextViews methods ***/
static inline HRESULT IEnumTfContextViews_Clone(IEnumTfContextViews* This,IEnumTfContextViews **ret) {
    return This->lpVtbl->Clone(This,ret);
}
static inline HRESULT IEnumTfContextViews_Next(IEnumTfContextViews* This,ULONG count,ITfContextView **views,ULONG *fetched) {
    return This->lpVtbl->Next(This,count,views,fetched);
}
static inline HRESULT IEnumTfContextViews_Reset(IEnumTfContextViews* This) {
    return This->lpVtbl->Reset(This);
}
static inline HRESULT IEnumTfContextViews_Skip(IEnumTfContextViews* This,ULONG count) {
    return This->lpVtbl->Skip(This,count);
}
#endif
#endif

#endif


#endif  /* __IEnumTfContextViews_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IEnumTfProperties interface
 */
#ifndef __IEnumTfProperties_INTERFACE_DEFINED__
#define __IEnumTfProperties_INTERFACE_DEFINED__

DEFINE_GUID(IID_IEnumTfProperties, 0x19188cb0, 0xaca9, 0x11d2, 0xaf,0xc5, 0x00,0x10,0x5a,0x27,0x99,0xb5);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("19188cb0-aca9-11d2-afc5-00105a2799b5")
IEnumTfProperties : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Clone(
        IEnumTfProperties **ret) = 0;

    virtual HRESULT STDMETHODCALLTYPE Next(
        ULONG count,
        ITfProperty **props,
        ULONG *fetched) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reset(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Skip(
        ULONG count) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IEnumTfProperties, 0x19188cb0, 0xaca9, 0x11d2, 0xaf,0xc5, 0x00,0x10,0x5a,0x27,0x99,0xb5)
#endif
#else
typedef struct IEnumTfPropertiesVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IEnumTfProperties *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IEnumTfProperties *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IEnumTfProperties *This);

    /*** IEnumTfProperties methods ***/
    HRESULT (STDMETHODCALLTYPE *Clone)(
        IEnumTfProperties *This,
        IEnumTfProperties **ret);

    HRESULT (STDMETHODCALLTYPE *Next)(
        IEnumTfProperties *This,
        ULONG count,
        ITfProperty **props,
        ULONG *fetched);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        IEnumTfProperties *This);

    HRESULT (STDMETHODCALLTYPE *Skip)(
        IEnumTfProperties *This,
        ULONG count);

    END_INTERFACE
} IEnumTfPropertiesVtbl;

interface IEnumTfProperties {
    CONST_VTBL IEnumTfPropertiesVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IEnumTfProperties_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IEnumTfProperties_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IEnumTfProperties_Release(This) (This)->lpVtbl->Release(This)
/*** IEnumTfProperties methods ***/
#define IEnumTfProperties_Clone(This,ret) (This)->lpVtbl->Clone(This,ret)
#define IEnumTfProperties_Next(This,count,props,fetched) (This)->lpVtbl->Next(This,count,props,fetched)
#define IEnumTfProperties_Reset(This) (This)->lpVtbl->Reset(This)
#define IEnumTfProperties_Skip(This,count) (This)->lpVtbl->Skip(This,count)
#else
/*** IUnknown methods ***/
static inline HRESULT IEnumTfProperties_QueryInterface(IEnumTfProperties* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IEnumTfProperties_AddRef(IEnumTfProperties* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IEnumTfProperties_Release(IEnumTfProperties* This) {
    return This->lpVtbl->Release(This);
}
/*** IEnumTfProperties methods ***/
static inline HRESULT IEnumTfProperties_Clone(IEnumTfProperties* This,IEnumTfProperties **ret) {
    return This->lpVtbl->Clone(This,ret);
}
static inline HRESULT IEnumTfProperties_Next(IEnumTfProperties* This,ULONG count,ITfProperty **props,ULONG *fetched) {
    return This->lpVtbl->Next(This,count,props,fetched);
}
static inline HRESULT IEnumTfProperties_Reset(IEnumTfProperties* This) {
    return This->lpVtbl->Reset(This);
}
static inline HRESULT IEnumTfProperties_Skip(IEnumTfProperties* This,ULONG count) {
    return This->lpVtbl->Skip(This,count);
}
#endif
#endif

#endif


#endif  /* __IEnumTfProperties_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IEnumTfPropertyValue interface
 */
#ifndef __IEnumTfPropertyValue_INTERFACE_DEFINED__
#define __IEnumTfPropertyValue_INTERFACE_DEFINED__

DEFINE_GUID(IID_IEnumTfPropertyValue, 0x8ed8981b, 0x7c10, 0x4d7d, 0x9f,0xb3, 0xab,0x72,0xe9,0xc7,0x5f,0x72);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("8ed8981b-7c10-4d7d-9fb3-ab72e9c75f72")
IEnumTfPropertyValue : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Clone(
        IEnumTfPropertyValue **property_value) = 0;

    virtual HRESULT STDMETHODCALLTYPE Next(
        ULONG count,
        TF_PROPERTYVAL *values,
        ULONG *fetched) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reset(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Skip(
        ULONG count) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IEnumTfPropertyValue, 0x8ed8981b, 0x7c10, 0x4d7d, 0x9f,0xb3, 0xab,0x72,0xe9,0xc7,0x5f,0x72)
#endif
#else
typedef struct IEnumTfPropertyValueVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IEnumTfPropertyValue *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IEnumTfPropertyValue *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IEnumTfPropertyValue *This);

    /*** IEnumTfPropertyValue methods ***/
    HRESULT (STDMETHODCALLTYPE *Clone)(
        IEnumTfPropertyValue *This,
        IEnumTfPropertyValue **property_value);

    HRESULT (STDMETHODCALLTYPE *Next)(
        IEnumTfPropertyValue *This,
        ULONG count,
        TF_PROPERTYVAL *values,
        ULONG *fetched);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        IEnumTfPropertyValue *This);

    HRESULT (STDMETHODCALLTYPE *Skip)(
        IEnumTfPropertyValue *This,
        ULONG count);

    END_INTERFACE
} IEnumTfPropertyValueVtbl;

interface IEnumTfPropertyValue {
    CONST_VTBL IEnumTfPropertyValueVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IEnumTfPropertyValue_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IEnumTfPropertyValue_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IEnumTfPropertyValue_Release(This) (This)->lpVtbl->Release(This)
/*** IEnumTfPropertyValue methods ***/
#define IEnumTfPropertyValue_Clone(This,property_value) (This)->lpVtbl->Clone(This,property_value)
#define IEnumTfPropertyValue_Next(This,count,values,fetched) (This)->lpVtbl->Next(This,count,values,fetched)
#define IEnumTfPropertyValue_Reset(This) (This)->lpVtbl->Reset(This)
#define IEnumTfPropertyValue_Skip(This,count) (This)->lpVtbl->Skip(This,count)
#else
/*** IUnknown methods ***/
static inline HRESULT IEnumTfPropertyValue_QueryInterface(IEnumTfPropertyValue* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IEnumTfPropertyValue_AddRef(IEnumTfPropertyValue* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IEnumTfPropertyValue_Release(IEnumTfPropertyValue* This) {
    return This->lpVtbl->Release(This);
}
/*** IEnumTfPropertyValue methods ***/
static inline HRESULT IEnumTfPropertyValue_Clone(IEnumTfPropertyValue* This,IEnumTfPropertyValue **property_value) {
    return This->lpVtbl->Clone(This,property_value);
}
static inline HRESULT IEnumTfPropertyValue_Next(IEnumTfPropertyValue* This,ULONG count,TF_PROPERTYVAL *values,ULONG *fetched) {
    return This->lpVtbl->Next(This,count,values,fetched);
}
static inline HRESULT IEnumTfPropertyValue_Reset(IEnumTfPropertyValue* This) {
    return This->lpVtbl->Reset(This);
}
static inline HRESULT IEnumTfPropertyValue_Skip(IEnumTfPropertyValue* This,ULONG count) {
    return This->lpVtbl->Skip(This,count);
}
#endif
#endif

#endif


#endif  /* __IEnumTfPropertyValue_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITfRangeBackup interface
 */
#ifndef __ITfRangeBackup_INTERFACE_DEFINED__
#define __ITfRangeBackup_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITfRangeBackup, 0x463a506d, 0x6992, 0x49d2, 0x9b,0x88, 0x93,0xd5,0x5e,0x70,0xbb,0x16);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("463a506d-6992-49d2-9b88-93d55e70bb16")
ITfRangeBackup : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Restore(
        TfEditCookie cookie,
        ITfRange *range) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITfRangeBackup, 0x463a506d, 0x6992, 0x49d2, 0x9b,0x88, 0x93,0xd5,0x5e,0x70,0xbb,0x16)
#endif
#else
typedef struct ITfRangeBackupVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITfRangeBackup *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITfRangeBackup *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITfRangeBackup *This);

    /*** ITfRangeBackup methods ***/
    HRESULT (STDMETHODCALLTYPE *Restore)(
        ITfRangeBackup *This,
        TfEditCookie cookie,
        ITfRange *range);

    END_INTERFACE
} ITfRangeBackupVtbl;

interface ITfRangeBackup {
    CONST_VTBL ITfRangeBackupVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITfRangeBackup_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITfRangeBackup_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITfRangeBackup_Release(This) (This)->lpVtbl->Release(This)
/*** ITfRangeBackup methods ***/
#define ITfRangeBackup_Restore(This,cookie,range) (This)->lpVtbl->Restore(This,cookie,range)
#else
/*** IUnknown methods ***/
static inline HRESULT ITfRangeBackup_QueryInterface(ITfRangeBackup* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITfRangeBackup_AddRef(ITfRangeBackup* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITfRangeBackup_Release(ITfRangeBackup* This) {
    return This->lpVtbl->Release(This);
}
/*** ITfRangeBackup methods ***/
static inline HRESULT ITfRangeBackup_Restore(ITfRangeBackup* This,TfEditCookie cookie,ITfRange *range) {
    return This->lpVtbl->Restore(This,cookie,range);
}
#endif
#endif

#endif


#endif  /* __ITfRangeBackup_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITextStoreACPServices interface
 */
#ifndef __ITextStoreACPServices_INTERFACE_DEFINED__
#define __ITextStoreACPServices_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITextStoreACPServices, 0xaa80e901, 0x2021, 0x11d2, 0x93,0xe0, 0x00,0x60,0xb0,0x67,0xb8,0x6e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("aa80e901-2021-11d2-93e0-0060b067b86e")
ITextStoreACPServices : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Serialize(
        ITfProperty *prop,
        ITfRange *range,
        TF_PERSISTENT_PROPERTY_HEADER_ACP *header,
        IStream *stream) = 0;

    virtual HRESULT STDMETHODCALLTYPE Unserialize(
        ITfProperty *prop,
        const TF_PERSISTENT_PROPERTY_HEADER_ACP *header,
        IStream *stream,
        ITfPersistentPropertyLoaderACP *loader) = 0;

    virtual HRESULT STDMETHODCALLTYPE ForceLoadProperty(
        ITfProperty *prop) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateRange(
        LONG start,
        LONG end,
        ITfRangeACP **range) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITextStoreACPServices, 0xaa80e901, 0x2021, 0x11d2, 0x93,0xe0, 0x00,0x60,0xb0,0x67,0xb8,0x6e)
#endif
#else
typedef struct ITextStoreACPServicesVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITextStoreACPServices *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITextStoreACPServices *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITextStoreACPServices *This);

    /*** ITextStoreACPServices methods ***/
    HRESULT (STDMETHODCALLTYPE *Serialize)(
        ITextStoreACPServices *This,
        ITfProperty *prop,
        ITfRange *range,
        TF_PERSISTENT_PROPERTY_HEADER_ACP *header,
        IStream *stream);

    HRESULT (STDMETHODCALLTYPE *Unserialize)(
        ITextStoreACPServices *This,
        ITfProperty *prop,
        const TF_PERSISTENT_PROPERTY_HEADER_ACP *header,
        IStream *stream,
        ITfPersistentPropertyLoaderACP *loader);

    HRESULT (STDMETHODCALLTYPE *ForceLoadProperty)(
        ITextStoreACPServices *This,
        ITfProperty *prop);

    HRESULT (STDMETHODCALLTYPE *CreateRange)(
        ITextStoreACPServices *This,
        LONG start,
        LONG end,
        ITfRangeACP **range);

    END_INTERFACE
} ITextStoreACPServicesVtbl;

interface ITextStoreACPServices {
    CONST_VTBL ITextStoreACPServicesVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITextStoreACPServices_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITextStoreACPServices_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITextStoreACPServices_Release(This) (This)->lpVtbl->Release(This)
/*** ITextStoreACPServices methods ***/
#define ITextStoreACPServices_Serialize(This,prop,range,header,stream) (This)->lpVtbl->Serialize(This,prop,range,header,stream)
#define ITextStoreACPServices_Unserialize(This,prop,header,stream,loader) (This)->lpVtbl->Unserialize(This,prop,header,stream,loader)
#define ITextStoreACPServices_ForceLoadProperty(This,prop) (This)->lpVtbl->ForceLoadProperty(This,prop)
#define ITextStoreACPServices_CreateRange(This,start,end,range) (This)->lpVtbl->CreateRange(This,start,end,range)
#else
/*** IUnknown methods ***/
static inline HRESULT ITextStoreACPServices_QueryInterface(ITextStoreACPServices* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITextStoreACPServices_AddRef(ITextStoreACPServices* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITextStoreACPServices_Release(ITextStoreACPServices* This) {
    return This->lpVtbl->Release(This);
}
/*** ITextStoreACPServices methods ***/
static inline HRESULT ITextStoreACPServices_Serialize(ITextStoreACPServices* This,ITfProperty *prop,ITfRange *range,TF_PERSISTENT_PROPERTY_HEADER_ACP *header,IStream *stream) {
    return This->lpVtbl->Serialize(This,prop,range,header,stream);
}
static inline HRESULT ITextStoreACPServices_Unserialize(ITextStoreACPServices* This,ITfProperty *prop,const TF_PERSISTENT_PROPERTY_HEADER_ACP *header,IStream *stream,ITfPersistentPropertyLoaderACP *loader) {
    return This->lpVtbl->Unserialize(This,prop,header,stream,loader);
}
static inline HRESULT ITextStoreACPServices_ForceLoadProperty(ITextStoreACPServices* This,ITfProperty *prop) {
    return This->lpVtbl->ForceLoadProperty(This,prop);
}
static inline HRESULT ITextStoreACPServices_CreateRange(ITextStoreACPServices* This,LONG start,LONG end,ITfRangeACP **range) {
    return This->lpVtbl->CreateRange(This,start,end,range);
}
#endif
#endif

#endif


#endif  /* __ITextStoreACPServices_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITfContext interface
 */
#ifndef __ITfContext_INTERFACE_DEFINED__
#define __ITfContext_INTERFACE_DEFINED__

#define TF_ES_ASYNCDONTCARE (0x0)

#define TF_ES_SYNC (0x1)

#define TF_ES_READ (0x2)

#define TF_ES_READWRITE (0x6)

#define TF_ES_ASYNC (0x8)

typedef enum __WIDL_msctf_generated_name_00000013 {
    TF_AE_NONE = 0,
    TF_AE_START = 1,
    TF_AE_END = 2
} TfActiveSelEnd;
typedef struct TF_SELECTIONSTYLE {
    TfActiveSelEnd ase;
    WINBOOL fInterimChar;
} TF_SELECTIONSTYLE;
typedef struct TF_SELECTION {
    ITfRange *range;
    TF_SELECTIONSTYLE style;
} TF_SELECTION;
#define TF_DEFAULT_SELECTION (TS_DEFAULT_SELECTION)

typedef TS_STATUS TF_STATUS;
#define TF_SD_READONLY (TS_SD_READONLY)

#define TF_SD_LOADING (TS_SD_LOADING)

#define TF_SS_DISJOINTSEL (TS_SS_DISJOINTSEL)

#define TF_SS_REGIONS (TS_SS_REGIONS)

#define TF_SS_TRANSITORY (TS_SS_TRANSITORY)

DEFINE_GUID(IID_ITfContext, 0xaa80e7fd, 0x2021, 0x11d2, 0x93,0xe0, 0x00,0x60,0xb0,0x67,0xb8,0x6e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("aa80e7fd-2021-11d2-93e0-0060b067b86e")
ITfContext : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE RequestEditSession(
        TfClientId tid,
        ITfEditSession *pes,
        DWORD dwFlags,
        HRESULT *phrSession) = 0;

    virtual HRESULT STDMETHODCALLTYPE InWriteSession(
        TfClientId tid,
        WINBOOL *pfWriteSession) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSelection(
        TfEditCookie ec,
        ULONG ulIndex,
        ULONG ulCount,
        TF_SELECTION *pSelection,
        ULONG *pcFetched) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSelection(
        TfEditCookie ec,
        ULONG ulCount,
        const TF_SELECTION *pSelection) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStart(
        TfEditCookie ec,
        ITfRange **ppStart) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetEnd(
        TfEditCookie ec,
        ITfRange **ppEnd) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetActiveView(
        ITfContextView **ppView) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumViews(
        IEnumTfContextViews **ppEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStatus(
        TF_STATUS *pdcs) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetProperty(
        REFGUID guidProp,
        ITfProperty **ppProp) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAppProperty(
        REFGUID guidProp,
        ITfReadOnlyProperty **ppProp) = 0;

    virtual HRESULT STDMETHODCALLTYPE TrackProperties(
        const GUID **prgProp,
        ULONG cProp,
        const GUID **prgAppProp,
        ULONG cAppProp,
        ITfReadOnlyProperty **ppProperty) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumProperties(
        IEnumTfProperties **ppEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDocumentMgr(
        ITfDocumentMgr **ppDm) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateRangeBackup(
        TfEditCookie ec,
        ITfRange *pRange,
        ITfRangeBackup **ppBackup) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITfContext, 0xaa80e7fd, 0x2021, 0x11d2, 0x93,0xe0, 0x00,0x60,0xb0,0x67,0xb8,0x6e)
#endif
#else
typedef struct ITfContextVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITfContext *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITfContext *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITfContext *This);

    /*** ITfContext methods ***/
    HRESULT (STDMETHODCALLTYPE *RequestEditSession)(
        ITfContext *This,
        TfClientId tid,
        ITfEditSession *pes,
        DWORD dwFlags,
        HRESULT *phrSession);

    HRESULT (STDMETHODCALLTYPE *InWriteSession)(
        ITfContext *This,
        TfClientId tid,
        WINBOOL *pfWriteSession);

    HRESULT (STDMETHODCALLTYPE *GetSelection)(
        ITfContext *This,
        TfEditCookie ec,
        ULONG ulIndex,
        ULONG ulCount,
        TF_SELECTION *pSelection,
        ULONG *pcFetched);

    HRESULT (STDMETHODCALLTYPE *SetSelection)(
        ITfContext *This,
        TfEditCookie ec,
        ULONG ulCount,
        const TF_SELECTION *pSelection);

    HRESULT (STDMETHODCALLTYPE *GetStart)(
        ITfContext *This,
        TfEditCookie ec,
        ITfRange **ppStart);

    HRESULT (STDMETHODCALLTYPE *GetEnd)(
        ITfContext *This,
        TfEditCookie ec,
        ITfRange **ppEnd);

    HRESULT (STDMETHODCALLTYPE *GetActiveView)(
        ITfContext *This,
        ITfContextView **ppView);

    HRESULT (STDMETHODCALLTYPE *EnumViews)(
        ITfContext *This,
        IEnumTfContextViews **ppEnum);

    HRESULT (STDMETHODCALLTYPE *GetStatus)(
        ITfContext *This,
        TF_STATUS *pdcs);

    HRESULT (STDMETHODCALLTYPE *GetProperty)(
        ITfContext *This,
        REFGUID guidProp,
        ITfProperty **ppProp);

    HRESULT (STDMETHODCALLTYPE *GetAppProperty)(
        ITfContext *This,
        REFGUID guidProp,
        ITfReadOnlyProperty **ppProp);

    HRESULT (STDMETHODCALLTYPE *TrackProperties)(
        ITfContext *This,
        const GUID **prgProp,
        ULONG cProp,
        const GUID **prgAppProp,
        ULONG cAppProp,
        ITfReadOnlyProperty **ppProperty);

    HRESULT (STDMETHODCALLTYPE *EnumProperties)(
        ITfContext *This,
        IEnumTfProperties **ppEnum);

    HRESULT (STDMETHODCALLTYPE *GetDocumentMgr)(
        ITfContext *This,
        ITfDocumentMgr **ppDm);

    HRESULT (STDMETHODCALLTYPE *CreateRangeBackup)(
        ITfContext *This,
        TfEditCookie ec,
        ITfRange *pRange,
        ITfRangeBackup **ppBackup);

    END_INTERFACE
} ITfContextVtbl;

interface ITfContext {
    CONST_VTBL ITfContextVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITfContext_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITfContext_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITfContext_Release(This) (This)->lpVtbl->Release(This)
/*** ITfContext methods ***/
#define ITfContext_RequestEditSession(This,tid,pes,dwFlags,phrSession) (This)->lpVtbl->RequestEditSession(This,tid,pes,dwFlags,phrSession)
#define ITfContext_InWriteSession(This,tid,pfWriteSession) (This)->lpVtbl->InWriteSession(This,tid,pfWriteSession)
#define ITfContext_GetSelection(This,ec,ulIndex,ulCount,pSelection,pcFetched) (This)->lpVtbl->GetSelection(This,ec,ulIndex,ulCount,pSelection,pcFetched)
#define ITfContext_SetSelection(This,ec,ulCount,pSelection) (This)->lpVtbl->SetSelection(This,ec,ulCount,pSelection)
#define ITfContext_GetStart(This,ec,ppStart) (This)->lpVtbl->GetStart(This,ec,ppStart)
#define ITfContext_GetEnd(This,ec,ppEnd) (This)->lpVtbl->GetEnd(This,ec,ppEnd)
#define ITfContext_GetActiveView(This,ppView) (This)->lpVtbl->GetActiveView(This,ppView)
#define ITfContext_EnumViews(This,ppEnum) (This)->lpVtbl->EnumViews(This,ppEnum)
#define ITfContext_GetStatus(This,pdcs) (This)->lpVtbl->GetStatus(This,pdcs)
#define ITfContext_GetProperty(This,guidProp,ppProp) (This)->lpVtbl->GetProperty(This,guidProp,ppProp)
#define ITfContext_GetAppProperty(This,guidProp,ppProp) (This)->lpVtbl->GetAppProperty(This,guidProp,ppProp)
#define ITfContext_TrackProperties(This,prgProp,cProp,prgAppProp,cAppProp,ppProperty) (This)->lpVtbl->TrackProperties(This,prgProp,cProp,prgAppProp,cAppProp,ppProperty)
#define ITfContext_EnumProperties(This,ppEnum) (This)->lpVtbl->EnumProperties(This,ppEnum)
#define ITfContext_GetDocumentMgr(This,ppDm) (This)->lpVtbl->GetDocumentMgr(This,ppDm)
#define ITfContext_CreateRangeBackup(This,ec,pRange,ppBackup) (This)->lpVtbl->CreateRangeBackup(This,ec,pRange,ppBackup)
#else
/*** IUnknown methods ***/
static inline HRESULT ITfContext_QueryInterface(ITfContext* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITfContext_AddRef(ITfContext* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITfContext_Release(ITfContext* This) {
    return This->lpVtbl->Release(This);
}
/*** ITfContext methods ***/
static inline HRESULT ITfContext_RequestEditSession(ITfContext* This,TfClientId tid,ITfEditSession *pes,DWORD dwFlags,HRESULT *phrSession) {
    return This->lpVtbl->RequestEditSession(This,tid,pes,dwFlags,phrSession);
}
static inline HRESULT ITfContext_InWriteSession(ITfContext* This,TfClientId tid,WINBOOL *pfWriteSession) {
    return This->lpVtbl->InWriteSession(This,tid,pfWriteSession);
}
static inline HRESULT ITfContext_GetSelection(ITfContext* This,TfEditCookie ec,ULONG ulIndex,ULONG ulCount,TF_SELECTION *pSelection,ULONG *pcFetched) {
    return This->lpVtbl->GetSelection(This,ec,ulIndex,ulCount,pSelection,pcFetched);
}
static inline HRESULT ITfContext_SetSelection(ITfContext* This,TfEditCookie ec,ULONG ulCount,const TF_SELECTION *pSelection) {
    return This->lpVtbl->SetSelection(This,ec,ulCount,pSelection);
}
static inline HRESULT ITfContext_GetStart(ITfContext* This,TfEditCookie ec,ITfRange **ppStart) {
    return This->lpVtbl->GetStart(This,ec,ppStart);
}
static inline HRESULT ITfContext_GetEnd(ITfContext* This,TfEditCookie ec,ITfRange **ppEnd) {
    return This->lpVtbl->GetEnd(This,ec,ppEnd);
}
static inline HRESULT ITfContext_GetActiveView(ITfContext* This,ITfContextView **ppView) {
    return This->lpVtbl->GetActiveView(This,ppView);
}
static inline HRESULT ITfContext_EnumViews(ITfContext* This,IEnumTfContextViews **ppEnum) {
    return This->lpVtbl->EnumViews(This,ppEnum);
}
static inline HRESULT ITfContext_GetStatus(ITfContext* This,TF_STATUS *pdcs) {
    return This->lpVtbl->GetStatus(This,pdcs);
}
static inline HRESULT ITfContext_GetProperty(ITfContext* This,REFGUID guidProp,ITfProperty **ppProp) {
    return This->lpVtbl->GetProperty(This,guidProp,ppProp);
}
static inline HRESULT ITfContext_GetAppProperty(ITfContext* This,REFGUID guidProp,ITfReadOnlyProperty **ppProp) {
    return This->lpVtbl->GetAppProperty(This,guidProp,ppProp);
}
static inline HRESULT ITfContext_TrackProperties(ITfContext* This,const GUID **prgProp,ULONG cProp,const GUID **prgAppProp,ULONG cAppProp,ITfReadOnlyProperty **ppProperty) {
    return This->lpVtbl->TrackProperties(This,prgProp,cProp,prgAppProp,cAppProp,ppProperty);
}
static inline HRESULT ITfContext_EnumProperties(ITfContext* This,IEnumTfProperties **ppEnum) {
    return This->lpVtbl->EnumProperties(This,ppEnum);
}
static inline HRESULT ITfContext_GetDocumentMgr(ITfContext* This,ITfDocumentMgr **ppDm) {
    return This->lpVtbl->GetDocumentMgr(This,ppDm);
}
static inline HRESULT ITfContext_CreateRangeBackup(ITfContext* This,TfEditCookie ec,ITfRange *pRange,ITfRangeBackup **ppBackup) {
    return This->lpVtbl->CreateRangeBackup(This,ec,pRange,ppBackup);
}
#endif
#endif

#endif


#endif  /* __ITfContext_INTERFACE_DEFINED__ */

#define TF_INVALID_COOKIE (0xffffffff)

/*****************************************************************************
 * ITfSource interface
 */
#ifndef __ITfSource_INTERFACE_DEFINED__
#define __ITfSource_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITfSource, 0x4ea48a35, 0x60ae, 0x446f, 0x8f,0xd6, 0xe6,0xa8,0xd8,0x24,0x59,0xf7);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("4ea48a35-60ae-446f-8fd6-e6a8d82459f7")
ITfSource : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE AdviseSink(
        REFIID riid,
        IUnknown *punk,
        DWORD *pdwCookie) = 0;

    virtual HRESULT STDMETHODCALLTYPE UnadviseSink(
        DWORD dwCookie) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITfSource, 0x4ea48a35, 0x60ae, 0x446f, 0x8f,0xd6, 0xe6,0xa8,0xd8,0x24,0x59,0xf7)
#endif
#else
typedef struct ITfSourceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITfSource *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITfSource *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITfSource *This);

    /*** ITfSource methods ***/
    HRESULT (STDMETHODCALLTYPE *AdviseSink)(
        ITfSource *This,
        REFIID riid,
        IUnknown *punk,
        DWORD *pdwCookie);

    HRESULT (STDMETHODCALLTYPE *UnadviseSink)(
        ITfSource *This,
        DWORD dwCookie);

    END_INTERFACE
} ITfSourceVtbl;

interface ITfSource {
    CONST_VTBL ITfSourceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITfSource_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITfSource_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITfSource_Release(This) (This)->lpVtbl->Release(This)
/*** ITfSource methods ***/
#define ITfSource_AdviseSink(This,riid,punk,pdwCookie) (This)->lpVtbl->AdviseSink(This,riid,punk,pdwCookie)
#define ITfSource_UnadviseSink(This,dwCookie) (This)->lpVtbl->UnadviseSink(This,dwCookie)
#else
/*** IUnknown methods ***/
static inline HRESULT ITfSource_QueryInterface(ITfSource* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITfSource_AddRef(ITfSource* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITfSource_Release(ITfSource* This) {
    return This->lpVtbl->Release(This);
}
/*** ITfSource methods ***/
static inline HRESULT ITfSource_AdviseSink(ITfSource* This,REFIID riid,IUnknown *punk,DWORD *pdwCookie) {
    return This->lpVtbl->AdviseSink(This,riid,punk,pdwCookie);
}
static inline HRESULT ITfSource_UnadviseSink(ITfSource* This,DWORD dwCookie) {
    return This->lpVtbl->UnadviseSink(This,dwCookie);
}
#endif
#endif

#endif


#endif  /* __ITfSource_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITfInputProcessorProfiles interface
 */
#ifndef __ITfInputProcessorProfiles_INTERFACE_DEFINED__
#define __ITfInputProcessorProfiles_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITfInputProcessorProfiles, 0x1f02b6c5, 0x7842, 0x4ee6, 0x8a,0x0b, 0x9a,0x24,0x18,0x3a,0x95,0xca);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("1f02b6c5-7842-4ee6-8a0b-9a24183a95ca")
ITfInputProcessorProfiles : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Register(
        REFCLSID rclsid) = 0;

    virtual HRESULT STDMETHODCALLTYPE Unregister(
        REFCLSID rclsid) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddLanguageProfile(
        REFCLSID rclsid,
        LANGID langid,
        REFGUID guidProfile,
        const WCHAR *pchDesc,
        ULONG cchDesc,
        const WCHAR *pchIconFile,
        ULONG cchFile,
        ULONG uIconIndex) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveLanguageProfile(
        REFCLSID rclsid,
        LANGID langid,
        REFGUID guidProfile) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumInputProcessorInfo(
        IEnumGUID **ppEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDefaultLanguageProfile(
        LANGID langid,
        REFGUID catid,
        CLSID *pclsid,
        GUID *pguidProfile) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDefaultLanguageProfile(
        LANGID langid,
        REFCLSID rclsid,
        REFGUID guidProfiles) = 0;

    virtual HRESULT STDMETHODCALLTYPE ActivateLanguageProfile(
        REFCLSID rclsid,
        LANGID langid,
        REFGUID guidProfiles) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetActiveLanguageProfile(
        REFCLSID rclsid,
        LANGID *plangid,
        GUID *pguidProfile) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetLanguageProfileDescription(
        REFCLSID rclsid,
        LANGID langid,
        REFGUID guidProfile,
        BSTR *pbstrProfile) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCurrentLanguage(
        LANGID *plangid) = 0;

    virtual HRESULT STDMETHODCALLTYPE ChangeCurrentLanguage(
        LANGID langid) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetLanguageList(
        LANGID **ppLangId,
        ULONG *pulCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumLanguageProfiles(
        LANGID langid,
        IEnumTfLanguageProfiles **ppEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnableLanguageProfile(
        REFCLSID rclsid,
        LANGID langid,
        REFGUID guidProfile,
        WINBOOL fEnable) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsEnabledLanguageProfile(
        REFCLSID rclsid,
        LANGID langid,
        REFGUID guidProfile,
        WINBOOL *pfEnable) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnableLanguageProfileByDefault(
        REFCLSID rclsid,
        LANGID langid,
        REFGUID guidProfile,
        WINBOOL fEnable) = 0;

    virtual HRESULT STDMETHODCALLTYPE SubstituteKeyboardLayout(
        REFCLSID rclsid,
        LANGID langid,
        REFGUID guidProfile,
        HKL hKL) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITfInputProcessorProfiles, 0x1f02b6c5, 0x7842, 0x4ee6, 0x8a,0x0b, 0x9a,0x24,0x18,0x3a,0x95,0xca)
#endif
#else
typedef struct ITfInputProcessorProfilesVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITfInputProcessorProfiles *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITfInputProcessorProfiles *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITfInputProcessorProfiles *This);

    /*** ITfInputProcessorProfiles methods ***/
    HRESULT (STDMETHODCALLTYPE *Register)(
        ITfInputProcessorProfiles *This,
        REFCLSID rclsid);

    HRESULT (STDMETHODCALLTYPE *Unregister)(
        ITfInputProcessorProfiles *This,
        REFCLSID rclsid);

    HRESULT (STDMETHODCALLTYPE *AddLanguageProfile)(
        ITfInputProcessorProfiles *This,
        REFCLSID rclsid,
        LANGID langid,
        REFGUID guidProfile,
        const WCHAR *pchDesc,
        ULONG cchDesc,
        const WCHAR *pchIconFile,
        ULONG cchFile,
        ULONG uIconIndex);

    HRESULT (STDMETHODCALLTYPE *RemoveLanguageProfile)(
        ITfInputProcessorProfiles *This,
        REFCLSID rclsid,
        LANGID langid,
        REFGUID guidProfile);

    HRESULT (STDMETHODCALLTYPE *EnumInputProcessorInfo)(
        ITfInputProcessorProfiles *This,
        IEnumGUID **ppEnum);

    HRESULT (STDMETHODCALLTYPE *GetDefaultLanguageProfile)(
        ITfInputProcessorProfiles *This,
        LANGID langid,
        REFGUID catid,
        CLSID *pclsid,
        GUID *pguidProfile);

    HRESULT (STDMETHODCALLTYPE *SetDefaultLanguageProfile)(
        ITfInputProcessorProfiles *This,
        LANGID langid,
        REFCLSID rclsid,
        REFGUID guidProfiles);

    HRESULT (STDMETHODCALLTYPE *ActivateLanguageProfile)(
        ITfInputProcessorProfiles *This,
        REFCLSID rclsid,
        LANGID langid,
        REFGUID guidProfiles);

    HRESULT (STDMETHODCALLTYPE *GetActiveLanguageProfile)(
        ITfInputProcessorProfiles *This,
        REFCLSID rclsid,
        LANGID *plangid,
        GUID *pguidProfile);

    HRESULT (STDMETHODCALLTYPE *GetLanguageProfileDescription)(
        ITfInputProcessorProfiles *This,
        REFCLSID rclsid,
        LANGID langid,
        REFGUID guidProfile,
        BSTR *pbstrProfile);

    HRESULT (STDMETHODCALLTYPE *GetCurrentLanguage)(
        ITfInputProcessorProfiles *This,
        LANGID *plangid);

    HRESULT (STDMETHODCALLTYPE *ChangeCurrentLanguage)(
        ITfInputProcessorProfiles *This,
        LANGID langid);

    HRESULT (STDMETHODCALLTYPE *GetLanguageList)(
        ITfInputProcessorProfiles *This,
        LANGID **ppLangId,
        ULONG *pulCount);

    HRESULT (STDMETHODCALLTYPE *EnumLanguageProfiles)(
        ITfInputProcessorProfiles *This,
        LANGID langid,
        IEnumTfLanguageProfiles **ppEnum);

    HRESULT (STDMETHODCALLTYPE *EnableLanguageProfile)(
        ITfInputProcessorProfiles *This,
        REFCLSID rclsid,
        LANGID langid,
        REFGUID guidProfile,
        WINBOOL fEnable);

    HRESULT (STDMETHODCALLTYPE *IsEnabledLanguageProfile)(
        ITfInputProcessorProfiles *This,
        REFCLSID rclsid,
        LANGID langid,
        REFGUID guidProfile,
        WINBOOL *pfEnable);

    HRESULT (STDMETHODCALLTYPE *EnableLanguageProfileByDefault)(
        ITfInputProcessorProfiles *This,
        REFCLSID rclsid,
        LANGID langid,
        REFGUID guidProfile,
        WINBOOL fEnable);

    HRESULT (STDMETHODCALLTYPE *SubstituteKeyboardLayout)(
        ITfInputProcessorProfiles *This,
        REFCLSID rclsid,
        LANGID langid,
        REFGUID guidProfile,
        HKL hKL);

    END_INTERFACE
} ITfInputProcessorProfilesVtbl;

interface ITfInputProcessorProfiles {
    CONST_VTBL ITfInputProcessorProfilesVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITfInputProcessorProfiles_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITfInputProcessorProfiles_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITfInputProcessorProfiles_Release(This) (This)->lpVtbl->Release(This)
/*** ITfInputProcessorProfiles methods ***/
#define ITfInputProcessorProfiles_Register(This,rclsid) (This)->lpVtbl->Register(This,rclsid)
#define ITfInputProcessorProfiles_Unregister(This,rclsid) (This)->lpVtbl->Unregister(This,rclsid)
#define ITfInputProcessorProfiles_AddLanguageProfile(This,rclsid,langid,guidProfile,pchDesc,cchDesc,pchIconFile,cchFile,uIconIndex) (This)->lpVtbl->AddLanguageProfile(This,rclsid,langid,guidProfile,pchDesc,cchDesc,pchIconFile,cchFile,uIconIndex)
#define ITfInputProcessorProfiles_RemoveLanguageProfile(This,rclsid,langid,guidProfile) (This)->lpVtbl->RemoveLanguageProfile(This,rclsid,langid,guidProfile)
#define ITfInputProcessorProfiles_EnumInputProcessorInfo(This,ppEnum) (This)->lpVtbl->EnumInputProcessorInfo(This,ppEnum)
#define ITfInputProcessorProfiles_GetDefaultLanguageProfile(This,langid,catid,pclsid,pguidProfile) (This)->lpVtbl->GetDefaultLanguageProfile(This,langid,catid,pclsid,pguidProfile)
#define ITfInputProcessorProfiles_SetDefaultLanguageProfile(This,langid,rclsid,guidProfiles) (This)->lpVtbl->SetDefaultLanguageProfile(This,langid,rclsid,guidProfiles)
#define ITfInputProcessorProfiles_ActivateLanguageProfile(This,rclsid,langid,guidProfiles) (This)->lpVtbl->ActivateLanguageProfile(This,rclsid,langid,guidProfiles)
#define ITfInputProcessorProfiles_GetActiveLanguageProfile(This,rclsid,plangid,pguidProfile) (This)->lpVtbl->GetActiveLanguageProfile(This,rclsid,plangid,pguidProfile)
#define ITfInputProcessorProfiles_GetLanguageProfileDescription(This,rclsid,langid,guidProfile,pbstrProfile) (This)->lpVtbl->GetLanguageProfileDescription(This,rclsid,langid,guidProfile,pbstrProfile)
#define ITfInputProcessorProfiles_GetCurrentLanguage(This,plangid) (This)->lpVtbl->GetCurrentLanguage(This,plangid)
#define ITfInputProcessorProfiles_ChangeCurrentLanguage(This,langid) (This)->lpVtbl->ChangeCurrentLanguage(This,langid)
#define ITfInputProcessorProfiles_GetLanguageList(This,ppLangId,pulCount) (This)->lpVtbl->GetLanguageList(This,ppLangId,pulCount)
#define ITfInputProcessorProfiles_EnumLanguageProfiles(This,langid,ppEnum) (This)->lpVtbl->EnumLanguageProfiles(This,langid,ppEnum)
#define ITfInputProcessorProfiles_EnableLanguageProfile(This,rclsid,langid,guidProfile,fEnable) (This)->lpVtbl->EnableLanguageProfile(This,rclsid,langid,guidProfile,fEnable)
#define ITfInputProcessorProfiles_IsEnabledLanguageProfile(This,rclsid,langid,guidProfile,pfEnable) (This)->lpVtbl->IsEnabledLanguageProfile(This,rclsid,langid,guidProfile,pfEnable)
#define ITfInputProcessorProfiles_EnableLanguageProfileByDefault(This,rclsid,langid,guidProfile,fEnable) (This)->lpVtbl->EnableLanguageProfileByDefault(This,rclsid,langid,guidProfile,fEnable)
#define ITfInputProcessorProfiles_SubstituteKeyboardLayout(This,rclsid,langid,guidProfile,hKL) (This)->lpVtbl->SubstituteKeyboardLayout(This,rclsid,langid,guidProfile,hKL)
#else
/*** IUnknown methods ***/
static inline HRESULT ITfInputProcessorProfiles_QueryInterface(ITfInputProcessorProfiles* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITfInputProcessorProfiles_AddRef(ITfInputProcessorProfiles* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITfInputProcessorProfiles_Release(ITfInputProcessorProfiles* This) {
    return This->lpVtbl->Release(This);
}
/*** ITfInputProcessorProfiles methods ***/
static inline HRESULT ITfInputProcessorProfiles_Register(ITfInputProcessorProfiles* This,REFCLSID rclsid) {
    return This->lpVtbl->Register(This,rclsid);
}
static inline HRESULT ITfInputProcessorProfiles_Unregister(ITfInputProcessorProfiles* This,REFCLSID rclsid) {
    return This->lpVtbl->Unregister(This,rclsid);
}
static inline HRESULT ITfInputProcessorProfiles_AddLanguageProfile(ITfInputProcessorProfiles* This,REFCLSID rclsid,LANGID langid,REFGUID guidProfile,const WCHAR *pchDesc,ULONG cchDesc,const WCHAR *pchIconFile,ULONG cchFile,ULONG uIconIndex) {
    return This->lpVtbl->AddLanguageProfile(This,rclsid,langid,guidProfile,pchDesc,cchDesc,pchIconFile,cchFile,uIconIndex);
}
static inline HRESULT ITfInputProcessorProfiles_RemoveLanguageProfile(ITfInputProcessorProfiles* This,REFCLSID rclsid,LANGID langid,REFGUID guidProfile) {
    return This->lpVtbl->RemoveLanguageProfile(This,rclsid,langid,guidProfile);
}
static inline HRESULT ITfInputProcessorProfiles_EnumInputProcessorInfo(ITfInputProcessorProfiles* This,IEnumGUID **ppEnum) {
    return This->lpVtbl->EnumInputProcessorInfo(This,ppEnum);
}
static inline HRESULT ITfInputProcessorProfiles_GetDefaultLanguageProfile(ITfInputProcessorProfiles* This,LANGID langid,REFGUID catid,CLSID *pclsid,GUID *pguidProfile) {
    return This->lpVtbl->GetDefaultLanguageProfile(This,langid,catid,pclsid,pguidProfile);
}
static inline HRESULT ITfInputProcessorProfiles_SetDefaultLanguageProfile(ITfInputProcessorProfiles* This,LANGID langid,REFCLSID rclsid,REFGUID guidProfiles) {
    return This->lpVtbl->SetDefaultLanguageProfile(This,langid,rclsid,guidProfiles);
}
static inline HRESULT ITfInputProcessorProfiles_ActivateLanguageProfile(ITfInputProcessorProfiles* This,REFCLSID rclsid,LANGID langid,REFGUID guidProfiles) {
    return This->lpVtbl->ActivateLanguageProfile(This,rclsid,langid,guidProfiles);
}
static inline HRESULT ITfInputProcessorProfiles_GetActiveLanguageProfile(ITfInputProcessorProfiles* This,REFCLSID rclsid,LANGID *plangid,GUID *pguidProfile) {
    return This->lpVtbl->GetActiveLanguageProfile(This,rclsid,plangid,pguidProfile);
}
static inline HRESULT ITfInputProcessorProfiles_GetLanguageProfileDescription(ITfInputProcessorProfiles* This,REFCLSID rclsid,LANGID langid,REFGUID guidProfile,BSTR *pbstrProfile) {
    return This->lpVtbl->GetLanguageProfileDescription(This,rclsid,langid,guidProfile,pbstrProfile);
}
static inline HRESULT ITfInputProcessorProfiles_GetCurrentLanguage(ITfInputProcessorProfiles* This,LANGID *plangid) {
    return This->lpVtbl->GetCurrentLanguage(This,plangid);
}
static inline HRESULT ITfInputProcessorProfiles_ChangeCurrentLanguage(ITfInputProcessorProfiles* This,LANGID langid) {
    return This->lpVtbl->ChangeCurrentLanguage(This,langid);
}
static inline HRESULT ITfInputProcessorProfiles_GetLanguageList(ITfInputProcessorProfiles* This,LANGID **ppLangId,ULONG *pulCount) {
    return This->lpVtbl->GetLanguageList(This,ppLangId,pulCount);
}
static inline HRESULT ITfInputProcessorProfiles_EnumLanguageProfiles(ITfInputProcessorProfiles* This,LANGID langid,IEnumTfLanguageProfiles **ppEnum) {
    return This->lpVtbl->EnumLanguageProfiles(This,langid,ppEnum);
}
static inline HRESULT ITfInputProcessorProfiles_EnableLanguageProfile(ITfInputProcessorProfiles* This,REFCLSID rclsid,LANGID langid,REFGUID guidProfile,WINBOOL fEnable) {
    return This->lpVtbl->EnableLanguageProfile(This,rclsid,langid,guidProfile,fEnable);
}
static inline HRESULT ITfInputProcessorProfiles_IsEnabledLanguageProfile(ITfInputProcessorProfiles* This,REFCLSID rclsid,LANGID langid,REFGUID guidProfile,WINBOOL *pfEnable) {
    return This->lpVtbl->IsEnabledLanguageProfile(This,rclsid,langid,guidProfile,pfEnable);
}
static inline HRESULT ITfInputProcessorProfiles_EnableLanguageProfileByDefault(ITfInputProcessorProfiles* This,REFCLSID rclsid,LANGID langid,REFGUID guidProfile,WINBOOL fEnable) {
    return This->lpVtbl->EnableLanguageProfileByDefault(This,rclsid,langid,guidProfile,fEnable);
}
static inline HRESULT ITfInputProcessorProfiles_SubstituteKeyboardLayout(ITfInputProcessorProfiles* This,REFCLSID rclsid,LANGID langid,REFGUID guidProfile,HKL hKL) {
    return This->lpVtbl->SubstituteKeyboardLayout(This,rclsid,langid,guidProfile,hKL);
}
#endif
#endif

#endif


#endif  /* __ITfInputProcessorProfiles_INTERFACE_DEFINED__ */

typedef struct TF_INPUTPROCESSORPROFILE {
    DWORD dwProfileType;
    LANGID langid;
    CLSID clsid;
    GUID guidProfile;
    GUID catid;
    HKL hklSubstitute;
    DWORD dwCaps;
    HKL hkl;
    DWORD dwFlags;
} TF_INPUTPROCESSORPROFILE;
/*****************************************************************************
 * IEnumTfInputProcessorProfiles interface
 */
#ifndef __IEnumTfInputProcessorProfiles_INTERFACE_DEFINED__
#define __IEnumTfInputProcessorProfiles_INTERFACE_DEFINED__

DEFINE_GUID(IID_IEnumTfInputProcessorProfiles, 0x71c6e74d, 0x0f28, 0x11d8, 0xa8,0x2a, 0x00,0x06,0x5b,0x84,0x43,0x5c);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("71c6e74d-0f28-11d8-a82a-00065b84435c")
IEnumTfInputProcessorProfiles : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Clone(
        IEnumTfInputProcessorProfiles **ppEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE Next(
        ULONG ulCount,
        TF_INPUTPROCESSORPROFILE *pProfile,
        ULONG *pcFetch) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reset(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Skip(
        ULONG ulCount) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IEnumTfInputProcessorProfiles, 0x71c6e74d, 0x0f28, 0x11d8, 0xa8,0x2a, 0x00,0x06,0x5b,0x84,0x43,0x5c)
#endif
#else
typedef struct IEnumTfInputProcessorProfilesVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IEnumTfInputProcessorProfiles *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IEnumTfInputProcessorProfiles *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IEnumTfInputProcessorProfiles *This);

    /*** IEnumTfInputProcessorProfiles methods ***/
    HRESULT (STDMETHODCALLTYPE *Clone)(
        IEnumTfInputProcessorProfiles *This,
        IEnumTfInputProcessorProfiles **ppEnum);

    HRESULT (STDMETHODCALLTYPE *Next)(
        IEnumTfInputProcessorProfiles *This,
        ULONG ulCount,
        TF_INPUTPROCESSORPROFILE *pProfile,
        ULONG *pcFetch);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        IEnumTfInputProcessorProfiles *This);

    HRESULT (STDMETHODCALLTYPE *Skip)(
        IEnumTfInputProcessorProfiles *This,
        ULONG ulCount);

    END_INTERFACE
} IEnumTfInputProcessorProfilesVtbl;

interface IEnumTfInputProcessorProfiles {
    CONST_VTBL IEnumTfInputProcessorProfilesVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IEnumTfInputProcessorProfiles_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IEnumTfInputProcessorProfiles_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IEnumTfInputProcessorProfiles_Release(This) (This)->lpVtbl->Release(This)
/*** IEnumTfInputProcessorProfiles methods ***/
#define IEnumTfInputProcessorProfiles_Clone(This,ppEnum) (This)->lpVtbl->Clone(This,ppEnum)
#define IEnumTfInputProcessorProfiles_Next(This,ulCount,pProfile,pcFetch) (This)->lpVtbl->Next(This,ulCount,pProfile,pcFetch)
#define IEnumTfInputProcessorProfiles_Reset(This) (This)->lpVtbl->Reset(This)
#define IEnumTfInputProcessorProfiles_Skip(This,ulCount) (This)->lpVtbl->Skip(This,ulCount)
#else
/*** IUnknown methods ***/
static inline HRESULT IEnumTfInputProcessorProfiles_QueryInterface(IEnumTfInputProcessorProfiles* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IEnumTfInputProcessorProfiles_AddRef(IEnumTfInputProcessorProfiles* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IEnumTfInputProcessorProfiles_Release(IEnumTfInputProcessorProfiles* This) {
    return This->lpVtbl->Release(This);
}
/*** IEnumTfInputProcessorProfiles methods ***/
static inline HRESULT IEnumTfInputProcessorProfiles_Clone(IEnumTfInputProcessorProfiles* This,IEnumTfInputProcessorProfiles **ppEnum) {
    return This->lpVtbl->Clone(This,ppEnum);
}
static inline HRESULT IEnumTfInputProcessorProfiles_Next(IEnumTfInputProcessorProfiles* This,ULONG ulCount,TF_INPUTPROCESSORPROFILE *pProfile,ULONG *pcFetch) {
    return This->lpVtbl->Next(This,ulCount,pProfile,pcFetch);
}
static inline HRESULT IEnumTfInputProcessorProfiles_Reset(IEnumTfInputProcessorProfiles* This) {
    return This->lpVtbl->Reset(This);
}
static inline HRESULT IEnumTfInputProcessorProfiles_Skip(IEnumTfInputProcessorProfiles* This,ULONG ulCount) {
    return This->lpVtbl->Skip(This,ulCount);
}
#endif
#endif

#endif


#endif  /* __IEnumTfInputProcessorProfiles_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITfInputProcessorProfileMgr interface
 */
#ifndef __ITfInputProcessorProfileMgr_INTERFACE_DEFINED__
#define __ITfInputProcessorProfileMgr_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITfInputProcessorProfileMgr, 0x71c6e74c, 0x0f28, 0x11d8, 0xa8,0x2a, 0x00,0x06,0x5b,0x84,0x43,0x5c);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("71c6e74c-0f28-11d8-a82a-00065b84435c")
ITfInputProcessorProfileMgr : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE ActivateProfile(
        DWORD dwProfileType,
        LANGID langid,
        REFCLSID clsid,
        REFGUID guidProfile,
        HKL hkl,
        DWORD dwFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE DeactivateProfile(
        DWORD dwProfileType,
        LANGID langid,
        REFCLSID clsid,
        REFGUID guidProfile,
        HKL hkl,
        DWORD dwFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetProfile(
        DWORD dwProfileType,
        LANGID langid,
        REFCLSID clsid,
        REFGUID guidProfile,
        HKL hkl,
        TF_INPUTPROCESSORPROFILE *pProfile) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumProfiles(
        LANGID langid,
        IEnumTfInputProcessorProfiles **ppEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE ReleaseInputProcessor(
        REFCLSID rclsid,
        DWORD dwFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE RegisterProfile(
        REFCLSID rclsid,
        LANGID langid,
        REFGUID guidProfile,
        const WCHAR *pchDesc,
        ULONG cchDesc,
        const WCHAR *pchIconFile,
        ULONG cchFile,
        ULONG uIconIndex,
        HKL hklsubstitute,
        DWORD dwPreferredLayout,
        WINBOOL bEnabledByDefault,
        DWORD dwFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE UnregisterProfile(
        REFCLSID rclsid,
        LANGID langid,
        REFGUID guidProfile,
        DWORD dwFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetActiveProfile(
        REFGUID catid,
        TF_INPUTPROCESSORPROFILE *pProfile) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITfInputProcessorProfileMgr, 0x71c6e74c, 0x0f28, 0x11d8, 0xa8,0x2a, 0x00,0x06,0x5b,0x84,0x43,0x5c)
#endif
#else
typedef struct ITfInputProcessorProfileMgrVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITfInputProcessorProfileMgr *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITfInputProcessorProfileMgr *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITfInputProcessorProfileMgr *This);

    /*** ITfInputProcessorProfileMgr methods ***/
    HRESULT (STDMETHODCALLTYPE *ActivateProfile)(
        ITfInputProcessorProfileMgr *This,
        DWORD dwProfileType,
        LANGID langid,
        REFCLSID clsid,
        REFGUID guidProfile,
        HKL hkl,
        DWORD dwFlags);

    HRESULT (STDMETHODCALLTYPE *DeactivateProfile)(
        ITfInputProcessorProfileMgr *This,
        DWORD dwProfileType,
        LANGID langid,
        REFCLSID clsid,
        REFGUID guidProfile,
        HKL hkl,
        DWORD dwFlags);

    HRESULT (STDMETHODCALLTYPE *GetProfile)(
        ITfInputProcessorProfileMgr *This,
        DWORD dwProfileType,
        LANGID langid,
        REFCLSID clsid,
        REFGUID guidProfile,
        HKL hkl,
        TF_INPUTPROCESSORPROFILE *pProfile);

    HRESULT (STDMETHODCALLTYPE *EnumProfiles)(
        ITfInputProcessorProfileMgr *This,
        LANGID langid,
        IEnumTfInputProcessorProfiles **ppEnum);

    HRESULT (STDMETHODCALLTYPE *ReleaseInputProcessor)(
        ITfInputProcessorProfileMgr *This,
        REFCLSID rclsid,
        DWORD dwFlags);

    HRESULT (STDMETHODCALLTYPE *RegisterProfile)(
        ITfInputProcessorProfileMgr *This,
        REFCLSID rclsid,
        LANGID langid,
        REFGUID guidProfile,
        const WCHAR *pchDesc,
        ULONG cchDesc,
        const WCHAR *pchIconFile,
        ULONG cchFile,
        ULONG uIconIndex,
        HKL hklsubstitute,
        DWORD dwPreferredLayout,
        WINBOOL bEnabledByDefault,
        DWORD dwFlags);

    HRESULT (STDMETHODCALLTYPE *UnregisterProfile)(
        ITfInputProcessorProfileMgr *This,
        REFCLSID rclsid,
        LANGID langid,
        REFGUID guidProfile,
        DWORD dwFlags);

    HRESULT (STDMETHODCALLTYPE *GetActiveProfile)(
        ITfInputProcessorProfileMgr *This,
        REFGUID catid,
        TF_INPUTPROCESSORPROFILE *pProfile);

    END_INTERFACE
} ITfInputProcessorProfileMgrVtbl;

interface ITfInputProcessorProfileMgr {
    CONST_VTBL ITfInputProcessorProfileMgrVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITfInputProcessorProfileMgr_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITfInputProcessorProfileMgr_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITfInputProcessorProfileMgr_Release(This) (This)->lpVtbl->Release(This)
/*** ITfInputProcessorProfileMgr methods ***/
#define ITfInputProcessorProfileMgr_ActivateProfile(This,dwProfileType,langid,clsid,guidProfile,hkl,dwFlags) (This)->lpVtbl->ActivateProfile(This,dwProfileType,langid,clsid,guidProfile,hkl,dwFlags)
#define ITfInputProcessorProfileMgr_DeactivateProfile(This,dwProfileType,langid,clsid,guidProfile,hkl,dwFlags) (This)->lpVtbl->DeactivateProfile(This,dwProfileType,langid,clsid,guidProfile,hkl,dwFlags)
#define ITfInputProcessorProfileMgr_GetProfile(This,dwProfileType,langid,clsid,guidProfile,hkl,pProfile) (This)->lpVtbl->GetProfile(This,dwProfileType,langid,clsid,guidProfile,hkl,pProfile)
#define ITfInputProcessorProfileMgr_EnumProfiles(This,langid,ppEnum) (This)->lpVtbl->EnumProfiles(This,langid,ppEnum)
#define ITfInputProcessorProfileMgr_ReleaseInputProcessor(This,rclsid,dwFlags) (This)->lpVtbl->ReleaseInputProcessor(This,rclsid,dwFlags)
#define ITfInputProcessorProfileMgr_RegisterProfile(This,rclsid,langid,guidProfile,pchDesc,cchDesc,pchIconFile,cchFile,uIconIndex,hklsubstitute,dwPreferredLayout,bEnabledByDefault,dwFlags) (This)->lpVtbl->RegisterProfile(This,rclsid,langid,guidProfile,pchDesc,cchDesc,pchIconFile,cchFile,uIconIndex,hklsubstitute,dwPreferredLayout,bEnabledByDefault,dwFlags)
#define ITfInputProcessorProfileMgr_UnregisterProfile(This,rclsid,langid,guidProfile,dwFlags) (This)->lpVtbl->UnregisterProfile(This,rclsid,langid,guidProfile,dwFlags)
#define ITfInputProcessorProfileMgr_GetActiveProfile(This,catid,pProfile) (This)->lpVtbl->GetActiveProfile(This,catid,pProfile)
#else
/*** IUnknown methods ***/
static inline HRESULT ITfInputProcessorProfileMgr_QueryInterface(ITfInputProcessorProfileMgr* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITfInputProcessorProfileMgr_AddRef(ITfInputProcessorProfileMgr* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITfInputProcessorProfileMgr_Release(ITfInputProcessorProfileMgr* This) {
    return This->lpVtbl->Release(This);
}
/*** ITfInputProcessorProfileMgr methods ***/
static inline HRESULT ITfInputProcessorProfileMgr_ActivateProfile(ITfInputProcessorProfileMgr* This,DWORD dwProfileType,LANGID langid,REFCLSID clsid,REFGUID guidProfile,HKL hkl,DWORD dwFlags) {
    return This->lpVtbl->ActivateProfile(This,dwProfileType,langid,clsid,guidProfile,hkl,dwFlags);
}
static inline HRESULT ITfInputProcessorProfileMgr_DeactivateProfile(ITfInputProcessorProfileMgr* This,DWORD dwProfileType,LANGID langid,REFCLSID clsid,REFGUID guidProfile,HKL hkl,DWORD dwFlags) {
    return This->lpVtbl->DeactivateProfile(This,dwProfileType,langid,clsid,guidProfile,hkl,dwFlags);
}
static inline HRESULT ITfInputProcessorProfileMgr_GetProfile(ITfInputProcessorProfileMgr* This,DWORD dwProfileType,LANGID langid,REFCLSID clsid,REFGUID guidProfile,HKL hkl,TF_INPUTPROCESSORPROFILE *pProfile) {
    return This->lpVtbl->GetProfile(This,dwProfileType,langid,clsid,guidProfile,hkl,pProfile);
}
static inline HRESULT ITfInputProcessorProfileMgr_EnumProfiles(ITfInputProcessorProfileMgr* This,LANGID langid,IEnumTfInputProcessorProfiles **ppEnum) {
    return This->lpVtbl->EnumProfiles(This,langid,ppEnum);
}
static inline HRESULT ITfInputProcessorProfileMgr_ReleaseInputProcessor(ITfInputProcessorProfileMgr* This,REFCLSID rclsid,DWORD dwFlags) {
    return This->lpVtbl->ReleaseInputProcessor(This,rclsid,dwFlags);
}
static inline HRESULT ITfInputProcessorProfileMgr_RegisterProfile(ITfInputProcessorProfileMgr* This,REFCLSID rclsid,LANGID langid,REFGUID guidProfile,const WCHAR *pchDesc,ULONG cchDesc,const WCHAR *pchIconFile,ULONG cchFile,ULONG uIconIndex,HKL hklsubstitute,DWORD dwPreferredLayout,WINBOOL bEnabledByDefault,DWORD dwFlags) {
    return This->lpVtbl->RegisterProfile(This,rclsid,langid,guidProfile,pchDesc,cchDesc,pchIconFile,cchFile,uIconIndex,hklsubstitute,dwPreferredLayout,bEnabledByDefault,dwFlags);
}
static inline HRESULT ITfInputProcessorProfileMgr_UnregisterProfile(ITfInputProcessorProfileMgr* This,REFCLSID rclsid,LANGID langid,REFGUID guidProfile,DWORD dwFlags) {
    return This->lpVtbl->UnregisterProfile(This,rclsid,langid,guidProfile,dwFlags);
}
static inline HRESULT ITfInputProcessorProfileMgr_GetActiveProfile(ITfInputProcessorProfileMgr* This,REFGUID catid,TF_INPUTPROCESSORPROFILE *pProfile) {
    return This->lpVtbl->GetActiveProfile(This,catid,pProfile);
}
#endif
#endif

#endif


#endif  /* __ITfInputProcessorProfileMgr_INTERFACE_DEFINED__ */

typedef enum __WIDL_msctf_generated_name_00000014 {
    TF_LS_NONE = 0,
    TF_LS_SOLID = 1,
    TF_LS_DOT = 2,
    TF_LS_DASH = 3,
    TF_LS_SQUIGGLE = 4
} TF_DA_LINESTYLE;
typedef enum __WIDL_msctf_generated_name_00000015 {
    TF_CT_NONE = 0,
    TF_CT_SYSCOLOR = 1,
    TF_CT_COLORREF = 2
} TF_DA_COLORTYPE;
typedef struct TF_DA_COLOR {
    TF_DA_COLORTYPE type;
    __C89_NAMELESS union {
        int nIndex;
        COLORREF cr;
    } __C89_NAMELESSUNIONNAME;
} TF_DA_COLOR;
typedef enum __WIDL_msctf_generated_name_00000016 {
    TF_ATTR_INPUT = 0,
    TF_ATTR_TARGET_CONVERTED = 1,
    TF_ATTR_CONVERTED = 2,
    TF_ATTR_TARGET_NOTCONVERTED = 3,
    TF_ATTR_INPUT_ERROR = 4,
    TF_ATTR_FIXEDCONVERTED = 5,
    TF_ATTR_OTHER = -1
} TF_DA_ATTR_INFO;
typedef struct TF_DISPLAYATTRIBUTE {
    TF_DA_COLOR crText;
    TF_DA_COLOR crBk;
    TF_DA_LINESTYLE lsStyle;
    WINBOOL fBoldLine;
    TF_DA_COLOR crLine;
    TF_DA_ATTR_INFO bAttr;
} TF_DISPLAYATTRIBUTE;
/*****************************************************************************
 * ITfDisplayAttributeInfo interface
 */
#ifndef __ITfDisplayAttributeInfo_INTERFACE_DEFINED__
#define __ITfDisplayAttributeInfo_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITfDisplayAttributeInfo, 0x70528852, 0x2f26, 0x4aea, 0x8c,0x96, 0x21,0x51,0x50,0x57,0x89,0x32);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("70528852-2f26-4aea-8c96-************")
ITfDisplayAttributeInfo : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetGUID(
        GUID *pguid) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDescription(
        BSTR *pbstrDesc) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAttributeInfo(
        TF_DISPLAYATTRIBUTE *pda) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetAttributeInfo(
        const TF_DISPLAYATTRIBUTE *pda) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reset(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITfDisplayAttributeInfo, 0x70528852, 0x2f26, 0x4aea, 0x8c,0x96, 0x21,0x51,0x50,0x57,0x89,0x32)
#endif
#else
typedef struct ITfDisplayAttributeInfoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITfDisplayAttributeInfo *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITfDisplayAttributeInfo *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITfDisplayAttributeInfo *This);

    /*** ITfDisplayAttributeInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *GetGUID)(
        ITfDisplayAttributeInfo *This,
        GUID *pguid);

    HRESULT (STDMETHODCALLTYPE *GetDescription)(
        ITfDisplayAttributeInfo *This,
        BSTR *pbstrDesc);

    HRESULT (STDMETHODCALLTYPE *GetAttributeInfo)(
        ITfDisplayAttributeInfo *This,
        TF_DISPLAYATTRIBUTE *pda);

    HRESULT (STDMETHODCALLTYPE *SetAttributeInfo)(
        ITfDisplayAttributeInfo *This,
        const TF_DISPLAYATTRIBUTE *pda);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        ITfDisplayAttributeInfo *This);

    END_INTERFACE
} ITfDisplayAttributeInfoVtbl;

interface ITfDisplayAttributeInfo {
    CONST_VTBL ITfDisplayAttributeInfoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITfDisplayAttributeInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITfDisplayAttributeInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITfDisplayAttributeInfo_Release(This) (This)->lpVtbl->Release(This)
/*** ITfDisplayAttributeInfo methods ***/
#define ITfDisplayAttributeInfo_GetGUID(This,pguid) (This)->lpVtbl->GetGUID(This,pguid)
#define ITfDisplayAttributeInfo_GetDescription(This,pbstrDesc) (This)->lpVtbl->GetDescription(This,pbstrDesc)
#define ITfDisplayAttributeInfo_GetAttributeInfo(This,pda) (This)->lpVtbl->GetAttributeInfo(This,pda)
#define ITfDisplayAttributeInfo_SetAttributeInfo(This,pda) (This)->lpVtbl->SetAttributeInfo(This,pda)
#define ITfDisplayAttributeInfo_Reset(This) (This)->lpVtbl->Reset(This)
#else
/*** IUnknown methods ***/
static inline HRESULT ITfDisplayAttributeInfo_QueryInterface(ITfDisplayAttributeInfo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITfDisplayAttributeInfo_AddRef(ITfDisplayAttributeInfo* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITfDisplayAttributeInfo_Release(ITfDisplayAttributeInfo* This) {
    return This->lpVtbl->Release(This);
}
/*** ITfDisplayAttributeInfo methods ***/
static inline HRESULT ITfDisplayAttributeInfo_GetGUID(ITfDisplayAttributeInfo* This,GUID *pguid) {
    return This->lpVtbl->GetGUID(This,pguid);
}
static inline HRESULT ITfDisplayAttributeInfo_GetDescription(ITfDisplayAttributeInfo* This,BSTR *pbstrDesc) {
    return This->lpVtbl->GetDescription(This,pbstrDesc);
}
static inline HRESULT ITfDisplayAttributeInfo_GetAttributeInfo(ITfDisplayAttributeInfo* This,TF_DISPLAYATTRIBUTE *pda) {
    return This->lpVtbl->GetAttributeInfo(This,pda);
}
static inline HRESULT ITfDisplayAttributeInfo_SetAttributeInfo(ITfDisplayAttributeInfo* This,const TF_DISPLAYATTRIBUTE *pda) {
    return This->lpVtbl->SetAttributeInfo(This,pda);
}
static inline HRESULT ITfDisplayAttributeInfo_Reset(ITfDisplayAttributeInfo* This) {
    return This->lpVtbl->Reset(This);
}
#endif
#endif

#endif


#endif  /* __ITfDisplayAttributeInfo_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IEnumTfDisplayAttributeInfo interface
 */
#ifndef __IEnumTfDisplayAttributeInfo_INTERFACE_DEFINED__
#define __IEnumTfDisplayAttributeInfo_INTERFACE_DEFINED__

DEFINE_GUID(IID_IEnumTfDisplayAttributeInfo, 0x7cef04d7, 0xcb75, 0x4e80, 0xa7,0xab, 0x5f,0x5b,0xc7,0xd3,0x32,0xde);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("7cef04d7-cb75-4e80-a7ab-5f5bc7d332de")
IEnumTfDisplayAttributeInfo : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Clone(
        IEnumTfDisplayAttributeInfo **ppEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE Next(
        ULONG ulCount,
        ITfDisplayAttributeInfo **rgInfo,
        ULONG *pcFetched) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reset(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Skip(
        ULONG ulCount) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IEnumTfDisplayAttributeInfo, 0x7cef04d7, 0xcb75, 0x4e80, 0xa7,0xab, 0x5f,0x5b,0xc7,0xd3,0x32,0xde)
#endif
#else
typedef struct IEnumTfDisplayAttributeInfoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IEnumTfDisplayAttributeInfo *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IEnumTfDisplayAttributeInfo *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IEnumTfDisplayAttributeInfo *This);

    /*** IEnumTfDisplayAttributeInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *Clone)(
        IEnumTfDisplayAttributeInfo *This,
        IEnumTfDisplayAttributeInfo **ppEnum);

    HRESULT (STDMETHODCALLTYPE *Next)(
        IEnumTfDisplayAttributeInfo *This,
        ULONG ulCount,
        ITfDisplayAttributeInfo **rgInfo,
        ULONG *pcFetched);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        IEnumTfDisplayAttributeInfo *This);

    HRESULT (STDMETHODCALLTYPE *Skip)(
        IEnumTfDisplayAttributeInfo *This,
        ULONG ulCount);

    END_INTERFACE
} IEnumTfDisplayAttributeInfoVtbl;

interface IEnumTfDisplayAttributeInfo {
    CONST_VTBL IEnumTfDisplayAttributeInfoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IEnumTfDisplayAttributeInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IEnumTfDisplayAttributeInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IEnumTfDisplayAttributeInfo_Release(This) (This)->lpVtbl->Release(This)
/*** IEnumTfDisplayAttributeInfo methods ***/
#define IEnumTfDisplayAttributeInfo_Clone(This,ppEnum) (This)->lpVtbl->Clone(This,ppEnum)
#define IEnumTfDisplayAttributeInfo_Next(This,ulCount,rgInfo,pcFetched) (This)->lpVtbl->Next(This,ulCount,rgInfo,pcFetched)
#define IEnumTfDisplayAttributeInfo_Reset(This) (This)->lpVtbl->Reset(This)
#define IEnumTfDisplayAttributeInfo_Skip(This,ulCount) (This)->lpVtbl->Skip(This,ulCount)
#else
/*** IUnknown methods ***/
static inline HRESULT IEnumTfDisplayAttributeInfo_QueryInterface(IEnumTfDisplayAttributeInfo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IEnumTfDisplayAttributeInfo_AddRef(IEnumTfDisplayAttributeInfo* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IEnumTfDisplayAttributeInfo_Release(IEnumTfDisplayAttributeInfo* This) {
    return This->lpVtbl->Release(This);
}
/*** IEnumTfDisplayAttributeInfo methods ***/
static inline HRESULT IEnumTfDisplayAttributeInfo_Clone(IEnumTfDisplayAttributeInfo* This,IEnumTfDisplayAttributeInfo **ppEnum) {
    return This->lpVtbl->Clone(This,ppEnum);
}
static inline HRESULT IEnumTfDisplayAttributeInfo_Next(IEnumTfDisplayAttributeInfo* This,ULONG ulCount,ITfDisplayAttributeInfo **rgInfo,ULONG *pcFetched) {
    return This->lpVtbl->Next(This,ulCount,rgInfo,pcFetched);
}
static inline HRESULT IEnumTfDisplayAttributeInfo_Reset(IEnumTfDisplayAttributeInfo* This) {
    return This->lpVtbl->Reset(This);
}
static inline HRESULT IEnumTfDisplayAttributeInfo_Skip(IEnumTfDisplayAttributeInfo* This,ULONG ulCount) {
    return This->lpVtbl->Skip(This,ulCount);
}
#endif
#endif

#endif


#endif  /* __IEnumTfDisplayAttributeInfo_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITfDisplayAttributeMgr interface
 */
#ifndef __ITfDisplayAttributeMgr_INTERFACE_DEFINED__
#define __ITfDisplayAttributeMgr_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITfDisplayAttributeMgr, 0x8ded7393, 0x5db1, 0x475c, 0x9e,0x71, 0xa3,0x91,0x11,0xb0,0xff,0x67);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("8ded7393-5db1-475c-9e71-a39111b0ff67")
ITfDisplayAttributeMgr : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE OnUpdateInfo(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumDisplayAttributeInfo(
        IEnumTfDisplayAttributeInfo **ppEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDisplayAttributeInfo(
        REFGUID guid,
        ITfDisplayAttributeInfo **ppInfo,
        CLSID *pclsidOwner) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITfDisplayAttributeMgr, 0x8ded7393, 0x5db1, 0x475c, 0x9e,0x71, 0xa3,0x91,0x11,0xb0,0xff,0x67)
#endif
#else
typedef struct ITfDisplayAttributeMgrVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITfDisplayAttributeMgr *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITfDisplayAttributeMgr *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITfDisplayAttributeMgr *This);

    /*** ITfDisplayAttributeMgr methods ***/
    HRESULT (STDMETHODCALLTYPE *OnUpdateInfo)(
        ITfDisplayAttributeMgr *This);

    HRESULT (STDMETHODCALLTYPE *EnumDisplayAttributeInfo)(
        ITfDisplayAttributeMgr *This,
        IEnumTfDisplayAttributeInfo **ppEnum);

    HRESULT (STDMETHODCALLTYPE *GetDisplayAttributeInfo)(
        ITfDisplayAttributeMgr *This,
        REFGUID guid,
        ITfDisplayAttributeInfo **ppInfo,
        CLSID *pclsidOwner);

    END_INTERFACE
} ITfDisplayAttributeMgrVtbl;

interface ITfDisplayAttributeMgr {
    CONST_VTBL ITfDisplayAttributeMgrVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITfDisplayAttributeMgr_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITfDisplayAttributeMgr_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITfDisplayAttributeMgr_Release(This) (This)->lpVtbl->Release(This)
/*** ITfDisplayAttributeMgr methods ***/
#define ITfDisplayAttributeMgr_OnUpdateInfo(This) (This)->lpVtbl->OnUpdateInfo(This)
#define ITfDisplayAttributeMgr_EnumDisplayAttributeInfo(This,ppEnum) (This)->lpVtbl->EnumDisplayAttributeInfo(This,ppEnum)
#define ITfDisplayAttributeMgr_GetDisplayAttributeInfo(This,guid,ppInfo,pclsidOwner) (This)->lpVtbl->GetDisplayAttributeInfo(This,guid,ppInfo,pclsidOwner)
#else
/*** IUnknown methods ***/
static inline HRESULT ITfDisplayAttributeMgr_QueryInterface(ITfDisplayAttributeMgr* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITfDisplayAttributeMgr_AddRef(ITfDisplayAttributeMgr* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITfDisplayAttributeMgr_Release(ITfDisplayAttributeMgr* This) {
    return This->lpVtbl->Release(This);
}
/*** ITfDisplayAttributeMgr methods ***/
static inline HRESULT ITfDisplayAttributeMgr_OnUpdateInfo(ITfDisplayAttributeMgr* This) {
    return This->lpVtbl->OnUpdateInfo(This);
}
static inline HRESULT ITfDisplayAttributeMgr_EnumDisplayAttributeInfo(ITfDisplayAttributeMgr* This,IEnumTfDisplayAttributeInfo **ppEnum) {
    return This->lpVtbl->EnumDisplayAttributeInfo(This,ppEnum);
}
static inline HRESULT ITfDisplayAttributeMgr_GetDisplayAttributeInfo(ITfDisplayAttributeMgr* This,REFGUID guid,ITfDisplayAttributeInfo **ppInfo,CLSID *pclsidOwner) {
    return This->lpVtbl->GetDisplayAttributeInfo(This,guid,ppInfo,pclsidOwner);
}
#endif
#endif

#endif


#endif  /* __ITfDisplayAttributeMgr_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITfCategoryMgr interface
 */
#ifndef __ITfCategoryMgr_INTERFACE_DEFINED__
#define __ITfCategoryMgr_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITfCategoryMgr, 0xc3acefb5, 0xf69d, 0x4905, 0x93,0x8f, 0xfc,0xad,0xcf,0x4b,0xe8,0x30);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("c3acefb5-f69d-4905-938f-fcadcf4be830")
ITfCategoryMgr : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE RegisterCategory(
        REFCLSID rclsid,
        REFGUID rcatid,
        REFGUID rguid) = 0;

    virtual HRESULT STDMETHODCALLTYPE UnregisterCategory(
        REFCLSID rclsid,
        REFGUID rcatid,
        REFGUID rguid) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumCategoriesInItem(
        REFGUID rguid,
        IEnumGUID **ppEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumItemsInCategory(
        REFGUID rcatid,
        IEnumGUID **ppEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE FindClosestCategory(
        REFGUID rguid,
        GUID *pcatid,
        const GUID **ppcatidList,
        ULONG ulCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE RegisterGUIDDescription(
        REFCLSID rclsid,
        REFGUID rguid,
        const WCHAR *pchDesc,
        ULONG cch) = 0;

    virtual HRESULT STDMETHODCALLTYPE UnregisterGUIDDescription(
        REFCLSID rclsid,
        REFGUID rguid) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetGUIDDescription(
        REFGUID rguid,
        BSTR *pbstrDesc) = 0;

    virtual HRESULT STDMETHODCALLTYPE RegisterGUIDDWORD(
        REFCLSID rclsid,
        REFGUID rguid,
        DWORD dw) = 0;

    virtual HRESULT STDMETHODCALLTYPE UnregisterGUIDDWORD(
        REFCLSID rclsid,
        REFGUID rguid) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetGUIDDWORD(
        REFGUID rguid,
        DWORD *pdw) = 0;

    virtual HRESULT STDMETHODCALLTYPE RegisterGUID(
        REFGUID rguid,
        TfGuidAtom *pguidatom) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetGUID(
        TfGuidAtom guidatom,
        GUID *pguid) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsEqualTfGuidAtom(
        TfGuidAtom guidatom,
        REFGUID rguid,
        WINBOOL *pfEqual) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITfCategoryMgr, 0xc3acefb5, 0xf69d, 0x4905, 0x93,0x8f, 0xfc,0xad,0xcf,0x4b,0xe8,0x30)
#endif
#else
typedef struct ITfCategoryMgrVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITfCategoryMgr *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITfCategoryMgr *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITfCategoryMgr *This);

    /*** ITfCategoryMgr methods ***/
    HRESULT (STDMETHODCALLTYPE *RegisterCategory)(
        ITfCategoryMgr *This,
        REFCLSID rclsid,
        REFGUID rcatid,
        REFGUID rguid);

    HRESULT (STDMETHODCALLTYPE *UnregisterCategory)(
        ITfCategoryMgr *This,
        REFCLSID rclsid,
        REFGUID rcatid,
        REFGUID rguid);

    HRESULT (STDMETHODCALLTYPE *EnumCategoriesInItem)(
        ITfCategoryMgr *This,
        REFGUID rguid,
        IEnumGUID **ppEnum);

    HRESULT (STDMETHODCALLTYPE *EnumItemsInCategory)(
        ITfCategoryMgr *This,
        REFGUID rcatid,
        IEnumGUID **ppEnum);

    HRESULT (STDMETHODCALLTYPE *FindClosestCategory)(
        ITfCategoryMgr *This,
        REFGUID rguid,
        GUID *pcatid,
        const GUID **ppcatidList,
        ULONG ulCount);

    HRESULT (STDMETHODCALLTYPE *RegisterGUIDDescription)(
        ITfCategoryMgr *This,
        REFCLSID rclsid,
        REFGUID rguid,
        const WCHAR *pchDesc,
        ULONG cch);

    HRESULT (STDMETHODCALLTYPE *UnregisterGUIDDescription)(
        ITfCategoryMgr *This,
        REFCLSID rclsid,
        REFGUID rguid);

    HRESULT (STDMETHODCALLTYPE *GetGUIDDescription)(
        ITfCategoryMgr *This,
        REFGUID rguid,
        BSTR *pbstrDesc);

    HRESULT (STDMETHODCALLTYPE *RegisterGUIDDWORD)(
        ITfCategoryMgr *This,
        REFCLSID rclsid,
        REFGUID rguid,
        DWORD dw);

    HRESULT (STDMETHODCALLTYPE *UnregisterGUIDDWORD)(
        ITfCategoryMgr *This,
        REFCLSID rclsid,
        REFGUID rguid);

    HRESULT (STDMETHODCALLTYPE *GetGUIDDWORD)(
        ITfCategoryMgr *This,
        REFGUID rguid,
        DWORD *pdw);

    HRESULT (STDMETHODCALLTYPE *RegisterGUID)(
        ITfCategoryMgr *This,
        REFGUID rguid,
        TfGuidAtom *pguidatom);

    HRESULT (STDMETHODCALLTYPE *GetGUID)(
        ITfCategoryMgr *This,
        TfGuidAtom guidatom,
        GUID *pguid);

    HRESULT (STDMETHODCALLTYPE *IsEqualTfGuidAtom)(
        ITfCategoryMgr *This,
        TfGuidAtom guidatom,
        REFGUID rguid,
        WINBOOL *pfEqual);

    END_INTERFACE
} ITfCategoryMgrVtbl;

interface ITfCategoryMgr {
    CONST_VTBL ITfCategoryMgrVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITfCategoryMgr_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITfCategoryMgr_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITfCategoryMgr_Release(This) (This)->lpVtbl->Release(This)
/*** ITfCategoryMgr methods ***/
#define ITfCategoryMgr_RegisterCategory(This,rclsid,rcatid,rguid) (This)->lpVtbl->RegisterCategory(This,rclsid,rcatid,rguid)
#define ITfCategoryMgr_UnregisterCategory(This,rclsid,rcatid,rguid) (This)->lpVtbl->UnregisterCategory(This,rclsid,rcatid,rguid)
#define ITfCategoryMgr_EnumCategoriesInItem(This,rguid,ppEnum) (This)->lpVtbl->EnumCategoriesInItem(This,rguid,ppEnum)
#define ITfCategoryMgr_EnumItemsInCategory(This,rcatid,ppEnum) (This)->lpVtbl->EnumItemsInCategory(This,rcatid,ppEnum)
#define ITfCategoryMgr_FindClosestCategory(This,rguid,pcatid,ppcatidList,ulCount) (This)->lpVtbl->FindClosestCategory(This,rguid,pcatid,ppcatidList,ulCount)
#define ITfCategoryMgr_RegisterGUIDDescription(This,rclsid,rguid,pchDesc,cch) (This)->lpVtbl->RegisterGUIDDescription(This,rclsid,rguid,pchDesc,cch)
#define ITfCategoryMgr_UnregisterGUIDDescription(This,rclsid,rguid) (This)->lpVtbl->UnregisterGUIDDescription(This,rclsid,rguid)
#define ITfCategoryMgr_GetGUIDDescription(This,rguid,pbstrDesc) (This)->lpVtbl->GetGUIDDescription(This,rguid,pbstrDesc)
#define ITfCategoryMgr_RegisterGUIDDWORD(This,rclsid,rguid,dw) (This)->lpVtbl->RegisterGUIDDWORD(This,rclsid,rguid,dw)
#define ITfCategoryMgr_UnregisterGUIDDWORD(This,rclsid,rguid) (This)->lpVtbl->UnregisterGUIDDWORD(This,rclsid,rguid)
#define ITfCategoryMgr_GetGUIDDWORD(This,rguid,pdw) (This)->lpVtbl->GetGUIDDWORD(This,rguid,pdw)
#define ITfCategoryMgr_RegisterGUID(This,rguid,pguidatom) (This)->lpVtbl->RegisterGUID(This,rguid,pguidatom)
#define ITfCategoryMgr_GetGUID(This,guidatom,pguid) (This)->lpVtbl->GetGUID(This,guidatom,pguid)
#define ITfCategoryMgr_IsEqualTfGuidAtom(This,guidatom,rguid,pfEqual) (This)->lpVtbl->IsEqualTfGuidAtom(This,guidatom,rguid,pfEqual)
#else
/*** IUnknown methods ***/
static inline HRESULT ITfCategoryMgr_QueryInterface(ITfCategoryMgr* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITfCategoryMgr_AddRef(ITfCategoryMgr* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITfCategoryMgr_Release(ITfCategoryMgr* This) {
    return This->lpVtbl->Release(This);
}
/*** ITfCategoryMgr methods ***/
static inline HRESULT ITfCategoryMgr_RegisterCategory(ITfCategoryMgr* This,REFCLSID rclsid,REFGUID rcatid,REFGUID rguid) {
    return This->lpVtbl->RegisterCategory(This,rclsid,rcatid,rguid);
}
static inline HRESULT ITfCategoryMgr_UnregisterCategory(ITfCategoryMgr* This,REFCLSID rclsid,REFGUID rcatid,REFGUID rguid) {
    return This->lpVtbl->UnregisterCategory(This,rclsid,rcatid,rguid);
}
static inline HRESULT ITfCategoryMgr_EnumCategoriesInItem(ITfCategoryMgr* This,REFGUID rguid,IEnumGUID **ppEnum) {
    return This->lpVtbl->EnumCategoriesInItem(This,rguid,ppEnum);
}
static inline HRESULT ITfCategoryMgr_EnumItemsInCategory(ITfCategoryMgr* This,REFGUID rcatid,IEnumGUID **ppEnum) {
    return This->lpVtbl->EnumItemsInCategory(This,rcatid,ppEnum);
}
static inline HRESULT ITfCategoryMgr_FindClosestCategory(ITfCategoryMgr* This,REFGUID rguid,GUID *pcatid,const GUID **ppcatidList,ULONG ulCount) {
    return This->lpVtbl->FindClosestCategory(This,rguid,pcatid,ppcatidList,ulCount);
}
static inline HRESULT ITfCategoryMgr_RegisterGUIDDescription(ITfCategoryMgr* This,REFCLSID rclsid,REFGUID rguid,const WCHAR *pchDesc,ULONG cch) {
    return This->lpVtbl->RegisterGUIDDescription(This,rclsid,rguid,pchDesc,cch);
}
static inline HRESULT ITfCategoryMgr_UnregisterGUIDDescription(ITfCategoryMgr* This,REFCLSID rclsid,REFGUID rguid) {
    return This->lpVtbl->UnregisterGUIDDescription(This,rclsid,rguid);
}
static inline HRESULT ITfCategoryMgr_GetGUIDDescription(ITfCategoryMgr* This,REFGUID rguid,BSTR *pbstrDesc) {
    return This->lpVtbl->GetGUIDDescription(This,rguid,pbstrDesc);
}
static inline HRESULT ITfCategoryMgr_RegisterGUIDDWORD(ITfCategoryMgr* This,REFCLSID rclsid,REFGUID rguid,DWORD dw) {
    return This->lpVtbl->RegisterGUIDDWORD(This,rclsid,rguid,dw);
}
static inline HRESULT ITfCategoryMgr_UnregisterGUIDDWORD(ITfCategoryMgr* This,REFCLSID rclsid,REFGUID rguid) {
    return This->lpVtbl->UnregisterGUIDDWORD(This,rclsid,rguid);
}
static inline HRESULT ITfCategoryMgr_GetGUIDDWORD(ITfCategoryMgr* This,REFGUID rguid,DWORD *pdw) {
    return This->lpVtbl->GetGUIDDWORD(This,rguid,pdw);
}
static inline HRESULT ITfCategoryMgr_RegisterGUID(ITfCategoryMgr* This,REFGUID rguid,TfGuidAtom *pguidatom) {
    return This->lpVtbl->RegisterGUID(This,rguid,pguidatom);
}
static inline HRESULT ITfCategoryMgr_GetGUID(ITfCategoryMgr* This,TfGuidAtom guidatom,GUID *pguid) {
    return This->lpVtbl->GetGUID(This,guidatom,pguid);
}
static inline HRESULT ITfCategoryMgr_IsEqualTfGuidAtom(ITfCategoryMgr* This,TfGuidAtom guidatom,REFGUID rguid,WINBOOL *pfEqual) {
    return This->lpVtbl->IsEqualTfGuidAtom(This,guidatom,rguid,pfEqual);
}
#endif
#endif

#endif


#endif  /* __ITfCategoryMgr_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IEnumTfRanges interface
 */
#ifndef __IEnumTfRanges_INTERFACE_DEFINED__
#define __IEnumTfRanges_INTERFACE_DEFINED__

DEFINE_GUID(IID_IEnumTfRanges, 0xf99d3f40, 0x8e32, 0x11d2, 0xbf,0x46, 0x00,0x10,0x5a,0x27,0x99,0xb5);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("f99d3f40-8e32-11d2-bf46-00105a2799b5")
IEnumTfRanges : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Clone(
        IEnumTfRanges **ppEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE Next(
        ULONG ulCount,
        ITfRange **ppRange,
        ULONG *pcFetched) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reset(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Skip(
        ULONG ulCount) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IEnumTfRanges, 0xf99d3f40, 0x8e32, 0x11d2, 0xbf,0x46, 0x00,0x10,0x5a,0x27,0x99,0xb5)
#endif
#else
typedef struct IEnumTfRangesVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IEnumTfRanges *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IEnumTfRanges *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IEnumTfRanges *This);

    /*** IEnumTfRanges methods ***/
    HRESULT (STDMETHODCALLTYPE *Clone)(
        IEnumTfRanges *This,
        IEnumTfRanges **ppEnum);

    HRESULT (STDMETHODCALLTYPE *Next)(
        IEnumTfRanges *This,
        ULONG ulCount,
        ITfRange **ppRange,
        ULONG *pcFetched);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        IEnumTfRanges *This);

    HRESULT (STDMETHODCALLTYPE *Skip)(
        IEnumTfRanges *This,
        ULONG ulCount);

    END_INTERFACE
} IEnumTfRangesVtbl;

interface IEnumTfRanges {
    CONST_VTBL IEnumTfRangesVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IEnumTfRanges_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IEnumTfRanges_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IEnumTfRanges_Release(This) (This)->lpVtbl->Release(This)
/*** IEnumTfRanges methods ***/
#define IEnumTfRanges_Clone(This,ppEnum) (This)->lpVtbl->Clone(This,ppEnum)
#define IEnumTfRanges_Next(This,ulCount,ppRange,pcFetched) (This)->lpVtbl->Next(This,ulCount,ppRange,pcFetched)
#define IEnumTfRanges_Reset(This) (This)->lpVtbl->Reset(This)
#define IEnumTfRanges_Skip(This,ulCount) (This)->lpVtbl->Skip(This,ulCount)
#else
/*** IUnknown methods ***/
static inline HRESULT IEnumTfRanges_QueryInterface(IEnumTfRanges* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IEnumTfRanges_AddRef(IEnumTfRanges* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IEnumTfRanges_Release(IEnumTfRanges* This) {
    return This->lpVtbl->Release(This);
}
/*** IEnumTfRanges methods ***/
static inline HRESULT IEnumTfRanges_Clone(IEnumTfRanges* This,IEnumTfRanges **ppEnum) {
    return This->lpVtbl->Clone(This,ppEnum);
}
static inline HRESULT IEnumTfRanges_Next(IEnumTfRanges* This,ULONG ulCount,ITfRange **ppRange,ULONG *pcFetched) {
    return This->lpVtbl->Next(This,ulCount,ppRange,pcFetched);
}
static inline HRESULT IEnumTfRanges_Reset(IEnumTfRanges* This) {
    return This->lpVtbl->Reset(This);
}
static inline HRESULT IEnumTfRanges_Skip(IEnumTfRanges* This,ULONG ulCount) {
    return This->lpVtbl->Skip(This,ulCount);
}
#endif
#endif

#endif


#endif  /* __IEnumTfRanges_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITfEditRecord interface
 */
#ifndef __ITfEditRecord_INTERFACE_DEFINED__
#define __ITfEditRecord_INTERFACE_DEFINED__

#define TF_GTP_INCL_TEXT (0x1)

DEFINE_GUID(IID_ITfEditRecord, 0x42d4d099, 0x7c1a, 0x4a89, 0xb8,0x36, 0x6c,0x6f,0x22,0x16,0x0d,0xf0);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("42d4d099-7c1a-4a89-b836-6c6f22160df0")
ITfEditRecord : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetSelectionStatus(
        WINBOOL *changed) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTextAndPropertyUpdates(
        DWORD flags,
        const GUID **props,
        ULONG count,
        IEnumTfRanges **ret) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITfEditRecord, 0x42d4d099, 0x7c1a, 0x4a89, 0xb8,0x36, 0x6c,0x6f,0x22,0x16,0x0d,0xf0)
#endif
#else
typedef struct ITfEditRecordVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITfEditRecord *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITfEditRecord *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITfEditRecord *This);

    /*** ITfEditRecord methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSelectionStatus)(
        ITfEditRecord *This,
        WINBOOL *changed);

    HRESULT (STDMETHODCALLTYPE *GetTextAndPropertyUpdates)(
        ITfEditRecord *This,
        DWORD flags,
        const GUID **props,
        ULONG count,
        IEnumTfRanges **ret);

    END_INTERFACE
} ITfEditRecordVtbl;

interface ITfEditRecord {
    CONST_VTBL ITfEditRecordVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITfEditRecord_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITfEditRecord_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITfEditRecord_Release(This) (This)->lpVtbl->Release(This)
/*** ITfEditRecord methods ***/
#define ITfEditRecord_GetSelectionStatus(This,changed) (This)->lpVtbl->GetSelectionStatus(This,changed)
#define ITfEditRecord_GetTextAndPropertyUpdates(This,flags,props,count,ret) (This)->lpVtbl->GetTextAndPropertyUpdates(This,flags,props,count,ret)
#else
/*** IUnknown methods ***/
static inline HRESULT ITfEditRecord_QueryInterface(ITfEditRecord* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITfEditRecord_AddRef(ITfEditRecord* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITfEditRecord_Release(ITfEditRecord* This) {
    return This->lpVtbl->Release(This);
}
/*** ITfEditRecord methods ***/
static inline HRESULT ITfEditRecord_GetSelectionStatus(ITfEditRecord* This,WINBOOL *changed) {
    return This->lpVtbl->GetSelectionStatus(This,changed);
}
static inline HRESULT ITfEditRecord_GetTextAndPropertyUpdates(ITfEditRecord* This,DWORD flags,const GUID **props,ULONG count,IEnumTfRanges **ret) {
    return This->lpVtbl->GetTextAndPropertyUpdates(This,flags,props,count,ret);
}
#endif
#endif

#endif


#endif  /* __ITfEditRecord_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITfTextEditSink interface
 */
#ifndef __ITfTextEditSink_INTERFACE_DEFINED__
#define __ITfTextEditSink_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITfTextEditSink, 0x8127d409, 0xccd3, 0x4683, 0x96,0x7a, 0xb4,0x3d,0x5b,0x48,0x2b,0xf7);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("8127d409-ccd3-4683-967a-b43d5b482bf7")
ITfTextEditSink : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE OnEndEdit(
        ITfContext *pic,
        TfEditCookie ecReadOnly,
        ITfEditRecord *pEditRecord) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITfTextEditSink, 0x8127d409, 0xccd3, 0x4683, 0x96,0x7a, 0xb4,0x3d,0x5b,0x48,0x2b,0xf7)
#endif
#else
typedef struct ITfTextEditSinkVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITfTextEditSink *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITfTextEditSink *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITfTextEditSink *This);

    /*** ITfTextEditSink methods ***/
    HRESULT (STDMETHODCALLTYPE *OnEndEdit)(
        ITfTextEditSink *This,
        ITfContext *pic,
        TfEditCookie ecReadOnly,
        ITfEditRecord *pEditRecord);

    END_INTERFACE
} ITfTextEditSinkVtbl;

interface ITfTextEditSink {
    CONST_VTBL ITfTextEditSinkVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITfTextEditSink_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITfTextEditSink_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITfTextEditSink_Release(This) (This)->lpVtbl->Release(This)
/*** ITfTextEditSink methods ***/
#define ITfTextEditSink_OnEndEdit(This,pic,ecReadOnly,pEditRecord) (This)->lpVtbl->OnEndEdit(This,pic,ecReadOnly,pEditRecord)
#else
/*** IUnknown methods ***/
static inline HRESULT ITfTextEditSink_QueryInterface(ITfTextEditSink* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITfTextEditSink_AddRef(ITfTextEditSink* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITfTextEditSink_Release(ITfTextEditSink* This) {
    return This->lpVtbl->Release(This);
}
/*** ITfTextEditSink methods ***/
static inline HRESULT ITfTextEditSink_OnEndEdit(ITfTextEditSink* This,ITfContext *pic,TfEditCookie ecReadOnly,ITfEditRecord *pEditRecord) {
    return This->lpVtbl->OnEndEdit(This,pic,ecReadOnly,pEditRecord);
}
#endif
#endif

#endif


#endif  /* __ITfTextEditSink_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITfContextOwnerCompositionSink interface
 */
#ifndef __ITfContextOwnerCompositionSink_INTERFACE_DEFINED__
#define __ITfContextOwnerCompositionSink_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITfContextOwnerCompositionSink, 0x5f20aa40, 0xb57a, 0x4f34, 0x96,0xab, 0x35,0x76,0xf3,0x77,0xcc,0x79);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("5f20aa40-b57a-4f34-96ab-3576f377cc79")
ITfContextOwnerCompositionSink : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE OnStartComposition(
        ITfCompositionView *pComposition,
        WINBOOL *pfOk) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnUpdateComposition(
        ITfCompositionView *pComposition,
        ITfRange *pRangeNew) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnEndComposition(
        ITfCompositionView *pComposition) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITfContextOwnerCompositionSink, 0x5f20aa40, 0xb57a, 0x4f34, 0x96,0xab, 0x35,0x76,0xf3,0x77,0xcc,0x79)
#endif
#else
typedef struct ITfContextOwnerCompositionSinkVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITfContextOwnerCompositionSink *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITfContextOwnerCompositionSink *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITfContextOwnerCompositionSink *This);

    /*** ITfContextOwnerCompositionSink methods ***/
    HRESULT (STDMETHODCALLTYPE *OnStartComposition)(
        ITfContextOwnerCompositionSink *This,
        ITfCompositionView *pComposition,
        WINBOOL *pfOk);

    HRESULT (STDMETHODCALLTYPE *OnUpdateComposition)(
        ITfContextOwnerCompositionSink *This,
        ITfCompositionView *pComposition,
        ITfRange *pRangeNew);

    HRESULT (STDMETHODCALLTYPE *OnEndComposition)(
        ITfContextOwnerCompositionSink *This,
        ITfCompositionView *pComposition);

    END_INTERFACE
} ITfContextOwnerCompositionSinkVtbl;

interface ITfContextOwnerCompositionSink {
    CONST_VTBL ITfContextOwnerCompositionSinkVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITfContextOwnerCompositionSink_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITfContextOwnerCompositionSink_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITfContextOwnerCompositionSink_Release(This) (This)->lpVtbl->Release(This)
/*** ITfContextOwnerCompositionSink methods ***/
#define ITfContextOwnerCompositionSink_OnStartComposition(This,pComposition,pfOk) (This)->lpVtbl->OnStartComposition(This,pComposition,pfOk)
#define ITfContextOwnerCompositionSink_OnUpdateComposition(This,pComposition,pRangeNew) (This)->lpVtbl->OnUpdateComposition(This,pComposition,pRangeNew)
#define ITfContextOwnerCompositionSink_OnEndComposition(This,pComposition) (This)->lpVtbl->OnEndComposition(This,pComposition)
#else
/*** IUnknown methods ***/
static inline HRESULT ITfContextOwnerCompositionSink_QueryInterface(ITfContextOwnerCompositionSink* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITfContextOwnerCompositionSink_AddRef(ITfContextOwnerCompositionSink* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITfContextOwnerCompositionSink_Release(ITfContextOwnerCompositionSink* This) {
    return This->lpVtbl->Release(This);
}
/*** ITfContextOwnerCompositionSink methods ***/
static inline HRESULT ITfContextOwnerCompositionSink_OnStartComposition(ITfContextOwnerCompositionSink* This,ITfCompositionView *pComposition,WINBOOL *pfOk) {
    return This->lpVtbl->OnStartComposition(This,pComposition,pfOk);
}
static inline HRESULT ITfContextOwnerCompositionSink_OnUpdateComposition(ITfContextOwnerCompositionSink* This,ITfCompositionView *pComposition,ITfRange *pRangeNew) {
    return This->lpVtbl->OnUpdateComposition(This,pComposition,pRangeNew);
}
static inline HRESULT ITfContextOwnerCompositionSink_OnEndComposition(ITfContextOwnerCompositionSink* This,ITfCompositionView *pComposition) {
    return This->lpVtbl->OnEndComposition(This,pComposition);
}
#endif
#endif

#endif


#endif  /* __ITfContextOwnerCompositionSink_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITfActiveLanguageProfileNotifySink interface
 */
#ifndef __ITfActiveLanguageProfileNotifySink_INTERFACE_DEFINED__
#define __ITfActiveLanguageProfileNotifySink_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITfActiveLanguageProfileNotifySink, 0xb246cb75, 0xa93e, 0x4652, 0xbf,0x8c, 0xb3,0xfe,0x0c,0xfd,0x7e,0x57);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("b246cb75-a93e-4652-bf8c-b3fe0cfd7e57")
ITfActiveLanguageProfileNotifySink : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE OnActivated(
        REFCLSID clsid,
        REFGUID guidProfile,
        WINBOOL fActivated) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITfActiveLanguageProfileNotifySink, 0xb246cb75, 0xa93e, 0x4652, 0xbf,0x8c, 0xb3,0xfe,0x0c,0xfd,0x7e,0x57)
#endif
#else
typedef struct ITfActiveLanguageProfileNotifySinkVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITfActiveLanguageProfileNotifySink *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITfActiveLanguageProfileNotifySink *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITfActiveLanguageProfileNotifySink *This);

    /*** ITfActiveLanguageProfileNotifySink methods ***/
    HRESULT (STDMETHODCALLTYPE *OnActivated)(
        ITfActiveLanguageProfileNotifySink *This,
        REFCLSID clsid,
        REFGUID guidProfile,
        WINBOOL fActivated);

    END_INTERFACE
} ITfActiveLanguageProfileNotifySinkVtbl;

interface ITfActiveLanguageProfileNotifySink {
    CONST_VTBL ITfActiveLanguageProfileNotifySinkVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITfActiveLanguageProfileNotifySink_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITfActiveLanguageProfileNotifySink_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITfActiveLanguageProfileNotifySink_Release(This) (This)->lpVtbl->Release(This)
/*** ITfActiveLanguageProfileNotifySink methods ***/
#define ITfActiveLanguageProfileNotifySink_OnActivated(This,clsid,guidProfile,fActivated) (This)->lpVtbl->OnActivated(This,clsid,guidProfile,fActivated)
#else
/*** IUnknown methods ***/
static inline HRESULT ITfActiveLanguageProfileNotifySink_QueryInterface(ITfActiveLanguageProfileNotifySink* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITfActiveLanguageProfileNotifySink_AddRef(ITfActiveLanguageProfileNotifySink* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITfActiveLanguageProfileNotifySink_Release(ITfActiveLanguageProfileNotifySink* This) {
    return This->lpVtbl->Release(This);
}
/*** ITfActiveLanguageProfileNotifySink methods ***/
static inline HRESULT ITfActiveLanguageProfileNotifySink_OnActivated(ITfActiveLanguageProfileNotifySink* This,REFCLSID clsid,REFGUID guidProfile,WINBOOL fActivated) {
    return This->lpVtbl->OnActivated(This,clsid,guidProfile,fActivated);
}
#endif
#endif

#endif


#endif  /* __ITfActiveLanguageProfileNotifySink_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IEnumTfLanguageProfiles interface
 */
#ifndef __IEnumTfLanguageProfiles_INTERFACE_DEFINED__
#define __IEnumTfLanguageProfiles_INTERFACE_DEFINED__

DEFINE_GUID(IID_IEnumTfLanguageProfiles, 0x3d61bf11, 0xac5f, 0x42c8, 0xa4,0xcb, 0x93,0x1b,0xcc,0x28,0xc7,0x44);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("3d61bf11-ac5f-42c8-a4cb-931bcc28c744")
IEnumTfLanguageProfiles : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Clone(
        IEnumTfLanguageProfiles **ppEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE Next(
        ULONG ulCount,
        TF_LANGUAGEPROFILE *pProfile,
        ULONG *pcFetch) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reset(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Skip(
        ULONG ulCount) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IEnumTfLanguageProfiles, 0x3d61bf11, 0xac5f, 0x42c8, 0xa4,0xcb, 0x93,0x1b,0xcc,0x28,0xc7,0x44)
#endif
#else
typedef struct IEnumTfLanguageProfilesVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IEnumTfLanguageProfiles *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IEnumTfLanguageProfiles *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IEnumTfLanguageProfiles *This);

    /*** IEnumTfLanguageProfiles methods ***/
    HRESULT (STDMETHODCALLTYPE *Clone)(
        IEnumTfLanguageProfiles *This,
        IEnumTfLanguageProfiles **ppEnum);

    HRESULT (STDMETHODCALLTYPE *Next)(
        IEnumTfLanguageProfiles *This,
        ULONG ulCount,
        TF_LANGUAGEPROFILE *pProfile,
        ULONG *pcFetch);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        IEnumTfLanguageProfiles *This);

    HRESULT (STDMETHODCALLTYPE *Skip)(
        IEnumTfLanguageProfiles *This,
        ULONG ulCount);

    END_INTERFACE
} IEnumTfLanguageProfilesVtbl;

interface IEnumTfLanguageProfiles {
    CONST_VTBL IEnumTfLanguageProfilesVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IEnumTfLanguageProfiles_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IEnumTfLanguageProfiles_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IEnumTfLanguageProfiles_Release(This) (This)->lpVtbl->Release(This)
/*** IEnumTfLanguageProfiles methods ***/
#define IEnumTfLanguageProfiles_Clone(This,ppEnum) (This)->lpVtbl->Clone(This,ppEnum)
#define IEnumTfLanguageProfiles_Next(This,ulCount,pProfile,pcFetch) (This)->lpVtbl->Next(This,ulCount,pProfile,pcFetch)
#define IEnumTfLanguageProfiles_Reset(This) (This)->lpVtbl->Reset(This)
#define IEnumTfLanguageProfiles_Skip(This,ulCount) (This)->lpVtbl->Skip(This,ulCount)
#else
/*** IUnknown methods ***/
static inline HRESULT IEnumTfLanguageProfiles_QueryInterface(IEnumTfLanguageProfiles* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IEnumTfLanguageProfiles_AddRef(IEnumTfLanguageProfiles* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IEnumTfLanguageProfiles_Release(IEnumTfLanguageProfiles* This) {
    return This->lpVtbl->Release(This);
}
/*** IEnumTfLanguageProfiles methods ***/
static inline HRESULT IEnumTfLanguageProfiles_Clone(IEnumTfLanguageProfiles* This,IEnumTfLanguageProfiles **ppEnum) {
    return This->lpVtbl->Clone(This,ppEnum);
}
static inline HRESULT IEnumTfLanguageProfiles_Next(IEnumTfLanguageProfiles* This,ULONG ulCount,TF_LANGUAGEPROFILE *pProfile,ULONG *pcFetch) {
    return This->lpVtbl->Next(This,ulCount,pProfile,pcFetch);
}
static inline HRESULT IEnumTfLanguageProfiles_Reset(IEnumTfLanguageProfiles* This) {
    return This->lpVtbl->Reset(This);
}
static inline HRESULT IEnumTfLanguageProfiles_Skip(IEnumTfLanguageProfiles* This,ULONG ulCount) {
    return This->lpVtbl->Skip(This,ulCount);
}
#endif
#endif

#endif


#endif  /* __IEnumTfLanguageProfiles_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITfTextInputProcessor interface
 */
#ifndef __ITfTextInputProcessor_INTERFACE_DEFINED__
#define __ITfTextInputProcessor_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITfTextInputProcessor, 0xaa80e7f7, 0x2021, 0x11d2, 0x93,0xe0, 0x00,0x60,0xb0,0x67,0xb8,0x6e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("aa80e7f7-2021-11d2-93e0-0060b067b86e")
ITfTextInputProcessor : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Activate(
        ITfThreadMgr *ptim,
        TfClientId tid) = 0;

    virtual HRESULT STDMETHODCALLTYPE Deactivate(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITfTextInputProcessor, 0xaa80e7f7, 0x2021, 0x11d2, 0x93,0xe0, 0x00,0x60,0xb0,0x67,0xb8,0x6e)
#endif
#else
typedef struct ITfTextInputProcessorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITfTextInputProcessor *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITfTextInputProcessor *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITfTextInputProcessor *This);

    /*** ITfTextInputProcessor methods ***/
    HRESULT (STDMETHODCALLTYPE *Activate)(
        ITfTextInputProcessor *This,
        ITfThreadMgr *ptim,
        TfClientId tid);

    HRESULT (STDMETHODCALLTYPE *Deactivate)(
        ITfTextInputProcessor *This);

    END_INTERFACE
} ITfTextInputProcessorVtbl;

interface ITfTextInputProcessor {
    CONST_VTBL ITfTextInputProcessorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITfTextInputProcessor_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITfTextInputProcessor_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITfTextInputProcessor_Release(This) (This)->lpVtbl->Release(This)
/*** ITfTextInputProcessor methods ***/
#define ITfTextInputProcessor_Activate(This,ptim,tid) (This)->lpVtbl->Activate(This,ptim,tid)
#define ITfTextInputProcessor_Deactivate(This) (This)->lpVtbl->Deactivate(This)
#else
/*** IUnknown methods ***/
static inline HRESULT ITfTextInputProcessor_QueryInterface(ITfTextInputProcessor* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITfTextInputProcessor_AddRef(ITfTextInputProcessor* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITfTextInputProcessor_Release(ITfTextInputProcessor* This) {
    return This->lpVtbl->Release(This);
}
/*** ITfTextInputProcessor methods ***/
static inline HRESULT ITfTextInputProcessor_Activate(ITfTextInputProcessor* This,ITfThreadMgr *ptim,TfClientId tid) {
    return This->lpVtbl->Activate(This,ptim,tid);
}
static inline HRESULT ITfTextInputProcessor_Deactivate(ITfTextInputProcessor* This) {
    return This->lpVtbl->Deactivate(This);
}
#endif
#endif

#endif


#endif  /* __ITfTextInputProcessor_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITfThreadMgrEventSink interface
 */
#ifndef __ITfThreadMgrEventSink_INTERFACE_DEFINED__
#define __ITfThreadMgrEventSink_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITfThreadMgrEventSink, 0xaa80e80e, 0x2021, 0x11d2, 0x93,0xe0, 0x00,0x60,0xb0,0x67,0xb8,0x6e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("aa80e80e-2021-11d2-93e0-0060b067b86e")
ITfThreadMgrEventSink : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE OnInitDocumentMgr(
        ITfDocumentMgr *pdim) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnUninitDocumentMgr(
        ITfDocumentMgr *pdim) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnSetFocus(
        ITfDocumentMgr *pdimFocus,
        ITfDocumentMgr *pdimPrevFocus) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnPushContext(
        ITfContext *pic) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnPopContext(
        ITfContext *pic) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITfThreadMgrEventSink, 0xaa80e80e, 0x2021, 0x11d2, 0x93,0xe0, 0x00,0x60,0xb0,0x67,0xb8,0x6e)
#endif
#else
typedef struct ITfThreadMgrEventSinkVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITfThreadMgrEventSink *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITfThreadMgrEventSink *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITfThreadMgrEventSink *This);

    /*** ITfThreadMgrEventSink methods ***/
    HRESULT (STDMETHODCALLTYPE *OnInitDocumentMgr)(
        ITfThreadMgrEventSink *This,
        ITfDocumentMgr *pdim);

    HRESULT (STDMETHODCALLTYPE *OnUninitDocumentMgr)(
        ITfThreadMgrEventSink *This,
        ITfDocumentMgr *pdim);

    HRESULT (STDMETHODCALLTYPE *OnSetFocus)(
        ITfThreadMgrEventSink *This,
        ITfDocumentMgr *pdimFocus,
        ITfDocumentMgr *pdimPrevFocus);

    HRESULT (STDMETHODCALLTYPE *OnPushContext)(
        ITfThreadMgrEventSink *This,
        ITfContext *pic);

    HRESULT (STDMETHODCALLTYPE *OnPopContext)(
        ITfThreadMgrEventSink *This,
        ITfContext *pic);

    END_INTERFACE
} ITfThreadMgrEventSinkVtbl;

interface ITfThreadMgrEventSink {
    CONST_VTBL ITfThreadMgrEventSinkVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITfThreadMgrEventSink_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITfThreadMgrEventSink_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITfThreadMgrEventSink_Release(This) (This)->lpVtbl->Release(This)
/*** ITfThreadMgrEventSink methods ***/
#define ITfThreadMgrEventSink_OnInitDocumentMgr(This,pdim) (This)->lpVtbl->OnInitDocumentMgr(This,pdim)
#define ITfThreadMgrEventSink_OnUninitDocumentMgr(This,pdim) (This)->lpVtbl->OnUninitDocumentMgr(This,pdim)
#define ITfThreadMgrEventSink_OnSetFocus(This,pdimFocus,pdimPrevFocus) (This)->lpVtbl->OnSetFocus(This,pdimFocus,pdimPrevFocus)
#define ITfThreadMgrEventSink_OnPushContext(This,pic) (This)->lpVtbl->OnPushContext(This,pic)
#define ITfThreadMgrEventSink_OnPopContext(This,pic) (This)->lpVtbl->OnPopContext(This,pic)
#else
/*** IUnknown methods ***/
static inline HRESULT ITfThreadMgrEventSink_QueryInterface(ITfThreadMgrEventSink* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITfThreadMgrEventSink_AddRef(ITfThreadMgrEventSink* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITfThreadMgrEventSink_Release(ITfThreadMgrEventSink* This) {
    return This->lpVtbl->Release(This);
}
/*** ITfThreadMgrEventSink methods ***/
static inline HRESULT ITfThreadMgrEventSink_OnInitDocumentMgr(ITfThreadMgrEventSink* This,ITfDocumentMgr *pdim) {
    return This->lpVtbl->OnInitDocumentMgr(This,pdim);
}
static inline HRESULT ITfThreadMgrEventSink_OnUninitDocumentMgr(ITfThreadMgrEventSink* This,ITfDocumentMgr *pdim) {
    return This->lpVtbl->OnUninitDocumentMgr(This,pdim);
}
static inline HRESULT ITfThreadMgrEventSink_OnSetFocus(ITfThreadMgrEventSink* This,ITfDocumentMgr *pdimFocus,ITfDocumentMgr *pdimPrevFocus) {
    return This->lpVtbl->OnSetFocus(This,pdimFocus,pdimPrevFocus);
}
static inline HRESULT ITfThreadMgrEventSink_OnPushContext(ITfThreadMgrEventSink* This,ITfContext *pic) {
    return This->lpVtbl->OnPushContext(This,pic);
}
static inline HRESULT ITfThreadMgrEventSink_OnPopContext(ITfThreadMgrEventSink* This,ITfContext *pic) {
    return This->lpVtbl->OnPopContext(This,pic);
}
#endif
#endif

#endif


#endif  /* __ITfThreadMgrEventSink_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITfKeystrokeMgr interface
 */
#ifndef __ITfKeystrokeMgr_INTERFACE_DEFINED__
#define __ITfKeystrokeMgr_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITfKeystrokeMgr, 0xaa80e7f0, 0x2021, 0x11d2, 0x93,0xe0, 0x00,0x60,0xb0,0x67,0xb8,0x6e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("aa80e7f0-2021-11d2-93e0-0060b067b86e")
ITfKeystrokeMgr : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE AdviseKeyEventSink(
        TfClientId tid,
        ITfKeyEventSink *pSink,
        WINBOOL fForeground) = 0;

    virtual HRESULT STDMETHODCALLTYPE UnadviseKeyEventSink(
        TfClientId tid) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetForeground(
        CLSID *pclsid) = 0;

    virtual HRESULT STDMETHODCALLTYPE TestKeyDown(
        WPARAM wParam,
        LPARAM lParam,
        WINBOOL *pfEaten) = 0;

    virtual HRESULT STDMETHODCALLTYPE TestKeyUp(
        WPARAM wParam,
        LPARAM lParam,
        WINBOOL *pfEaten) = 0;

    virtual HRESULT STDMETHODCALLTYPE KeyDown(
        WPARAM wParam,
        LPARAM lParam,
        WINBOOL *pfEaten) = 0;

    virtual HRESULT STDMETHODCALLTYPE KeyUp(
        WPARAM wParam,
        LPARAM lParam,
        WINBOOL *pfEaten) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPreservedKey(
        ITfContext *pic,
        const TF_PRESERVEDKEY *pprekey,
        GUID *pguid) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsPreservedKey(
        REFGUID rguid,
        const TF_PRESERVEDKEY *pprekey,
        WINBOOL *pfRegistered) = 0;

    virtual HRESULT STDMETHODCALLTYPE PreserveKey(
        TfClientId tid,
        REFGUID rguid,
        const TF_PRESERVEDKEY *prekey,
        const WCHAR *pchDesc,
        ULONG cchDesc) = 0;

    virtual HRESULT STDMETHODCALLTYPE UnpreserveKey(
        REFGUID rguid,
        const TF_PRESERVEDKEY *pprekey) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetPreservedKeyDescription(
        REFGUID rguid,
        const WCHAR *pchDesc,
        ULONG cchDesc) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPreservedKeyDescription(
        REFGUID rguid,
        BSTR *pbstrDesc) = 0;

    virtual HRESULT STDMETHODCALLTYPE SimulatePreservedKey(
        ITfContext *pic,
        REFGUID rguid,
        WINBOOL *pfEaten) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITfKeystrokeMgr, 0xaa80e7f0, 0x2021, 0x11d2, 0x93,0xe0, 0x00,0x60,0xb0,0x67,0xb8,0x6e)
#endif
#else
typedef struct ITfKeystrokeMgrVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITfKeystrokeMgr *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITfKeystrokeMgr *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITfKeystrokeMgr *This);

    /*** ITfKeystrokeMgr methods ***/
    HRESULT (STDMETHODCALLTYPE *AdviseKeyEventSink)(
        ITfKeystrokeMgr *This,
        TfClientId tid,
        ITfKeyEventSink *pSink,
        WINBOOL fForeground);

    HRESULT (STDMETHODCALLTYPE *UnadviseKeyEventSink)(
        ITfKeystrokeMgr *This,
        TfClientId tid);

    HRESULT (STDMETHODCALLTYPE *GetForeground)(
        ITfKeystrokeMgr *This,
        CLSID *pclsid);

    HRESULT (STDMETHODCALLTYPE *TestKeyDown)(
        ITfKeystrokeMgr *This,
        WPARAM wParam,
        LPARAM lParam,
        WINBOOL *pfEaten);

    HRESULT (STDMETHODCALLTYPE *TestKeyUp)(
        ITfKeystrokeMgr *This,
        WPARAM wParam,
        LPARAM lParam,
        WINBOOL *pfEaten);

    HRESULT (STDMETHODCALLTYPE *KeyDown)(
        ITfKeystrokeMgr *This,
        WPARAM wParam,
        LPARAM lParam,
        WINBOOL *pfEaten);

    HRESULT (STDMETHODCALLTYPE *KeyUp)(
        ITfKeystrokeMgr *This,
        WPARAM wParam,
        LPARAM lParam,
        WINBOOL *pfEaten);

    HRESULT (STDMETHODCALLTYPE *GetPreservedKey)(
        ITfKeystrokeMgr *This,
        ITfContext *pic,
        const TF_PRESERVEDKEY *pprekey,
        GUID *pguid);

    HRESULT (STDMETHODCALLTYPE *IsPreservedKey)(
        ITfKeystrokeMgr *This,
        REFGUID rguid,
        const TF_PRESERVEDKEY *pprekey,
        WINBOOL *pfRegistered);

    HRESULT (STDMETHODCALLTYPE *PreserveKey)(
        ITfKeystrokeMgr *This,
        TfClientId tid,
        REFGUID rguid,
        const TF_PRESERVEDKEY *prekey,
        const WCHAR *pchDesc,
        ULONG cchDesc);

    HRESULT (STDMETHODCALLTYPE *UnpreserveKey)(
        ITfKeystrokeMgr *This,
        REFGUID rguid,
        const TF_PRESERVEDKEY *pprekey);

    HRESULT (STDMETHODCALLTYPE *SetPreservedKeyDescription)(
        ITfKeystrokeMgr *This,
        REFGUID rguid,
        const WCHAR *pchDesc,
        ULONG cchDesc);

    HRESULT (STDMETHODCALLTYPE *GetPreservedKeyDescription)(
        ITfKeystrokeMgr *This,
        REFGUID rguid,
        BSTR *pbstrDesc);

    HRESULT (STDMETHODCALLTYPE *SimulatePreservedKey)(
        ITfKeystrokeMgr *This,
        ITfContext *pic,
        REFGUID rguid,
        WINBOOL *pfEaten);

    END_INTERFACE
} ITfKeystrokeMgrVtbl;

interface ITfKeystrokeMgr {
    CONST_VTBL ITfKeystrokeMgrVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITfKeystrokeMgr_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITfKeystrokeMgr_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITfKeystrokeMgr_Release(This) (This)->lpVtbl->Release(This)
/*** ITfKeystrokeMgr methods ***/
#define ITfKeystrokeMgr_AdviseKeyEventSink(This,tid,pSink,fForeground) (This)->lpVtbl->AdviseKeyEventSink(This,tid,pSink,fForeground)
#define ITfKeystrokeMgr_UnadviseKeyEventSink(This,tid) (This)->lpVtbl->UnadviseKeyEventSink(This,tid)
#define ITfKeystrokeMgr_GetForeground(This,pclsid) (This)->lpVtbl->GetForeground(This,pclsid)
#define ITfKeystrokeMgr_TestKeyDown(This,wParam,lParam,pfEaten) (This)->lpVtbl->TestKeyDown(This,wParam,lParam,pfEaten)
#define ITfKeystrokeMgr_TestKeyUp(This,wParam,lParam,pfEaten) (This)->lpVtbl->TestKeyUp(This,wParam,lParam,pfEaten)
#define ITfKeystrokeMgr_KeyDown(This,wParam,lParam,pfEaten) (This)->lpVtbl->KeyDown(This,wParam,lParam,pfEaten)
#define ITfKeystrokeMgr_KeyUp(This,wParam,lParam,pfEaten) (This)->lpVtbl->KeyUp(This,wParam,lParam,pfEaten)
#define ITfKeystrokeMgr_GetPreservedKey(This,pic,pprekey,pguid) (This)->lpVtbl->GetPreservedKey(This,pic,pprekey,pguid)
#define ITfKeystrokeMgr_IsPreservedKey(This,rguid,pprekey,pfRegistered) (This)->lpVtbl->IsPreservedKey(This,rguid,pprekey,pfRegistered)
#define ITfKeystrokeMgr_PreserveKey(This,tid,rguid,prekey,pchDesc,cchDesc) (This)->lpVtbl->PreserveKey(This,tid,rguid,prekey,pchDesc,cchDesc)
#define ITfKeystrokeMgr_UnpreserveKey(This,rguid,pprekey) (This)->lpVtbl->UnpreserveKey(This,rguid,pprekey)
#define ITfKeystrokeMgr_SetPreservedKeyDescription(This,rguid,pchDesc,cchDesc) (This)->lpVtbl->SetPreservedKeyDescription(This,rguid,pchDesc,cchDesc)
#define ITfKeystrokeMgr_GetPreservedKeyDescription(This,rguid,pbstrDesc) (This)->lpVtbl->GetPreservedKeyDescription(This,rguid,pbstrDesc)
#define ITfKeystrokeMgr_SimulatePreservedKey(This,pic,rguid,pfEaten) (This)->lpVtbl->SimulatePreservedKey(This,pic,rguid,pfEaten)
#else
/*** IUnknown methods ***/
static inline HRESULT ITfKeystrokeMgr_QueryInterface(ITfKeystrokeMgr* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITfKeystrokeMgr_AddRef(ITfKeystrokeMgr* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITfKeystrokeMgr_Release(ITfKeystrokeMgr* This) {
    return This->lpVtbl->Release(This);
}
/*** ITfKeystrokeMgr methods ***/
static inline HRESULT ITfKeystrokeMgr_AdviseKeyEventSink(ITfKeystrokeMgr* This,TfClientId tid,ITfKeyEventSink *pSink,WINBOOL fForeground) {
    return This->lpVtbl->AdviseKeyEventSink(This,tid,pSink,fForeground);
}
static inline HRESULT ITfKeystrokeMgr_UnadviseKeyEventSink(ITfKeystrokeMgr* This,TfClientId tid) {
    return This->lpVtbl->UnadviseKeyEventSink(This,tid);
}
static inline HRESULT ITfKeystrokeMgr_GetForeground(ITfKeystrokeMgr* This,CLSID *pclsid) {
    return This->lpVtbl->GetForeground(This,pclsid);
}
static inline HRESULT ITfKeystrokeMgr_TestKeyDown(ITfKeystrokeMgr* This,WPARAM wParam,LPARAM lParam,WINBOOL *pfEaten) {
    return This->lpVtbl->TestKeyDown(This,wParam,lParam,pfEaten);
}
static inline HRESULT ITfKeystrokeMgr_TestKeyUp(ITfKeystrokeMgr* This,WPARAM wParam,LPARAM lParam,WINBOOL *pfEaten) {
    return This->lpVtbl->TestKeyUp(This,wParam,lParam,pfEaten);
}
static inline HRESULT ITfKeystrokeMgr_KeyDown(ITfKeystrokeMgr* This,WPARAM wParam,LPARAM lParam,WINBOOL *pfEaten) {
    return This->lpVtbl->KeyDown(This,wParam,lParam,pfEaten);
}
static inline HRESULT ITfKeystrokeMgr_KeyUp(ITfKeystrokeMgr* This,WPARAM wParam,LPARAM lParam,WINBOOL *pfEaten) {
    return This->lpVtbl->KeyUp(This,wParam,lParam,pfEaten);
}
static inline HRESULT ITfKeystrokeMgr_GetPreservedKey(ITfKeystrokeMgr* This,ITfContext *pic,const TF_PRESERVEDKEY *pprekey,GUID *pguid) {
    return This->lpVtbl->GetPreservedKey(This,pic,pprekey,pguid);
}
static inline HRESULT ITfKeystrokeMgr_IsPreservedKey(ITfKeystrokeMgr* This,REFGUID rguid,const TF_PRESERVEDKEY *pprekey,WINBOOL *pfRegistered) {
    return This->lpVtbl->IsPreservedKey(This,rguid,pprekey,pfRegistered);
}
static inline HRESULT ITfKeystrokeMgr_PreserveKey(ITfKeystrokeMgr* This,TfClientId tid,REFGUID rguid,const TF_PRESERVEDKEY *prekey,const WCHAR *pchDesc,ULONG cchDesc) {
    return This->lpVtbl->PreserveKey(This,tid,rguid,prekey,pchDesc,cchDesc);
}
static inline HRESULT ITfKeystrokeMgr_UnpreserveKey(ITfKeystrokeMgr* This,REFGUID rguid,const TF_PRESERVEDKEY *pprekey) {
    return This->lpVtbl->UnpreserveKey(This,rguid,pprekey);
}
static inline HRESULT ITfKeystrokeMgr_SetPreservedKeyDescription(ITfKeystrokeMgr* This,REFGUID rguid,const WCHAR *pchDesc,ULONG cchDesc) {
    return This->lpVtbl->SetPreservedKeyDescription(This,rguid,pchDesc,cchDesc);
}
static inline HRESULT ITfKeystrokeMgr_GetPreservedKeyDescription(ITfKeystrokeMgr* This,REFGUID rguid,BSTR *pbstrDesc) {
    return This->lpVtbl->GetPreservedKeyDescription(This,rguid,pbstrDesc);
}
static inline HRESULT ITfKeystrokeMgr_SimulatePreservedKey(ITfKeystrokeMgr* This,ITfContext *pic,REFGUID rguid,WINBOOL *pfEaten) {
    return This->lpVtbl->SimulatePreservedKey(This,pic,rguid,pfEaten);
}
#endif
#endif

#endif


#endif  /* __ITfKeystrokeMgr_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITfKeyEventSink interface
 */
#ifndef __ITfKeyEventSink_INTERFACE_DEFINED__
#define __ITfKeyEventSink_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITfKeyEventSink, 0xaa80e7f5, 0x2021, 0x11d2, 0x93,0xe0, 0x00,0x60,0xb0,0x67,0xb8,0x6e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("aa80e7f5-2021-11d2-93e0-0060b067b86e")
ITfKeyEventSink : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE OnSetFocus(
        WINBOOL fForeground) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnTestKeyDown(
        ITfContext *pic,
        WPARAM wParam,
        LPARAM lParam,
        WINBOOL *pfEaten) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnTestKeyUp(
        ITfContext *pic,
        WPARAM wParam,
        LPARAM lParam,
        WINBOOL *pfEaten) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnKeyDown(
        ITfContext *pic,
        WPARAM wParam,
        LPARAM lParam,
        WINBOOL *pfEaten) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnKeyUp(
        ITfContext *pic,
        WPARAM wParam,
        LPARAM lParam,
        WINBOOL *pfEaten) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnPreservedKey(
        ITfContext *pic,
        REFGUID rguid,
        WINBOOL *pfEaten) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITfKeyEventSink, 0xaa80e7f5, 0x2021, 0x11d2, 0x93,0xe0, 0x00,0x60,0xb0,0x67,0xb8,0x6e)
#endif
#else
typedef struct ITfKeyEventSinkVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITfKeyEventSink *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITfKeyEventSink *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITfKeyEventSink *This);

    /*** ITfKeyEventSink methods ***/
    HRESULT (STDMETHODCALLTYPE *OnSetFocus)(
        ITfKeyEventSink *This,
        WINBOOL fForeground);

    HRESULT (STDMETHODCALLTYPE *OnTestKeyDown)(
        ITfKeyEventSink *This,
        ITfContext *pic,
        WPARAM wParam,
        LPARAM lParam,
        WINBOOL *pfEaten);

    HRESULT (STDMETHODCALLTYPE *OnTestKeyUp)(
        ITfKeyEventSink *This,
        ITfContext *pic,
        WPARAM wParam,
        LPARAM lParam,
        WINBOOL *pfEaten);

    HRESULT (STDMETHODCALLTYPE *OnKeyDown)(
        ITfKeyEventSink *This,
        ITfContext *pic,
        WPARAM wParam,
        LPARAM lParam,
        WINBOOL *pfEaten);

    HRESULT (STDMETHODCALLTYPE *OnKeyUp)(
        ITfKeyEventSink *This,
        ITfContext *pic,
        WPARAM wParam,
        LPARAM lParam,
        WINBOOL *pfEaten);

    HRESULT (STDMETHODCALLTYPE *OnPreservedKey)(
        ITfKeyEventSink *This,
        ITfContext *pic,
        REFGUID rguid,
        WINBOOL *pfEaten);

    END_INTERFACE
} ITfKeyEventSinkVtbl;

interface ITfKeyEventSink {
    CONST_VTBL ITfKeyEventSinkVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITfKeyEventSink_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITfKeyEventSink_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITfKeyEventSink_Release(This) (This)->lpVtbl->Release(This)
/*** ITfKeyEventSink methods ***/
#define ITfKeyEventSink_OnSetFocus(This,fForeground) (This)->lpVtbl->OnSetFocus(This,fForeground)
#define ITfKeyEventSink_OnTestKeyDown(This,pic,wParam,lParam,pfEaten) (This)->lpVtbl->OnTestKeyDown(This,pic,wParam,lParam,pfEaten)
#define ITfKeyEventSink_OnTestKeyUp(This,pic,wParam,lParam,pfEaten) (This)->lpVtbl->OnTestKeyUp(This,pic,wParam,lParam,pfEaten)
#define ITfKeyEventSink_OnKeyDown(This,pic,wParam,lParam,pfEaten) (This)->lpVtbl->OnKeyDown(This,pic,wParam,lParam,pfEaten)
#define ITfKeyEventSink_OnKeyUp(This,pic,wParam,lParam,pfEaten) (This)->lpVtbl->OnKeyUp(This,pic,wParam,lParam,pfEaten)
#define ITfKeyEventSink_OnPreservedKey(This,pic,rguid,pfEaten) (This)->lpVtbl->OnPreservedKey(This,pic,rguid,pfEaten)
#else
/*** IUnknown methods ***/
static inline HRESULT ITfKeyEventSink_QueryInterface(ITfKeyEventSink* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITfKeyEventSink_AddRef(ITfKeyEventSink* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITfKeyEventSink_Release(ITfKeyEventSink* This) {
    return This->lpVtbl->Release(This);
}
/*** ITfKeyEventSink methods ***/
static inline HRESULT ITfKeyEventSink_OnSetFocus(ITfKeyEventSink* This,WINBOOL fForeground) {
    return This->lpVtbl->OnSetFocus(This,fForeground);
}
static inline HRESULT ITfKeyEventSink_OnTestKeyDown(ITfKeyEventSink* This,ITfContext *pic,WPARAM wParam,LPARAM lParam,WINBOOL *pfEaten) {
    return This->lpVtbl->OnTestKeyDown(This,pic,wParam,lParam,pfEaten);
}
static inline HRESULT ITfKeyEventSink_OnTestKeyUp(ITfKeyEventSink* This,ITfContext *pic,WPARAM wParam,LPARAM lParam,WINBOOL *pfEaten) {
    return This->lpVtbl->OnTestKeyUp(This,pic,wParam,lParam,pfEaten);
}
static inline HRESULT ITfKeyEventSink_OnKeyDown(ITfKeyEventSink* This,ITfContext *pic,WPARAM wParam,LPARAM lParam,WINBOOL *pfEaten) {
    return This->lpVtbl->OnKeyDown(This,pic,wParam,lParam,pfEaten);
}
static inline HRESULT ITfKeyEventSink_OnKeyUp(ITfKeyEventSink* This,ITfContext *pic,WPARAM wParam,LPARAM lParam,WINBOOL *pfEaten) {
    return This->lpVtbl->OnKeyUp(This,pic,wParam,lParam,pfEaten);
}
static inline HRESULT ITfKeyEventSink_OnPreservedKey(ITfKeyEventSink* This,ITfContext *pic,REFGUID rguid,WINBOOL *pfEaten) {
    return This->lpVtbl->OnPreservedKey(This,pic,rguid,pfEaten);
}
#endif
#endif

#endif


#endif  /* __ITfKeyEventSink_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITfKeyTraceEventSink interface
 */
#ifndef __ITfKeyTraceEventSink_INTERFACE_DEFINED__
#define __ITfKeyTraceEventSink_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITfKeyTraceEventSink, 0x1cd4c13b, 0x1c36, 0x4191, 0xa7,0x0a, 0x7f,0x3e,0x61,0x1f,0x36,0x7d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("1cd4c13b-1c36-4191-a70a-7f3e611f367d")
ITfKeyTraceEventSink : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE OnKeyTraceDown(
        WPARAM wParam,
        LPARAM lParam) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnKeyTraceUp(
        WPARAM wParam,
        LPARAM lParam) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITfKeyTraceEventSink, 0x1cd4c13b, 0x1c36, 0x4191, 0xa7,0x0a, 0x7f,0x3e,0x61,0x1f,0x36,0x7d)
#endif
#else
typedef struct ITfKeyTraceEventSinkVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITfKeyTraceEventSink *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITfKeyTraceEventSink *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITfKeyTraceEventSink *This);

    /*** ITfKeyTraceEventSink methods ***/
    HRESULT (STDMETHODCALLTYPE *OnKeyTraceDown)(
        ITfKeyTraceEventSink *This,
        WPARAM wParam,
        LPARAM lParam);

    HRESULT (STDMETHODCALLTYPE *OnKeyTraceUp)(
        ITfKeyTraceEventSink *This,
        WPARAM wParam,
        LPARAM lParam);

    END_INTERFACE
} ITfKeyTraceEventSinkVtbl;

interface ITfKeyTraceEventSink {
    CONST_VTBL ITfKeyTraceEventSinkVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITfKeyTraceEventSink_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITfKeyTraceEventSink_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITfKeyTraceEventSink_Release(This) (This)->lpVtbl->Release(This)
/*** ITfKeyTraceEventSink methods ***/
#define ITfKeyTraceEventSink_OnKeyTraceDown(This,wParam,lParam) (This)->lpVtbl->OnKeyTraceDown(This,wParam,lParam)
#define ITfKeyTraceEventSink_OnKeyTraceUp(This,wParam,lParam) (This)->lpVtbl->OnKeyTraceUp(This,wParam,lParam)
#else
/*** IUnknown methods ***/
static inline HRESULT ITfKeyTraceEventSink_QueryInterface(ITfKeyTraceEventSink* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITfKeyTraceEventSink_AddRef(ITfKeyTraceEventSink* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITfKeyTraceEventSink_Release(ITfKeyTraceEventSink* This) {
    return This->lpVtbl->Release(This);
}
/*** ITfKeyTraceEventSink methods ***/
static inline HRESULT ITfKeyTraceEventSink_OnKeyTraceDown(ITfKeyTraceEventSink* This,WPARAM wParam,LPARAM lParam) {
    return This->lpVtbl->OnKeyTraceDown(This,wParam,lParam);
}
static inline HRESULT ITfKeyTraceEventSink_OnKeyTraceUp(ITfKeyTraceEventSink* This,WPARAM wParam,LPARAM lParam) {
    return This->lpVtbl->OnKeyTraceUp(This,wParam,lParam);
}
#endif
#endif

#endif


#endif  /* __ITfKeyTraceEventSink_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITfUIElementSink interface
 */
#ifndef __ITfUIElementSink_INTERFACE_DEFINED__
#define __ITfUIElementSink_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITfUIElementSink, 0xea1ea136, 0x19df, 0x11d7, 0xa6,0xd2, 0x00,0x06,0x5b,0x84,0x43,0x5c);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("ea1ea136-19df-11d7-a6d2-00065b84435c")
ITfUIElementSink : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE BeginUIElement(
        DWORD id,
        WINBOOL *show) = 0;

    virtual HRESULT STDMETHODCALLTYPE UpdateUIElement(
        DWORD id) = 0;

    virtual HRESULT STDMETHODCALLTYPE EndUIElement(
        DWORD id) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITfUIElementSink, 0xea1ea136, 0x19df, 0x11d7, 0xa6,0xd2, 0x00,0x06,0x5b,0x84,0x43,0x5c)
#endif
#else
typedef struct ITfUIElementSinkVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITfUIElementSink *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITfUIElementSink *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITfUIElementSink *This);

    /*** ITfUIElementSink methods ***/
    HRESULT (STDMETHODCALLTYPE *BeginUIElement)(
        ITfUIElementSink *This,
        DWORD id,
        WINBOOL *show);

    HRESULT (STDMETHODCALLTYPE *UpdateUIElement)(
        ITfUIElementSink *This,
        DWORD id);

    HRESULT (STDMETHODCALLTYPE *EndUIElement)(
        ITfUIElementSink *This,
        DWORD id);

    END_INTERFACE
} ITfUIElementSinkVtbl;

interface ITfUIElementSink {
    CONST_VTBL ITfUIElementSinkVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITfUIElementSink_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITfUIElementSink_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITfUIElementSink_Release(This) (This)->lpVtbl->Release(This)
/*** ITfUIElementSink methods ***/
#define ITfUIElementSink_BeginUIElement(This,id,show) (This)->lpVtbl->BeginUIElement(This,id,show)
#define ITfUIElementSink_UpdateUIElement(This,id) (This)->lpVtbl->UpdateUIElement(This,id)
#define ITfUIElementSink_EndUIElement(This,id) (This)->lpVtbl->EndUIElement(This,id)
#else
/*** IUnknown methods ***/
static inline HRESULT ITfUIElementSink_QueryInterface(ITfUIElementSink* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITfUIElementSink_AddRef(ITfUIElementSink* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITfUIElementSink_Release(ITfUIElementSink* This) {
    return This->lpVtbl->Release(This);
}
/*** ITfUIElementSink methods ***/
static inline HRESULT ITfUIElementSink_BeginUIElement(ITfUIElementSink* This,DWORD id,WINBOOL *show) {
    return This->lpVtbl->BeginUIElement(This,id,show);
}
static inline HRESULT ITfUIElementSink_UpdateUIElement(ITfUIElementSink* This,DWORD id) {
    return This->lpVtbl->UpdateUIElement(This,id);
}
static inline HRESULT ITfUIElementSink_EndUIElement(ITfUIElementSink* This,DWORD id) {
    return This->lpVtbl->EndUIElement(This,id);
}
#endif
#endif

#endif


#endif  /* __ITfUIElementSink_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITfMessagePump interface
 */
#ifndef __ITfMessagePump_INTERFACE_DEFINED__
#define __ITfMessagePump_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITfMessagePump, 0x8f1b8ad8, 0x0b6b, 0x4874, 0x90,0xc5, 0xbd,0x76,0x01,0x1e,0x8f,0x7c);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("8f1b8ad8-0b6b-4874-90c5-bd76011e8f7c")
ITfMessagePump : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE PeekMessageA(
        LPMSG pMsg,
        HWND hwnd,
        UINT wMsgFilterMin,
        UINT wMsgFilterMax,
        UINT wRemoveMsg,
        WINBOOL *pfResult) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMessageA(
        LPMSG pMsg,
        HWND hwnd,
        UINT wMsgFilterMin,
        UINT wMsgFilterMax,
        WINBOOL *pfResult) = 0;

    virtual HRESULT STDMETHODCALLTYPE PeekMessageW(
        LPMSG pMsg,
        HWND hwnd,
        UINT wMsgFilterMin,
        UINT wMsgFilterMax,
        UINT wRemoveMsg,
        WINBOOL *pfResult) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMessageW(
        LPMSG pMsg,
        HWND hwnd,
        UINT wMsgFilterMin,
        UINT wMsgFilterMax,
        WINBOOL *pfResult) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITfMessagePump, 0x8f1b8ad8, 0x0b6b, 0x4874, 0x90,0xc5, 0xbd,0x76,0x01,0x1e,0x8f,0x7c)
#endif
#else
typedef struct ITfMessagePumpVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITfMessagePump *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITfMessagePump *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITfMessagePump *This);

    /*** ITfMessagePump methods ***/
    HRESULT (STDMETHODCALLTYPE *PeekMessageA)(
        ITfMessagePump *This,
        LPMSG pMsg,
        HWND hwnd,
        UINT wMsgFilterMin,
        UINT wMsgFilterMax,
        UINT wRemoveMsg,
        WINBOOL *pfResult);

    HRESULT (STDMETHODCALLTYPE *GetMessageA)(
        ITfMessagePump *This,
        LPMSG pMsg,
        HWND hwnd,
        UINT wMsgFilterMin,
        UINT wMsgFilterMax,
        WINBOOL *pfResult);

    HRESULT (STDMETHODCALLTYPE *PeekMessageW)(
        ITfMessagePump *This,
        LPMSG pMsg,
        HWND hwnd,
        UINT wMsgFilterMin,
        UINT wMsgFilterMax,
        UINT wRemoveMsg,
        WINBOOL *pfResult);

    HRESULT (STDMETHODCALLTYPE *GetMessageW)(
        ITfMessagePump *This,
        LPMSG pMsg,
        HWND hwnd,
        UINT wMsgFilterMin,
        UINT wMsgFilterMax,
        WINBOOL *pfResult);

    END_INTERFACE
} ITfMessagePumpVtbl;

interface ITfMessagePump {
    CONST_VTBL ITfMessagePumpVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITfMessagePump_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITfMessagePump_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITfMessagePump_Release(This) (This)->lpVtbl->Release(This)
/*** ITfMessagePump methods ***/
#define ITfMessagePump_PeekMessageA(This,pMsg,hwnd,wMsgFilterMin,wMsgFilterMax,wRemoveMsg,pfResult) (This)->lpVtbl->PeekMessageA(This,pMsg,hwnd,wMsgFilterMin,wMsgFilterMax,wRemoveMsg,pfResult)
#define ITfMessagePump_GetMessageA(This,pMsg,hwnd,wMsgFilterMin,wMsgFilterMax,pfResult) (This)->lpVtbl->GetMessageA(This,pMsg,hwnd,wMsgFilterMin,wMsgFilterMax,pfResult)
#define ITfMessagePump_PeekMessageW(This,pMsg,hwnd,wMsgFilterMin,wMsgFilterMax,wRemoveMsg,pfResult) (This)->lpVtbl->PeekMessageW(This,pMsg,hwnd,wMsgFilterMin,wMsgFilterMax,wRemoveMsg,pfResult)
#define ITfMessagePump_GetMessageW(This,pMsg,hwnd,wMsgFilterMin,wMsgFilterMax,pfResult) (This)->lpVtbl->GetMessageW(This,pMsg,hwnd,wMsgFilterMin,wMsgFilterMax,pfResult)
#else
/*** IUnknown methods ***/
static inline HRESULT ITfMessagePump_QueryInterface(ITfMessagePump* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITfMessagePump_AddRef(ITfMessagePump* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITfMessagePump_Release(ITfMessagePump* This) {
    return This->lpVtbl->Release(This);
}
/*** ITfMessagePump methods ***/
static inline HRESULT ITfMessagePump_PeekMessageA(ITfMessagePump* This,LPMSG pMsg,HWND hwnd,UINT wMsgFilterMin,UINT wMsgFilterMax,UINT wRemoveMsg,WINBOOL *pfResult) {
    return This->lpVtbl->PeekMessageA(This,pMsg,hwnd,wMsgFilterMin,wMsgFilterMax,wRemoveMsg,pfResult);
}
static inline HRESULT ITfMessagePump_GetMessageA(ITfMessagePump* This,LPMSG pMsg,HWND hwnd,UINT wMsgFilterMin,UINT wMsgFilterMax,WINBOOL *pfResult) {
    return This->lpVtbl->GetMessageA(This,pMsg,hwnd,wMsgFilterMin,wMsgFilterMax,pfResult);
}
static inline HRESULT ITfMessagePump_PeekMessageW(ITfMessagePump* This,LPMSG pMsg,HWND hwnd,UINT wMsgFilterMin,UINT wMsgFilterMax,UINT wRemoveMsg,WINBOOL *pfResult) {
    return This->lpVtbl->PeekMessageW(This,pMsg,hwnd,wMsgFilterMin,wMsgFilterMax,wRemoveMsg,pfResult);
}
static inline HRESULT ITfMessagePump_GetMessageW(ITfMessagePump* This,LPMSG pMsg,HWND hwnd,UINT wMsgFilterMin,UINT wMsgFilterMax,WINBOOL *pfResult) {
    return This->lpVtbl->GetMessageW(This,pMsg,hwnd,wMsgFilterMin,wMsgFilterMax,pfResult);
}
#endif
#endif

#endif


#endif  /* __ITfMessagePump_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITfClientId interface
 */
#ifndef __ITfClientId_INTERFACE_DEFINED__
#define __ITfClientId_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITfClientId, 0xd60a7b49, 0x1b9f, 0x4be2, 0xb7,0x02, 0x47,0xe9,0xdc,0x05,0xde,0xc3);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d60a7b49-1b9f-4be2-b702-47e9dc05dec3")
ITfClientId : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetClientId(
        REFCLSID rclsid,
        TfClientId *ptid) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITfClientId, 0xd60a7b49, 0x1b9f, 0x4be2, 0xb7,0x02, 0x47,0xe9,0xdc,0x05,0xde,0xc3)
#endif
#else
typedef struct ITfClientIdVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITfClientId *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITfClientId *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITfClientId *This);

    /*** ITfClientId methods ***/
    HRESULT (STDMETHODCALLTYPE *GetClientId)(
        ITfClientId *This,
        REFCLSID rclsid,
        TfClientId *ptid);

    END_INTERFACE
} ITfClientIdVtbl;

interface ITfClientId {
    CONST_VTBL ITfClientIdVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITfClientId_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITfClientId_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITfClientId_Release(This) (This)->lpVtbl->Release(This)
/*** ITfClientId methods ***/
#define ITfClientId_GetClientId(This,rclsid,ptid) (This)->lpVtbl->GetClientId(This,rclsid,ptid)
#else
/*** IUnknown methods ***/
static inline HRESULT ITfClientId_QueryInterface(ITfClientId* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITfClientId_AddRef(ITfClientId* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITfClientId_Release(ITfClientId* This) {
    return This->lpVtbl->Release(This);
}
/*** ITfClientId methods ***/
static inline HRESULT ITfClientId_GetClientId(ITfClientId* This,REFCLSID rclsid,TfClientId *ptid) {
    return This->lpVtbl->GetClientId(This,rclsid,ptid);
}
#endif
#endif

#endif


#endif  /* __ITfClientId_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITfLanguageProfileNotifySink interface
 */
#ifndef __ITfLanguageProfileNotifySink_INTERFACE_DEFINED__
#define __ITfLanguageProfileNotifySink_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITfLanguageProfileNotifySink, 0x43c9fe15, 0xf494, 0x4c17, 0x9d,0xe2, 0xb8,0xa4,0xac,0x35,0x0a,0xa8);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("43c9fe15-f494-4c17-9de2-b8a4ac350aa8")
ITfLanguageProfileNotifySink : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE OnLanguageChange(
        LANGID langid,
        WINBOOL *pfAccept) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnLanguageChanged(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITfLanguageProfileNotifySink, 0x43c9fe15, 0xf494, 0x4c17, 0x9d,0xe2, 0xb8,0xa4,0xac,0x35,0x0a,0xa8)
#endif
#else
typedef struct ITfLanguageProfileNotifySinkVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITfLanguageProfileNotifySink *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITfLanguageProfileNotifySink *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITfLanguageProfileNotifySink *This);

    /*** ITfLanguageProfileNotifySink methods ***/
    HRESULT (STDMETHODCALLTYPE *OnLanguageChange)(
        ITfLanguageProfileNotifySink *This,
        LANGID langid,
        WINBOOL *pfAccept);

    HRESULT (STDMETHODCALLTYPE *OnLanguageChanged)(
        ITfLanguageProfileNotifySink *This);

    END_INTERFACE
} ITfLanguageProfileNotifySinkVtbl;

interface ITfLanguageProfileNotifySink {
    CONST_VTBL ITfLanguageProfileNotifySinkVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITfLanguageProfileNotifySink_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITfLanguageProfileNotifySink_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITfLanguageProfileNotifySink_Release(This) (This)->lpVtbl->Release(This)
/*** ITfLanguageProfileNotifySink methods ***/
#define ITfLanguageProfileNotifySink_OnLanguageChange(This,langid,pfAccept) (This)->lpVtbl->OnLanguageChange(This,langid,pfAccept)
#define ITfLanguageProfileNotifySink_OnLanguageChanged(This) (This)->lpVtbl->OnLanguageChanged(This)
#else
/*** IUnknown methods ***/
static inline HRESULT ITfLanguageProfileNotifySink_QueryInterface(ITfLanguageProfileNotifySink* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITfLanguageProfileNotifySink_AddRef(ITfLanguageProfileNotifySink* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITfLanguageProfileNotifySink_Release(ITfLanguageProfileNotifySink* This) {
    return This->lpVtbl->Release(This);
}
/*** ITfLanguageProfileNotifySink methods ***/
static inline HRESULT ITfLanguageProfileNotifySink_OnLanguageChange(ITfLanguageProfileNotifySink* This,LANGID langid,WINBOOL *pfAccept) {
    return This->lpVtbl->OnLanguageChange(This,langid,pfAccept);
}
static inline HRESULT ITfLanguageProfileNotifySink_OnLanguageChanged(ITfLanguageProfileNotifySink* This) {
    return This->lpVtbl->OnLanguageChanged(This);
}
#endif
#endif

#endif


#endif  /* __ITfLanguageProfileNotifySink_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITfEditSession interface
 */
#ifndef __ITfEditSession_INTERFACE_DEFINED__
#define __ITfEditSession_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITfEditSession, 0xaa80e803, 0x2021, 0x11d2, 0x93,0xe0, 0x00,0x60,0xb0,0x67,0xb8,0x6e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("aa80e803-2021-11d2-93e0-0060b067b86e")
ITfEditSession : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE DoEditSession(
        TfEditCookie ec) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITfEditSession, 0xaa80e803, 0x2021, 0x11d2, 0x93,0xe0, 0x00,0x60,0xb0,0x67,0xb8,0x6e)
#endif
#else
typedef struct ITfEditSessionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITfEditSession *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITfEditSession *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITfEditSession *This);

    /*** ITfEditSession methods ***/
    HRESULT (STDMETHODCALLTYPE *DoEditSession)(
        ITfEditSession *This,
        TfEditCookie ec);

    END_INTERFACE
} ITfEditSessionVtbl;

interface ITfEditSession {
    CONST_VTBL ITfEditSessionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITfEditSession_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITfEditSession_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITfEditSession_Release(This) (This)->lpVtbl->Release(This)
/*** ITfEditSession methods ***/
#define ITfEditSession_DoEditSession(This,ec) (This)->lpVtbl->DoEditSession(This,ec)
#else
/*** IUnknown methods ***/
static inline HRESULT ITfEditSession_QueryInterface(ITfEditSession* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITfEditSession_AddRef(ITfEditSession* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITfEditSession_Release(ITfEditSession* This) {
    return This->lpVtbl->Release(This);
}
/*** ITfEditSession methods ***/
static inline HRESULT ITfEditSession_DoEditSession(ITfEditSession* This,TfEditCookie ec) {
    return This->lpVtbl->DoEditSession(This,ec);
}
#endif
#endif

#endif


#endif  /* __ITfEditSession_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITfRange interface
 */
#ifndef __ITfRange_INTERFACE_DEFINED__
#define __ITfRange_INTERFACE_DEFINED__

#define TF_CHAR_EMBEDDED (TS_CHAR_EMBEDDED)

typedef enum __WIDL_msctf_generated_name_00000017 {
    TF_GRAVITY_BACKWARD = 0,
    TF_GRAVITY_FORWARD = 1
} TfGravity;
typedef enum __WIDL_msctf_generated_name_00000018 {
    TF_SD_BACKWARD = 0,
    TF_SD_FORWARD = 1
} TfShiftDir;
#define TF_HF_OBJECT (1)

#define TF_TF_MOVESTART (1)

#define TF_TF_IGNOREEND (2)

#define TF_ST_CORRECTION (1)

#define TF_IE_CORRECTION (1)

typedef struct TF_HALTCOND {
    ITfRange *pHaltRange;
    TfAnchor aHaltPos;
    DWORD dwFlags;
} TF_HALTCOND;
DEFINE_GUID(IID_ITfRange, 0xaa80e7ff, 0x2021, 0x11d2, 0x93,0xe0, 0x00,0x60,0xb0,0x67,0xb8,0x6e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("aa80e7ff-2021-11d2-93e0-0060b067b86e")
ITfRange : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetText(
        TfEditCookie ec,
        DWORD dwFlags,
        WCHAR *pchText,
        ULONG cchMax,
        ULONG *pcch) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetText(
        TfEditCookie ec,
        DWORD dwFlags,
        const WCHAR *pchText,
        LONG cch) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFormattedText(
        TfEditCookie ec,
        IDataObject **ppDataObject) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetEmbedded(
        TfEditCookie ec,
        REFGUID rguidService,
        REFIID riid,
        IUnknown **ppunk) = 0;

    virtual HRESULT STDMETHODCALLTYPE InsertEmbedded(
        TfEditCookie ec,
        DWORD dwFlags,
        IDataObject *pDataObject) = 0;

    virtual HRESULT STDMETHODCALLTYPE ShiftStart(
        TfEditCookie ec,
        LONG cchReq,
        LONG *pcch,
        const TF_HALTCOND *pHalt) = 0;

    virtual HRESULT STDMETHODCALLTYPE ShiftEnd(
        TfEditCookie ec,
        LONG cchReq,
        LONG *pcch,
        const TF_HALTCOND *pHalt) = 0;

    virtual HRESULT STDMETHODCALLTYPE ShiftStartToRange(
        TfEditCookie ec,
        ITfRange *pRange,
        TfAnchor aPos) = 0;

    virtual HRESULT STDMETHODCALLTYPE ShiftEndToRange(
        TfEditCookie ec,
        ITfRange *pRange,
        TfAnchor aPos) = 0;

    virtual HRESULT STDMETHODCALLTYPE ShiftStartRegion(
        TfEditCookie ec,
        TfShiftDir dir,
        WINBOOL *pfNoRegion) = 0;

    virtual HRESULT STDMETHODCALLTYPE ShiftEndRegion(
        TfEditCookie ec,
        TfShiftDir dir,
        WINBOOL *pfNoRegion) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsEmpty(
        TfEditCookie ec,
        WINBOOL *pfEmpty) = 0;

    virtual HRESULT STDMETHODCALLTYPE Collapse(
        TfEditCookie ec,
        TfAnchor aPos) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsEqualStart(
        TfEditCookie ec,
        ITfRange *pWith,
        TfAnchor aPos,
        WINBOOL *pfEqual) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsEqualEnd(
        TfEditCookie ec,
        ITfRange *pWith,
        TfAnchor aPos,
        WINBOOL *pfEqual) = 0;

    virtual HRESULT STDMETHODCALLTYPE CompareStart(
        TfEditCookie ec,
        ITfRange *pWith,
        TfAnchor aPos,
        LONG *plResult) = 0;

    virtual HRESULT STDMETHODCALLTYPE CompareEnd(
        TfEditCookie ec,
        ITfRange *pWith,
        TfAnchor aPos,
        LONG *plResult) = 0;

    virtual HRESULT STDMETHODCALLTYPE AdjustForInsert(
        TfEditCookie ec,
        ULONG cchInsert,
        WINBOOL *pfInsertOk) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetGravity(
        TfGravity *pgStart,
        TfGravity *pgEnd) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetGravity(
        TfEditCookie ec,
        TfGravity gStart,
        TfGravity gEnd) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        ITfRange **ppClone) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetContext(
        ITfContext **ppContext) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITfRange, 0xaa80e7ff, 0x2021, 0x11d2, 0x93,0xe0, 0x00,0x60,0xb0,0x67,0xb8,0x6e)
#endif
#else
typedef struct ITfRangeVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITfRange *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITfRange *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITfRange *This);

    /*** ITfRange methods ***/
    HRESULT (STDMETHODCALLTYPE *GetText)(
        ITfRange *This,
        TfEditCookie ec,
        DWORD dwFlags,
        WCHAR *pchText,
        ULONG cchMax,
        ULONG *pcch);

    HRESULT (STDMETHODCALLTYPE *SetText)(
        ITfRange *This,
        TfEditCookie ec,
        DWORD dwFlags,
        const WCHAR *pchText,
        LONG cch);

    HRESULT (STDMETHODCALLTYPE *GetFormattedText)(
        ITfRange *This,
        TfEditCookie ec,
        IDataObject **ppDataObject);

    HRESULT (STDMETHODCALLTYPE *GetEmbedded)(
        ITfRange *This,
        TfEditCookie ec,
        REFGUID rguidService,
        REFIID riid,
        IUnknown **ppunk);

    HRESULT (STDMETHODCALLTYPE *InsertEmbedded)(
        ITfRange *This,
        TfEditCookie ec,
        DWORD dwFlags,
        IDataObject *pDataObject);

    HRESULT (STDMETHODCALLTYPE *ShiftStart)(
        ITfRange *This,
        TfEditCookie ec,
        LONG cchReq,
        LONG *pcch,
        const TF_HALTCOND *pHalt);

    HRESULT (STDMETHODCALLTYPE *ShiftEnd)(
        ITfRange *This,
        TfEditCookie ec,
        LONG cchReq,
        LONG *pcch,
        const TF_HALTCOND *pHalt);

    HRESULT (STDMETHODCALLTYPE *ShiftStartToRange)(
        ITfRange *This,
        TfEditCookie ec,
        ITfRange *pRange,
        TfAnchor aPos);

    HRESULT (STDMETHODCALLTYPE *ShiftEndToRange)(
        ITfRange *This,
        TfEditCookie ec,
        ITfRange *pRange,
        TfAnchor aPos);

    HRESULT (STDMETHODCALLTYPE *ShiftStartRegion)(
        ITfRange *This,
        TfEditCookie ec,
        TfShiftDir dir,
        WINBOOL *pfNoRegion);

    HRESULT (STDMETHODCALLTYPE *ShiftEndRegion)(
        ITfRange *This,
        TfEditCookie ec,
        TfShiftDir dir,
        WINBOOL *pfNoRegion);

    HRESULT (STDMETHODCALLTYPE *IsEmpty)(
        ITfRange *This,
        TfEditCookie ec,
        WINBOOL *pfEmpty);

    HRESULT (STDMETHODCALLTYPE *Collapse)(
        ITfRange *This,
        TfEditCookie ec,
        TfAnchor aPos);

    HRESULT (STDMETHODCALLTYPE *IsEqualStart)(
        ITfRange *This,
        TfEditCookie ec,
        ITfRange *pWith,
        TfAnchor aPos,
        WINBOOL *pfEqual);

    HRESULT (STDMETHODCALLTYPE *IsEqualEnd)(
        ITfRange *This,
        TfEditCookie ec,
        ITfRange *pWith,
        TfAnchor aPos,
        WINBOOL *pfEqual);

    HRESULT (STDMETHODCALLTYPE *CompareStart)(
        ITfRange *This,
        TfEditCookie ec,
        ITfRange *pWith,
        TfAnchor aPos,
        LONG *plResult);

    HRESULT (STDMETHODCALLTYPE *CompareEnd)(
        ITfRange *This,
        TfEditCookie ec,
        ITfRange *pWith,
        TfAnchor aPos,
        LONG *plResult);

    HRESULT (STDMETHODCALLTYPE *AdjustForInsert)(
        ITfRange *This,
        TfEditCookie ec,
        ULONG cchInsert,
        WINBOOL *pfInsertOk);

    HRESULT (STDMETHODCALLTYPE *GetGravity)(
        ITfRange *This,
        TfGravity *pgStart,
        TfGravity *pgEnd);

    HRESULT (STDMETHODCALLTYPE *SetGravity)(
        ITfRange *This,
        TfEditCookie ec,
        TfGravity gStart,
        TfGravity gEnd);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        ITfRange *This,
        ITfRange **ppClone);

    HRESULT (STDMETHODCALLTYPE *GetContext)(
        ITfRange *This,
        ITfContext **ppContext);

    END_INTERFACE
} ITfRangeVtbl;

interface ITfRange {
    CONST_VTBL ITfRangeVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITfRange_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITfRange_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITfRange_Release(This) (This)->lpVtbl->Release(This)
/*** ITfRange methods ***/
#define ITfRange_GetText(This,ec,dwFlags,pchText,cchMax,pcch) (This)->lpVtbl->GetText(This,ec,dwFlags,pchText,cchMax,pcch)
#define ITfRange_SetText(This,ec,dwFlags,pchText,cch) (This)->lpVtbl->SetText(This,ec,dwFlags,pchText,cch)
#define ITfRange_GetFormattedText(This,ec,ppDataObject) (This)->lpVtbl->GetFormattedText(This,ec,ppDataObject)
#define ITfRange_GetEmbedded(This,ec,rguidService,riid,ppunk) (This)->lpVtbl->GetEmbedded(This,ec,rguidService,riid,ppunk)
#define ITfRange_InsertEmbedded(This,ec,dwFlags,pDataObject) (This)->lpVtbl->InsertEmbedded(This,ec,dwFlags,pDataObject)
#define ITfRange_ShiftStart(This,ec,cchReq,pcch,pHalt) (This)->lpVtbl->ShiftStart(This,ec,cchReq,pcch,pHalt)
#define ITfRange_ShiftEnd(This,ec,cchReq,pcch,pHalt) (This)->lpVtbl->ShiftEnd(This,ec,cchReq,pcch,pHalt)
#define ITfRange_ShiftStartToRange(This,ec,pRange,aPos) (This)->lpVtbl->ShiftStartToRange(This,ec,pRange,aPos)
#define ITfRange_ShiftEndToRange(This,ec,pRange,aPos) (This)->lpVtbl->ShiftEndToRange(This,ec,pRange,aPos)
#define ITfRange_ShiftStartRegion(This,ec,dir,pfNoRegion) (This)->lpVtbl->ShiftStartRegion(This,ec,dir,pfNoRegion)
#define ITfRange_ShiftEndRegion(This,ec,dir,pfNoRegion) (This)->lpVtbl->ShiftEndRegion(This,ec,dir,pfNoRegion)
#define ITfRange_IsEmpty(This,ec,pfEmpty) (This)->lpVtbl->IsEmpty(This,ec,pfEmpty)
#define ITfRange_Collapse(This,ec,aPos) (This)->lpVtbl->Collapse(This,ec,aPos)
#define ITfRange_IsEqualStart(This,ec,pWith,aPos,pfEqual) (This)->lpVtbl->IsEqualStart(This,ec,pWith,aPos,pfEqual)
#define ITfRange_IsEqualEnd(This,ec,pWith,aPos,pfEqual) (This)->lpVtbl->IsEqualEnd(This,ec,pWith,aPos,pfEqual)
#define ITfRange_CompareStart(This,ec,pWith,aPos,plResult) (This)->lpVtbl->CompareStart(This,ec,pWith,aPos,plResult)
#define ITfRange_CompareEnd(This,ec,pWith,aPos,plResult) (This)->lpVtbl->CompareEnd(This,ec,pWith,aPos,plResult)
#define ITfRange_AdjustForInsert(This,ec,cchInsert,pfInsertOk) (This)->lpVtbl->AdjustForInsert(This,ec,cchInsert,pfInsertOk)
#define ITfRange_GetGravity(This,pgStart,pgEnd) (This)->lpVtbl->GetGravity(This,pgStart,pgEnd)
#define ITfRange_SetGravity(This,ec,gStart,gEnd) (This)->lpVtbl->SetGravity(This,ec,gStart,gEnd)
#define ITfRange_Clone(This,ppClone) (This)->lpVtbl->Clone(This,ppClone)
#define ITfRange_GetContext(This,ppContext) (This)->lpVtbl->GetContext(This,ppContext)
#else
/*** IUnknown methods ***/
static inline HRESULT ITfRange_QueryInterface(ITfRange* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITfRange_AddRef(ITfRange* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITfRange_Release(ITfRange* This) {
    return This->lpVtbl->Release(This);
}
/*** ITfRange methods ***/
static inline HRESULT ITfRange_GetText(ITfRange* This,TfEditCookie ec,DWORD dwFlags,WCHAR *pchText,ULONG cchMax,ULONG *pcch) {
    return This->lpVtbl->GetText(This,ec,dwFlags,pchText,cchMax,pcch);
}
static inline HRESULT ITfRange_SetText(ITfRange* This,TfEditCookie ec,DWORD dwFlags,const WCHAR *pchText,LONG cch) {
    return This->lpVtbl->SetText(This,ec,dwFlags,pchText,cch);
}
static inline HRESULT ITfRange_GetFormattedText(ITfRange* This,TfEditCookie ec,IDataObject **ppDataObject) {
    return This->lpVtbl->GetFormattedText(This,ec,ppDataObject);
}
static inline HRESULT ITfRange_GetEmbedded(ITfRange* This,TfEditCookie ec,REFGUID rguidService,REFIID riid,IUnknown **ppunk) {
    return This->lpVtbl->GetEmbedded(This,ec,rguidService,riid,ppunk);
}
static inline HRESULT ITfRange_InsertEmbedded(ITfRange* This,TfEditCookie ec,DWORD dwFlags,IDataObject *pDataObject) {
    return This->lpVtbl->InsertEmbedded(This,ec,dwFlags,pDataObject);
}
static inline HRESULT ITfRange_ShiftStart(ITfRange* This,TfEditCookie ec,LONG cchReq,LONG *pcch,const TF_HALTCOND *pHalt) {
    return This->lpVtbl->ShiftStart(This,ec,cchReq,pcch,pHalt);
}
static inline HRESULT ITfRange_ShiftEnd(ITfRange* This,TfEditCookie ec,LONG cchReq,LONG *pcch,const TF_HALTCOND *pHalt) {
    return This->lpVtbl->ShiftEnd(This,ec,cchReq,pcch,pHalt);
}
static inline HRESULT ITfRange_ShiftStartToRange(ITfRange* This,TfEditCookie ec,ITfRange *pRange,TfAnchor aPos) {
    return This->lpVtbl->ShiftStartToRange(This,ec,pRange,aPos);
}
static inline HRESULT ITfRange_ShiftEndToRange(ITfRange* This,TfEditCookie ec,ITfRange *pRange,TfAnchor aPos) {
    return This->lpVtbl->ShiftEndToRange(This,ec,pRange,aPos);
}
static inline HRESULT ITfRange_ShiftStartRegion(ITfRange* This,TfEditCookie ec,TfShiftDir dir,WINBOOL *pfNoRegion) {
    return This->lpVtbl->ShiftStartRegion(This,ec,dir,pfNoRegion);
}
static inline HRESULT ITfRange_ShiftEndRegion(ITfRange* This,TfEditCookie ec,TfShiftDir dir,WINBOOL *pfNoRegion) {
    return This->lpVtbl->ShiftEndRegion(This,ec,dir,pfNoRegion);
}
static inline HRESULT ITfRange_IsEmpty(ITfRange* This,TfEditCookie ec,WINBOOL *pfEmpty) {
    return This->lpVtbl->IsEmpty(This,ec,pfEmpty);
}
static inline HRESULT ITfRange_Collapse(ITfRange* This,TfEditCookie ec,TfAnchor aPos) {
    return This->lpVtbl->Collapse(This,ec,aPos);
}
static inline HRESULT ITfRange_IsEqualStart(ITfRange* This,TfEditCookie ec,ITfRange *pWith,TfAnchor aPos,WINBOOL *pfEqual) {
    return This->lpVtbl->IsEqualStart(This,ec,pWith,aPos,pfEqual);
}
static inline HRESULT ITfRange_IsEqualEnd(ITfRange* This,TfEditCookie ec,ITfRange *pWith,TfAnchor aPos,WINBOOL *pfEqual) {
    return This->lpVtbl->IsEqualEnd(This,ec,pWith,aPos,pfEqual);
}
static inline HRESULT ITfRange_CompareStart(ITfRange* This,TfEditCookie ec,ITfRange *pWith,TfAnchor aPos,LONG *plResult) {
    return This->lpVtbl->CompareStart(This,ec,pWith,aPos,plResult);
}
static inline HRESULT ITfRange_CompareEnd(ITfRange* This,TfEditCookie ec,ITfRange *pWith,TfAnchor aPos,LONG *plResult) {
    return This->lpVtbl->CompareEnd(This,ec,pWith,aPos,plResult);
}
static inline HRESULT ITfRange_AdjustForInsert(ITfRange* This,TfEditCookie ec,ULONG cchInsert,WINBOOL *pfInsertOk) {
    return This->lpVtbl->AdjustForInsert(This,ec,cchInsert,pfInsertOk);
}
static inline HRESULT ITfRange_GetGravity(ITfRange* This,TfGravity *pgStart,TfGravity *pgEnd) {
    return This->lpVtbl->GetGravity(This,pgStart,pgEnd);
}
static inline HRESULT ITfRange_SetGravity(ITfRange* This,TfEditCookie ec,TfGravity gStart,TfGravity gEnd) {
    return This->lpVtbl->SetGravity(This,ec,gStart,gEnd);
}
static inline HRESULT ITfRange_Clone(ITfRange* This,ITfRange **ppClone) {
    return This->lpVtbl->Clone(This,ppClone);
}
static inline HRESULT ITfRange_GetContext(ITfRange* This,ITfContext **ppContext) {
    return This->lpVtbl->GetContext(This,ppContext);
}
#endif
#endif

#endif


#endif  /* __ITfRange_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITfRangeACP interface
 */
#ifndef __ITfRangeACP_INTERFACE_DEFINED__
#define __ITfRangeACP_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITfRangeACP, 0x057a6296, 0x029b, 0x4154, 0xb7,0x9a, 0x0d,0x46,0x1d,0x4e,0xa9,0x4c);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("057a6296-029b-4154-b79a-0d461d4ea94c")
ITfRangeACP : public ITfRange
{
    virtual HRESULT STDMETHODCALLTYPE GetExtent(
        LONG *pacpAnchor,
        LONG *pcch) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetExtent(
        LONG acpAnchor,
        LONG cch) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITfRangeACP, 0x057a6296, 0x029b, 0x4154, 0xb7,0x9a, 0x0d,0x46,0x1d,0x4e,0xa9,0x4c)
#endif
#else
typedef struct ITfRangeACPVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITfRangeACP *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITfRangeACP *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITfRangeACP *This);

    /*** ITfRange methods ***/
    HRESULT (STDMETHODCALLTYPE *GetText)(
        ITfRangeACP *This,
        TfEditCookie ec,
        DWORD dwFlags,
        WCHAR *pchText,
        ULONG cchMax,
        ULONG *pcch);

    HRESULT (STDMETHODCALLTYPE *SetText)(
        ITfRangeACP *This,
        TfEditCookie ec,
        DWORD dwFlags,
        const WCHAR *pchText,
        LONG cch);

    HRESULT (STDMETHODCALLTYPE *GetFormattedText)(
        ITfRangeACP *This,
        TfEditCookie ec,
        IDataObject **ppDataObject);

    HRESULT (STDMETHODCALLTYPE *GetEmbedded)(
        ITfRangeACP *This,
        TfEditCookie ec,
        REFGUID rguidService,
        REFIID riid,
        IUnknown **ppunk);

    HRESULT (STDMETHODCALLTYPE *InsertEmbedded)(
        ITfRangeACP *This,
        TfEditCookie ec,
        DWORD dwFlags,
        IDataObject *pDataObject);

    HRESULT (STDMETHODCALLTYPE *ShiftStart)(
        ITfRangeACP *This,
        TfEditCookie ec,
        LONG cchReq,
        LONG *pcch,
        const TF_HALTCOND *pHalt);

    HRESULT (STDMETHODCALLTYPE *ShiftEnd)(
        ITfRangeACP *This,
        TfEditCookie ec,
        LONG cchReq,
        LONG *pcch,
        const TF_HALTCOND *pHalt);

    HRESULT (STDMETHODCALLTYPE *ShiftStartToRange)(
        ITfRangeACP *This,
        TfEditCookie ec,
        ITfRange *pRange,
        TfAnchor aPos);

    HRESULT (STDMETHODCALLTYPE *ShiftEndToRange)(
        ITfRangeACP *This,
        TfEditCookie ec,
        ITfRange *pRange,
        TfAnchor aPos);

    HRESULT (STDMETHODCALLTYPE *ShiftStartRegion)(
        ITfRangeACP *This,
        TfEditCookie ec,
        TfShiftDir dir,
        WINBOOL *pfNoRegion);

    HRESULT (STDMETHODCALLTYPE *ShiftEndRegion)(
        ITfRangeACP *This,
        TfEditCookie ec,
        TfShiftDir dir,
        WINBOOL *pfNoRegion);

    HRESULT (STDMETHODCALLTYPE *IsEmpty)(
        ITfRangeACP *This,
        TfEditCookie ec,
        WINBOOL *pfEmpty);

    HRESULT (STDMETHODCALLTYPE *Collapse)(
        ITfRangeACP *This,
        TfEditCookie ec,
        TfAnchor aPos);

    HRESULT (STDMETHODCALLTYPE *IsEqualStart)(
        ITfRangeACP *This,
        TfEditCookie ec,
        ITfRange *pWith,
        TfAnchor aPos,
        WINBOOL *pfEqual);

    HRESULT (STDMETHODCALLTYPE *IsEqualEnd)(
        ITfRangeACP *This,
        TfEditCookie ec,
        ITfRange *pWith,
        TfAnchor aPos,
        WINBOOL *pfEqual);

    HRESULT (STDMETHODCALLTYPE *CompareStart)(
        ITfRangeACP *This,
        TfEditCookie ec,
        ITfRange *pWith,
        TfAnchor aPos,
        LONG *plResult);

    HRESULT (STDMETHODCALLTYPE *CompareEnd)(
        ITfRangeACP *This,
        TfEditCookie ec,
        ITfRange *pWith,
        TfAnchor aPos,
        LONG *plResult);

    HRESULT (STDMETHODCALLTYPE *AdjustForInsert)(
        ITfRangeACP *This,
        TfEditCookie ec,
        ULONG cchInsert,
        WINBOOL *pfInsertOk);

    HRESULT (STDMETHODCALLTYPE *GetGravity)(
        ITfRangeACP *This,
        TfGravity *pgStart,
        TfGravity *pgEnd);

    HRESULT (STDMETHODCALLTYPE *SetGravity)(
        ITfRangeACP *This,
        TfEditCookie ec,
        TfGravity gStart,
        TfGravity gEnd);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        ITfRangeACP *This,
        ITfRange **ppClone);

    HRESULT (STDMETHODCALLTYPE *GetContext)(
        ITfRangeACP *This,
        ITfContext **ppContext);

    /*** ITfRangeACP methods ***/
    HRESULT (STDMETHODCALLTYPE *GetExtent)(
        ITfRangeACP *This,
        LONG *pacpAnchor,
        LONG *pcch);

    HRESULT (STDMETHODCALLTYPE *SetExtent)(
        ITfRangeACP *This,
        LONG acpAnchor,
        LONG cch);

    END_INTERFACE
} ITfRangeACPVtbl;

interface ITfRangeACP {
    CONST_VTBL ITfRangeACPVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITfRangeACP_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITfRangeACP_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITfRangeACP_Release(This) (This)->lpVtbl->Release(This)
/*** ITfRange methods ***/
#define ITfRangeACP_GetText(This,ec,dwFlags,pchText,cchMax,pcch) (This)->lpVtbl->GetText(This,ec,dwFlags,pchText,cchMax,pcch)
#define ITfRangeACP_SetText(This,ec,dwFlags,pchText,cch) (This)->lpVtbl->SetText(This,ec,dwFlags,pchText,cch)
#define ITfRangeACP_GetFormattedText(This,ec,ppDataObject) (This)->lpVtbl->GetFormattedText(This,ec,ppDataObject)
#define ITfRangeACP_GetEmbedded(This,ec,rguidService,riid,ppunk) (This)->lpVtbl->GetEmbedded(This,ec,rguidService,riid,ppunk)
#define ITfRangeACP_InsertEmbedded(This,ec,dwFlags,pDataObject) (This)->lpVtbl->InsertEmbedded(This,ec,dwFlags,pDataObject)
#define ITfRangeACP_ShiftStart(This,ec,cchReq,pcch,pHalt) (This)->lpVtbl->ShiftStart(This,ec,cchReq,pcch,pHalt)
#define ITfRangeACP_ShiftEnd(This,ec,cchReq,pcch,pHalt) (This)->lpVtbl->ShiftEnd(This,ec,cchReq,pcch,pHalt)
#define ITfRangeACP_ShiftStartToRange(This,ec,pRange,aPos) (This)->lpVtbl->ShiftStartToRange(This,ec,pRange,aPos)
#define ITfRangeACP_ShiftEndToRange(This,ec,pRange,aPos) (This)->lpVtbl->ShiftEndToRange(This,ec,pRange,aPos)
#define ITfRangeACP_ShiftStartRegion(This,ec,dir,pfNoRegion) (This)->lpVtbl->ShiftStartRegion(This,ec,dir,pfNoRegion)
#define ITfRangeACP_ShiftEndRegion(This,ec,dir,pfNoRegion) (This)->lpVtbl->ShiftEndRegion(This,ec,dir,pfNoRegion)
#define ITfRangeACP_IsEmpty(This,ec,pfEmpty) (This)->lpVtbl->IsEmpty(This,ec,pfEmpty)
#define ITfRangeACP_Collapse(This,ec,aPos) (This)->lpVtbl->Collapse(This,ec,aPos)
#define ITfRangeACP_IsEqualStart(This,ec,pWith,aPos,pfEqual) (This)->lpVtbl->IsEqualStart(This,ec,pWith,aPos,pfEqual)
#define ITfRangeACP_IsEqualEnd(This,ec,pWith,aPos,pfEqual) (This)->lpVtbl->IsEqualEnd(This,ec,pWith,aPos,pfEqual)
#define ITfRangeACP_CompareStart(This,ec,pWith,aPos,plResult) (This)->lpVtbl->CompareStart(This,ec,pWith,aPos,plResult)
#define ITfRangeACP_CompareEnd(This,ec,pWith,aPos,plResult) (This)->lpVtbl->CompareEnd(This,ec,pWith,aPos,plResult)
#define ITfRangeACP_AdjustForInsert(This,ec,cchInsert,pfInsertOk) (This)->lpVtbl->AdjustForInsert(This,ec,cchInsert,pfInsertOk)
#define ITfRangeACP_GetGravity(This,pgStart,pgEnd) (This)->lpVtbl->GetGravity(This,pgStart,pgEnd)
#define ITfRangeACP_SetGravity(This,ec,gStart,gEnd) (This)->lpVtbl->SetGravity(This,ec,gStart,gEnd)
#define ITfRangeACP_Clone(This,ppClone) (This)->lpVtbl->Clone(This,ppClone)
#define ITfRangeACP_GetContext(This,ppContext) (This)->lpVtbl->GetContext(This,ppContext)
/*** ITfRangeACP methods ***/
#define ITfRangeACP_GetExtent(This,pacpAnchor,pcch) (This)->lpVtbl->GetExtent(This,pacpAnchor,pcch)
#define ITfRangeACP_SetExtent(This,acpAnchor,cch) (This)->lpVtbl->SetExtent(This,acpAnchor,cch)
#else
/*** IUnknown methods ***/
static inline HRESULT ITfRangeACP_QueryInterface(ITfRangeACP* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITfRangeACP_AddRef(ITfRangeACP* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITfRangeACP_Release(ITfRangeACP* This) {
    return This->lpVtbl->Release(This);
}
/*** ITfRange methods ***/
static inline HRESULT ITfRangeACP_GetText(ITfRangeACP* This,TfEditCookie ec,DWORD dwFlags,WCHAR *pchText,ULONG cchMax,ULONG *pcch) {
    return This->lpVtbl->GetText(This,ec,dwFlags,pchText,cchMax,pcch);
}
static inline HRESULT ITfRangeACP_SetText(ITfRangeACP* This,TfEditCookie ec,DWORD dwFlags,const WCHAR *pchText,LONG cch) {
    return This->lpVtbl->SetText(This,ec,dwFlags,pchText,cch);
}
static inline HRESULT ITfRangeACP_GetFormattedText(ITfRangeACP* This,TfEditCookie ec,IDataObject **ppDataObject) {
    return This->lpVtbl->GetFormattedText(This,ec,ppDataObject);
}
static inline HRESULT ITfRangeACP_GetEmbedded(ITfRangeACP* This,TfEditCookie ec,REFGUID rguidService,REFIID riid,IUnknown **ppunk) {
    return This->lpVtbl->GetEmbedded(This,ec,rguidService,riid,ppunk);
}
static inline HRESULT ITfRangeACP_InsertEmbedded(ITfRangeACP* This,TfEditCookie ec,DWORD dwFlags,IDataObject *pDataObject) {
    return This->lpVtbl->InsertEmbedded(This,ec,dwFlags,pDataObject);
}
static inline HRESULT ITfRangeACP_ShiftStart(ITfRangeACP* This,TfEditCookie ec,LONG cchReq,LONG *pcch,const TF_HALTCOND *pHalt) {
    return This->lpVtbl->ShiftStart(This,ec,cchReq,pcch,pHalt);
}
static inline HRESULT ITfRangeACP_ShiftEnd(ITfRangeACP* This,TfEditCookie ec,LONG cchReq,LONG *pcch,const TF_HALTCOND *pHalt) {
    return This->lpVtbl->ShiftEnd(This,ec,cchReq,pcch,pHalt);
}
static inline HRESULT ITfRangeACP_ShiftStartToRange(ITfRangeACP* This,TfEditCookie ec,ITfRange *pRange,TfAnchor aPos) {
    return This->lpVtbl->ShiftStartToRange(This,ec,pRange,aPos);
}
static inline HRESULT ITfRangeACP_ShiftEndToRange(ITfRangeACP* This,TfEditCookie ec,ITfRange *pRange,TfAnchor aPos) {
    return This->lpVtbl->ShiftEndToRange(This,ec,pRange,aPos);
}
static inline HRESULT ITfRangeACP_ShiftStartRegion(ITfRangeACP* This,TfEditCookie ec,TfShiftDir dir,WINBOOL *pfNoRegion) {
    return This->lpVtbl->ShiftStartRegion(This,ec,dir,pfNoRegion);
}
static inline HRESULT ITfRangeACP_ShiftEndRegion(ITfRangeACP* This,TfEditCookie ec,TfShiftDir dir,WINBOOL *pfNoRegion) {
    return This->lpVtbl->ShiftEndRegion(This,ec,dir,pfNoRegion);
}
static inline HRESULT ITfRangeACP_IsEmpty(ITfRangeACP* This,TfEditCookie ec,WINBOOL *pfEmpty) {
    return This->lpVtbl->IsEmpty(This,ec,pfEmpty);
}
static inline HRESULT ITfRangeACP_Collapse(ITfRangeACP* This,TfEditCookie ec,TfAnchor aPos) {
    return This->lpVtbl->Collapse(This,ec,aPos);
}
static inline HRESULT ITfRangeACP_IsEqualStart(ITfRangeACP* This,TfEditCookie ec,ITfRange *pWith,TfAnchor aPos,WINBOOL *pfEqual) {
    return This->lpVtbl->IsEqualStart(This,ec,pWith,aPos,pfEqual);
}
static inline HRESULT ITfRangeACP_IsEqualEnd(ITfRangeACP* This,TfEditCookie ec,ITfRange *pWith,TfAnchor aPos,WINBOOL *pfEqual) {
    return This->lpVtbl->IsEqualEnd(This,ec,pWith,aPos,pfEqual);
}
static inline HRESULT ITfRangeACP_CompareStart(ITfRangeACP* This,TfEditCookie ec,ITfRange *pWith,TfAnchor aPos,LONG *plResult) {
    return This->lpVtbl->CompareStart(This,ec,pWith,aPos,plResult);
}
static inline HRESULT ITfRangeACP_CompareEnd(ITfRangeACP* This,TfEditCookie ec,ITfRange *pWith,TfAnchor aPos,LONG *plResult) {
    return This->lpVtbl->CompareEnd(This,ec,pWith,aPos,plResult);
}
static inline HRESULT ITfRangeACP_AdjustForInsert(ITfRangeACP* This,TfEditCookie ec,ULONG cchInsert,WINBOOL *pfInsertOk) {
    return This->lpVtbl->AdjustForInsert(This,ec,cchInsert,pfInsertOk);
}
static inline HRESULT ITfRangeACP_GetGravity(ITfRangeACP* This,TfGravity *pgStart,TfGravity *pgEnd) {
    return This->lpVtbl->GetGravity(This,pgStart,pgEnd);
}
static inline HRESULT ITfRangeACP_SetGravity(ITfRangeACP* This,TfEditCookie ec,TfGravity gStart,TfGravity gEnd) {
    return This->lpVtbl->SetGravity(This,ec,gStart,gEnd);
}
static inline HRESULT ITfRangeACP_Clone(ITfRangeACP* This,ITfRange **ppClone) {
    return This->lpVtbl->Clone(This,ppClone);
}
static inline HRESULT ITfRangeACP_GetContext(ITfRangeACP* This,ITfContext **ppContext) {
    return This->lpVtbl->GetContext(This,ppContext);
}
/*** ITfRangeACP methods ***/
static inline HRESULT ITfRangeACP_GetExtent(ITfRangeACP* This,LONG *pacpAnchor,LONG *pcch) {
    return This->lpVtbl->GetExtent(This,pacpAnchor,pcch);
}
static inline HRESULT ITfRangeACP_SetExtent(ITfRangeACP* This,LONG acpAnchor,LONG cch) {
    return This->lpVtbl->SetExtent(This,acpAnchor,cch);
}
#endif
#endif

#endif


#endif  /* __ITfRangeACP_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITfInsertAtSelection interface
 */
#ifndef __ITfInsertAtSelection_INTERFACE_DEFINED__
#define __ITfInsertAtSelection_INTERFACE_DEFINED__

#define TF_IAS_NOQUERY (0x1)

#define TF_IAS_QUERYONLY (0x2)

#define TF_IAS_NO_DEFAULT_COMPOSITION (0x80000000)

DEFINE_GUID(IID_ITfInsertAtSelection, 0x55ce16ba, 0x3014, 0x41c1, 0x9c,0xeb, 0xfa,0xde,0x14,0x46,0xac,0x6c);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("55ce16ba-3014-41c1-9ceb-fade1446ac6c")
ITfInsertAtSelection : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE InsertTextAtSelection(
        TfEditCookie ec,
        DWORD dwFlags,
        const WCHAR *pchText,
        LONG cch,
        ITfRange **ppRange) = 0;

    virtual HRESULT STDMETHODCALLTYPE InsertEmbeddedAtSelection(
        TfEditCookie ec,
        DWORD dwFlags,
        IDataObject *pDataObject,
        ITfRange **ppRange) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITfInsertAtSelection, 0x55ce16ba, 0x3014, 0x41c1, 0x9c,0xeb, 0xfa,0xde,0x14,0x46,0xac,0x6c)
#endif
#else
typedef struct ITfInsertAtSelectionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITfInsertAtSelection *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITfInsertAtSelection *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITfInsertAtSelection *This);

    /*** ITfInsertAtSelection methods ***/
    HRESULT (STDMETHODCALLTYPE *InsertTextAtSelection)(
        ITfInsertAtSelection *This,
        TfEditCookie ec,
        DWORD dwFlags,
        const WCHAR *pchText,
        LONG cch,
        ITfRange **ppRange);

    HRESULT (STDMETHODCALLTYPE *InsertEmbeddedAtSelection)(
        ITfInsertAtSelection *This,
        TfEditCookie ec,
        DWORD dwFlags,
        IDataObject *pDataObject,
        ITfRange **ppRange);

    END_INTERFACE
} ITfInsertAtSelectionVtbl;

interface ITfInsertAtSelection {
    CONST_VTBL ITfInsertAtSelectionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITfInsertAtSelection_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITfInsertAtSelection_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITfInsertAtSelection_Release(This) (This)->lpVtbl->Release(This)
/*** ITfInsertAtSelection methods ***/
#define ITfInsertAtSelection_InsertTextAtSelection(This,ec,dwFlags,pchText,cch,ppRange) (This)->lpVtbl->InsertTextAtSelection(This,ec,dwFlags,pchText,cch,ppRange)
#define ITfInsertAtSelection_InsertEmbeddedAtSelection(This,ec,dwFlags,pDataObject,ppRange) (This)->lpVtbl->InsertEmbeddedAtSelection(This,ec,dwFlags,pDataObject,ppRange)
#else
/*** IUnknown methods ***/
static inline HRESULT ITfInsertAtSelection_QueryInterface(ITfInsertAtSelection* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITfInsertAtSelection_AddRef(ITfInsertAtSelection* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITfInsertAtSelection_Release(ITfInsertAtSelection* This) {
    return This->lpVtbl->Release(This);
}
/*** ITfInsertAtSelection methods ***/
static inline HRESULT ITfInsertAtSelection_InsertTextAtSelection(ITfInsertAtSelection* This,TfEditCookie ec,DWORD dwFlags,const WCHAR *pchText,LONG cch,ITfRange **ppRange) {
    return This->lpVtbl->InsertTextAtSelection(This,ec,dwFlags,pchText,cch,ppRange);
}
static inline HRESULT ITfInsertAtSelection_InsertEmbeddedAtSelection(ITfInsertAtSelection* This,TfEditCookie ec,DWORD dwFlags,IDataObject *pDataObject,ITfRange **ppRange) {
    return This->lpVtbl->InsertEmbeddedAtSelection(This,ec,dwFlags,pDataObject,ppRange);
}
#endif
#endif

#endif


#endif  /* __ITfInsertAtSelection_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITfPropertyStore interface
 */
#ifndef __ITfPropertyStore_INTERFACE_DEFINED__
#define __ITfPropertyStore_INTERFACE_DEFINED__

#define TF_TU_CORRECTION (0x1)

DEFINE_GUID(IID_ITfPropertyStore, 0x6834b120, 0x88cb, 0x11d2, 0xbf,0x45, 0x00,0x10,0x5a,0x27,0x99,0xb5);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("6834b120-88cb-11d2-bf45-00105a2799b5")
ITfPropertyStore : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetType(
        GUID *pguid) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDataType(
        DWORD *pdwReserved) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetData(
        VARIANT *pvarValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnTextUpdated(
        DWORD dwFlags,
        ITfRange *pRangeNew,
        WINBOOL *pfAccept) = 0;

    virtual HRESULT STDMETHODCALLTYPE Shrink(
        ITfRange *pRangeNew,
        WINBOOL *pfFree) = 0;

    virtual HRESULT STDMETHODCALLTYPE Divide(
        ITfRange *pRangeThis,
        ITfRange *pRangeNew,
        ITfPropertyStore **ppPropStore) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        ITfPropertyStore **pPropStore) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPropertyRangeCreator(
        CLSID *pclsid) = 0;

    virtual HRESULT STDMETHODCALLTYPE Serialize(
        IStream *pStream,
        ULONG *pcb) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITfPropertyStore, 0x6834b120, 0x88cb, 0x11d2, 0xbf,0x45, 0x00,0x10,0x5a,0x27,0x99,0xb5)
#endif
#else
typedef struct ITfPropertyStoreVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITfPropertyStore *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITfPropertyStore *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITfPropertyStore *This);

    /*** ITfPropertyStore methods ***/
    HRESULT (STDMETHODCALLTYPE *GetType)(
        ITfPropertyStore *This,
        GUID *pguid);

    HRESULT (STDMETHODCALLTYPE *GetDataType)(
        ITfPropertyStore *This,
        DWORD *pdwReserved);

    HRESULT (STDMETHODCALLTYPE *GetData)(
        ITfPropertyStore *This,
        VARIANT *pvarValue);

    HRESULT (STDMETHODCALLTYPE *OnTextUpdated)(
        ITfPropertyStore *This,
        DWORD dwFlags,
        ITfRange *pRangeNew,
        WINBOOL *pfAccept);

    HRESULT (STDMETHODCALLTYPE *Shrink)(
        ITfPropertyStore *This,
        ITfRange *pRangeNew,
        WINBOOL *pfFree);

    HRESULT (STDMETHODCALLTYPE *Divide)(
        ITfPropertyStore *This,
        ITfRange *pRangeThis,
        ITfRange *pRangeNew,
        ITfPropertyStore **ppPropStore);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        ITfPropertyStore *This,
        ITfPropertyStore **pPropStore);

    HRESULT (STDMETHODCALLTYPE *GetPropertyRangeCreator)(
        ITfPropertyStore *This,
        CLSID *pclsid);

    HRESULT (STDMETHODCALLTYPE *Serialize)(
        ITfPropertyStore *This,
        IStream *pStream,
        ULONG *pcb);

    END_INTERFACE
} ITfPropertyStoreVtbl;

interface ITfPropertyStore {
    CONST_VTBL ITfPropertyStoreVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITfPropertyStore_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITfPropertyStore_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITfPropertyStore_Release(This) (This)->lpVtbl->Release(This)
/*** ITfPropertyStore methods ***/
#define ITfPropertyStore_GetType(This,pguid) (This)->lpVtbl->GetType(This,pguid)
#define ITfPropertyStore_GetDataType(This,pdwReserved) (This)->lpVtbl->GetDataType(This,pdwReserved)
#define ITfPropertyStore_GetData(This,pvarValue) (This)->lpVtbl->GetData(This,pvarValue)
#define ITfPropertyStore_OnTextUpdated(This,dwFlags,pRangeNew,pfAccept) (This)->lpVtbl->OnTextUpdated(This,dwFlags,pRangeNew,pfAccept)
#define ITfPropertyStore_Shrink(This,pRangeNew,pfFree) (This)->lpVtbl->Shrink(This,pRangeNew,pfFree)
#define ITfPropertyStore_Divide(This,pRangeThis,pRangeNew,ppPropStore) (This)->lpVtbl->Divide(This,pRangeThis,pRangeNew,ppPropStore)
#define ITfPropertyStore_Clone(This,pPropStore) (This)->lpVtbl->Clone(This,pPropStore)
#define ITfPropertyStore_GetPropertyRangeCreator(This,pclsid) (This)->lpVtbl->GetPropertyRangeCreator(This,pclsid)
#define ITfPropertyStore_Serialize(This,pStream,pcb) (This)->lpVtbl->Serialize(This,pStream,pcb)
#else
/*** IUnknown methods ***/
static inline HRESULT ITfPropertyStore_QueryInterface(ITfPropertyStore* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITfPropertyStore_AddRef(ITfPropertyStore* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITfPropertyStore_Release(ITfPropertyStore* This) {
    return This->lpVtbl->Release(This);
}
/*** ITfPropertyStore methods ***/
static inline HRESULT ITfPropertyStore_GetType(ITfPropertyStore* This,GUID *pguid) {
    return This->lpVtbl->GetType(This,pguid);
}
static inline HRESULT ITfPropertyStore_GetDataType(ITfPropertyStore* This,DWORD *pdwReserved) {
    return This->lpVtbl->GetDataType(This,pdwReserved);
}
static inline HRESULT ITfPropertyStore_GetData(ITfPropertyStore* This,VARIANT *pvarValue) {
    return This->lpVtbl->GetData(This,pvarValue);
}
static inline HRESULT ITfPropertyStore_OnTextUpdated(ITfPropertyStore* This,DWORD dwFlags,ITfRange *pRangeNew,WINBOOL *pfAccept) {
    return This->lpVtbl->OnTextUpdated(This,dwFlags,pRangeNew,pfAccept);
}
static inline HRESULT ITfPropertyStore_Shrink(ITfPropertyStore* This,ITfRange *pRangeNew,WINBOOL *pfFree) {
    return This->lpVtbl->Shrink(This,pRangeNew,pfFree);
}
static inline HRESULT ITfPropertyStore_Divide(ITfPropertyStore* This,ITfRange *pRangeThis,ITfRange *pRangeNew,ITfPropertyStore **ppPropStore) {
    return This->lpVtbl->Divide(This,pRangeThis,pRangeNew,ppPropStore);
}
static inline HRESULT ITfPropertyStore_Clone(ITfPropertyStore* This,ITfPropertyStore **pPropStore) {
    return This->lpVtbl->Clone(This,pPropStore);
}
static inline HRESULT ITfPropertyStore_GetPropertyRangeCreator(ITfPropertyStore* This,CLSID *pclsid) {
    return This->lpVtbl->GetPropertyRangeCreator(This,pclsid);
}
static inline HRESULT ITfPropertyStore_Serialize(ITfPropertyStore* This,IStream *pStream,ULONG *pcb) {
    return This->lpVtbl->Serialize(This,pStream,pcb);
}
#endif
#endif

#endif


#endif  /* __ITfPropertyStore_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IEnumITfCompositionView interface
 */
#ifndef __IEnumITfCompositionView_INTERFACE_DEFINED__
#define __IEnumITfCompositionView_INTERFACE_DEFINED__

DEFINE_GUID(IID_IEnumITfCompositionView, 0x5efd22ba, 0x7838, 0x46cb, 0x88,0xe2, 0xca,0xdb,0x14,0x12,0x4f,0x8f);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("5efd22ba-7838-46cb-88e2-cadb14124f8f")
IEnumITfCompositionView : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Clone(
        IEnumITfCompositionView **ppEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE Next(
        ULONG ulCount,
        ITfCompositionView **rgCompositionView,
        ULONG *pcFetched) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reset(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Skip(
        ULONG ulCount) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IEnumITfCompositionView, 0x5efd22ba, 0x7838, 0x46cb, 0x88,0xe2, 0xca,0xdb,0x14,0x12,0x4f,0x8f)
#endif
#else
typedef struct IEnumITfCompositionViewVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IEnumITfCompositionView *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IEnumITfCompositionView *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IEnumITfCompositionView *This);

    /*** IEnumITfCompositionView methods ***/
    HRESULT (STDMETHODCALLTYPE *Clone)(
        IEnumITfCompositionView *This,
        IEnumITfCompositionView **ppEnum);

    HRESULT (STDMETHODCALLTYPE *Next)(
        IEnumITfCompositionView *This,
        ULONG ulCount,
        ITfCompositionView **rgCompositionView,
        ULONG *pcFetched);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        IEnumITfCompositionView *This);

    HRESULT (STDMETHODCALLTYPE *Skip)(
        IEnumITfCompositionView *This,
        ULONG ulCount);

    END_INTERFACE
} IEnumITfCompositionViewVtbl;

interface IEnumITfCompositionView {
    CONST_VTBL IEnumITfCompositionViewVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IEnumITfCompositionView_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IEnumITfCompositionView_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IEnumITfCompositionView_Release(This) (This)->lpVtbl->Release(This)
/*** IEnumITfCompositionView methods ***/
#define IEnumITfCompositionView_Clone(This,ppEnum) (This)->lpVtbl->Clone(This,ppEnum)
#define IEnumITfCompositionView_Next(This,ulCount,rgCompositionView,pcFetched) (This)->lpVtbl->Next(This,ulCount,rgCompositionView,pcFetched)
#define IEnumITfCompositionView_Reset(This) (This)->lpVtbl->Reset(This)
#define IEnumITfCompositionView_Skip(This,ulCount) (This)->lpVtbl->Skip(This,ulCount)
#else
/*** IUnknown methods ***/
static inline HRESULT IEnumITfCompositionView_QueryInterface(IEnumITfCompositionView* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IEnumITfCompositionView_AddRef(IEnumITfCompositionView* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IEnumITfCompositionView_Release(IEnumITfCompositionView* This) {
    return This->lpVtbl->Release(This);
}
/*** IEnumITfCompositionView methods ***/
static inline HRESULT IEnumITfCompositionView_Clone(IEnumITfCompositionView* This,IEnumITfCompositionView **ppEnum) {
    return This->lpVtbl->Clone(This,ppEnum);
}
static inline HRESULT IEnumITfCompositionView_Next(IEnumITfCompositionView* This,ULONG ulCount,ITfCompositionView **rgCompositionView,ULONG *pcFetched) {
    return This->lpVtbl->Next(This,ulCount,rgCompositionView,pcFetched);
}
static inline HRESULT IEnumITfCompositionView_Reset(IEnumITfCompositionView* This) {
    return This->lpVtbl->Reset(This);
}
static inline HRESULT IEnumITfCompositionView_Skip(IEnumITfCompositionView* This,ULONG ulCount) {
    return This->lpVtbl->Skip(This,ulCount);
}
#endif
#endif

#endif


#endif  /* __IEnumITfCompositionView_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITfComposition interface
 */
#ifndef __ITfComposition_INTERFACE_DEFINED__
#define __ITfComposition_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITfComposition, 0x20168d64, 0x5a8f, 0x4a5a, 0xb7,0xbd, 0xcf,0xa2,0x9f,0x4d,0x0f,0xd9);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("20168d64-5a8f-4a5a-b7bd-cfa29f4d0fd9")
ITfComposition : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetRange(
        ITfRange **ppRange) = 0;

    virtual HRESULT STDMETHODCALLTYPE ShiftStart(
        TfEditCookie ecWrite,
        ITfRange *pNewStart) = 0;

    virtual HRESULT STDMETHODCALLTYPE ShiftEnd(
        TfEditCookie ecWrite,
        ITfRange *pNewEnd) = 0;

    virtual HRESULT STDMETHODCALLTYPE EndComposition(
        TfEditCookie ecWrite) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITfComposition, 0x20168d64, 0x5a8f, 0x4a5a, 0xb7,0xbd, 0xcf,0xa2,0x9f,0x4d,0x0f,0xd9)
#endif
#else
typedef struct ITfCompositionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITfComposition *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITfComposition *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITfComposition *This);

    /*** ITfComposition methods ***/
    HRESULT (STDMETHODCALLTYPE *GetRange)(
        ITfComposition *This,
        ITfRange **ppRange);

    HRESULT (STDMETHODCALLTYPE *ShiftStart)(
        ITfComposition *This,
        TfEditCookie ecWrite,
        ITfRange *pNewStart);

    HRESULT (STDMETHODCALLTYPE *ShiftEnd)(
        ITfComposition *This,
        TfEditCookie ecWrite,
        ITfRange *pNewEnd);

    HRESULT (STDMETHODCALLTYPE *EndComposition)(
        ITfComposition *This,
        TfEditCookie ecWrite);

    END_INTERFACE
} ITfCompositionVtbl;

interface ITfComposition {
    CONST_VTBL ITfCompositionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITfComposition_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITfComposition_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITfComposition_Release(This) (This)->lpVtbl->Release(This)
/*** ITfComposition methods ***/
#define ITfComposition_GetRange(This,ppRange) (This)->lpVtbl->GetRange(This,ppRange)
#define ITfComposition_ShiftStart(This,ecWrite,pNewStart) (This)->lpVtbl->ShiftStart(This,ecWrite,pNewStart)
#define ITfComposition_ShiftEnd(This,ecWrite,pNewEnd) (This)->lpVtbl->ShiftEnd(This,ecWrite,pNewEnd)
#define ITfComposition_EndComposition(This,ecWrite) (This)->lpVtbl->EndComposition(This,ecWrite)
#else
/*** IUnknown methods ***/
static inline HRESULT ITfComposition_QueryInterface(ITfComposition* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITfComposition_AddRef(ITfComposition* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITfComposition_Release(ITfComposition* This) {
    return This->lpVtbl->Release(This);
}
/*** ITfComposition methods ***/
static inline HRESULT ITfComposition_GetRange(ITfComposition* This,ITfRange **ppRange) {
    return This->lpVtbl->GetRange(This,ppRange);
}
static inline HRESULT ITfComposition_ShiftStart(ITfComposition* This,TfEditCookie ecWrite,ITfRange *pNewStart) {
    return This->lpVtbl->ShiftStart(This,ecWrite,pNewStart);
}
static inline HRESULT ITfComposition_ShiftEnd(ITfComposition* This,TfEditCookie ecWrite,ITfRange *pNewEnd) {
    return This->lpVtbl->ShiftEnd(This,ecWrite,pNewEnd);
}
static inline HRESULT ITfComposition_EndComposition(ITfComposition* This,TfEditCookie ecWrite) {
    return This->lpVtbl->EndComposition(This,ecWrite);
}
#endif
#endif

#endif


#endif  /* __ITfComposition_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITfCompositionSink interface
 */
#ifndef __ITfCompositionSink_INTERFACE_DEFINED__
#define __ITfCompositionSink_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITfCompositionSink, 0xa781718c, 0x579a, 0x4b15, 0xa2,0x80, 0x32,0xb8,0x57,0x7a,0xcc,0x5e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a781718c-579a-4b15-a280-32b8577acc5e")
ITfCompositionSink : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE OnCompositionTerminated(
        TfEditCookie ecWrite,
        ITfComposition *pComposition) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITfCompositionSink, 0xa781718c, 0x579a, 0x4b15, 0xa2,0x80, 0x32,0xb8,0x57,0x7a,0xcc,0x5e)
#endif
#else
typedef struct ITfCompositionSinkVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITfCompositionSink *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITfCompositionSink *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITfCompositionSink *This);

    /*** ITfCompositionSink methods ***/
    HRESULT (STDMETHODCALLTYPE *OnCompositionTerminated)(
        ITfCompositionSink *This,
        TfEditCookie ecWrite,
        ITfComposition *pComposition);

    END_INTERFACE
} ITfCompositionSinkVtbl;

interface ITfCompositionSink {
    CONST_VTBL ITfCompositionSinkVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITfCompositionSink_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITfCompositionSink_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITfCompositionSink_Release(This) (This)->lpVtbl->Release(This)
/*** ITfCompositionSink methods ***/
#define ITfCompositionSink_OnCompositionTerminated(This,ecWrite,pComposition) (This)->lpVtbl->OnCompositionTerminated(This,ecWrite,pComposition)
#else
/*** IUnknown methods ***/
static inline HRESULT ITfCompositionSink_QueryInterface(ITfCompositionSink* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITfCompositionSink_AddRef(ITfCompositionSink* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITfCompositionSink_Release(ITfCompositionSink* This) {
    return This->lpVtbl->Release(This);
}
/*** ITfCompositionSink methods ***/
static inline HRESULT ITfCompositionSink_OnCompositionTerminated(ITfCompositionSink* This,TfEditCookie ecWrite,ITfComposition *pComposition) {
    return This->lpVtbl->OnCompositionTerminated(This,ecWrite,pComposition);
}
#endif
#endif

#endif


#endif  /* __ITfCompositionSink_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITfContextComposition interface
 */
#ifndef __ITfContextComposition_INTERFACE_DEFINED__
#define __ITfContextComposition_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITfContextComposition, 0xd40c8aae, 0xac92, 0x4fc7, 0x9a,0x11, 0x0e,0xe0,0xe2,0x3a,0xa3,0x9b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d40c8aae-ac92-4fc7-9a11-0ee0e23aa39b")
ITfContextComposition : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE StartComposition(
        TfEditCookie ecWrite,
        ITfRange *pCompositionRange,
        ITfCompositionSink *pSink,
        ITfComposition **ppComposition) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumCompositions(
        IEnumITfCompositionView **ppEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE FindComposition(
        TfEditCookie ecRead,
        ITfRange *pTestRange,
        IEnumITfCompositionView **ppEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE TakeOwnership(
        TfEditCookie ecWrite,
        ITfCompositionView *pComposition,
        ITfCompositionSink *pSink,
        ITfComposition **ppComposition) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITfContextComposition, 0xd40c8aae, 0xac92, 0x4fc7, 0x9a,0x11, 0x0e,0xe0,0xe2,0x3a,0xa3,0x9b)
#endif
#else
typedef struct ITfContextCompositionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITfContextComposition *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITfContextComposition *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITfContextComposition *This);

    /*** ITfContextComposition methods ***/
    HRESULT (STDMETHODCALLTYPE *StartComposition)(
        ITfContextComposition *This,
        TfEditCookie ecWrite,
        ITfRange *pCompositionRange,
        ITfCompositionSink *pSink,
        ITfComposition **ppComposition);

    HRESULT (STDMETHODCALLTYPE *EnumCompositions)(
        ITfContextComposition *This,
        IEnumITfCompositionView **ppEnum);

    HRESULT (STDMETHODCALLTYPE *FindComposition)(
        ITfContextComposition *This,
        TfEditCookie ecRead,
        ITfRange *pTestRange,
        IEnumITfCompositionView **ppEnum);

    HRESULT (STDMETHODCALLTYPE *TakeOwnership)(
        ITfContextComposition *This,
        TfEditCookie ecWrite,
        ITfCompositionView *pComposition,
        ITfCompositionSink *pSink,
        ITfComposition **ppComposition);

    END_INTERFACE
} ITfContextCompositionVtbl;

interface ITfContextComposition {
    CONST_VTBL ITfContextCompositionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITfContextComposition_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITfContextComposition_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITfContextComposition_Release(This) (This)->lpVtbl->Release(This)
/*** ITfContextComposition methods ***/
#define ITfContextComposition_StartComposition(This,ecWrite,pCompositionRange,pSink,ppComposition) (This)->lpVtbl->StartComposition(This,ecWrite,pCompositionRange,pSink,ppComposition)
#define ITfContextComposition_EnumCompositions(This,ppEnum) (This)->lpVtbl->EnumCompositions(This,ppEnum)
#define ITfContextComposition_FindComposition(This,ecRead,pTestRange,ppEnum) (This)->lpVtbl->FindComposition(This,ecRead,pTestRange,ppEnum)
#define ITfContextComposition_TakeOwnership(This,ecWrite,pComposition,pSink,ppComposition) (This)->lpVtbl->TakeOwnership(This,ecWrite,pComposition,pSink,ppComposition)
#else
/*** IUnknown methods ***/
static inline HRESULT ITfContextComposition_QueryInterface(ITfContextComposition* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITfContextComposition_AddRef(ITfContextComposition* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITfContextComposition_Release(ITfContextComposition* This) {
    return This->lpVtbl->Release(This);
}
/*** ITfContextComposition methods ***/
static inline HRESULT ITfContextComposition_StartComposition(ITfContextComposition* This,TfEditCookie ecWrite,ITfRange *pCompositionRange,ITfCompositionSink *pSink,ITfComposition **ppComposition) {
    return This->lpVtbl->StartComposition(This,ecWrite,pCompositionRange,pSink,ppComposition);
}
static inline HRESULT ITfContextComposition_EnumCompositions(ITfContextComposition* This,IEnumITfCompositionView **ppEnum) {
    return This->lpVtbl->EnumCompositions(This,ppEnum);
}
static inline HRESULT ITfContextComposition_FindComposition(ITfContextComposition* This,TfEditCookie ecRead,ITfRange *pTestRange,IEnumITfCompositionView **ppEnum) {
    return This->lpVtbl->FindComposition(This,ecRead,pTestRange,ppEnum);
}
static inline HRESULT ITfContextComposition_TakeOwnership(ITfContextComposition* This,TfEditCookie ecWrite,ITfCompositionView *pComposition,ITfCompositionSink *pSink,ITfComposition **ppComposition) {
    return This->lpVtbl->TakeOwnership(This,ecWrite,pComposition,pSink,ppComposition);
}
#endif
#endif

#endif


#endif  /* __ITfContextComposition_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITfContextOwnerCompositionServices interface
 */
#ifndef __ITfContextOwnerCompositionServices_INTERFACE_DEFINED__
#define __ITfContextOwnerCompositionServices_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITfContextOwnerCompositionServices, 0x86462810, 0x593b, 0x4916, 0x97,0x64, 0x19,0xc0,0x8e,0x9c,0xe1,0x10);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("86462810-593b-4916-9764-19c08e9ce110")
ITfContextOwnerCompositionServices : public ITfContextComposition
{
    virtual HRESULT STDMETHODCALLTYPE TerminateComposition(
        ITfCompositionView *pComposition) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITfContextOwnerCompositionServices, 0x86462810, 0x593b, 0x4916, 0x97,0x64, 0x19,0xc0,0x8e,0x9c,0xe1,0x10)
#endif
#else
typedef struct ITfContextOwnerCompositionServicesVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITfContextOwnerCompositionServices *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITfContextOwnerCompositionServices *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITfContextOwnerCompositionServices *This);

    /*** ITfContextComposition methods ***/
    HRESULT (STDMETHODCALLTYPE *StartComposition)(
        ITfContextOwnerCompositionServices *This,
        TfEditCookie ecWrite,
        ITfRange *pCompositionRange,
        ITfCompositionSink *pSink,
        ITfComposition **ppComposition);

    HRESULT (STDMETHODCALLTYPE *EnumCompositions)(
        ITfContextOwnerCompositionServices *This,
        IEnumITfCompositionView **ppEnum);

    HRESULT (STDMETHODCALLTYPE *FindComposition)(
        ITfContextOwnerCompositionServices *This,
        TfEditCookie ecRead,
        ITfRange *pTestRange,
        IEnumITfCompositionView **ppEnum);

    HRESULT (STDMETHODCALLTYPE *TakeOwnership)(
        ITfContextOwnerCompositionServices *This,
        TfEditCookie ecWrite,
        ITfCompositionView *pComposition,
        ITfCompositionSink *pSink,
        ITfComposition **ppComposition);

    /*** ITfContextOwnerCompositionServices methods ***/
    HRESULT (STDMETHODCALLTYPE *TerminateComposition)(
        ITfContextOwnerCompositionServices *This,
        ITfCompositionView *pComposition);

    END_INTERFACE
} ITfContextOwnerCompositionServicesVtbl;

interface ITfContextOwnerCompositionServices {
    CONST_VTBL ITfContextOwnerCompositionServicesVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITfContextOwnerCompositionServices_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITfContextOwnerCompositionServices_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITfContextOwnerCompositionServices_Release(This) (This)->lpVtbl->Release(This)
/*** ITfContextComposition methods ***/
#define ITfContextOwnerCompositionServices_StartComposition(This,ecWrite,pCompositionRange,pSink,ppComposition) (This)->lpVtbl->StartComposition(This,ecWrite,pCompositionRange,pSink,ppComposition)
#define ITfContextOwnerCompositionServices_EnumCompositions(This,ppEnum) (This)->lpVtbl->EnumCompositions(This,ppEnum)
#define ITfContextOwnerCompositionServices_FindComposition(This,ecRead,pTestRange,ppEnum) (This)->lpVtbl->FindComposition(This,ecRead,pTestRange,ppEnum)
#define ITfContextOwnerCompositionServices_TakeOwnership(This,ecWrite,pComposition,pSink,ppComposition) (This)->lpVtbl->TakeOwnership(This,ecWrite,pComposition,pSink,ppComposition)
/*** ITfContextOwnerCompositionServices methods ***/
#define ITfContextOwnerCompositionServices_TerminateComposition(This,pComposition) (This)->lpVtbl->TerminateComposition(This,pComposition)
#else
/*** IUnknown methods ***/
static inline HRESULT ITfContextOwnerCompositionServices_QueryInterface(ITfContextOwnerCompositionServices* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITfContextOwnerCompositionServices_AddRef(ITfContextOwnerCompositionServices* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITfContextOwnerCompositionServices_Release(ITfContextOwnerCompositionServices* This) {
    return This->lpVtbl->Release(This);
}
/*** ITfContextComposition methods ***/
static inline HRESULT ITfContextOwnerCompositionServices_StartComposition(ITfContextOwnerCompositionServices* This,TfEditCookie ecWrite,ITfRange *pCompositionRange,ITfCompositionSink *pSink,ITfComposition **ppComposition) {
    return This->lpVtbl->StartComposition(This,ecWrite,pCompositionRange,pSink,ppComposition);
}
static inline HRESULT ITfContextOwnerCompositionServices_EnumCompositions(ITfContextOwnerCompositionServices* This,IEnumITfCompositionView **ppEnum) {
    return This->lpVtbl->EnumCompositions(This,ppEnum);
}
static inline HRESULT ITfContextOwnerCompositionServices_FindComposition(ITfContextOwnerCompositionServices* This,TfEditCookie ecRead,ITfRange *pTestRange,IEnumITfCompositionView **ppEnum) {
    return This->lpVtbl->FindComposition(This,ecRead,pTestRange,ppEnum);
}
static inline HRESULT ITfContextOwnerCompositionServices_TakeOwnership(ITfContextOwnerCompositionServices* This,TfEditCookie ecWrite,ITfCompositionView *pComposition,ITfCompositionSink *pSink,ITfComposition **ppComposition) {
    return This->lpVtbl->TakeOwnership(This,ecWrite,pComposition,pSink,ppComposition);
}
/*** ITfContextOwnerCompositionServices methods ***/
static inline HRESULT ITfContextOwnerCompositionServices_TerminateComposition(ITfContextOwnerCompositionServices* This,ITfCompositionView *pComposition) {
    return This->lpVtbl->TerminateComposition(This,pComposition);
}
#endif
#endif

#endif


#endif  /* __ITfContextOwnerCompositionServices_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITfPersistentPropertyLoaderACP interface
 */
#ifndef __ITfPersistentPropertyLoaderACP_INTERFACE_DEFINED__
#define __ITfPersistentPropertyLoaderACP_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITfPersistentPropertyLoaderACP, 0x4ef89150, 0x0807, 0x11d3, 0x8d,0xf0, 0x00,0x10,0x5a,0x27,0x99,0xb5);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("4ef89150-0807-11d3-8df0-00105a2799b5")
ITfPersistentPropertyLoaderACP : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE LoadProperty(
        const TF_PERSISTENT_PROPERTY_HEADER_ACP *pHdr,
        IStream **ppStream) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITfPersistentPropertyLoaderACP, 0x4ef89150, 0x0807, 0x11d3, 0x8d,0xf0, 0x00,0x10,0x5a,0x27,0x99,0xb5)
#endif
#else
typedef struct ITfPersistentPropertyLoaderACPVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITfPersistentPropertyLoaderACP *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITfPersistentPropertyLoaderACP *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITfPersistentPropertyLoaderACP *This);

    /*** ITfPersistentPropertyLoaderACP methods ***/
    HRESULT (STDMETHODCALLTYPE *LoadProperty)(
        ITfPersistentPropertyLoaderACP *This,
        const TF_PERSISTENT_PROPERTY_HEADER_ACP *pHdr,
        IStream **ppStream);

    END_INTERFACE
} ITfPersistentPropertyLoaderACPVtbl;

interface ITfPersistentPropertyLoaderACP {
    CONST_VTBL ITfPersistentPropertyLoaderACPVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITfPersistentPropertyLoaderACP_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITfPersistentPropertyLoaderACP_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITfPersistentPropertyLoaderACP_Release(This) (This)->lpVtbl->Release(This)
/*** ITfPersistentPropertyLoaderACP methods ***/
#define ITfPersistentPropertyLoaderACP_LoadProperty(This,pHdr,ppStream) (This)->lpVtbl->LoadProperty(This,pHdr,ppStream)
#else
/*** IUnknown methods ***/
static inline HRESULT ITfPersistentPropertyLoaderACP_QueryInterface(ITfPersistentPropertyLoaderACP* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITfPersistentPropertyLoaderACP_AddRef(ITfPersistentPropertyLoaderACP* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITfPersistentPropertyLoaderACP_Release(ITfPersistentPropertyLoaderACP* This) {
    return This->lpVtbl->Release(This);
}
/*** ITfPersistentPropertyLoaderACP methods ***/
static inline HRESULT ITfPersistentPropertyLoaderACP_LoadProperty(ITfPersistentPropertyLoaderACP* This,const TF_PERSISTENT_PROPERTY_HEADER_ACP *pHdr,IStream **ppStream) {
    return This->lpVtbl->LoadProperty(This,pHdr,ppStream);
}
#endif
#endif

#endif


#endif  /* __ITfPersistentPropertyLoaderACP_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITfContextOwnerServices interface
 */
#ifndef __ITfContextOwnerServices_INTERFACE_DEFINED__
#define __ITfContextOwnerServices_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITfContextOwnerServices, 0xb23eb630, 0x3e1c, 0x11d3, 0xa7,0x45, 0x00,0x50,0x04,0x0a,0xb4,0x07);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("b23eb630-3e1c-11d3-a745-0050040ab407")
ITfContextOwnerServices : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE OnLayoutChange(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnStatusChange(
        DWORD dwFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnAttributeChange(
        REFGUID rguidAttribute) = 0;

    virtual HRESULT STDMETHODCALLTYPE Serialize(
        ITfProperty *pProp,
        ITfRange *pRange,
        TF_PERSISTENT_PROPERTY_HEADER_ACP *pHdr,
        IStream *pStream) = 0;

    virtual HRESULT STDMETHODCALLTYPE Unserialize(
        ITfProperty *pProp,
        const TF_PERSISTENT_PROPERTY_HEADER_ACP *pHdr,
        IStream *pStream,
        ITfPersistentPropertyLoaderACP *pLoader) = 0;

    virtual HRESULT STDMETHODCALLTYPE ForceLoadProperty(
        ITfProperty *pProp) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateRange(
        LONG acpStart,
        LONG acpEnd,
        ITfRangeACP **ppRange) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITfContextOwnerServices, 0xb23eb630, 0x3e1c, 0x11d3, 0xa7,0x45, 0x00,0x50,0x04,0x0a,0xb4,0x07)
#endif
#else
typedef struct ITfContextOwnerServicesVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITfContextOwnerServices *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITfContextOwnerServices *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITfContextOwnerServices *This);

    /*** ITfContextOwnerServices methods ***/
    HRESULT (STDMETHODCALLTYPE *OnLayoutChange)(
        ITfContextOwnerServices *This);

    HRESULT (STDMETHODCALLTYPE *OnStatusChange)(
        ITfContextOwnerServices *This,
        DWORD dwFlags);

    HRESULT (STDMETHODCALLTYPE *OnAttributeChange)(
        ITfContextOwnerServices *This,
        REFGUID rguidAttribute);

    HRESULT (STDMETHODCALLTYPE *Serialize)(
        ITfContextOwnerServices *This,
        ITfProperty *pProp,
        ITfRange *pRange,
        TF_PERSISTENT_PROPERTY_HEADER_ACP *pHdr,
        IStream *pStream);

    HRESULT (STDMETHODCALLTYPE *Unserialize)(
        ITfContextOwnerServices *This,
        ITfProperty *pProp,
        const TF_PERSISTENT_PROPERTY_HEADER_ACP *pHdr,
        IStream *pStream,
        ITfPersistentPropertyLoaderACP *pLoader);

    HRESULT (STDMETHODCALLTYPE *ForceLoadProperty)(
        ITfContextOwnerServices *This,
        ITfProperty *pProp);

    HRESULT (STDMETHODCALLTYPE *CreateRange)(
        ITfContextOwnerServices *This,
        LONG acpStart,
        LONG acpEnd,
        ITfRangeACP **ppRange);

    END_INTERFACE
} ITfContextOwnerServicesVtbl;

interface ITfContextOwnerServices {
    CONST_VTBL ITfContextOwnerServicesVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITfContextOwnerServices_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITfContextOwnerServices_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITfContextOwnerServices_Release(This) (This)->lpVtbl->Release(This)
/*** ITfContextOwnerServices methods ***/
#define ITfContextOwnerServices_OnLayoutChange(This) (This)->lpVtbl->OnLayoutChange(This)
#define ITfContextOwnerServices_OnStatusChange(This,dwFlags) (This)->lpVtbl->OnStatusChange(This,dwFlags)
#define ITfContextOwnerServices_OnAttributeChange(This,rguidAttribute) (This)->lpVtbl->OnAttributeChange(This,rguidAttribute)
#define ITfContextOwnerServices_Serialize(This,pProp,pRange,pHdr,pStream) (This)->lpVtbl->Serialize(This,pProp,pRange,pHdr,pStream)
#define ITfContextOwnerServices_Unserialize(This,pProp,pHdr,pStream,pLoader) (This)->lpVtbl->Unserialize(This,pProp,pHdr,pStream,pLoader)
#define ITfContextOwnerServices_ForceLoadProperty(This,pProp) (This)->lpVtbl->ForceLoadProperty(This,pProp)
#define ITfContextOwnerServices_CreateRange(This,acpStart,acpEnd,ppRange) (This)->lpVtbl->CreateRange(This,acpStart,acpEnd,ppRange)
#else
/*** IUnknown methods ***/
static inline HRESULT ITfContextOwnerServices_QueryInterface(ITfContextOwnerServices* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITfContextOwnerServices_AddRef(ITfContextOwnerServices* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITfContextOwnerServices_Release(ITfContextOwnerServices* This) {
    return This->lpVtbl->Release(This);
}
/*** ITfContextOwnerServices methods ***/
static inline HRESULT ITfContextOwnerServices_OnLayoutChange(ITfContextOwnerServices* This) {
    return This->lpVtbl->OnLayoutChange(This);
}
static inline HRESULT ITfContextOwnerServices_OnStatusChange(ITfContextOwnerServices* This,DWORD dwFlags) {
    return This->lpVtbl->OnStatusChange(This,dwFlags);
}
static inline HRESULT ITfContextOwnerServices_OnAttributeChange(ITfContextOwnerServices* This,REFGUID rguidAttribute) {
    return This->lpVtbl->OnAttributeChange(This,rguidAttribute);
}
static inline HRESULT ITfContextOwnerServices_Serialize(ITfContextOwnerServices* This,ITfProperty *pProp,ITfRange *pRange,TF_PERSISTENT_PROPERTY_HEADER_ACP *pHdr,IStream *pStream) {
    return This->lpVtbl->Serialize(This,pProp,pRange,pHdr,pStream);
}
static inline HRESULT ITfContextOwnerServices_Unserialize(ITfContextOwnerServices* This,ITfProperty *pProp,const TF_PERSISTENT_PROPERTY_HEADER_ACP *pHdr,IStream *pStream,ITfPersistentPropertyLoaderACP *pLoader) {
    return This->lpVtbl->Unserialize(This,pProp,pHdr,pStream,pLoader);
}
static inline HRESULT ITfContextOwnerServices_ForceLoadProperty(ITfContextOwnerServices* This,ITfProperty *pProp) {
    return This->lpVtbl->ForceLoadProperty(This,pProp);
}
static inline HRESULT ITfContextOwnerServices_CreateRange(ITfContextOwnerServices* This,LONG acpStart,LONG acpEnd,ITfRangeACP **ppRange) {
    return This->lpVtbl->CreateRange(This,acpStart,acpEnd,ppRange);
}
#endif
#endif

#endif


#endif  /* __ITfContextOwnerServices_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITfReadOnlyProperty interface
 */
#ifndef __ITfReadOnlyProperty_INTERFACE_DEFINED__
#define __ITfReadOnlyProperty_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITfReadOnlyProperty, 0x17d49a3d, 0xf8b8, 0x4b2f, 0xb2,0x54, 0x52,0x31,0x9d,0xd6,0x4c,0x53);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("17d49a3d-f8b8-4b2f-b254-52319dd64c53")
ITfReadOnlyProperty : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetType(
        GUID *pguid) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumRanges(
        TfEditCookie ec,
        IEnumTfRanges **ppEnum,
        ITfRange *pTargetRange) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetValue(
        TfEditCookie ec,
        ITfRange *pRange,
        VARIANT *pvarValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetContext(
        ITfContext **ppContext) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITfReadOnlyProperty, 0x17d49a3d, 0xf8b8, 0x4b2f, 0xb2,0x54, 0x52,0x31,0x9d,0xd6,0x4c,0x53)
#endif
#else
typedef struct ITfReadOnlyPropertyVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITfReadOnlyProperty *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITfReadOnlyProperty *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITfReadOnlyProperty *This);

    /*** ITfReadOnlyProperty methods ***/
    HRESULT (STDMETHODCALLTYPE *GetType)(
        ITfReadOnlyProperty *This,
        GUID *pguid);

    HRESULT (STDMETHODCALLTYPE *EnumRanges)(
        ITfReadOnlyProperty *This,
        TfEditCookie ec,
        IEnumTfRanges **ppEnum,
        ITfRange *pTargetRange);

    HRESULT (STDMETHODCALLTYPE *GetValue)(
        ITfReadOnlyProperty *This,
        TfEditCookie ec,
        ITfRange *pRange,
        VARIANT *pvarValue);

    HRESULT (STDMETHODCALLTYPE *GetContext)(
        ITfReadOnlyProperty *This,
        ITfContext **ppContext);

    END_INTERFACE
} ITfReadOnlyPropertyVtbl;

interface ITfReadOnlyProperty {
    CONST_VTBL ITfReadOnlyPropertyVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITfReadOnlyProperty_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITfReadOnlyProperty_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITfReadOnlyProperty_Release(This) (This)->lpVtbl->Release(This)
/*** ITfReadOnlyProperty methods ***/
#define ITfReadOnlyProperty_GetType(This,pguid) (This)->lpVtbl->GetType(This,pguid)
#define ITfReadOnlyProperty_EnumRanges(This,ec,ppEnum,pTargetRange) (This)->lpVtbl->EnumRanges(This,ec,ppEnum,pTargetRange)
#define ITfReadOnlyProperty_GetValue(This,ec,pRange,pvarValue) (This)->lpVtbl->GetValue(This,ec,pRange,pvarValue)
#define ITfReadOnlyProperty_GetContext(This,ppContext) (This)->lpVtbl->GetContext(This,ppContext)
#else
/*** IUnknown methods ***/
static inline HRESULT ITfReadOnlyProperty_QueryInterface(ITfReadOnlyProperty* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITfReadOnlyProperty_AddRef(ITfReadOnlyProperty* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITfReadOnlyProperty_Release(ITfReadOnlyProperty* This) {
    return This->lpVtbl->Release(This);
}
/*** ITfReadOnlyProperty methods ***/
static inline HRESULT ITfReadOnlyProperty_GetType(ITfReadOnlyProperty* This,GUID *pguid) {
    return This->lpVtbl->GetType(This,pguid);
}
static inline HRESULT ITfReadOnlyProperty_EnumRanges(ITfReadOnlyProperty* This,TfEditCookie ec,IEnumTfRanges **ppEnum,ITfRange *pTargetRange) {
    return This->lpVtbl->EnumRanges(This,ec,ppEnum,pTargetRange);
}
static inline HRESULT ITfReadOnlyProperty_GetValue(ITfReadOnlyProperty* This,TfEditCookie ec,ITfRange *pRange,VARIANT *pvarValue) {
    return This->lpVtbl->GetValue(This,ec,pRange,pvarValue);
}
static inline HRESULT ITfReadOnlyProperty_GetContext(ITfReadOnlyProperty* This,ITfContext **ppContext) {
    return This->lpVtbl->GetContext(This,ppContext);
}
#endif
#endif

#endif


#endif  /* __ITfReadOnlyProperty_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITfProperty interface
 */
#ifndef __ITfProperty_INTERFACE_DEFINED__
#define __ITfProperty_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITfProperty, 0xe2449660, 0x9542, 0x11d2, 0xbf,0x46, 0x00,0x10,0x5a,0x27,0x99,0xb5);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("e2449660-9542-11d2-bf46-00105a2799b5")
ITfProperty : public ITfReadOnlyProperty
{
    virtual HRESULT STDMETHODCALLTYPE FindRange(
        TfEditCookie ec,
        ITfRange *pRange,
        ITfRange **ppRange,
        TfAnchor aPos) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetValueStore(
        TfEditCookie ec,
        ITfRange *pRange,
        ITfPropertyStore *pPropStore) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetValue(
        TfEditCookie ec,
        ITfRange *pRange,
        const VARIANT *pvarValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clear(
        TfEditCookie ec,
        ITfRange *pRange) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITfProperty, 0xe2449660, 0x9542, 0x11d2, 0xbf,0x46, 0x00,0x10,0x5a,0x27,0x99,0xb5)
#endif
#else
typedef struct ITfPropertyVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITfProperty *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITfProperty *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITfProperty *This);

    /*** ITfReadOnlyProperty methods ***/
    HRESULT (STDMETHODCALLTYPE *GetType)(
        ITfProperty *This,
        GUID *pguid);

    HRESULT (STDMETHODCALLTYPE *EnumRanges)(
        ITfProperty *This,
        TfEditCookie ec,
        IEnumTfRanges **ppEnum,
        ITfRange *pTargetRange);

    HRESULT (STDMETHODCALLTYPE *GetValue)(
        ITfProperty *This,
        TfEditCookie ec,
        ITfRange *pRange,
        VARIANT *pvarValue);

    HRESULT (STDMETHODCALLTYPE *GetContext)(
        ITfProperty *This,
        ITfContext **ppContext);

    /*** ITfProperty methods ***/
    HRESULT (STDMETHODCALLTYPE *FindRange)(
        ITfProperty *This,
        TfEditCookie ec,
        ITfRange *pRange,
        ITfRange **ppRange,
        TfAnchor aPos);

    HRESULT (STDMETHODCALLTYPE *SetValueStore)(
        ITfProperty *This,
        TfEditCookie ec,
        ITfRange *pRange,
        ITfPropertyStore *pPropStore);

    HRESULT (STDMETHODCALLTYPE *SetValue)(
        ITfProperty *This,
        TfEditCookie ec,
        ITfRange *pRange,
        const VARIANT *pvarValue);

    HRESULT (STDMETHODCALLTYPE *Clear)(
        ITfProperty *This,
        TfEditCookie ec,
        ITfRange *pRange);

    END_INTERFACE
} ITfPropertyVtbl;

interface ITfProperty {
    CONST_VTBL ITfPropertyVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITfProperty_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITfProperty_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITfProperty_Release(This) (This)->lpVtbl->Release(This)
/*** ITfReadOnlyProperty methods ***/
#define ITfProperty_GetType(This,pguid) (This)->lpVtbl->GetType(This,pguid)
#define ITfProperty_EnumRanges(This,ec,ppEnum,pTargetRange) (This)->lpVtbl->EnumRanges(This,ec,ppEnum,pTargetRange)
#define ITfProperty_GetValue(This,ec,pRange,pvarValue) (This)->lpVtbl->GetValue(This,ec,pRange,pvarValue)
#define ITfProperty_GetContext(This,ppContext) (This)->lpVtbl->GetContext(This,ppContext)
/*** ITfProperty methods ***/
#define ITfProperty_FindRange(This,ec,pRange,ppRange,aPos) (This)->lpVtbl->FindRange(This,ec,pRange,ppRange,aPos)
#define ITfProperty_SetValueStore(This,ec,pRange,pPropStore) (This)->lpVtbl->SetValueStore(This,ec,pRange,pPropStore)
#define ITfProperty_SetValue(This,ec,pRange,pvarValue) (This)->lpVtbl->SetValue(This,ec,pRange,pvarValue)
#define ITfProperty_Clear(This,ec,pRange) (This)->lpVtbl->Clear(This,ec,pRange)
#else
/*** IUnknown methods ***/
static inline HRESULT ITfProperty_QueryInterface(ITfProperty* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITfProperty_AddRef(ITfProperty* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITfProperty_Release(ITfProperty* This) {
    return This->lpVtbl->Release(This);
}
/*** ITfReadOnlyProperty methods ***/
static inline HRESULT ITfProperty_GetType(ITfProperty* This,GUID *pguid) {
    return This->lpVtbl->GetType(This,pguid);
}
static inline HRESULT ITfProperty_EnumRanges(ITfProperty* This,TfEditCookie ec,IEnumTfRanges **ppEnum,ITfRange *pTargetRange) {
    return This->lpVtbl->EnumRanges(This,ec,ppEnum,pTargetRange);
}
static inline HRESULT ITfProperty_GetValue(ITfProperty* This,TfEditCookie ec,ITfRange *pRange,VARIANT *pvarValue) {
    return This->lpVtbl->GetValue(This,ec,pRange,pvarValue);
}
static inline HRESULT ITfProperty_GetContext(ITfProperty* This,ITfContext **ppContext) {
    return This->lpVtbl->GetContext(This,ppContext);
}
/*** ITfProperty methods ***/
static inline HRESULT ITfProperty_FindRange(ITfProperty* This,TfEditCookie ec,ITfRange *pRange,ITfRange **ppRange,TfAnchor aPos) {
    return This->lpVtbl->FindRange(This,ec,pRange,ppRange,aPos);
}
static inline HRESULT ITfProperty_SetValueStore(ITfProperty* This,TfEditCookie ec,ITfRange *pRange,ITfPropertyStore *pPropStore) {
    return This->lpVtbl->SetValueStore(This,ec,pRange,pPropStore);
}
static inline HRESULT ITfProperty_SetValue(ITfProperty* This,TfEditCookie ec,ITfRange *pRange,const VARIANT *pvarValue) {
    return This->lpVtbl->SetValue(This,ec,pRange,pvarValue);
}
static inline HRESULT ITfProperty_Clear(ITfProperty* This,TfEditCookie ec,ITfRange *pRange) {
    return This->lpVtbl->Clear(This,ec,pRange);
}
#endif
#endif

#endif


#endif  /* __ITfProperty_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITfCompartment interface
 */
#ifndef __ITfCompartment_INTERFACE_DEFINED__
#define __ITfCompartment_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITfCompartment, 0xbb08f7a9, 0x607a, 0x4384, 0x86,0x23, 0x05,0x68,0x92,0xb6,0x43,0x71);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("bb08f7a9-607a-4384-8623-056892b64371")
ITfCompartment : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetValue(
        TfClientId tid,
        const VARIANT *pvarValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetValue(
        VARIANT *pvarValue) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITfCompartment, 0xbb08f7a9, 0x607a, 0x4384, 0x86,0x23, 0x05,0x68,0x92,0xb6,0x43,0x71)
#endif
#else
typedef struct ITfCompartmentVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITfCompartment *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITfCompartment *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITfCompartment *This);

    /*** ITfCompartment methods ***/
    HRESULT (STDMETHODCALLTYPE *SetValue)(
        ITfCompartment *This,
        TfClientId tid,
        const VARIANT *pvarValue);

    HRESULT (STDMETHODCALLTYPE *GetValue)(
        ITfCompartment *This,
        VARIANT *pvarValue);

    END_INTERFACE
} ITfCompartmentVtbl;

interface ITfCompartment {
    CONST_VTBL ITfCompartmentVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITfCompartment_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITfCompartment_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITfCompartment_Release(This) (This)->lpVtbl->Release(This)
/*** ITfCompartment methods ***/
#define ITfCompartment_SetValue(This,tid,pvarValue) (This)->lpVtbl->SetValue(This,tid,pvarValue)
#define ITfCompartment_GetValue(This,pvarValue) (This)->lpVtbl->GetValue(This,pvarValue)
#else
/*** IUnknown methods ***/
static inline HRESULT ITfCompartment_QueryInterface(ITfCompartment* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITfCompartment_AddRef(ITfCompartment* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITfCompartment_Release(ITfCompartment* This) {
    return This->lpVtbl->Release(This);
}
/*** ITfCompartment methods ***/
static inline HRESULT ITfCompartment_SetValue(ITfCompartment* This,TfClientId tid,const VARIANT *pvarValue) {
    return This->lpVtbl->SetValue(This,tid,pvarValue);
}
static inline HRESULT ITfCompartment_GetValue(ITfCompartment* This,VARIANT *pvarValue) {
    return This->lpVtbl->GetValue(This,pvarValue);
}
#endif
#endif

#endif


#endif  /* __ITfCompartment_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITfCompartmentMgr interface
 */
#ifndef __ITfCompartmentMgr_INTERFACE_DEFINED__
#define __ITfCompartmentMgr_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITfCompartmentMgr, 0x7dcf57ac, 0x18ad, 0x438b, 0x82,0x4d, 0x97,0x9b,0xff,0xb7,0x4b,0x7c);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("7dcf57ac-18ad-438b-824d-979bffb74b7c")
ITfCompartmentMgr : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetCompartment(
        REFGUID rguid,
        ITfCompartment **ppcomp) = 0;

    virtual HRESULT STDMETHODCALLTYPE ClearCompartment(
        TfClientId tid,
        REFGUID rguid) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumCompartments(
        IEnumGUID **ppEnum) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITfCompartmentMgr, 0x7dcf57ac, 0x18ad, 0x438b, 0x82,0x4d, 0x97,0x9b,0xff,0xb7,0x4b,0x7c)
#endif
#else
typedef struct ITfCompartmentMgrVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITfCompartmentMgr *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITfCompartmentMgr *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITfCompartmentMgr *This);

    /*** ITfCompartmentMgr methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCompartment)(
        ITfCompartmentMgr *This,
        REFGUID rguid,
        ITfCompartment **ppcomp);

    HRESULT (STDMETHODCALLTYPE *ClearCompartment)(
        ITfCompartmentMgr *This,
        TfClientId tid,
        REFGUID rguid);

    HRESULT (STDMETHODCALLTYPE *EnumCompartments)(
        ITfCompartmentMgr *This,
        IEnumGUID **ppEnum);

    END_INTERFACE
} ITfCompartmentMgrVtbl;

interface ITfCompartmentMgr {
    CONST_VTBL ITfCompartmentMgrVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITfCompartmentMgr_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITfCompartmentMgr_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITfCompartmentMgr_Release(This) (This)->lpVtbl->Release(This)
/*** ITfCompartmentMgr methods ***/
#define ITfCompartmentMgr_GetCompartment(This,rguid,ppcomp) (This)->lpVtbl->GetCompartment(This,rguid,ppcomp)
#define ITfCompartmentMgr_ClearCompartment(This,tid,rguid) (This)->lpVtbl->ClearCompartment(This,tid,rguid)
#define ITfCompartmentMgr_EnumCompartments(This,ppEnum) (This)->lpVtbl->EnumCompartments(This,ppEnum)
#else
/*** IUnknown methods ***/
static inline HRESULT ITfCompartmentMgr_QueryInterface(ITfCompartmentMgr* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITfCompartmentMgr_AddRef(ITfCompartmentMgr* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITfCompartmentMgr_Release(ITfCompartmentMgr* This) {
    return This->lpVtbl->Release(This);
}
/*** ITfCompartmentMgr methods ***/
static inline HRESULT ITfCompartmentMgr_GetCompartment(ITfCompartmentMgr* This,REFGUID rguid,ITfCompartment **ppcomp) {
    return This->lpVtbl->GetCompartment(This,rguid,ppcomp);
}
static inline HRESULT ITfCompartmentMgr_ClearCompartment(ITfCompartmentMgr* This,TfClientId tid,REFGUID rguid) {
    return This->lpVtbl->ClearCompartment(This,tid,rguid);
}
static inline HRESULT ITfCompartmentMgr_EnumCompartments(ITfCompartmentMgr* This,IEnumGUID **ppEnum) {
    return This->lpVtbl->EnumCompartments(This,ppEnum);
}
#endif
#endif

#endif


#endif  /* __ITfCompartmentMgr_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITfCompartmentEventSink interface
 */
#ifndef __ITfCompartmentEventSink_INTERFACE_DEFINED__
#define __ITfCompartmentEventSink_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITfCompartmentEventSink, 0x743abd5f, 0xf26d, 0x48df, 0x8c,0xc5, 0x23,0x84,0x92,0x41,0x9b,0x64);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("743abd5f-f26d-48df-8cc5-238492419b64")
ITfCompartmentEventSink : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE OnChange(
        REFGUID rguid) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITfCompartmentEventSink, 0x743abd5f, 0xf26d, 0x48df, 0x8c,0xc5, 0x23,0x84,0x92,0x41,0x9b,0x64)
#endif
#else
typedef struct ITfCompartmentEventSinkVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITfCompartmentEventSink *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITfCompartmentEventSink *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITfCompartmentEventSink *This);

    /*** ITfCompartmentEventSink methods ***/
    HRESULT (STDMETHODCALLTYPE *OnChange)(
        ITfCompartmentEventSink *This,
        REFGUID rguid);

    END_INTERFACE
} ITfCompartmentEventSinkVtbl;

interface ITfCompartmentEventSink {
    CONST_VTBL ITfCompartmentEventSinkVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITfCompartmentEventSink_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITfCompartmentEventSink_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITfCompartmentEventSink_Release(This) (This)->lpVtbl->Release(This)
/*** ITfCompartmentEventSink methods ***/
#define ITfCompartmentEventSink_OnChange(This,rguid) (This)->lpVtbl->OnChange(This,rguid)
#else
/*** IUnknown methods ***/
static inline HRESULT ITfCompartmentEventSink_QueryInterface(ITfCompartmentEventSink* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITfCompartmentEventSink_AddRef(ITfCompartmentEventSink* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITfCompartmentEventSink_Release(ITfCompartmentEventSink* This) {
    return This->lpVtbl->Release(This);
}
/*** ITfCompartmentEventSink methods ***/
static inline HRESULT ITfCompartmentEventSink_OnChange(ITfCompartmentEventSink* This,REFGUID rguid) {
    return This->lpVtbl->OnChange(This,rguid);
}
#endif
#endif

#endif


#endif  /* __ITfCompartmentEventSink_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IEnumTfContexts interface
 */
#ifndef __IEnumTfContexts_INTERFACE_DEFINED__
#define __IEnumTfContexts_INTERFACE_DEFINED__

DEFINE_GUID(IID_IEnumTfContexts, 0x8f1a7ea6, 0x1654, 0x4502, 0xa8,0x6e, 0xb2,0x90,0x23,0x44,0xd5,0x07);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("8f1a7ea6-1654-4502-a86e-b2902344d507")
IEnumTfContexts : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Clone(
        IEnumTfContexts **ppEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE Next(
        ULONG ulCount,
        ITfContext **rgContext,
        ULONG *pcFetched) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reset(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Skip(
        ULONG ulCount) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IEnumTfContexts, 0x8f1a7ea6, 0x1654, 0x4502, 0xa8,0x6e, 0xb2,0x90,0x23,0x44,0xd5,0x07)
#endif
#else
typedef struct IEnumTfContextsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IEnumTfContexts *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IEnumTfContexts *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IEnumTfContexts *This);

    /*** IEnumTfContexts methods ***/
    HRESULT (STDMETHODCALLTYPE *Clone)(
        IEnumTfContexts *This,
        IEnumTfContexts **ppEnum);

    HRESULT (STDMETHODCALLTYPE *Next)(
        IEnumTfContexts *This,
        ULONG ulCount,
        ITfContext **rgContext,
        ULONG *pcFetched);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        IEnumTfContexts *This);

    HRESULT (STDMETHODCALLTYPE *Skip)(
        IEnumTfContexts *This,
        ULONG ulCount);

    END_INTERFACE
} IEnumTfContextsVtbl;

interface IEnumTfContexts {
    CONST_VTBL IEnumTfContextsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IEnumTfContexts_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IEnumTfContexts_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IEnumTfContexts_Release(This) (This)->lpVtbl->Release(This)
/*** IEnumTfContexts methods ***/
#define IEnumTfContexts_Clone(This,ppEnum) (This)->lpVtbl->Clone(This,ppEnum)
#define IEnumTfContexts_Next(This,ulCount,rgContext,pcFetched) (This)->lpVtbl->Next(This,ulCount,rgContext,pcFetched)
#define IEnumTfContexts_Reset(This) (This)->lpVtbl->Reset(This)
#define IEnumTfContexts_Skip(This,ulCount) (This)->lpVtbl->Skip(This,ulCount)
#else
/*** IUnknown methods ***/
static inline HRESULT IEnumTfContexts_QueryInterface(IEnumTfContexts* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IEnumTfContexts_AddRef(IEnumTfContexts* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IEnumTfContexts_Release(IEnumTfContexts* This) {
    return This->lpVtbl->Release(This);
}
/*** IEnumTfContexts methods ***/
static inline HRESULT IEnumTfContexts_Clone(IEnumTfContexts* This,IEnumTfContexts **ppEnum) {
    return This->lpVtbl->Clone(This,ppEnum);
}
static inline HRESULT IEnumTfContexts_Next(IEnumTfContexts* This,ULONG ulCount,ITfContext **rgContext,ULONG *pcFetched) {
    return This->lpVtbl->Next(This,ulCount,rgContext,pcFetched);
}
static inline HRESULT IEnumTfContexts_Reset(IEnumTfContexts* This) {
    return This->lpVtbl->Reset(This);
}
static inline HRESULT IEnumTfContexts_Skip(IEnumTfContexts* This,ULONG ulCount) {
    return This->lpVtbl->Skip(This,ulCount);
}
#endif
#endif

#endif


#endif  /* __IEnumTfContexts_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IEnumTfDocumentMgrs interface
 */
#ifndef __IEnumTfDocumentMgrs_INTERFACE_DEFINED__
#define __IEnumTfDocumentMgrs_INTERFACE_DEFINED__

DEFINE_GUID(IID_IEnumTfDocumentMgrs, 0xaa80e808, 0x2021, 0x11d2, 0x93,0xe0, 0x00,0x60,0xb0,0x67,0xb8,0x6e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("aa80e808-2021-11d2-93e0-0060b067b86e")
IEnumTfDocumentMgrs : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Clone(
        IEnumTfDocumentMgrs **ppEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE Next(
        ULONG ulCount,
        ITfDocumentMgr **rgDocumentMgr,
        ULONG *pcFetched) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reset(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Skip(
        ULONG ulCount) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IEnumTfDocumentMgrs, 0xaa80e808, 0x2021, 0x11d2, 0x93,0xe0, 0x00,0x60,0xb0,0x67,0xb8,0x6e)
#endif
#else
typedef struct IEnumTfDocumentMgrsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IEnumTfDocumentMgrs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IEnumTfDocumentMgrs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IEnumTfDocumentMgrs *This);

    /*** IEnumTfDocumentMgrs methods ***/
    HRESULT (STDMETHODCALLTYPE *Clone)(
        IEnumTfDocumentMgrs *This,
        IEnumTfDocumentMgrs **ppEnum);

    HRESULT (STDMETHODCALLTYPE *Next)(
        IEnumTfDocumentMgrs *This,
        ULONG ulCount,
        ITfDocumentMgr **rgDocumentMgr,
        ULONG *pcFetched);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        IEnumTfDocumentMgrs *This);

    HRESULT (STDMETHODCALLTYPE *Skip)(
        IEnumTfDocumentMgrs *This,
        ULONG ulCount);

    END_INTERFACE
} IEnumTfDocumentMgrsVtbl;

interface IEnumTfDocumentMgrs {
    CONST_VTBL IEnumTfDocumentMgrsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IEnumTfDocumentMgrs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IEnumTfDocumentMgrs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IEnumTfDocumentMgrs_Release(This) (This)->lpVtbl->Release(This)
/*** IEnumTfDocumentMgrs methods ***/
#define IEnumTfDocumentMgrs_Clone(This,ppEnum) (This)->lpVtbl->Clone(This,ppEnum)
#define IEnumTfDocumentMgrs_Next(This,ulCount,rgDocumentMgr,pcFetched) (This)->lpVtbl->Next(This,ulCount,rgDocumentMgr,pcFetched)
#define IEnumTfDocumentMgrs_Reset(This) (This)->lpVtbl->Reset(This)
#define IEnumTfDocumentMgrs_Skip(This,ulCount) (This)->lpVtbl->Skip(This,ulCount)
#else
/*** IUnknown methods ***/
static inline HRESULT IEnumTfDocumentMgrs_QueryInterface(IEnumTfDocumentMgrs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IEnumTfDocumentMgrs_AddRef(IEnumTfDocumentMgrs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IEnumTfDocumentMgrs_Release(IEnumTfDocumentMgrs* This) {
    return This->lpVtbl->Release(This);
}
/*** IEnumTfDocumentMgrs methods ***/
static inline HRESULT IEnumTfDocumentMgrs_Clone(IEnumTfDocumentMgrs* This,IEnumTfDocumentMgrs **ppEnum) {
    return This->lpVtbl->Clone(This,ppEnum);
}
static inline HRESULT IEnumTfDocumentMgrs_Next(IEnumTfDocumentMgrs* This,ULONG ulCount,ITfDocumentMgr **rgDocumentMgr,ULONG *pcFetched) {
    return This->lpVtbl->Next(This,ulCount,rgDocumentMgr,pcFetched);
}
static inline HRESULT IEnumTfDocumentMgrs_Reset(IEnumTfDocumentMgrs* This) {
    return This->lpVtbl->Reset(This);
}
static inline HRESULT IEnumTfDocumentMgrs_Skip(IEnumTfDocumentMgrs* This,ULONG ulCount) {
    return This->lpVtbl->Skip(This,ulCount);
}
#endif
#endif

#endif


#endif  /* __IEnumTfDocumentMgrs_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITfUIElement interface
 */
#ifndef __ITfUIElement_INTERFACE_DEFINED__
#define __ITfUIElement_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITfUIElement, 0xea1ea137, 0x19df, 0x11d7, 0xa6,0xd2, 0x00,0x06,0x5b,0x84,0x43,0x5c);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("ea1ea137-19df-11d7-a6d2-00065b84435c")
ITfUIElement : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetDescription(
        BSTR *description) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetGUID(
        GUID *guid) = 0;

    virtual HRESULT STDMETHODCALLTYPE Show(
        WINBOOL show) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsShown(
        WINBOOL *show) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITfUIElement, 0xea1ea137, 0x19df, 0x11d7, 0xa6,0xd2, 0x00,0x06,0x5b,0x84,0x43,0x5c)
#endif
#else
typedef struct ITfUIElementVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITfUIElement *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITfUIElement *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITfUIElement *This);

    /*** ITfUIElement methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDescription)(
        ITfUIElement *This,
        BSTR *description);

    HRESULT (STDMETHODCALLTYPE *GetGUID)(
        ITfUIElement *This,
        GUID *guid);

    HRESULT (STDMETHODCALLTYPE *Show)(
        ITfUIElement *This,
        WINBOOL show);

    HRESULT (STDMETHODCALLTYPE *IsShown)(
        ITfUIElement *This,
        WINBOOL *show);

    END_INTERFACE
} ITfUIElementVtbl;

interface ITfUIElement {
    CONST_VTBL ITfUIElementVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITfUIElement_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITfUIElement_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITfUIElement_Release(This) (This)->lpVtbl->Release(This)
/*** ITfUIElement methods ***/
#define ITfUIElement_GetDescription(This,description) (This)->lpVtbl->GetDescription(This,description)
#define ITfUIElement_GetGUID(This,guid) (This)->lpVtbl->GetGUID(This,guid)
#define ITfUIElement_Show(This,show) (This)->lpVtbl->Show(This,show)
#define ITfUIElement_IsShown(This,show) (This)->lpVtbl->IsShown(This,show)
#else
/*** IUnknown methods ***/
static inline HRESULT ITfUIElement_QueryInterface(ITfUIElement* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITfUIElement_AddRef(ITfUIElement* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITfUIElement_Release(ITfUIElement* This) {
    return This->lpVtbl->Release(This);
}
/*** ITfUIElement methods ***/
static inline HRESULT ITfUIElement_GetDescription(ITfUIElement* This,BSTR *description) {
    return This->lpVtbl->GetDescription(This,description);
}
static inline HRESULT ITfUIElement_GetGUID(ITfUIElement* This,GUID *guid) {
    return This->lpVtbl->GetGUID(This,guid);
}
static inline HRESULT ITfUIElement_Show(ITfUIElement* This,WINBOOL show) {
    return This->lpVtbl->Show(This,show);
}
static inline HRESULT ITfUIElement_IsShown(ITfUIElement* This,WINBOOL *show) {
    return This->lpVtbl->IsShown(This,show);
}
#endif
#endif

#endif


#endif  /* __ITfUIElement_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IEnumTfUIElements interface
 */
#ifndef __IEnumTfUIElements_INTERFACE_DEFINED__
#define __IEnumTfUIElements_INTERFACE_DEFINED__

DEFINE_GUID(IID_IEnumTfUIElements, 0x887aa91e, 0xacba, 0x4931, 0x84,0xda, 0x3c,0x52,0x08,0xcf,0x54,0x3f);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("887aa91e-acba-4931-84da-3c5208cf543f")
IEnumTfUIElements : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Clone(
        IEnumTfUIElements **enum_elements) = 0;

    virtual HRESULT STDMETHODCALLTYPE Next(
        ULONG count,
        ITfUIElement **element,
        ULONG *fetched) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reset(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Skip(
        ULONG count) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IEnumTfUIElements, 0x887aa91e, 0xacba, 0x4931, 0x84,0xda, 0x3c,0x52,0x08,0xcf,0x54,0x3f)
#endif
#else
typedef struct IEnumTfUIElementsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IEnumTfUIElements *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IEnumTfUIElements *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IEnumTfUIElements *This);

    /*** IEnumTfUIElements methods ***/
    HRESULT (STDMETHODCALLTYPE *Clone)(
        IEnumTfUIElements *This,
        IEnumTfUIElements **enum_elements);

    HRESULT (STDMETHODCALLTYPE *Next)(
        IEnumTfUIElements *This,
        ULONG count,
        ITfUIElement **element,
        ULONG *fetched);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        IEnumTfUIElements *This);

    HRESULT (STDMETHODCALLTYPE *Skip)(
        IEnumTfUIElements *This,
        ULONG count);

    END_INTERFACE
} IEnumTfUIElementsVtbl;

interface IEnumTfUIElements {
    CONST_VTBL IEnumTfUIElementsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IEnumTfUIElements_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IEnumTfUIElements_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IEnumTfUIElements_Release(This) (This)->lpVtbl->Release(This)
/*** IEnumTfUIElements methods ***/
#define IEnumTfUIElements_Clone(This,enum_elements) (This)->lpVtbl->Clone(This,enum_elements)
#define IEnumTfUIElements_Next(This,count,element,fetched) (This)->lpVtbl->Next(This,count,element,fetched)
#define IEnumTfUIElements_Reset(This) (This)->lpVtbl->Reset(This)
#define IEnumTfUIElements_Skip(This,count) (This)->lpVtbl->Skip(This,count)
#else
/*** IUnknown methods ***/
static inline HRESULT IEnumTfUIElements_QueryInterface(IEnumTfUIElements* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IEnumTfUIElements_AddRef(IEnumTfUIElements* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IEnumTfUIElements_Release(IEnumTfUIElements* This) {
    return This->lpVtbl->Release(This);
}
/*** IEnumTfUIElements methods ***/
static inline HRESULT IEnumTfUIElements_Clone(IEnumTfUIElements* This,IEnumTfUIElements **enum_elements) {
    return This->lpVtbl->Clone(This,enum_elements);
}
static inline HRESULT IEnumTfUIElements_Next(IEnumTfUIElements* This,ULONG count,ITfUIElement **element,ULONG *fetched) {
    return This->lpVtbl->Next(This,count,element,fetched);
}
static inline HRESULT IEnumTfUIElements_Reset(IEnumTfUIElements* This) {
    return This->lpVtbl->Reset(This);
}
static inline HRESULT IEnumTfUIElements_Skip(IEnumTfUIElements* This,ULONG count) {
    return This->lpVtbl->Skip(This,count);
}
#endif
#endif

#endif


#endif  /* __IEnumTfUIElements_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITfUIElementMgr interface
 */
#ifndef __ITfUIElementMgr_INTERFACE_DEFINED__
#define __ITfUIElementMgr_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITfUIElementMgr, 0xea1ea135, 0x19df, 0x11d7, 0xa6,0xd2, 0x00,0x06,0x5b,0x84,0x43,0x5c);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("ea1ea135-19df-11d7-a6d2-00065b84435c")
ITfUIElementMgr : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE BeginUIElement(
        ITfUIElement *element,
        WINBOOL *show,
        DWORD *id) = 0;

    virtual HRESULT STDMETHODCALLTYPE UpdateUIElement(
        DWORD id) = 0;

    virtual HRESULT STDMETHODCALLTYPE EndUIElement(
        DWORD id) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetUIElement(
        DWORD id,
        ITfUIElement **element) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumUIElements(
        IEnumTfUIElements **enum_elements) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITfUIElementMgr, 0xea1ea135, 0x19df, 0x11d7, 0xa6,0xd2, 0x00,0x06,0x5b,0x84,0x43,0x5c)
#endif
#else
typedef struct ITfUIElementMgrVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITfUIElementMgr *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITfUIElementMgr *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITfUIElementMgr *This);

    /*** ITfUIElementMgr methods ***/
    HRESULT (STDMETHODCALLTYPE *BeginUIElement)(
        ITfUIElementMgr *This,
        ITfUIElement *element,
        WINBOOL *show,
        DWORD *id);

    HRESULT (STDMETHODCALLTYPE *UpdateUIElement)(
        ITfUIElementMgr *This,
        DWORD id);

    HRESULT (STDMETHODCALLTYPE *EndUIElement)(
        ITfUIElementMgr *This,
        DWORD id);

    HRESULT (STDMETHODCALLTYPE *GetUIElement)(
        ITfUIElementMgr *This,
        DWORD id,
        ITfUIElement **element);

    HRESULT (STDMETHODCALLTYPE *EnumUIElements)(
        ITfUIElementMgr *This,
        IEnumTfUIElements **enum_elements);

    END_INTERFACE
} ITfUIElementMgrVtbl;

interface ITfUIElementMgr {
    CONST_VTBL ITfUIElementMgrVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITfUIElementMgr_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITfUIElementMgr_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITfUIElementMgr_Release(This) (This)->lpVtbl->Release(This)
/*** ITfUIElementMgr methods ***/
#define ITfUIElementMgr_BeginUIElement(This,element,show,id) (This)->lpVtbl->BeginUIElement(This,element,show,id)
#define ITfUIElementMgr_UpdateUIElement(This,id) (This)->lpVtbl->UpdateUIElement(This,id)
#define ITfUIElementMgr_EndUIElement(This,id) (This)->lpVtbl->EndUIElement(This,id)
#define ITfUIElementMgr_GetUIElement(This,id,element) (This)->lpVtbl->GetUIElement(This,id,element)
#define ITfUIElementMgr_EnumUIElements(This,enum_elements) (This)->lpVtbl->EnumUIElements(This,enum_elements)
#else
/*** IUnknown methods ***/
static inline HRESULT ITfUIElementMgr_QueryInterface(ITfUIElementMgr* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITfUIElementMgr_AddRef(ITfUIElementMgr* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITfUIElementMgr_Release(ITfUIElementMgr* This) {
    return This->lpVtbl->Release(This);
}
/*** ITfUIElementMgr methods ***/
static inline HRESULT ITfUIElementMgr_BeginUIElement(ITfUIElementMgr* This,ITfUIElement *element,WINBOOL *show,DWORD *id) {
    return This->lpVtbl->BeginUIElement(This,element,show,id);
}
static inline HRESULT ITfUIElementMgr_UpdateUIElement(ITfUIElementMgr* This,DWORD id) {
    return This->lpVtbl->UpdateUIElement(This,id);
}
static inline HRESULT ITfUIElementMgr_EndUIElement(ITfUIElementMgr* This,DWORD id) {
    return This->lpVtbl->EndUIElement(This,id);
}
static inline HRESULT ITfUIElementMgr_GetUIElement(ITfUIElementMgr* This,DWORD id,ITfUIElement **element) {
    return This->lpVtbl->GetUIElement(This,id,element);
}
static inline HRESULT ITfUIElementMgr_EnumUIElements(ITfUIElementMgr* This,IEnumTfUIElements **enum_elements) {
    return This->lpVtbl->EnumUIElements(This,enum_elements);
}
#endif
#endif

#endif


#endif  /* __ITfUIElementMgr_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITfSourceSingle interface
 */
#ifndef __ITfSourceSingle_INTERFACE_DEFINED__
#define __ITfSourceSingle_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITfSourceSingle, 0x73131f9c, 0x56a9, 0x49dd, 0xb0,0xee, 0xd0,0x46,0x63,0x3f,0x75,0x28);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("73131f9c-56a9-49dd-b0ee-d046633f7528")
ITfSourceSingle : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE AdviseSingleSink(
        TfClientId tid,
        REFIID riid,
        IUnknown *punk) = 0;

    virtual HRESULT STDMETHODCALLTYPE UnadviseSingleSink(
        TfClientId tid,
        REFIID riid) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITfSourceSingle, 0x73131f9c, 0x56a9, 0x49dd, 0xb0,0xee, 0xd0,0x46,0x63,0x3f,0x75,0x28)
#endif
#else
typedef struct ITfSourceSingleVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITfSourceSingle *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITfSourceSingle *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITfSourceSingle *This);

    /*** ITfSourceSingle methods ***/
    HRESULT (STDMETHODCALLTYPE *AdviseSingleSink)(
        ITfSourceSingle *This,
        TfClientId tid,
        REFIID riid,
        IUnknown *punk);

    HRESULT (STDMETHODCALLTYPE *UnadviseSingleSink)(
        ITfSourceSingle *This,
        TfClientId tid,
        REFIID riid);

    END_INTERFACE
} ITfSourceSingleVtbl;

interface ITfSourceSingle {
    CONST_VTBL ITfSourceSingleVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITfSourceSingle_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITfSourceSingle_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITfSourceSingle_Release(This) (This)->lpVtbl->Release(This)
/*** ITfSourceSingle methods ***/
#define ITfSourceSingle_AdviseSingleSink(This,tid,riid,punk) (This)->lpVtbl->AdviseSingleSink(This,tid,riid,punk)
#define ITfSourceSingle_UnadviseSingleSink(This,tid,riid) (This)->lpVtbl->UnadviseSingleSink(This,tid,riid)
#else
/*** IUnknown methods ***/
static inline HRESULT ITfSourceSingle_QueryInterface(ITfSourceSingle* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITfSourceSingle_AddRef(ITfSourceSingle* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITfSourceSingle_Release(ITfSourceSingle* This) {
    return This->lpVtbl->Release(This);
}
/*** ITfSourceSingle methods ***/
static inline HRESULT ITfSourceSingle_AdviseSingleSink(ITfSourceSingle* This,TfClientId tid,REFIID riid,IUnknown *punk) {
    return This->lpVtbl->AdviseSingleSink(This,tid,riid,punk);
}
static inline HRESULT ITfSourceSingle_UnadviseSingleSink(ITfSourceSingle* This,TfClientId tid,REFIID riid) {
    return This->lpVtbl->UnadviseSingleSink(This,tid,riid);
}
#endif
#endif

#endif


#endif  /* __ITfSourceSingle_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITfThreadFocusSink interface
 */
#ifndef __ITfThreadFocusSink_INTERFACE_DEFINED__
#define __ITfThreadFocusSink_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITfThreadFocusSink, 0xc0f1db0c, 0x3a20, 0x405c, 0xa3,0x03, 0x96,0xb6,0x01,0x0a,0x88,0x5f);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("c0f1db0c-3a20-405c-a303-96b6010a885f")
ITfThreadFocusSink : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE OnSetThreadFocus(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnKillThreadFocus(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITfThreadFocusSink, 0xc0f1db0c, 0x3a20, 0x405c, 0xa3,0x03, 0x96,0xb6,0x01,0x0a,0x88,0x5f)
#endif
#else
typedef struct ITfThreadFocusSinkVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITfThreadFocusSink *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITfThreadFocusSink *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITfThreadFocusSink *This);

    /*** ITfThreadFocusSink methods ***/
    HRESULT (STDMETHODCALLTYPE *OnSetThreadFocus)(
        ITfThreadFocusSink *This);

    HRESULT (STDMETHODCALLTYPE *OnKillThreadFocus)(
        ITfThreadFocusSink *This);

    END_INTERFACE
} ITfThreadFocusSinkVtbl;

interface ITfThreadFocusSink {
    CONST_VTBL ITfThreadFocusSinkVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITfThreadFocusSink_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITfThreadFocusSink_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITfThreadFocusSink_Release(This) (This)->lpVtbl->Release(This)
/*** ITfThreadFocusSink methods ***/
#define ITfThreadFocusSink_OnSetThreadFocus(This) (This)->lpVtbl->OnSetThreadFocus(This)
#define ITfThreadFocusSink_OnKillThreadFocus(This) (This)->lpVtbl->OnKillThreadFocus(This)
#else
/*** IUnknown methods ***/
static inline HRESULT ITfThreadFocusSink_QueryInterface(ITfThreadFocusSink* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITfThreadFocusSink_AddRef(ITfThreadFocusSink* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITfThreadFocusSink_Release(ITfThreadFocusSink* This) {
    return This->lpVtbl->Release(This);
}
/*** ITfThreadFocusSink methods ***/
static inline HRESULT ITfThreadFocusSink_OnSetThreadFocus(ITfThreadFocusSink* This) {
    return This->lpVtbl->OnSetThreadFocus(This);
}
static inline HRESULT ITfThreadFocusSink_OnKillThreadFocus(ITfThreadFocusSink* This) {
    return This->lpVtbl->OnKillThreadFocus(This);
}
#endif
#endif

#endif


#endif  /* __ITfThreadFocusSink_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITfInputProcessorProfileActivationSink interface
 */
#ifndef __ITfInputProcessorProfileActivationSink_INTERFACE_DEFINED__
#define __ITfInputProcessorProfileActivationSink_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITfInputProcessorProfileActivationSink, 0x71c6e74e, 0x0f28, 0x11d8, 0xa8,0x2a, 0x00,0x06,0x5b,0x84,0x43,0x5c);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("71c6e74e-0f28-11d8-a82a-00065b84435c")
ITfInputProcessorProfileActivationSink : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE OnActivated(
        DWORD dwProfileType,
        LANGID langid,
        REFCLSID clsid,
        REFGUID catid,
        REFGUID guidProfile,
        HKL hkl,
        DWORD dwFlags) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITfInputProcessorProfileActivationSink, 0x71c6e74e, 0x0f28, 0x11d8, 0xa8,0x2a, 0x00,0x06,0x5b,0x84,0x43,0x5c)
#endif
#else
typedef struct ITfInputProcessorProfileActivationSinkVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITfInputProcessorProfileActivationSink *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITfInputProcessorProfileActivationSink *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITfInputProcessorProfileActivationSink *This);

    /*** ITfInputProcessorProfileActivationSink methods ***/
    HRESULT (STDMETHODCALLTYPE *OnActivated)(
        ITfInputProcessorProfileActivationSink *This,
        DWORD dwProfileType,
        LANGID langid,
        REFCLSID clsid,
        REFGUID catid,
        REFGUID guidProfile,
        HKL hkl,
        DWORD dwFlags);

    END_INTERFACE
} ITfInputProcessorProfileActivationSinkVtbl;

interface ITfInputProcessorProfileActivationSink {
    CONST_VTBL ITfInputProcessorProfileActivationSinkVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITfInputProcessorProfileActivationSink_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITfInputProcessorProfileActivationSink_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITfInputProcessorProfileActivationSink_Release(This) (This)->lpVtbl->Release(This)
/*** ITfInputProcessorProfileActivationSink methods ***/
#define ITfInputProcessorProfileActivationSink_OnActivated(This,dwProfileType,langid,clsid,catid,guidProfile,hkl,dwFlags) (This)->lpVtbl->OnActivated(This,dwProfileType,langid,clsid,catid,guidProfile,hkl,dwFlags)
#else
/*** IUnknown methods ***/
static inline HRESULT ITfInputProcessorProfileActivationSink_QueryInterface(ITfInputProcessorProfileActivationSink* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITfInputProcessorProfileActivationSink_AddRef(ITfInputProcessorProfileActivationSink* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITfInputProcessorProfileActivationSink_Release(ITfInputProcessorProfileActivationSink* This) {
    return This->lpVtbl->Release(This);
}
/*** ITfInputProcessorProfileActivationSink methods ***/
static inline HRESULT ITfInputProcessorProfileActivationSink_OnActivated(ITfInputProcessorProfileActivationSink* This,DWORD dwProfileType,LANGID langid,REFCLSID clsid,REFGUID catid,REFGUID guidProfile,HKL hkl,DWORD dwFlags) {
    return This->lpVtbl->OnActivated(This,dwProfileType,langid,clsid,catid,guidProfile,hkl,dwFlags);
}
#endif
#endif

#endif


#endif  /* __ITfInputProcessorProfileActivationSink_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITfMouseSink interface
 */
#ifndef __ITfMouseSink_INTERFACE_DEFINED__
#define __ITfMouseSink_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITfMouseSink, 0xa1adaaa2, 0x3a24, 0x449d, 0xac,0x96, 0x51,0x83,0xe7,0xf5,0xc2,0x17);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a1adaaa2-3a24-449d-ac96-5183e7f5c217")
ITfMouseSink : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE OnMouseEvent(
        ULONG uEdge,
        ULONG uQuadrant,
        DWORD dwBtnStatus,
        WINBOOL *pfEaten) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITfMouseSink, 0xa1adaaa2, 0x3a24, 0x449d, 0xac,0x96, 0x51,0x83,0xe7,0xf5,0xc2,0x17)
#endif
#else
typedef struct ITfMouseSinkVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITfMouseSink *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITfMouseSink *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITfMouseSink *This);

    /*** ITfMouseSink methods ***/
    HRESULT (STDMETHODCALLTYPE *OnMouseEvent)(
        ITfMouseSink *This,
        ULONG uEdge,
        ULONG uQuadrant,
        DWORD dwBtnStatus,
        WINBOOL *pfEaten);

    END_INTERFACE
} ITfMouseSinkVtbl;

interface ITfMouseSink {
    CONST_VTBL ITfMouseSinkVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITfMouseSink_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITfMouseSink_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITfMouseSink_Release(This) (This)->lpVtbl->Release(This)
/*** ITfMouseSink methods ***/
#define ITfMouseSink_OnMouseEvent(This,uEdge,uQuadrant,dwBtnStatus,pfEaten) (This)->lpVtbl->OnMouseEvent(This,uEdge,uQuadrant,dwBtnStatus,pfEaten)
#else
/*** IUnknown methods ***/
static inline HRESULT ITfMouseSink_QueryInterface(ITfMouseSink* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITfMouseSink_AddRef(ITfMouseSink* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITfMouseSink_Release(ITfMouseSink* This) {
    return This->lpVtbl->Release(This);
}
/*** ITfMouseSink methods ***/
static inline HRESULT ITfMouseSink_OnMouseEvent(ITfMouseSink* This,ULONG uEdge,ULONG uQuadrant,DWORD dwBtnStatus,WINBOOL *pfEaten) {
    return This->lpVtbl->OnMouseEvent(This,uEdge,uQuadrant,dwBtnStatus,pfEaten);
}
#endif
#endif

#endif


#endif  /* __ITfMouseSink_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITfMouseTracker interface
 */
#ifndef __ITfMouseTracker_INTERFACE_DEFINED__
#define __ITfMouseTracker_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITfMouseTracker, 0x09d146cd, 0xa544, 0x4132, 0x92,0x5b, 0x7a,0xfa,0x8e,0xf3,0x22,0xd0);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("09d146cd-a544-4132-925b-7afa8ef322d0")
ITfMouseTracker : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE AdviseMouseSink(
        ITfRange *range,
        ITfMouseSink *pSink,
        DWORD *pdwCookie) = 0;

    virtual HRESULT STDMETHODCALLTYPE UnadviseMouseSink(
        DWORD dwCookie) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITfMouseTracker, 0x09d146cd, 0xa544, 0x4132, 0x92,0x5b, 0x7a,0xfa,0x8e,0xf3,0x22,0xd0)
#endif
#else
typedef struct ITfMouseTrackerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITfMouseTracker *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITfMouseTracker *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITfMouseTracker *This);

    /*** ITfMouseTracker methods ***/
    HRESULT (STDMETHODCALLTYPE *AdviseMouseSink)(
        ITfMouseTracker *This,
        ITfRange *range,
        ITfMouseSink *pSink,
        DWORD *pdwCookie);

    HRESULT (STDMETHODCALLTYPE *UnadviseMouseSink)(
        ITfMouseTracker *This,
        DWORD dwCookie);

    END_INTERFACE
} ITfMouseTrackerVtbl;

interface ITfMouseTracker {
    CONST_VTBL ITfMouseTrackerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITfMouseTracker_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITfMouseTracker_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITfMouseTracker_Release(This) (This)->lpVtbl->Release(This)
/*** ITfMouseTracker methods ***/
#define ITfMouseTracker_AdviseMouseSink(This,range,pSink,pdwCookie) (This)->lpVtbl->AdviseMouseSink(This,range,pSink,pdwCookie)
#define ITfMouseTracker_UnadviseMouseSink(This,dwCookie) (This)->lpVtbl->UnadviseMouseSink(This,dwCookie)
#else
/*** IUnknown methods ***/
static inline HRESULT ITfMouseTracker_QueryInterface(ITfMouseTracker* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITfMouseTracker_AddRef(ITfMouseTracker* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITfMouseTracker_Release(ITfMouseTracker* This) {
    return This->lpVtbl->Release(This);
}
/*** ITfMouseTracker methods ***/
static inline HRESULT ITfMouseTracker_AdviseMouseSink(ITfMouseTracker* This,ITfRange *range,ITfMouseSink *pSink,DWORD *pdwCookie) {
    return This->lpVtbl->AdviseMouseSink(This,range,pSink,pdwCookie);
}
static inline HRESULT ITfMouseTracker_UnadviseMouseSink(ITfMouseTracker* This,DWORD dwCookie) {
    return This->lpVtbl->UnadviseMouseSink(This,dwCookie);
}
#endif
#endif

#endif


#endif  /* __ITfMouseTracker_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITfMouseTrackerACP interface
 */
#ifndef __ITfMouseTrackerACP_INTERFACE_DEFINED__
#define __ITfMouseTrackerACP_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITfMouseTrackerACP, 0x3bdd78e2, 0xc16e, 0x47fd, 0xb8,0x83, 0xce,0x6f,0xac,0xc1,0xa2,0x08);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("3bdd78e2-c16e-47fd-b883-ce6facc1a208")
ITfMouseTrackerACP : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE AdviseMouseSink(
        ITfRangeACP *range,
        ITfMouseSink *pSink,
        DWORD *pdwCookie) = 0;

    virtual HRESULT STDMETHODCALLTYPE UnadviseMouseSink(
        DWORD dwCookie) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITfMouseTrackerACP, 0x3bdd78e2, 0xc16e, 0x47fd, 0xb8,0x83, 0xce,0x6f,0xac,0xc1,0xa2,0x08)
#endif
#else
typedef struct ITfMouseTrackerACPVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITfMouseTrackerACP *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITfMouseTrackerACP *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITfMouseTrackerACP *This);

    /*** ITfMouseTrackerACP methods ***/
    HRESULT (STDMETHODCALLTYPE *AdviseMouseSink)(
        ITfMouseTrackerACP *This,
        ITfRangeACP *range,
        ITfMouseSink *pSink,
        DWORD *pdwCookie);

    HRESULT (STDMETHODCALLTYPE *UnadviseMouseSink)(
        ITfMouseTrackerACP *This,
        DWORD dwCookie);

    END_INTERFACE
} ITfMouseTrackerACPVtbl;

interface ITfMouseTrackerACP {
    CONST_VTBL ITfMouseTrackerACPVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITfMouseTrackerACP_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITfMouseTrackerACP_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITfMouseTrackerACP_Release(This) (This)->lpVtbl->Release(This)
/*** ITfMouseTrackerACP methods ***/
#define ITfMouseTrackerACP_AdviseMouseSink(This,range,pSink,pdwCookie) (This)->lpVtbl->AdviseMouseSink(This,range,pSink,pdwCookie)
#define ITfMouseTrackerACP_UnadviseMouseSink(This,dwCookie) (This)->lpVtbl->UnadviseMouseSink(This,dwCookie)
#else
/*** IUnknown methods ***/
static inline HRESULT ITfMouseTrackerACP_QueryInterface(ITfMouseTrackerACP* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITfMouseTrackerACP_AddRef(ITfMouseTrackerACP* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITfMouseTrackerACP_Release(ITfMouseTrackerACP* This) {
    return This->lpVtbl->Release(This);
}
/*** ITfMouseTrackerACP methods ***/
static inline HRESULT ITfMouseTrackerACP_AdviseMouseSink(ITfMouseTrackerACP* This,ITfRangeACP *range,ITfMouseSink *pSink,DWORD *pdwCookie) {
    return This->lpVtbl->AdviseMouseSink(This,range,pSink,pdwCookie);
}
static inline HRESULT ITfMouseTrackerACP_UnadviseMouseSink(ITfMouseTrackerACP* This,DWORD dwCookie) {
    return This->lpVtbl->UnadviseMouseSink(This,dwCookie);
}
#endif
#endif

#endif


#endif  /* __ITfMouseTrackerACP_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITfTransitoryExtensionSink interface
 */
#ifndef __ITfTransitoryExtensionSink_INTERFACE_DEFINED__
#define __ITfTransitoryExtensionSink_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITfTransitoryExtensionSink, 0xa615096f, 0x1c57, 0x4813, 0x8a,0x15, 0x55,0xee,0x6e,0x5a,0x83,0x9c);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a615096f-1c57-4813-8a15-55ee6e5a839c")
ITfTransitoryExtensionSink : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE OnTransitoryExtensionUpdated(
        ITfContext *pic,
        TfEditCookie ecReadOnly,
        ITfRange *pResultRange,
        ITfRange *pCompositionRange,
        WINBOOL *pfDeleteResultRange) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITfTransitoryExtensionSink, 0xa615096f, 0x1c57, 0x4813, 0x8a,0x15, 0x55,0xee,0x6e,0x5a,0x83,0x9c)
#endif
#else
typedef struct ITfTransitoryExtensionSinkVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITfTransitoryExtensionSink *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITfTransitoryExtensionSink *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITfTransitoryExtensionSink *This);

    /*** ITfTransitoryExtensionSink methods ***/
    HRESULT (STDMETHODCALLTYPE *OnTransitoryExtensionUpdated)(
        ITfTransitoryExtensionSink *This,
        ITfContext *pic,
        TfEditCookie ecReadOnly,
        ITfRange *pResultRange,
        ITfRange *pCompositionRange,
        WINBOOL *pfDeleteResultRange);

    END_INTERFACE
} ITfTransitoryExtensionSinkVtbl;

interface ITfTransitoryExtensionSink {
    CONST_VTBL ITfTransitoryExtensionSinkVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITfTransitoryExtensionSink_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITfTransitoryExtensionSink_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITfTransitoryExtensionSink_Release(This) (This)->lpVtbl->Release(This)
/*** ITfTransitoryExtensionSink methods ***/
#define ITfTransitoryExtensionSink_OnTransitoryExtensionUpdated(This,pic,ecReadOnly,pResultRange,pCompositionRange,pfDeleteResultRange) (This)->lpVtbl->OnTransitoryExtensionUpdated(This,pic,ecReadOnly,pResultRange,pCompositionRange,pfDeleteResultRange)
#else
/*** IUnknown methods ***/
static inline HRESULT ITfTransitoryExtensionSink_QueryInterface(ITfTransitoryExtensionSink* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITfTransitoryExtensionSink_AddRef(ITfTransitoryExtensionSink* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITfTransitoryExtensionSink_Release(ITfTransitoryExtensionSink* This) {
    return This->lpVtbl->Release(This);
}
/*** ITfTransitoryExtensionSink methods ***/
static inline HRESULT ITfTransitoryExtensionSink_OnTransitoryExtensionUpdated(ITfTransitoryExtensionSink* This,ITfContext *pic,TfEditCookie ecReadOnly,ITfRange *pResultRange,ITfRange *pCompositionRange,WINBOOL *pfDeleteResultRange) {
    return This->lpVtbl->OnTransitoryExtensionUpdated(This,pic,ecReadOnly,pResultRange,pCompositionRange,pfDeleteResultRange);
}
#endif
#endif

#endif


#endif  /* __ITfTransitoryExtensionSink_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER BSTR_UserSize     (ULONG *, ULONG, BSTR *);
unsigned char * __RPC_USER BSTR_UserMarshal  (ULONG *, unsigned char *, BSTR *);
unsigned char * __RPC_USER BSTR_UserUnmarshal(ULONG *, unsigned char *, BSTR *);
void            __RPC_USER BSTR_UserFree     (ULONG *, BSTR *);
ULONG           __RPC_USER HWND_UserSize     (ULONG *, ULONG, HWND *);
unsigned char * __RPC_USER HWND_UserMarshal  (ULONG *, unsigned char *, HWND *);
unsigned char * __RPC_USER HWND_UserUnmarshal(ULONG *, unsigned char *, HWND *);
void            __RPC_USER HWND_UserFree     (ULONG *, HWND *);
ULONG           __RPC_USER VARIANT_UserSize     (ULONG *, ULONG, VARIANT *);
unsigned char * __RPC_USER VARIANT_UserMarshal  (ULONG *, unsigned char *, VARIANT *);
unsigned char * __RPC_USER VARIANT_UserUnmarshal(ULONG *, unsigned char *, VARIANT *);
void            __RPC_USER VARIANT_UserFree     (ULONG *, VARIANT *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __msctf_h__ */
