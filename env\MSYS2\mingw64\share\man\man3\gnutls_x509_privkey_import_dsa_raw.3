.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_privkey_import_dsa_raw" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_privkey_import_dsa_raw \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_privkey_import_dsa_raw(gnutls_x509_privkey_t " key ", const gnutls_datum_t * " p ", const gnutls_datum_t * " q ", const gnutls_datum_t * " g ", const gnutls_datum_t * " y ", const gnutls_datum_t * " x ");"
.SH ARGUMENTS
.IP "gnutls_x509_privkey_t key" 12
The data to store the parsed key
.IP "const gnutls_datum_t * p" 12
holds the p
.IP "const gnutls_datum_t * q" 12
holds the q
.IP "const gnutls_datum_t * g" 12
holds the g
.IP "const gnutls_datum_t * y" 12
holds the y (optional)
.IP "const gnutls_datum_t * x" 12
holds the x
.SH "DESCRIPTION"
This function will convert the given DSA raw parameters to the
native \fBgnutls_x509_privkey_t\fP format.  The output will be stored
in  \fIkey\fP .
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
