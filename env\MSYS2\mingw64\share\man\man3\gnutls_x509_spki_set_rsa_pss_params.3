.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_spki_set_rsa_pss_params" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_spki_set_rsa_pss_params \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "void gnutls_x509_spki_set_rsa_pss_params(gnutls_x509_spki_t " spki ", gnutls_digest_algorithm_t " dig ", unsigned int " salt_size ");"
.SH ARGUMENTS
.IP "gnutls_x509_spki_t spki" 12
the SubjectPublicKeyInfo structure
.IP "gnutls_digest_algorithm_t dig" 12
a digest algorithm of type \fBgnutls_digest_algorithm_t\fP
.IP "unsigned int salt_size" 12
the size of salt string
.SH "DESCRIPTION"
This function will set the public key parameters for
an RSA\-PSS algorithm, in the SubjectPublicKeyInfo structure.
.SH "SINCE"
3.6.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
