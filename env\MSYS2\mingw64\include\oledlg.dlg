/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */
#define IDC_STATIC -1
#include "oledlg.h"

IDD_INSERTOBJECT DIALOG DISCARDABLE 6,18,293,150
STYLE DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "Insert Object"
FONT 8,"MS Shell Dlg"
BEGIN
    CONTROL "Create &New",IDC_IO_CREATENEW,"But<PERSON>",BS_AUTORADIOBUTTON | WS_GROUP | WS_TABSTOP,6,18,73,10
    CONTROL "Create from &File",IDC_IO_CREATEFROMFILE,"But<PERSON>",BS_AUTORADIOBUTTON | WS_TABSTOP,6,36,73,10
    CONTROL "Create &Control",IDC_IO_INSERTCONT<PERSON><PERSON>,"<PERSON><PERSON>",<PERSON>_<PERSON>TORADIOBUTTON,6,54,72,10
    LTEXT "Object &Type:",IDC_IO_OBJECTTYPETEXT,81,6,110,8
    LISTBOX IDC_IO_OBJECTTYPELIST,82,17,132,78,LBS_SORT |
                    LBS_USETABSTOPS | WS_VSCROLL | WS_GROUP | WS_TABSTOP
    LTEXT "Fil&e:",IDC_IO_FILETEXT,82,24,20,8
    LTEXT "",IDC_IO_FILETYPE,120,24,80,8
    EDITTEXT IDC_IO_FILEDISPLAY,82,34,132,12,ES_AUTOHSCROLL
    PUSHBUTTON "&Browse...",IDC_IO_FILE,82,50,48,14
    CONTROL "&Link",IDC_IO_LINKFILE,"Button",BS_AUTOCHECKBOX |
                    WS_TABSTOP,140,52,40,10
    LISTBOX IDC_IO_CONTROLTYPELIST,82,17,132,66,LBS_SORT |
                    LBS_USETABSTOPS | NOT WS_VISIBLE | WS_DISABLED |
                    WS_VSCROLL | WS_GROUP | WS_TABSTOP
    PUSHBUTTON "&Add Control...",IDC_IO_ADDCONTROL,82,78,66,14
    DEFPUSHBUTTON "OK",IDOK,221,6,66,14,WS_GROUP
    PUSHBUTTON "Cancel",IDCANCEL,221,23,66,14
    PUSHBUTTON "&Help",IDC_OLEUIHELP,221,40,66,14
    CONTROL "&Display As Icon",IDC_IO_DISPLAYASICON,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,222,62,66,10
    CONTROL "",IDC_IO_ICONDISPLAY,"OLE2UIiconbox",0x0,221,79,66,46
    PUSHBUTTON "Change &Icon...",IDC_IO_CHANGEICON,221,130,66,14
    GROUPBOX "Result",IDC_STATIC,6,96,210,47,WS_GROUP
    CONTROL "",IDC_IO_RESULTIMAGE,"OLE2UIresimage",0x0,10,106,42,34
    LTEXT "<< result text goes here >>",IDC_IO_RESULTTEXT,56,106,156,32,SS_NOTIFY | SS_NOPREFIX | NOT WS_GROUP
END

IDD_CHANGEICON DIALOG DISCARDABLE 18,18,257,153
STYLE DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "Change Icon"
FONT 8,"MS Shell Dlg"
BEGIN
    GROUPBOX "Icon",IDC_CI_GROUP,4,6,180,124
    CONTROL "&Current",IDC_CI_CURRENT,"Button",BS_AUTORADIOBUTTON |
                    WS_GROUP,10,19,46,10
    CONTROL "&Default",IDC_CI_DEFAULT,"Button",BS_AUTORADIOBUTTON,10,44,46,10
    CONTROL "&From File:",IDC_CI_FROMFILE,"Button",BS_AUTORADIOBUTTON,10,68,46,10
    ICON "",IDC_CI_CURRENTICON,58,15,18,20,SS_NOTIFY
    ICON "",IDC_CI_DEFAULTICON,58,40,18,20,SS_NOTIFY
    EDITTEXT IDC_CI_FROMFILEEDIT,58,68,119,12,ES_AUTOHSCROLL |
                    WS_GROUP
    LISTBOX IDC_CI_ICONLIST,58,84,119,40,LBS_OWNERDRAWFIXED |
                    LBS_NOINTEGRALHEIGHT | LBS_MULTICOLUMN |
                    LBS_DISABLENOSCROLL | WS_HSCROLL | WS_TABSTOP
    LTEXT "&Label:",IDC_CI_LABEL,6,138,32,8
    EDITTEXT IDC_CI_LABELEDIT,38,136,146,12,ES_AUTOHSCROLL
    DEFPUSHBUTTON "OK",IDOK,191,6,60,14
    PUSHBUTTON "Cancel",IDCANCEL,191,23,60,14
    PUSHBUTTON "&Browse...",IDC_CI_BROWSE,191,40,60,14
    PUSHBUTTON "&Help",IDC_OLEUIHELP,191,57,60,14
    CONTROL "",IDC_CI_ICONDISPLAY,"OLE2UIiconbox",0x0,187,84,66,46
END

IDD_EDITLINKS DIALOG DISCARDABLE 9,25,326,158
STYLE DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "Links"
FONT 8,"MS Shell Dlg"
BEGIN
    LTEXT "&Links:",IDC_EL_COL1,11,11,26,8
    LTEXT "Type",IDC_EL_COL2,137,12,20,8
    LTEXT "Update",IDC_EL_COL3,201,12,25,8
    LISTBOX IDC_EL_LINKSLISTBOX,11,23,237,81,LBS_SORT |
                    LBS_OWNERDRAWFIXED | LBS_USETABSTOPS | LBS_EXTENDEDSEL |
                    WS_VSCROLL | WS_TABSTOP
    DEFPUSHBUTTON "Cancel",IDCANCEL,254,11,66,14
    PUSHBUTTON "&Update Now",IDC_EL_UPDATENOW,254,32,66,14
    PUSHBUTTON "&Open Source",IDC_EL_OPENSOURCE,254,49,66,14,WS_GROUP
    PUSHBUTTON "&Change Source...",IDC_EL_CHANGESOURCE,254,66,66,14,WS_GROUP
    PUSHBUTTON "&Break Link",IDC_EL_CANCELLINK,255,88,66,14
    PUSHBUTTON "&Help",IDC_OLEUIHELP,255,136,66,14
    CONTROL "&Automatic",IDC_EL_AUTOMATIC,"Button",BS_AUTORADIOBUTTON | WS_GROUP | WS_TABSTOP,61,138,65,10
    CONTROL "&Manual",IDC_EL_MANUAL,"Button",BS_AUTORADIOBUTTON,130,138,39,10
    LTEXT "Source:",IDC_STATIC,9,113,30,8
    LTEXT "Type:",IDC_STATIC,9,125,20,8
    LTEXT "Update:",IDC_STATIC,9,139,32,8
    LTEXT "<< source display string goes here >>",IDC_EL_LINKSOURCE,45,113,195,8,SS_NOPREFIX
    LTEXT "<< current type goes here >>",IDC_EL_LINKTYPE,45,125,195,8,SS_NOPREFIX
END

IDD_PASTESPECIAL DIALOG DISCARDABLE 3,15,293,140
STYLE DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "Paste Special"
FONT 8,"MS Shell Dlg"
BEGIN
    LTEXT "Source:",IDC_STATIC,6,9,30,8
    CONTROL "&Paste",IDC_PS_PASTE,"Button",BS_AUTORADIOBUTTON |
                    WS_GROUP | WS_TABSTOP,6,38,55,10
    CONTROL "Paste &Link",IDC_PS_PASTELINK,"Button",BS_AUTORADIOBUTTON,6,63,55,10
    LTEXT "&As:",IDC_STATIC,65,25,16,8
    LISTBOX IDC_PS_PASTELIST,65,36,153,57,LBS_USETABSTOPS | NOT
                    WS_VISIBLE | WS_VSCROLL | WS_GROUP | WS_TABSTOP
    LISTBOX IDC_PS_PASTELINKLIST,65,36,153,57,LBS_USETABSTOPS | NOT
                    WS_VISIBLE | WS_VSCROLL | WS_GROUP | WS_TABSTOP
    LISTBOX IDC_PS_DISPLAYLIST,65,36,153,57,LBS_USETABSTOPS |
                    WS_VSCROLL | WS_GROUP | WS_TABSTOP
    DEFPUSHBUTTON "OK",IDOK,224,6,66,14,WS_GROUP
    PUSHBUTTON "Cancel",IDCANCEL,224,23,66,14
    PUSHBUTTON "&Help",IDC_OLEUIHELP,224,42,66,14
    CONTROL "&Display As Icon",IDC_PS_DISPLAYASICON,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,224,59,66,14
    CONTROL "",IDC_PS_ICONDISPLAY,"OLE2UIiconbox",0x0,224,75,66,44
    PUSHBUTTON "Change &Icon...",IDC_PS_CHANGEICON,224,123,66,14
    CONTROL "",IDC_PS_RESULTIMAGE,"OLE2UIresimage",0x0,8,101,42,34
    LTEXT "<< result text goes here >>",IDC_PS_RESULTTEXT,54,100,159,35,SS_NOPREFIX | NOT WS_GROUP
    GROUPBOX "Result",IDC_STATIC,6,90,212,48,WS_GROUP
    LTEXT "<< source text goes here >>",IDC_PS_SOURCETEXT,39,9,178,8
END

IDD_BUSY DIALOG DISCARDABLE 0,0,214,76
STYLE DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "Server Busy"
FONT 8,"MS Shell Dlg"
BEGIN
#ifdef CHICO
    DEFPUSHBUTTON "&Retry",IDC_BZ_RETRY,92,55,53,15
    PUSHBUTTON "&Cancel",IDCANCEL,152,55,53,15
    LTEXT "This action cannot be completed because the other program is busy. Click the appropriate button on the task bar to activate the busy program and correct the problem.",IDC_BZ_MESSAGE1,35,11,167,35
#else
    DEFPUSHBUTTON "&Switch To...",IDC_BZ_SWITCHTO,33,55,53,15
    PUSHBUTTON "&Retry",IDC_BZ_RETRY,92,55,53,15
    PUSHBUTTON "&Cancel",IDCANCEL,152,55,53,15
    LTEXT "This action cannot be completed because the other program is busy. Choose 'Switch To' to activate the busy program and correct the problem.",IDC_BZ_MESSAGE1,35,11,167,35
#endif
    ICON "",IDC_BZ_ICON,8,18,18,20
END

IDD_CONVERT DIALOG DISCARDABLE 60,26,270,146
STYLE DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "Convert"
FONT 8,"MS Shell Dlg"
BEGIN
    RTEXT "Current Type:",IDC_STATIC,5,7,47,8
    LTEXT "<< current object type goes here >>",IDC_CV_OBJECTTYPE,55,7,131,8
    LTEXT "Object T&ype:",IDC_STATIC,70,21,89,8
    LISTBOX IDC_CV_ACTIVATELIST,71,32,118,53,LBS_SORT |
                    LBS_USETABSTOPS | WS_VSCROLL | WS_GROUP | WS_TABSTOP
    LISTBOX IDC_CV_CONVERTLIST,350,180,118,53,LBS_SORT |
                    LBS_USETABSTOPS | WS_VSCROLL | WS_GROUP | WS_TABSTOP
    DEFPUSHBUTTON "OK",IDOK,198,6,66,14,WS_GROUP
    PUSHBUTTON "Cancel",IDCANCEL,198,24,66,14
    PUSHBUTTON "&Help",IDC_OLEUIHELP,198,42,66,14
    CONTROL "&Display As Icon",IDC_CV_DISPLAYASICON,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,200,61,64,10
    GROUPBOX "Result",IDC_STATIC,6,87,183,51
    CONTROL "&Convert to:",IDC_CV_CONVERTTO,"Button",BS_AUTORADIOBUTTON,6,34,59,10
    CONTROL "&Activate as:",IDC_CV_ACTIVATEAS,"Button",BS_AUTORADIOBUTTON,7,58,59,10
    LTEXT "<< result text goes here >>",IDC_CV_RESULTTEXT,11,98,174,35
    PUSHBUTTON "Change &Icon...",IDC_CV_CHANGEICON,198,125,66,14
    CONTROL "",IDC_CV_ICONDISPLAY,"OLE2UIiconbox",0x0,198,75,66,46
END

IDD_LINKSOURCEUNAVAILABLE DIALOG DISCARDABLE 21,34,175,78
STYLE DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU
FONT 8,"MS Shell Dlg"
BEGIN
    ICON "",IDC_PU_ICON,8,8,18,20
    LTEXT "This action cannot be completed because the selected link's source is presently unavailable.",IDC_PU_TEXT,48,8,117,32
    DEFPUSHBUTTON "OK",IDOK,39,58,40,14
    PUSHBUTTON "Links...",IDC_PU_LINKS,95,58,40,14
END

IDD_CANNOTUPDATELINK DIALOG DISCARDABLE 21,34,175,78
STYLE DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU
FONT 8,"MS Shell Dlg"
BEGIN
    ICON "",IDC_PU_ICON,8,8,18,20
    LTEXT "Some links could not be updated because their sources are presently unavailable.",IDC_PU_TEXT,48,8,117,32
    DEFPUSHBUTTON "OK",IDOK,39,58,40,14
    PUSHBUTTON "Links...",IDC_PU_LINKS,95,58,40,14
END

IDD_SERVERNOTREGW DIALOG DISCARDABLE 39,30,198,78
STYLE DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU
FONT 8,"MS Shell Dlg"
BEGIN
    ICON "",IDC_PU_ICON,8,8,18,20
    LTEXT "The program necessary to activate this %ls is unavailable. You may convert it to or activate it as another type of object using Convert...",IDC_PU_TEXT,39,8,152,36
    DEFPUSHBUTTON "Convert...",IDC_PU_CONVERT,23,58,40,14
    PUSHBUTTON "Cancel",IDCANCEL,79,58,40,14
    PUSHBUTTON "Help",IDC_OLEUIHELP,135,58,40,14
END

IDD_SERVERNOTREGA DIALOG DISCARDABLE 39,30,198,78
STYLE DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU
FONT 8,"MS Shell Dlg"
BEGIN
    ICON "",IDC_PU_ICON,8,8,18,20
    LTEXT "The program necessary to activate this %hs is unavailable. You may convert it to or activate it as another type of object using Convert...",IDC_PU_TEXT,39,8,152,36
    DEFPUSHBUTTON "Convert...",IDC_PU_CONVERT,23,58,40,14
    PUSHBUTTON "Cancel",IDCANCEL,79,58,40,14
    PUSHBUTTON "Help",IDC_OLEUIHELP,135,58,40,14
END

IDD_LINKTYPECHANGEDW DIALOG DISCARDABLE 39,30,198,78
STYLE DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU
FONT 8,"MS Shell Dlg"
BEGIN
    ICON "",IDC_PU_ICON,8,8,18,20
    LTEXT "The link is no longer a %ls. Please choose a different command offered by the new type.",IDC_PU_TEXT,39,8,152,36
    PUSHBUTTON "OK",IDOK,79,58,40,14
END

IDD_LINKTYPECHANGEDA DIALOG DISCARDABLE 39,30,198,78
STYLE DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU
FONT 8,"MS Shell Dlg"
BEGIN
    ICON "",IDC_PU_ICON,8,8,18,20
    LTEXT "The link is no longer a %hs. Please choose a different command offered by the new type.",IDC_PU_TEXT,39,8,152,36
    PUSHBUTTON "OK",IDOK,79,58,40,14
END

IDD_SERVERNOTFOUND DIALOG DISCARDABLE 36,39,183,90
STYLE DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU
FONT 8,"MS Shell Dlg"
BEGIN
    ICON "",IDC_PU_ICON,8,8,18,20
    LTEXT "The server program cannot be found.\n\nMake sure the program is properly installed, or exists in your system path, and that is has not been deleted, moved, or renamed.",IDC_PU_TEXT,38,8,136,52
    DEFPUSHBUTTON "OK",IDOK,71,70,40,14
END

IDD_UPDATELINKS DIALOG DISCARDABLE 50,57,179,42
STYLE DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "Update Links"
FONT 8,"MS Shell Dlg"
BEGIN
    CONTROL "",IDC_UL_METER,"Static",SS_BLACKFRAME,5,28,124,10
    DEFPUSHBUTTON "Stop",IDC_UL_STOP,134,26,40,14
    LTEXT "Update links...",IDC_STATIC,5,7,56,8
    CTEXT "0%",IDC_UL_PERCENT,37,18,64,8
END

IDD_OUTOFMEMORY DIALOG DISCARDABLE 36,39,178,73
STYLE DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU
FONT 8,"MS Shell Dlg"
BEGIN
    ICON "",IDC_PU_ICON,8,8,18,20
    LTEXT "Insufficient memory to complete the operation.",IDC_PU_TEXT,40,12,127,30
    DEFPUSHBUTTON "OK",IDOK,71,49,40,14
END

IDD_GNRLPROPS DIALOG DISCARDABLE 0,0,204,152
STYLE WS_CHILD | WS_VISIBLE | WS_CAPTION
CAPTION "General"
FONT 8,"MS Shell Dlg"
BEGIN
    ICON "",IDC_GP_OBJECTICON,6,9,18,20
    LTEXT "<< name goes here >>",IDC_GP_OBJECTNAME,32,15,167,21
    CONTROL "",IDC_STATIC,"Static",SS_BLACKRECT,6,43,193,1
    LTEXT "Type:",IDC_STATIC,6,50,20,8
    LTEXT "<< type goes here >>",IDC_GP_OBJECTTYPE,42,50,102,8
    LTEXT "Size:",IDC_STATIC,6,64,18,8
    LTEXT "<< size goes here >>",IDC_GP_OBJECTSIZE,42,64,101,8
    LTEXT "Location:",IDC_STATIC,6,76,30,8
    LTEXT "<< location goes here >>",IDC_GP_OBJECTLOCATION,42,76,154,8
    CONTROL "",IDC_STATIC,"Static",SS_BLACKRECT,6,90,193,1
    PUSHBUTTON "&Convert...",IDC_GP_CONVERT,149,48,50,14
    PUSHBUTTON "&Help",IDC_OLEUIHELP,160,134,50,14
END

IDD_VIEWPROPS DIALOG DISCARDABLE 0,0,204,154
STYLE WS_POPUP | WS_VISIBLE | WS_CAPTION
CAPTION "View"
FONT 8,"MS Shell Dlg"
BEGIN
    GROUPBOX " Appearance ",IDC_STATIC,6,6,193,101
    CONTROL "",IDC_VP_ICONDISPLAY,"OLE2UIiconbox",0x0,12,54,66,46
    CONTROL "Display as &editable information",IDC_VP_EDITABLE,"Button",BS_AUTORADIOBUTTON,81,29,113,10
    CONTROL "Display as &icon",IDC_VP_ASICON,"Button",BS_AUTORADIOBUTTON,81,58,116,10
    PUSHBUTTON "&Change Icon...",IDC_VP_CHANGEICON,92,72,58,14
    LTEXT "&Scale:",IDC_VP_SCALETXT,6,113,25,8
    EDITTEXT IDC_VP_PERCENT,6,123,33,13,ES_AUTOHSCROLL
    LTEXT "%",IDC_STATIC,41,126,9,8
    CONTROL "&Relative to Original Size",IDC_VP_RELATIVE,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,60,125,132,10
    CONTROL "",IDC_VP_RESULTIMAGE,"OLE2UIresimage",0x0,12,18,66,34
    PUSHBUTTON "&Help",IDC_OLEUIHELP,160,134,50,14
END

IDD_LINKPROPS DIALOG DISCARDABLE 0,0,204,154
STYLE WS_CHILD | WS_VISIBLE | WS_CAPTION
CAPTION "Link"
FONT 8,"MS Shell Dlg"
BEGIN
    GROUPBOX " Linked To ",IDC_STATIC,6,6,192,51
    LTEXT "< first line of linked to >\n<second line of linked to >",IDC_LP_LINKSOURCE,11,19,181,18
    PUSHBUTTON "&Change Source...",IDC_LP_CHANGESOURCE,12,39,66,14
    GROUPBOX " Update ",IDC_STATIC,6,60,85,42
    CONTROL "Automatically",IDC_LP_AUTOMATIC,"Button",BS_AUTORADIOBUTTON,12,74,76,10
    CONTROL "Manually",IDC_LP_MANUAL,"Button",BS_AUTORADIOBUTTON,12,87,76,10
    GROUPBOX " Last Update ",IDC_STATIC,97,60,101,42
    LTEXT "Date:",IDC_STATIC,102,74,22,8
    LTEXT "< date >",IDC_LP_DATE,125,74,70,8
    LTEXT "Time:",IDC_STATIC,102,85,21,8
    LTEXT "< HH:MM:SS XX >",IDC_LP_TIME,125,85,71,8
    PUSHBUTTON "&Open Source",IDC_LP_OPENSOURCE,15,114,50,14
    PUSHBUTTON "&Update Now",IDC_LP_UPDATENOW,76,114,50,14
    PUSHBUTTON "&Break Link",IDC_LP_BREAKLINK,135,114,50,14
    PUSHBUTTON "&Help",IDC_OLEUIHELP,160,134,50,14
END

IDD_CONVERTONLY DIALOG DISCARDABLE 60,26,243,95
STYLE DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "Convert"
FONT 8,"MS Shell Dlg"
BEGIN
    LTEXT "Current Type:",IDC_STATIC,6,7,47,8
    LTEXT "<< current type goes here >>",IDC_CV_OBJECTTYPE,55,7,126,8
    LTEXT "Object T&ype:",IDC_STATIC,6,20,89,8
    LISTBOX IDC_CV_CONVERTLIST,6,31,150,58,LBS_SORT |
                    LBS_USETABSTOPS | WS_VSCROLL | WS_GROUP | WS_TABSTOP
    DEFPUSHBUTTON "OK",IDOK,187,6,50,14,WS_GROUP
    PUSHBUTTON "Cancel",IDCANCEL,187,23,50,14
    PUSHBUTTON "&Help",IDC_OLEUIHELP,187,40,50,14
END

IDD_CHANGESOURCE DIALOG DISCARDABLE 36,24,265,160
STYLE DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "Open"
FONT 8,"MS Shell Dlg"
BEGIN
    LTEXT "&Source File Name:",stc3,6,4,76,9
    EDITTEXT edt1,6,14,195,12,ES_AUTOHSCROLL
    LISTBOX lst1,6,34,90,68,LBS_SORT | LBS_OWNERDRAWFIXED |
                    LBS_HASSTRINGS | LBS_DISABLENOSCROLL | WS_VSCROLL |
                    WS_TABSTOP
    LTEXT "&Directories:",IDC_STATIC,110,28,92,9
    LTEXT "",stc1,110,36,92,9,SS_NOPREFIX
    LISTBOX lst2,110,49,92,53,LBS_SORT | LBS_OWNERDRAWFIXED |
                    LBS_HASSTRINGS | LBS_DISABLENOSCROLL | WS_VSCROLL |
                    WS_TABSTOP
    LTEXT "List Files of &Type:",stc2,6,104,90,9
    COMBOBOX cmb1,6,114,90,36,CBS_DROPDOWNLIST | CBS_AUTOHSCROLL |
                    WS_BORDER | WS_VSCROLL | WS_TABSTOP
    LTEXT "Dri&ves:",stc4,110,104,92,9
    COMBOBOX cmb2,110,114,92,68,CBS_DROPDOWNLIST | CBS_OWNERDRAWFIXED |
                    CBS_AUTOHSCROLL | CBS_SORT | CBS_HASSTRINGS | WS_BORDER |
                    WS_VSCROLL | WS_TABSTOP
    LTEXT "Source &Item:",IDC_STATIC,6,133,63,8
    EDITTEXT edt2,6,142,197,13,ES_AUTOHSCROLL
    DEFPUSHBUTTON "OK",IDOK,208,6,50,14,WS_GROUP
    PUSHBUTTON "Cancel",IDCANCEL,208,24,50,14,WS_GROUP
    PUSHBUTTON "&Help",pshHelp,208,46,50,14,WS_GROUP
    CONTROL "&Read Only",chx1,"Button",BS_AUTOCHECKBOX | WS_GROUP |
                    WS_TABSTOP,208,68,50,12
END

IDD_CHANGESOURCE4 DIALOG DISCARDABLE 36,24,224,26
STYLE WS_CHILD | WS_VISIBLE | WS_CLIPSIBLINGS | 0x400L
FONT 8,"MS Shell Dlg"
BEGIN
    LTEXT "&Item Name:",IDC_STATIC,11,12,37,8
    EDITTEXT edt2,60,10,155,13,ES_AUTOHSCROLL
    LTEXT "",stc32,6,2,213,8
END

IDD_CONVERT4 DIALOG DISCARDABLE 60,26,270,146
STYLE DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "Convert"
FONT 8,"MS Shell Dlg"
BEGIN
    RTEXT "Current Type:",IDC_STATIC,5,7,47,8
    LTEXT "Object T&ype:",IDC_STATIC,70,21,89,8
    LISTBOX IDC_CV_ACTIVATELIST,71,32,118,53,LBS_SORT |
                    LBS_USETABSTOPS | WS_VSCROLL | WS_GROUP | WS_TABSTOP
    LISTBOX IDC_CV_CONVERTLIST,350,180,118,53,LBS_SORT |
                    LBS_USETABSTOPS | WS_VSCROLL | WS_GROUP | WS_TABSTOP
    DEFPUSHBUTTON "OK",IDOK,198,6,66,14,WS_GROUP
    PUSHBUTTON "Cancel",IDCANCEL,198,24,66,14
    PUSHBUTTON "&Help",IDC_OLEUIHELP,198,42,66,14
    CONTROL "&Display As Icon",IDC_CV_DISPLAYASICON,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,200,61,64,10
    GROUPBOX "Result",IDC_STATIC,6,87,183,51
    CONTROL "&Convert to:",IDC_CV_CONVERTTO,"Button",BS_AUTORADIOBUTTON,6,34,59,10
    CONTROL "&Activate as:",IDC_CV_ACTIVATEAS,"Button",BS_AUTORADIOBUTTON,7,58,59,10
    LTEXT "<< result text goes here >>",IDC_CV_RESULTTEXT,11,98,174,35
    PUSHBUTTON "Change &Icon...",IDC_CV_CHANGEICON,198,125,66,14
    CONTROL "",IDC_CV_ICONDISPLAY,"OLE2UIiconbox",0x0,198,75,66,46
    EDITTEXT IDC_CV_OBJECTTYPE,56,7,134,8,ES_AUTOHSCROLL |
                    ES_READONLY | NOT WS_BORDER | NOT WS_TABSTOP
END

IDD_CONVERTONLY4 DIALOG DISCARDABLE 60,26,243,95
STYLE DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "Convert"
FONT 8,"MS Shell Dlg"
BEGIN
    LTEXT "Current Type:",IDC_STATIC,6,7,47,8
    LTEXT "Object T&ype:",IDC_STATIC,6,20,89,8
    LISTBOX IDC_CV_CONVERTLIST,6,31,150,58,LBS_SORT |
                    LBS_USETABSTOPS | WS_VSCROLL | WS_GROUP | WS_TABSTOP
    DEFPUSHBUTTON "OK",IDOK,187,6,50,14,WS_GROUP
    PUSHBUTTON "Cancel",IDCANCEL,187,23,50,14
    PUSHBUTTON "&Help",IDC_OLEUIHELP,187,40,50,14
    EDITTEXT IDC_CV_OBJECTTYPE,53,7,128,8 ,ES_AUTOHSCROLL |
                    ES_READONLY | NOT WS_BORDER | NOT WS_TABSTOP
END

IDD_EDITLINKS4 DIALOG DISCARDABLE 9,25,326,158
STYLE DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "Links"
FONT 8,"MS Shell Dlg"
BEGIN
    LTEXT "&Links:",IDC_EL_COL1,11,11,26,8
    LTEXT "Type",IDC_EL_COL2,137,12,20,8
    LTEXT "Update",IDC_EL_COL3,201,12,25,8
    LISTBOX IDC_EL_LINKSLISTBOX,11,23,237,81,LBS_SORT |
                    LBS_OWNERDRAWFIXED | LBS_USETABSTOPS | LBS_EXTENDEDSEL |
                    WS_VSCROLL | WS_TABSTOP
    DEFPUSHBUTTON "Cancel",IDCANCEL,254,11,66,14
    PUSHBUTTON "&Update Now",IDC_EL_UPDATENOW,254,32,66,14
    PUSHBUTTON "&Open Source",IDC_EL_OPENSOURCE,254,49,66,14,WS_GROUP
    PUSHBUTTON "&Change Source...",IDC_EL_CHANGESOURCE,254,66,66,14,WS_GROUP
    PUSHBUTTON "&Break Link",IDC_EL_CANCELLINK,255,88,66,14
    PUSHBUTTON "&Help",IDC_OLEUIHELP,255,136,66,14
    CONTROL "&Automatic",IDC_EL_AUTOMATIC,"Button",BS_AUTORADIOBUTTON | WS_GROUP | WS_TABSTOP,61,139,65,8
    CONTROL "&Manual",IDC_EL_MANUAL,"Button",BS_AUTORADIOBUTTON,130,139,39,8
    LTEXT "Source:",IDC_STATIC,9,113,30,8
    LTEXT "Type:",IDC_STATIC,9,125,20,8
    LTEXT "Update:",IDC_STATIC,9,139,32,8
    EDITTEXT IDC_EL_LINKSOURCE,44,113,204,8,ES_AUTOHSCROLL |
                    ES_READONLY | NOT WS_BORDER | NOT WS_TABSTOP
    EDITTEXT IDC_EL_LINKTYPE,44,125,204,8,ES_AUTOHSCROLL |
                    ES_READONLY | NOT WS_BORDER | NOT WS_TABSTOP
END

IDD_GNRLPROPS4 DIALOG DISCARDABLE 0,0,204,152
STYLE WS_CHILD | WS_VISIBLE | WS_CAPTION
CAPTION "General"
FONT 8,"MS Shell Dlg"
BEGIN
    ICON "",IDC_GP_OBJECTICON,6,9,18,20
    CONTROL "",IDC_STATIC,"Static",SS_BLACKRECT,6,43,193,1
    LTEXT "Type:",IDC_STATIC,6,50,20,8
    LTEXT "Size:",IDC_STATIC,6,64,18,8
    LTEXT "Location:",IDC_STATIC,6,76,30,8
    CONTROL "",IDC_STATIC,"Static",SS_BLACKRECT,6,90,193,1
    PUSHBUTTON "&Convert...",IDC_GP_CONVERT,149,48,50,14
    EDITTEXT IDC_GP_OBJECTNAME,34,14,164,25,ES_MULTILINE |
                    ES_AUTOHSCROLL | ES_READONLY | NOT WS_BORDER | NOT
                    WS_TABSTOP
    EDITTEXT IDC_GP_OBJECTTYPE,42,48,102,12,ES_AUTOHSCROLL |
                    ES_READONLY | NOT WS_BORDER | NOT WS_TABSTOP
    EDITTEXT IDC_GP_OBJECTSIZE,42,62,102,12,ES_AUTOHSCROLL |
                    ES_READONLY | NOT WS_BORDER | NOT WS_TABSTOP
    EDITTEXT IDC_GP_OBJECTLOCATION,42,75,157,12,ES_AUTOHSCROLL |
                    ES_READONLY | NOT WS_BORDER | NOT WS_TABSTOP
    PUSHBUTTON "&Help",IDC_OLEUIHELP,160,134,50,14
END

IDD_LINKPROPS4 DIALOG DISCARDABLE 0,0,204,154
STYLE WS_CHILD | WS_VISIBLE | WS_CAPTION
CAPTION "Link"
FONT 8,"MS Shell Dlg"
BEGIN
    GROUPBOX " Linked To ",IDC_STATIC,6,6,192,51
    PUSHBUTTON "&Change Source...",IDC_LP_CHANGESOURCE,12,40,66,14
    GROUPBOX " Update ",IDC_STATIC,6,60,85,42
    CONTROL "Automatically",IDC_LP_AUTOMATIC,"Button",BS_AUTORADIOBUTTON,12,74,76,10
    CONTROL "Manually",IDC_LP_MANUAL,"Button",BS_AUTORADIOBUTTON,12,87,76,10
    GROUPBOX " Last Update ",IDC_STATIC,97,60,101,42
    LTEXT "Date:",IDC_STATIC,102,74,22,8
    LTEXT "Time:",IDC_STATIC,102,85,21,8
    PUSHBUTTON "&Open Source",IDC_LP_OPENSOURCE,15,114,50,14
    PUSHBUTTON "&Update Now",IDC_LP_UPDATENOW,76,114,50,14
    PUSHBUTTON "&Break Link",IDC_LP_BREAKLINK,135,114,50,14
    EDITTEXT IDC_LP_LINKSOURCE,12,18,182,19,ES_MULTILINE |
                    ES_READONLY | NOT WS_BORDER | NOT WS_TABSTOP
    EDITTEXT IDC_LP_DATE,123,73,70,12,ES_AUTOHSCROLL | ES_READONLY |
                    NOT WS_BORDER
    EDITTEXT IDC_LP_TIME,123,84,70,12,ES_AUTOHSCROLL | ES_READONLY |
                    NOT WS_BORDER
    PUSHBUTTON "&Help",IDC_OLEUIHELP,160,134,50,14
END

IDD_PASTESPECIAL4 DIALOG DISCARDABLE 3,15,293,140
STYLE DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "Paste Special"
FONT 8,"MS Shell Dlg"
BEGIN
    LTEXT "Source:",IDC_STATIC,6,9,30,8
    CONTROL "&Paste",IDC_PS_PASTE,"Button",BS_AUTORADIOBUTTON |
                    WS_GROUP | WS_TABSTOP,6,38,55,10
    CONTROL "Paste &Link",IDC_PS_PASTELINK,"Button",BS_AUTORADIOBUTTON,6,63,55,10
    LTEXT "&As:",IDC_STATIC,65,25,16,8
    LISTBOX IDC_PS_PASTELIST,65,36,153,57,LBS_USETABSTOPS | NOT
                    WS_VISIBLE | WS_VSCROLL | WS_GROUP | WS_TABSTOP
    LISTBOX IDC_PS_PASTELINKLIST,65,36,153,57,LBS_USETABSTOPS | NOT
                    WS_VISIBLE | WS_VSCROLL | WS_GROUP | WS_TABSTOP
    LISTBOX IDC_PS_DISPLAYLIST,65,36,153,57,LBS_USETABSTOPS |
                    WS_VSCROLL | WS_GROUP | WS_TABSTOP
    DEFPUSHBUTTON "OK",IDOK,224,6,66,14,WS_GROUP
    PUSHBUTTON "Cancel",IDCANCEL,224,23,66,14
    PUSHBUTTON "&Help",IDC_OLEUIHELP,224,42,66,14
    CONTROL "&Display As Icon",IDC_PS_DISPLAYASICON,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,224,59,66,14
    CONTROL "",IDC_PS_ICONDISPLAY,"OLE2UIiconbox",0x0,224,75,66,44
    PUSHBUTTON "Change &Icon...",IDC_PS_CHANGEICON,224,123,66,14
    CONTROL "",IDC_PS_RESULTIMAGE,"OLE2UIresimage",0x0,8,101,42,34
    LTEXT "<< result text goes here >>",IDC_PS_RESULTTEXT,54,100,159,35,SS_NOPREFIX | NOT WS_GROUP
    GROUPBOX "Result",IDC_STATIC,6,90,212,48,WS_GROUP
    EDITTEXT IDC_PS_SOURCETEXT,37,9,180,8,ES_AUTOHSCROLL |
                    ES_READONLY | NOT WS_BORDER | NOT WS_TABSTOP
END
