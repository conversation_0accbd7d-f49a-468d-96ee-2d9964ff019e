/* This file is part of the KDE project
   Copyright (C) 2007 Shane King

   This program is free software; you can redistribute it and/or
   modify it under the terms of the GNU Library General Public
   License as published by the Free Software Foundation; either
   version 2 of the License, or (at your option) any later version.

   This program is distributed in the hope that it will be useful,
   but WITHOUT ANY WARRANTY; without even the implied warranty of
   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
   Library General Public License for more details.

   You should have received a copy of the GNU Library General Public License
   along with this program; see the file COPYING.  If not, write to
   the Free Software Foundation, Inc., 51 Franklin Street, Fifth Floor,
   Boston, MA 02110-1301, USA.
*/

import "unknwn.idl";
import "strmif.idl";
import "wtypes.idl";

[
    object,
    uuid(fa2aa8f4-8b62-11d0-a520-000000000000)
]
interface IAMMediaContent : IDispatch
{
    [propget] HRESULT AuthorName(BSTR *pbstrAuthorName);
    [propget] HRESULT Title(BSTR *pbstrTitle);
    [propget] HRESULT Rating(BSTR *pbstrRating);
    [propget] HRESULT Description(BSTR *pbstrDescription);
    [propget] HRESULT Copyright(BSTR *pbstrCopyright);
    [propget] HRESULT BaseURL(BSTR *pbstrBaseURL);
    [propget] HRESULT LogoURL(BSTR *pbstrLogoURL);
    [propget] HRESULT LogoIconURL(BSTR *pbstrLogoURL);
    [propget] HRESULT WatermarkURL(BSTR *pbstrWatermarkURL);
    [propget] HRESULT MoreInfoURL(BSTR *pbstrMoreInfoURL);
    [propget] HRESULT MoreInfoBannerImage(BSTR *pbstrMoreInfoBannerImage);
    [propget] HRESULT MoreInfoBannerURL(BSTR *pbstrMoreInfoBannerURL);
    [propget] HRESULT MoreInfoText(BSTR *pbstrMoreInfoText);
}

[
    object,
    uuid(fa2aa8f3-8b62-11d0-a520-000000000000)
]
interface IAMNetworkStatus : IDispatch
{
    [propget] HRESULT ReceivedPackets(long *pReceivedPackets);
    [propget] HRESULT RecoveredPackets(long *pRecoveredPackets);
    [propget] HRESULT LostPackets(long *pLostPackets);
    [propget] HRESULT ReceptionQuality(long *pReceptionQuality);
    [propget] HRESULT BufferingCount(long *pBufferingCount);
    [propget] HRESULT IsBroadcast(VARIANT_BOOL *pIsBroadcast);
    [propget] HRESULT BufferingProgress(long *pBufferingProgress);
}

