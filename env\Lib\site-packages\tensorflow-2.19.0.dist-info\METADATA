Metadata-Version: 2.1
Name: tensorflow
Version: 2.19.0
Summary: TensorFlow is an open source machine learning framework for everyone.
Home-page: https://www.tensorflow.org/
Download-URL: https://github.com/tensorflow/tensorflow/tags
Author: Google Inc.
Author-email: <EMAIL>
License: Apache 2.0
Keywords: tensorflow tensor machine learning
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: GPU :: NVIDIA CUDA :: 12
Classifier: Environment :: GPU :: NVIDIA CUDA :: 12 :: 12.2
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Education
Classifier: Intended Audience :: Science/Research
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.9
Classifier: Topic :: Scientific/Engineering
Classifier: Topic :: Scientific/Engineering :: Artificial Intelligence
Classifier: Topic :: Scientific/Engineering :: Mathematics
Classifier: Topic :: Software Development
Classifier: Topic :: Software Development :: Libraries
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Requires-Python: >=3.9
Description-Content-Type: text/markdown
Requires-Dist: absl-py >=1.0.0
Requires-Dist: astunparse >=1.6.0
Requires-Dist: flatbuffers >=24.3.25
Requires-Dist: gast !=0.5.0,!=0.5.1,!=0.5.2,>=0.2.1
Requires-Dist: google-pasta >=0.1.1
Requires-Dist: libclang >=13.0.0
Requires-Dist: opt-einsum >=2.3.2
Requires-Dist: packaging
Requires-Dist: protobuf !=4.21.0,!=4.21.1,!=4.21.2,!=4.21.3,!=4.21.4,!=4.21.5,<6.0.0dev,>=3.20.3
Requires-Dist: requests <3,>=2.21.0
Requires-Dist: setuptools
Requires-Dist: six >=1.12.0
Requires-Dist: termcolor >=1.1.0
Requires-Dist: typing-extensions >=3.6.6
Requires-Dist: wrapt >=1.11.0
Requires-Dist: grpcio <2.0,>=1.24.3
Requires-Dist: tensorboard ~=2.19.0
Requires-Dist: keras >=3.5.0
Requires-Dist: numpy <2.2.0,>=1.26.0
Requires-Dist: h5py >=3.11.0
Requires-Dist: ml-dtypes <1.0.0,>=0.5.1
Requires-Dist: tensorflow-io-gcs-filesystem >=0.23.1 ; python_version < "3.12"
Provides-Extra: and-cuda
Requires-Dist: nvidia-cublas-cu12 ==******** ; extra == 'and-cuda'
Requires-Dist: nvidia-cuda-cupti-cu12 ==12.5.82 ; extra == 'and-cuda'
Requires-Dist: nvidia-cuda-nvcc-cu12 ==12.5.82 ; extra == 'and-cuda'
Requires-Dist: nvidia-cuda-nvrtc-cu12 ==12.5.82 ; extra == 'and-cuda'
Requires-Dist: nvidia-cuda-runtime-cu12 ==12.5.82 ; extra == 'and-cuda'
Requires-Dist: nvidia-cudnn-cu12 ==9.3.0.75 ; extra == 'and-cuda'
Requires-Dist: nvidia-cufft-cu12 ==11.2.3.61 ; extra == 'and-cuda'
Requires-Dist: nvidia-curand-cu12 ==10.3.6.82 ; extra == 'and-cuda'
Requires-Dist: nvidia-cusolver-cu12 ==11.6.3.83 ; extra == 'and-cuda'
Requires-Dist: nvidia-cusparse-cu12 ==12.5.1.3 ; extra == 'and-cuda'
Requires-Dist: nvidia-nccl-cu12 ==2.23.4 ; extra == 'and-cuda'
Requires-Dist: nvidia-nvjitlink-cu12 ==12.5.82 ; extra == 'and-cuda'

[![Python](https://img.shields.io/pypi/pyversions/tensorflow.svg?style=plastic)](https://badge.fury.io/py/tensorflow)
[![PyPI](https://badge.fury.io/py/tensorflow.svg)](https://badge.fury.io/py/tensorflow)

TensorFlow is an open source software library for high performance numerical
computation. Its flexible architecture allows easy deployment of computation
across a variety of platforms (CPUs, GPUs, TPUs), and from desktops to clusters
of servers to mobile and edge devices.

Originally developed by researchers and engineers from the Google Brain team
within Google's AI organization, it comes with strong support for machine
learning and deep learning and the flexible numerical computation core is used
across many other scientific domains. TensorFlow is licensed under [Apache
2.0](https://github.com/tensorflow/tensorflow/blob/master/LICENSE).
