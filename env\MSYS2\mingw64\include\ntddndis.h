/**
* This file is part of the mingw-w64 runtime package.
* No warranty is given; refer to the file DISCLAIMER within this package.
*/

#ifndef _NTDDNDIS_
#define _NTDDNDIS_

#include <winapifamily.h>

#if WINAPI_FAMILY_PARTITION (WINAPI_PARTITION_DESKTOP)

#ifdef __cplusplus
extern "C" {
#endif

#ifndef NDIS_SUPPORT_NDIS6
#if defined (UM_NDIS60) || defined (UM_NDIS61) || defined (UM_NDIS620) || defined (UM_NDIS630)
#define NDIS_SUPPORT_NDIS6 1
#else
#define NDIS_SUPPORT_NDIS6 0
#endif
#endif

#if NTDDI_VERSION >= 0x06000000 || NDIS_SUPPORT_NDIS6
#include <ifdef.h>
#include <devpkey.h>
#include <pciprop.h>
#endif

#ifndef NDIS_SUPPORT_NDIS61
#if defined (UM_NDIS61) || defined (UM_NDIS620) || defined (UM_NDIS630)
#define NDIS_SUPPORT_NDIS61 1
#else
#define NDIS_SUPPORT_NDIS61 0
#endif
#endif

#ifndef NDIS_SUPPORT_NDIS620
#if defined (UM_NDIS620) || defined (UM_NDIS630)
#define NDIS_SUPPORT_NDIS620 1
#else
#define NDIS_SUPPORT_NDIS620 0
#endif
#endif

#ifndef NDIS_SUPPORT_NDIS630
#ifdef UM_NDIS630
#define NDIS_SUPPORT_NDIS630 1
#else
#define NDIS_SUPPORT_NDIS630 0
#endif
#endif

#define DD_NDIS_DEVICE_NAME "\\Device\\UNKNOWN"

#define _NDIS_CONTROL_CODE(request, method) CTL_CODE (FILE_DEVICE_PHYSICAL_NETCARD, request, method, FILE_ANY_ACCESS)

#define IOCTL_NDIS_QUERY_GLOBAL_STATS _NDIS_CONTROL_CODE (0, METHOD_OUT_DIRECT)
#define IOCTL_NDIS_QUERY_ALL_STATS _NDIS_CONTROL_CODE (1, METHOD_OUT_DIRECT)
#define IOCTL_NDIS_DO_PNP_OPERATION _NDIS_CONTROL_CODE (2, METHOD_BUFFERED)
#define IOCTL_NDIS_QUERY_SELECTED_STATS _NDIS_CONTROL_CODE (3, METHOD_OUT_DIRECT)
#define IOCTL_NDIS_ENUMERATE_INTERFACES _NDIS_CONTROL_CODE (4, METHOD_BUFFERED)
#define IOCTL_NDIS_ADD_TDI_DEVICE _NDIS_CONTROL_CODE (5, METHOD_BUFFERED)
#define IOCTL_NDIS_GET_LOG_DATA _NDIS_CONTROL_CODE (7, METHOD_OUT_DIRECT)
#define IOCTL_NDIS_GET_VERSION _NDIS_CONTROL_CODE (8, METHOD_BUFFERED)

#define IOCTL_NDIS_RESERVED1 _NDIS_CONTROL_CODE (9, METHOD_BUFFERED)
#define IOCTL_NDIS_RESERVED2 _NDIS_CONTROL_CODE (0xa, METHOD_BUFFERED)
#define IOCTL_NDIS_RESERVED3 _NDIS_CONTROL_CODE (0xb, METHOD_BUFFERED)
#define IOCTL_NDIS_RESERVED4 _NDIS_CONTROL_CODE (0xc, METHOD_BUFFERED)
#define IOCTL_NDIS_RESERVED5 CTL_CODE (FILE_DEVICE_PHYSICAL_NETCARD, 0xd, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_NDIS_RESERVED6 CTL_CODE (FILE_DEVICE_PHYSICAL_NETCARD, 0xe, METHOD_BUFFERED, FILE_WRITE_ACCESS)
#define IOCTL_NDIS_RESERVED7 _NDIS_CONTROL_CODE (0xf, METHOD_OUT_DIRECT)
#define IOCTL_NDIS_RESERVED8 _NDIS_CONTROL_CODE (0x10, METHOD_BUFFERED)
#define IOCTL_NDIS_RESERVED9 _NDIS_CONTROL_CODE (0x11, METHOD_BUFFERED)
#define IOCTL_NDIS_RESERVED10 _NDIS_CONTROL_CODE (0x12, METHOD_BUFFERED)
#define IOCTL_NDIS_RESERVED11 _NDIS_CONTROL_CODE (0x13, METHOD_BUFFERED)
#define IOCTL_NDIS_RESERVED12 _NDIS_CONTROL_CODE (0x14, METHOD_BUFFERED)
#define IOCTL_NDIS_RESERVED13 _NDIS_CONTROL_CODE (0x15, METHOD_BUFFERED)
#define IOCTL_NDIS_RESERVED14 _NDIS_CONTROL_CODE (0x16, METHOD_BUFFERED)
#define IOCTL_NDIS_RESERVED15 _NDIS_CONTROL_CODE (0x17, METHOD_BUFFERED)
#define IOCTL_NDIS_RESERVED16 _NDIS_CONTROL_CODE (0x18, METHOD_BUFFERED)
#define IOCTL_NDIS_RESERVED17 _NDIS_CONTROL_CODE (0x19, METHOD_BUFFERED)
#define IOCTL_NDIS_RESERVED18 _NDIS_CONTROL_CODE (0x1a, METHOD_BUFFERED)
#define IOCTL_NDIS_RESERVED19 _NDIS_CONTROL_CODE (0x1b, METHOD_BUFFERED)
#define IOCTL_NDIS_RESERVED20 _NDIS_CONTROL_CODE (0x1c, METHOD_BUFFERED)
#define IOCTL_NDIS_RESERVED21 _NDIS_CONTROL_CODE (0x1d, METHOD_BUFFERED)
#define IOCTL_NDIS_RESERVED22 _NDIS_CONTROL_CODE (0x1e, METHOD_BUFFERED)
#define IOCTL_NDIS_RESERVED23 _NDIS_CONTROL_CODE (0x1f, METHOD_BUFFERED)
#define IOCTL_NDIS_RESERVED24 _NDIS_CONTROL_CODE (0x20, METHOD_BUFFERED)
#define IOCTL_NDIS_RESERVED25 _NDIS_CONTROL_CODE (0x21, METHOD_BUFFERED)
#define IOCTL_NDIS_RESERVED26 _NDIS_CONTROL_CODE (0x22, METHOD_BUFFERED)
#define IOCTL_NDIS_RESERVED27 _NDIS_CONTROL_CODE (0x23, METHOD_BUFFERED)
#define IOCTL_NDIS_RESERVED28 _NDIS_CONTROL_CODE (0x24, METHOD_BUFFERED)
#define IOCTL_NDIS_RESERVED29 _NDIS_CONTROL_CODE (0x25, METHOD_BUFFERED)
#define IOCTL_NDIS_RESERVED30 _NDIS_CONTROL_CODE (0x26, METHOD_BUFFERED)

  typedef ULONG NDIS_OID, *PNDIS_OID;
  typedef struct _NDIS_STATISTICS_VALUE {
    NDIS_OID Oid;
    ULONG DataLength;
    UCHAR Data[1];
  } NDIS_STATISTICS_VALUE;

  typedef NDIS_STATISTICS_VALUE UNALIGNED *PNDIS_STATISTICS_VALUE;

#if NTDDI_VERSION >= 0x06000000 || NDIS_SUPPORT_NDIS6
  typedef struct _NDIS_STATISTICS_VALUE_EX {
    NDIS_OID Oid;
    ULONG DataLength;
    ULONG Length;
    UCHAR Data[1];
  } NDIS_STATISTICS_VALUE_EX;

  typedef NDIS_STATISTICS_VALUE_EX UNALIGNED *PNDIS_STATISTICS_VALUE_EX;
#endif

  typedef struct _NDIS_VAR_DATA_DESC {
    USHORT Length;
    USHORT MaximumLength;
    ULONG_PTR Offset;
  } NDIS_VAR_DATA_DESC, *PNDIS_VAR_DATA_DESC;

#ifndef GUID_DEFINED
#include <guiddef.h>
#endif

#define NDIS_OBJECT_TYPE_DEFAULT 0x80
#define NDIS_OBJECT_TYPE_MINIPORT_INIT_PARAMETERS 0x81
#define NDIS_OBJECT_TYPE_SG_DMA_DESCRIPTION 0x83
#define NDIS_OBJECT_TYPE_MINIPORT_INTERRUPT 0x84
#define NDIS_OBJECT_TYPE_DEVICE_OBJECT_ATTRIBUTES 0x85
#define NDIS_OBJECT_TYPE_BIND_PARAMETERS 0x86
#define NDIS_OBJECT_TYPE_OPEN_PARAMETERS 0x87
#define NDIS_OBJECT_TYPE_RSS_CAPABILITIES 0x88
#define NDIS_OBJECT_TYPE_RSS_PARAMETERS 0x89
#define NDIS_OBJECT_TYPE_MINIPORT_DRIVER_CHARACTERISTICS 0x8a
#define NDIS_OBJECT_TYPE_FILTER_DRIVER_CHARACTERISTICS 0x8b
#define NDIS_OBJECT_TYPE_FILTER_PARTIAL_CHARACTERISTICS 0x8c
#define NDIS_OBJECT_TYPE_FILTER_ATTRIBUTES 0x8d
#define NDIS_OBJECT_TYPE_CLIENT_CHIMNEY_OFFLOAD_GENERIC_CHARACTERISTICS 0x8e
#define NDIS_OBJECT_TYPE_PROVIDER_CHIMNEY_OFFLOAD_GENERIC_CHARACTERISTICS 0x8f
#define NDIS_OBJECT_TYPE_CO_PROTOCOL_CHARACTERISTICS 0x90
#define NDIS_OBJECT_TYPE_CO_MINIPORT_CHARACTERISTICS 0x91
#define NDIS_OBJECT_TYPE_MINIPORT_PNP_CHARACTERISTICS 0x92
#define NDIS_OBJECT_TYPE_CLIENT_CHIMNEY_OFFLOAD_CHARACTERISTICS 0x93
#define NDIS_OBJECT_TYPE_PROVIDER_CHIMNEY_OFFLOAD_CHARACTERISTICS 0x94
#define NDIS_OBJECT_TYPE_PROTOCOL_DRIVER_CHARACTERISTICS 0x95
#define NDIS_OBJECT_TYPE_REQUEST_EX 0x96
#define NDIS_OBJECT_TYPE_OID_REQUEST 0x96
#define NDIS_OBJECT_TYPE_TIMER_CHARACTERISTICS 0x97
#define NDIS_OBJECT_TYPE_STATUS_INDICATION 0x98
#define NDIS_OBJECT_TYPE_FILTER_ATTACH_PARAMETERS 0x99
#define NDIS_OBJECT_TYPE_FILTER_PAUSE_PARAMETERS 0x9a
#define NDIS_OBJECT_TYPE_FILTER_RESTART_PARAMETERS 0x9b
#define NDIS_OBJECT_TYPE_PORT_CHARACTERISTICS 0x9c
#define NDIS_OBJECT_TYPE_PORT_STATE 0x9d
#define NDIS_OBJECT_TYPE_MINIPORT_ADAPTER_REGISTRATION_ATTRIBUTES 0x9e
#define NDIS_OBJECT_TYPE_MINIPORT_ADAPTER_GENERAL_ATTRIBUTES 0x9f
#define NDIS_OBJECT_TYPE_MINIPORT_ADAPTER_OFFLOAD_ATTRIBUTES 0xa0
#define NDIS_OBJECT_TYPE_MINIPORT_ADAPTER_NATIVE_802_11_ATTRIBUTES 0xa1
#define NDIS_OBJECT_TYPE_RESTART_GENERAL_ATTRIBUTES 0xa2
#define NDIS_OBJECT_TYPE_PROTOCOL_RESTART_PARAMETERS 0xa3
#define NDIS_OBJECT_TYPE_MINIPORT_ADD_DEVICE_REGISTRATION_ATTRIBUTES 0xa4
#define NDIS_OBJECT_TYPE_CO_CALL_MANAGER_OPTIONAL_HANDLERS 0xa5
#define NDIS_OBJECT_TYPE_CO_CLIENT_OPTIONAL_HANDLERS 0xa6
#define NDIS_OBJECT_TYPE_OFFLOAD 0xa7
#define NDIS_OBJECT_TYPE_OFFLOAD_ENCAPSULATION 0xa8
#define NDIS_OBJECT_TYPE_CONFIGURATION_OBJECT 0xa9
#define NDIS_OBJECT_TYPE_DRIVER_WRAPPER_OBJECT 0xaa
#if NDIS_SUPPORT_NDIS61
#define NDIS_OBJECT_TYPE_HD_SPLIT_ATTRIBUTES 0xab
#endif
#define NDIS_OBJECT_TYPE_NSI_NETWORK_RW_STRUCT 0xac
#define NDIS_OBJECT_TYPE_NSI_COMPARTMENT_RW_STRUCT 0xad
#define NDIS_OBJECT_TYPE_NSI_INTERFACE_PERSIST_RW_STRUCT 0xae
#if NDIS_SUPPORT_NDIS61
#define NDIS_OBJECT_TYPE_MINIPORT_ADAPTER_HARDWARE_ASSIST_ATTRIBUTES 0xaf
#endif
#if NDIS_SUPPORT_NDIS620
#define NDIS_OBJECT_TYPE_SHARED_MEMORY_PROVIDER_CHARACTERISTICS 0xb0
#define NDIS_OBJECT_TYPE_RSS_PROCESSOR_INFO 0xb1
#endif
#if NDIS_SUPPORT_NDIS630
#define NDIS_OBJECT_TYPE_NDK_PROVIDER_CHARACTERISTICS 0xb2
#define NDIS_OBJECT_TYPE_MINIPORT_ADAPTER_NDK_ATTRIBUTES 0xb3
#define NDIS_OBJECT_TYPE_MINIPORT_SS_CHARACTERISTICS 0xb4
#define NDIS_OBJECT_TYPE_QOS_CAPABILITIES 0xb5
#define NDIS_OBJECT_TYPE_QOS_PARAMETERS 0xb6
#define NDIS_OBJECT_TYPE_QOS_CLASSIFICATION_ELEMENT 0xb7
#define NDIS_OBJECT_TYPE_SWITCH_OPTIONAL_HANDLERS 0xb8
#define NDIS_OBJECT_TYPE_IOCTL_OID_INFO 0xb9
#define NDIS_OBJECT_TYPE_LBFO_DIAGNOSTIC_OID 0xba
#endif

  typedef struct _NDIS_OBJECT_HEADER {
    UCHAR Type;
    UCHAR Revision;
    USHORT Size;
  } NDIS_OBJECT_HEADER, *PNDIS_OBJECT_HEADER;

  typedef enum _NDIS_REQUEST_TYPE {
    NdisRequestQueryInformation,
    NdisRequestSetInformation,
    NdisRequestQueryStatistics,
    NdisRequestOpen,
    NdisRequestClose,
    NdisRequestSend,
    NdisRequestTransferData,
    NdisRequestReset,
    NdisRequestGeneric1,
    NdisRequestGeneric2,
    NdisRequestGeneric3,
    NdisRequestGeneric4
#if NDIS_SUPPORT_NDIS6
    , NdisRequestMethod
#endif
  } NDIS_REQUEST_TYPE, *PNDIS_REQUEST_TYPE;

#define NDIS_OBJECT_REVISION_1 1

#if NTDDI_VERSION >= 0x06000000 || NDIS_SUPPORT_NDIS6
#define NDIS_STATISTICS_FLAGS_VALID_DIRECTED_FRAMES_RCV 0x00000001
#define NDIS_STATISTICS_FLAGS_VALID_MULTICAST_FRAMES_RCV 0x00000002
#define NDIS_STATISTICS_FLAGS_VALID_BROADCAST_FRAMES_RCV 0x00000004
#define NDIS_STATISTICS_FLAGS_VALID_BYTES_RCV 0x00000008
#define NDIS_STATISTICS_FLAGS_VALID_RCV_DISCARDS 0x00000010
#define NDIS_STATISTICS_FLAGS_VALID_RCV_ERROR 0x00000020
#define NDIS_STATISTICS_FLAGS_VALID_DIRECTED_FRAMES_XMIT 0x00000040
#define NDIS_STATISTICS_FLAGS_VALID_MULTICAST_FRAMES_XMIT 0x00000080
#define NDIS_STATISTICS_FLAGS_VALID_BROADCAST_FRAMES_XMIT 0x00000100
#define NDIS_STATISTICS_FLAGS_VALID_BYTES_XMIT 0x00000200
#define NDIS_STATISTICS_FLAGS_VALID_XMIT_ERROR 0x00000400
#define NDIS_STATISTICS_FLAGS_VALID_XMIT_DISCARDS 0x00008000
#define NDIS_STATISTICS_FLAGS_VALID_DIRECTED_BYTES_RCV 0x00010000
#define NDIS_STATISTICS_FLAGS_VALID_MULTICAST_BYTES_RCV 0x00020000
#define NDIS_STATISTICS_FLAGS_VALID_BROADCAST_BYTES_RCV 0x00040000
#define NDIS_STATISTICS_FLAGS_VALID_DIRECTED_BYTES_XMIT 0x00080000
#define NDIS_STATISTICS_FLAGS_VALID_MULTICAST_BYTES_XMIT 0x00100000
#define NDIS_STATISTICS_FLAGS_VALID_BROADCAST_BYTES_XMIT 0x00200000

#define NDIS_INTERRUPT_MODERATION_CHANGE_NEEDS_RESET 0x00000001
#define NDIS_INTERRUPT_MODERATION_CHANGE_NEEDS_REINITIALIZE 0x00000002

#define NDIS_STATISTICS_INFO_REVISION_1 1
#define NDIS_INTERRUPT_MODERATION_PARAMETERS_REVISION_1 1
#define NDIS_TIMEOUT_DPC_REQUEST_CAPABILITIES_REVISION_1 1
#define NDIS_OBJECT_TYPE_PCI_DEVICE_CUSTOM_PROPERTIES_REVISION_1 1
#if NTDDI_VERSION >= 0x06010000 || NDIS_SUPPORT_NDIS620
#define NDIS_OBJECT_TYPE_PCI_DEVICE_CUSTOM_PROPERTIES_REVISION_2 2
#endif
#if NDIS_SUPPORT_NDIS630
#define NDIS_RSC_STATISTICS_REVISION_1 1
#endif

#define NDIS_SIZEOF_STATISTICS_INFO_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_STATISTICS_INFO, ifHCOutBroadcastOctets)
#define NDIS_SIZEOF_INTERRUPT_MODERATION_PARAMETERS_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_INTERRUPT_MODERATION_PARAMETERS, InterruptModeration)
#define NDIS_SIZEOF_TIMEOUT_DPC_REQUEST_CAPABILITIES_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_TIMEOUT_DPC_REQUEST_CAPABILITIES, TimeoutArray)
#define NDIS_SIZEOF_PCI_DEVICE_CUSTOM_PROPERTIES_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_PCI_DEVICE_CUSTOM_PROPERTIES, MaxLinkWidth)
#if NTDDI_VERSION >= 0x06010000 || NDIS_SUPPORT_NDIS620
#define NDIS_SIZEOF_PCI_DEVICE_CUSTOM_PROPERTIES_REVISION_2 RTL_SIZEOF_THROUGH_FIELD (NDIS_PCI_DEVICE_CUSTOM_PROPERTIES, MaxInterruptMessages)
#endif
#if NDIS_SUPPORT_NDIS630
#define NDIS_SIZEOF_RSC_STATISTICS_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_RSC_STATISTICS_INFO, Aborts)
#endif

  typedef struct _NDIS_STATISTICS_INFO {
    NDIS_OBJECT_HEADER Header;
    ULONG SupportedStatistics;
    ULONG64 ifInDiscards;
    ULONG64 ifInErrors;
    ULONG64 ifHCInOctets;
    ULONG64 ifHCInUcastPkts;
    ULONG64 ifHCInMulticastPkts;
    ULONG64 ifHCInBroadcastPkts;
    ULONG64 ifHCOutOctets;
    ULONG64 ifHCOutUcastPkts;
    ULONG64 ifHCOutMulticastPkts;
    ULONG64 ifHCOutBroadcastPkts;
    ULONG64 ifOutErrors;
    ULONG64 ifOutDiscards;
    ULONG64 ifHCInUcastOctets;
    ULONG64 ifHCInMulticastOctets;
    ULONG64 ifHCInBroadcastOctets;
    ULONG64 ifHCOutUcastOctets;
    ULONG64 ifHCOutMulticastOctets;
    ULONG64 ifHCOutBroadcastOctets;
  } NDIS_STATISTICS_INFO, *PNDIS_STATISTICS_INFO;

#if NDIS_SUPPORT_NDIS630
  typedef struct _NDIS_RSC_STATISTICS_INFO {
    NDIS_OBJECT_HEADER Header;
    ULONG64 CoalescedPkts;
    ULONG64 CoalescedOctets;
    ULONG64 CoalesceEvents;
    ULONG64 Aborts;
  } NDIS_RSC_STATISTICS_INFO, *PNDIS_RSC_STATISTICS_INFO;
#endif

  typedef enum _NDIS_INTERRUPT_MODERATION {
    NdisInterruptModerationUnknown,
    NdisInterruptModerationNotSupported,
    NdisInterruptModerationEnabled,
    NdisInterruptModerationDisabled
  } NDIS_INTERRUPT_MODERATION, *PNDIS_INTERRUPT_MODERATION;

  typedef struct _NDIS_INTERRUPT_MODERATION_PARAMETERS {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    NDIS_INTERRUPT_MODERATION InterruptModeration;
  } NDIS_INTERRUPT_MODERATION_PARAMETERS, *PNDIS_INTERRUPT_MODERATION_PARAMETERS;

  typedef struct _NDIS_TIMEOUT_DPC_REQUEST_CAPABILITIES {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    ULONG TimeoutArrayLength;
    ULONG TimeoutArray[1];
  } NDIS_TIMEOUT_DPC_REQUEST_CAPABILITIES, *PNDIS_TIMEOUT_DPC_REQUEST_CAPABILITIES;

  typedef struct _NDIS_PCI_DEVICE_CUSTOM_PROPERTIES {
    NDIS_OBJECT_HEADER Header;
    UINT32 DeviceType;
    UINT32 CurrentSpeedAndMode;
    UINT32 CurrentPayloadSize;
    UINT32 MaxPayloadSize;
    UINT32 MaxReadRequestSize;
    UINT32 CurrentLinkSpeed;
    UINT32 CurrentLinkWidth;
    UINT32 MaxLinkSpeed;
    UINT32 MaxLinkWidth;
#if (NTDDI_VERSION >= 0x06010000 || NDIS_SUPPORT_NDIS620)
    UINT32 PciExpressVersion;
    UINT32 InterruptType;
    UINT32 MaxInterruptMessages;
#endif
  } NDIS_PCI_DEVICE_CUSTOM_PROPERTIES, *PNDIS_PCI_DEVICE_CUSTOM_PROPERTIES;
#endif

#define OID_GEN_SUPPORTED_LIST 0x00010101
#define OID_GEN_HARDWARE_STATUS 0x00010102
#define OID_GEN_MEDIA_SUPPORTED 0x00010103
#define OID_GEN_MEDIA_IN_USE 0x00010104
#define OID_GEN_MAXIMUM_LOOKAHEAD 0x00010105
#define OID_GEN_MAXIMUM_FRAME_SIZE 0x00010106
#define OID_GEN_LINK_SPEED 0x00010107
#define OID_GEN_TRANSMIT_BUFFER_SPACE 0x00010108
#define OID_GEN_RECEIVE_BUFFER_SPACE 0x00010109
#define OID_GEN_TRANSMIT_BLOCK_SIZE 0x0001010a
#define OID_GEN_RECEIVE_BLOCK_SIZE 0x0001010b
#define OID_GEN_VENDOR_ID 0x0001010c
#define OID_GEN_VENDOR_DESCRIPTION 0x0001010d
#define OID_GEN_CURRENT_PACKET_FILTER 0x0001010e
#define OID_GEN_CURRENT_LOOKAHEAD 0x0001010f
#define OID_GEN_DRIVER_VERSION 0x00010110
#define OID_GEN_MAXIMUM_TOTAL_SIZE 0x00010111
#define OID_GEN_PROTOCOL_OPTIONS 0x00010112
#define OID_GEN_MAC_OPTIONS 0x00010113
#define OID_GEN_MEDIA_CONNECT_STATUS 0x00010114
#define OID_GEN_MAXIMUM_SEND_PACKETS 0x00010115

#define OID_GEN_VENDOR_DRIVER_VERSION 0x00010116
#define OID_GEN_SUPPORTED_GUIDS 0x00010117
#define OID_GEN_NETWORK_LAYER_ADDRESSES 0x00010118
#define OID_GEN_TRANSPORT_HEADER_OFFSET 0x00010119
#define OID_GEN_MEDIA_CAPABILITIES 0x00010201
#define OID_GEN_PHYSICAL_MEDIUM 0x00010202
#if NTDDI_VERSION >= 0x06000000 || NDIS_SUPPORT_NDIS6
#define OID_GEN_RECEIVE_SCALE_CAPABILITIES 0x00010203
#define OID_GEN_RECEIVE_SCALE_PARAMETERS 0x00010204
#define OID_GEN_MAC_ADDRESS 0x00010205
#define OID_GEN_MAX_LINK_SPEED 0x00010206
#define OID_GEN_LINK_STATE 0x00010207
#define OID_GEN_LINK_PARAMETERS 0x00010208
#define OID_GEN_INTERRUPT_MODERATION 0x00010209
#define OID_GEN_NDIS_RESERVED_3 0x0001020a
#define OID_GEN_NDIS_RESERVED_4 0x0001020b
#define OID_GEN_NDIS_RESERVED_5 0x0001020c
#define OID_GEN_ENUMERATE_PORTS 0x0001020d
#define OID_GEN_PORT_STATE 0x0001020e
#define OID_GEN_PORT_AUTHENTICATION_PARAMETERS 0x0001020f
#define OID_GEN_TIMEOUT_DPC_REQUEST_CAPABILITIES 0x00010210
#define OID_GEN_PCI_DEVICE_CUSTOM_PROPERTIES 0x00010211
#define OID_GEN_NDIS_RESERVED_6 0x00010212
#define OID_GEN_PHYSICAL_MEDIUM_EX 0x00010213
#endif

#define OID_GEN_MACHINE_NAME 0x0001021a
#define OID_GEN_RNDIS_CONFIG_PARAMETER 0x0001021b
#define OID_GEN_VLAN_ID 0x0001021c
#if NTDDI_VERSION >= 0x06000000 || NDIS_SUPPORT_NDIS6
#define OID_GEN_RECEIVE_HASH 0x0001021f
#define OID_GEN_MINIPORT_RESTART_ATTRIBUTES 0x0001021d

#if NDIS_SUPPORT_NDIS61
#define OID_GEN_HD_SPLIT_PARAMETERS 0x0001021e
#define OID_GEN_HD_SPLIT_CURRENT_CONFIG 0x00010220
#endif

#define OID_GEN_PROMISCUOUS_MODE 0x00010280
#define OID_GEN_LAST_CHANGE 0x00010281
#define OID_GEN_DISCONTINUITY_TIME 0x00010282
#define OID_GEN_OPERATIONAL_STATUS 0x00010283
#define OID_GEN_XMIT_LINK_SPEED 0x00010284
#define OID_GEN_RCV_LINK_SPEED 0x00010285
#define OID_GEN_UNKNOWN_PROTOS 0x00010286
#define OID_GEN_INTERFACE_INFO 0x00010287
#define OID_GEN_ADMIN_STATUS 0x00010288
#define OID_GEN_ALIAS 0x00010289
#define OID_GEN_MEDIA_CONNECT_STATUS_EX 0x0001028a
#define OID_GEN_LINK_SPEED_EX 0x0001028b
#define OID_GEN_MEDIA_DUPLEX_STATE 0x0001028c
#define OID_GEN_IP_OPER_STATUS 0x0001028d

#define OID_WWAN_DRIVER_CAPS 0x0e010100
#define OID_WWAN_DEVICE_CAPS 0x0e010101
#define OID_WWAN_READY_INFO 0x0e010102
#define OID_WWAN_RADIO_STATE 0x0e010103
#define OID_WWAN_PIN 0x0e010104
#define OID_WWAN_PIN_LIST 0x0e010105
#define OID_WWAN_HOME_PROVIDER 0x0e010106
#define OID_WWAN_PREFERRED_PROVIDERS 0x0e010107
#define OID_WWAN_VISIBLE_PROVIDERS 0x0e010108
#define OID_WWAN_REGISTER_STATE 0x0e010109
#define OID_WWAN_PACKET_SERVICE 0x0e01010a
#define OID_WWAN_SIGNAL_STATE 0x0e01010b
#define OID_WWAN_CONNECT 0x0e01010c
#define OID_WWAN_PROVISIONED_CONTEXTS 0x0e01010d
#define OID_WWAN_SERVICE_ACTIVATION 0x0e01010e
#define OID_WWAN_SMS_CONFIGURATION 0x0e01010f
#define OID_WWAN_SMS_READ 0x0e010110
#define OID_WWAN_SMS_SEND 0x0e010111
#define OID_WWAN_SMS_DELETE 0x0e010112
#define OID_WWAN_SMS_STATUS 0x0e010113
#define OID_WWAN_VENDOR_SPECIFIC 0x0e010114
#endif

#if NTDDI_VERSION >= 0x06020000 || NDIS_SUPPORT_NDIS630
#define OID_WWAN_AUTH_CHALLENGE 0x0e010115
#define OID_WWAN_ENUMERATE_DEVICE_SERVICES 0x0e010116
#define OID_WWAN_SUBSCRIBE_DEVICE_SERVICE_EVENTS 0x0e010117
#define OID_WWAN_DEVICE_SERVICE_COMMAND 0x0e010118
#define OID_WWAN_USSD 0x0e010119
#define OID_WWAN_PIN_EX 0x0e010121
#define OID_WWAN_ENUMERATE_DEVICE_SERVICE_COMMANDS 0x0e010122
#define OID_WWAN_DEVICE_SERVICE_SESSION 0x0e010123
#define OID_WWAN_DEVICE_SERVICE_SESSION_WRITE 0x0e010124
#define OID_WWAN_PREFERRED_MULTICARRIER_PROVIDERS 0x0e010125
#endif

#define OID_GEN_XMIT_OK 0x00020101
#define OID_GEN_RCV_OK 0x00020102
#define OID_GEN_XMIT_ERROR 0x00020103
#define OID_GEN_RCV_ERROR 0x00020104
#define OID_GEN_RCV_NO_BUFFER 0x00020105
#if NTDDI_VERSION >= 0x06000000 || NDIS_SUPPORT_NDIS6
#define OID_GEN_STATISTICS 0x00020106
#endif

#define OID_GEN_DIRECTED_BYTES_XMIT 0x00020201
#define OID_GEN_DIRECTED_FRAMES_XMIT 0x00020202
#define OID_GEN_MULTICAST_BYTES_XMIT 0x00020203
#define OID_GEN_MULTICAST_FRAMES_XMIT 0x00020204
#define OID_GEN_BROADCAST_BYTES_XMIT 0x00020205
#define OID_GEN_BROADCAST_FRAMES_XMIT 0x00020206
#define OID_GEN_DIRECTED_BYTES_RCV 0x00020207
#define OID_GEN_DIRECTED_FRAMES_RCV 0x00020208
#define OID_GEN_MULTICAST_BYTES_RCV 0x00020209
#define OID_GEN_MULTICAST_FRAMES_RCV 0x0002020a
#define OID_GEN_BROADCAST_BYTES_RCV 0x0002020b
#define OID_GEN_BROADCAST_FRAMES_RCV 0x0002020c
#define OID_GEN_RCV_CRC_ERROR 0x0002020d
#define OID_GEN_TRANSMIT_QUEUE_LENGTH 0x0002020e
#define OID_GEN_GET_TIME_CAPS 0x0002020f
#define OID_GEN_GET_NETCARD_TIME 0x00020210
#define OID_GEN_NETCARD_LOAD 0x00020211
#define OID_GEN_DEVICE_PROFILE 0x00020212
#define OID_GEN_INIT_TIME_MS 0x00020213
#define OID_GEN_RESET_COUNTS 0x00020214
#define OID_GEN_MEDIA_SENSE_COUNTS 0x00020215
#define OID_GEN_FRIENDLY_NAME 0x00020216
#define OID_GEN_NDIS_RESERVED_1 0x00020217
#define OID_GEN_NDIS_RESERVED_2 0x00020218
#define OID_GEN_NDIS_RESERVED_5 0x0001020c
#if NTDDI_VERSION >= 0x06000000 || NDIS_SUPPORT_NDIS6
#define OID_GEN_BYTES_RCV 0x00020219
#define OID_GEN_BYTES_XMIT 0x0002021a
#define OID_GEN_RCV_DISCARDS 0x0002021b
#define OID_GEN_XMIT_DISCARDS 0x0002021c
#endif
#if NTDDI_VERSION >= 0x06020000 || NDIS_SUPPORT_NDIS630
#define OID_TCP_RSC_STATISTICS 0x0002021d
#define OID_GEN_NDIS_RESERVED_7 0x0002021e
#endif

#define OID_GEN_CO_SUPPORTED_LIST OID_GEN_SUPPORTED_LIST
#define OID_GEN_CO_HARDWARE_STATUS OID_GEN_HARDWARE_STATUS
#define OID_GEN_CO_MEDIA_SUPPORTED OID_GEN_MEDIA_SUPPORTED
#define OID_GEN_CO_MEDIA_IN_USE OID_GEN_MEDIA_IN_USE
#define OID_GEN_CO_LINK_SPEED OID_GEN_LINK_SPEED
#define OID_GEN_CO_VENDOR_ID OID_GEN_VENDOR_ID
#define OID_GEN_CO_VENDOR_DESCRIPTION OID_GEN_VENDOR_DESCRIPTION
#define OID_GEN_CO_DRIVER_VERSION OID_GEN_DRIVER_VERSION
#define OID_GEN_CO_PROTOCOL_OPTIONS OID_GEN_PROTOCOL_OPTIONS
#define OID_GEN_CO_MAC_OPTIONS OID_GEN_MAC_OPTIONS
#define OID_GEN_CO_MEDIA_CONNECT_STATUS OID_GEN_MEDIA_CONNECT_STATUS
#define OID_GEN_CO_VENDOR_DRIVER_VERSION OID_GEN_VENDOR_DRIVER_VERSION
#define OID_GEN_CO_SUPPORTED_GUIDS OID_GEN_SUPPORTED_GUIDS
#define OID_GEN_CO_GET_TIME_CAPS OID_GEN_GET_TIME_CAPS
#define OID_GEN_CO_GET_NETCARD_TIME OID_GEN_GET_NETCARD_TIME
#define OID_GEN_CO_MINIMUM_LINK_SPEED 0x00020120

#define OID_GEN_CO_XMIT_PDUS_OK OID_GEN_XMIT_OK
#define OID_GEN_CO_RCV_PDUS_OK OID_GEN_RCV_OK
#define OID_GEN_CO_XMIT_PDUS_ERROR OID_GEN_XMIT_ERROR
#define OID_GEN_CO_RCV_PDUS_ERROR OID_GEN_RCV_ERROR
#define OID_GEN_CO_RCV_PDUS_NO_BUFFER OID_GEN_RCV_NO_BUFFER

#define OID_GEN_CO_RCV_CRC_ERROR OID_GEN_RCV_CRC_ERROR
#define OID_GEN_CO_TRANSMIT_QUEUE_LENGTH OID_GEN_TRANSMIT_QUEUE_LENGTH
#define OID_GEN_CO_BYTES_XMIT OID_GEN_DIRECTED_BYTES_XMIT
#define OID_GEN_CO_BYTES_RCV OID_GEN_DIRECTED_BYTES_RCV
#define OID_GEN_CO_NETCARD_LOAD OID_GEN_NETCARD_LOAD
#define OID_GEN_CO_DEVICE_PROFILE OID_GEN_DEVICE_PROFILE
#define OID_GEN_CO_BYTES_XMIT_OUTSTANDING 0x00020221

#define OID_802_3_PERMANENT_ADDRESS 0x01010101
#define OID_802_3_CURRENT_ADDRESS 0x01010102
#define OID_802_3_MULTICAST_LIST 0x01010103
#define OID_802_3_MAXIMUM_LIST_SIZE 0x01010104

#define OID_802_3_MAC_OPTIONS 0x01010105

#define NDIS_802_3_MAC_OPTION_PRIORITY 0x00000001

#define OID_802_3_RCV_ERROR_ALIGNMENT 0x01020101
#define OID_802_3_XMIT_ONE_COLLISION 0x01020102
#define OID_802_3_XMIT_MORE_COLLISIONS 0x01020103

#define OID_802_3_XMIT_DEFERRED 0x01020201
#define OID_802_3_XMIT_MAX_COLLISIONS 0x01020202
#define OID_802_3_RCV_OVERRUN 0x01020203
#define OID_802_3_XMIT_UNDERRUN 0x01020204
#define OID_802_3_XMIT_HEARTBEAT_FAILURE 0x01020205
#define OID_802_3_XMIT_TIMES_CRS_LOST 0x01020206
#define OID_802_3_XMIT_LATE_COLLISIONS 0x01020207

#if (NTDDI_VERSION >= 0x06000000 || NDIS_SUPPORT_NDIS6)
#define OID_802_3_ADD_MULTICAST_ADDRESS 0x01010208
#define OID_802_3_DELETE_MULTICAST_ADDRESS 0x01010209
#endif

#define OID_802_5_PERMANENT_ADDRESS 0x02010101
#define OID_802_5_CURRENT_ADDRESS 0x02010102
#define OID_802_5_CURRENT_FUNCTIONAL 0x02010103
#define OID_802_5_CURRENT_GROUP 0x02010104
#define OID_802_5_LAST_OPEN_STATUS 0x02010105
#define OID_802_5_CURRENT_RING_STATUS 0x02010106
#define OID_802_5_CURRENT_RING_STATE 0x02010107

#define OID_802_5_LINE_ERRORS 0x02020101
#define OID_802_5_LOST_FRAMES 0x02020102

#define OID_802_5_BURST_ERRORS 0x02020201
#define OID_802_5_AC_ERRORS 0x02020202
#define OID_802_5_ABORT_DELIMETERS 0x02020203
#define OID_802_5_FRAME_COPIED_ERRORS 0x02020204
#define OID_802_5_FREQUENCY_ERRORS 0x02020205
#define OID_802_5_TOKEN_ERRORS 0x02020206
#define OID_802_5_INTERNAL_ERRORS 0x02020207

#define OID_FDDI_LONG_PERMANENT_ADDR 0x03010101
#define OID_FDDI_LONG_CURRENT_ADDR 0x03010102
#define OID_FDDI_LONG_MULTICAST_LIST 0x03010103
#define OID_FDDI_LONG_MAX_LIST_SIZE 0x03010104
#define OID_FDDI_SHORT_PERMANENT_ADDR 0x03010105
#define OID_FDDI_SHORT_CURRENT_ADDR 0x03010106
#define OID_FDDI_SHORT_MULTICAST_LIST 0x03010107
#define OID_FDDI_SHORT_MAX_LIST_SIZE 0x03010108

#define OID_FDDI_ATTACHMENT_TYPE 0x03020101
#define OID_FDDI_UPSTREAM_NODE_LONG 0x03020102
#define OID_FDDI_DOWNSTREAM_NODE_LONG 0x03020103
#define OID_FDDI_FRAME_ERRORS 0x03020104
#define OID_FDDI_FRAMES_LOST 0x03020105
#define OID_FDDI_RING_MGT_STATE 0x03020106
#define OID_FDDI_LCT_FAILURES 0x03020107
#define OID_FDDI_LEM_REJECTS 0x03020108
#define OID_FDDI_LCONNECTION_STATE 0x03020109

#define OID_FDDI_SMT_STATION_ID 0x03030201
#define OID_FDDI_SMT_OP_VERSION_ID 0x03030202
#define OID_FDDI_SMT_HI_VERSION_ID 0x03030203
#define OID_FDDI_SMT_LO_VERSION_ID 0x03030204
#define OID_FDDI_SMT_MANUFACTURER_DATA 0x03030205
#define OID_FDDI_SMT_USER_DATA 0x03030206
#define OID_FDDI_SMT_MIB_VERSION_ID 0x03030207
#define OID_FDDI_SMT_MAC_CT 0x03030208
#define OID_FDDI_SMT_NON_MASTER_CT 0x03030209
#define OID_FDDI_SMT_MASTER_CT 0x0303020a
#define OID_FDDI_SMT_AVAILABLE_PATHS 0x0303020b
#define OID_FDDI_SMT_CONFIG_CAPABILITIES 0x0303020c
#define OID_FDDI_SMT_CONFIG_POLICY 0x0303020d
#define OID_FDDI_SMT_CONNECTION_POLICY 0x0303020e
#define OID_FDDI_SMT_T_NOTIFY 0x0303020f
#define OID_FDDI_SMT_STAT_RPT_POLICY 0x03030210
#define OID_FDDI_SMT_TRACE_MAX_EXPIRATION 0x03030211
#define OID_FDDI_SMT_PORT_INDEXES 0x03030212
#define OID_FDDI_SMT_MAC_INDEXES 0x03030213
#define OID_FDDI_SMT_BYPASS_PRESENT 0x03030214
#define OID_FDDI_SMT_ECM_STATE 0x03030215
#define OID_FDDI_SMT_CF_STATE 0x03030216
#define OID_FDDI_SMT_HOLD_STATE 0x03030217
#define OID_FDDI_SMT_REMOTE_DISCONNECT_FLAG 0x03030218
#define OID_FDDI_SMT_STATION_STATUS 0x03030219
#define OID_FDDI_SMT_PEER_WRAP_FLAG 0x0303021a
#define OID_FDDI_SMT_MSG_TIME_STAMP 0x0303021b
#define OID_FDDI_SMT_TRANSITION_TIME_STAMP 0x0303021c
#define OID_FDDI_SMT_SET_COUNT 0x0303021d
#define OID_FDDI_SMT_LAST_SET_STATION_ID 0x0303021e
#define OID_FDDI_MAC_FRAME_STATUS_FUNCTIONS 0x0303021f
#define OID_FDDI_MAC_BRIDGE_FUNCTIONS 0x03030220
#define OID_FDDI_MAC_T_MAX_CAPABILITY 0x03030221
#define OID_FDDI_MAC_TVX_CAPABILITY 0x03030222
#define OID_FDDI_MAC_AVAILABLE_PATHS 0x03030223
#define OID_FDDI_MAC_CURRENT_PATH 0x03030224
#define OID_FDDI_MAC_UPSTREAM_NBR 0x03030225
#define OID_FDDI_MAC_DOWNSTREAM_NBR 0x03030226
#define OID_FDDI_MAC_OLD_UPSTREAM_NBR 0x03030227
#define OID_FDDI_MAC_OLD_DOWNSTREAM_NBR 0x03030228
#define OID_FDDI_MAC_DUP_ADDRESS_TEST 0x03030229
#define OID_FDDI_MAC_REQUESTED_PATHS 0x0303022a
#define OID_FDDI_MAC_DOWNSTREAM_PORT_TYPE 0x0303022b
#define OID_FDDI_MAC_INDEX 0x0303022c
#define OID_FDDI_MAC_SMT_ADDRESS 0x0303022d
#define OID_FDDI_MAC_LONG_GRP_ADDRESS 0x0303022e
#define OID_FDDI_MAC_SHORT_GRP_ADDRESS 0x0303022f
#define OID_FDDI_MAC_T_REQ 0x03030230
#define OID_FDDI_MAC_T_NEG 0x03030231
#define OID_FDDI_MAC_T_MAX 0x03030232
#define OID_FDDI_MAC_TVX_VALUE 0x03030233
#define OID_FDDI_MAC_T_PRI0 0x03030234
#define OID_FDDI_MAC_T_PRI1 0x03030235
#define OID_FDDI_MAC_T_PRI2 0x03030236
#define OID_FDDI_MAC_T_PRI3 0x03030237
#define OID_FDDI_MAC_T_PRI4 0x03030238
#define OID_FDDI_MAC_T_PRI5 0x03030239
#define OID_FDDI_MAC_T_PRI6 0x0303023a
#define OID_FDDI_MAC_FRAME_CT 0x0303023b
#define OID_FDDI_MAC_COPIED_CT 0x0303023c
#define OID_FDDI_MAC_TRANSMIT_CT 0x0303023d
#define OID_FDDI_MAC_TOKEN_CT 0x0303023e
#define OID_FDDI_MAC_ERROR_CT 0x0303023f
#define OID_FDDI_MAC_LOST_CT 0x03030240
#define OID_FDDI_MAC_TVX_EXPIRED_CT 0x03030241
#define OID_FDDI_MAC_NOT_COPIED_CT 0x03030242
#define OID_FDDI_MAC_LATE_CT 0x03030243
#define OID_FDDI_MAC_RING_OP_CT 0x03030244
#define OID_FDDI_MAC_FRAME_ERROR_THRESHOLD 0x03030245
#define OID_FDDI_MAC_FRAME_ERROR_RATIO 0x03030246
#define OID_FDDI_MAC_NOT_COPIED_THRESHOLD 0x03030247
#define OID_FDDI_MAC_NOT_COPIED_RATIO 0x03030248
#define OID_FDDI_MAC_RMT_STATE 0x03030249
#define OID_FDDI_MAC_DA_FLAG 0x0303024a
#define OID_FDDI_MAC_UNDA_FLAG 0x0303024b
#define OID_FDDI_MAC_FRAME_ERROR_FLAG 0x0303024c
#define OID_FDDI_MAC_NOT_COPIED_FLAG 0x0303024d
#define OID_FDDI_MAC_MA_UNITDATA_AVAILABLE 0x0303024e
#define OID_FDDI_MAC_HARDWARE_PRESENT 0x0303024f
#define OID_FDDI_MAC_MA_UNITDATA_ENABLE 0x03030250
#define OID_FDDI_PATH_INDEX 0x03030251
#define OID_FDDI_PATH_RING_LATENCY 0x03030252
#define OID_FDDI_PATH_TRACE_STATUS 0x03030253
#define OID_FDDI_PATH_SBA_PAYLOAD 0x03030254
#define OID_FDDI_PATH_SBA_OVERHEAD 0x03030255
#define OID_FDDI_PATH_CONFIGURATION 0x03030256
#define OID_FDDI_PATH_T_R_MODE 0x03030257
#define OID_FDDI_PATH_SBA_AVAILABLE 0x03030258
#define OID_FDDI_PATH_TVX_LOWER_BOUND 0x03030259
#define OID_FDDI_PATH_T_MAX_LOWER_BOUND 0x0303025a
#define OID_FDDI_PATH_MAX_T_REQ 0x0303025b
#define OID_FDDI_PORT_MY_TYPE 0x0303025c
#define OID_FDDI_PORT_NEIGHBOR_TYPE 0x0303025d
#define OID_FDDI_PORT_CONNECTION_POLICIES 0x0303025e
#define OID_FDDI_PORT_MAC_INDICATED 0x0303025f
#define OID_FDDI_PORT_CURRENT_PATH 0x03030260
#define OID_FDDI_PORT_REQUESTED_PATHS 0x03030261
#define OID_FDDI_PORT_MAC_PLACEMENT 0x03030262
#define OID_FDDI_PORT_AVAILABLE_PATHS 0x03030263
#define OID_FDDI_PORT_MAC_LOOP_TIME 0x03030264
#define OID_FDDI_PORT_PMD_CLASS 0x03030265
#define OID_FDDI_PORT_CONNECTION_CAPABILITIES 0x03030266
#define OID_FDDI_PORT_INDEX 0x03030267
#define OID_FDDI_PORT_MAINT_LS 0x03030268
#define OID_FDDI_PORT_BS_FLAG 0x03030269
#define OID_FDDI_PORT_PC_LS 0x0303026a
#define OID_FDDI_PORT_EB_ERROR_CT 0x0303026b
#define OID_FDDI_PORT_LCT_FAIL_CT 0x0303026c
#define OID_FDDI_PORT_LER_ESTIMATE 0x0303026d
#define OID_FDDI_PORT_LEM_REJECT_CT 0x0303026e
#define OID_FDDI_PORT_LEM_CT 0x0303026f
#define OID_FDDI_PORT_LER_CUTOFF 0x03030270
#define OID_FDDI_PORT_LER_ALARM 0x03030271
#define OID_FDDI_PORT_CONNNECT_STATE 0x03030272
#define OID_FDDI_PORT_PCM_STATE 0x03030273
#define OID_FDDI_PORT_PC_WITHHOLD 0x03030274
#define OID_FDDI_PORT_LER_FLAG 0x03030275
#define OID_FDDI_PORT_HARDWARE_PRESENT 0x03030276
#define OID_FDDI_SMT_STATION_ACTION 0x03030277
#define OID_FDDI_PORT_ACTION 0x03030278
#define OID_FDDI_IF_DESCR 0x03030279
#define OID_FDDI_IF_TYPE 0x0303027a
#define OID_FDDI_IF_MTU 0x0303027b
#define OID_FDDI_IF_SPEED 0x0303027c
#define OID_FDDI_IF_PHYS_ADDRESS 0x0303027d
#define OID_FDDI_IF_ADMIN_STATUS 0x0303027e
#define OID_FDDI_IF_OPER_STATUS 0x0303027f
#define OID_FDDI_IF_LAST_CHANGE 0x03030280
#define OID_FDDI_IF_IN_OCTETS 0x03030281
#define OID_FDDI_IF_IN_UCAST_PKTS 0x03030282
#define OID_FDDI_IF_IN_NUCAST_PKTS 0x03030283
#define OID_FDDI_IF_IN_DISCARDS 0x03030284
#define OID_FDDI_IF_IN_ERRORS 0x03030285
#define OID_FDDI_IF_IN_UNKNOWN_PROTOS 0x03030286
#define OID_FDDI_IF_OUT_OCTETS 0x03030287
#define OID_FDDI_IF_OUT_UCAST_PKTS 0x03030288
#define OID_FDDI_IF_OUT_NUCAST_PKTS 0x03030289
#define OID_FDDI_IF_OUT_DISCARDS 0x0303028a
#define OID_FDDI_IF_OUT_ERRORS 0x0303028b
#define OID_FDDI_IF_OUT_QLEN 0x0303028c
#define OID_FDDI_IF_SPECIFIC 0x0303028d

#define OID_WAN_PERMANENT_ADDRESS 0x04010101
#define OID_WAN_CURRENT_ADDRESS 0x04010102
#define OID_WAN_QUALITY_OF_SERVICE 0x04010103
#define OID_WAN_PROTOCOL_TYPE 0x04010104
#define OID_WAN_MEDIUM_SUBTYPE 0x04010105
#define OID_WAN_HEADER_FORMAT 0x04010106

#define OID_WAN_GET_INFO 0x04010107
#define OID_WAN_SET_LINK_INFO 0x04010108
#define OID_WAN_GET_LINK_INFO 0x04010109
#define OID_WAN_LINE_COUNT 0x0401010a
#define OID_WAN_PROTOCOL_CAPS 0x0401010b

#define OID_WAN_GET_BRIDGE_INFO 0x0401020a
#define OID_WAN_SET_BRIDGE_INFO 0x0401020b
#define OID_WAN_GET_COMP_INFO 0x0401020c
#define OID_WAN_SET_COMP_INFO 0x0401020d
#define OID_WAN_GET_STATS_INFO 0x0401020e

#define OID_WAN_CO_GET_INFO 0x04010180
#define OID_WAN_CO_SET_LINK_INFO 0x04010181
#define OID_WAN_CO_GET_LINK_INFO 0x04010182
#define OID_WAN_CO_GET_COMP_INFO 0x04010280
#define OID_WAN_CO_SET_COMP_INFO 0x04010281
#define OID_WAN_CO_GET_STATS_INFO 0x04010282

#define OID_LTALK_CURRENT_NODE_ID 0x05010102

#define OID_LTALK_IN_BROADCASTS 0x05020101
#define OID_LTALK_IN_LENGTH_ERRORS 0x05020102

#define OID_LTALK_OUT_NO_HANDLERS 0x05020201
#define OID_LTALK_COLLISIONS 0x05020202
#define OID_LTALK_DEFERS 0x05020203
#define OID_LTALK_NO_DATA_ERRORS 0x05020204
#define OID_LTALK_RANDOM_CTS_ERRORS 0x05020205
#define OID_LTALK_FCS_ERRORS 0x05020206

#define OID_ARCNET_PERMANENT_ADDRESS 0x06010101
#define OID_ARCNET_CURRENT_ADDRESS 0x06010102

#define OID_ARCNET_RECONFIGURATIONS 0x06020201

#define OID_TAPI_ACCEPT 0x07030101
#define OID_TAPI_ANSWER 0x07030102
#define OID_TAPI_CLOSE 0x07030103
#define OID_TAPI_CLOSE_CALL 0x07030104
#define OID_TAPI_CONDITIONAL_MEDIA_DETECTION 0x07030105
#define OID_TAPI_CONFIG_DIALOG 0x07030106
#define OID_TAPI_DEV_SPECIFIC 0x07030107
#define OID_TAPI_DIAL 0x07030108
#define OID_TAPI_DROP 0x07030109
#define OID_TAPI_GET_ADDRESS_CAPS 0x0703010a
#define OID_TAPI_GET_ADDRESS_ID 0x0703010b
#define OID_TAPI_GET_ADDRESS_STATUS 0x0703010c
#define OID_TAPI_GET_CALL_ADDRESS_ID 0x0703010d
#define OID_TAPI_GET_CALL_INFO 0x0703010e
#define OID_TAPI_GET_CALL_STATUS 0x0703010f
#define OID_TAPI_GET_DEV_CAPS 0x07030110
#define OID_TAPI_GET_DEV_CONFIG 0x07030111
#define OID_TAPI_GET_EXTENSION_ID 0x07030112
#define OID_TAPI_GET_ID 0x07030113
#define OID_TAPI_GET_LINE_DEV_STATUS 0x07030114
#define OID_TAPI_MAKE_CALL 0x07030115
#define OID_TAPI_NEGOTIATE_EXT_VERSION 0x07030116
#define OID_TAPI_OPEN 0x07030117
#define OID_TAPI_PROVIDER_INITIALIZE 0x07030118
#define OID_TAPI_PROVIDER_SHUTDOWN 0x07030119
#define OID_TAPI_SECURE_CALL 0x0703011a
#define OID_TAPI_SELECT_EXT_VERSION 0x0703011b
#define OID_TAPI_SEND_USER_USER_INFO 0x0703011c
#define OID_TAPI_SET_APP_SPECIFIC 0x0703011d
#define OID_TAPI_SET_CALL_PARAMS 0x0703011e
#define OID_TAPI_SET_DEFAULT_MEDIA_DETECTION 0x0703011f
#define OID_TAPI_SET_DEV_CONFIG 0x07030120
#define OID_TAPI_SET_MEDIA_MODE 0x07030121
#define OID_TAPI_SET_STATUS_MESSAGES 0x07030122
#define OID_TAPI_GATHER_DIGITS 0x07030123
#define OID_TAPI_MONITOR_DIGITS 0x07030124

#define OID_ATM_SUPPORTED_VC_RATES 0x08010101
#define OID_ATM_SUPPORTED_SERVICE_CATEGORY 0x08010102
#define OID_ATM_SUPPORTED_AAL_TYPES 0x08010103
#define OID_ATM_HW_CURRENT_ADDRESS 0x08010104
#define OID_ATM_MAX_ACTIVE_VCS 0x08010105
#define OID_ATM_MAX_ACTIVE_VCI_BITS 0x08010106
#define OID_ATM_MAX_ACTIVE_VPI_BITS 0x08010107
#define OID_ATM_MAX_AAL0_PACKET_SIZE 0x08010108
#define OID_ATM_MAX_AAL1_PACKET_SIZE 0x08010109
#define OID_ATM_MAX_AAL34_PACKET_SIZE 0x0801010a
#define OID_ATM_MAX_AAL5_PACKET_SIZE 0x0801010b

#define OID_ATM_SIGNALING_VPIVCI 0x08010201
#define OID_ATM_ASSIGNED_VPI 0x08010202
#define OID_ATM_ACQUIRE_ACCESS_NET_RESOURCES 0x08010203
#define OID_ATM_RELEASE_ACCESS_NET_RESOURCES 0x08010204
#define OID_ATM_ILMI_VPIVCI 0x08010205
#define OID_ATM_DIGITAL_BROADCAST_VPIVCI 0x08010206
#define OID_ATM_GET_NEAREST_FLOW 0x08010207
#define OID_ATM_ALIGNMENT_REQUIRED 0x08010208
#define OID_ATM_LECS_ADDRESS 0x08010209
#define OID_ATM_SERVICE_ADDRESS 0x0801020a

#define OID_ATM_CALL_PROCEEDING 0x0801020b
#define OID_ATM_CALL_ALERTING 0x0801020c
#define OID_ATM_PARTY_ALERTING 0x0801020d
#define OID_ATM_CALL_NOTIFY 0x0801020e

#define OID_ATM_MY_IP_NM_ADDRESS 0x0801020f

#define OID_ATM_RCV_CELLS_OK 0x08020101
#define OID_ATM_XMIT_CELLS_OK 0x08020102
#define OID_ATM_RCV_CELLS_DROPPED 0x08020103

#define OID_ATM_RCV_INVALID_VPI_VCI 0x08020201
#define OID_ATM_CELLS_HEC_ERROR 0x08020202
#define OID_ATM_RCV_REASSEMBLY_ERROR 0x08020203

#define OID_802_11_BSSID 0x0d010101
#define OID_802_11_SSID 0x0d010102
#define OID_802_11_NETWORK_TYPES_SUPPORTED 0x0d010203
#define OID_802_11_NETWORK_TYPE_IN_USE 0x0d010204
#define OID_802_11_TX_POWER_LEVEL 0x0d010205
#define OID_802_11_RSSI 0x0d010206
#define OID_802_11_RSSI_TRIGGER 0x0d010207
#define OID_802_11_INFRASTRUCTURE_MODE 0x0d010108
#define OID_802_11_FRAGMENTATION_THRESHOLD 0x0d010209
#define OID_802_11_RTS_THRESHOLD 0x0d01020a
#define OID_802_11_NUMBER_OF_ANTENNAS 0x0d01020b
#define OID_802_11_RX_ANTENNA_SELECTED 0x0d01020c
#define OID_802_11_TX_ANTENNA_SELECTED 0x0d01020d
#define OID_802_11_SUPPORTED_RATES 0x0d01020e
#define OID_802_11_DESIRED_RATES 0x0d010210
#define OID_802_11_CONFIGURATION 0x0d010211
#define OID_802_11_STATISTICS 0x0d020212
#define OID_802_11_ADD_WEP 0x0d010113
#define OID_802_11_REMOVE_WEP 0x0d010114
#define OID_802_11_DISASSOCIATE 0x0d010115
#define OID_802_11_POWER_MODE 0x0d010216
#define OID_802_11_BSSID_LIST 0x0d010217
#define OID_802_11_AUTHENTICATION_MODE 0x0d010118
#define OID_802_11_PRIVACY_FILTER 0x0d010119
#define OID_802_11_BSSID_LIST_SCAN 0x0d01011a
#define OID_802_11_WEP_STATUS 0x0d01011b

#define OID_802_11_ENCRYPTION_STATUS OID_802_11_WEP_STATUS
#define OID_802_11_RELOAD_DEFAULTS 0x0d01011c

#define OID_802_11_ADD_KEY 0x0d01011d
#define OID_802_11_REMOVE_KEY 0x0d01011e
#define OID_802_11_ASSOCIATION_INFORMATION 0x0d01011f
#define OID_802_11_TEST 0x0d010120
#define OID_802_11_MEDIA_STREAM_MODE 0x0d010121
#define OID_802_11_CAPABILITY 0x0d010122
#define OID_802_11_PMKID 0x0d010123
#define OID_802_11_NON_BCAST_SSID_LIST 0x0d010124
#define OID_802_11_RADIO_STATUS 0x0d010125

#define NDIS_ETH_TYPE_IPV4 0x0800
#define NDIS_ETH_TYPE_ARP 0x0806
#define NDIS_ETH_TYPE_IPV6 0x86dd
#define NDIS_ETH_TYPE_802_1X 0x888e
#define NDIS_ETH_TYPE_802_1Q 0x8100
#define NDIS_ETH_TYPE_SLOW_PROTOCOL 0x8809

#define NDIS_802_11_LENGTH_SSID 32
#define NDIS_802_11_LENGTH_RATES 8
#define NDIS_802_11_LENGTH_RATES_EX 16

#define NDIS_802_11_AUTH_REQUEST_REAUTH 0x01
#define NDIS_802_11_AUTH_REQUEST_KEYUPDATE 0x02
#define NDIS_802_11_AUTH_REQUEST_PAIRWISE_ERROR 0x06
#define NDIS_802_11_AUTH_REQUEST_GROUP_ERROR 0x0e
#define NDIS_802_11_AUTH_REQUEST_AUTH_FIELDS 0x0f

#define NDIS_802_11_PMKID_CANDIDATE_PREAUTH_ENABLED 0x01

#define NDIS_802_11_AI_REQFI_CAPABILITIES 1
#define NDIS_802_11_AI_REQFI_LISTENINTERVAL 2
#define NDIS_802_11_AI_REQFI_CURRENTAPADDRESS 4

#define NDIS_802_11_AI_RESFI_CAPABILITIES 1
#define NDIS_802_11_AI_RESFI_STATUSCODE 2
#define NDIS_802_11_AI_RESFI_ASSOCIATIONID 4

  typedef enum _NDIS_802_11_STATUS_TYPE {
    Ndis802_11StatusType_Authentication,
    Ndis802_11StatusType_MediaStreamMode,
    Ndis802_11StatusType_PMKID_CandidateList,
    Ndis802_11StatusTypeMax
  } NDIS_802_11_STATUS_TYPE, *PNDIS_802_11_STATUS_TYPE;

  typedef UCHAR NDIS_802_11_MAC_ADDRESS[6];

  typedef struct _NDIS_802_11_STATUS_INDICATION {
    NDIS_802_11_STATUS_TYPE StatusType;
  } NDIS_802_11_STATUS_INDICATION, *PNDIS_802_11_STATUS_INDICATION;

  typedef struct _NDIS_802_11_AUTHENTICATION_REQUEST {
    ULONG Length;
    NDIS_802_11_MAC_ADDRESS Bssid;
    ULONG Flags;
  } NDIS_802_11_AUTHENTICATION_REQUEST, *PNDIS_802_11_AUTHENTICATION_REQUEST;

  typedef struct _PMKID_CANDIDATE {
    NDIS_802_11_MAC_ADDRESS BSSID;
    ULONG Flags;
  } PMKID_CANDIDATE, *PPMKID_CANDIDATE;

  typedef struct _NDIS_802_11_PMKID_CANDIDATE_LIST {
    ULONG Version;
    ULONG NumCandidates;
    PMKID_CANDIDATE CandidateList[1];
  } NDIS_802_11_PMKID_CANDIDATE_LIST, *PNDIS_802_11_PMKID_CANDIDATE_LIST;

  typedef enum _NDIS_802_11_NETWORK_TYPE {
    Ndis802_11FH,
    Ndis802_11DS,
    Ndis802_11OFDM5,
    Ndis802_11OFDM24,
    Ndis802_11Automode,
    Ndis802_11NetworkTypeMax
  } NDIS_802_11_NETWORK_TYPE, *PNDIS_802_11_NETWORK_TYPE;

  typedef struct _NDIS_802_11_NETWORK_TYPE_LIST {
    ULONG NumberOfItems;
    NDIS_802_11_NETWORK_TYPE NetworkType [1];
  } NDIS_802_11_NETWORK_TYPE_LIST, *PNDIS_802_11_NETWORK_TYPE_LIST;

  typedef enum _NDIS_802_11_POWER_MODE {
    Ndis802_11PowerModeCAM,
    Ndis802_11PowerModeMAX_PSP,
    Ndis802_11PowerModeFast_PSP,
    Ndis802_11PowerModeMax
  } NDIS_802_11_POWER_MODE, *PNDIS_802_11_POWER_MODE;

  typedef ULONG NDIS_802_11_TX_POWER_LEVEL;
  typedef LONG NDIS_802_11_RSSI;

  typedef struct _NDIS_802_11_CONFIGURATION_FH {
    ULONG Length;
    ULONG HopPattern;
    ULONG HopSet;
    ULONG DwellTime;
  } NDIS_802_11_CONFIGURATION_FH, *PNDIS_802_11_CONFIGURATION_FH;

  typedef struct _NDIS_802_11_CONFIGURATION {
    ULONG Length;
    ULONG BeaconPeriod;
    ULONG ATIMWindow;
    ULONG DSConfig;
    NDIS_802_11_CONFIGURATION_FH FHConfig;
  } NDIS_802_11_CONFIGURATION, *PNDIS_802_11_CONFIGURATION;

  typedef struct _NDIS_802_11_STATISTICS {
    ULONG Length;
    LARGE_INTEGER TransmittedFragmentCount;
    LARGE_INTEGER MulticastTransmittedFrameCount;
    LARGE_INTEGER FailedCount;
    LARGE_INTEGER RetryCount;
    LARGE_INTEGER MultipleRetryCount;
    LARGE_INTEGER RTSSuccessCount;
    LARGE_INTEGER RTSFailureCount;
    LARGE_INTEGER ACKFailureCount;
    LARGE_INTEGER FrameDuplicateCount;
    LARGE_INTEGER ReceivedFragmentCount;
    LARGE_INTEGER MulticastReceivedFrameCount;
    LARGE_INTEGER FCSErrorCount;
    LARGE_INTEGER TKIPLocalMICFailures;
    LARGE_INTEGER TKIPICVErrorCount;
    LARGE_INTEGER TKIPCounterMeasuresInvoked;
    LARGE_INTEGER TKIPReplays;
    LARGE_INTEGER CCMPFormatErrors;
    LARGE_INTEGER CCMPReplays;
    LARGE_INTEGER CCMPDecryptErrors;
    LARGE_INTEGER FourWayHandshakeFailures;
    LARGE_INTEGER WEPUndecryptableCount;
    LARGE_INTEGER WEPICVErrorCount;
    LARGE_INTEGER DecryptSuccessCount;
    LARGE_INTEGER DecryptFailureCount;
  } NDIS_802_11_STATISTICS, *PNDIS_802_11_STATISTICS;

  typedef ULONG NDIS_802_11_KEY_INDEX;
  typedef ULONGLONG NDIS_802_11_KEY_RSC;

  typedef struct _NDIS_802_11_KEY {
    ULONG Length;
    ULONG KeyIndex;
    ULONG KeyLength;
    NDIS_802_11_MAC_ADDRESS BSSID;
    NDIS_802_11_KEY_RSC KeyRSC;
    UCHAR KeyMaterial[1];
  } NDIS_802_11_KEY, *PNDIS_802_11_KEY;

  typedef struct _NDIS_802_11_REMOVE_KEY {
    ULONG Length;
    ULONG KeyIndex;
    NDIS_802_11_MAC_ADDRESS BSSID;
  } NDIS_802_11_REMOVE_KEY, *PNDIS_802_11_REMOVE_KEY;
  typedef struct _NDIS_802_11_WEP {
    ULONG Length;
    ULONG KeyIndex;
    ULONG KeyLength;
    UCHAR KeyMaterial[1];
  } NDIS_802_11_WEP, *PNDIS_802_11_WEP;

  typedef enum _NDIS_802_11_NETWORK_INFRASTRUCTURE {
    Ndis802_11IBSS,
    Ndis802_11Infrastructure,
    Ndis802_11AutoUnknown,
    Ndis802_11InfrastructureMax
  } NDIS_802_11_NETWORK_INFRASTRUCTURE, *PNDIS_802_11_NETWORK_INFRASTRUCTURE;

  typedef enum _NDIS_802_11_AUTHENTICATION_MODE {
    Ndis802_11AuthModeOpen,
    Ndis802_11AuthModeShared,
    Ndis802_11AuthModeAutoSwitch,
    Ndis802_11AuthModeWPA,
    Ndis802_11AuthModeWPAPSK,
    Ndis802_11AuthModeWPANone,
    Ndis802_11AuthModeWPA2,
    Ndis802_11AuthModeWPA2PSK,
    Ndis802_11AuthModeMax
  } NDIS_802_11_AUTHENTICATION_MODE, *PNDIS_802_11_AUTHENTICATION_MODE;

  typedef UCHAR NDIS_802_11_RATES[NDIS_802_11_LENGTH_RATES];
  typedef UCHAR NDIS_802_11_RATES_EX[NDIS_802_11_LENGTH_RATES_EX];

  typedef struct _NDIS_802_11_SSID {
    ULONG SsidLength;
    UCHAR Ssid[NDIS_802_11_LENGTH_SSID];
  } NDIS_802_11_SSID, *PNDIS_802_11_SSID;

  typedef struct _NDIS_WLAN_BSSID {
    ULONG Length;
    NDIS_802_11_MAC_ADDRESS MacAddress;
    UCHAR Reserved[2];
    NDIS_802_11_SSID Ssid;
    ULONG Privacy;
    NDIS_802_11_RSSI Rssi;
    NDIS_802_11_NETWORK_TYPE NetworkTypeInUse;
    NDIS_802_11_CONFIGURATION Configuration;
    NDIS_802_11_NETWORK_INFRASTRUCTURE InfrastructureMode;
    NDIS_802_11_RATES SupportedRates;
  } NDIS_WLAN_BSSID, *PNDIS_WLAN_BSSID;

  typedef struct _NDIS_802_11_BSSID_LIST {
    ULONG NumberOfItems;
    NDIS_WLAN_BSSID Bssid[1];
  } NDIS_802_11_BSSID_LIST, *PNDIS_802_11_BSSID_LIST;

  typedef struct _NDIS_WLAN_BSSID_EX {
    ULONG Length;
    NDIS_802_11_MAC_ADDRESS MacAddress;
    UCHAR Reserved[2];
    NDIS_802_11_SSID Ssid;
    ULONG Privacy;
    NDIS_802_11_RSSI Rssi;
    NDIS_802_11_NETWORK_TYPE NetworkTypeInUse;
    NDIS_802_11_CONFIGURATION Configuration;
    NDIS_802_11_NETWORK_INFRASTRUCTURE InfrastructureMode;
    NDIS_802_11_RATES_EX SupportedRates;
    ULONG IELength;
    UCHAR IEs[1];
  } NDIS_WLAN_BSSID_EX, *PNDIS_WLAN_BSSID_EX;

  typedef struct _NDIS_802_11_BSSID_LIST_EX {
    ULONG NumberOfItems;
    NDIS_WLAN_BSSID_EX Bssid[1];
  } NDIS_802_11_BSSID_LIST_EX, *PNDIS_802_11_BSSID_LIST_EX;

  typedef struct _NDIS_802_11_FIXED_IEs {
    UCHAR Timestamp[8];
    USHORT BeaconInterval;
    USHORT Capabilities;
  } NDIS_802_11_FIXED_IEs, *PNDIS_802_11_FIXED_IEs;

  typedef struct _NDIS_802_11_VARIABLE_IEs {
    UCHAR ElementID;
    UCHAR Length;
    UCHAR data[1];
  } NDIS_802_11_VARIABLE_IEs, *PNDIS_802_11_VARIABLE_IEs;

  typedef ULONG NDIS_802_11_FRAGMENTATION_THRESHOLD;
  typedef ULONG NDIS_802_11_RTS_THRESHOLD;
  typedef ULONG NDIS_802_11_ANTENNA;

  typedef enum _NDIS_802_11_PRIVACY_FILTER {
    Ndis802_11PrivFilterAcceptAll,
    Ndis802_11PrivFilter8021xWEP
  } NDIS_802_11_PRIVACY_FILTER, *PNDIS_802_11_PRIVACY_FILTER;

  typedef enum _NDIS_802_11_WEP_STATUS {
    Ndis802_11WEPEnabled,
    Ndis802_11Encryption1Enabled = Ndis802_11WEPEnabled,
    Ndis802_11WEPDisabled,
    Ndis802_11EncryptionDisabled = Ndis802_11WEPDisabled,
    Ndis802_11WEPKeyAbsent,
    Ndis802_11Encryption1KeyAbsent = Ndis802_11WEPKeyAbsent,
    Ndis802_11WEPNotSupported,
    Ndis802_11EncryptionNotSupported = Ndis802_11WEPNotSupported,
    Ndis802_11Encryption2Enabled,
    Ndis802_11Encryption2KeyAbsent,
    Ndis802_11Encryption3Enabled,
    Ndis802_11Encryption3KeyAbsent
  } NDIS_802_11_WEP_STATUS, *PNDIS_802_11_WEP_STATUS, NDIS_802_11_ENCRYPTION_STATUS, *PNDIS_802_11_ENCRYPTION_STATUS;

  typedef enum _NDIS_802_11_RELOAD_DEFAULTS {
    Ndis802_11ReloadWEPKeys
  } NDIS_802_11_RELOAD_DEFAULTS, *PNDIS_802_11_RELOAD_DEFAULTS;

  typedef struct _NDIS_802_11_AI_REQFI {
    USHORT Capabilities;
    USHORT ListenInterval;
    NDIS_802_11_MAC_ADDRESS CurrentAPAddress;
  } NDIS_802_11_AI_REQFI, *PNDIS_802_11_AI_REQFI;

  typedef struct _NDIS_802_11_AI_RESFI {
    USHORT Capabilities;
    USHORT StatusCode;
    USHORT AssociationId;
  } NDIS_802_11_AI_RESFI, *PNDIS_802_11_AI_RESFI;

  typedef struct _NDIS_802_11_ASSOCIATION_INFORMATION {
    ULONG Length;
    USHORT AvailableRequestFixedIEs;
    NDIS_802_11_AI_REQFI RequestFixedIEs;
    ULONG RequestIELength;
    ULONG OffsetRequestIEs;
    USHORT AvailableResponseFixedIEs;
    NDIS_802_11_AI_RESFI ResponseFixedIEs;
    ULONG ResponseIELength;
    ULONG OffsetResponseIEs;
  } NDIS_802_11_ASSOCIATION_INFORMATION, *PNDIS_802_11_ASSOCIATION_INFORMATION;

  typedef struct _NDIS_802_11_AUTHENTICATION_EVENT {
    NDIS_802_11_STATUS_INDICATION Status;
    NDIS_802_11_AUTHENTICATION_REQUEST Request[1];
  } NDIS_802_11_AUTHENTICATION_EVENT, *PNDIS_802_11_AUTHENTICATION_EVENT;

  typedef struct _NDIS_802_11_TEST {
    ULONG Length;
    ULONG Type;
    __C89_NAMELESS union {
      NDIS_802_11_AUTHENTICATION_EVENT AuthenticationEvent;
      NDIS_802_11_RSSI RssiTrigger;
    };
  } NDIS_802_11_TEST, *PNDIS_802_11_TEST;

  typedef enum _NDIS_802_11_MEDIA_STREAM_MODE {
    Ndis802_11MediaStreamOff,
    Ndis802_11MediaStreamOn
  } NDIS_802_11_MEDIA_STREAM_MODE, *PNDIS_802_11_MEDIA_STREAM_MODE;

  typedef UCHAR NDIS_802_11_PMKID_VALUE[16];

  typedef struct _BSSID_INFO {
    NDIS_802_11_MAC_ADDRESS BSSID;
    NDIS_802_11_PMKID_VALUE PMKID;
  } BSSID_INFO, *PBSSID_INFO;

  typedef struct _NDIS_802_11_PMKID {
    ULONG Length;
    ULONG BSSIDInfoCount;
    BSSID_INFO BSSIDInfo[1];
  } NDIS_802_11_PMKID, *PNDIS_802_11_PMKID;

  typedef struct _NDIS_802_11_AUTHENTICATION_ENCRYPTION {
    NDIS_802_11_AUTHENTICATION_MODE AuthModeSupported;
    NDIS_802_11_ENCRYPTION_STATUS EncryptStatusSupported;
  } NDIS_802_11_AUTHENTICATION_ENCRYPTION, *PNDIS_802_11_AUTHENTICATION_ENCRYPTION;

  typedef struct _NDIS_802_11_CAPABILITY {
    ULONG Length;
    ULONG Version;
    ULONG NoOfPMKIDs;
    ULONG NoOfAuthEncryptPairsSupported;
    NDIS_802_11_AUTHENTICATION_ENCRYPTION AuthenticationEncryptionSupported[1];
  } NDIS_802_11_CAPABILITY, *PNDIS_802_11_CAPABILITY;

  typedef struct _NDIS_802_11_NON_BCAST_SSID_LIST {
    ULONG NumberOfItems;
    NDIS_802_11_SSID Non_Bcast_Ssid[1];
  } NDIS_802_11_NON_BCAST_SSID_LIST, *PNDIS_802_11_NON_BCAST_SSID_LIST;

  typedef enum _NDIS_802_11_RADIO_STATUS {
    Ndis802_11RadioStatusOn,
    Ndis802_11RadioStatusHardwareOff,
    Ndis802_11RadioStatusSoftwareOff,
    Ndis802_11RadioStatusHardwareSoftwareOff,
    Ndis802_11RadioStatusMax
  } NDIS_802_11_RADIO_STATUS, *PNDIS_802_11_RADIO_STATUS;

#define OID_IRDA_RECEIVING 0x0a010100
#define OID_IRDA_TURNAROUND_TIME 0x0a010101
#define OID_IRDA_SUPPORTED_SPEEDS 0x0a010102
#define OID_IRDA_LINK_SPEED 0x0a010103
#define OID_IRDA_MEDIA_BUSY 0x0a010104

#define OID_IRDA_EXTRA_RCV_BOFS 0x0a010200
#define OID_IRDA_RATE_SNIFF 0x0a010201
#define OID_IRDA_UNICAST_LIST 0x0a010202
#define OID_IRDA_MAX_UNICAST_LIST_SIZE 0x0a010203
#define OID_IRDA_MAX_RECEIVE_WINDOW_SIZE 0x0a010204
#define OID_IRDA_MAX_SEND_WINDOW_SIZE 0x0a010205
#define OID_IRDA_RESERVED1 0x0a01020a
#define OID_IRDA_RESERVED2 0x0a01020f

#define OID_1394_LOCAL_NODE_INFO 0x0c010101
#define OID_1394_VC_INFO 0x0c010102

#define OID_CO_ADD_PVC 0xfe000001
#define OID_CO_DELETE_PVC 0xfe000002
#define OID_CO_GET_CALL_INFORMATION 0xfe000003
#define OID_CO_ADD_ADDRESS 0xfe000004
#define OID_CO_DELETE_ADDRESS 0xfe000005
#define OID_CO_GET_ADDRESSES 0xfe000006
#define OID_CO_ADDRESS_CHANGE 0xfe000007
#define OID_CO_SIGNALING_ENABLED 0xfe000008
#define OID_CO_SIGNALING_DISABLED 0xfe000009
#define OID_CO_AF_CLOSE 0xfe00000a

#define OID_CO_TAPI_CM_CAPS 0xfe001001
#define OID_CO_TAPI_LINE_CAPS 0xfe001002
#define OID_CO_TAPI_ADDRESS_CAPS 0xfe001003
#define OID_CO_TAPI_TRANSLATE_TAPI_CALLPARAMS 0xfe001004
#define OID_CO_TAPI_TRANSLATE_NDIS_CALLPARAMS 0xfe001005
#define OID_CO_TAPI_TRANSLATE_TAPI_SAP 0xfe001006
#define OID_CO_TAPI_GET_CALL_DIAGNOSTICS 0xfe001007
#define OID_CO_TAPI_REPORT_DIGITS 0xfe001008
#define OID_CO_TAPI_DONT_REPORT_DIGITS 0xfe001009

#define OID_PNP_CAPABILITIES 0xfd010100
#define OID_PNP_SET_POWER 0xfd010101
#define OID_PNP_QUERY_POWER 0xfd010102
#define OID_PNP_ADD_WAKE_UP_PATTERN 0xfd010103
#define OID_PNP_REMOVE_WAKE_UP_PATTERN 0xfd010104
#define OID_PNP_WAKE_UP_PATTERN_LIST 0xfd010105
#define OID_PNP_ENABLE_WAKE_UP 0xfd010106

#define OID_PNP_WAKE_UP_OK 0xfd020200
#define OID_PNP_WAKE_UP_ERROR 0xfd020201

#if (NTDDI_VERSION >= 0x06010000 || NDIS_SUPPORT_NDIS620)
#define OID_PM_CURRENT_CAPABILITIES 0xfd010107
#define OID_PM_HARDWARE_CAPABILITIES 0xfd010108
#define OID_PM_PARAMETERS 0xfd010109
#define OID_PM_ADD_WOL_PATTERN 0xfd01010a
#define OID_PM_REMOVE_WOL_PATTERN 0xfd01010b
#define OID_PM_WOL_PATTERN_LIST 0xfd01010c
#define OID_PM_ADD_PROTOCOL_OFFLOAD 0xfd01010d
#define OID_PM_GET_PROTOCOL_OFFLOAD 0xfd01010e
#define OID_PM_REMOVE_PROTOCOL_OFFLOAD 0xfd01010f
#define OID_PM_PROTOCOL_OFFLOAD_LIST 0xfd010110
#define OID_PM_RESERVED_1 0xfd010111

#define OID_RECEIVE_FILTER_HARDWARE_CAPABILITIES 0x00010221
#define OID_RECEIVE_FILTER_GLOBAL_PARAMETERS 0x00010222
#define OID_RECEIVE_FILTER_ALLOCATE_QUEUE 0x00010223
#define OID_RECEIVE_FILTER_FREE_QUEUE 0x00010224
#define OID_RECEIVE_FILTER_ENUM_QUEUES 0x00010225
#define OID_RECEIVE_FILTER_QUEUE_PARAMETERS 0x00010226
#define OID_RECEIVE_FILTER_SET_FILTER 0x00010227
#define OID_RECEIVE_FILTER_CLEAR_FILTER 0x00010228
#define OID_RECEIVE_FILTER_ENUM_FILTERS 0x00010229
#define OID_RECEIVE_FILTER_PARAMETERS 0x0001022a
#define OID_RECEIVE_FILTER_QUEUE_ALLOCATION_COMPLETE 0x0001022b
#define OID_RECEIVE_FILTER_CURRENT_CAPABILITIES 0x0001022d
#define OID_NIC_SWITCH_HARDWARE_CAPABILITIES 0x0001022e
#define OID_NIC_SWITCH_CURRENT_CAPABILITIES 0x0001022f
#if NDIS_SUPPORT_NDIS630
#define OID_RECEIVE_FILTER_MOVE_FILTER 0x00010230
#endif
#define OID_VLAN_RESERVED1 0x00010231
#define OID_VLAN_RESERVED2 0x00010232
#define OID_VLAN_RESERVED3 0x00010233
#define OID_VLAN_RESERVED4 0x00010234
#if NDIS_SUPPORT_NDIS630
#define OID_PACKET_COALESCING_FILTER_MATCH_COUNT 0x00010235
#endif
#endif

#if NTDDI_VERSION >= 0x06020000 || NDIS_SUPPORT_NDIS630
#define OID_NIC_SWITCH_CREATE_SWITCH 0x00010237
#define OID_NIC_SWITCH_PARAMETERS 0x00010238
#define OID_NIC_SWITCH_DELETE_SWITCH 0x00010239
#define OID_NIC_SWITCH_ENUM_SWITCHES 0x00010240
#define OID_NIC_SWITCH_CREATE_VPORT 0x00010241
#define OID_NIC_SWITCH_VPORT_PARAMETERS 0x00010242
#define OID_NIC_SWITCH_ENUM_VPORTS 0x00010243
#define OID_NIC_SWITCH_DELETE_VPORT 0x00010244
#define OID_NIC_SWITCH_ALLOCATE_VF 0x00010245
#define OID_NIC_SWITCH_FREE_VF 0x00010246
#define OID_NIC_SWITCH_VF_PARAMETERS 0x00010247
#define OID_NIC_SWITCH_ENUM_VFS 0x00010248

#define OID_SRIOV_HARDWARE_CAPABILITIES 0x00010249
#define OID_SRIOV_CURRENT_CAPABILITIES 0x00010250
#define OID_SRIOV_READ_VF_CONFIG_SPACE 0x00010251
#define OID_SRIOV_WRITE_VF_CONFIG_SPACE 0x00010252
#define OID_SRIOV_READ_VF_CONFIG_BLOCK 0x00010253
#define OID_SRIOV_WRITE_VF_CONFIG_BLOCK 0x00010254
#define OID_SRIOV_RESET_VF 0x00010255
#define OID_SRIOV_SET_VF_POWER_STATE 0x00010256
#define OID_SRIOV_VF_VENDOR_DEVICE_ID 0x00010257
#define OID_SRIOV_PROBED_BARS 0x00010258
#define OID_SRIOV_BAR_RESOURCES 0x00010259
#define OID_SRIOV_PF_LUID 0x00010260

#define OID_SRIOV_CONFIG_STATE 0x00010261
#define OID_SRIOV_VF_SERIAL_NUMBER 0x00010262
#define OID_SRIOV_VF_INVALIDATE_CONFIG_BLOCK 0x00010269

#define OID_SWITCH_PROPERTY_ADD 0x00010263
#define OID_SWITCH_PROPERTY_UPDATE 0x00010264
#define OID_SWITCH_PROPERTY_DELETE 0x00010265
#define OID_SWITCH_PROPERTY_ENUM 0x00010266
#define OID_SWITCH_FEATURE_STATUS_QUERY 0x00010267

#define OID_SWITCH_NIC_REQUEST 0x00010270
#define OID_SWITCH_PORT_PROPERTY_ADD 0x00010271
#define OID_SWITCH_PORT_PROPERTY_UPDATE 0x00010272
#define OID_SWITCH_PORT_PROPERTY_DELETE 0x00010273
#define OID_SWITCH_PORT_PROPERTY_ENUM 0x00010274
#define OID_SWITCH_PARAMETERS 0x00010275
#define OID_SWITCH_PORT_ARRAY 0x00010276
#define OID_SWITCH_NIC_ARRAY 0x00010277
#define OID_SWITCH_PORT_CREATE 0x00010278
#define OID_SWITCH_PORT_DELETE 0x00010279
#define OID_SWITCH_NIC_CREATE 0x0001027a
#define OID_SWITCH_NIC_CONNECT 0x0001027b
#define OID_SWITCH_NIC_DISCONNECT 0x0001027c
#define OID_SWITCH_NIC_DELETE 0x0001027d
#define OID_SWITCH_PORT_FEATURE_STATUS_QUERY 0x0001027e
#define OID_SWITCH_PORT_TEARDOWN 0x0001027f
#define OID_SWITCH_NIC_SAVE 0x00010290
#define OID_SWITCH_NIC_SAVE_COMPLETE 0x00010291
#define OID_SWITCH_NIC_RESTORE 0x00010292
#define OID_SWITCH_NIC_RESTORE_COMPLETE 0x00010293
#define OID_SWITCH_NIC_UPDATED 0x00010294
#define OID_SWITCH_PORT_UPDATED 0x00010295
#endif

#define NDIS_PNP_WAKE_UP_MAGIC_PACKET 0x00000001
#define NDIS_PNP_WAKE_UP_PATTERN_MATCH 0x00000002
#define NDIS_PNP_WAKE_UP_LINK_CHANGE 0x00000004

#define OID_TCP_TASK_OFFLOAD 0xfc010201
#define OID_TCP_TASK_IPSEC_ADD_SA 0xfc010202
#define OID_TCP_TASK_IPSEC_DELETE_SA 0xfc010203
#define OID_TCP_SAN_SUPPORT 0xfc010204
#define OID_TCP_TASK_IPSEC_ADD_UDPESP_SA 0xfc010205
#define OID_TCP_TASK_IPSEC_DELETE_UDPESP_SA 0xfc010206
#define OID_TCP4_OFFLOAD_STATS 0xfc010207
#define OID_TCP6_OFFLOAD_STATS 0xfc010208
#define OID_IP4_OFFLOAD_STATS 0xfc010209
#define OID_IP6_OFFLOAD_STATS 0xfc01020a

#define OID_TCP_OFFLOAD_CURRENT_CONFIG 0xfc01020b
#define OID_TCP_OFFLOAD_PARAMETERS 0xfc01020c
#define OID_TCP_OFFLOAD_HARDWARE_CAPABILITIES 0xfc01020d
#define OID_TCP_CONNECTION_OFFLOAD_CURRENT_CONFIG 0xfc01020e
#define OID_TCP_CONNECTION_OFFLOAD_HARDWARE_CAPABILITIES 0xfc01020f
#define OID_OFFLOAD_ENCAPSULATION 0x0101010a

#if NDIS_SUPPORT_NDIS61
#define OID_TCP_TASK_IPSEC_OFFLOAD_V2_ADD_SA 0xfc030202
#define OID_TCP_TASK_IPSEC_OFFLOAD_V2_DELETE_SA 0xfc030203
#define OID_TCP_TASK_IPSEC_OFFLOAD_V2_UPDATE_SA 0xfc030204
#endif
#if NDIS_SUPPORT_NDIS630
#define OID_TCP_TASK_IPSEC_OFFLOAD_V2_ADD_SA_EX 0xfc030205
#endif

#define OID_FFP_SUPPORT 0xfc010210
#define OID_FFP_FLUSH 0xfc010211
#define OID_FFP_CONTROL 0xfc010212
#define OID_FFP_PARAMS 0xfc010213
#define OID_FFP_DATA 0xfc010214

#define OID_FFP_DRIVER_STATS 0xfc020210
#define OID_FFP_ADAPTER_STATS 0xfc020211

#define OID_TCP_CONNECTION_OFFLOAD_PARAMETERS 0xfc030201

#if NTDDI_VERSION >= 0x06010000 || NDIS_SUPPORT_NDIS620
#define OID_TUNNEL_INTERFACE_SET_OID 0x0f010106
#define OID_TUNNEL_INTERFACE_RELEASE_OID 0x0f010107
#endif

#define OID_QOS_RESERVED1 0xfb010100
#define OID_QOS_RESERVED2 0xfb010101
#define OID_QOS_RESERVED3 0xfb010102
#define OID_QOS_RESERVED4 0xfb010103
#define OID_QOS_RESERVED5 0xfb010104
#define OID_QOS_RESERVED6 0xfb010105
#define OID_QOS_RESERVED7 0xfb010106
#define OID_QOS_RESERVED8 0xfb010107
#define OID_QOS_RESERVED9 0xfb010108
#define OID_QOS_RESERVED10 0xfb010109
#define OID_QOS_RESERVED11 0xfb01010a
#define OID_QOS_RESERVED12 0xfb01010b
#define OID_QOS_RESERVED13 0xfb01010c
#define OID_QOS_RESERVED14 0xfb01010d
#define OID_QOS_RESERVED15 0xfb01010e
#define OID_QOS_RESERVED16 0xfb01010f
#define OID_QOS_RESERVED17 0xfb010110
#define OID_QOS_RESERVED18 0xfb010111
#define OID_QOS_RESERVED19 0xfb010112
#define OID_QOS_RESERVED20 0xfb010113

#define OFFLOAD_MAX_SAS 3

#define OFFLOAD_INBOUND_SA 0x0001
#define OFFLOAD_OUTBOUND_SA 0x0002

  typedef struct NDIS_CO_DEVICE_PROFILE {
    NDIS_VAR_DATA_DESC DeviceDescription;
    NDIS_VAR_DATA_DESC DevSpecificInfo;
    ULONG ulTAPISupplementaryPassThru;
    ULONG ulAddressModes;
    ULONG ulNumAddresses;
    ULONG ulBearerModes;
    ULONG ulMaxTxRate;
    ULONG ulMinTxRate;
    ULONG ulMaxRxRate;
    ULONG ulMinRxRate;
    ULONG ulMediaModes;
    ULONG ulGenerateToneModes;
    ULONG ulGenerateToneMaxNumFreq;
    ULONG ulGenerateDigitModes;
    ULONG ulMonitorToneMaxNumFreq;
    ULONG ulMonitorToneMaxNumEntries;
    ULONG ulMonitorDigitModes;
    ULONG ulGatherDigitsMinTimeout;
    ULONG ulGatherDigitsMaxTimeout;
    ULONG ulDevCapFlags;
    ULONG ulMaxNumActiveCalls;
    ULONG ulAnswerMode;
    ULONG ulUUIAcceptSize;
    ULONG ulUUIAnswerSize;
    ULONG ulUUIMakeCallSize;
    ULONG ulUUIDropSize;
    ULONG ulUUISendUserUserInfoSize;
    ULONG ulUUICallInfoSize;
  } NDIS_CO_DEVICE_PROFILE, *PNDIS_CO_DEVICE_PROFILE;

#ifndef IP_EXPORT_INCLUDED
  typedef ULONG IPAddr, IPMask;
#endif
  typedef ULONG SPI_TYPE;

  typedef enum _OFFLOAD_OPERATION_E {
    AUTHENTICATE = 1,
    ENCRYPT
  } OFFLOAD_OPERATION_E;

  typedef struct _OFFLOAD_ALGO_INFO {
    ULONG algoIdentifier;
    ULONG algoKeylen;
    ULONG algoRounds;
  } OFFLOAD_ALGO_INFO, *POFFLOAD_ALGO_INFO;

  typedef enum _OFFLOAD_CONF_ALGO {
    OFFLOAD_IPSEC_CONF_NONE,
    OFFLOAD_IPSEC_CONF_DES,
    OFFLOAD_IPSEC_CONF_RESERVED,
    OFFLOAD_IPSEC_CONF_3_DES,
    OFFLOAD_IPSEC_CONF_MAX
  } OFFLOAD_CONF_ALGO;

  typedef enum _OFFLOAD_INTEGRITY_ALGO {
    OFFLOAD_IPSEC_INTEGRITY_NONE,
    OFFLOAD_IPSEC_INTEGRITY_MD5,
    OFFLOAD_IPSEC_INTEGRITY_SHA,
    OFFLOAD_IPSEC_INTEGRITY_MAX
  } OFFLOAD_INTEGRITY_ALGO;

  typedef struct _OFFLOAD_SECURITY_ASSOCIATION {
    OFFLOAD_OPERATION_E Operation;
    SPI_TYPE SPI;
    OFFLOAD_ALGO_INFO IntegrityAlgo;
    OFFLOAD_ALGO_INFO ConfAlgo;
    OFFLOAD_ALGO_INFO Reserved;
  } OFFLOAD_SECURITY_ASSOCIATION, *POFFLOAD_SECURITY_ASSOCIATION;

  typedef struct _OFFLOAD_IPSEC_ADD_SA {
    IPAddr SrcAddr;
    IPMask SrcMask;
    IPAddr DestAddr;
    IPMask DestMask;
    ULONG Protocol;
    USHORT SrcPort;
    USHORT DestPort;
    IPAddr SrcTunnelAddr;
    IPAddr DestTunnelAddr;
    USHORT Flags;
    SHORT NumSAs;
    OFFLOAD_SECURITY_ASSOCIATION SecAssoc[OFFLOAD_MAX_SAS];
    HANDLE OffloadHandle;
    ULONG KeyLen;
    UCHAR KeyMat[1];
  } OFFLOAD_IPSEC_ADD_SA, *POFFLOAD_IPSEC_ADD_SA;

  typedef struct _OFFLOAD_IPSEC_DELETE_SA {
    HANDLE OffloadHandle;
  } OFFLOAD_IPSEC_DELETE_SA, *POFFLOAD_IPSEC_DELETE_SA;

  typedef enum _UDP_ENCAP_TYPE {
    OFFLOAD_IPSEC_UDPESP_ENCAPTYPE_IKE,
    OFFLOAD_IPSEC_UDPESP_ENCAPTYPE_OTHER
  } UDP_ENCAP_TYPE, *PUDP_ENCAP_TYPE;

  typedef struct _OFFLOAD_IPSEC_UDPESP_ENCAPTYPE_ENTRY {
    UDP_ENCAP_TYPE UdpEncapType;
    USHORT DstEncapPort;
  } OFFLOAD_IPSEC_UDPESP_ENCAPTYPE_ENTRY, *POFFLOAD_IPSEC_UDPESP_ENCAPTYPE_ENTRY;

  typedef struct _OFFLOAD_IPSEC_ADD_UDPESP_SA {
    IPAddr SrcAddr;
    IPMask SrcMask;
    IPAddr DstAddr;
    IPMask DstMask;
    ULONG Protocol;
    USHORT SrcPort;
    USHORT DstPort;
    IPAddr SrcTunnelAddr;
    IPAddr DstTunnelAddr;
    USHORT Flags;
    SHORT NumSAs;
    OFFLOAD_SECURITY_ASSOCIATION SecAssoc[OFFLOAD_MAX_SAS];
    HANDLE OffloadHandle;
    OFFLOAD_IPSEC_UDPESP_ENCAPTYPE_ENTRY EncapTypeEntry;
    HANDLE EncapTypeEntryOffldHandle;
    ULONG KeyLen;
    UCHAR KeyMat[1];
  } OFFLOAD_IPSEC_ADD_UDPESP_SA, *POFFLOAD_IPSEC_ADD_UDPESP_SA;

  typedef struct _OFFLOAD_IPSEC_DELETE_UDPESP_SA {
    HANDLE OffloadHandle;
    HANDLE EncapTypeEntryOffldHandle;
  } OFFLOAD_IPSEC_DELETE_UDPESP_SA, *POFFLOAD_IPSEC_DELETE_UDPESP_SA;

  typedef ULONG NDIS_VLAN_ID;

  typedef enum _NDIS_MEDIUM {
    NdisMedium802_3,
    NdisMedium802_5,
    NdisMediumFddi,
    NdisMediumWan,
    NdisMediumLocalTalk,
    NdisMediumDix,
    NdisMediumArcnetRaw,
    NdisMediumArcnet878_2,
    NdisMediumAtm,
    NdisMediumWirelessWan,
    NdisMediumIrda,
    NdisMediumBpc,
    NdisMediumCoWan,
    NdisMedium1394,
    NdisMediumInfiniBand,
#if NTDDI_VERSION >= 0x06000000 || NDIS_SUPPORT_NDIS6
    NdisMediumTunnel,
    NdisMediumNative802_11,
    NdisMediumLoopback,
#endif
#if NTDDI_VERSION >= 0x06010000
    NdisMediumWiMAX,
    NdisMediumIP,
#endif
    NdisMediumMax
  } NDIS_MEDIUM, *PNDIS_MEDIUM;

  typedef enum _NDIS_PHYSICAL_MEDIUM {
    NdisPhysicalMediumUnspecified,
    NdisPhysicalMediumWirelessLan,
    NdisPhysicalMediumCableModem,
    NdisPhysicalMediumPhoneLine,
    NdisPhysicalMediumPowerLine,
    NdisPhysicalMediumDSL,
    NdisPhysicalMediumFibreChannel,
    NdisPhysicalMedium1394,
    NdisPhysicalMediumWirelessWan,
    NdisPhysicalMediumNative802_11,
    NdisPhysicalMediumBluetooth,
    NdisPhysicalMediumInfiniband,
    NdisPhysicalMediumWiMax,
    NdisPhysicalMediumUWB,
    NdisPhysicalMedium802_3,
    NdisPhysicalMedium802_5,
    NdisPhysicalMediumIrda,
    NdisPhysicalMediumWiredWAN,
    NdisPhysicalMediumWiredCoWan,
    NdisPhysicalMediumOther,
    NdisPhysicalMediumMax
  } NDIS_PHYSICAL_MEDIUM, *PNDIS_PHYSICAL_MEDIUM;

  typedef struct _TRANSPORT_HEADER_OFFSET {
    USHORT ProtocolType;
    USHORT HeaderOffset;
  } TRANSPORT_HEADER_OFFSET, *PTRANSPORT_HEADER_OFFSET;

  typedef struct _NETWORK_ADDRESS {
    USHORT AddressLength;
    USHORT AddressType;
    UCHAR Address[1];
  } NETWORK_ADDRESS, *PNETWORK_ADDRESS;

  typedef struct _NETWORK_ADDRESS_LIST {
    LONG AddressCount;
    USHORT AddressType;
    NETWORK_ADDRESS Address[1];
  } NETWORK_ADDRESS_LIST, *PNETWORK_ADDRESS_LIST;

  typedef struct _NETWORK_ADDRESS_IP {
    USHORT sin_port;
    ULONG in_addr;
    UCHAR sin_zero[8];
  } NETWORK_ADDRESS_IP, *PNETWORK_ADDRESS_IP;

#define NDIS_PROTOCOL_ID_DEFAULT 0x00
#define NDIS_PROTOCOL_ID_TCP_IP 0x02
#define NDIS_PROTOCOL_ID_IPX 0x06
#define NDIS_PROTOCOL_ID_NBF 0x07
#define NDIS_PROTOCOL_ID_MAX 0x0f
#define NDIS_PROTOCOL_ID_MASK 0x0f

#define READABLE_LOCAL_CLOCK 0x00000001
#define CLOCK_NETWORK_DERIVED 0x00000002
#define CLOCK_PRECISION 0x00000004
#define RECEIVE_TIME_INDICATION_CAPABLE 0x00000008
#define TIMED_SEND_CAPABLE 0x00000010
#define TIME_STAMP_CAPABLE 0x00000020

#define NDIS_DEVICE_WAKE_UP_ENABLE 0x00000001
#define NDIS_DEVICE_WAKE_ON_PATTERN_MATCH_ENABLE 0x00000002
#define NDIS_DEVICE_WAKE_ON_MAGIC_PACKET_ENABLE 0x00000004

#define WAN_PROTOCOL_KEEPS_STATS 0x00000001

#define NETWORK_ADDRESS_LENGTH_IP sizeof (NETWORK_ADDRESS_IP)
#define NETWORK_ADDRESS_LENGTH_IPX sizeof (NETWORK_ADDRESS_IPX)

  typedef struct _NETWORK_ADDRESS_IPX {
    ULONG NetworkAddress;
    UCHAR NodeAddress[6];
    USHORT Socket;
  } NETWORK_ADDRESS_IPX, *PNETWORK_ADDRESS_IPX;

  typedef enum _NDIS_HARDWARE_STATUS {
    NdisHardwareStatusReady,
    NdisHardwareStatusInitializing,
    NdisHardwareStatusReset,
    NdisHardwareStatusClosing,
    NdisHardwareStatusNotReady
  } NDIS_HARDWARE_STATUS, *PNDIS_HARDWARE_STATUS;

  typedef struct _GEN_GET_TIME_CAPS {
    ULONG Flags;
    ULONG ClockPrecision;
  } GEN_GET_TIME_CAPS, *PGEN_GET_TIME_CAPS;

  typedef struct _GEN_GET_NETCARD_TIME {
    ULONGLONG ReadTime;
  } GEN_GET_NETCARD_TIME, *PGEN_GET_NETCARD_TIME;

  typedef struct _NDIS_PM_PACKET_PATTERN {
    ULONG Priority;
    ULONG Reserved;
    ULONG MaskSize;
    ULONG PatternOffset;
    ULONG PatternSize;
    ULONG PatternFlags;
  } NDIS_PM_PACKET_PATTERN, *PNDIS_PM_PACKET_PATTERN;

  typedef enum _NDIS_DEVICE_POWER_STATE {
    NdisDeviceStateUnspecified = 0,
    NdisDeviceStateD0,
    NdisDeviceStateD1,
    NdisDeviceStateD2,
    NdisDeviceStateD3,
    NdisDeviceStateMaximum
  } NDIS_DEVICE_POWER_STATE, *PNDIS_DEVICE_POWER_STATE;

  typedef struct _NDIS_PM_WAKE_UP_CAPABILITIES {
    NDIS_DEVICE_POWER_STATE MinMagicPacketWakeUp;
    NDIS_DEVICE_POWER_STATE MinPatternWakeUp;
    NDIS_DEVICE_POWER_STATE MinLinkChangeWakeUp;
  } NDIS_PM_WAKE_UP_CAPABILITIES, *PNDIS_PM_WAKE_UP_CAPABILITIES;

  typedef struct _NDIS_PNP_CAPABILITIES {
    ULONG Flags;
    NDIS_PM_WAKE_UP_CAPABILITIES WakeUpCapabilities;
  } NDIS_PNP_CAPABILITIES, *PNDIS_PNP_CAPABILITIES;

  typedef enum _NDIS_FDDI_ATTACHMENT_TYPE {
    NdisFddiTypeIsolated = 1,
    NdisFddiTypeLocalA,
    NdisFddiTypeLocalB,
    NdisFddiTypeLocalAB,
    NdisFddiTypeLocalS,
    NdisFddiTypeWrapA,
    NdisFddiTypeWrapB,
    NdisFddiTypeWrapAB,
    NdisFddiTypeWrapS,
    NdisFddiTypeCWrapA,
    NdisFddiTypeCWrapB,
    NdisFddiTypeCWrapS,
    NdisFddiTypeThrough
  } NDIS_FDDI_ATTACHMENT_TYPE, *PNDIS_FDDI_ATTACHMENT_TYPE;

  typedef enum _NDIS_FDDI_RING_MGT_STATE {
    NdisFddiRingIsolated = 1,
    NdisFddiRingNonOperational,
    NdisFddiRingOperational,
    NdisFddiRingDetect,
    NdisFddiRingNonOperationalDup,
    NdisFddiRingOperationalDup,
    NdisFddiRingDirected,
    NdisFddiRingTrace
  } NDIS_FDDI_RING_MGT_STATE, *PNDIS_FDDI_RING_MGT_STATE;

  typedef enum _NDIS_FDDI_LCONNECTION_STATE {
    NdisFddiStateOff = 1,
    NdisFddiStateBreak,
    NdisFddiStateTrace,
    NdisFddiStateConnect,
    NdisFddiStateNext,
    NdisFddiStateSignal,
    NdisFddiStateJoin,
    NdisFddiStateVerify,
    NdisFddiStateActive,
    NdisFddiStateMaintenance
  } NDIS_FDDI_LCONNECTION_STATE, *PNDIS_FDDI_LCONNECTION_STATE;

  typedef enum _NDIS_WAN_MEDIUM_SUBTYPE {
    NdisWanMediumHub,
    NdisWanMediumX_25,
    NdisWanMediumIsdn,
    NdisWanMediumSerial,
    NdisWanMediumFrameRelay,
    NdisWanMediumAtm,
    NdisWanMediumSonet,
    NdisWanMediumSW56K,
    NdisWanMediumPPTP,
    NdisWanMediumL2TP,
    NdisWanMediumIrda,
    NdisWanMediumParallel,
    NdisWanMediumPppoe,
#if NTDDI_VERSION >= 0x06000000
    NdisWanMediumSSTP,
    NdisWanMediumAgileVPN,
#endif
  } NDIS_WAN_MEDIUM_SUBTYPE, *PNDIS_WAN_MEDIUM_SUBTYPE;

  typedef enum _NDIS_WAN_HEADER_FORMAT {
    NdisWanHeaderNative,
    NdisWanHeaderEthernet
  } NDIS_WAN_HEADER_FORMAT, *PNDIS_WAN_HEADER_FORMAT;

  typedef enum _NDIS_WAN_QUALITY {
    NdisWanRaw,
    NdisWanErrorControl,
    NdisWanReliable
  } NDIS_WAN_QUALITY, *PNDIS_WAN_QUALITY;

  typedef struct _NDIS_WAN_PROTOCOL_CAPS {
    ULONG Flags;
    ULONG Reserved;
  } NDIS_WAN_PROTOCOL_CAPS, *PNDIS_WAN_PROTOCOL_CAPS;

  typedef enum _NDIS_802_5_RING_STATE {
    NdisRingStateOpened = 1,
    NdisRingStateClosed,
    NdisRingStateOpening,
    NdisRingStateClosing,
    NdisRingStateOpenFailure,
    NdisRingStateRingFailure
  } NDIS_802_5_RING_STATE, *PNDIS_802_5_RING_STATE;

  typedef enum _NDIS_MEDIA_STATE {
    NdisMediaStateConnected,
    NdisMediaStateDisconnected
  } NDIS_MEDIA_STATE, *PNDIS_MEDIA_STATE;

  typedef ULONG Priority_802_3;

  typedef struct _NDIS_CO_LINK_SPEED {
    ULONG Outbound;
    ULONG Inbound;
  } NDIS_CO_LINK_SPEED, *PNDIS_CO_LINK_SPEED;

#if NTDDI_VERSION >= 0x06000000 || NDIS_SUPPORT_NDIS6
#define NDIS_LINK_SPEED_UNKNOWN NET_IF_LINK_SPEED_UNKNOWN

  typedef struct _NDIS_LINK_SPEED {
    ULONG64 XmitLinkSpeed;
    ULONG64 RcvLinkSpeed;
  } NDIS_LINK_SPEED, *PNDIS_LINK_SPEED;
#endif

#ifndef _NDIS_
  typedef int NDIS_STATUS, *PNDIS_STATUS;
#endif

#if NTDDI_VERSION >= 0x06000000 || NDIS_SUPPORT_NDIS6
#ifndef __WINDOT11_H__
#include <windot11.h>
#endif
#endif

#define fNDIS_GUID_TO_OID 0x00000001
#define fNDIS_GUID_TO_STATUS 0x00000002
#define fNDIS_GUID_ANSI_STRING 0x00000004
#define fNDIS_GUID_UNICODE_STRING 0x00000008
#define fNDIS_GUID_ARRAY 0x00000010
#define fNDIS_GUID_ALLOW_READ 0x00000020
#define fNDIS_GUID_ALLOW_WRITE 0x00000040
#define fNDIS_GUID_METHOD 0x00000080
#define fNDIS_GUID_NDIS_RESERVED 0x00000100
#define fNDIS_GUID_SUPPORT_COMMON_HEADER 0x00000200

#define NDIS_PACKET_TYPE_DIRECTED 0x00000001
#define NDIS_PACKET_TYPE_MULTICAST 0x00000002
#define NDIS_PACKET_TYPE_ALL_MULTICAST 0x00000004
#define NDIS_PACKET_TYPE_BROADCAST 0x00000008
#define NDIS_PACKET_TYPE_SOURCE_ROUTING 0x00000010
#define NDIS_PACKET_TYPE_PROMISCUOUS 0x00000020
#define NDIS_PACKET_TYPE_SMT 0x00000040
#define NDIS_PACKET_TYPE_ALL_LOCAL 0x00000080
#define NDIS_PACKET_TYPE_GROUP 0x00001000
#define NDIS_PACKET_TYPE_ALL_FUNCTIONAL 0x00002000
#define NDIS_PACKET_TYPE_FUNCTIONAL 0x00004000
#define NDIS_PACKET_TYPE_MAC_FRAME 0x00008000
#define NDIS_PACKET_TYPE_NO_LOCAL 0x00010000

#define NDIS_RING_SIGNAL_LOSS 0x00008000
#define NDIS_RING_HARD_ERROR 0x00004000
#define NDIS_RING_SOFT_ERROR 0x00002000
#define NDIS_RING_TRANSMIT_BEACON 0x00001000
#define NDIS_RING_LOBE_WIRE_FAULT 0x00000800
#define NDIS_RING_AUTO_REMOVAL_ERROR 0x00000400
#define NDIS_RING_REMOVE_RECEIVED 0x00000200
#define NDIS_RING_COUNTER_OVERFLOW 0x00000100
#define NDIS_RING_SINGLE_STATION 0x00000080
#define NDIS_RING_RING_RECOVERY 0x00000040

#define NDIS_PROT_OPTION_ESTIMATED_LENGTH 0x00000001
#define NDIS_PROT_OPTION_NO_LOOPBACK 0x00000002
#define NDIS_PROT_OPTION_NO_RSVD_ON_RCVPKT 0x00000004
#define NDIS_PROT_OPTION_SEND_RESTRICTED 0x00000008

#define NDIS_MAC_OPTION_COPY_LOOKAHEAD_DATA 0x00000001
#define NDIS_MAC_OPTION_RECEIVE_SERIALIZED 0x00000002
#define NDIS_MAC_OPTION_TRANSFERS_NOT_PEND 0x00000004
#define NDIS_MAC_OPTION_NO_LOOPBACK 0x00000008

#define NDIS_MAC_OPTION_FULL_DUPLEX 0x00000010

#define NDIS_MAC_OPTION_EOTX_INDICATION 0x00000020
#define NDIS_MAC_OPTION_8021P_PRIORITY 0x00000040
#define NDIS_MAC_OPTION_SUPPORTS_MAC_ADDRESS_OVERWRITE 0x00000080
#define NDIS_MAC_OPTION_RECEIVE_AT_DPC 0x00000100
#define NDIS_MAC_OPTION_8021Q_VLAN 0x00000200
#define NDIS_MAC_OPTION_RESERVED 0x80000000

#define NDIS_MEDIA_CAP_TRANSMIT 0x00000001
#define NDIS_MEDIA_CAP_RECEIVE 0x00000002

#define NDIS_CO_MAC_OPTION_DYNAMIC_LINK_SPEED 0x00000001

  typedef struct _NDIS_GUID {
    GUID Guid;
    __C89_NAMELESS union {
      NDIS_OID Oid;
      NDIS_STATUS Status;
    };
    ULONG Size;
    ULONG Flags;
  } NDIS_GUID, *PNDIS_GUID;

  typedef struct _NDIS_IRDA_PACKET_INFO {
    ULONG ExtraBOFs;
    ULONG MinTurnAroundTime;
  } NDIS_IRDA_PACKET_INFO, *PNDIS_IRDA_PACKET_INFO;

#if NTDDI_VERSION >= 0x06000000 || NDIS_SUPPORT_NDIS6
#define NDIS_MAKE_NET_LUID(PNLUID, IFTYPE, NLUIDIDX) { (PNLUID)->Info.IfType = IFTYPE; (PNLUID)->Info.NetLuidIndex = NLUIDIDX; (PNLUID)->Info.Reserved = 0; }

#define MAXIMUM_IP_OPER_STATUS_ADDRESS_FAMILIES_SUPPORTED 32

#define NDIS_IF_MAX_STRING_SIZE IF_MAX_STRING_SIZE
#define NDIS_MAX_PHYS_ADDRESS_LENGTH IF_MAX_PHYS_ADDRESS_LENGTH

#define NDIS_LINK_STATE_XMIT_LINK_SPEED_AUTO_NEGOTIATED 0x00000001
#define NDIS_LINK_STATE_RCV_LINK_SPEED_AUTO_NEGOTIATED 0x00000002
#define NDIS_LINK_STATE_DUPLEX_AUTO_NEGOTIATED 0x00000004
#define NDIS_LINK_STATE_PAUSE_FUNCTIONS_AUTO_NEGOTIATED 0x00000008

#define NDIS_LINK_STATE_REVISION_1 1
#define NDIS_LINK_PARAMETERS_REVISION_1 1
#define NDIS_OPER_STATE_REVISION_1 1
#define NDIS_IP_OPER_STATUS_INFO_REVISION_1 1
#define NDIS_IP_OPER_STATE_REVISION_1 1

#define NDIS_SIZEOF_LINK_STATE_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_LINK_STATE, AutoNegotiationFlags)
#define NDIS_SIZEOF_LINK_PARAMETERS_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_LINK_PARAMETERS, AutoNegotiationFlags)
#define NDIS_SIZEOF_OPER_STATE_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_OPER_STATE, OperationalStatusFlags)
#define NDIS_SIZEOF_IP_OPER_STATUS_INFO_REVISION_1 FIELD_OFFSET (NDIS_IP_OPER_STATUS_INFO, IpOperationalStatus) + MAXIMUM_IP_OPER_STATUS_ADDRESS_FAMILIES_SUPPORTED *sizeof (NDIS_IP_OPER_STATUS)
#define NDIS_SIZEOF_IP_OPER_STATE_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_IP_OPER_STATE, IpOperationalStatus)

  typedef IF_COUNTED_STRING NDIS_IF_COUNTED_STRING, *PNDIS_IF_COUNTED_STRING;
  typedef IF_PHYSICAL_ADDRESS NDIS_IF_PHYSICAL_ADDRESS, *PNDIS_IF_PHYSICAL_ADDRESS;
  typedef NET_IF_MEDIA_CONNECT_STATE NDIS_MEDIA_CONNECT_STATE, *PNDIS_MEDIA_CONNECT_STATE;
  typedef NET_IF_MEDIA_DUPLEX_STATE NDIS_MEDIA_DUPLEX_STATE, *PNDIS_MEDIA_DUPLEX_STATE;

  typedef enum _NDIS_SUPPORTED_PAUSE_FUNCTIONS {
    NdisPauseFunctionsUnsupported,
    NdisPauseFunctionsSendOnly,
    NdisPauseFunctionsReceiveOnly,
    NdisPauseFunctionsSendAndReceive,
    NdisPauseFunctionsUnknown
  } NDIS_SUPPORTED_PAUSE_FUNCTIONS, *PNDIS_SUPPORTED_PAUSE_FUNCTIONS;

  typedef struct _NDIS_LINK_STATE {
    NDIS_OBJECT_HEADER Header;
    NDIS_MEDIA_CONNECT_STATE MediaConnectState;
    NDIS_MEDIA_DUPLEX_STATE MediaDuplexState;
    ULONG64 XmitLinkSpeed;
    ULONG64 RcvLinkSpeed;
    NDIS_SUPPORTED_PAUSE_FUNCTIONS PauseFunctions;
    ULONG AutoNegotiationFlags;
  } NDIS_LINK_STATE, *PNDIS_LINK_STATE;

  typedef struct _NDIS_LINK_PARAMETERS {
    NDIS_OBJECT_HEADER Header;
    NDIS_MEDIA_DUPLEX_STATE MediaDuplexState;
    ULONG64 XmitLinkSpeed;
    ULONG64 RcvLinkSpeed;
    NDIS_SUPPORTED_PAUSE_FUNCTIONS PauseFunctions;
    ULONG AutoNegotiationFlags;
  } NDIS_LINK_PARAMETERS, *PNDIS_LINK_PARAMETERS;

  typedef struct _NDIS_OPER_STATE {
    NDIS_OBJECT_HEADER Header;
    NET_IF_OPER_STATUS OperationalStatus;
    ULONG OperationalStatusFlags;
  } NDIS_OPER_STATE, *PNDIS_OPER_STATE;

  typedef struct _NDIS_IP_OPER_STATUS {
    ULONG AddressFamily;
    NET_IF_OPER_STATUS OperationalStatus;
    ULONG OperationalStatusFlags;
  } NDIS_IP_OPER_STATUS, *PNDIS_IP_OPER_STATUS;

  typedef struct _NDIS_IP_OPER_STATUS_INFO {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    ULONG NumberofAddressFamiliesReturned;
    NDIS_IP_OPER_STATUS IpOperationalStatus[MAXIMUM_IP_OPER_STATUS_ADDRESS_FAMILIES_SUPPORTED];
  } NDIS_IP_OPER_STATUS_INFO, *PNDIS_IP_OPER_STATUS_INFO;

  typedef struct _NDIS_IP_OPER_STATE {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    NDIS_IP_OPER_STATUS IpOperationalStatus;
  } NDIS_IP_OPER_STATE, *PNDIS_IP_OPER_STATE;

#define NDIS_OFFLOAD_PARAMETERS_NO_CHANGE 0
#define NDIS_OFFLOAD_PARAMETERS_TX_RX_DISABLED 1
#define NDIS_OFFLOAD_PARAMETERS_TX_ENABLED_RX_DISABLED 2
#define NDIS_OFFLOAD_PARAMETERS_RX_ENABLED_TX_DISABLED 3
#define NDIS_OFFLOAD_PARAMETERS_TX_RX_ENABLED 4

#define NDIS_OFFLOAD_PARAMETERS_LSOV1_DISABLED 1
#define NDIS_OFFLOAD_PARAMETERS_LSOV1_ENABLED 2

#define NDIS_OFFLOAD_PARAMETERS_IPSECV1_DISABLED 1
#define NDIS_OFFLOAD_PARAMETERS_IPSECV1_AH_ENABLED 2
#define NDIS_OFFLOAD_PARAMETERS_IPSECV1_ESP_ENABLED 3
#define NDIS_OFFLOAD_PARAMETERS_IPSECV1_AH_AND_ESP_ENABLED 4

#define NDIS_OFFLOAD_PARAMETERS_LSOV2_DISABLED 1
#define NDIS_OFFLOAD_PARAMETERS_LSOV2_ENABLED 2

#if NDIS_SUPPORT_NDIS61
#define NDIS_OFFLOAD_PARAMETERS_IPSECV2_DISABLED 1
#define NDIS_OFFLOAD_PARAMETERS_IPSECV2_AH_ENABLED 2
#define NDIS_OFFLOAD_PARAMETERS_IPSECV2_ESP_ENABLED 3
#define NDIS_OFFLOAD_PARAMETERS_IPSECV2_AH_AND_ESP_ENABLED 4
#endif

#if NDIS_SUPPORT_NDIS630
#define NDIS_OFFLOAD_PARAMETERS_RSC_DISABLED 1
#define NDIS_OFFLOAD_PARAMETERS_RSC_ENABLED 2

#define NDIS_ENCAPSULATION_TYPE_GRE_MAC 0x00000001
#define NDIS_ENCAPSULATION_TYPE_MAX NDIS_ENCAPSULATION_TYPE_GRE_MAC
#endif

#define NDIS_OFFLOAD_PARAMETERS_CONNECTION_OFFLOAD_DISABLED 1
#define NDIS_OFFLOAD_PARAMETERS_CONNECTION_OFFLOAD_ENABLED 2

#define NDIS_OFFLOAD_PARAMETERS_REVISION_1 1
#if NDIS_SUPPORT_NDIS61
#define NDIS_OFFLOAD_PARAMETERS_REVISION_2 2
#endif
#if NDIS_SUPPORT_NDIS630
#define NDIS_OFFLOAD_PARAMETERS_REVISION_3 3
#define NDIS_OFFLOAD_PARAMETERS_SKIP_REGISTRY_UPDATE 0x00000001
#endif

  typedef struct _NDIS_OFFLOAD_PARAMETERS {
    NDIS_OBJECT_HEADER Header;
    UCHAR IPv4Checksum;
    UCHAR TCPIPv4Checksum;
    UCHAR UDPIPv4Checksum;
    UCHAR TCPIPv6Checksum;
    UCHAR UDPIPv6Checksum;
    UCHAR LsoV1;
    UCHAR IPsecV1;
    UCHAR LsoV2IPv4;
    UCHAR LsoV2IPv6;
    UCHAR TcpConnectionIPv4;
    UCHAR TcpConnectionIPv6;
    ULONG Flags;
#if NDIS_SUPPORT_NDIS61
    UCHAR IPsecV2;
    UCHAR IPsecV2IPv4;
#endif
#if NDIS_SUPPORT_NDIS630
    __C89_NAMELESS struct {
      UCHAR RscIPv4;
      UCHAR RscIPv6;
    };
#endif
#if NDIS_SUPPORT_NDIS630
    __C89_NAMELESS struct {
      UCHAR EncapsulatedPacketTaskOffload;
      UCHAR EncapsulationTypes;
    };
#endif
  } NDIS_OFFLOAD_PARAMETERS, *PNDIS_OFFLOAD_PARAMETERS;

#define NDIS_SIZEOF_OFFLOAD_PARAMETERS_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_OFFLOAD_PARAMETERS, Flags)
#if NDIS_SUPPORT_NDIS61
#define NDIS_SIZEOF_OFFLOAD_PARAMETERS_REVISION_2 RTL_SIZEOF_THROUGH_FIELD (NDIS_OFFLOAD_PARAMETERS, IPsecV2IPv4)
#endif
#if NDIS_SUPPORT_NDIS630
#define NDIS_SIZEOF_OFFLOAD_PARAMETERS_REVISION_3 RTL_SIZEOF_THROUGH_FIELD (NDIS_OFFLOAD_PARAMETERS, EncapsulationTypes)
#endif

#define NDIS_OFFLOAD_NOT_SUPPORTED 0
#define NDIS_OFFLOAD_SUPPORTED 1

#define NDIS_OFFLOAD_SET_NO_CHANGE 0
#define NDIS_OFFLOAD_SET_ON 1
#define NDIS_OFFLOAD_SET_OFF 2

#define NDIS_ENCAPSULATION_NOT_SUPPORTED 0x00000000
#define NDIS_ENCAPSULATION_NULL 0x00000001
#define NDIS_ENCAPSULATION_IEEE_802_3 0x00000002
#define NDIS_ENCAPSULATION_IEEE_802_3_P_AND_Q 0x00000004
#define NDIS_ENCAPSULATION_IEEE_802_3_P_AND_Q_IN_OOB 0x00000008
#define NDIS_ENCAPSULATION_IEEE_LLC_SNAP_ROUTED 0x00000010

  typedef struct _NDIS_TCP_LARGE_SEND_OFFLOAD_V1 {
    struct {
      ULONG Encapsulation;
      ULONG MaxOffLoadSize;
      ULONG MinSegmentCount;
      ULONG TcpOptions:2;
      ULONG IpOptions:2;
    } IPv4;
  } NDIS_TCP_LARGE_SEND_OFFLOAD_V1, *PNDIS_TCP_LARGE_SEND_OFFLOAD_V1;

  typedef struct _NDIS_TCP_IP_CHECKSUM_OFFLOAD {
    struct {
      ULONG Encapsulation;
      ULONG IpOptionsSupported:2;
      ULONG TcpOptionsSupported:2;
      ULONG TcpChecksum:2;
      ULONG UdpChecksum:2;
      ULONG IpChecksum:2;
    } IPv4Transmit;
    struct {
      ULONG Encapsulation;
      ULONG IpOptionsSupported:2;
      ULONG TcpOptionsSupported:2;
      ULONG TcpChecksum:2;
      ULONG UdpChecksum:2;
      ULONG IpChecksum:2;
    } IPv4Receive;
    struct {
      ULONG Encapsulation;
      ULONG IpExtensionHeadersSupported:2;
      ULONG TcpOptionsSupported:2;
      ULONG TcpChecksum:2;
      ULONG UdpChecksum:2;
    } IPv6Transmit;
    struct {
      ULONG Encapsulation;
      ULONG IpExtensionHeadersSupported:2;
      ULONG TcpOptionsSupported:2;
      ULONG TcpChecksum:2;
      ULONG UdpChecksum:2;
    } IPv6Receive;
  } NDIS_TCP_IP_CHECKSUM_OFFLOAD, *PNDIS_TCP_IP_CHECKSUM_OFFLOAD;
  typedef struct _NDIS_IPSEC_OFFLOAD_V1 {
    struct {
      ULONG Encapsulation;
      ULONG AhEspCombined;
      ULONG TransportTunnelCombined;
      ULONG IPv4Options;
      ULONG Flags;
    } Supported;
    struct {
      ULONG Md5 : 2;
      ULONG Sha_1 : 2;
      ULONG Transport : 2;
      ULONG Tunnel : 2;
      ULONG Send : 2;
      ULONG Receive : 2;
    } IPv4AH;
    struct {
      ULONG Des : 2;
      ULONG Reserved : 2;
      ULONG TripleDes : 2;
      ULONG NullEsp : 2;
      ULONG Transport : 2;
      ULONG Tunnel : 2;
      ULONG Send : 2;
      ULONG Receive : 2;
    } IPv4ESP;
  } NDIS_IPSEC_OFFLOAD_V1, *PNDIS_IPSEC_OFFLOAD_V1;

  typedef struct _NDIS_TCP_LARGE_SEND_OFFLOAD_V2 {
    struct {
      ULONG Encapsulation;
      ULONG MaxOffLoadSize;
      ULONG MinSegmentCount;
    }IPv4;
    struct {
      ULONG Encapsulation;
      ULONG MaxOffLoadSize;
      ULONG MinSegmentCount;
      ULONG IpExtensionHeadersSupported:2;
      ULONG TcpOptionsSupported:2;
    } IPv6;
  } NDIS_TCP_LARGE_SEND_OFFLOAD_V2, *PNDIS_TCP_LARGE_SEND_OFFLOAD_V2;

#if NDIS_SUPPORT_NDIS61
#define IPSEC_OFFLOAD_V2_AUTHENTICATION_MD5 0x00000001
#define IPSEC_OFFLOAD_V2_AUTHENTICATION_SHA_1 0x00000002
#define IPSEC_OFFLOAD_V2_AUTHENTICATION_SHA_256 0x00000004
#define IPSEC_OFFLOAD_V2_AUTHENTICATION_AES_GCM_128 0x00000008
#define IPSEC_OFFLOAD_V2_AUTHENTICATION_AES_GCM_192 0x00000010
#define IPSEC_OFFLOAD_V2_AUTHENTICATION_AES_GCM_256 0x00000020

#define IPSEC_OFFLOAD_V2_ENCRYPTION_NONE 0x00000001
#define IPSEC_OFFLOAD_V2_ENCRYPTION_DES_CBC 0x00000002
#define IPSEC_OFFLOAD_V2_ENCRYPTION_3_DES_CBC 0x00000004
#define IPSEC_OFFLOAD_V2_ENCRYPTION_AES_GCM_128 0x00000008
#define IPSEC_OFFLOAD_V2_ENCRYPTION_AES_GCM_192 0x00000010
#define IPSEC_OFFLOAD_V2_ENCRYPTION_AES_GCM_256 0x00000020
#define IPSEC_OFFLOAD_V2_ENCRYPTION_AES_CBC_128 0x00000040
#define IPSEC_OFFLOAD_V2_ENCRYPTION_AES_CBC_192 0x00000080
#define IPSEC_OFFLOAD_V2_ENCRYPTION_AES_CBC_256 0x00000100

  typedef struct _NDIS_IPSEC_OFFLOAD_V2 {
    ULONG Encapsulation;
    BOOLEAN IPv6Supported;
    BOOLEAN IPv4Options;
    BOOLEAN IPv6NonIPsecExtensionHeaders;
    BOOLEAN Ah;
    BOOLEAN Esp;
    BOOLEAN AhEspCombined;
    BOOLEAN Transport;
    BOOLEAN Tunnel;
    BOOLEAN TransportTunnelCombined;
    BOOLEAN LsoSupported;
    BOOLEAN ExtendedSequenceNumbers;
    ULONG UdpEsp;
    ULONG AuthenticationAlgorithms;
    ULONG EncryptionAlgorithms;
    ULONG SaOffloadCapacity;
  } NDIS_IPSEC_OFFLOAD_V2, *PNDIS_IPSEC_OFFLOAD_V2;
#endif

#if NDIS_SUPPORT_NDIS630
#define NDIS_ENCAPSULATED_PACKET_TASK_OFFLOAD_NOT_SUPPORTED 0x00000000
#define NDIS_ENCAPSULATED_PACKET_TASK_OFFLOAD_INNER_IPV4 0x00000001
#define NDIS_ENCAPSULATED_PACKET_TASK_OFFLOAD_OUTER_IPV4 0x00000002
#define NDIS_ENCAPSULATED_PACKET_TASK_OFFLOAD_INNER_IPV6 0x00000004
#define NDIS_ENCAPSULATED_PACKET_TASK_OFFLOAD_OUTER_IPV6 0x00000008
#endif

#define NDIS_OFFLOAD_FLAGS_GROUP_CHECKSUM_CAPABILITIES 0x1
#if NDIS_SUPPORT_NDIS630
#define IPSEC_OFFLOAD_V2_AND_TCP_CHECKSUM_COEXISTENCE 0x2
#define IPSEC_OFFLOAD_V2_AND_UDP_CHECKSUM_COEXISTENCE 0x4
#endif

#define NDIS_MAXIMUM_PORTS 0x1000000

#define NDIS_DEFAULT_PORT_NUMBER ((NDIS_PORT_NUMBER) 0)

#define NDIS_WMI_DEFAULT_METHOD_ID 1

#define NDIS_WMI_OBJECT_TYPE_SET 0x01
#define NDIS_WMI_OBJECT_TYPE_METHOD 0x02
#define NDIS_WMI_OBJECT_TYPE_EVENT 0x03
#define NDIS_WMI_OBJECT_TYPE_ENUM_ADAPTER 0x04
#define NDIS_WMI_OBJECT_TYPE_OUTPUT_INFO 0x05

#define NDIS_DEVICE_TYPE_ENDPOINT 0x00000001

#define NDIS_OFFLOAD_REVISION_1 1
#define NDIS_TCP_CONNECTION_OFFLOAD_REVISION_1 1
#define NDIS_PORT_AUTHENTICATION_PARAMETERS_REVISION_1 1
#define NDIS_WMI_METHOD_HEADER_REVISION_1 1
#define NDIS_WMI_SET_HEADER_REVISION_1 1
#define NDIS_WMI_EVENT_HEADER_REVISION_1 1
#define NDIS_WMI_ENUM_ADAPTER_REVISION_1 1
#if NDIS_SUPPORT_NDIS61
#define NDIS_TCP_CONNECTION_OFFLOAD_REVISION_2 2
#define NDIS_OFFLOAD_REVISION_2 2
#endif
#if NDIS_SUPPORT_NDIS630
#define NDIS_OFFLOAD_REVISION_3 3
#define NDIS_TCP_RECV_SEG_COALESC_OFFLOAD_REVISION_1 1
#endif

#define NDIS_SIZEOF_NDIS_OFFLOAD_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_OFFLOAD, Flags)
#define NDIS_SIZEOF_NDIS_WMI_OFFLOAD_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_WMI_OFFLOAD, Flags)
#define NDIS_SIZEOF_TCP_CONNECTION_OFFLOAD_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_TCP_CONNECTION_OFFLOAD, Flags)
#define NDIS_SIZEOF_WMI_TCP_CONNECTION_OFFLOAD_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_WMI_TCP_CONNECTION_OFFLOAD, Flags)
#define NDIS_SIZEOF_PORT_AUTHENTICATION_PARAMETERS_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_PORT_AUTHENTICATION_PARAMETERS, RcvAuthorizationState)
#define NDIS_SIZEOF_WMI_METHOD_HEADER_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_WMI_METHOD_HEADER, Padding)
#define NDIS_SIZEOF_WMI_SET_HEADER_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_WMI_SET_HEADER, Padding)
#define NDIS_SIZEOF_WMI_EVENT_HEADER_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_WMI_EVENT_HEADER, Padding)
#define NDIS_SIZEOF_WMI_ENUM_ADAPTER_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_WMI_ENUM_ADAPTER, DeviceName)
#if NDIS_SUPPORT_NDIS61
#define NDIS_SIZEOF_TCP_CONNECTION_OFFLOAD_REVISION_2 RTL_SIZEOF_THROUGH_FIELD (NDIS_TCP_CONNECTION_OFFLOAD, Flags)
#define NDIS_SIZEOF_NDIS_WMI_OFFLOAD_REVISION_2 RTL_SIZEOF_THROUGH_FIELD (NDIS_WMI_OFFLOAD, IPsecV2)
#define NDIS_SIZEOF_NDIS_OFFLOAD_REVISION_2 RTL_SIZEOF_THROUGH_FIELD (NDIS_OFFLOAD, IPsecV2)
#endif
#if NDIS_SUPPORT_NDIS630
#define NDIS_SIZEOF_NDIS_WMI_OFFLOAD_REVISION_3 RTL_SIZEOF_THROUGH_FIELD (NDIS_WMI_OFFLOAD, EncapsulatedPacketTaskOffloadGre)
#define NDIS_SIZEOF_NDIS_OFFLOAD_REVISION_3 RTL_SIZEOF_THROUGH_FIELD (NDIS_OFFLOAD, EncapsulatedPacketTaskOffloadGre)
#define NDIS_SIZEOF_TCP_RECV_SEG_COALESC_OFFLOAD_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_TCP_RECV_SEG_COALESCE_OFFLOAD, IPv6.Enabled)
#define NDIS_SIZEOF_ENCAPSULATED_PACKET_TASK_OFFLOAD_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_ENCAPSULATED_PACKET_TASK_OFFLOAD, MaxHeaderSizeSupported)
#endif

#if NDIS_SUPPORT_NDIS630
  typedef struct _NDIS_TCP_RECV_SEG_COALESCE_OFFLOAD {
    struct {
      BOOLEAN Enabled;
    } IPv4;
    struct {
      BOOLEAN Enabled;
    } IPv6;
  } NDIS_TCP_RECV_SEG_COALESCE_OFFLOAD, *PNDIS_TCP_RECV_SEG_COALESCE_OFFLOAD;

  typedef struct _NDIS_ENCAPSULATED_PACKET_TASK_OFFLOAD {
    ULONG TransmitChecksumOffloadSupported:4;
    ULONG ReceiveChecksumOffloadSupported:4;
    ULONG LsoV2Supported:4;
    ULONG RssSupported:4;
    ULONG VmqSupported:4;
    ULONG MaxHeaderSizeSupported;
  } NDIS_ENCAPSULATED_PACKET_TASK_OFFLOAD, *PNDIS_ENCAPSULATED_PACKET_TASK_OFFLOAD;
#endif

  typedef struct _NDIS_OFFLOAD {
    NDIS_OBJECT_HEADER Header;
    NDIS_TCP_IP_CHECKSUM_OFFLOAD Checksum;
    NDIS_TCP_LARGE_SEND_OFFLOAD_V1 LsoV1;
    NDIS_IPSEC_OFFLOAD_V1 IPsecV1;
    NDIS_TCP_LARGE_SEND_OFFLOAD_V2 LsoV2;
    ULONG Flags;
#if NDIS_SUPPORT_NDIS61
    NDIS_IPSEC_OFFLOAD_V2 IPsecV2;
#endif
#if NDIS_SUPPORT_NDIS630
    NDIS_TCP_RECV_SEG_COALESCE_OFFLOAD Rsc;
    NDIS_ENCAPSULATED_PACKET_TASK_OFFLOAD EncapsulatedPacketTaskOffloadGre;
#endif
  } NDIS_OFFLOAD, *PNDIS_OFFLOAD;

  typedef struct _NDIS_WMI_TCP_LARGE_SEND_OFFLOAD_V1 {
    struct {
      ULONG Encapsulation;
      ULONG MaxOffLoadSize;
      ULONG MinSegmentCount;
      ULONG TcpOptions;
      ULONG IpOptions;
    } IPv4;
  } NDIS_WMI_TCP_LARGE_SEND_OFFLOAD_V1, *PNDIS_WMI_TCP_LARGE_SEND_OFFLOAD_V1;

  typedef struct _NDIS_WMI_TCP_IP_CHECKSUM_OFFLOAD {
    struct {
      ULONG Encapsulation;
      ULONG IpOptionsSupported;
      ULONG TcpOptionsSupported;
      ULONG TcpChecksum;
      ULONG UdpChecksum;
      ULONG IpChecksum;
    } IPv4Transmit;
    struct {
      ULONG Encapsulation;
      ULONG IpOptionsSupported;
      ULONG TcpOptionsSupported;
      ULONG TcpChecksum;
      ULONG UdpChecksum;
      ULONG IpChecksum;
    } IPv4Receive;
    struct {
      ULONG Encapsulation;
      ULONG IpExtensionHeadersSupported;
      ULONG TcpOptionsSupported;
      ULONG TcpChecksum;
      ULONG UdpChecksum;
    } IPv6Transmit;
    struct {
      ULONG Encapsulation;
      ULONG IpExtensionHeadersSupported;
      ULONG TcpOptionsSupported;
      ULONG TcpChecksum;
      ULONG UdpChecksum;
    } IPv6Receive;
  } NDIS_WMI_TCP_IP_CHECKSUM_OFFLOAD, *PNDIS_WMI_TCP_IP_CHECKSUM_OFFLOAD;

  typedef struct _NDIS_WMI_IPSEC_OFFLOAD_V1 {
    struct {
      ULONG Encapsulation;
      ULONG AhEspCombined;
      ULONG TransportTunnelCombined;
      ULONG IPv4Options;
      ULONG Flags;
    } Supported;
    struct {
      ULONG Md5;
      ULONG Sha_1;
      ULONG Transport;
      ULONG Tunnel;
      ULONG Send;
      ULONG Receive;
    } IPv4AH;
    struct {
      ULONG Des;
      ULONG Reserved;
      ULONG TripleDes;
      ULONG NullEsp;
      ULONG Transport;
      ULONG Tunnel;
      ULONG Send;
      ULONG Receive;
    } IPv4ESP;
  } NDIS_WMI_IPSEC_OFFLOAD_V1, *PNDIS_WMI_IPSEC_OFFLOAD_V1;

  typedef struct _NDIS_WMI_TCP_LARGE_SEND_OFFLOAD_V2 {
    struct {
      ULONG Encapsulation;
      ULONG MaxOffLoadSize;
      ULONG MinSegmentCount;
    } IPv4;
    struct {
      ULONG Encapsulation;
      ULONG MaxOffLoadSize;
      ULONG MinSegmentCount;
      ULONG IpExtensionHeadersSupported;
      ULONG TcpOptionsSupported;
    } IPv6;
  } NDIS_WMI_TCP_LARGE_SEND_OFFLOAD_V2, *PNDIS_WMI_TCP_LARGE_SEND_OFFLOAD_V2;

  typedef struct _NDIS_WMI_OFFLOAD {
    NDIS_OBJECT_HEADER Header;
    NDIS_WMI_TCP_IP_CHECKSUM_OFFLOAD Checksum;
    NDIS_WMI_TCP_LARGE_SEND_OFFLOAD_V1 LsoV1;
    NDIS_WMI_IPSEC_OFFLOAD_V1 IPsecV1;
    NDIS_WMI_TCP_LARGE_SEND_OFFLOAD_V2 LsoV2;
    ULONG Flags;
#if NDIS_SUPPORT_NDIS61
    NDIS_IPSEC_OFFLOAD_V2 IPsecV2;
#endif
#if NDIS_SUPPORT_NDIS630
    NDIS_TCP_RECV_SEG_COALESCE_OFFLOAD Rsc;
    NDIS_ENCAPSULATED_PACKET_TASK_OFFLOAD EncapsulatedPacketTaskOffloadGre;
#endif
  } NDIS_WMI_OFFLOAD, *PNDIS_WMI_OFFLOAD;

  typedef struct _NDIS_TCP_CONNECTION_OFFLOAD {
    NDIS_OBJECT_HEADER Header;
    ULONG Encapsulation;
    ULONG SupportIPv4:2;
    ULONG SupportIPv6:2;
    ULONG SupportIPv6ExtensionHeaders:2;
    ULONG SupportSack:2;
#if NDIS_SUPPORT_NDIS61
    ULONG CongestionAlgorithm:4;
#endif
    ULONG TcpConnectionOffloadCapacity;
    ULONG Flags;
  } NDIS_TCP_CONNECTION_OFFLOAD, *PNDIS_TCP_CONNECTION_OFFLOAD;

  typedef struct _NDIS_WMI_TCP_CONNECTION_OFFLOAD {
    NDIS_OBJECT_HEADER Header;
    ULONG Encapsulation;
    ULONG SupportIPv4;
    ULONG SupportIPv6;
    ULONG SupportIPv6ExtensionHeaders;
    ULONG SupportSack;
    ULONG TcpConnectionOffloadCapacity;
    ULONG Flags;
  } NDIS_WMI_TCP_CONNECTION_OFFLOAD, *PNDIS_WMI_TCP_CONNECTION_OFFLOAD;

  typedef ULONG NDIS_PORT_NUMBER, *PNDIS_PORT_NUMBER;

  typedef enum _NDIS_PORT_TYPE {
    NdisPortTypeUndefined,
    NdisPortTypeBridge,
    NdisPortTypeRasConnection,
    NdisPortType8021xSupplicant,
#if NDIS_SUPPORT_NDIS630
    NdisPortTypeNdisImPlatform,
#endif
    NdisPortTypeMax
  } NDIS_PORT_TYPE, *PNDIS_PORT_TYPE;

  typedef enum _NDIS_PORT_AUTHORIZATION_STATE {
    NdisPortAuthorizationUnknown,
    NdisPortAuthorized,
    NdisPortUnauthorized,
    NdisPortReauthorizing
  } NDIS_PORT_AUTHORIZATION_STATE, *PNDIS_PORT_AUTHORIZATION_STATE;

  typedef enum _NDIS_PORT_CONTROL_STATE {
    NdisPortControlStateUnknown,
    NdisPortControlStateControlled,
    NdisPortControlStateUncontrolled
  } NDIS_PORT_CONTROL_STATE, *PNDIS_PORT_CONTROL_STATE;

  typedef NDIS_PORT_CONTROL_STATE NDIS_PORT_CONTROLL_STATE;
  typedef PNDIS_PORT_CONTROL_STATE PNDIS_PORT_CONTROLL_STATE;

  typedef struct _NDIS_PORT_AUTHENTICATION_PARAMETERS {
    NDIS_OBJECT_HEADER Header;
    NDIS_PORT_CONTROL_STATE SendControlState;
    NDIS_PORT_CONTROL_STATE RcvControlState;
    NDIS_PORT_AUTHORIZATION_STATE SendAuthorizationState;
    NDIS_PORT_AUTHORIZATION_STATE RcvAuthorizationState;
  } NDIS_PORT_AUTHENTICATION_PARAMETERS, *PNDIS_PORT_AUTHENTICATION_PARAMETERS;

  typedef enum _NDIS_NETWORK_CHANGE_TYPE {
    NdisPossibleNetworkChange = 1,
    NdisDefinitelyNetworkChange,
    NdisNetworkChangeFromMediaConnect,
    NdisNetworkChangeMax
  } NDIS_NETWORK_CHANGE_TYPE, *PNDIS_NETWORK_CHANGE_TYPE;

  typedef struct _NDIS_WMI_METHOD_HEADER {
    NDIS_OBJECT_HEADER Header;
    NDIS_PORT_NUMBER PortNumber;
    NET_LUID NetLuid;
    ULONG64 RequestId;
    ULONG Timeout;
    UCHAR Padding[4];
  } NDIS_WMI_METHOD_HEADER, *PNDIS_WMI_METHOD_HEADER;

  typedef struct _NDIS_WMI_SET_HEADER {
    NDIS_OBJECT_HEADER Header;
    NDIS_PORT_NUMBER PortNumber;
    NET_LUID NetLuid;
    ULONG64 RequestId;
    ULONG Timeout;
    UCHAR Padding[4];
  } NDIS_WMI_SET_HEADER, *PNDIS_WMI_SET_HEADER;

  typedef struct _NDIS_WMI_EVENT_HEADER {
    NDIS_OBJECT_HEADER Header;
    NET_IFINDEX IfIndex;
    NET_LUID NetLuid;
    ULONG64 RequestId;
    NDIS_PORT_NUMBER PortNumber;
    ULONG DeviceNameLength;
    ULONG DeviceNameOffset;
    UCHAR Padding[4];
  } NDIS_WMI_EVENT_HEADER, *PNDIS_WMI_EVENT_HEADER;

  typedef struct _NDIS_WMI_ENUM_ADAPTER {
    NDIS_OBJECT_HEADER Header;
    NET_IFINDEX IfIndex;
    NET_LUID NetLuid;
    USHORT DeviceNameLength;
    CHAR DeviceName[1];
  } NDIS_WMI_ENUM_ADAPTER, *PNDIS_WMI_ENUM_ADAPTER;

#if NDIS_SUPPORT_NDIS61
#define NDIS_HD_SPLIT_COMBINE_ALL_HEADERS 0x00000001

#define NDIS_HD_SPLIT_CAPS_SUPPORTS_HEADER_DATA_SPLIT 0x00000001
#define NDIS_HD_SPLIT_CAPS_SUPPORTS_IPV4_OPTIONS 0x00000002
#define NDIS_HD_SPLIT_CAPS_SUPPORTS_IPV6_EXTENSION_HEADERS 0x00000004
#define NDIS_HD_SPLIT_CAPS_SUPPORTS_TCP_OPTIONS 0x00000008

#define NDIS_HD_SPLIT_ENABLE_HEADER_DATA_SPLIT 0x00000001

#define NDIS_HD_SPLIT_PARAMETERS_REVISION_1 1
#define NDIS_HD_SPLIT_CURRENT_CONFIG_REVISION_1 1

#define NDIS_SIZEOF_HD_SPLIT_PARAMETERS_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_HD_SPLIT_PARAMETERS, HDSplitCombineFlags)
#define NDIS_SIZEOF_HD_SPLIT_CURRENT_CONFIG_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_HD_SPLIT_CURRENT_CONFIG, MaxHeaderSize)

  typedef struct _NDIS_HD_SPLIT_PARAMETERS {
    NDIS_OBJECT_HEADER Header;
    ULONG HDSplitCombineFlags;
  } NDIS_HD_SPLIT_PARAMETERS, *PNDIS_HD_SPLIT_PARAMETERS;

  typedef struct _NDIS_HD_SPLIT_CURRENT_CONFIG {
    NDIS_OBJECT_HEADER Header;
    ULONG HardwareCapabilities;
    ULONG CurrentCapabilities;
    ULONG HDSplitFlags;
    ULONG HDSplitCombineFlags;
    ULONG BackfillSize;
    ULONG MaxHeaderSize;
  } NDIS_HD_SPLIT_CURRENT_CONFIG, *PNDIS_HD_SPLIT_CURRENT_CONFIG;
#endif

#define NDIS_WMI_OUTPUT_INFO_REVISION_1 1

  typedef struct NDIS_WMI_OUTPUT_INFO {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    UCHAR SupportedRevision;
    ULONG DataOffset;
  } NDIS_WMI_OUTPUT_INFO, *PNDIS_WMI_OUTPUT_INFO;

#if NDIS_SUPPORT_NDIS620
#define NDIS_PM_WOL_BITMAP_PATTERN_SUPPORTED 0x00000001
#define NDIS_PM_WOL_MAGIC_PACKET_SUPPORTED 0x00000002
#define NDIS_PM_WOL_IPV4_TCP_SYN_SUPPORTED 0x00000004
#define NDIS_PM_WOL_IPV6_TCP_SYN_SUPPORTED 0x00000008
#define NDIS_PM_WOL_IPV4_DEST_ADDR_WILDCARD_SUPPORTED 0x00000200
#define NDIS_PM_WOL_IPV6_DEST_ADDR_WILDCARD_SUPPORTED 0x00000800
#define NDIS_PM_WOL_EAPOL_REQUEST_ID_MESSAGE_SUPPORTED 0x00010000

#define NDIS_PM_PROTOCOL_OFFLOAD_ARP_SUPPORTED 0x00000001
#define NDIS_PM_PROTOCOL_OFFLOAD_NS_SUPPORTED 0x00000002
#define NDIS_PM_PROTOCOL_OFFLOAD_80211_RSN_REKEY_SUPPORTED 0x00000080

#if NDIS_SUPPORT_NDIS630
#define NDIS_PM_WAKE_ON_MEDIA_CONNECT_SUPPORTED 0x00000001
#define NDIS_PM_WAKE_ON_MEDIA_DISCONNECT_SUPPORTED 0x00000002

#define NDIS_WLAN_WAKE_ON_NLO_DISCOVERY_SUPPORTED 0x00000001
#define NDIS_WLAN_WAKE_ON_AP_ASSOCIATION_LOST_SUPPORTED 0x00000002
#define NDIS_WLAN_WAKE_ON_GTK_HANDSHAKE_ERROR_SUPPORTED 0x00000004
#define NDIS_WLAN_WAKE_ON_4WAY_HANDSHAKE_REQUEST_SUPPORTED 0x00000008

#define NDIS_WWAN_WAKE_ON_REGISTER_STATE_SUPPORTED 0x00000001
#define NDIS_WWAN_WAKE_ON_SMS_RECEIVE_SUPPORTED 0x00000002
#define NDIS_WWAN_WAKE_ON_USSD_RECEIVE_SUPPORTED 0x00000004

#define NDIS_PM_WAKE_PACKET_INDICATION_SUPPORTED 0x00000001
#define NDIS_PM_SELECTIVE_SUSPEND_SUPPORTED 0x00000002
#endif

#define NDIS_PM_WOL_BITMAP_PATTERN_ENABLED 0x00000001
#define NDIS_PM_WOL_MAGIC_PACKET_ENABLED 0x00000002
#define NDIS_PM_WOL_IPV4_TCP_SYN_ENABLED 0x00000004
#define NDIS_PM_WOL_IPV6_TCP_SYN_ENABLED 0x00000008
#define NDIS_PM_WOL_IPV4_DEST_ADDR_WILDCARD_ENABLED 0x00000200
#define NDIS_PM_WOL_IPV6_DEST_ADDR_WILDCARD_ENABLED 0x00000800
#define NDIS_PM_WOL_EAPOL_REQUEST_ID_MESSAGE_ENABLED 0x00010000

#define NDIS_PM_PROTOCOL_OFFLOAD_ARP_ENABLED 0x00000001
#define NDIS_PM_PROTOCOL_OFFLOAD_NS_ENABLED 0x00000002
#define NDIS_PM_PROTOCOL_OFFLOAD_80211_RSN_REKEY_ENABLED 0x00000080

#define NDIS_PM_WAKE_ON_LINK_CHANGE_ENABLED 0x1
#if NDIS_SUPPORT_NDIS630
#define NDIS_PM_WAKE_ON_MEDIA_DISCONNECT_ENABLED 0x2
#define NDIS_PM_SELECTIVE_SUSPEND_ENABLED 0x10

#define NDIS_WLAN_WAKE_ON_NLO_DISCOVERY_ENABLED 0x1
#define NDIS_WLAN_WAKE_ON_AP_ASSOCIATION_LOST_ENABLED 0x2
#define NDIS_WLAN_WAKE_ON_GTK_HANDSHAKE_ERROR_ENABLED 0x4
#define NDIS_WLAN_WAKE_ON_4WAY_HANDSHAKE_REQUEST_ENABLED 0x8

#define NDIS_WWAN_WAKE_ON_REGISTER_STATE_ENABLED 0x1
#define NDIS_WWAN_WAKE_ON_SMS_RECEIVE_ENABLED 0x2
#define NDIS_WWAN_WAKE_ON_USSD_RECEIVE_ENABLED 0x4
#endif

#define NDIS_PM_WOL_PRIORITY_LOWEST 0xffffffff
#define NDIS_PM_WOL_PRIORITY_NORMAL 0x10000000
#define NDIS_PM_WOL_PRIORITY_HIGHEST 0x00000001

#define NDIS_PM_PROTOCOL_OFFLOAD_PRIORITY_LOWEST 0xffffffff
#define NDIS_PM_PROTOCOL_OFFLOAD_PRIORITY_NORMAL 0x10000000
#define NDIS_PM_PROTOCOL_OFFLOAD_PRIORITY_HIGHEST 0x00000001

#define NDIS_PM_MAX_STRING_SIZE 64

#define EAPOL_REQUEST_ID_WOL_FLAG_MUST_ENCRYPT 0x00000001

#define NDIS_PM_MAX_PATTERN_ID 0x0000ffff

#define NDIS_PM_PRIVATE_PATTERN_ID 0x00000001

#define DOT11_RSN_KEK_LENGTH 16
#define DOT11_RSN_KCK_LENGTH 16


#define NDIS_RECEIVE_FILTER_MAC_HEADER_SUPPORTED 0x00000001
#define NDIS_RECEIVE_FILTER_IPV4_HEADER_SUPPORTED 0x00000002
#define NDIS_RECEIVE_FILTER_IPV6_HEADER_SUPPORTED 0x00000004
#define NDIS_RECEIVE_FILTER_ARP_HEADER_SUPPORTED 0x00000008
#define NDIS_RECEIVE_FILTER_UDP_HEADER_SUPPORTED 0x00000010

#define NDIS_RECEIVE_FILTER_MAC_HEADER_DEST_ADDR_SUPPORTED 0x00000001
#define NDIS_RECEIVE_FILTER_MAC_HEADER_SOURCE_ADDR_SUPPORTED 0x00000002
#define NDIS_RECEIVE_FILTER_MAC_HEADER_PROTOCOL_SUPPORTED 0x00000004
#define NDIS_RECEIVE_FILTER_MAC_HEADER_VLAN_ID_SUPPORTED 0x00000008
#define NDIS_RECEIVE_FILTER_MAC_HEADER_PRIORITY_SUPPORTED 0x00000010
#define NDIS_RECEIVE_FILTER_MAC_HEADER_PACKET_TYPE_SUPPORTED 0x00000020

#define NDIS_RECEIVE_FILTER_ARP_HEADER_OPERATION_SUPPORTED 0x1
#define NDIS_RECEIVE_FILTER_ARP_HEADER_SPA_SUPPORTED 0x2
#define NDIS_RECEIVE_FILTER_ARP_HEADER_TPA_SUPPORTED 0x4

#define NDIS_RECEIVE_FILTER_IPV4_HEADER_PROTOCOL_SUPPORTED 0x1
#define NDIS_RECEIVE_FILTER_IPV6_HEADER_PROTOCOL_SUPPORTED 0x1
#define NDIS_RECEIVE_FILTER_UDP_HEADER_DEST_PORT_SUPPORTED 0x1

#define NDIS_RECEIVE_FILTER_TEST_HEADER_FIELD_EQUAL_SUPPORTED 0x00000001
#define NDIS_RECEIVE_FILTER_TEST_HEADER_FIELD_MASK_EQUAL_SUPPORTED 0x00000002
#define NDIS_RECEIVE_FILTER_TEST_HEADER_FIELD_NOT_EQUAL_SUPPORTED 0x00000004

#define NDIS_RECEIVE_FILTER_MSI_X_SUPPORTED 0x00000001
#define NDIS_RECEIVE_FILTER_VM_QUEUE_SUPPORTED 0x00000002
#define NDIS_RECEIVE_FILTER_LOOKAHEAD_SPLIT_SUPPORTED 0x00000004
#if NDIS_SUPPORT_NDIS630
#define NDIS_RECEIVE_FILTER_DYNAMIC_PROCESSOR_AFFINITY_CHANGE_SUPPORTED 0x00000008
#define NDIS_RECEIVE_FILTER_INTERRUPT_VECTOR_COALESCING_SUPPORTED 0x00000010
#define NDIS_RECEIVE_FILTER_ANY_VLAN_SUPPORTED 0x00000020
#define NDIS_RECEIVE_FILTER_IMPLAT_MIN_OF_QUEUES_MODE 0x00000040
#define NDIS_RECEIVE_FILTER_IMPLAT_SUM_OF_QUEUES_MODE 0x00000080
#define NDIS_RECEIVE_FILTER_PACKET_COALESCING_SUPPORTED_ON_DEFAULT_QUEUE 0x00000100
#endif

#define NDIS_RECEIVE_FILTER_VMQ_FILTERS_ENABLED 0x00000001
#define NDIS_RECEIVE_FILTER_PACKET_COALESCING_FILTERS_ENABLED 0x00000002

#define NDIS_RECEIVE_FILTER_VM_QUEUES_ENABLED 0x00000001

#if NDIS_SUPPORT_NDIS630
#define NDIS_NIC_SWITCH_CAPS_VLAN_SUPPORTED 0x00000001
#define NDIS_NIC_SWITCH_CAPS_PER_VPORT_INTERRUPT_MODERATION_SUPPORTED 0x00000002
#define NDIS_NIC_SWITCH_CAPS_ASYMMETRIC_QUEUE_PAIRS_FOR_NONDEFAULT_VPORT_SUPPORTED 0x00000004
#define NDIS_NIC_SWITCH_CAPS_VF_RSS_SUPPORTED 0x00000008
#define NDIS_NIC_SWITCH_CAPS_SINGLE_VPORT_POOL 0x00000010
#endif


#define NDIS_DEFAULT_RECEIVE_QUEUE_ID 0
#define NDIS_DEFAULT_RECEIVE_QUEUE_GROUP_ID 0
#define NDIS_DEFAULT_RECEIVE_FILTER_ID 0

#define NDIS_RECEIVE_FILTER_FIELD_MAC_HEADER_VLAN_UNTAGGED_OR_ZERO 0x00000001
#define NDIS_RECEIVE_FILTER_PACKET_ENCAPSULATION_GRE 0x00000002

#define NDIS_RECEIVE_QUEUE_PARAMETERS_PER_QUEUE_RECEIVE_INDICATION 0x00000001
#define NDIS_RECEIVE_QUEUE_PARAMETERS_LOOKAHEAD_SPLIT_REQUIRED 0x00000002
#define NDIS_RECEIVE_QUEUE_PARAMETERS_FLAGS_CHANGED 0x00010000
#define NDIS_RECEIVE_QUEUE_PARAMETERS_PROCESSOR_AFFINITY_CHANGED 0x00020000
#define NDIS_RECEIVE_QUEUE_PARAMETERS_SUGGESTED_RECV_BUFFER_NUMBERS_CHANGED 0x00040000
#define NDIS_RECEIVE_QUEUE_PARAMETERS_NAME_CHANGED 0x00080000
#if NDIS_SUPPORT_NDIS630
#define NDIS_RECEIVE_QUEUE_PARAMETERS_INTERRUPT_COALESCING_DOMAIN_ID_CHANGED 0x00100000
#endif

#define NDIS_RECEIVE_QUEUE_PARAMETERS_CHANGE_MASK 0xffff0000

#if NDIS_SUPPORT_NDIS630
#define NDIS_RECEIVE_FILTER_INFO_ARRAY_VPORT_ID_SPECIFIED 0x00000001
#endif

#define NDIS_PM_CAPABILITIES_REVISION_1 1
#define NDIS_PM_PARAMETERS_REVISION_1 1
#define NDIS_PM_WOL_PATTERN_REVISION_1 1
#define NDIS_PM_PROTOCOL_OFFLOAD_REVISION_1 1
#define NDIS_WMI_PM_ADMIN_CONFIG_REVISION_1 1
#define NDIS_WMI_PM_ACTIVE_CAPABILITIES_REVISION_1 1
#define NDIS_RECEIVE_FILTER_CAPABILITIES_REVISION_1 1
#define NDIS_NIC_SWITCH_CAPABILITIES_REVISION_1 1
#define NDIS_RECEIVE_FILTER_GLOBAL_PARAMETERS_REVISION_1 1
#define NDIS_RECEIVE_FILTER_FIELD_PARAMETERS_REVISION_1 1
#define NDIS_RECEIVE_FILTER_PARAMETERS_REVISION_1 1
#define NDIS_RECEIVE_FILTER_CLEAR_PARAMETERS_REVISION_1 1
#define NDIS_RECEIVE_QUEUE_PARAMETERS_REVISION_1 1
#define NDIS_RECEIVE_QUEUE_FREE_PARAMETERS_REVISION_1 1
#define NDIS_RECEIVE_QUEUE_INFO_REVISION_1 1
#define NDIS_RECEIVE_QUEUE_INFO_ARRAY_REVISION_1 1
#define NDIS_RECEIVE_FILTER_INFO_REVISION_1 1
#define NDIS_RECEIVE_FILTER_INFO_ARRAY_REVISION_1 1
#define NDIS_RECEIVE_QUEUE_ALLOCATION_COMPLETE_PARAMETERS_REVISION_1 1
#define NDIS_RECEIVE_QUEUE_ALLOCATION_COMPLETE_ARRAY_REVISION_1 1
#if NDIS_SUPPORT_NDIS630
#define NDIS_PM_CAPABILITIES_REVISION_2 2
#define NDIS_PM_PARAMETERS_REVISION_2 2
#define NDIS_PM_WOL_PATTERN_REVISION_2 2
#define NDIS_PM_WAKE_REASON_REVISION_1 1
#define NDIS_PM_WAKE_PACKET_REVISION_1 1
#define NDIS_RECEIVE_FILTER_CAPABILITIES_REVISION_2 2
#define NDIS_NIC_SWITCH_CAPABILITIES_REVISION_2 2
#define NDIS_RECEIVE_FILTER_FIELD_PARAMETERS_REVISION_2 2
#define NDIS_RECEIVE_FILTER_PARAMETERS_REVISION_2 2
#define NDIS_RECEIVE_QUEUE_PARAMETERS_REVISION_2 2
#define NDIS_RECEIVE_FILTER_INFO_ARRAY_REVISION_2 2
#define NDIS_RECEIVE_QUEUE_INFO_REVISION_2 2
#endif

#define NDIS_SIZEOF_WMI_OUTPUT_INFO_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_WMI_OUTPUT_INFO, DataOffset)
#define NDIS_SIZEOF_NDIS_PM_CAPABILITIES_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_PM_CAPABILITIES, MinLinkChangeWakeUp)
#define NDIS_SIZEOF_NDIS_PM_PARAMETERS_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_PM_PARAMETERS, WakeUpFlags)
#define NDIS_SIZEOF_NDIS_PM_WOL_PATTERN_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_PM_WOL_PATTERN, WoLPattern)
#define NDIS_SIZEOF_NDIS_PM_PROTOCOL_OFFLOAD_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_PM_PROTOCOL_OFFLOAD, ProtocolOffloadParameters)
#define NDIS_SIZEOF_WMI_PM_ADMIN_CONFIG_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_WMI_PM_ADMIN_CONFIG, PMWiFiRekeyOffload)
#define NDIS_SIZEOF_WMI_PM_ACTIVE_CAPABILITIES_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_WMI_PM_ACTIVE_CAPABILITIES, PMWiFiRekeyOffload)
#define NDIS_SIZEOF_RECEIVE_FILTER_CAPABILITIES_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_RECEIVE_FILTER_CAPABILITIES, MaxLookaheadSplitSize)
#define NDIS_SIZEOF_NIC_SWITCH_CAPABILITIES_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_NIC_SWITCH_CAPABILITIES, NdisReserved3)
#define NDIS_SIZEOF_RECEIVE_FILTER_GLOBAL_PARAMETERS_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_RECEIVE_FILTER_GLOBAL_PARAMETERS, EnabledQueueTypes)
#define NDIS_SIZEOF_RECEIVE_FILTER_FIELD_PARAMETERS_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_RECEIVE_FILTER_FIELD_PARAMETERS, ResultValue)
#define NDIS_SIZEOF_RECEIVE_FILTER_PARAMETERS_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_RECEIVE_FILTER_PARAMETERS, RequestedFilterIdBitCount)
#define NDIS_SIZEOF_RECEIVE_FILTER_CLEAR_PARAMETERS_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_RECEIVE_FILTER_CLEAR_PARAMETERS, FilterId)
#define NDIS_SIZEOF_RECEIVE_QUEUE_FREE_PARAMETERS_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_RECEIVE_QUEUE_FREE_PARAMETERS, QueueId)
#define NDIS_SIZEOF_RECEIVE_QUEUE_PARAMETERS_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_RECEIVE_QUEUE_PARAMETERS, QueueName)
#define NDIS_SIZEOF_RECEIVE_QUEUE_INFO_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_RECEIVE_QUEUE_INFO, QueueName)
#define NDIS_SIZEOF_RECEIVE_QUEUE_INFO_ARRAY_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_RECEIVE_QUEUE_INFO_ARRAY, ElementSize)
#define NDIS_SIZEOF_RECEIVE_FILTER_INFO_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_RECEIVE_FILTER_INFO, FilterId)
#define NDIS_SIZEOF_RECEIVE_QUEUE_ALLOCATION_COMPLETE_ARRAY_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_RECEIVE_QUEUE_ALLOCATION_COMPLETE_ARRAY, ElementSize)
#define NDIS_SIZEOF_RECEIVE_QUEUE_ALLOCATION_COMPLETE_PARAMETERS_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_RECEIVE_QUEUE_ALLOCATION_COMPLETE_PARAMETERS, CompletionStatus)
#define NDIS_SIZEOF_RECEIVE_FILTER_INFO_ARRAY_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_RECEIVE_FILTER_INFO_ARRAY, ElementSize)
#if NDIS_SUPPORT_NDIS630
#define NDIS_SIZEOF_NDIS_PM_CAPABILITIES_REVISION_2 RTL_SIZEOF_THROUGH_FIELD (NDIS_PM_CAPABILITIES, MediaSpecificWakeUpEvents)
#define NDIS_SIZEOF_NDIS_PM_PARAMETERS_REVISION_2 RTL_SIZEOF_THROUGH_FIELD (NDIS_PM_PARAMETERS, MediaSpecificWakeUpEvents)
#define NDIS_SIZEOF_NDIS_PM_WOL_PATTERN_REVISION_2 RTL_SIZEOF_THROUGH_FIELD (NDIS_PM_WOL_PATTERN, WoLPattern)
#define NDIS_SIZEOF_PM_WAKE_REASON_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_PM_WAKE_REASON, InfoBufferSize)
#define NDIS_SIZEOF_PM_WAKE_PACKET_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_PM_WAKE_PACKET, SavedPacketOffset)
#define NDIS_SIZEOF_RECEIVE_FILTER_CAPABILITIES_REVISION_2 RTL_SIZEOF_THROUGH_FIELD (NDIS_RECEIVE_FILTER_CAPABILITIES, NdisReserved)
#define NDIS_SIZEOF_NIC_SWITCH_CAPABILITIES_REVISION_2 RTL_SIZEOF_THROUGH_FIELD (NDIS_NIC_SWITCH_CAPABILITIES, NdisReserved17)
#define NDIS_SIZEOF_RECEIVE_FILTER_FIELD_PARAMETERS_REVISION_2 RTL_SIZEOF_THROUGH_FIELD (NDIS_RECEIVE_FILTER_FIELD_PARAMETERS, ResultValue)
#define NDIS_SIZEOF_RECEIVE_FILTER_PARAMETERS_REVISION_2 RTL_SIZEOF_THROUGH_FIELD (NDIS_RECEIVE_FILTER_PARAMETERS, VPortId)
#define NDIS_SIZEOF_RECEIVE_FILTER_INFO_ARRAY_REVISION_2 RTL_SIZEOF_THROUGH_FIELD (NDIS_RECEIVE_FILTER_INFO_ARRAY, VPortId)
#define NDIS_SIZEOF_RECEIVE_QUEUE_INFO_REVISION_2 RTL_SIZEOF_THROUGH_FIELD (NDIS_RECEIVE_QUEUE_INFO, InterruptCoalescingDomainId)
#define NDIS_SIZEOF_RECEIVE_QUEUE_PARAMETERS_REVISION_2 RTL_SIZEOF_THROUGH_FIELD (NDIS_RECEIVE_QUEUE_PARAMETERS, InterruptCoalescingDomainId)
#endif

  typedef enum _NDIS_PM_WOL_PACKET {
    NdisPMWoLPacketUnspecified,
    NdisPMWoLPacketBitmapPattern,
    NdisPMWoLPacketMagicPacket,
    NdisPMWoLPacketIPv4TcpSyn,
    NdisPMWoLPacketIPv6TcpSyn,
    NdisPMWoLPacketEapolRequestIdMessage,
    NdisPMWoLPacketMaximum
  } NDIS_PM_WOL_PACKET, *PNDIS_PM_WOL_PACKET;

  typedef enum _NDIS_PM_PROTOCOL_OFFLOAD_TYPE {
    NdisPMProtocolOffloadIdUnspecified,
    NdisPMProtocolOffloadIdIPv4ARP,
    NdisPMProtocolOffloadIdIPv6NS,
    NdisPMProtocolOffload80211RSNRekey,
    NdisPMProtocolOffloadIdMaximum
  } NDIS_PM_PROTOCOL_OFFLOAD_TYPE, *PNDIS_PM_PROTOCOL_OFFLOAD_TYPE;

  typedef struct _NDIS_PM_COUNTED_STRING {
    USHORT Length;
    WCHAR String[NDIS_PM_MAX_STRING_SIZE + 1];
  } NDIS_PM_COUNTED_STRING, *PNDIS_PM_COUNTED_STRING;

  typedef struct _NDIS_PM_CAPABILITIES {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    ULONG SupportedWoLPacketPatterns;
    ULONG NumTotalWoLPatterns;
    ULONG MaxWoLPatternSize;
    ULONG MaxWoLPatternOffset;
    ULONG MaxWoLPacketSaveBuffer;
    ULONG SupportedProtocolOffloads;
    ULONG NumArpOffloadIPv4Addresses;
    ULONG NumNSOffloadIPv6Addresses;
    NDIS_DEVICE_POWER_STATE MinMagicPacketWakeUp;
    NDIS_DEVICE_POWER_STATE MinPatternWakeUp;
    NDIS_DEVICE_POWER_STATE MinLinkChangeWakeUp;
#if NDIS_SUPPORT_NDIS630
    ULONG SupportedWakeUpEvents;
    ULONG MediaSpecificWakeUpEvents;
#endif
  } NDIS_PM_CAPABILITIES, *PNDIS_PM_CAPABILITIES;

  typedef struct _NDIS_PM_PARAMETERS {
    NDIS_OBJECT_HEADER Header;
    ULONG EnabledWoLPacketPatterns;
    ULONG EnabledProtocolOffloads;
    ULONG WakeUpFlags;
#if NDIS_SUPPORT_NDIS630
    ULONG MediaSpecificWakeUpEvents;
#endif
  } NDIS_PM_PARAMETERS, *PNDIS_PM_PARAMETERS;

  typedef struct _NDIS_PM_WOL_PATTERN {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    ULONG Priority;
    NDIS_PM_WOL_PACKET WoLPacketType;
    NDIS_PM_COUNTED_STRING FriendlyName;
    ULONG PatternId;
    ULONG NextWoLPatternOffset;
    union _WOL_PATTERN {
      struct _IPV4_TCP_SYN_WOL_PACKET_PARAMETERS {
	ULONG Flags;
	UCHAR IPv4SourceAddress[4];
	UCHAR IPv4DestAddress[4];
	USHORT TCPSourcePortNumber;
	USHORT TCPDestPortNumber;
      } IPv4TcpSynParameters;
      struct _IPV6_TCP_SYN_WOL_PACKET_PARAMETERS {
	ULONG Flags;
	UCHAR IPv6SourceAddress[16];
	UCHAR IPv6DestAddress[16];
	USHORT TCPSourcePortNumber;
	USHORT TCPDestPortNumber;
      } IPv6TcpSynParameters;
      struct _EAPOL_REQUEST_ID_MESSAGE_WOL_PACKET_PARAMETERS {
	ULONG Flags;
      } EapolRequestIdMessageParameters;
      struct _WOL_BITMAP_PATTERN {
	ULONG Flags;
	ULONG MaskOffset;
	ULONG MaskSize;
	ULONG PatternOffset;
	ULONG PatternSize;
      } WoLBitMapPattern;
    } WoLPattern;
  } NDIS_PM_WOL_PATTERN, *PNDIS_PM_WOL_PATTERN;

  typedef struct _NDIS_PM_PROTOCOL_OFFLOAD {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    ULONG Priority;
    NDIS_PM_PROTOCOL_OFFLOAD_TYPE ProtocolOffloadType;
    NDIS_PM_COUNTED_STRING FriendlyName;
    ULONG ProtocolOffloadId;
    ULONG NextProtocolOffloadOffset;
    union _PROTOCOL_OFFLOAD_PARAMETERS {
      struct _IPV4_ARP_PARAMETERS {
	ULONG Flags;
	UCHAR RemoteIPv4Address[4];
	UCHAR HostIPv4Address[4];
	UCHAR MacAddress[6];
      } IPv4ARPParameters;
      struct _IPV6_NS_PARAMETERS {
	ULONG Flags;
	UCHAR RemoteIPv6Address[16];
	UCHAR SolicitedNodeIPv6Address[16];
	UCHAR MacAddress[6];
	UCHAR TargetIPv6Addresses[2][16];
      } IPv6NSParameters;
      struct _DOT11_RSN_REKEY_PARAMETERS {
	ULONG Flags;
	UCHAR KCK[DOT11_RSN_KCK_LENGTH];
	UCHAR KEK[DOT11_RSN_KEK_LENGTH];
	ULONGLONG KeyReplayCounter;
      } Dot11RSNRekeyParameters;
    } ProtocolOffloadParameters;
  } NDIS_PM_PROTOCOL_OFFLOAD, *PNDIS_PM_PROTOCOL_OFFLOAD;

#if NDIS_SUPPORT_NDIS630
  typedef enum _NDIS_PM_WAKE_REASON_TYPE {
    NdisWakeReasonUnspecified = 0x0000,
    NdisWakeReasonPacket = 0x0001,
    NdisWakeReasonMediaDisconnect = 0x0002,
    NdisWakeReasonMediaConnect = 0x0003,
    NdisWakeReasonWlanNLODiscovery = 0x1000,
    NdisWakeReasonWlanAPAssociationLost = 0x1001,
    NdisWakeReasonWlanGTKHandshakeError = 0x1002,
    NdisWakeReasonWlan4WayHandshakeRequest = 0x1003,
    NdisWakeReasonWwanRegisterState = 0x2000,
    NdisWakeReasonWwanSMSReceive = 0x2001,
    NdisWakeReasonWwanUSSDReceive = 0x2002
  } NDIS_PM_WAKE_REASON_TYPE, *PNDIS_PM_WAKE_REASON_TYPE;

  typedef struct _NDIS_PM_WAKE_REASON {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    NDIS_PM_WAKE_REASON_TYPE WakeReason;
    ULONG InfoBufferOffset;
    ULONG InfoBufferSize;
  } NDIS_PM_WAKE_REASON, *PNDIS_PM_WAKE_REASON;

  typedef struct _NDIS_PM_WAKE_PACKET {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    ULONG PatternId;
    NDIS_PM_COUNTED_STRING PatternFriendlyName;
    ULONG OriginalPacketSize;
    ULONG SavedPacketSize;
    ULONG SavedPacketOffset;
  } NDIS_PM_WAKE_PACKET, *PNDIS_PM_WAKE_PACKET;
#endif

  typedef enum _NDIS_PM_ADMIN_CONFIG_STATE {
    NdisPMAdminConfigUnspecified = 0,
    NdisPMAdminConfigDisabled = 1,
    NdisPMAdminConfigEnabled = 2
  } NDIS_PM_ADMIN_CONFIG_STATE, *PNDIS_PM_ADMIN_CONFIG_STATE;

  typedef struct _NDIS_WMI_PM_ADMIN_CONFIG {
    NDIS_OBJECT_HEADER Header;
    NDIS_PM_ADMIN_CONFIG_STATE WakeOnPattern;
    NDIS_PM_ADMIN_CONFIG_STATE WakeOnMagicPacket;
    NDIS_PM_ADMIN_CONFIG_STATE DeviceSleepOnDisconnect;
    NDIS_PM_ADMIN_CONFIG_STATE PMARPOffload;
    NDIS_PM_ADMIN_CONFIG_STATE PMNSOffload;
    NDIS_PM_ADMIN_CONFIG_STATE PMWiFiRekeyOffload;
  } NDIS_WMI_PM_ADMIN_CONFIG, *PNDIS_WMI_PM_ADMIN_CONFIG;

  typedef enum _NDIS_PM_CAPABILITY_STATE {
    NdisPMAdminConfigUnsupported = 0,
    NdisPMAdminConfigInactive = 1,
    NdisPMAdminConfigActive = 2
  } NDIS_PM_CAPABILITY_STATE, *PNDIS_PM_CAPABILITY_STATE;

  typedef struct _NDIS_WMI_PM_ACTIVE_CAPABILITIES {
    NDIS_OBJECT_HEADER Header;
    NDIS_PM_CAPABILITY_STATE WakeOnPattern;
    NDIS_PM_CAPABILITY_STATE WakeOnMagicPacket;
    NDIS_PM_CAPABILITY_STATE DeviceSleepOnDisconnect;
    NDIS_PM_CAPABILITY_STATE PMARPOffload;
    NDIS_PM_CAPABILITY_STATE PMNSOffload;
    NDIS_PM_CAPABILITY_STATE PMWiFiRekeyOffload;
  } NDIS_WMI_PM_ACTIVE_CAPABILITIES, *PNDIS_WMI_PM_ACTIVE_CAPABILITIES;

  typedef struct _NDIS_RECEIVE_FILTER_CAPABILITIES {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    ULONG EnabledFilterTypes;
    ULONG EnabledQueueTypes;
    ULONG NumQueues;
    ULONG SupportedQueueProperties;
    ULONG SupportedFilterTests;
    ULONG SupportedHeaders;
    ULONG SupportedMacHeaderFields;
    ULONG MaxMacHeaderFilters;
    ULONG MaxQueueGroups;
    ULONG MaxQueuesPerQueueGroup;
    ULONG MinLookaheadSplitSize;
    ULONG MaxLookaheadSplitSize;
#if NDIS_SUPPORT_NDIS630
    ULONG SupportedARPHeaderFields;
    ULONG SupportedIPv4HeaderFields;
    ULONG SupportedIPv6HeaderFields;
    ULONG SupportedUdpHeaderFields;
    ULONG MaxFieldTestsPerPacketCoalescingFilter;
    ULONG MaxPacketCoalescingFilters;
    ULONG NdisReserved;
#endif
  } NDIS_RECEIVE_FILTER_CAPABILITIES, *PNDIS_RECEIVE_FILTER_CAPABILITIES;

  typedef struct _NDIS_NIC_SWITCH_CAPABILITIES {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    ULONG NdisReserved1;
    ULONG NumTotalMacAddresses;
    ULONG NumMacAddressesPerPort;
    ULONG NumVlansPerPort;
    ULONG NdisReserved2;
    ULONG NdisReserved3;
#if NDIS_SUPPORT_NDIS630
    ULONG NicSwitchCapabilities;
    ULONG MaxNumSwitches;
    ULONG MaxNumVPorts;
    ULONG NdisReserved4;
    ULONG MaxNumVFs;
    ULONG MaxNumQueuePairs;
    ULONG NdisReserved5;
    ULONG NdisReserved6;
    ULONG NdisReserved7;
    ULONG MaxNumQueuePairsPerNonDefaultVPort;
    ULONG NdisReserved8;
    ULONG NdisReserved9;
    ULONG NdisReserved10;
    ULONG NdisReserved11;
    ULONG NdisReserved12;
    ULONG MaxNumMacAddresses;
    ULONG NdisReserved13;
    ULONG NdisReserved14;
    ULONG NdisReserved15;
    ULONG NdisReserved16;
    ULONG NdisReserved17;
#endif
  } NDIS_NIC_SWITCH_CAPABILITIES, *PNDIS_NIC_SWITCH_CAPABILITIES;

  typedef struct _NDIS_RECEIVE_FILTER_GLOBAL_PARAMETERS {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    ULONG EnabledFilterTypes;
    ULONG EnabledQueueTypes;
  } NDIS_RECEIVE_FILTER_GLOBAL_PARAMETERS, *PNDIS_RECEIVE_FILTER_GLOBAL_PARAMETERS;

  typedef ULONG NDIS_RECEIVE_QUEUE_ID, *PNDIS_RECEIVE_QUEUE_ID;
  typedef ULONG NDIS_RECEIVE_QUEUE_GROUP_ID, *PNDIS_RECEIVE_QUEUE_GROUP_ID;
  typedef ULONG NDIS_RECEIVE_FILTER_ID, *PNDIS_RECEIVE_FILTER_ID;

  typedef enum _NDIS_RECEIVE_FILTER_TYPE {
    NdisReceiveFilterTypeUndefined,
    NdisReceiveFilterTypeVMQueue,
    NdisReceiveFilterTypePacketCoalescing,
    NdisReceiveFilterTypeMaximum
  } NDIS_RECEIVE_FILTER_TYPE, *PNDIS_RECEIVE_FILTER_TYPE;

  typedef enum _NDIS_FRAME_HEADER {
    NdisFrameHeaderUndefined,
    NdisFrameHeaderMac,
    NdisFrameHeaderArp,
    NdisFrameHeaderIPv4,
    NdisFrameHeaderIPv6,
    NdisFrameHeaderUdp,
    NdisFrameHeaderMaximum
  } NDIS_FRAME_HEADER, *PNDIS_FRAME_HEADER;

  typedef enum _NDIS_MAC_HEADER_FIELD {
    NdisMacHeaderFieldUndefined,
    NdisMacHeaderFieldDestinationAddress,
    NdisMacHeaderFieldSourceAddress,
    NdisMacHeaderFieldProtocol,
    NdisMacHeaderFieldVlanId,
    NdisMacHeaderFieldPriority,
    NdisMacHeaderFieldPacketType,
    NdisMacHeaderFieldMaximum
  } NDIS_MAC_HEADER_FIELD, *PNDIS_MAC_HEADER_FIELD;

  typedef enum _NDIS_MAC_PACKET_TYPE {
    NdisMacPacketTypeUndefined = 0,
    NdisMacPacketTypeUnicast = 1,
    NdisMacPacketTypeMulticast = 2,
    NdisMacPacketTypeBroadcast = 3,
    NdisMacPacketTypeMaximum
  } NDIS_MAC_PACKET_TYPE, *PNDIS_MAC_PACKET_TYPE;

  typedef enum _NDIS_ARP_HEADER_FIELD {
    NdisARPHeaderFieldUndefined,
    NdisARPHeaderFieldOperation,
    NdisARPHeaderFieldSPA,
    NdisARPHeaderFieldTPA,
    NdisARPHeaderFieldMaximum
  } NDIS_ARP_HEADER_FIELD, *PNDIS_ARP_HEADER_FIELD;

  typedef enum _NDIS_IPV4_HEADER_FIELD {
    NdisIPv4HeaderFieldUndefined,
    NdisIPv4HeaderFieldProtocol,
    NdisIPv4HeaderFieldMaximum
  } NDIS_IPV4_HEADER_FIELD, *PNDIS_IPV4_HEADER_FIELD;

  typedef enum _NDIS_IPV6_HEADER_FIELD {
    NdisIPv6HeaderFieldUndefined,
    NdisIPv6HeaderFieldProtocol,
    NdisIPv6HeaderFieldMaximum
  } NDIS_IPV6_HEADER_FIELD, *PNDIS_IPV6_HEADER_FIELD;

  typedef enum _NDIS_UDP_HEADER_FIELD {
    NdisUdpHeaderFieldUndefined,
    NdisUdpHeaderFieldDestinationPort,
    NdisUdpHeaderFieldMaximum
  } NDIS_UDP_HEADER_FIELD, *PNDIS_UDP_HEADER_FIELD;

  typedef enum _NDIS_RECEIVE_FILTER_TEST {
    NdisReceiveFilterTestUndefined,
    NdisReceiveFilterTestEqual,
    NdisReceiveFilterTestMaskEqual,
    NdisReceiveFilterTestNotEqual,
    NdisReceiveFilterTestMaximum
  } NDIS_RECEIVE_FILTER_TEST, *PNDIS_RECEIVE_FILTER_TEST;

  typedef struct _NDIS_RECEIVE_FILTER_FIELD_PARAMETERS {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    NDIS_FRAME_HEADER FrameHeader;
    NDIS_RECEIVE_FILTER_TEST ReceiveFilterTest;
    union _HEADER_FIELD {
      NDIS_MAC_HEADER_FIELD MacHeaderField;
      NDIS_ARP_HEADER_FIELD ArpHeaderField;
      NDIS_IPV4_HEADER_FIELD IPv4HeaderField;
      NDIS_IPV6_HEADER_FIELD IPv6HeaderField;
      NDIS_UDP_HEADER_FIELD UdpHeaderField;
    } HeaderField;
    union _FIELD_VALUE {
      UCHAR FieldByteValue;
      USHORT FieldShortValue;
      ULONG FieldLongValue;
      ULONG64 FieldLong64Value;
      UCHAR FieldByteArrayValue[16];
    } FieldValue;
    union _RESULT_VALUE {
      UCHAR ResultByteValue;
      USHORT ResultShortValue;
      ULONG ResultLongValue;
      ULONG64 ResultLong64Value;
      UCHAR ResultByteArrayValue[16];
    } ResultValue;
  } NDIS_RECEIVE_FILTER_FIELD_PARAMETERS, *PNDIS_RECEIVE_FILTER_FIELD_PARAMETERS;

#if NDIS_SUPPORT_NDIS630
  typedef ULONG NDIS_NIC_SWITCH_VPORT_ID, *PNDIS_NIC_SWITCH_VPORT_ID;
#endif

  typedef struct _NDIS_RECEIVE_FILTER_PARAMETERS {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    NDIS_RECEIVE_FILTER_TYPE FilterType;
    NDIS_RECEIVE_QUEUE_ID QueueId;
    NDIS_RECEIVE_FILTER_ID FilterId;
    ULONG FieldParametersArrayOffset;
    ULONG FieldParametersArrayNumElements;
    ULONG FieldParametersArrayElementSize;
    ULONG RequestedFilterIdBitCount;
#if NDIS_SUPPORT_NDIS630
    ULONG MaxCoalescingDelay;
    NDIS_NIC_SWITCH_VPORT_ID VPortId;
#endif
  } NDIS_RECEIVE_FILTER_PARAMETERS, *PNDIS_RECEIVE_FILTER_PARAMETERS;

  typedef struct _NDIS_RECEIVE_FILTER_CLEAR_PARAMETERS {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    NDIS_RECEIVE_QUEUE_ID QueueId;
    NDIS_RECEIVE_FILTER_ID FilterId;
  } NDIS_RECEIVE_FILTER_CLEAR_PARAMETERS, *PNDIS_RECEIVE_FILTER_CLEAR_PARAMETERS;

  typedef enum _NDIS_RECEIVE_QUEUE_TYPE {
    NdisReceiveQueueTypeUnspecified,
    NdisReceiveQueueTypeVMQueue,
    NdisReceiveQueueTypeMaximum
  } NDIS_RECEIVE_QUEUE_TYPE, *PNDIS_RECEIVE_QUEUE_TYPE;

  typedef NDIS_IF_COUNTED_STRING NDIS_QUEUE_NAME, *PNDIS_QUEUE_NAME;
  typedef NDIS_IF_COUNTED_STRING NDIS_VM_NAME, *PNDIS_VM_NAME;
  typedef NDIS_IF_COUNTED_STRING NDIS_VM_FRIENDLYNAME, *PNDIS_VM_FRIENDLYNAME;
  typedef NDIS_IF_COUNTED_STRING NDIS_SWITCH_PORT_PROPERTY_PROFILE_NAME, *PNDIS_SWITCH_PORT_PROPERTY_PROFILE_NAME;
  typedef NDIS_IF_COUNTED_STRING NDIS_SWITCH_PORT_PROPERTY_PROFILE_CDN_LABEL, *PNDIS_SWITCH_PORT_PROPERTY_PROFILE_CDN_LABEL;
  typedef NDIS_IF_COUNTED_STRING NDIS_SWITCH_NAME, *PNDIS_SWITCH_NAME;
  typedef NDIS_IF_COUNTED_STRING NDIS_SWITCH_FRIENDLYNAME, *PNDIS_SWITCH_FRIENDLYNAME;
  typedef NDIS_IF_COUNTED_STRING NDIS_SWITCH_PORT_NAME, *PNDIS_SWITCH_PORT_NAME;
  typedef NDIS_IF_COUNTED_STRING NDIS_SWITCH_PORT_FRIENDLYNAME, *PNDIS_SWITCH_PORT_FRIENDLYNAME;
  typedef NDIS_IF_COUNTED_STRING NDIS_SWITCH_NIC_NAME, *PNDIS_SWITCH_NIC_NAME;
  typedef NDIS_IF_COUNTED_STRING NDIS_SWITCH_NIC_FRIENDLYNAME, *PNDIS_SWITCH_NIC_FRIENDLYNAME;
  typedef NDIS_IF_COUNTED_STRING NDIS_SWITCH_EXTENSION_FRIENDLYNAME, *PNDIS_SWITCH_EXTENSION_FRIENDLYNAME;
  typedef NDIS_IF_COUNTED_STRING NDIS_VENDOR_NAME, *PNDIS_VENDOR_NAME;

  typedef struct _NDIS_RECEIVE_QUEUE_PARAMETERS {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    NDIS_RECEIVE_QUEUE_TYPE QueueType;
    NDIS_RECEIVE_QUEUE_ID QueueId;
    NDIS_RECEIVE_QUEUE_GROUP_ID QueueGroupId;
    GROUP_AFFINITY ProcessorAffinity;
    ULONG NumSuggestedReceiveBuffers;
    ULONG MSIXTableEntry;
    ULONG LookaheadSize;
    NDIS_VM_NAME VmName;
    NDIS_QUEUE_NAME QueueName;
#if NDIS_SUPPORT_NDIS630
    ULONG PortId;
    ULONG InterruptCoalescingDomainId;
#endif
  } NDIS_RECEIVE_QUEUE_PARAMETERS, *PNDIS_RECEIVE_QUEUE_PARAMETERS;

  typedef struct _NDIS_RECEIVE_QUEUE_FREE_PARAMETERS {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    NDIS_RECEIVE_QUEUE_ID QueueId;
  } NDIS_RECEIVE_QUEUE_FREE_PARAMETERS, *PNDIS_RECEIVE_QUEUE_FREE_PARAMETERS;

  typedef enum _NDIS_RECEIVE_QUEUE_OPERATIONAL_STATE {
    NdisReceiveQueueOperationalStateUndefined,
    NdisReceiveQueueOperationalStateRunning,
    NdisReceiveQueueOperationalStatePaused,
    NdisReceiveQueueOperationalStateDmaStopped,
    NdisReceiveQueueOperationalStateMaximum
  } NDIS_RECEIVE_QUEUE_OPERATIONAL_STATE, *PNDIS_RECEIVE_QUEUE_OPERATIONAL_STATE;

  typedef struct _NDIS_RECEIVE_QUEUE_INFO {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    NDIS_RECEIVE_QUEUE_TYPE QueueType;
    NDIS_RECEIVE_QUEUE_ID QueueId;
    NDIS_RECEIVE_QUEUE_GROUP_ID QueueGroupId;
    NDIS_RECEIVE_QUEUE_OPERATIONAL_STATE QueueState;
    GROUP_AFFINITY ProcessorAffinity;
    ULONG NumSuggestedReceiveBuffers;
    ULONG MSIXTableEntry;
    ULONG LookaheadSize;
    NDIS_VM_NAME VmName;
    NDIS_QUEUE_NAME QueueName;
#if NDIS_SUPPORT_NDIS630
    ULONG NumFilters;
    ULONG InterruptCoalescingDomainId;
#endif
  } NDIS_RECEIVE_QUEUE_INFO, *PNDIS_RECEIVE_QUEUE_INFO;

  typedef struct _NDIS_RECEIVE_QUEUE_INFO_ARRAY {
    NDIS_OBJECT_HEADER Header;
    ULONG FirstElementOffset;
    ULONG NumElements;
    ULONG ElementSize;
  } NDIS_RECEIVE_QUEUE_INFO_ARRAY, *PNDIS_RECEIVE_QUEUE_INFO_ARRAY;

  typedef struct _NDIS_RECEIVE_FILTER_INFO {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    NDIS_RECEIVE_FILTER_TYPE FilterType;
    NDIS_RECEIVE_FILTER_ID FilterId;
  } NDIS_RECEIVE_FILTER_INFO, *PNDIS_RECEIVE_FILTER_INFO;

  typedef struct _NDIS_RECEIVE_FILTER_INFO_ARRAY {
    NDIS_OBJECT_HEADER Header;
    NDIS_RECEIVE_QUEUE_ID QueueId;
    ULONG FirstElementOffset;
    ULONG NumElements;
    ULONG ElementSize;
#if NDIS_SUPPORT_NDIS630
    ULONG Flags;
    NDIS_NIC_SWITCH_VPORT_ID VPortId;
#endif
  } NDIS_RECEIVE_FILTER_INFO_ARRAY, *PNDIS_RECEIVE_FILTER_INFO_ARRAY;

  typedef struct _NDIS_RECEIVE_QUEUE_ALLOCATION_COMPLETE_PARAMETERS {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    NDIS_RECEIVE_QUEUE_ID QueueId;
    NDIS_STATUS CompletionStatus;
  } NDIS_RECEIVE_QUEUE_ALLOCATION_COMPLETE_PARAMETERS, *PNDIS_RECEIVE_QUEUE_ALLOCATION_COMPLETE_PARAMETERS;

  typedef struct _NDIS_RECEIVE_QUEUE_ALLOCATION_COMPLETE_ARRAY {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    ULONG FirstElementOffset;
    ULONG NumElements;
    ULONG ElementSize;
  } NDIS_RECEIVE_QUEUE_ALLOCATION_COMPLETE_ARRAY, *PNDIS_RECEIVE_QUEUE_ALLOCATION_COMPLETE_ARRAY;
#endif

#if NTDDI_VERSION >= 0x06000000 || NDIS_SUPPORT_NDIS6
#define NDIS_RSS_CAPS_MESSAGE_SIGNALED_INTERRUPTS 0x01000000
#define NDIS_RSS_CAPS_CLASSIFICATION_AT_ISR 0x02000000
#define NDIS_RSS_CAPS_CLASSIFICATION_AT_DPC 0x04000000
#if NDIS_SUPPORT_NDIS620
#define NDIS_RSS_CAPS_USING_MSI_X 0x08000000
#endif
#if NDIS_SUPPORT_NDIS630
#define NDIS_RSS_CAPS_RSS_AVAILABLE_ON_PORTS 0x10000000
#define NDIS_RSS_CAPS_SUPPORTS_MSI_X 0x20000000
#endif
#define NDIS_RSS_CAPS_HASH_TYPE_TCP_IPV4 0x00000100
#define NDIS_RSS_CAPS_HASH_TYPE_TCP_IPV6 0x00000200
#define NDIS_RSS_CAPS_HASH_TYPE_TCP_IPV6_EX 0x00000400

#define NdisHashFunctionToeplitz 0x00000001
#define NdisHashFunctionReserved1 0x00000002
#define NdisHashFunctionReserved2 0x00000004
#define NdisHashFunctionReserved3 0x00000008

#define NDIS_HASH_FUNCTION_MASK 0x000000ff
#define NDIS_HASH_TYPE_MASK 0x00ffff00

#define NDIS_RSS_HASH_FUNC_FROM_HASH_INFO(HINFO) ((HINFO) & (NDIS_HASH_FUNCTION_MASK))
#define NDIS_RSS_HASH_TYPE_FROM_HASH_INFO(HINFO) ((HINFO) & (NDIS_HASH_TYPE_MASK))
#define NDIS_RSS_HASH_INFO_FROM_TYPE_AND_FUNC(HTYPE, HFCT) ((HTYPE) | (HFCT))

#define NDIS_HASH_IPV4 0x00000100
#define NDIS_HASH_TCP_IPV4 0x00000200
#define NDIS_HASH_IPV6 0x00000400
#define NDIS_HASH_IPV6_EX 0x00000800
#define NDIS_HASH_TCP_IPV6 0x00001000
#define NDIS_HASH_TCP_IPV6_EX 0x00002000

#define NDIS_RSS_PARAM_FLAG_BASE_CPU_UNCHANGED 0x0001
#define NDIS_RSS_PARAM_FLAG_HASH_INFO_UNCHANGED 0x0002
#define NDIS_RSS_PARAM_FLAG_ITABLE_UNCHANGED 0x0004
#define NDIS_RSS_PARAM_FLAG_HASH_KEY_UNCHANGED 0x0008
#define NDIS_RSS_PARAM_FLAG_DISABLE_RSS 0x0010

#define NDIS_RSS_INDIRECTION_TABLE_SIZE_REVISION_1 128
#define NDIS_RSS_HASH_SECRET_KEY_SIZE_REVISION_1 40

#define NDIS_RSS_INDIRECTION_TABLE_MAX_SIZE_REVISION_1 128
#define NDIS_RSS_HASH_SECRET_KEY_MAX_SIZE_REVISION_1 40

#if NDIS_SUPPORT_NDIS620
#define NDIS_RSS_INDIRECTION_TABLE_MAX_SIZE_REVISION_2 (128 * sizeof (PROCESSOR_NUMBER))
#define NDIS_RSS_HASH_SECRET_KEY_MAX_SIZE_REVISION_2 40
#endif

#define NDIS_RECEIVE_HASH_FLAG_ENABLE_HASH 0x00000001
#define NDIS_RECEIVE_HASH_FLAG_HASH_INFO_UNCHANGED 0x00000002
#define NDIS_RECEIVE_HASH_FLAG_HASH_KEY_UNCHANGED 0x00000004

#define NDIS_PORT_CHAR_USE_DEFAULT_AUTH_SETTINGS 0x00000001

#define NDIS_RECEIVE_SCALE_CAPABILITIES_REVISION_1 1
#define NDIS_RECEIVE_HASH_PARAMETERS_REVISION_1 1
#define NDIS_PORT_STATE_REVISION_1 1
#define NDIS_PORT_CHARACTERISTICS_REVISION_1 1
#define NDIS_PORT_ARRAY_REVISION_1 1
#define NDIS_RECEIVE_SCALE_PARAMETERS_REVISION_1 1
#if NDIS_SUPPORT_NDIS620
#define NDIS_RECEIVE_SCALE_PARAMETERS_REVISION_2 2
#endif
#if NDIS_SUPPORT_NDIS630
#define NDIS_RECEIVE_SCALE_CAPABILITIES_REVISION_2 2
#endif

#define NDIS_SIZEOF_RECEIVE_SCALE_CAPABILITIES_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_RECEIVE_SCALE_CAPABILITIES, NumberOfReceiveQueues)
#define NDIS_SIZEOF_RECEIVE_SCALE_PARAMETERS_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_RECEIVE_SCALE_PARAMETERS, HashSecretKeyOffset)
#define NDIS_SIZEOF_RECEIVE_HASH_PARAMETERS_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_RECEIVE_HASH_PARAMETERS, HashSecretKeyOffset)
#define NDIS_SIZEOF_PORT_STATE_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_PORT_STATE, Flags)
#define NDIS_SIZEOF_PORT_CHARACTERISTICS_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_PORT_CHARACTERISTICS, RcvAuthorizationState)
#define NDIS_SIZEOF_PORT_ARRAY_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_PORT_ARRAY, Ports)
#if NDIS_SUPPORT_NDIS620
#define NDIS_SIZEOF_RECEIVE_SCALE_PARAMETERS_REVISION_2 RTL_SIZEOF_THROUGH_FIELD (NDIS_RECEIVE_SCALE_PARAMETERS, ProcessorMasksEntrySize)
#endif
#if NDIS_SUPPORT_NDIS630
#define NDIS_SIZEOF_RECEIVE_SCALE_CAPABILITIES_REVISION_2 RTL_SIZEOF_THROUGH_FIELD (NDIS_RECEIVE_SCALE_CAPABILITIES, NumberOfIndirectionTableEntries)
#endif

  typedef struct _NDIS_RECEIVE_SCALE_CAPABILITIES {
    NDIS_OBJECT_HEADER Header;
    ULONG CapabilitiesFlags;
    ULONG NumberOfInterruptMessages;
    ULONG NumberOfReceiveQueues;
#if NDIS_SUPPORT_NDIS630
    USHORT NumberOfIndirectionTableEntries;
#endif
  } NDIS_RECEIVE_SCALE_CAPABILITIES, *PNDIS_RECEIVE_SCALE_CAPABILITIES;

  typedef struct _NDIS_RECEIVE_SCALE_PARAMETERS {
    NDIS_OBJECT_HEADER Header;
    USHORT Flags;
    USHORT BaseCpuNumber;
    ULONG HashInformation;
    USHORT IndirectionTableSize;
    ULONG IndirectionTableOffset;
    USHORT HashSecretKeySize;
    ULONG HashSecretKeyOffset;
#if NDIS_SUPPORT_NDIS620
    ULONG ProcessorMasksOffset;
    ULONG NumberOfProcessorMasks;
    ULONG ProcessorMasksEntrySize;
#endif
  } NDIS_RECEIVE_SCALE_PARAMETERS, *PNDIS_RECEIVE_SCALE_PARAMETERS;

  typedef struct _NDIS_RECEIVE_HASH_PARAMETERS {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    ULONG HashInformation;
    USHORT HashSecretKeySize;
    ULONG HashSecretKeyOffset;
  } NDIS_RECEIVE_HASH_PARAMETERS, *PNDIS_RECEIVE_HASH_PARAMETERS;

  typedef enum _NDIS_PROCESSOR_VENDOR {
    NdisProcessorVendorUnknown,
    NdisProcessorVendorGenuinIntel,
    NdisProcessorVendorGenuineIntel = NdisProcessorVendorGenuinIntel,
    NdisProcessorVendorAuthenticAMD
  } NDIS_PROCESSOR_VENDOR, *PNDIS_PROCESSOR_VENDOR;

#if NDIS_SUPPORT_NDIS620
#define NDIS_HYPERVISOR_INFO_FLAG_HYPERVISOR_PRESENT 0x00000001

#define NDIS_RSS_PROCESSOR_INFO_REVISION_1 1
#define NDIS_SYSTEM_PROCESSOR_INFO_EX_REVISION_1 1
#define NDIS_HYPERVISOR_INFO_REVISION_1 1
#define NDIS_WMI_RECEIVE_QUEUE_INFO_REVISION_1 1
#define NDIS_WMI_RECEIVE_QUEUE_PARAMETERS_REVISION_1 1
#if NDIS_SUPPORT_NDIS630
#define NDIS_RSS_PROCESSOR_INFO_REVISION_2 2
#endif

#define NDIS_SIZEOF_RSS_PROCESSOR_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_RSS_PROCESSOR, PreferenceIndex)
#define NDIS_SIZEOF_RSS_PROCESSOR_INFO_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_RSS_PROCESSOR_INFO, RssProcessorEntrySize)
#define NDIS_SIZEOF_SYSTEM_PROCESSOR_INFO_EX_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_SYSTEM_PROCESSOR_INFO_EX, ProcessorInfoEntrySize)
#define NDIS_SIZEOF_HYPERVISOR_INFO_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_HYPERVISOR_INFO, PartitionType)
#define NDIS_SIZEOF_WMI_RECEIVE_QUEUE_PARAMETERS_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_WMI_RECEIVE_QUEUE_PARAMETERS, QueueName)
#define NDIS_SIZEOF_WMI_RECEIVE_QUEUE_INFO_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_WMI_RECEIVE_QUEUE_INFO, QueueName)
#if NDIS_SUPPORT_NDIS630
#define NDIS_SIZEOF_RSS_PROCESSOR_INFO_REVISION_2 RTL_SIZEOF_THROUGH_FIELD (NDIS_RSS_PROCESSOR_INFO, RssProfile)
#endif

  typedef struct _NDIS_RSS_PROCESSOR {
    PROCESSOR_NUMBER ProcNum;
    USHORT PreferenceIndex;
    USHORT Reserved;
  } NDIS_RSS_PROCESSOR, *PNDIS_RSS_PROCESSOR;

#if NDIS_SUPPORT_NDIS630
  typedef enum _NDIS_RSS_PROFILE {
    NdisRssProfileClosest = 1,
    NdisRssProfileClosestStatic,
    NdisRssProfileNuma,
    NdisRssProfileNumaStatic,
    NdisRssProfileConservative,
    NdisRssProfileMaximum
  } NDIS_RSS_PROFILE, *PNDIS_RSS_PROFILE;
#endif

  typedef struct _NDIS_RSS_PROCESSOR_INFO {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    PROCESSOR_NUMBER RssBaseProcessor;
    ULONG MaxNumRssProcessors;
    USHORT PreferredNumaNode;
    ULONG RssProcessorArrayOffset;
    ULONG RssProcessorCount;
    ULONG RssProcessorEntrySize;
#if NDIS_SUPPORT_NDIS630
    PROCESSOR_NUMBER RssMaxProcessor;
    NDIS_RSS_PROFILE RssProfile;
#endif
  } NDIS_RSS_PROCESSOR_INFO, *PNDIS_RSS_PROCESSOR_INFO;

  typedef struct _NDIS_PROCESSOR_INFO_EX {
    PROCESSOR_NUMBER ProcNum;
    ULONG SocketId;
    ULONG CoreId;
    ULONG HyperThreadId;
    USHORT NodeId;
    USHORT NodeDistance;
  } NDIS_PROCESSOR_INFO_EX, *PNDIS_PROCESSOR_INFO_EX;

  typedef struct _NDIS_SYSTEM_PROCESSOR_INFO_EX {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    NDIS_PROCESSOR_VENDOR ProcessorVendor;
    ULONG NumSockets;
    ULONG NumCores;
    ULONG NumCoresPerSocket;
    ULONG MaxHyperThreadingProcsPerCore;
    ULONG ProcessorInfoOffset;
    ULONG NumberOfProcessors;
    ULONG ProcessorInfoEntrySize;
  } NDIS_SYSTEM_PROCESSOR_INFO_EX, *PNDIS_SYSTEM_PROCESSOR_INFO_EX;

  typedef enum _NDIS_HYPERVISOR_PARTITION_TYPE {
    NdisHypervisorPartitionTypeUnknown,
    NdisHypervisorPartitionTypeMsHvParent,
    NdisHypervisorPartitionMsHvChild,
    NdisHypervisorPartitionTypeMax
  } NDIS_HYPERVISOR_PARTITION_TYPE, *PNDIS_HYPERVISOR_PARTITION_TYPE;

  typedef struct _NDIS_HYPERVISOR_INFO {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    NDIS_HYPERVISOR_PARTITION_TYPE PartitionType;
  } NDIS_HYPERVISOR_INFO, *PNDIS_HYPERVISOR_INFO;

  typedef struct _NDIS_WMI_GROUP_AFFINITY {
    ULONG64 Mask;
    USHORT Group;
    USHORT Reserved[3];
  } NDIS_WMI_GROUP_AFFINITY, *PNDIS_WMI_GROUP_AFFINITY;

  typedef struct _NDIS_WMI_RECEIVE_QUEUE_PARAMETERS {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    NDIS_RECEIVE_QUEUE_TYPE QueueType;
    NDIS_RECEIVE_QUEUE_ID QueueId;
    NDIS_RECEIVE_QUEUE_GROUP_ID QueueGroupId;
    NDIS_WMI_GROUP_AFFINITY ProcessorAffinity;
    ULONG NumSuggestedReceiveBuffers;
    ULONG MSIXTableEntry;
    ULONG LookaheadSize;
    NDIS_VM_NAME VmName;
    NDIS_QUEUE_NAME QueueName;
  } NDIS_WMI_RECEIVE_QUEUE_PARAMETERS, *PNDIS_WMI_RECEIVE_QUEUE_PARAMETERS;

  typedef struct _NDIS_WMI_RECEIVE_QUEUE_INFO {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    NDIS_RECEIVE_QUEUE_TYPE QueueType;
    NDIS_RECEIVE_QUEUE_ID QueueId;
    NDIS_RECEIVE_QUEUE_GROUP_ID QueueGroupId;
    NDIS_RECEIVE_QUEUE_OPERATIONAL_STATE QueueState;
    NDIS_WMI_GROUP_AFFINITY ProcessorAffinity;
    ULONG NumSuggestedReceiveBuffers;
    ULONG MSIXTableEntry;
    ULONG LookaheadSize;
    NDIS_VM_NAME VmName;
    NDIS_QUEUE_NAME QueueName;
  } NDIS_WMI_RECEIVE_QUEUE_INFO, *PNDIS_WMI_RECEIVE_QUEUE_INFO;
#endif

#if NDIS_SUPPORT_NDIS630

#define NDIS_NDK_PERFORMANCE_COUNTER_MASK(CNTFIELD) (((ULONG64) 1) << (FIELD_OFFSET (NDIS_NDK_PERFORMANCE_COUNTERS, CNTFIELD) / sizeof (ULONG64)))

#define OID_NDK_SET_STATE 0xfc040201
#define OID_NDK_STATISTICS 0xfc040202
#define OID_NDK_CONNECTIONS 0xfc040203
#define OID_NDK_LOCAL_ENDPOINTS 0xfc040204

#define OID_QOS_HARDWARE_CAPABILITIES 0xfc050001
#define OID_QOS_CURRENT_CAPABILITIES 0xfc050002
#define OID_QOS_PARAMETERS 0xfc050003
#define OID_QOS_OPERATIONAL_PARAMETERS 0xfc050004
#define OID_QOS_REMOTE_PARAMETERS 0xfc050005

#define NDIS_QOS_MAXIMUM_PRIORITIES 8
#define NDIS_QOS_MAXIMUM_TRAFFIC_CLASSES 8

#define NDIS_QOS_CAPABILITIES_STRICT_TSA_SUPPORTED 0x00000001
#define NDIS_QOS_CAPABILITIES_MACSEC_BYPASS_SUPPORTED 0x00000002
#define NDIS_QOS_CAPABILITIES_CEE_DCBX_SUPPORTED 0x00000004
#define NDIS_QOS_CAPABILITIES_IEEE_DCBX_SUPPORTED 0x00000008

#define NDIS_QOS_CLASSIFICATION_SET_BY_MINIPORT_MASK 0xff000000
#define NDIS_QOS_CLASSIFICATION_ENFORCED_BY_MINIPORT 0x01000000

#define NDIS_QOS_CONDITION_RESERVED 0x0
#define NDIS_QOS_CONDITION_DEFAULT 0x1
#define NDIS_QOS_CONDITION_TCP_PORT 0x2
#define NDIS_QOS_CONDITION_UDP_PORT 0x3
#define NDIS_QOS_CONDITION_TCP_OR_UDP_PORT 0x4
#define NDIS_QOS_CONDITION_ETHERTYPE 0x5
#define NDIS_QOS_CONDITION_NETDIRECT_PORT 0x6
#define NDIS_QOS_CONDITION_MAXIMUM 0x7

#define NDIS_QOS_ACTION_PRIORITY 0x0
#define NDIS_QOS_ACTION_MAXIMUM 0x1

#define NDIS_QOS_PARAMETERS_ETS_CHANGED 0x00000001
#define NDIS_QOS_PARAMETERS_ETS_CONFIGURED 0x00000002
#define NDIS_QOS_PARAMETERS_PFC_CHANGED 0x00000100
#define NDIS_QOS_PARAMETERS_PFC_CONFIGURED 0x00000200
#define NDIS_QOS_PARAMETERS_CLASSIFICATION_CHANGED 0x00010000
#define NDIS_QOS_PARAMETERS_CLASSIFICATION_CONFIGURED 0x00020000
#define NDIS_QOS_PARAMETERS_WILLING 0x80000000

#define NDIS_QOS_TSA_STRICT 0x0
#define NDIS_QOS_TSA_CBS 0x1
#define NDIS_QOS_TSA_ETS 0x2
#define NDIS_QOS_TSA_MAXIMUM 0x3

#define NDIS_PF_FUNCTION_ID (USHORT) -1
#define NDIS_INVALID_VF_FUNCTION_ID (USHORT) -1
#define NDIS_INVALID_RID (ULONG) -1
#define NDIS_DEFAULT_VPORT_ID 0
#define NDIS_DEFAULT_SWITCH_ID 0
#define NDIS_INVALID_SWITCH_ID (ULONG) -1

#define NDIS_NIC_SWITCH_PARAMETERS_CHANGE_MASK 0xffff0000
#define NDIS_NIC_SWITCH_PARAMETERS_SWITCH_NAME_CHANGED 0x00010000

#define NDIS_SRIOV_CAPS_SRIOV_SUPPORTED 0x00000001
#define NDIS_SRIOV_CAPS_PF_MINIPORT 0x00000002
#define NDIS_SRIOV_CAPS_VF_MINIPORT 0x00000004

#define NDIS_NIC_SWITCH_VF_INFO_ARRAY_ENUM_ON_SPECIFIC_SWITCH 0x00000001

#define NDIS_NIC_SWITCH_VPORT_PARAMS_LOOKAHEAD_SPLIT_ENABLED 0x00000001
#define NDIS_NIC_SWITCH_VPORT_PARAMS_CHANGE_MASK 0xffff0000
#define NDIS_NIC_SWITCH_VPORT_PARAMS_FLAGS_CHANGED 0x00010000
#define NDIS_NIC_SWITCH_VPORT_PARAMS_NAME_CHANGED 0x00020000
#define NDIS_NIC_SWITCH_VPORT_PARAMS_INT_MOD_CHANGED 0x00040000
#define NDIS_NIC_SWITCH_VPORT_PARAMS_STATE_CHANGED 0x00080000
#define NDIS_NIC_SWITCH_VPORT_PARAMS_PROCESSOR_AFFINITY_CHANGED 0x00100000

#define NDIS_NIC_SWITCH_VPORT_INFO_ARRAY_ENUM_ON_SPECIFIC_FUNCTION 0x00000001
#define NDIS_NIC_SWITCH_VPORT_INFO_ARRAY_ENUM_ON_SPECIFIC_SWITCH 0x00000002

#define NDIS_NIC_SWITCH_VPORT_INFO_LOOKAHEAD_SPLIT_ENABLED 0x00000001

  DEFINE_GUID (GUID_NDIS_NDK_CAPABILITIES, 0x7969ba4d, 0xdd80, 0x4bc7, 0xb3, 0xe6, 0x68, 0x04, 0x39, 0x97, 0xe5, 0x19);
  DEFINE_GUID (GUID_NDIS_NDK_STATE, 0x530c69c9, 0x2f51, 0x49de, 0xa1, 0xaf, 0x08, 0x8d, 0x54, 0xff, 0xa4, 0x74);

#define NDIS_NDK_CAPABILITIES_REVISION_1 1
#define NDIS_NDK_STATISTICS_INFO_REVISION_1 1
#define NDIS_NDK_CONNECTIONS_REVISION_1 1
#define NDIS_NDK_LOCAL_ENDPOINTS_REVISION_1 1
#define NDIS_QOS_CAPABILITIES_REVISION_1 1
#define NDIS_QOS_CLASSIFICATION_ELEMENT_REVISION_1 1
#define NDIS_QOS_PARAMETERS_REVISION_1 1
#define NDIS_NIC_SWITCH_PARAMETERS_REVISION_1 1
#define NDIS_NIC_SWITCH_DELETE_SWITCH_PARAMETERS_REVISION_1 1
#define NDIS_NIC_SWITCH_INFO_REVISION_1 1
#define NDIS_NIC_SWITCH_INFO_ARRAY_REVISION_1 1
#define NDIS_NIC_SWITCH_VPORT_PARAMETERS_REVISION_1 1
#define NDIS_NIC_SWITCH_DELETE_VPORT_PARAMETERS_REVISION_1 1
#define NDIS_NIC_SWITCH_VPORT_INFO_REVISION_1 1
#define NDIS_NIC_SWITCH_VPORT_INFO_ARRAY_REVISION_1 1
#define NDIS_NIC_SWITCH_VF_PARAMETERS_REVISION_1 1
#define NDIS_NIC_SWITCH_FREE_VF_PARAMETERS_REVISION_1 1
#define NDIS_NIC_SWITCH_VF_INFO_REVISION_1 1
#define NDIS_NIC_SWITCH_VF_INFO_ARRAY_REVISION_1 1
#define NDIS_SRIOV_CAPABILITIES_REVISION_1 1
#define NDIS_SRIOV_READ_VF_CONFIG_SPACE_PARAMETERS_REVISION_1 1
#define NDIS_SRIOV_WRITE_VF_CONFIG_SPACE_PARAMETERS_REVISION_1 1
#define NDIS_SRIOV_READ_VF_CONFIG_BLOCK_PARAMETERS_REVISION_1 1
#define NDIS_SRIOV_WRITE_VF_CONFIG_BLOCK_PARAMETERS_REVISION_1 1
#define NDIS_SRIOV_RESET_VF_PARAMETERS_REVISION_1 1
#define NDIS_SRIOV_SET_VF_POWER_STATE_PARAMETERS_REVISION_1 1
#define NDIS_SRIOV_CONFIG_STATE_PARAMETERS_REVISION_1 1
#define NDIS_SRIOV_VF_VENDOR_DEVICE_ID_INFO_REVISION_1 1
#define NDIS_SRIOV_PROBED_BARS_INFO_REVISION_1 1
#define NDIS_RECEIVE_FILTER_MOVE_FILTER_PARAMETERS_REVISION_1 1
#define NDIS_SRIOV_BAR_RESOURCES_INFO_REVISION_1 1
#define NDIS_SRIOV_PF_LUID_INFO_REVISION_1 1
#define NDIS_SRIOV_VF_SERIAL_NUMBER_INFO_REVISION_1 1
#define NDIS_SRIOV_VF_INVALIDATE_CONFIG_BLOCK_INFO_REVISION_1 1
#define NDIS_SWITCH_OBJECT_SERIALIZATION_VERSION_1 1
#define NDIS_SWITCH_PORT_PROPERTY_SECURITY_REVISION_1 1
#define NDIS_SWITCH_PORT_PROPERTY_VLAN_REVISION_1 1
#define NDIS_SWITCH_PORT_PROPERTY_PROFILE_REVISION_1 1
#define NDIS_SWITCH_PORT_PROPERTY_CUSTOM_REVISION_1 1
#define NDIS_SWITCH_PORT_PROPERTY_PARAMETERS_REVISION_1 1
#define NDIS_SWITCH_PORT_PROPERTY_DELETE_PARAMETERS_REVISION_1 1
#define NDIS_SWITCH_PORT_PROPERTY_ENUM_PARAMETERS_REVISION_1 1
#define NDIS_SWITCH_PORT_PROPERTY_ENUM_INFO_REVISION_1 1
#define NDIS_SWITCH_PROPERTY_PARAMETERS_REVISION_1 1
#define NDIS_SWITCH_PROPERTY_CUSTOM_REVISION_1 1
#define NDIS_SWITCH_PORT_FEATURE_STATUS_PARAMETERS_REVISION_1 1
#define NDIS_SWITCH_PORT_FEATURE_STATUS_CUSTOM_REVISION_1 1
#define NDIS_SWITCH_PROPERTY_DELETE_PARAMETERS_REVISION_1 1
#define NDIS_SWITCH_PROPERTY_ENUM_INFO_REVISION_1 1
#define NDIS_SWITCH_PROPERTY_ENUM_PARAMETERS_REVISION_1 1
#define NDIS_SWITCH_FEATURE_STATUS_CUSTOM_REVISION_1 1
#define NDIS_SWITCH_PORT_PARAMETERS_REVISION_1 1
#define NDIS_SWITCH_PORT_ARRAY_REVISION_1 1
#define NDIS_SWITCH_PARAMETERS_REVISION_1 1
#define NDIS_SWITCH_NIC_PARAMETERS_REVISION_1 1
#define NDIS_SWITCH_NIC_ARRAY_REVISION_1 1
#define NDIS_SWITCH_NIC_OID_REQUEST_REVISION_1 1
#define NDIS_SWITCH_FEATURE_STATUS_PARAMETERS_REVISION_1 1
#define NDIS_SWITCH_NIC_SAVE_STATE_REVISION_1 1

#define NDIS_SIZEOF_NDK_CAPABILITIES_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_NDK_CAPABILITIES, NdkInfo)
#define NDIS_SIZEOF_NDK_STATISTICS_INFO_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_NDK_STATISTICS_INFO, CounterSet)
#define NDIS_SIZEOF_NDK_CONNECTIONS_REVISION_1(n) FIELD_OFFSET (NDIS_NDK_CONNECTIONS, Connections[n])
#define NDIS_SIZEOF_NDK_LOCAL_ENDPOINTS_REVISION_1(n) FIELD_OFFSET (NDIS_NDK_LOCAL_ENDPOINTS, LocalEndpoints[n])
#define NDIS_SIZEOF_QOS_CAPABILITIES_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_QOS_CAPABILITIES, MaxNumPfcEnabledTrafficClasses)
#define NDIS_SIZEOF_QOS_CLASSIFICATION_ELEMENT_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_QOS_CLASSIFICATION_ELEMENT, ActionField)
#define NDIS_SIZEOF_QOS_PARAMETERS_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_QOS_PARAMETERS, FirstClassificationElementOffset)
#define NDIS_SIZEOF_NIC_SWITCH_PARAMETERS_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_NIC_SWITCH_PARAMETERS, NdisReserved3)
#define NDIS_SIZEOF_NIC_SWITCH_DELETE_SWITCH_PARAMETERS_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_NIC_SWITCH_DELETE_SWITCH_PARAMETERS, SwitchId)
#define NDIS_SIZEOF_NIC_SWITCH_INFO_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_NIC_SWITCH_INFO, NumActiveNonDefaultVPortVlanIds)
#define NDIS_SIZEOF_NIC_SWITCH_INFO_ARRAY_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_NIC_SWITCH_INFO_ARRAY, ElementSize)
#define NDIS_SIZEOF_NIC_SWITCH_VPORT_PARAMETERS_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_NIC_SWITCH_VPORT_PARAMETERS, LookaheadSize)
#define NDIS_SIZEOF_NIC_SWITCH_DELETE_VPORT_PARAMETERS_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_NIC_SWITCH_DELETE_VPORT_PARAMETERS, VPortId)
#define NDIS_SIZEOF_NIC_SWITCH_VPORT_INFO_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_NIC_SWITCH_VPORT_INFO, NumFilters)
#define NDIS_SIZEOF_NIC_SWITCH_VPORT_INFO_ARRAY_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_NIC_SWITCH_VPORT_INFO_ARRAY, ElementSize)
#define NDIS_SIZEOF_NIC_SWITCH_VF_PARAMETERS_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_NIC_SWITCH_VF_PARAMETERS, RequestorId)
#define NDIS_SIZEOF_NIC_SWITCH_FREE_VF_PARAMETERS_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_NIC_SWITCH_FREE_VF_PARAMETERS, VFId)
#define NDIS_SIZEOF_NIC_SWITCH_VF_INFO_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_NIC_SWITCH_VF_INFO, RequestorId)
#define NDIS_SIZEOF_NIC_SWITCH_VF_INFO_ARRAY_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_NIC_SWITCH_VF_INFO_ARRAY, ElementSize)
#define NDIS_SIZEOF_SRIOV_CAPABILITIES_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_SRIOV_CAPABILITIES, SriovCapabilities)
#define NDIS_SIZEOF_SRIOV_READ_VF_CONFIG_SPACE_PARAMETERS_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_SRIOV_READ_VF_CONFIG_SPACE_PARAMETERS, BufferOffset)
#define NDIS_SIZEOF_SRIOV_WRITE_VF_CONFIG_SPACE_PARAMETERS_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_SRIOV_WRITE_VF_CONFIG_SPACE_PARAMETERS, BufferOffset)
#define NDIS_SIZEOF_SRIOV_READ_VF_CONFIG_BLOCK_PARAMETERS_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_SRIOV_READ_VF_CONFIG_BLOCK_PARAMETERS, BufferOffset)
#define NDIS_SIZEOF_SRIOV_WRITE_VF_CONFIG_BLOCK_PARAMETERS_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_SRIOV_WRITE_VF_CONFIG_BLOCK_PARAMETERS, BufferOffset)
#define NDIS_SIZEOF_SRIOV_RESET_VF_PARAMETERS_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_SRIOV_RESET_VF_PARAMETERS, VFId)
#define NDIS_SIZEOF_SRIOV_SET_VF_POWER_STATE_PARAMETERS_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_SRIOV_SET_VF_POWER_STATE_PARAMETERS, WakeEnable)
#define NDIS_SIZEOF_SRIOV_CONFIG_STATE_PARAMETERS_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_SRIOV_CONFIG_STATE_PARAMETERS, Length)
#define NDIS_SIZEOF_SRIOV_VF_VENDOR_DEVICE_ID_INFO_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_SRIOV_VF_VENDOR_DEVICE_ID_INFO, DeviceId)
#define NDIS_SIZEOF_SRIOV_PROBED_BARS_INFO_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_SRIOV_PROBED_BARS_INFO, BaseRegisterValuesOffset)
#define NDIS_SIZEOF_RECEIVE_FILTER_MOVE_FILTER_PARAMETERS_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_RECEIVE_FILTER_MOVE_FILTER_PARAMETERS, DestVPortId)
#define NDIS_SIZEOF_SRIOV_BAR_RESOURCES_INFO_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_SRIOV_BAR_RESOURCES_INFO, BarResourcesOffset)
#define NDIS_SIZEOF_SRIOV_PF_LUID_INFO_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_SRIOV_PF_LUID_INFO, Luid)
#define NDIS_SIZEOF_SRIOV_VF_SERIAL_NUMBER_INFO_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_SRIOV_VF_SERIAL_NUMBER_INFO, SerialNumber)
#define NDIS_SIZEOF_SRIOV_VF_INVALIDATE_CONFIG_BLOCK_INFO_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_SRIOV_VF_INVALIDATE_CONFIG_BLOCK_INFO, BlockMask)
#define NDIS_SIZEOF_NDIS_SWITCH_PORT_PROPERTY_SECURITY_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_SWITCH_PORT_PROPERTY_SECURITY, AllowTeaming)
#define NDIS_SIZEOF_NDIS_SWITCH_PORT_PROPERTY_VLAN_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_SWITCH_PORT_PROPERTY_VLAN, VlanProperties)
#define NDIS_SIZEOF_NDIS_SWITCH_PORT_PROPERTY_PROFILE_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_SWITCH_PORT_PROPERTY_PROFILE, CdnLabel)
#define NDIS_SIZEOF_NDIS_SWITCH_PORT_PROPERTY_CUSTOM_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_SWITCH_PORT_PROPERTY_CUSTOM, PropertyBufferOffset)
#define NDIS_SIZEOF_NDIS_SWITCH_PORT_PROPERTY_PARAMETERS_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_SWITCH_PORT_PROPERTY_PARAMETERS, Reserved)
#define NDIS_SIZEOF_NDIS_SWITCH_PORT_PROPERTY_ENUM_PARAMETERS_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_SWITCH_PORT_PROPERTY_ENUM_PARAMETERS, Reserved)
#define NDIS_SIZEOF_NDIS_SWITCH_PORT_PROPERTY_ENUM_INFO_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_SWITCH_PORT_PROPERTY_ENUM_INFO, PropertyBufferOffset)
#define NDIS_SIZEOF_NDIS_SWITCH_PORT_FEATURE_STATUS_PARAMETERS_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_SWITCH_PORT_FEATURE_STATUS_PARAMETERS, Reserved)
#define NDIS_SIZEOF_NDIS_SWITCH_PORT_FEATURE_STATUS_CUSTOM_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_SWITCH_PORT_FEATURE_STATUS_CUSTOM, FeatureStatusBufferOffset)
#define NDIS_SIZEOF_NDIS_SWITCH_PROPERTY_CUSTOM_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_SWITCH_PROPERTY_CUSTOM, PropertyBufferOffset)
#define NDIS_SIZEOF_NDIS_SWITCH_PORT_PROPERTY_DELETE_PARAMETERS_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_SWITCH_PORT_PROPERTY_DELETE_PARAMETERS, PropertyInstanceId)
#define NDIS_SIZEOF_NDIS_SWITCH_PROPERTY_PARAMETERS_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_SWITCH_PROPERTY_PARAMETERS, PropertyBufferOffset)
#define NDIS_SIZEOF_NDIS_SWITCH_PROPERTY_ENUM_INFO_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_SWITCH_PROPERTY_ENUM_INFO, PropertyBufferOffset)
#define NDIS_SIZEOF_NDIS_SWITCH_PROPERTY_ENUM_PARAMETERS_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_SWITCH_PROPERTY_ENUM_PARAMETERS, NumProperties)
#define NDIS_SIZEOF_NDIS_SWITCH_FEATURE_STATUS_PARAMETERS_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_SWITCH_FEATURE_STATUS_PARAMETERS, FeatureStatusBufferLength)
#define NDIS_SIZEOF_NDIS_SWITCH_PROPERTY_DELETE_PARAMETERS_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_SWITCH_PROPERTY_DELETE_PARAMETERS, PropertyInstanceId)
#define NDIS_SIZEOF_NDIS_SWITCH_PARAMETERS_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_SWITCH_PARAMETERS, IsActive)
#define NDIS_SIZEOF_NDIS_SWITCH_FEATURE_STATUS_CUSTOM_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_SWITCH_FEATURE_STATUS_CUSTOM, FeatureStatusCustomBufferOffset)
#define NDIS_SIZEOF_NDIS_SWITCH_PORT_PARAMETERS_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_SWITCH_PORT_PARAMETERS, PortState)
#define NDIS_SIZEOF_NDIS_SWITCH_PORT_ARRAY_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_SWITCH_PORT_ARRAY, ElementSize)
#define NDIS_SIZEOF_NDIS_SWITCH_NIC_OID_REQUEST_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_SWITCH_NIC_OID_REQUEST, OidRequest)
#define NDIS_SIZEOF_NDIS_SWITCH_NIC_SAVE_STATE_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_SWITCH_NIC_SAVE_STATE, SaveDataOffset)
#define NDIS_SIZEOF_NDIS_SWITCH_NIC_PARAMETERS_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_SWITCH_NIC_PARAMETERS, VFAssigned)
#define NDIS_SIZEOF_NDIS_SWITCH_NIC_ARRAY_REVISION_1 RTL_SIZEOF_THROUGH_FIELD (NDIS_SWITCH_NIC_ARRAY, ElementSize)

#define NDIS_SWITCH_PORT_PROPERTY_CUSTOM_GET_BUFFER(PPROPC) ((PVOID) ((PUCHAR) (PPROPC) + (PPROPC)->PropertyBufferOffset))
#define NDIS_SWITCH_PORT_PROPERTY_PARAMETERS_GET_PROPERTY(PPARM) ((PVOID) ((PUCHAR) (PPARM) + (PPARM)->PropertyBufferOffset))
#define NDIS_SWITCH_CREATE_PROPERTY_VERSION(VMAJOR, VMINOR) (((VMAJOR) << 8) + (VMINOR))
#define NDIS_SWITCH_PORT_PROPERTY_ENUM_PARAMETERS_GET_FIRST_INFO(PEPARM) ((PNDIS_SWITCH_PORT_PROPERTY_ENUM_INFO) ((PUCHAR) (PEPARM) + (PEPARM)->FirstPropertyOffset))
#define NDIS_SWITCH_PORT_PROPERTY_ENUM_INFO_GET_NEXT(PEINFO) ((PNDIS_SWITCH_PORT_PROPERTY_ENUM_INFO) ((ULONG_PTR) (PEINFO) + (PEINFO)->QwordAlignedPropertyBufferLength + sizeof (NDIS_SWITCH_PORT_PROPERTY_ENUM_INFO)))
#define NDIS_SWITCH_PORT_PROPERTY_ENUM_INFO_GET_PROPERTY(PEINFO) ((PVOID) ((PUCHAR) (PEINFO) + (PEINFO)->PropertyBufferOffset))
#define NDIS_SWITCH_CREATE_FEATURE_STATUS_VERSION(VMAJOR, VMINOR) (((VMAJOR) << 8) + (VMINOR))
#define NDIS_SWITCH_PROPERTY_CUSTOM_GET_BUFFER(SWPROPC) ((PVOID) ((PUCHAR) (SWPROPC) + (SWPROPC)->PropertyBufferOffset))
#define NDIS_SWITCH_PROPERTY_PARAMETERS_GET_PROPERTY(SWPARA) ((PVOID) ((PUCHAR) (SWPARA) + (SWPARA)->PropertyBufferOffset))
#define NDIS_SWITCH_PROPERTY_ENUM_INFO_GET_NEXT(SWEINFO) ((PNDIS_SWITCH_PROPERTY_ENUM_INFO) ((ULONG_PTR) (SWEINFO) + (SWEINFO)->QwordAlignedPropertyBufferLength + sizeof (NDIS_SWITCH_PROPERTY_ENUM_INFO)))
#define NDIS_SWITCH_PROPERTY_ENUM_INFO_GET_PROPERTY(SWEINFO) ((PVOID) ((PUCHAR) (SWEINFO) + (SWEINFO)->PropertyBufferOffset))
#define NDIS_SWITCH_PROPERTY_ENUM_PARAMETERS_GET_FIRST_INFO(SWEPARM) ((PNDIS_SWITCH_PROPERTY_ENUM_INFO) ((PUCHAR) (SWEPARM) + (SWEPARM)->FirstPropertyOffset))
#define NDIS_SWITCH_PORT_AT_ARRAY_INDEX(PA, IDX) ((PNDIS_SWITCH_PORT_PARAMETERS) ((PUCHAR) (PA) + (PA)->FirstElementOffset + ((PA)->ElementSize * (IDX))))
#define NDIS_SWITCH_NIC_AT_ARRAY_INDEX(NA, IDX) ((PNDIS_SWITCH_NIC_PARAMETERS) ((PUCHAR) (NA) + (NA)->FirstElementOffset + ((NA)->ElementSize * (IDX))))

#include <ndkinfo.h>
#include <ws2def.h>
#ifndef __CYGWIN__
#include <ws2ipdef.h>
#endif

  typedef struct _NDIS_NDK_PERFORMANCE_COUNTERS {
    ULONG64 Connect;
    ULONG64 Accept;
    ULONG64 ConnectFailure;
    ULONG64 ConnectionError;
    ULONG64 ActiveConnection;
    ULONG64 Reserved01;
    ULONG64 Reserved02;
    ULONG64 Reserved03;
    ULONG64 Reserved04;
    ULONG64 Reserved05;
    ULONG64 Reserved06;
    ULONG64 Reserved07;
    ULONG64 Reserved08;
    ULONG64 Reserved09;
    ULONG64 Reserved10;
    ULONG64 Reserved11;
    ULONG64 Reserved12;
    ULONG64 Reserved13;
    ULONG64 Reserved14;
    ULONG64 Reserved15;
    ULONG64 Reserved16;
    ULONG64 Reserved17;
    ULONG64 Reserved18;
    ULONG64 Reserved19;
    ULONG64 Reserved20;
    ULONG64 CQError;
    ULONG64 RDMAInOctets;
    ULONG64 RDMAOutOctets;
    ULONG64 RDMAInFrames;
    ULONG64 RDMAOutFrames;
  } NDIS_NDK_PERFORMANCE_COUNTERS, *PNDIS_NDK_PERFORMANCE_COUNTERS;

  typedef struct _NDIS_NDK_CAPABILITIES {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    ULONG MaxQpCount;
    ULONG MaxCqCount;
    ULONG MaxMrCount;
    ULONG MaxPdCount;
    ULONG MaxInboundReadLimit;
    ULONG MaxOutboundReadLimit;
    ULONG MaxMwCount;
    ULONG MaxSrqCount;
    ULONG64 MissingCounterMask;
    NDK_ADAPTER_INFO *NdkInfo;
  } NDIS_NDK_CAPABILITIES, *PNDIS_NDK_CAPABILITIES;

  typedef struct _NDK_WMI_ADAPTER_INFO {
    NDK_VERSION Version;
    UINT32 VendorId;
    UINT32 DeviceId;
    ULONGLONG MaxRegistrationSize;
    ULONGLONG MaxWindowSize;
    ULONG FRMRPageCount;
    ULONG MaxInitiatorRequestSge;
    ULONG MaxReceiveRequestSge;
    ULONG MaxReadRequestSge;
    ULONG MaxTransferLength;
    ULONG MaxInlineDataSize;
    ULONG MaxInboundReadLimit;
    ULONG MaxOutboundReadLimit;
    ULONG MaxReceiveQueueDepth;
    ULONG MaxInitiatorQueueDepth;
    ULONG MaxSrqDepth;
    ULONG MaxCqDepth;
    ULONG LargeRequestThreshold;
    ULONG MaxCallerData;
    ULONG MaxCalleeData;
    ULONG AdapterFlags;
  } NDK_WMI_ADAPTER_INFO, *PNDK_WMI_ADAPTER_INFO;

  typedef struct _NDIS_WMI_NDK_CAPABILITIES {
    ULONG MaxQpCount;
    ULONG MaxCqCount;
    ULONG MaxMrCount;
    ULONG MaxPdCount;
    ULONG MaxInboundReadLimit;
    ULONG MaxOutboundReadLimit;
    ULONG MaxMwCount;
    ULONG MaxSrqCount;
    ULONG64 MissingCounterMask;
    NDK_WMI_ADAPTER_INFO NdkInfo;
  } NDIS_WMI_NDK_CAPABILITIES, *PNDIS_WMI_NDK_CAPABILITIES;

  typedef struct _NDIS_NDK_STATISTICS_INFO {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    NDIS_NDK_PERFORMANCE_COUNTERS CounterSet;
  } NDIS_NDK_STATISTICS_INFO;

  typedef struct _NDIS_NDK_CONNECTION_ENTRY {
    SOCKADDR_INET Local;
    SOCKADDR_INET Remote;
    BOOLEAN UserModeOwner;
    ULONG OwnerPid;
  } NDIS_NDK_CONNECTION_ENTRY;

  typedef struct _NDIS_NDK_CONNECTIONS {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    ULONG Count;
    BOOLEAN NDConnectionsMappedtoTCPConnections;
    NDIS_NDK_CONNECTION_ENTRY Connections[1];
  } NDIS_NDK_CONNECTIONS;

  typedef struct _NDIS_NDK_LOCAL_ENDPOINT_ENTRY {
    SOCKADDR_INET Local;
    BOOLEAN UserModeOwner;
    BOOLEAN Listener;
    ULONG OwnerPid;
  } NDIS_NDK_LOCAL_ENDPOINT_ENTRY;

  typedef struct _NDIS_NDK_LOCAL_ENDPOINTS {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    ULONG Count;
    BOOLEAN NDLocalEndpointsMappedtoTCPLocalEndpoints;
    NDIS_NDK_LOCAL_ENDPOINT_ENTRY LocalEndpoints[1];
  } NDIS_NDK_LOCAL_ENDPOINTS;

  typedef struct _NDIS_QOS_CAPABILITIES {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    ULONG MaxNumTrafficClasses;
    ULONG MaxNumEtsCapableTrafficClasses;
    ULONG MaxNumPfcEnabledTrafficClasses;
  } NDIS_QOS_CAPABILITIES, *PNDIS_QOS_CAPABILITIES;

  typedef struct _NDIS_QOS_CLASSIFICATION_ELEMENT {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    USHORT ConditionSelector;
    USHORT ConditionField;
    USHORT ActionSelector;
    USHORT ActionField;
  } NDIS_QOS_CLASSIFICATION_ELEMENT, *PNDIS_QOS_CLASSIFICATION_ELEMENT;

  typedef struct _NDIS_QOS_PARAMETERS {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    ULONG NumTrafficClasses;
    UCHAR PriorityAssignmentTable[NDIS_QOS_MAXIMUM_PRIORITIES];
    UCHAR TcBandwidthAssignmentTable[NDIS_QOS_MAXIMUM_TRAFFIC_CLASSES];
    UCHAR TsaAssignmentTable[NDIS_QOS_MAXIMUM_TRAFFIC_CLASSES];
    ULONG PfcEnable;
    ULONG NumClassificationElements;
    ULONG ClassificationElementSize;
    ULONG FirstClassificationElementOffset;
  } NDIS_QOS_PARAMETERS, *PNDIS_QOS_PARAMETERS;

  typedef NDIS_IF_COUNTED_STRING NDIS_NIC_SWITCH_FRIENDLYNAME, *PNDIS_NIC_SWITCH_FRIENDLYNAME;
  typedef NDIS_IF_COUNTED_STRING NDIS_VPORT_NAME, *PNDIS_VPORT_NAME;
  typedef ULONG NDIS_NIC_SWITCH_ID, *PNDIS_NIC_SWITCH_ID;
  typedef USHORT NDIS_SRIOV_FUNCTION_ID, *PNDIS_SRIOV_FUNCTION_ID;
  typedef ULONG NDIS_VF_RID, *PNDIS_VF_RID;

  typedef enum _NDIS_NIC_SWITCH_TYPE {
    NdisNicSwitchTypeUnspecified,
    NdisNicSwitchTypeExternal,
    NdisNicSwitchTypeMax
  } NDIS_NIC_SWITCH_TYPE, *PNDIS_NIC_SWITCH_TYPE;

  typedef struct _NDIS_NIC_SWITCH_PARAMETERS {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    NDIS_NIC_SWITCH_TYPE SwitchType;
    NDIS_NIC_SWITCH_ID SwitchId;
    NDIS_NIC_SWITCH_FRIENDLYNAME SwitchFriendlyName;
    ULONG NumVFs;
    ULONG NdisReserved1;
    ULONG NdisReserved2;
    ULONG NdisReserved3;
  } NDIS_NIC_SWITCH_PARAMETERS, *PNDIS_NIC_SWITCH_PARAMETERS;

  typedef struct _NDIS_NIC_SWITCH_DELETE_SWITCH_PARAMETERS {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    NDIS_NIC_SWITCH_ID SwitchId;
  } NDIS_NIC_SWITCH_DELETE_SWITCH_PARAMETERS, *PNDIS_NIC_SWITCH_DELETE_SWITCH_PARAMETERS;

  typedef struct _NDIS_NIC_SWITCH_INFO {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    NDIS_NIC_SWITCH_TYPE SwitchType;
    NDIS_NIC_SWITCH_ID SwitchId;
    NDIS_NIC_SWITCH_FRIENDLYNAME SwitchFriendlyName;
    ULONG NumVFs;
    ULONG NumAllocatedVFs;
    ULONG NumVPorts;
    ULONG NumActiveVPorts;
    ULONG NumQueuePairsForDefaultVPort;
    ULONG NumQueuePairsForNonDefaultVPorts;
    ULONG NumActiveDefaultVPortMacAddresses;
    ULONG NumActiveNonDefaultVPortMacAddresses;
    ULONG NumActiveDefaultVPortVlanIds;
    ULONG NumActiveNonDefaultVPortVlanIds;
  } NDIS_NIC_SWITCH_INFO, *PNDIS_NIC_SWITCH_INFO;

  typedef struct _NDIS_NIC_SWITCH_INFO_ARRAY {
    NDIS_OBJECT_HEADER Header;
    ULONG FirstElementOffset;
    ULONG NumElements;
    ULONG ElementSize;
  } NDIS_NIC_SWITCH_INFO_ARRAY, *PNDIS_NIC_SWITCH_INFO_ARRAY;

  typedef enum _NDIS_NIC_SWITCH_VPORT_STATE {
    NdisNicSwitchVPortStateUndefined,
    NdisNicSwitchVPortStateActivated,
    NdisNicSwitchVPortStateDeactivated,
    NdisNicSwitchVPortStateMaximum
  } NDIS_NIC_SWITCH_VPORT_STATE, *PNDIS_NIC_SWITCH_VPORT_STATE;

  typedef enum _NDIS_NIC_SWITCH_VPORT_INTERRUPT_MODERATION {
    NdisNicSwitchVPortInterruptModerationUndefined = 0,
    NdisNicSwitchVPortInterruptModerationAdaptive = 1,
    NdisNicSwitchVPortInterruptModerationOff = 2,
    NdisNicSwitchVPortInterruptModerationLow = 100,
    NdisNicSwitchVPortInterruptModerationMedium = 200,
    NdisNicSwitchVPortInterruptModerationHigh = 300
  } NDIS_NIC_SWITCH_VPORT_INTERRUPT_MODERATION, *PNDIS_NIC_SWITCH_VPORT_INTERRUPT_MODERATION;

  typedef struct _NDIS_NIC_SWITCH_VPORT_PARAMETERS {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    NDIS_NIC_SWITCH_ID SwitchId;
    NDIS_NIC_SWITCH_VPORT_ID VPortId;
    NDIS_VPORT_NAME VPortName;
    NDIS_SRIOV_FUNCTION_ID AttachedFunctionId;
    ULONG NumQueuePairs;
    NDIS_NIC_SWITCH_VPORT_INTERRUPT_MODERATION InterruptModeration;
    NDIS_NIC_SWITCH_VPORT_STATE VPortState;
    GROUP_AFFINITY ProcessorAffinity;
    ULONG LookaheadSize;
  } NDIS_NIC_SWITCH_VPORT_PARAMETERS, *PNDIS_NIC_SWITCH_VPORT_PARAMETERS;

  typedef struct _NDIS_NIC_SWITCH_DELETE_VPORT_PARAMETERS {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    NDIS_NIC_SWITCH_VPORT_ID VPortId;
  } NDIS_NIC_SWITCH_DELETE_VPORT_PARAMETERS, *PNDIS_NIC_SWITCH_DELETE_VPORT_PARAMETERS;

  typedef struct _NDIS_NIC_SWITCH_VPORT_INFO {
    NDIS_OBJECT_HEADER Header;
    NDIS_NIC_SWITCH_VPORT_ID VPortId;
    ULONG Flags;
    NDIS_NIC_SWITCH_ID SwitchId;
    NDIS_VPORT_NAME VPortName;
    NDIS_SRIOV_FUNCTION_ID AttachedFunctionId;
    ULONG NumQueuePairs;
    NDIS_NIC_SWITCH_VPORT_INTERRUPT_MODERATION InterruptModeration;
    NDIS_NIC_SWITCH_VPORT_STATE VPortState;
    GROUP_AFFINITY ProcessorAffinity;
    ULONG LookaheadSize;
    ULONG NumFilters;
  } NDIS_NIC_SWITCH_VPORT_INFO, *PNDIS_NIC_SWITCH_VPORT_INFO;

  typedef struct _NDIS_NIC_SWITCH_VPORT_INFO_ARRAY {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    NDIS_NIC_SWITCH_ID SwitchId;
    NDIS_SRIOV_FUNCTION_ID AttachedFunctionId;
    ULONG FirstElementOffset;
    ULONG NumElements;
    ULONG ElementSize;
  } NDIS_NIC_SWITCH_VPORT_INFO_ARRAY, *PNDIS_NIC_SWITCH_VPORT_INFO_ARRAY;

  typedef struct _NDIS_NIC_SWITCH_VF_PARAMETERS {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    NDIS_NIC_SWITCH_ID SwitchId;
    NDIS_VM_NAME VMName;
    NDIS_VM_FRIENDLYNAME VMFriendlyName;
    NDIS_SWITCH_NIC_NAME NicName;
    USHORT MacAddressLength;
    UCHAR PermanentMacAddress[NDIS_MAX_PHYS_ADDRESS_LENGTH];
    UCHAR CurrentMacAddress[NDIS_MAX_PHYS_ADDRESS_LENGTH];
    NDIS_SRIOV_FUNCTION_ID VFId;
    NDIS_VF_RID RequestorId;
  } NDIS_NIC_SWITCH_VF_PARAMETERS, *PNDIS_NIC_SWITCH_VF_PARAMETERS;

  typedef struct _NDIS_NIC_SWITCH_FREE_VF_PARAMETERS {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    NDIS_SRIOV_FUNCTION_ID VFId;
  } NDIS_NIC_SWITCH_FREE_VF_PARAMETERS, *PNDIS_NIC_SWITCH_FREE_VF_PARAMETERS;

  typedef struct _NDIS_NIC_SWITCH_VF_INFO {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    NDIS_NIC_SWITCH_ID SwitchId;
    NDIS_VM_NAME VMName;
    NDIS_VM_FRIENDLYNAME VMFriendlyName;
    NDIS_SWITCH_NIC_NAME NicName;
    USHORT MacAddressLength;
    UCHAR PermanentMacAddress[NDIS_MAX_PHYS_ADDRESS_LENGTH];
    UCHAR CurrentMacAddress[NDIS_MAX_PHYS_ADDRESS_LENGTH];
    NDIS_SRIOV_FUNCTION_ID VFId;
    NDIS_VF_RID RequestorId;
  } NDIS_NIC_SWITCH_VF_INFO, *PNDIS_NIC_SWITCH_VF_INFO;

  typedef struct _NDIS_NIC_SWITCH_VF_INFO_ARRAY {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    NDIS_NIC_SWITCH_ID SwitchId;
    ULONG FirstElementOffset;
    ULONG NumElements;
    ULONG ElementSize;
  } NDIS_NIC_SWITCH_VF_INFO_ARRAY, *PNDIS_NIC_SWITCH_VF_INFO_ARRAY;

  typedef struct _NDIS_SRIOV_CAPABILITIES {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    ULONG SriovCapabilities;
  } NDIS_SRIOV_CAPABILITIES, *PNDIS_SRIOV_CAPABILITIES;

  typedef struct _NDIS_SRIOV_READ_VF_CONFIG_SPACE_PARAMETERS {
    NDIS_OBJECT_HEADER Header;
    NDIS_SRIOV_FUNCTION_ID VFId;
    ULONG Offset;
    ULONG Length;
    ULONG BufferOffset;
  } NDIS_SRIOV_READ_VF_CONFIG_SPACE_PARAMETERS, *PNDIS_SRIOV_READ_VF_CONFIG_SPACE_PARAMETERS;

  typedef struct _NDIS_SRIOV_WRITE_VF_CONFIG_SPACE_PARAMETERS {
    NDIS_OBJECT_HEADER Header;
    NDIS_SRIOV_FUNCTION_ID VFId;
    ULONG Offset;
    ULONG Length;
    ULONG BufferOffset;
  } NDIS_SRIOV_WRITE_VF_CONFIG_SPACE_PARAMETERS, *PNDIS_SRIOV_WRITE_VF_CONFIG_SPACE_PARAMETERS;

  typedef struct _NDIS_SRIOV_READ_VF_CONFIG_BLOCK_PARAMETERS {
    NDIS_OBJECT_HEADER Header;
    NDIS_SRIOV_FUNCTION_ID VFId;
    ULONG BlockId;
    ULONG Length;
    ULONG BufferOffset;
  } NDIS_SRIOV_READ_VF_CONFIG_BLOCK_PARAMETERS, *PNDIS_SRIOV_READ_VF_CONFIG_BLOCK_PARAMETERS;

  typedef struct _NDIS_SRIOV_WRITE_VF_CONFIG_BLOCK_PARAMETERS {
    NDIS_OBJECT_HEADER Header;
    NDIS_SRIOV_FUNCTION_ID VFId;
    ULONG BlockId;
    ULONG Length;
    ULONG BufferOffset;
  } NDIS_SRIOV_WRITE_VF_CONFIG_BLOCK_PARAMETERS, *PNDIS_SRIOV_WRITE_VF_CONFIG_BLOCK_PARAMETERS;

  typedef struct _NDIS_SRIOV_RESET_VF_PARAMETERS {
    NDIS_OBJECT_HEADER Header;
    NDIS_SRIOV_FUNCTION_ID VFId;
  } NDIS_SRIOV_RESET_VF_PARAMETERS, *PNDIS_SRIOV_RESET_VF_PARAMETERS;

  typedef struct _NDIS_SRIOV_SET_VF_POWER_STATE_PARAMETERS {
    NDIS_OBJECT_HEADER Header;
    NDIS_SRIOV_FUNCTION_ID VFId;
    NDIS_DEVICE_POWER_STATE PowerState;
    BOOLEAN WakeEnable;
  } NDIS_SRIOV_SET_VF_POWER_STATE_PARAMETERS, *PNDIS_SRIOV_SET_VF_POWER_STATE_PARAMETERS;

  typedef struct _NDIS_SRIOV_CONFIG_STATE_PARAMETERS {
    NDIS_OBJECT_HEADER Header;
    ULONG BlockId;
    ULONG Length;
  } NDIS_SRIOV_CONFIG_STATE_PARAMETERS, *PNDIS_SRIOV_CONFIG_STATE_PARAMETERS;

  typedef struct _NDIS_SRIOV_VF_VENDOR_DEVICE_ID_INFO {
    NDIS_OBJECT_HEADER Header;
    NDIS_SRIOV_FUNCTION_ID VFId;
    USHORT VendorId;
    USHORT DeviceId;
  } NDIS_SRIOV_VF_VENDOR_DEVICE_ID_INFO, *PNDIS_SRIOV_VF_VENDOR_DEVICE_ID_INFO;

  typedef struct _NDIS_SRIOV_PROBED_BARS_INFO {
    NDIS_OBJECT_HEADER Header;
    ULONG BaseRegisterValuesOffset;
  } NDIS_SRIOV_PROBED_BARS_INFO, *PNDIS_SRIOV_PROBED_BARS_INFO;

  typedef struct _NDIS_RECEIVE_FILTER_MOVE_FILTER_PARAMETERS {
    NDIS_OBJECT_HEADER Header;
    NDIS_RECEIVE_FILTER_ID FilterId;
    NDIS_RECEIVE_QUEUE_ID SourceQueueId;
    NDIS_NIC_SWITCH_VPORT_ID SourceVPortId;
    NDIS_RECEIVE_QUEUE_ID DestQueueId;
    NDIS_NIC_SWITCH_VPORT_ID DestVPortId;
  } NDIS_RECEIVE_FILTER_MOVE_FILTER_PARAMETERS, *PNDIS_RECEIVE_FILTER_MOVE_FILTER_PARAMETERS;

  typedef struct _NDIS_SRIOV_BAR_RESOURCES_INFO {
    NDIS_OBJECT_HEADER Header;
    NDIS_SRIOV_FUNCTION_ID VFId;
    USHORT BarIndex;
    ULONG BarResourcesOffset;
  } NDIS_SRIOV_BAR_RESOURCES_INFO, *PNDIS_SRIOV_BAR_RESOURCES_INFO;

  typedef struct _NDIS_SRIOV_PF_LUID_INFO {
    NDIS_OBJECT_HEADER Header;
    LUID Luid;
  } NDIS_SRIOV_PF_LUID_INFO, *PNDIS_SRIOV_PF_LUID_INFO;

  typedef struct _NDIS_SRIOV_VF_SERIAL_NUMBER_INFO {
    NDIS_OBJECT_HEADER Header;
    ULONG SerialNumber;
  } NDIS_SRIOV_VF_SERIAL_NUMBER_INFO, *PNDIS_SRIOV_VF_SERIAL_NUMBER_INFO;

  typedef struct _NDIS_SRIOV_VF_INVALIDATE_CONFIG_BLOCK_INFO {
    NDIS_OBJECT_HEADER Header;
    ULONG64 BlockMask;
  } NDIS_SRIOV_VF_INVALIDATE_CONFIG_BLOCK_INFO, *PNDIS_SRIOV_VF_INVALIDATE_CONFIG_BLOCK_INFO;

  typedef GUID NDIS_SWITCH_OBJECT_INSTANCE_ID, *PNDIS_SWITCH_OBJECT_INSTANCE_ID;
  typedef GUID NDIS_SWITCH_OBJECT_ID, *PNDIS_SWITCH_OBJECT_ID;
  typedef USHORT NDIS_SWITCH_OBJECT_VERSION, *PNDIS_SWITCH_OBJECT_VERSION;
  typedef USHORT NDIS_SWITCH_OBJECT_SERIALIZATION_VERSION, *PNDIS_SWITCH_OBJECT_SERIALIZATION_VERSION;
#ifndef _NDIS_SWITCH_PORT_ID
#define _NDIS_SWITCH_PORT_ID NDIS_SWITCH_PORT_ID
  typedef UINT32 NDIS_SWITCH_PORT_ID, *PNDIS_SWITCH_PORT_ID;
  typedef USHORT NDIS_SWITCH_NIC_INDEX, *PNDIS_SWITCH_NIC_INDEX;
#endif

  typedef enum _NDIS_SWITCH_PORT_PROPERTY_TYPE {
    NdisSwitchPortPropertyTypeUndefined,
    NdisSwitchPortPropertyTypeCustom,
    NdisSwitchPortPropertyTypeSecurity,
    NdisSwitchPortPropertyTypeVlan,
    NdisSwitchPortPropertyTypeProfile,
    NdisSwitchPortPropertyTypeMaximum
  } NDIS_SWITCH_PORT_PROPERTY_TYPE, *PNDIS_SWITCH_PORT_PROPERTY_TYPE;

  typedef struct _NDIS_SWITCH_PORT_PROPERTY_SECURITY {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    BOOLEAN AllowMacSpoofing;
    BOOLEAN AllowIeeePriorityTag;
    UINT32 VirtualSubnetId;
    BOOLEAN AllowTeaming;
  } NDIS_SWITCH_PORT_PROPERTY_SECURITY, *PNDIS_SWITCH_PORT_PROPERTY_SECURITY;

  typedef enum _NDIS_SWITCH_PORT_VLAN_MODE {
    NdisSwitchPortVlanModeUnknown = 0,
    NdisSwitchPortVlanModeAccess = 1,
    NdisSwitchPortVlanModeTrunk = 2,
    NdisSwitchPortVlanModePrivate = 3,
    NdisSwitchPortVlanModeMax = 4
  } NDIS_SWITCH_PORT_VLAN_MODE, *PNDIS_SWITCH_PORT_VLAN_MODE;

  typedef enum _NDIS_SWITCH_PORT_PVLAN_MODE {
    NdisSwitchPortPvlanModeUndefined = 0,
    NdisSwitchPortPvlanModeIsolated,
    NdisSwitchPortPvlanModeCommunity,
    NdisSwitchPortPvlanModePromiscuous
  } NDIS_SWITCH_PORT_PVLAN_MODE, *PNDIS_SWITCH_PORT_PVLAN_MODE;

  typedef struct _NDIS_SWITCH_PORT_PROPERTY_VLAN {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    NDIS_SWITCH_PORT_VLAN_MODE OperationMode;
    __C89_NAMELESS union {
      struct {
	UINT16 AccessVlanId;
	UINT16 NativeVlanId;
	UINT64 PruneVlanIdArray[64];
	UINT64 TrunkVlanIdArray[64];
      } VlanProperties;
      struct {
	NDIS_SWITCH_PORT_PVLAN_MODE PvlanMode;
	UINT16 PrimaryVlanId;
	__C89_NAMELESS union {
	  UINT16 SecondaryVlanId;
	  UINT64 SecondaryVlanIdArray[64];
	};
      } PvlanProperties;
    };
  } NDIS_SWITCH_PORT_PROPERTY_VLAN, *PNDIS_SWITCH_PORT_PROPERTY_VLAN;

  typedef struct _NDIS_SWITCH_PORT_PROPERTY_PROFILE {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    NDIS_SWITCH_PORT_PROPERTY_PROFILE_NAME ProfileName;
    GUID ProfileId;
    NDIS_VENDOR_NAME VendorName;
    GUID VendorId;
    UINT32 ProfileData;
    GUID NetCfgInstanceId;
    struct {
      UINT32 PciSegmentNumber:16;
      UINT32 PciBusNumber:8;
      UINT32 PciDeviceNumber:5;
      UINT32 PciFunctionNumber:3;
    } PciLocation;
    UINT32 CdnLabelId;
    NDIS_SWITCH_PORT_PROPERTY_PROFILE_CDN_LABEL CdnLabel;
  } NDIS_SWITCH_PORT_PROPERTY_PROFILE, *PNDIS_SWITCH_PORT_PROPERTY_PROFILE;

  typedef struct _NDIS_SWITCH_PORT_PROPERTY_CUSTOM {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    ULONG PropertyBufferLength;
    ULONG PropertyBufferOffset;
  } NDIS_SWITCH_PORT_PROPERTY_CUSTOM, *PNDIS_SWITCH_PORT_PROPERTY_CUSTOM;

  typedef struct _NDIS_SWITCH_PORT_PROPERTY_PARAMETERS {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    NDIS_SWITCH_PORT_ID PortId;
    NDIS_SWITCH_PORT_PROPERTY_TYPE PropertyType;
    NDIS_SWITCH_OBJECT_ID PropertyId;
    NDIS_SWITCH_OBJECT_VERSION PropertyVersion;
    NDIS_SWITCH_OBJECT_SERIALIZATION_VERSION SerializationVersion;
    NDIS_SWITCH_OBJECT_INSTANCE_ID PropertyInstanceId;
    ULONG PropertyBufferLength;
    ULONG PropertyBufferOffset;
    ULONG Reserved;
  } NDIS_SWITCH_PORT_PROPERTY_PARAMETERS, *PNDIS_SWITCH_PORT_PROPERTY_PARAMETERS;

  typedef struct _NDIS_SWITCH_PORT_PROPERTY_DELETE_PARAMETERS {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    NDIS_SWITCH_PORT_ID PortId;
    NDIS_SWITCH_PORT_PROPERTY_TYPE PropertyType;
    NDIS_SWITCH_OBJECT_ID PropertyId;
    NDIS_SWITCH_OBJECT_INSTANCE_ID PropertyInstanceId;
  } NDIS_SWITCH_PORT_PROPERTY_DELETE_PARAMETERS, *PNDIS_SWITCH_PORT_PROPERTY_DELETE_PARAMETERS;

  typedef struct _NDIS_SWITCH_PORT_PROPERTY_ENUM_PARAMETERS {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    NDIS_SWITCH_PORT_ID PortId;
    NDIS_SWITCH_PORT_PROPERTY_TYPE PropertyType;
    NDIS_SWITCH_OBJECT_ID PropertyId;
    NDIS_SWITCH_OBJECT_SERIALIZATION_VERSION SerializationVersion;
    ULONG FirstPropertyOffset;
    ULONG NumProperties;
    USHORT Reserved;
  } NDIS_SWITCH_PORT_PROPERTY_ENUM_PARAMETERS, *PNDIS_SWITCH_PORT_PROPERTY_ENUM_PARAMETERS;

  typedef struct _NDIS_SWITCH_PORT_PROPERTY_ENUM_INFO {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    NDIS_SWITCH_OBJECT_VERSION PropertyVersion;
    NDIS_SWITCH_OBJECT_INSTANCE_ID PropertyInstanceId;
    ULONG QwordAlignedPropertyBufferLength;
    ULONG PropertyBufferLength;
    ULONG PropertyBufferOffset;
  } NDIS_SWITCH_PORT_PROPERTY_ENUM_INFO, *PNDIS_SWITCH_PORT_PROPERTY_ENUM_INFO;

  typedef enum _NDIS_SWITCH_PORT_FEATURE_STATUS_TYPE {
    NdisSwitchPortFeatureStatusTypeUndefined,
    NdisSwitchPortFeatureStatusTypeCustom,
    NdisSwitchPortFeatureStatusTypeMaximum
  } NDIS_SWITCH_PORT_FEATURE_STATUS_TYPE, *PNDIS_SWITCH_PORT_FEATURE_STATUS_TYPE;

  typedef struct _NDIS_SWITCH_PORT_FEATURE_STATUS_PARAMETERS {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    NDIS_SWITCH_PORT_ID PortId;
    NDIS_SWITCH_PORT_FEATURE_STATUS_TYPE FeatureStatusType;
    NDIS_SWITCH_OBJECT_ID FeatureStatusId;
    NDIS_SWITCH_OBJECT_VERSION FeatureStatusVersion;
    NDIS_SWITCH_OBJECT_SERIALIZATION_VERSION SerializationVersion;
    NDIS_SWITCH_OBJECT_INSTANCE_ID FeatureStatusInstanceId;
    ULONG FeatureStatusBufferLength;
    ULONG FeatureStatusBufferOffset;
    ULONG Reserved;
  } NDIS_SWITCH_PORT_FEATURE_STATUS_PARAMETERS, *PNDIS_SWITCH_PORT_FEATURE_STATUS_PARAMETERS;

  typedef struct _NDIS_SWITCH_PORT_FEATURE_STATUS_CUSTOM {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    ULONG FeatureStatusBufferLength;
    ULONG FeatureStatusBufferOffset;
  } NDIS_SWITCH_PORT_FEATURE_STATUS_CUSTOM, *PNDIS_SWITCH_PORT_FEATURE_STATUS_CUSTOM;

  typedef enum _NDIS_SWITCH_PROPERTY_TYPE {
    NdisSwitchPropertyTypeUndefined,
    NdisSwitchPropertyTypeCustom,
    NdisSwitchPropertyTypeMaximum
  } NDIS_SWITCH_PROPERTY_TYPE, *PNDIS_SWITCH_PROPERTY_TYPE;

  typedef struct _NDIS_SWITCH_PROPERTY_CUSTOM {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    ULONG PropertyBufferLength;
    ULONG PropertyBufferOffset;
  } NDIS_SWITCH_PROPERTY_CUSTOM, *PNDIS_SWITCH_PROPERTY_CUSTOM;

  typedef struct _NDIS_SWITCH_PROPERTY_PARAMETERS {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    NDIS_SWITCH_PROPERTY_TYPE PropertyType;
    NDIS_SWITCH_OBJECT_ID PropertyId;
    NDIS_SWITCH_OBJECT_VERSION PropertyVersion;
    NDIS_SWITCH_OBJECT_SERIALIZATION_VERSION SerializationVersion;
    NDIS_SWITCH_OBJECT_INSTANCE_ID PropertyInstanceId;
    ULONG PropertyBufferLength;
    ULONG PropertyBufferOffset;
  } NDIS_SWITCH_PROPERTY_PARAMETERS, *PNDIS_SWITCH_PROPERTY_PARAMETERS;

  typedef struct _NDIS_SWITCH_PROPERTY_DELETE_PARAMETERS {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    NDIS_SWITCH_PROPERTY_TYPE PropertyType;
    NDIS_SWITCH_OBJECT_ID PropertyId;
    NDIS_SWITCH_OBJECT_INSTANCE_ID PropertyInstanceId;
  } NDIS_SWITCH_PROPERTY_DELETE_PARAMETERS, *PNDIS_SWITCH_PROPERTY_DELETE_PARAMETERS;

  typedef struct _NDIS_SWITCH_PROPERTY_ENUM_INFO {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    NDIS_SWITCH_OBJECT_INSTANCE_ID PropertyInstanceId;
    NDIS_SWITCH_OBJECT_VERSION PropertyVersion;
    ULONG QwordAlignedPropertyBufferLength;
    ULONG PropertyBufferLength;
    ULONG PropertyBufferOffset;
  } NDIS_SWITCH_PROPERTY_ENUM_INFO, *PNDIS_SWITCH_PROPERTY_ENUM_INFO;

  typedef struct _NDIS_SWITCH_PROPERTY_ENUM_PARAMETERS {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    NDIS_SWITCH_PROPERTY_TYPE PropertyType;
    NDIS_SWITCH_OBJECT_ID PropertyId;
    NDIS_SWITCH_OBJECT_SERIALIZATION_VERSION SerializationVersion;
    ULONG FirstPropertyOffset;
    ULONG NumProperties;
  } NDIS_SWITCH_PROPERTY_ENUM_PARAMETERS, *PNDIS_SWITCH_PROPERTY_ENUM_PARAMETERS;

  typedef enum _NDIS_SWITCH_FEATURE_STATUS_TYPE {
    NdisSwitchFeatureStatusTypeUndefined,
    NdisSwitchFeatureStatusTypeCustom,
    NdisSwitchFeatureStatusTypeMaximum
  } NDIS_SWITCH_FEATURE_STATUS_TYPE, *PNDIS_SWITCH_FEATURE_STATUS_TYPE;

  typedef struct _NDIS_SWITCH_FEATURE_STATUS_PARAMETERS {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    NDIS_SWITCH_FEATURE_STATUS_TYPE FeatureStatusType;
    NDIS_SWITCH_OBJECT_ID FeatureStatusId;
    NDIS_SWITCH_OBJECT_INSTANCE_ID FeatureStatusInstanceId;
    NDIS_SWITCH_OBJECT_VERSION FeatureStatusVersion;
    NDIS_SWITCH_OBJECT_SERIALIZATION_VERSION SerializationVersion;
    ULONG FeatureStatusBufferOffset;
    ULONG FeatureStatusBufferLength;
  } NDIS_SWITCH_FEATURE_STATUS_PARAMETERS, *PNDIS_SWITCH_FEATURE_STATUS_PARAMETERS;

  typedef struct _NDIS_SWITCH_FEATURE_STATUS_CUSTOM {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    ULONG FeatureStatusCustomBufferLength;
    ULONG FeatureStatusCustomBufferOffset;
  } NDIS_SWITCH_FEATURE_STATUS_CUSTOM, *PNDIS_SWITCH_FEATURE_STATUS_CUSTOM;

  typedef struct _NDIS_SWITCH_PARAMETERS {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    NDIS_SWITCH_NAME SwitchName;
    NDIS_SWITCH_FRIENDLYNAME SwitchFriendlyName;
    UINT32 NumSwitchPorts;
    BOOLEAN IsActive;
  } NDIS_SWITCH_PARAMETERS, *PNDIS_SWITCH_PARAMETERS;

  typedef enum _NDIS_SWITCH_PORT_TYPE {
    NdisSwitchPortTypeGeneric = 0,
    NdisSwitchPortTypeExternal = 1,
    NdisSwitchPortTypeSynthetic = 2,
    NdisSwitchPortTypeEmulated = 3,
    NdisSwitchPortTypeInternal = 4
  } NDIS_SWITCH_PORT_TYPE;

  typedef enum _NDIS_SWITCH_PORT_STATE {
    NdisSwitchPortStateUnknown = 0,
    NdisSwitchPortStateCreated = 1,
    NdisSwitchPortStateTeardown = 2,
    NdisSwitchPortStateDeleted = 3
  } NDIS_SWITCH_PORT_STATE;

  typedef struct _NDIS_SWITCH_PORT_PARAMETERS {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    NDIS_SWITCH_PORT_ID PortId;
    NDIS_SWITCH_PORT_NAME PortName;
    NDIS_SWITCH_PORT_FRIENDLYNAME PortFriendlyName;
    NDIS_SWITCH_PORT_TYPE PortType;
    BOOLEAN IsValidationPort;
    NDIS_SWITCH_PORT_STATE PortState;
  } NDIS_SWITCH_PORT_PARAMETERS, *PNDIS_SWITCH_PORT_PARAMETERS;

  typedef struct _NDIS_SWITCH_PORT_ARRAY {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    USHORT FirstElementOffset;
    ULONG NumElements;
    ULONG ElementSize;
  } NDIS_SWITCH_PORT_ARRAY, *PNDIS_SWITCH_PORT_ARRAY;

  typedef enum _NDIS_SWITCH_NIC_TYPE {
    NdisSwitchNicTypeExternal = 0,
    NdisSwitchNicTypeSynthetic = 1,
    NdisSwitchNicTypeEmulated = 2,
    NdisSwitchNicTypeInternal = 3
  } NDIS_SWITCH_NIC_TYPE;

  typedef enum _NDIS_SWITCH_NIC_STATE {
    NdisSwitchNicStateUnknown = 0,
    NdisSwitchNicStateCreated = 1,
    NdisSwitchNicStateConnected = 2,
    NdisSwitchNicStateDisconnected = 3,
    NdisSwitchNicStateDeleted = 4
  } NDIS_SWITCH_NIC_STATE;

  typedef struct _NDIS_SWITCH_NIC_PARAMETERS {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    NDIS_SWITCH_NIC_NAME NicName;
    NDIS_SWITCH_NIC_FRIENDLYNAME NicFriendlyName;
    NDIS_SWITCH_PORT_ID PortId;
    NDIS_SWITCH_NIC_INDEX NicIndex;
    NDIS_SWITCH_NIC_TYPE NicType;
    NDIS_SWITCH_NIC_STATE NicState;
    NDIS_VM_NAME VmName;
    NDIS_VM_FRIENDLYNAME VmFriendlyName;
    GUID NetCfgInstanceId;
    ULONG MTU;
    USHORT NumaNodeId;
    UCHAR PermanentMacAddress[NDIS_MAX_PHYS_ADDRESS_LENGTH];
    UCHAR VMMacAddress[NDIS_MAX_PHYS_ADDRESS_LENGTH];
    UCHAR CurrentMacAddress[NDIS_MAX_PHYS_ADDRESS_LENGTH];
    BOOLEAN VFAssigned;
  } NDIS_SWITCH_NIC_PARAMETERS, *PNDIS_SWITCH_NIC_PARAMETERS;

  typedef struct _NDIS_SWITCH_NIC_ARRAY {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    USHORT FirstElementOffset;
    ULONG NumElements;
    ULONG ElementSize;
  } NDIS_SWITCH_NIC_ARRAY, *PNDIS_SWITCH_NIC_ARRAY;

  typedef struct _NDIS_OID_REQUEST NDIS_OID_REQUEST, *PNDIS_OID_REQUEST;

  typedef struct _NDIS_SWITCH_NIC_OID_REQUEST {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    NDIS_SWITCH_PORT_ID SourcePortId;
    NDIS_SWITCH_NIC_INDEX SourceNicIndex;
    NDIS_SWITCH_PORT_ID DestinationPortId;
    NDIS_SWITCH_NIC_INDEX DestinationNicIndex;
    PNDIS_OID_REQUEST OidRequest;
  } NDIS_SWITCH_NIC_OID_REQUEST, *PNDIS_SWITCH_NIC_OID_REQUEST;

  typedef struct _NDIS_SWITCH_NIC_SAVE_STATE {
    NDIS_OBJECT_HEADER Header;
    ULONG Flags;
    NDIS_SWITCH_PORT_ID PortId;
    NDIS_SWITCH_NIC_INDEX NicIndex;
    GUID ExtensionId;
    NDIS_SWITCH_EXTENSION_FRIENDLYNAME ExtensionFriendlyName;
    GUID FeatureClassId;
    USHORT SaveDataSize;
    USHORT SaveDataOffset;
  } NDIS_SWITCH_NIC_SAVE_STATE, *PNDIS_SWITCH_NIC_SAVE_STATE;
#endif

  typedef struct _NDIS_PORT_STATE {
    NDIS_OBJECT_HEADER Header;
    NDIS_MEDIA_CONNECT_STATE MediaConnectState;
    ULONG64 XmitLinkSpeed;
    ULONG64 RcvLinkSpeed;
    NET_IF_DIRECTION_TYPE Direction;
    NDIS_PORT_CONTROL_STATE SendControlState;
    NDIS_PORT_CONTROL_STATE RcvControlState;
    NDIS_PORT_AUTHORIZATION_STATE SendAuthorizationState;
    NDIS_PORT_AUTHORIZATION_STATE RcvAuthorizationState;
    ULONG Flags;
  } NDIS_PORT_STATE, *PNDIS_PORT_STATE;

  typedef struct _NDIS_PORT_CHARACTERISTICS {
    NDIS_OBJECT_HEADER Header;
    NDIS_PORT_NUMBER PortNumber;
    ULONG Flags;
    NDIS_PORT_TYPE Type;
    NDIS_MEDIA_CONNECT_STATE MediaConnectState;
    ULONG64 XmitLinkSpeed;
    ULONG64 RcvLinkSpeed;
    NET_IF_DIRECTION_TYPE Direction;
    NDIS_PORT_CONTROL_STATE SendControlState;
    NDIS_PORT_CONTROL_STATE RcvControlState;
    NDIS_PORT_AUTHORIZATION_STATE SendAuthorizationState;
    NDIS_PORT_AUTHORIZATION_STATE RcvAuthorizationState;
  } NDIS_PORT_CHARACTERISTICS, *PNDIS_PORT_CHARACTERISTICS;

  typedef struct _NDIS_PORT NDIS_PORT, *PNDIS_PORT;

  struct _NDIS_PORT {
    PNDIS_PORT Next;
    PVOID NdisReserved;
    PVOID MiniportReserved;
    PVOID ProtocolReserved;
    NDIS_PORT_CHARACTERISTICS PortCharacteristics;
  };

  typedef struct _NDIS_PORT_ARRAY {
    NDIS_OBJECT_HEADER Header;
    ULONG NumberOfPorts;
    ULONG OffsetFirstPort;
    ULONG ElementSize;
    NDIS_PORT_CHARACTERISTICS Ports[1];
  } NDIS_PORT_ARRAY, *PNDIS_PORT_ARRAY;
#endif
#endif

#ifdef __cplusplus
}
#endif

#endif
#endif
