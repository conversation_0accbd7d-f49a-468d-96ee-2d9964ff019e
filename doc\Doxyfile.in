# Process with doxygen to generate API documentation

PROJECT_NAME           = @PACKAGE_NAME@
PROJECT_NUMBER         = @PACKAGE_VERSION@
PROJECT_BRIEF          = "RNN-based noise suppressor."
INPUT                  = @top_srcdir@/include/rnnoise.h
OPTIMIZE_OUTPUT_FOR_C  = YES

QUIET                  = YES
WARNINGS               = YES
WARN_IF_UNDOCUMENTED   = YES
WARN_IF_DOC_ERROR      = YES
WARN_NO_PARAMDOC       = YES

JAVADOC_AUTOBRIEF      = YES
SORT_MEMBER_DOCS       = NO

HAVE_DOT               = @HAVE_DOT@
